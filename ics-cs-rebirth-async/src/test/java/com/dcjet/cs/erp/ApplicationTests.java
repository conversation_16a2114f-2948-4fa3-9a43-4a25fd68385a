package com.dcjet.cs.erp;

import com.dcjet.cs.async.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ApplicationTests {

    @Test
    public void testforeach(){
        List<Obj> objList = new ArrayList<>();
        /*
        for (int i = 0; i < 10; i++) {
            Obj obj = new Obj();
            obj.setFacGNo(String.valueOf(i));
            obj.setQty(10);
            obj.setNetWt(20);
            obj.setGrossWt(30);
            objList.add(obj);
        }*/
        //Map<String,Integer> map = objList.stream().collect(Collectors.toMap());
        Obj obj1 = new Obj();
        obj1.setFacGNo("aaa");
        obj1.setQty(10);
        obj1.setNetWt(20);
        obj1.setGrossWt(30);
        objList.add(obj1);

        Obj obj2 = new Obj();
        obj2.setFacGNo("aaa");
        obj2.setQty(10);
        obj2.setNetWt(20);
        obj2.setGrossWt(30);
        objList.add(obj2);

        Obj obj3 = new Obj();
        obj3.setFacGNo("bbb");
        obj3.setQty(10);
        obj3.setNetWt(20);
        obj3.setGrossWt(30);
        objList.add(obj3);
        Map<String, Obj> map = objList.stream().collect(Collectors.toMap(Obj::getFacGNo, d->d, (oldValue, newValue)->newValue));
        Map<String, Obj> map2 = objList.stream().collect(Collectors.toMap(Obj::getFacGNo, it -> {
            Obj obj = new Obj();
            obj.setQty(it.getQty());
            obj.setNetWt(it.getNetWt());
            return obj;
        }, (oldObj, newObj) -> {
            oldObj.setQty(oldObj.getQty() + newObj.getQty());
            oldObj.setNetWt(oldObj.getNetWt() + newObj.getNetWt());
            oldObj.setGrossWt(oldObj.getGrossWt() + newObj.getGrossWt());
            return oldObj;
        }));
        System.out.println(map);
        System.out.println(map2);
    }

}

