package com.dcjet.cs.async.jobhandler_cus.technimark;

import com.dcjet.cs.gwstd.service.GwstdJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class DocumentsReportHandler {

    @Resource
    private GwstdJobService gwstdJobService;

    @XxlJob("documentsReportHandler")
    public ReturnT<String> execute(String strSystemCode) {
        log.info("=================泰马克出口单证统计表开始统计=================");
        gwstdJobService.documentsReport();
        log.info("=================泰马克出口单证统计表统计结束=================");
        return ReturnT.SUCCESS;
    }
}
