package com.dcjet.cs.async.jobhandler_cus.huanhong;

import com.dcjet.cs_cus.huanhong.service.TaxPaymentService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: WJ
 * @createDate: 2022/2/11 10:35
 */
@Component
@Slf4j
public class ReturnTaxPaymentDataHandler {

    @Resource
    private TaxPaymentService taxPaymentService;

    @XxlJob("returnTaxHandler")
    public ReturnT<String> execute(String strSystemCode)  {
        log.info("=================环鸿返回未支付税单信息任务开始=================");
        taxPaymentService.returnTaxData();
        log.info("=================环鸿返回未支付税单信息任务结束=================");
        return ReturnT.SUCCESS;
    }
}
