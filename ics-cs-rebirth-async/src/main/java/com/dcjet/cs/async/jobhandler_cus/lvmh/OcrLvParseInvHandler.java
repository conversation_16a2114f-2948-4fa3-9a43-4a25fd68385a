package com.dcjet.cs.async.jobhandler_cus.lvmh;

import com.dcjet.cs.ocrrecord.service.GwOcrLvmhInvoiceService;
import com.dcjet.cs.util.LoggerUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;

/**
 * @description: OCR解析
 * @author: WJ
 * @createDate: 2022/4/27 10:25
 */
@Component
public class OcrLvParseInvHandler {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private GwOcrLvmhInvoiceService service;

    @XxlJob("ocrLvParseInvHandler")
    public ReturnT<String> execute(String strSystemCode) {
        LoggerUtil.logInfo(logger, "=================OcrLvmhInvoice识别解析开始=================");
        service.parseInv();
        LoggerUtil.logInfo(logger, "=================OcrLvmhInvoice识别解析结束=================");
        return ReturnT.SUCCESS;
    }
}
