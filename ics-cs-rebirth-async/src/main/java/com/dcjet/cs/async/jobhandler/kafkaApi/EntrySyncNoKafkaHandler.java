package com.dcjet.cs.async.jobhandler.kafkaApi;

import com.dcjet.cs.noKafka.service.BillSyncNoKafkaService;
import com.dcjet.cs.noKafka.service.DutyFormMqSyncNoKafkaService;
import com.dcjet.cs.noKafka.service.EntryStatusNoKafkaService;
import com.dcjet.cs.noKafka.service.EntrySyncNoKafkaService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class EntrySyncNoKafkaHandler {

    @Resource
    private EntrySyncNoKafkaService entrySyncNoKafkaService;
    @Resource
    private BillSyncNoKafkaService billSyncNoKafkaService;
    @Resource
    private DutyFormMqSyncNoKafkaService dutyFormMqSyncNoKafkaService;
    @Resource
    private EntryStatusNoKafkaService entryStatusNoKafkaService;

    @XxlJob("entrySyncNoKafkaHandler")
    public ReturnT<String> execute(String strSystemCode)  {
        XxlJobLogger.log("=================非kafka模式处理数据消息任务开始=================");
        entrySyncNoKafkaService.doHandle(strSystemCode);
        billSyncNoKafkaService.doHandle(strSystemCode);
        dutyFormMqSyncNoKafkaService.doHandle(strSystemCode);
        //entryStatusNoKafkaService.doHandle(strSystemCode);
        XxlJobLogger.log("=================非kafka模式处理数据消息任务结束=================");
        return ReturnT.SUCCESS;
    }
}
