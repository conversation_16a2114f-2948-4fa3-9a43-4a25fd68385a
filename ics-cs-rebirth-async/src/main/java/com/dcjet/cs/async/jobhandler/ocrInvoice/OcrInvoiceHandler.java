package com.dcjet.cs.async.jobhandler.ocrInvoice;

import com.dcjet.cs.ocrInvoice.service.GwOcrInvoiceIEService;
import com.dcjet.cs.ocrInvoice.service.GwOcrInvoiceIHeadService;
import com.dcjet.cs.util.LoggerUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;

@Component
public class OcrInvoiceHandler {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private GwOcrInvoiceIEService gwOcrInvoiceIEService;

    @XxlJob("getOcrScanResult")
    public ReturnT<String> execute(String strSystemCode) {
        LoggerUtil.logInfo(logger, "=================OcrInvoice识别开始=================");
        gwOcrInvoiceIEService.identifyData();
        LoggerUtil.logInfo(logger, "=================OcrInvoice识别结束=================");
        return ReturnT.SUCCESS;
    }

}
