package com.dcjet.cs.async.jobhandler_cus.ft;

import com.dcjet.cs.gwstd.service.GwstdJobService;
import com.dcjet.cs.util.LoggerUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;

@Component
@Slf4j
public class ReportDecIHeadFtHandler {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private GwstdJobService gwstdJobService;

    @XxlJob("reportDecIHeadFtHandler")
    public ReturnT<String> execute(String strSystemCode)  {
        LoggerUtil.logInfo(logger,"=================报表中心-进口预录单表头(丰田)更新开始=================");
        gwstdJobService.reportDecIHeadFt();
        LoggerUtil.logInfo(logger,"=================报表中心-进口预录单表头(丰田)更新结束=================");
        return ReturnT.SUCCESS;
    }
}
