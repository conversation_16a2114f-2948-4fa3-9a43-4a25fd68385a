package com.dcjet.cs.async.jobhandler_cus.optorun;

import com.dcjet.cs.gwstd.service.GwstdJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class BalanceStatHandler {

    @Resource
    private GwstdJobService gwstdJobService;

    @XxlJob("balanceStatHandler")
    public ReturnT<String> execute(String strSystemCode)  {
        log.info("=================光驰平衡表核算任务开始=================");
        gwstdJobService.optorunBalance();
        log.info("=================光驰平衡表核算任务结束=================");
        return ReturnT.SUCCESS;
    }
}
