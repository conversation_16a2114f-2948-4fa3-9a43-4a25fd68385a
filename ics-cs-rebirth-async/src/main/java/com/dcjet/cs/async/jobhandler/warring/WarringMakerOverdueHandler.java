package com.dcjet.cs.async.jobhandler.warring;

import com.dcjet.cs.war.service.WarringMakerOverdueService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class WarringMakerOverdueHandler {
    @Resource
    private WarringMakerOverdueService warringMakerOverdueService;

    @XxlJob("warringMakerOverdueHandler")
    public ReturnT<String> execute(String strSystemCode) {
        log.info("=================进口制单超期预警分析任务开始=================");
        warringMakerOverdueService.checkData("");
        log.info("=================进口制单超期预警分析任务结束=================");
        return ReturnT.SUCCESS;
    }
}
