package com.dcjet.cs.async.jobhandler.clear;

import com.dcjet.cs.clear.ClearHistoryDataService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 关务数据清理
 * @author: WJ
 * @createDate: 2021/3/17 17:35
 */
@Component
@Slf4j
public class ClearHistoryDataHandler {
    @Resource
    private ClearHistoryDataService clearHistoryDataService;

    @XxlJob("clearHistoryData")
    public ReturnT<String> execute(String strSystemCode) {
        log.info("=================clearHistoryData开始=================");
        clearHistoryDataService.clearData();
        log.info("=================clearHistoryData结束=================");
        return ReturnT.SUCCESS;
    }
}
