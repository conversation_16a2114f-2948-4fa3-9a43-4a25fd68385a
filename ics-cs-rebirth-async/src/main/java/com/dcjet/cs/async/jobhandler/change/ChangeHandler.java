package com.dcjet.cs.async.jobhandler.change;

import com.dcjet.cs.mat.service.ChangeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 更新申报要素变更提醒
 */
@Component
@Slf4j
public class ChangeHandler {
    @Resource
    private ChangeService changeService;

    @XxlJob("gwChangeHandler")
    public ReturnT<String> execute(String strSystemCode) throws Exception {
        log.info("=================更新申报要素开始开始=================");
        changeService.updateChange();
        log.info("=================更新申报要素开始结束=================");
        return ReturnT.SUCCESS;
    }
}
