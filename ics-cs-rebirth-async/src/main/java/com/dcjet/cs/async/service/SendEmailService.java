package com.dcjet.cs.async.service;

import com.dcjet.cs.common.dao.GwMailSendTaskHeadMapper;
import com.dcjet.cs.common.model.GwMailSendTaskAttach;
import com.dcjet.cs.common.model.GwMailSendTaskHead;
import com.dcjet.cs.mail.model.AttachmentModel;
import com.dcjet.cs.mail.model.SendAutoEmailApi;
import com.dcjet.cs.mail.service.MailHelperService;
import com.dcjet.cs.mail.service.SendEmailUtil;
import com.dcjet.cs.util.Constants;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileConfig;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @Auther: zhuhui
 * @Date: 2019/5/28 15:30
 * @Description:
 */
@Component
public class SendEmailService {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Value("${fastdfs.http.url:}")
    private String fastDfsIp;

    @Resource(name = "xdoFileConfig")
    private XdoFileConfig xdoFileConfig;

    @Resource(name = "tempXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Value("${gw.email.sendType:}")
    private String sendType ;
    @Value("${gw.email.sendEmail:}")
    private String sendEmail ;
    @Value("${gw.email.sendPwd:}")
    private String sendPwd ;
    @Value("${gw.email.sendPort:}")
    private String sendPort ;
    @Value("${gw.email.sendSmtp:}")
    private String sendSmtp ;
    @Value("${dc.export.temp:}")
    private String tempPath;

    @Resource
    private SendEmailUtil sendEmailUtil;
    @Resource
    private GwMailSendTaskHeadMapper gwMailSendTaskHeadMapper;
    @Resource
    private MailHelperService mailHelperService;

    public void sendMail() throws Exception {
        /*
        获取邮件待发送记录及附件信息
         */
        String lockMark = UUID.randomUUID().toString();

        gwMailSendTaskHeadMapper.updateLockMark(lockMark, "0");
        GwMailSendTaskHead gwMailSendTaskHead = new GwMailSendTaskHead();
        gwMailSendTaskHead.setLockMark(lockMark);
        List<GwMailSendTaskHead> gwMailSendTaskHeadList = gwMailSendTaskHeadMapper.getList(gwMailSendTaskHead);
        List<GwMailSendTaskAttach> gwMailSendTaskAttachList = gwMailSendTaskHeadMapper.getAttachList(lockMark);
        for (GwMailSendTaskHead gwMailSendTaskHead1 : gwMailSendTaskHeadList) {
            List<GwMailSendTaskAttach> nowAttachList = gwMailSendTaskAttachList.stream().filter(x -> x.getHeadId().equals(gwMailSendTaskHead1.getSid())).collect(Collectors.toList());
            triggerSendEmail(gwMailSendTaskHead1, nowAttachList);
        }
    }

    /**
     * 功能描述
     *
     * @param gwMailSendTaskHead   邮件发送相关信息
     * @param gwMailSendTaskAttach 邮件发送附件信息
     * @return void
     * <AUTHOR>
     * @date 2021-11-02
     * @version 1.0
     */
    public void triggerSendEmail(GwMailSendTaskHead gwMailSendTaskHead, List<GwMailSendTaskAttach> gwMailSendTaskAttach) throws Exception {
        List<AttachmentModel> attachmentModelList = new ArrayList<>();
        for (GwMailSendTaskAttach gwMailSendTaskAttach1 : gwMailSendTaskAttach) {
            // gwMailSendTaskAttach1.setFileName(gwMailSendTaskAttach1.getFileName().substring(0,gwMailSendTaskAttach1.getFileName().indexOf(".")));
            //邮件附件信息
            if (StringUtils.isNotBlank(gwMailSendTaskAttach1.getUrl())) {
                AttachmentModel attachmentModel = null;
                //如果是华为云
                if (gwMailSendTaskAttach1.getUrl().contains("HUAWEI")) {
                    XdoFileConfig.Config config = xdoFileConfig.getConfigs().stream().filter((XdoFileConfig.Config s) -> s.getName().equals(fileHandler.instanceName)).findFirst().get();
                    attachmentModel = new AttachmentModel(gwMailSendTaskAttach1.getFileName()
                            , config.getHuaweiEndPoint()
                            , config.getHuaweiBucket()
                            , gwMailSendTaskAttach1.getUrl());
                }
                //如果是天翼云
                else if (gwMailSendTaskAttach1.getUrl().contains("TIANYI")) {
                    XdoFileConfig.Config config = xdoFileConfig.getConfigs().stream().filter((XdoFileConfig.Config s) -> s.getName().equals(fileHandler.instanceName)).findFirst().get();
                    attachmentModel = new AttachmentModel(gwMailSendTaskAttach1.getFileName()
                            , config.getTianyiEndPoint()
                            , config.getTianyiBucket()
                            , gwMailSendTaskAttach1.getUrl());
                }
                //如果是原文件服务器，增加前缀fastdfs拼装路径
                else if (gwMailSendTaskAttach1.getUrl().startsWith("group1")) {
                    String tempPath = String.format("%s/%s", fastDfsIp, gwMailSendTaskAttach1.getUrl());
                    if (StringUtils.isNotBlank(fastDfsIp)) {
                        if (!fastDfsIp.startsWith("http://".toLowerCase())) {
                            tempPath = String.format("http://%s/%s", fastDfsIp, gwMailSendTaskAttach1.getUrl());
                        }
                    }
                    attachmentModel = new AttachmentModel(gwMailSendTaskAttach1.getFileName(), tempPath);
                }
                //其他情况(定制项目)，直接路径
                else {
                    attachmentModel = new AttachmentModel(gwMailSendTaskAttach1.getFileName()
                            , String.format(tempPath+"%s", gwMailSendTaskAttach1.getUrl()));
                }
                attachmentModelList.add(attachmentModel);
            }
        }
        logger.info(String.format("%s**********发送邮件，收件人:%s**********", Constants.WARING_EMAIL, gwMailSendTaskHead.getRecipient()));
        SendAutoEmailApi sendAutoEmail = new SendAutoEmailApi();
        sendAutoEmail.setAddressee(gwMailSendTaskHead.getRecipient());
        sendAutoEmail.setTitle(gwMailSendTaskHead.getSubject());
        sendAutoEmail.setContent(gwMailSendTaskHead.getBody());
        sendAutoEmail.setSendId(gwMailSendTaskHead.getSender());
        sendAutoEmail.setSystemName("Apollo");
        sendAutoEmail.setIsHtml(gwMailSendTaskHead.getIsHtml());
        sendAutoEmail.setEmailAttachments(attachmentModelList);
        if(StringUtils.isNotBlank(sendType)&&"API".equals(sendType.toUpperCase())) {
            String message = sendEmailUtil.sendContentAttachApi(sendAutoEmail);

//            String message = sendEmailUtil.sendContentAttach(
//                    gwMailSendTaskHead.getSubject()
//                    , gwMailSendTaskHead.getRecipient()
//                    , gwMailSendTaskHead.getCc()
//                    , gwMailSendTaskHead.getBody()
//                    , attachmentModelList
//                    , false);

            if (message.contains(xdoi18n.XdoI18nUtil.t("提交成功"))) {
                gwMailSendTaskHead.setState("2");
                message = null;
            } else {
                gwMailSendTaskHead.setState("3");
            }
            gwMailSendTaskHead.setErrMess(message);
        }
        else if(StringUtils.isNotBlank(sendType)&&"LOCAL".equals(sendType.toUpperCase())) {
//        //发送邮件功能
        List<String> recipientList = new ArrayList<>(Arrays.asList(gwMailSendTaskHead.getRecipient().split(";")));

        MailHelperService sendMailBySSL = new MailHelperService(sendSmtp, sendPort, sendEmail, sendPwd, recipientList, gwMailSendTaskHead.getSubject(), gwMailSendTaskHead.getBody(), attachmentModelList,gwMailSendTaskHead.getIsHtml());
        ResultObject<String> emailStatus = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("邮件发送成功！"));
        emailStatus = sendMailBySSL.sendMail4all();
            if (emailStatus.isSuccess()) {
                gwMailSendTaskHead.setState("2");
            } else {
                gwMailSendTaskHead.setState("3");
            }
            gwMailSendTaskHead.setErrMess(emailStatus.getMessage());
        }
        else
        {
            gwMailSendTaskHead.setState("3");
            gwMailSendTaskHead.setErrMess(xdoi18n.XdoI18nUtil.t("发送邮件类型错误！API：标准云端发送，LOCAL：本地发送"));
        }
        gwMailSendTaskHeadMapper.updateByPrimaryKeySelective(gwMailSendTaskHead);
    }

}
