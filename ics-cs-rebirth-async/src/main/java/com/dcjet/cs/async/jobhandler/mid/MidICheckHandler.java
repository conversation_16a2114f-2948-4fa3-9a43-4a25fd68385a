package com.dcjet.cs.async.jobhandler.mid;

import com.dcjet.cs.gwstd.service.GwstdJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class MidICheckHandler {
    
    @Resource
    private GwstdJobService gwstdJobService;
    
    @XxlJob("midICheckHandler")
    public ReturnT<String> execute(String strSystemCode)  {
        log.info("=================中期核查进口物料核对任务开始=================");
        gwstdJobService.midICheck();
        log.info("=================中期核查进口物料核对任务结束=================");
        return ReturnT.SUCCESS;
    }
}
