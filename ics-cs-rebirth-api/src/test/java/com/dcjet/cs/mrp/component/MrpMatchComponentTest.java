package com.dcjet.cs.mrp.component;


import com.dcjet.cs.dto.mrp.MrpMatchStartParam;
import com.dcjet.cs.erp.dao.DecIBillHeadMapper;
import com.dcjet.cs.erp.dao.DecIBillListMapper;
import com.dcjet.cs.erp.model.DecIBillHead;
import com.dcjet.cs.erp.model.DecIBillList;
import com.dcjet.cs.mrp.dao.MrpMatchMapper;
import com.dcjet.cs.mrp.model.MrpMatch;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ConstantsStatus;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Supplier;

@RunWith(SpringRunner.class)
@SpringBootTest
public class MrpMatchComponentTest {

    @MockBean
    private DecIBillListMapper decIBillListMapper;

    @MockBean
    private DecIBillHeadMapper decIBillHeadMapper;

    @MockBean
    private MrpMatchMapper mrpMatchMapper;

    @Resource
    private MrpMatchComponent mrpMatchComponent;

    @Test
    public void matchBillListTest() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        MrpMatchStartParam param = new MrpMatchStartParam();
        String headId = UUID.randomUUID().toString();
        List<DecIBillList> iBillList = new ArrayList<>(2);
        DecIBillList decIBillList = new DecIBillList();
        decIBillList.setHeadId(headId);
        iBillList.add(decIBillList);
        decIBillList = new DecIBillList();
        decIBillList.setHeadId(headId);
        iBillList.add(decIBillList);
        Mockito.when(decIBillListMapper.getMrpBillList(param)).thenReturn(iBillList);

        DecIBillHead head = new DecIBillHead();
        head.setSid(headId);
        head.setEmsListNo(ConstantsStatus.STATUS_1);
        Mockito.when(decIBillHeadMapper.selectByPrimaryKey(head)).thenReturn(head);

        Mockito.when(mrpMatchMapper.sumByBillListSid("1", "2"))
                .thenReturn(new BigDecimal("100"));

        Map<String, BigDecimal> billListRemainMap = new HashMap<>(Constants.LIST_INITIAL_CAPACITY);

        Method m = mrpMatchComponent.getClass().getDeclaredMethod("matchBillList", Supplier.class, MrpMatchStartParam.class, Map.class);
        m.setAccessible(true);
        Supplier supplier = () -> fromPrice();
        List<MrpMatch> matchList = (List<MrpMatch>) m.invoke(mrpMatchComponent, supplier, param,  billListRemainMap);

    }


    private MrpMatch fromPrice() {
        MrpMatch match = new MrpMatch();
        match.setQty(new BigDecimal("100"));
        match.setOriginQty(BigDecimal.ZERO);
        match.setOriginRemainQty(BigDecimal.ZERO);
        match.setMatchQty(BigDecimal.ZERO);
        match.setOriginMrpQty(BigDecimal.ZERO);
        match.setRemainQty(new BigDecimal("100"));
        return match;
    }

}
