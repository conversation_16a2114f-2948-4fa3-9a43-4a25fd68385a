package com.dcjet.cs.tariff.model;

import org.junit.Test;

import java.math.BigDecimal;

public class TariffModelTest {

    @Test
    public void getOptimalRateTest() {
        /***
         * 料关税率取值逻辑
         * 按照企业料号对应的“HS编码+原产国”为关键字，有特别协定的优先取特别协定；如无特别协定，按协定、暂定、最惠、取最低值
         * 如原产国代码为701（国别不详），取普通税率；
         * 如原产国为142（中国）的，“暂定、最惠国”取最低值。
         */

        TariffModel model = new TariffModel();
        model.setAddRate(new BigDecimal("0.13"));
        model.setAgreementRate(new BigDecimal("0.04"));

    }

}
