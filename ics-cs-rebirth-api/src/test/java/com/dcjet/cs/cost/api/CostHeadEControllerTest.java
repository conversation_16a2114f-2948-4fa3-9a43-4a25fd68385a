package com.dcjet.cs.cost.api;

import com.dcjet.cs.basic.BaseTest;
import com.dcjet.cs.dto.cost.CostHeadEDto;
import com.dcjet.cs.dto.cost.CostHeadESearchParam;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.domain.ResultObject;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class CostHeadEControllerTest extends BaseTest {

    @Test
    public void listTest() {
        TypeReference<ResultObject<List<CostHeadEDto>>> listTypeReference = new TypeReference<ResultObject<List<CostHeadEDto>>>() {};
        CostHeadESearchParam searchParam = new CostHeadESearchParam();

        String json = post("/v1/cost/heade/list", searchParam);
        ResultObject<List<CostHeadEDto>> listResultObject = JsonObjectMapper.getInstance().fromJson(json, listTypeReference);
        Assert.assertTrue(listResultObject.isSuccess());
        Assert.assertTrue(listResultObject.getData().size() > 0);
    }
}
