package com.dcjet.cs.api_cus.panasonic;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto_cus.panasonic.DecErpEFreightListDto;
import com.dcjet.cs.dto_cus.panasonic.DecErpEFreightListParam;
import com.dcjet.cs_cus.panasonic.service.DecErpEFreightListService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.google.common.base.Strings;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-7-13
 */
@RestController
@RequestMapping("v1/decErpEFreightList")
@Api(tags = "出口运费管理接口")
public class DecErpEFreightListController {
    @Resource
    private DecErpEFreightListService decErpEFreightListService;
    @Resource
    private ExcelService excelService;
    @Resource
    private CommonService commonService;

    /**
     * @param decErpEFreightListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<DecErpEFreightListDto>> getListPaged(@RequestBody DecErpEFreightListParam decErpEFreightListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<DecErpEFreightListDto>> paged =
                decErpEFreightListService.getListPaged(decErpEFreightListParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param sid
     * @param decErpEFreightListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<DecErpEFreightListDto> update(@PathVariable String sid, @Valid @RequestBody DecErpEFreightListParam decErpEFreightListParam, UserInfoToken userInfo) {
        decErpEFreightListParam.setSid(sid);
        DecErpEFreightListDto decErpEFreightListDto = decErpEFreightListService.update(decErpEFreightListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (decErpEFreightListDto != null) {
            resultObject.setData(decErpEFreightListDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("更新失败!"));
        }
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<DecErpEFreightListParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<DecErpEFreightListDto> decErpEFreightListDtos = decErpEFreightListService.selectAll(exportParam.getExportColumns(), userInfo);
        decErpEFreightListDtos = convertForPrint(decErpEFreightListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), decErpEFreightListDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<DecErpEFreightListDto> convertForPrint(List<DecErpEFreightListDto> list) {
        for (DecErpEFreightListDto item : list) {
            if (item.getTrafMode() != null) {
                item.setTrafMode(commonService.convertPCode(item.getTrafMode(), PCodeType.TRANSF));
            }
            if (!Strings.isNullOrEmpty(item.getTransMode())) {
                item.setTransMode(commonService.convertPCode(item.getTransMode(), PCodeType.TRANSAC));
            }
            if (!Strings.isNullOrEmpty(item.getCurr())) {
                item.setCurr(commonService.convertPCode(item.getCurr(), PCodeType.CURR_OUTDATED));
            }
        }
        return list;
    }
}
