package com.dcjet.cs.api_cus.daikin;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto_cus.daikin.VCollectionHeadListDto;
import com.dcjet.cs.dto_cus.daikin.VCollectionHeadListParam;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs_cus.daikin.service.VCollectionHeadListService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@RestController
@RequestMapping("v1/vCollectionManagementHeadlist")
@Api(tags = "收汇报表接口")
public class VCollectionManagementHeadListController extends BaseController {
    @Resource
    private VCollectionHeadListService vCollectionManagementHeadListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param vCollectionManagementHeadListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("收汇报表分页查询接口")
    @PostMapping("list")
    public ResultObject<List<VCollectionHeadListDto>> getListPaged(@RequestBody VCollectionHeadListParam vCollectionManagementHeadListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<VCollectionHeadListDto>> paged = vCollectionManagementHeadListService.getListPaged(vCollectionManagementHeadListParam, pageParam, userInfo);
        return paged;
    }

    /**
     * 收费管理表体Excel数据导出
     *
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("收汇报表Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity entryExport(@Valid @RequestBody BasicExportParam<VCollectionHeadListParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<VCollectionHeadListDto> vCollectionManagementHeadListDtos = vCollectionManagementHeadListService.selectAll(exportParam.getExportColumns(), userInfo);
        vCollectionManagementHeadListDtos = entryConvertForPrint(vCollectionManagementHeadListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), vCollectionManagementHeadListDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<VCollectionHeadListDto> entryConvertForPrint(List<VCollectionHeadListDto> list) {
        for (VCollectionHeadListDto item : list) {

            if (item.getCurr() != null) {
                item.setCurr(item.getCurr() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurr()));
            }
        }
        return list;
    }
}
