package com.dcjet.cs.api_cus.knorr;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelImportService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.base.ImportResult;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto_cus.preerp.*;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs_cus.preerp.service.PreDecErpApproveService;
import com.dcjet.cs_cus.preerp.service.PreDecErpHeadService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-3-8
 */
@RestController
@RequestMapping("v1/preDecErpHead")
@Api(tags = "预报单")
public class PreDecErpHeadController extends BaseController {

    @Autowired
    private PreDecErpHeadService preDecErpHeadService;

    @Resource
    private ExcelImportService<PreDecErpHeadSupplementImportParam> excelImportService;

    @Resource
    private PreDecErpApproveService preDecErpApproveService;
    @Resource
    private ExcelService excelService;
    @Resource
    private CommonService commonService;

    /**
     * @param preDecErpHeadParam
     * @param pageParam
     * @param token
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<PreDecErpHeadDto>> getListPaged(@RequestBody PreDecErpHeadSearchParam preDecErpHeadParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<PreDecErpHeadDto>> paged = preDecErpHeadService.getListPaged(preDecErpHeadParam, pageParam, token);
        return paged;
    }

    @ApiOperation("关务分页查询接口")
    @PostMapping("gwlist")
    public ResultObject<List<PreDecErpHeadDto>> getGwList(@RequestBody PreDecErpHeadSearchParam preDecErpHeadParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<PreDecErpHeadDto>> paged = preDecErpHeadService.getGwList(preDecErpHeadParam, pageParam, token);
        return paged;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping("/exportList")
    public ResponseEntity exportList(@Valid @RequestBody BasicExportParam<PreDecErpHeadSearchParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<PreDecErpHeadDto> preDecErpHeadDtos = preDecErpHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        if (preDecErpHeadDtos != null) {
            preDecErpHeadDtos = convertForPrint(preDecErpHeadDtos);
        }
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), preDecErpHeadDtos);
    }

    /**
     * 预报分单导出
     *
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("预报分单导出Excel数据导出接口")
    @PostMapping("/gwExportList")
    public ResponseEntity gwExportList(@Valid @RequestBody BasicExportParam<PreDecErpHeadSearchParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<PreDecErpHeadDto> preDecErpHeadDtos = preDecErpHeadService.gwSelectAll(exportParam.getExportColumns(), userInfo);
        if (preDecErpHeadDtos != null) {
            preDecErpHeadDtos = convertForPrint(preDecErpHeadDtos);
        }
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), preDecErpHeadDtos);
    }

    public List<PreDecErpHeadDto> convertForPrint(List<PreDecErpHeadDto> list) {
        for (PreDecErpHeadDto item : list) {
            item.setTrafMode(commonService.convertPCode(item.getTrafMode(), PCodeType.TRANSF));
            item.setDespPort(commonService.convertPCode(item.getDespPort(), PCodeType.PORT_LIN));
            item.setDestPort(commonService.convertPCode(item.getDestPort(), PCodeType.PORT_LIN));
            item.setEntryPort(commonService.convertPCode(item.getEntryPort(), PCodeType.CIQ_ENTY_PORT));
            item.setOtherCurr(commonService.convertPCode(item.getOtherCurr(), PCodeType.CURR_OUTDATED));
            item.setFeeCurr(commonService.convertPCode(item.getFeeCurr(), PCodeType.CURR_OUTDATED));
            item.setTradeCountry(commonService.convertPCode(item.getTradeCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setWrapType(commonService.convertPCode(item.getWrapType(), PCodeType.WRAP));
            if (StringUtils.isNotBlank(item.getForwardName())) {
                item.setForwardCode(item.getForwardCode() + " " + item.getForwardName());
            }
            if (StringUtils.isNotBlank(item.getCustomerName())) {
                item.setCustomerCode(item.getCustomerCode() + " " + item.getCustomerName());
            }
            if (StringUtils.isNotBlank(item.getOverseasShipperName())) {
                item.setOverseasShipper(item.getOverseasShipper() + " " + item.getOverseasShipperName());
            }
            if (item.getStatus() != null) {
                item.setStatus(CommonEnum.PRE_STAUTS_ENUM.getValue(item.getStatus()));
            }
            if (StringUtils.isNotBlank(item.getContainerLcl())) {
                if (StringUtils.equals(ConstantsStatus.STATUS_0, item.getContainerLcl())) {
                    item.setContainerLcl("0 整箱");
                } else if (StringUtils.equals(ConstantsStatus.STATUS_1, item.getContainerLcl())) {
                    item.setContainerLcl("1 拼箱");
                }
            }
        }
        return list;
    }

    /***
     *
     * @param sid
     * @param token
     * @return
     */
    @ApiModelProperty("获取详细信息")
    @GetMapping("/{sid}")
    public ResultObject<PreDecErpHeadDto> get(@PathVariable String sid, UserInfoToken token) {
        PreDecErpHeadDto dto = preDecErpHeadService.get(sid, token);
        return ResultObject.createInstance(dto, 1);
    }

    @ApiOperation("审批情况")
    @GetMapping("/approve/{headId}")
    public ResultObject<List<PreDecErpApproveDto>> approveList(@PathVariable String headId, UserInfoToken token) {
        List<PreDecErpApproveDto> list = preDecErpApproveService.list(headId, token);
        ResultObject result = ResultObject.createInstance(true);
        result.setData(list);
        return result;
    }

    /**
     * @param preDecErpHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<PreDecErpHeadDto> insert(@Valid @RequestBody PreDecErpHeadParam preDecErpHeadParam, UserInfoToken userInfo) {
        ResultObject<PreDecErpHeadDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        PreDecErpHeadDto preDecErpHeadDto = preDecErpHeadService.insert(preDecErpHeadParam, userInfo);
        if (preDecErpHeadDto != null) {
            resultObject.setData(preDecErpHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    @ApiOperation("复制")
    @PostMapping("copy/{sid}")
    public ResultObject<PreDecErpHeadDto> copy(@PathVariable String sid, UserInfoToken token) {
        return ResultObject.createInstance(preDecErpHeadService.copy(sid, token), 1);
    }

    @ApiOperation("提交预报单")
    @PostMapping("submit/{idList}")
    public ResultObject<Boolean> submit(@PathVariable List<String> idList, UserInfoToken token) {
        preDecErpHeadService.submit(idList, token);
        return ResultObject.createInstance(Boolean.TRUE);
    }

    @ApiOperation("退回预报单")
    @PostMapping("back/{idList}")
    public ResultObject<Boolean> back(@PathVariable List<String> idList, @Valid @RequestBody PreDecErpHeadBackParam param, UserInfoToken token) {
        preDecErpHeadService.back(idList, param, token);
        return ResultObject.createInstance(Boolean.TRUE);
    }

    @ApiOperation("分单")
    @PostMapping("/distribute/{idList}")
    public ResultObject<Boolean> distribute(@PathVariable List<String> idList, @Valid @RequestBody PreDecErpHeadDistributeParam param, UserInfoToken token) {
        preDecErpHeadService.distribute(idList, param, token);
        return ResultObject.createInstance(Boolean.TRUE);
    }

    /**
     * @param sid
     * @param preDecErpHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<PreDecErpHeadDto> update(@PathVariable String sid, @Valid @RequestBody PreDecErpHeadParam preDecErpHeadParam, UserInfoToken userInfo) {
        PreDecErpHeadDto preDecErpHeadDto = preDecErpHeadService.update(sid, preDecErpHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (preDecErpHeadDto != null) {
            resultObject.setData(preDecErpHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    @ApiOperation("补录入信息")
    @PutMapping("supplement/{sid}")
    public ResultObject<PreDecErpHeadSupplementParam> supplement(@PathVariable String sid, @Valid @RequestBody PreDecErpHeadSupplementParam param, UserInfoToken token) {
        PreDecErpHeadDto dto = preDecErpHeadService.supplement(sid, param, token);
        return ResultObject.createInstance(dto, 1);
    }

    @ApiModelProperty("汇总导入修改调整数量")
    @PostMapping("/import/supplement")
    public ResultObject<ImportResult> importUpdateAdjustmentQty(MultipartFile file, ExcelImportParam param, UserInfoToken token) throws IOException, IllegalAccessException, InstantiationException {
        ImportResult result = excelImportService.importExcel(file.getInputStream(), preDecErpHeadService, param, PreDecErpHeadSupplementImportParam.class, token);
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(result);
        return resultObject;
    }

    @ApiModelProperty("导入正确的数据")
    @PostMapping("/import/supplement/{key}")
    public ResultObject<Integer> importCorrect(@PathVariable String key, UserInfoToken token) {
        int count = excelImportService.importCorrectData(preDecErpHeadService, key, token, PreDecErpHeadSupplementImportParam.class);
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(count);
        return resultObject;
    }

    @ApiModelProperty("导出错误的数据")
    @PostMapping("/export/supplement/{key}")
    public ResponseEntity exportError(@PathVariable String key) throws Exception {
        return excelImportService.exportErrorData(key);
    }

    @ApiModelProperty("导出导入的模版")
    @PostMapping("/export/supplement/tpl")
    public ResponseEntity exportTpl(@RequestParam(defaultValue = "导入模版") String name) throws Exception {
        return excelImportService.exportTpl(name, PreDecErpHeadSupplementImportParam.class);
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        preDecErpHeadService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * 校验分单号重复
     *
     * @param preDecErpHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("校验分单号重复接口")
    @PostMapping("uniqueCheck")
    public ResultObject uniqueCheck(@Valid @RequestBody PreDecErpHeadParam preDecErpHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = preDecErpHeadService.upiqueCheck(preDecErpHeadParam, userInfo);
        return resultObject;
    }

    @ApiOperation("关务分页查询接口")
    @PostMapping("getContainerlist")
    public ResultObject<List<PreDecErpContainerDto>> getContainerlist(@RequestBody PreDecErpHeadSearchParam preDecErpHeadParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<PreDecErpContainerDto>> paged = preDecErpHeadService.getContainerlist(preDecErpHeadParam, pageParam, token);
        return paged;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping("/exportContainerList")
    public ResponseEntity exportContainerList(@Valid @RequestBody BasicExportParam<PreDecErpHeadSearchParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<PreDecErpContainerDto> preDecErpHeadDtos = preDecErpHeadService.selectContainerAll(exportParam.getExportColumns(), userInfo);
        if (preDecErpHeadDtos != null) {
            preDecErpHeadDtos = convertForCPrint(preDecErpHeadDtos);
        }
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), preDecErpHeadDtos);
    }

    public List<PreDecErpContainerDto> convertForCPrint(List<PreDecErpContainerDto> list) {
        for (PreDecErpContainerDto item : list) {
            if (StringUtils.isNotBlank(item.getContainerModel())) {
                item.setContainerModel(item.getContainerModel() + "|" + CommonEnum.ContainerEnum.getValue(item.getContainerModel()));
            }
            if (StringUtils.isNotBlank(item.getContainerLcl())) {
                if (StringUtils.equals(ConstantsStatus.STATUS_0, item.getContainerLcl())) {
                    item.setContainerLcl("0 否");
                } else if (StringUtils.equals(ConstantsStatus.STATUS_1, item.getContainerLcl())) {
                    item.setContainerLcl("1 是");
                }
            }
        }
        return list;
    }
}
