package com.dcjet.cs.api_cus.daikin;

import com.dcjet.cs.dto_cus.daikin.OrderPriceDto;
import com.dcjet.cs.dto_cus.daikin.OrderPriceParam;
import com.dcjet.cs_cus.daikin.service.OrderPriceService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
@RestController
@RequestMapping("v1/orderPrice")
@Api(tags = "单价维护接口")
public class OrderPriceController extends BaseController {
    @Resource
    private OrderPriceService orderPriceService;

    /**
     * @param orderPriceParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<OrderPriceDto>> getListPaged(@RequestBody OrderPriceParam orderPriceParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<OrderPriceDto>> paged = orderPriceService.getListPaged(orderPriceParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param orderPriceParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<OrderPriceDto> insert(@Valid @RequestBody OrderPriceParam orderPriceParam, UserInfoToken userInfo) {
        ResultObject<OrderPriceDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        OrderPriceDto orderPriceDto = orderPriceService.insert(orderPriceParam, userInfo);
        if (orderPriceDto != null) {
            resultObject.setData(orderPriceDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param orderPriceParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<OrderPriceDto> update(@PathVariable String sid, @Valid @RequestBody OrderPriceParam orderPriceParam, UserInfoToken userInfo) {
        orderPriceParam.setSid(sid);
        OrderPriceDto orderPriceDto = orderPriceService.update(orderPriceParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (orderPriceDto != null) {
            resultObject.setData(orderPriceDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        orderPriceService.delete(sids, userInfo);
        return resultObject;
    }
}
