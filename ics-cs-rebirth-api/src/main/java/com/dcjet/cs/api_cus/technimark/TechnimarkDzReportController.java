package com.dcjet.cs.api_cus.technimark;

import com.dcjet.cs.dto_cus.technimark.TechnimarkDzReportDto;
import com.dcjet.cs.dto_cus.technimark.TechnimarkDzReportParam;
import com.dcjet.cs_cus.technimark.service.TechnimarkDzReportService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-11-18
 */
@RestController
@RequestMapping("v1/technimarkDzReport")
@Api(tags = "泰马克单证统计报表接口")
public class TechnimarkDzReportController extends BaseController {
    @Resource
    private TechnimarkDzReportService technimarkDzReportService;

    /**
     * @param technimarkDzReportParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<TechnimarkDzReportDto>> getListPaged(@RequestBody TechnimarkDzReportParam technimarkDzReportParam, PageParam pageParam, UserInfoToken userInfo) {
        technimarkDzReportParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<TechnimarkDzReportDto>> paged = technimarkDzReportService.getListPaged(technimarkDzReportParam, pageParam);
        return paged;
    }
}
