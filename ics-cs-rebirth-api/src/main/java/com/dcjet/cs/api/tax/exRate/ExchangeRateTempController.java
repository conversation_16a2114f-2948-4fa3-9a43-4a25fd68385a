package com.dcjet.cs.api.tax.exRate;

import com.dcjet.cs.dto.tax.exRate.ExchangeRateTempDto;
import com.dcjet.cs.dto.tax.exRate.ExchangeRateTempParam;
import com.dcjet.cs.tax.exRate.service.ExchangeRateTempService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-12-5
 */
@RestController
@RequestMapping("v1/exchangeRateTemp")
@Api(tags = "税金管理-临时汇率")
public class ExchangeRateTempController {
    @Resource
    private ExchangeRateTempService exchangeRateTempService;

    /**
     * @param exchangeRateTempParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<ExchangeRateTempDto>> getListPaged(@RequestBody ExchangeRateTempParam exchangeRateTempParam, PageParam pageParam, UserInfoToken userInfo) {
        // exchangeRateTempParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<ExchangeRateTempDto>> paged = exchangeRateTempService.getListPaged(exchangeRateTempParam, pageParam);
        return paged;
    }

    /**
     * @param exchangeRateTempParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<ExchangeRateTempDto> insert(@Valid @RequestBody ExchangeRateTempParam exchangeRateTempParam, UserInfoToken userInfo) {
        ResultObject<ExchangeRateTempDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        ExchangeRateTempDto exchangeRateTempDto = exchangeRateTempService.insert(exchangeRateTempParam, userInfo);
        if (exchangeRateTempDto != null) {
            resultObject.setData(exchangeRateTempDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param exchangeRateTempParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<ExchangeRateTempDto> update(@PathVariable String sid, @Valid @RequestBody ExchangeRateTempParam exchangeRateTempParam, UserInfoToken userInfo) {
        exchangeRateTempParam.setSid(sid);
        ExchangeRateTempDto exchangeRateTempDto = exchangeRateTempService.update(exchangeRateTempParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (exchangeRateTempDto != null) {
            resultObject.setData(exchangeRateTempDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("更新失败!"));
        }
        return resultObject;
    }
}
