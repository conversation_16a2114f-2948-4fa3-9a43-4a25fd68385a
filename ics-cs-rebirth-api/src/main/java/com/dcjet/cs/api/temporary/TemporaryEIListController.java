package com.dcjet.cs.api.temporary;

import com.dcjet.cs.dto.temporary.*;
import com.dcjet.cs.temporary.service.TemporaryEIListService;
import com.dcjet.cs.temporary.service.TemporaryEListService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 复运进境表体
 * <p>
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-04-16
 */
@Api(tags = "暂时进出境-复运进境表体")
@RestController
@RequestMapping("v1/temporary/eilist")
public class TemporaryEIListController {

    @Resource
    private TemporaryEIListService temporaryEIListService;

    @Resource
    private TemporaryEListService temporaryelistService;

    /**
     * 查询接口
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    @ApiOperation("列表")
    @PostMapping("list")
    public ResultObject<List<TemporaryEIListDto>> list(@RequestBody TemporaryEIListSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<TemporaryEIListDto>> paged = temporaryEIListService.list(searchParam, pageParam, token);
        return paged;
    }

    /***
     * 获取详细信息
     *
     * @param sid
     * @param token
     * @return
     */
    @ApiModelProperty("详细信息")
    @GetMapping("/{sid}")
    public ResultObject<TemporaryEIListDto> get(@PathVariable String sid, UserInfoToken token) {
        TemporaryEIListDto dto = temporaryEIListService.get(sid, token);
        return ResultObject.createInstance(dto, 1);
    }

    @ApiOperation("待提取的数据")
    @PostMapping("/unextraction")
    public ResultObject<List<TemporaryEListDto>> unextraction(@RequestBody TemporaryEIListExtractionParam searchParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<TemporaryEListDto>> resultObject = temporaryelistService.unextraction(searchParam, pageParam, token);
        return resultObject;
    }

    @ApiOperation("提取数据")
    @PostMapping("/extraction")
    public ResultObject<Boolean> extraction(@RequestBody @Valid TemporaryEIListExtractionParam param, UserInfoToken token) {
        temporaryEIListService.extraction(param, token);
        return ResultObject.createInstance(true, 1);
    }

    /**
     * 新增
     *
     * @param param
     * @param token
     * @return
     */
    @ApiOperation("新增")
    @PostMapping()
    public ResultObject<TemporaryEIListDto> insert(@Valid @RequestBody TemporaryEIListParam param, UserInfoToken token) {
        ResultObject<TemporaryEIListDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        TemporaryEIListDto dto = temporaryEIListService.insert(param, token);
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * 修改
     *
     * @param sid
     * @param param
     * @param token
     * @return
     */
    @ApiOperation("修改")
    @PutMapping("{sid}")
    public ResultObject<TemporaryEIListDto> update(@PathVariable String sid, @Valid @RequestBody TemporaryEIListParam param, UserInfoToken token) {
        TemporaryEIListDto dto = temporaryEIListService.update(sid, param, token);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * 删除
     *
     * @param sids
     * @return
     */
    @ApiOperation("删除")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        temporaryEIListService.delete(sids, token);
        return resultObject;
    }
}
