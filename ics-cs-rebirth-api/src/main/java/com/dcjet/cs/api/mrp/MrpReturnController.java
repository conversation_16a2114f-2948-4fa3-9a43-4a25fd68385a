package com.dcjet.cs.api.mrp;

import com.dcjet.cs.api.base.BasicController;
import com.dcjet.cs.common.service.ExcelImportService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.base.ImportResult;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto.mrp.*;
import com.dcjet.cs.mrp.model.MrpImgExg;
import com.dcjet.cs.mrp.service.MrpReturnService;
import com.dcjet.cs.mrp.service.MrpReturnSummaryService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "内销-折料")
@RestController
@RequestMapping("v1/mrp/return")
public class MrpReturnController extends BasicController<MrpReturnService, MrpReturnDto, MrpReturnParam, MrpReturnSearchParam> {

    @Resource
    private ExcelImportService<MrpReturnSummaryParam> excelImportService;

    @Resource
    private MrpReturnSummaryService mrpReturnSummaryService;

    @ApiOperation(value = "bom完整性检查")
    @GetMapping(value = "/bom/{headId}")
    public ResultObject<Integer> bomCheck(@PathVariable String headId) {
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(service.bomCheck(headId).size());
        return resultObject;
    }

    @ApiOperation("报关单记录Excel数据导出接口")
    @GetMapping("/export/bom/{headId}")
    public ResponseEntity export(@PathVariable String headId) throws Exception {
        List<MrpImgExg> exportData = service.bomCheck(headId);
        List<KeyValuePair<String, String>> headList = Arrays.asList(
                new KeyValuePair<>("facGNo", xdoi18n.XdoI18nUtil.t("企业料号")),
                new KeyValuePair<>("exgVersion", xdoi18n.XdoI18nUtil.t("BOM版本号")));

        return excelService.exportExcel(URLEncoder.encode(xdoi18n.XdoI18nUtil.t("缺失BOM的数据"), CommonVariable.UTF8), headList, exportData);
    }

    @ApiOperation(value = "开始折料")
    @PostMapping(value = "/start/{headId}")
    public ResultObject<List<MrpImgExgDto>> bomCheck(@PathVariable String headId, @RequestBody MrpReturnStartParam param, UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance();
        Pair<List<MrpImgExgDto>, String> pair = service.start(headId, param, token);
        if (pair != null) {
            resultObject.setSuccess(false);
            resultObject.setData(pair.getLeft());
            resultObject.setMessage(pair.getRight());
        }
        return resultObject;
    }

    @ApiOperation("统计数据")
    @PostMapping("stat")
    public ResultObject<MrpReturnStatDto> stat(@RequestBody @Valid MrpReturnSearchParam searchParam, UserInfoToken token) {
        ResultObject<MrpReturnStatDto> resultObject = ResultObject.createInstance();
        resultObject.setData(service.stat(searchParam, token));
        return resultObject;
    }

    @ApiOperation(value = "汇总")
    @PostMapping(value = "/summary")
    public ResultObject<List<MrpReturnSummaryDto>> summary(@Valid @RequestBody MrpReturnSummarySearchParam param, PageParam page, UserInfoToken token) {
        return mrpReturnSummaryService.findByPage(param, page, token);
    }

    @ApiOperation(value = "汇总")
    @PostMapping(value = "/summary/stat")
    public ResultObject<MrpReturnSummaryStatDto> summaryStat(@Valid @RequestBody MrpReturnSummarySearchParam param,UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(mrpReturnSummaryService.stat(param, token));
        return resultObject;
    }

    @ApiOperation("修改指定字段的值")
    @PutMapping("/summary/{sid}/{columns}")
    public ResultObject<MrpReturnSummaryDto> updateSummary(@PathVariable String sid, @PathVariable("columns") List<String> columnList, @RequestBody @Valid MrpReturnSummaryParam param, UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(mrpReturnSummaryService.update(sid, param, columnList, token));
        return resultObject;
    }

    @ApiOperation("汇总导出")
    @PostMapping("/export/summary")
    public ResponseEntity exportSummary(@Valid @RequestBody BasicExportParam<MrpReturnSummarySearchParam> exportParam, UserInfoToken token) throws Exception {
        MrpReturnSummarySearchParam param = exportParam.getExportColumns();

        Collection<MrpReturnSummaryDto> list  = mrpReturnSummaryService.getAll(param, null, token);
        List<MrpReturnSummaryDto> data = new ArrayList<>(list);
        return excelService.exportExcel(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), data);
    }

    @ApiOperation("汇总导入修改调整数量")
    @PostMapping("/import/summary/adjustmentqty")
    public ResultObject<ImportResult> importUpdateAdjustmentQty(MultipartFile file, ExcelImportParam param, UserInfoToken token) throws IOException, InstantiationException, IllegalAccessException {
        ImportResult result = excelImportService.importExcel(file.getInputStream(), mrpReturnSummaryService, param, MrpReturnSummaryParam.class, token);
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(result);
        return resultObject;
    }

    @ApiOperation("导入正确的数据")
    @PostMapping("/import/summary/{key}")
    public ResultObject<Integer> importCorrect(@PathVariable String key, UserInfoToken token) {
        int count = excelImportService.importCorrectData(mrpReturnSummaryService, key, token, MrpReturnSummaryParam.class);
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(count);
        return resultObject;
    }

    @ApiOperation("导出错误的数据")
    @PostMapping("/export/summary/{key}")
    public ResponseEntity exportError(@PathVariable String key) throws Exception {
        return excelImportService.exportErrorData(key);
    }

    @ApiOperation("清理料号数量<=的所有数据")
    @DeleteMapping("summary/clean/qty/{headId}")
    public ResultObject<Integer> cleanFacGNoQtyLTEZero(@PathVariable String headId, UserInfoToken userToken) {
        int count = mrpReturnSummaryService.cleanFacGNoQtyLteZero(headId, userToken);
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(count);
        return resultObject;
    }

    @ApiOperation("清理零值")
    @DeleteMapping("summary/cleanZero/{headId}")
    public ResultObject<Integer> cleanQtyEqualZero(@PathVariable String headId, UserInfoToken userToken) {
        int count = mrpReturnSummaryService.cleanQtyEqualZero(headId, userToken);
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(count);
        return resultObject;
    }
}
