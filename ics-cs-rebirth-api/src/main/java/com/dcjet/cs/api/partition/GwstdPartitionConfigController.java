package com.dcjet.cs.api.partition;

import com.dcjet.cs.dto.partition.GwstdPartitionConfigDto;
import com.dcjet.cs.dto.partition.GwstdPartitionConfigParam;
import com.dcjet.cs.partition.service.GwstdPartitionConfigService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * generated by Generate dc
 * 分区配置接口
 *
 * <AUTHOR>
 * @date: 2020-12-25
 */
@RestController
@RequestMapping("v1/gwstdPartitionConfig")
@Api(tags = "分区配置接口")
public class GwstdPartitionConfigController {
    @Resource
    private GwstdPartitionConfigService gwstdPartitionConfigService;

    /**
     * 查询接口
     */
    @ApiOperation("查询接口")
    @PostMapping("list")
    public ResultObject<List<GwstdPartitionConfigDto>> getListPaged(@RequestBody GwstdPartitionConfigParam gwstdPartitionConfigParam, PageParam pageParam, UserInfoToken userInfo) {
        return gwstdPartitionConfigService.getListPaged(gwstdPartitionConfigParam, pageParam, userInfo);
    }

    /**
     * 分配分区权限
     */
    @ApiOperation("分配分区")
    @PostMapping("grant/{sids}")
    public ResultObject grant(@PathVariable List<String> sids, UserInfoToken userInfo) {
        return gwstdPartitionConfigService.addPartition(sids, userInfo.getCompany());
    }
}
