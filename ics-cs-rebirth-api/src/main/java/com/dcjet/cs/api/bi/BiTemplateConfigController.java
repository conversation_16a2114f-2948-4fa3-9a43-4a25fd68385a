package com.dcjet.cs.api.bi;

import com.dcjet.cs.bi.service.BiTemplateConfigService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.BiTemplateConfigDto;
import com.dcjet.cs.dto.bi.BiTemplateConfigParam;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 *
 * <AUTHOR>
 * @date: 2019-5-8
 */
@RestController
@RequestMapping("v1/biTemplateConfig")
@Api(tags = "企业参数库-进出口模板接口")
public class BiTemplateConfigController extends BaseController {
    @Resource
    private BiTemplateConfigService biTemplateConfigService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param biTemplateConfigParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BiTemplateConfigDto>> getListPaged(@RequestBody BiTemplateConfigParam biTemplateConfigParam,UserInfoToken userInfo) {
        biTemplateConfigParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BiTemplateConfigDto>> paged = biTemplateConfigService.getList(biTemplateConfigParam);
        return paged;
    }
    /**
     * @param biTemplateConfigParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BiTemplateConfigDto> insert(@Valid @RequestBody BiTemplateConfigParam biTemplateConfigParam, UserInfoToken userInfo) {
        ResultObject<BiTemplateConfigDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        BiTemplateConfigDto biTemplateConfigDto = biTemplateConfigService.insert(biTemplateConfigParam, userInfo);
        if (biTemplateConfigDto != null) {
            resultObject.setData(biTemplateConfigDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param biTemplateConfigParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BiTemplateConfigDto> update(@PathVariable String sid, @Valid @RequestBody BiTemplateConfigParam biTemplateConfigParam, UserInfoToken userInfo) {
        biTemplateConfigParam.setSid(sid);
        BiTemplateConfigDto biTemplateConfigDto = biTemplateConfigService.update(biTemplateConfigParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("修改成功"));
        if (biTemplateConfigDto != null) {
            resultObject.setData(biTemplateConfigDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
		 biTemplateConfigService.delete(sids);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<BiTemplateConfigParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<BiTemplateConfigDto> biTemplateConfigDtos = biTemplateConfigService.selectAll(exportParam.getExportColumns(), userInfo);
        biTemplateConfigDtos = convertForPrint(biTemplateConfigDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), biTemplateConfigDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<BiTemplateConfigDto> convertForPrint(List<BiTemplateConfigDto> list) {
        for(BiTemplateConfigDto item : list) {
        }
        return list;
    }
}
