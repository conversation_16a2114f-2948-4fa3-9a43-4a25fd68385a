package com.dcjet.cs.api.cost;

import com.dcjet.cs.cost.service.CostAuditEService;
import com.dcjet.cs.dto.cost.CostCompareEHeadDto;
import com.dcjet.cs.dto.cost.CostCompareEHeadParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:出口费用审核
 * @author: WJ
 * @createDate: 2021/8/23 14:28
 */
@Api(tags = "费用管理-出口费用审核")
@RestController
@RequestMapping("v1/cost/auditE")
public class CostAuditEController {

    @Resource
    private CostAuditEService costAuditEService;

    /**
     * 进口费用审核表头查询
     * @param costCompareEHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("auditHead")
    public ResultObject<List<CostCompareEHeadDto>> getAuditPaged(@RequestBody CostCompareEHeadParam costCompareEHeadParam,
                                                                 PageParam pageParam, UserInfoToken userInfo) {
        return costAuditEService.getAuditPaged(costCompareEHeadParam, pageParam, userInfo);
    }

    /**
     * 审核通过 2、审核退回 3
     * @param paramList
     * @param status
     * @param userInfo
     * @return
     */
    @ApiOperation("审核")
    @PostMapping("audit/{status}")
    public ResultObject<List<CostCompareEHeadDto>> audit(@RequestBody List<CostCompareEHeadParam> paramList,
                                                         @PathVariable String status, UserInfoToken userInfo) {
        return costAuditEService.audit(paramList, status, userInfo);
    }

}
