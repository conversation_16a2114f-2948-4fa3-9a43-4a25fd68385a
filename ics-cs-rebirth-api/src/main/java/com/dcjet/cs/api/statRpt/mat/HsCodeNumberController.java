package com.dcjet.cs.api.statRpt.mat;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.statRpt.mat.dto.HsCodeNumber;
import com.dcjet.cs.dto.statRpt.mat.param.HsCodeNumberParam;
import com.dcjet.cs.statRpt.mat.service.HsCodeNumberService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/statRpt/mat/hsCodeNumber")
@Api(tags = "统计报表-物料统计-物料数量-涉及商品编码数量")
public class HsCodeNumberController {

    @Resource
    private HsCodeNumberService service;

    @Resource
    private ExcelService excelService;

    @ApiOperation("分页")
    @PostMapping("list")
    public ResultObject<List<HsCodeNumber>> getList(@Valid @RequestBody HsCodeNumberParam queryParam, UserInfoToken userInfo) {
        List<HsCodeNumber> result = service.getList(queryParam, userInfo);
        return ResultObject.createInstance(result, result.size(), 1);
    }

    @ApiOperation("导出")
    @PostMapping("/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<HsCodeNumberParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<HsCodeNumber> hsCodeNumbers = service.getList(exportParam.getExportColumns(), userInfo);
        convertForPrint(hsCodeNumbers);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), hsCodeNumbers);
    }

    public void convertForPrint(List<HsCodeNumber> list) {
        list.stream().forEach(item -> {
            if (StringUtils.isNotBlank(item.getBondMark())) {
                item.setBondMark(CommonEnum.BondMarkEnum.getValue(item.getBondMark()));
            }
            if (StringUtils.isNotBlank(item.getGmark())) {
                item.setGmark(CommonEnum.GMarkEnum.getValue(item.getGmark()));
            }
        });
    }
}
