package com.dcjet.cs.api.outward;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.outward.WfSendListDto;
import com.dcjet.cs.dto.outward.WfSendListParam;
import com.dcjet.cs.outward.service.WfSendListService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-7-18
 */
@RestController
@RequestMapping("v1/wfSendList")
@Api(tags = "外发加工-外发加工发货单表体接口")
public class WfSendListController {
    @Resource
    private WfSendListService wfSendListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param wfSendListParam
     * @param pageParam
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<WfSendListDto>> getListPaged(@RequestBody WfSendListParam wfSendListParam, PageParam pageParam) {
        ResultObject<List<WfSendListDto>> paged = wfSendListService.selectAllPaged(wfSendListParam, pageParam);
        return paged;
    }

    /**
     * @param wfSendListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<WfSendListDto> insert(@Valid @RequestBody WfSendListParam wfSendListParam, UserInfoToken userInfo) {
        ResultObject<WfSendListDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        //验证商品编码在pCode中是否存在
        if (wfSendListParam.getCodeTS() != null) {
            Map<String, String> mapTs = pCodeHolder.getTable(PCodeType.COMPLEX, wfSendListParam.getCodeTS()).orElse(null);
            if (mapTs == null) {
                //商品编码不存在
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("商品编码不存在"));
            }
        }
        WfSendListDto wfSendListDto = wfSendListService.insert(wfSendListParam, userInfo);
        if (wfSendListDto != null) {
            resultObject.setData(wfSendListDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param wfSendListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<WfSendListDto> update(@PathVariable String sid, @Valid @RequestBody WfSendListParam wfSendListParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        //验证商品编码在pCode中是否存在
        if (wfSendListParam.getCodeTS() != null) {
            Map<String, String> mapTs = pCodeHolder.getTable(PCodeType.COMPLEX, wfSendListParam.getCodeTS()).orElse(null);
            if (mapTs == null) {
                //商品编码不存在
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("商品编码不存在"));
            }
        }
        wfSendListParam.setSid(sid);
        WfSendListDto wfSendListDto = wfSendListService.update(wfSendListParam, userInfo);
        if (wfSendListDto != null) {
            resultObject.setData(wfSendListDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        wfSendListService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * @param exportParam
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<WfSendListParam> exportParam) throws Exception {
        List<WfSendListDto> wfSendListDtos = wfSendListService.selectAll(exportParam.getExportColumns());
        wfSendListDtos = convertForPrint(wfSendListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), wfSendListDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<WfSendListDto> convertForPrint(List<WfSendListDto> list) {
        for (WfSendListDto item : list) {
            if (StringUtils.isNotBlank(item.getGMark())) {
                item.setGMark(CommonEnum.GMarkEnum.getValue(item.getGMark()));
            }
            if (item.getUnit() != null) {
                item.setUnit(item.getUnit() + "|" + pCodeHolder.getValue("UNIT", item.getUnit()));
            }
            if (item.getCurr() != null) {
                item.setCurr(item.getCurr() + "|" + pCodeHolder.getValue("CURR_OUTDATED", item.getCurr()));
            }
        }
        return list;
    }
}
