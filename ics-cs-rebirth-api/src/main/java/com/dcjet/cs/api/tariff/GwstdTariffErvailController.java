package com.dcjet.cs.api.tariff;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.tariff.GwstdTariffErvailDto;
import com.dcjet.cs.dto.tariff.GwstdTariffErvailParam;
import com.dcjet.cs.tariff.service.GwstdTariffErvailService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-11-26
 */
@RestController
@RequestMapping("v1/gwstdTariffErvail")
@Api(tags = "税金管理-进口反补贴税税率")
public class GwstdTariffErvailController {
    @Resource
    private GwstdTariffErvailService gwstdTariffErvailService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param gwstdTariffErvailParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<GwstdTariffErvailDto>> getListPaged(@RequestBody GwstdTariffErvailParam gwstdTariffErvailParam, PageParam pageParam, UserInfoToken userInfo) {
        // gwstdTariffErvailParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<GwstdTariffErvailDto>> paged = gwstdTariffErvailService.getListPaged(gwstdTariffErvailParam, pageParam);
        return paged;
    }

    /**
     * @param gwstdTariffErvailParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<GwstdTariffErvailDto> insert(@Valid @RequestBody GwstdTariffErvailParam gwstdTariffErvailParam, UserInfoToken userInfo) {
        ResultObject<GwstdTariffErvailDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        GwstdTariffErvailDto gwstdTariffErvailDto = gwstdTariffErvailService.insert(gwstdTariffErvailParam, userInfo);
        if (gwstdTariffErvailDto != null) {
            resultObject.setData(gwstdTariffErvailDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param gwstdTariffErvailParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<GwstdTariffErvailDto> update(@PathVariable String sid, @Valid @RequestBody GwstdTariffErvailParam gwstdTariffErvailParam, UserInfoToken userInfo) {
        gwstdTariffErvailParam.setSid(sid);
        GwstdTariffErvailDto gwstdTariffErvailDto = gwstdTariffErvailService.update(gwstdTariffErvailParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (gwstdTariffErvailDto != null) {
            resultObject.setData(gwstdTariffErvailDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("更新失败!"));
        }
        return resultObject;
    }
}
