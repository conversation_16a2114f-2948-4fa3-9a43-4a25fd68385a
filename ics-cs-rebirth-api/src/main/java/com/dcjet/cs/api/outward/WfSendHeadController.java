package com.dcjet.cs.api.outward;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.outward.WfSendHeadDto;
import com.dcjet.cs.dto.outward.WfSendHeadParam;
import com.dcjet.cs.outward.service.WfSendHeadService;
import com.dcjet.cs.outward.service.WfSendListService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-7-18
 */
@RestController
@RequestMapping("v1/wfSendHead")
@Api(tags = "外发加工-外发加工发货单接口")
public class WfSendHeadController {
    @Resource
    private WfSendHeadService wfSendHeadService;
    @Resource
    private WfSendListService wfSendListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param wfSendHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<WfSendHeadDto>> getListPaged(@RequestBody WfSendHeadParam wfSendHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        wfSendHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<WfSendHeadDto>> paged = wfSendHeadService.getListPaged(wfSendHeadParam, pageParam);
        return paged;
    }

    /**
     * @param wfSendHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<WfSendHeadDto> insert(@Valid @RequestBody WfSendHeadParam wfSendHeadParam, UserInfoToken userInfo) {
        ResultObject<WfSendHeadDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        WfSendHeadDto wfSendHeadDto = wfSendHeadService.insert(wfSendHeadParam, userInfo);
        if (wfSendHeadDto != null) {
            resultObject.setData(wfSendHeadDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param wfSendHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<WfSendHeadDto> update(@PathVariable String sid, @Valid @RequestBody WfSendHeadParam wfSendHeadParam, UserInfoToken userInfo) {
        wfSendHeadParam.setSid(sid);
        WfSendHeadDto wfSendHeadDto = wfSendHeadService.update(wfSendHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (wfSendHeadDto != null) {
            resultObject.setData(wfSendHeadDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        /*
        int count = wfSendListService.getListNumByHeadIds(sids);
        if (count > 0) {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("该表头存在表体数据,请先删除表体数据!"));
        } else {
            // 删除表头数据
            wfSendHeadService.delete(sids);
        }*/
        // 删除表头数据
        wfSendHeadService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<WfSendHeadParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<WfSendHeadDto> wfSendHeadDtos = wfSendHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        wfSendHeadDtos = convertForPrint(wfSendHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), wfSendHeadDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<WfSendHeadDto> convertForPrint(List<WfSendHeadDto> list) {
        for (WfSendHeadDto item : list) {
            item.setContractorTradeName(item.getContractorCompanyCode() + " " + item.getContractorTradeName());
        }
        return list;
    }

    /**
     * 检查当前企业的发货单编号
     * 如果是自身，忽略自己
     *
     * @param billNo
     * @param sid
     * @param userInfo
     * @return ResultObject
     * @throws Exception
     */
    @ApiOperation("检查当前企业的发货单编号")
    @PostMapping("checkBillNo/{billNo}/{sid}")
    public ResultObject checkBillNo(@NotNull @PathVariable @ApiParam(name = "billNo", value = "发货单号") String billNo, @PathVariable @ApiParam(name = "sid", value = "表主键") String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        Map<String, String> map = new HashMap<>();
        map.put("billNo", billNo);
        map.put("tradeCode", userInfo.getCompany());
        map.put("sid", sid);
        // 检查发货单号是否存在
        WfSendHeadDto wfSendHeadDto = wfSendHeadService.checkBillNo(map);
        resultObject.setData(wfSendHeadDto);
        resultObject.setMessage(xdoi18n.XdoI18nUtil.t("查询成功"));
        return resultObject;
    }
}
