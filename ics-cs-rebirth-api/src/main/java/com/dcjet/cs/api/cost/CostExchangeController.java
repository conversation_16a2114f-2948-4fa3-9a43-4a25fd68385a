package com.dcjet.cs.api.cost;

import com.dcjet.cs.api.base.BasicController;
import com.dcjet.cs.cost.model.CostExchange;
import com.dcjet.cs.cost.service.CostExchangeService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.cost.CostExchangeDto;
import com.dcjet.cs.dto.cost.CostExchangeParam;
import com.dcjet.cs.dto.cost.CostExchangeSearchParam;
import com.dcjet.cs.dto.mat.MatPalletListDto;
import com.dcjet.cs.dto.mat.MatPalletListParam;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;


/**
 * 付汇管理
 * 
 * generated by Generate dc
 * <AUTHOR>
 * @date: 2020-03-02
 */
@Api(tags = "费用管理-付汇管理")
@RestController
@RequestMapping("v1/cost/exchange")
public class CostExchangeController
        extends BasicController<CostExchangeService, CostExchangeDto, CostExchangeParam, CostExchangeSearchParam> {


    @PostMapping("extraction")
    @ApiOperation(value = "提取接口")
    public ResultObject<Void> extraction(UserInfoToken token) {
        service.extraction(token);
        return ResultObject.createInstance(true);
    }

    @ApiOperation("锁定")
    @PutMapping("locked/{idList}")
    public ResultObject<Void> locked(@PathVariable List<String> idList, UserInfoToken token) {
        service.locked(idList, token);
        return ResultObject.createInstance(true);
    }

    @ApiOperation("解锁")
    @PutMapping("unlocked/{idList}")
    public ResultObject<Void> unlocked(@PathVariable List<String> idList, UserInfoToken token) {
        service.unlocked(idList, token);
        return ResultObject.createInstance(true);
    }

    @ApiOperation("批量维护")
    @PutMapping("maintain/{idList}")
    public ResultObject<Void> batchMaintain(@PathVariable List<String> idList, @RequestBody CostExchangeParam param, UserInfoToken token) {
        service.batchMaintain(idList, param, token);
        return ResultObject.createInstance(true);
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("外汇管理信息Excel数据导出接口")
    @PostMapping(value = "/exports")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<CostExchangeParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<CostExchangeDto> exchangeDtos = service.selectAll(exportParam.getExportColumns(), userInfo);
        return excelService.exportExcel(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), exchangeDtos);
    }

}