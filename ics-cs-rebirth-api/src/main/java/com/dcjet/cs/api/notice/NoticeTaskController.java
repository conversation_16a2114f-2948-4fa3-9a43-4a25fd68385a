package com.dcjet.cs.api.notice;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.notice.NoticeTaskDto;
import com.dcjet.cs.dto.notice.NoticeTaskParam;
import com.dcjet.cs.notice.service.NoticeTaskService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-5-19
 */
@RestController
@RequestMapping("v1/noticeTask")
@Api(tags = "预警通知设置-任务接口")
public class NoticeTaskController {
    @Resource
    private NoticeTaskService noticeTaskService;
    @Resource
    private ExcelService excelService;
    @Resource
    private ApplicationContext applicationContext;

    /**
     * @param noticeTaskParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<NoticeTaskDto>> getListPaged(@RequestBody NoticeTaskParam noticeTaskParam, PageParam pageParam, UserInfoToken userInfo) {
        noticeTaskParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<NoticeTaskDto>> paged = noticeTaskService.getListPaged(noticeTaskParam, pageParam);
        return paged;
    }

    /**
     * @param noticeTaskParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<NoticeTaskDto> insert(@Valid @RequestBody NoticeTaskParam noticeTaskParam, UserInfoToken userInfo) {
        ResultObject<NoticeTaskDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));

//        String bizCode = noticeTaskParam.getBizCode();
//        String bizDataId = noticeTaskParam.getBizDataId();
//        applicationContext.publishEvent(new NoticeTaskEvent(new NoticeTaskEventParam(ConstantsNoticeItem.DEC_I_RECEIVED, bizDataId)));
        NoticeTaskDto noticeTaskDto = noticeTaskService.insert(noticeTaskParam, userInfo);
        if (resultObject != null) {
            resultObject.setData(noticeTaskDto);
//            new Thread(noticeTaskService).start();
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        noticeTaskService.delete(sids);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<NoticeTaskParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<NoticeTaskDto> noticeTaskDtos = noticeTaskService.selectAll(exportParam.getExportColumns(), userInfo);
        noticeTaskDtos = convertForPrint(noticeTaskDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), noticeTaskDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<NoticeTaskDto> convertForPrint(List<NoticeTaskDto> list) {
        for (NoticeTaskDto item : list) {
        }
        return list;
    }
}
