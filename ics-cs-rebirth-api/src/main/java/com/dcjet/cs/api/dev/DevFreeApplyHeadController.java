package com.dcjet.cs.api.dev;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.common.service.FastdFsService;
import com.dcjet.cs.dev.dao.DevFreeApplyHeadMapper;
import com.dcjet.cs.dev.dao.DevFreeApplyListMapper;
import com.dcjet.cs.dev.model.DevFreeApplyHead;
import com.dcjet.cs.dev.model.DevFreeApplyList;
import com.dcjet.cs.dev.service.DevFreeApplyHeadService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.GwstdSealParam;
import com.dcjet.cs.dto.dev.DevFreeApplyHeadCopyParam;
import com.dcjet.cs.dto.dev.DevFreeApplyHeadDto;
import com.dcjet.cs.dto.dev.DevFreeApplyHeadParam;
import com.dcjet.cs.dto.erp.DecErpEHeadNDto;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-5-13
 */
@RestController
@RequestMapping("v1/devFreeApplyHead")
@Api(tags = "设备管理-免税申请表表头接口")
public class DevFreeApplyHeadController {
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource
    private DevFreeApplyHeadService devFreeApplyHeadService;
    @Resource
    private ExcelService excelService;
    @Resource
    private DevFreeApplyListMapper devFreeApplyListMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private DevFreeApplyHeadMapper devFreeApplyHeadMapper;
    @Resource
    private AttachedMapper attachedMapper;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private FastdFsService fastdFsService;

    /**
     * @param devFreeApplyHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<DevFreeApplyHeadDto>> getListPaged(@RequestBody DevFreeApplyHeadParam devFreeApplyHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        return devFreeApplyHeadService.getListPaged(devFreeApplyHeadParam, pageParam, userInfo);
    }

    /**
     * @param devFreeApplyHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<DevFreeApplyHeadDto> insert(@Valid @RequestBody DevFreeApplyHeadParam devFreeApplyHeadParam, UserInfoToken userInfo) throws UnsupportedEncodingException {
        return devFreeApplyHeadService.insert(devFreeApplyHeadParam, userInfo);
    }

    /**
     * @param sid
     * @param devFreeApplyHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<DevFreeApplyHeadDto> update(@PathVariable String sid, @Valid @RequestBody DevFreeApplyHeadParam devFreeApplyHeadParam, UserInfoToken userInfo) {
        devFreeApplyHeadParam.setSid(sid);
        devFreeApplyHeadParam.setTradeCode(userInfo.getCompany());
        DevFreeApplyHeadDto devFreeApplyHeadDto = devFreeApplyHeadService.update(devFreeApplyHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (devFreeApplyHeadDto != null) {
            resultObject.setData(devFreeApplyHeadDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("更新失败!"));
        }
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) throws Exception {
        return devFreeApplyHeadService.delete(sids);
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/forceDelete/{sids}")
    public ResultObject forceDelete(@PathVariable List<String> sids) throws Exception {
        return devFreeApplyHeadService.forceDelete(sids);
    }
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/forceCheckDelete/{sids}")
    public ResultObject forceCheckDelete(@PathVariable List<String> sids) throws Exception {
        return devFreeApplyHeadService.forceCheckDelete(sids);
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<DevFreeApplyHeadParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<DevFreeApplyHeadDto> devFreeApplyHeadDtos = devFreeApplyHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        devFreeApplyHeadDtos = convertForPrint(devFreeApplyHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), devFreeApplyHeadDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<DevFreeApplyHeadDto> convertForPrint(List<DevFreeApplyHeadDto> list) {
        for (DevFreeApplyHeadDto item : list) {
            // 供应商
            item.setSupplierCode(item.getSupplierName());
            // 报关行
            item.setDeclareCode(item.getDeclareName());
            // 征免性质
            item.setCutMode(pCodeHolder.getValue(PCodeType.LEVYTYPE, item.getCutMode()));
            // 成交方式
            item.setTransMode(pCodeHolder.getValue(PCodeType.TRANSAC, item.getTransMode()));
            // 主管海关
            if (StringUtils.isNotBlank(item.getMasterCustoms())) {
                item.setMasterCustoms(item.getMasterCustoms() + "(" + pCodeHolder.getValue(PCodeType.CUSTOMS_REL, item.getMasterCustoms()) + ")");
            }
            // 进出口岸
            if (StringUtils.isNotBlank(item.getEntryPort())) {
                item.setEntryPort(item.getEntryPort() + "(" + pCodeHolder.getValue(PCodeType.CUSTOMS_REL, item.getEntryPort()) + ")");
            }
            // 是否申报
            if (StringUtils.isNotBlank(item.getIsDeclare())) {
                if (CommonVariable.NO_CODE.equals(item.getIsDeclare())) {
                    item.setIsDeclare("否");
                } else if (CommonVariable.YES_CODE.equals(item.getIsDeclare())) {
                    item.setIsDeclare("是");
                }
            }
            // 是否有效
            if (StringUtils.isNotBlank(item.getIsEnabled())) {
                if (CommonVariable.NO_CODE.equals(item.getIsEnabled())) {
                    item.setIsEnabled("否");
                } else if (CommonVariable.YES_CODE.equals(item.getIsEnabled())) {
                    item.setIsEnabled("是");
                }
            }
            // 进出口类型
            if (StringUtils.isNotBlank(item.getIEMark())) {
                if (ConstantsStatus.TYPE_I.equals(item.getIEMark())) {
                    item.setIEMark("I 进口");
                } else if (ConstantsStatus.TYPE_E.equals(item.getIEMark())) {
                    item.setIEMark("E 出口");
                }
            }
            // 状态
//            if (StringUtils.isNotBlank(item.getStatus())) {
//                if (ConstantsStatus.STATUS_0.equals(item.getStatus())) {
//                    item.setStatus("暂存");
//                } else if (ConstantsStatus.STATUS_3.equals(item.getStatus())) {
//                    item.setStatus("报关生成");
//                } else if (ConstantsStatus.STATUS_4.equals(item.getStatus())) {
//                    item.setStatus("报关完成");
//                }
//            }
        }
        return list;
    }

    /**
     * @param devFreeApplyHeadParam
     * @param userInfo
     * @return com.xdo.domain.ResultObject<com.dcjet.cs.dto.erp.DecErpEHeadNDto>
     * <AUTHOR>
     * @date 2020/5/18 10:35
     */
    @ApiOperation("减免税管理-复制")
    @PostMapping("/copy")
    public ResultObject<DecErpEHeadNDto> copy(@Valid @RequestBody DevFreeApplyHeadCopyParam devFreeApplyHeadParam, UserInfoToken userInfo) throws UnsupportedEncodingException {
        return devFreeApplyHeadService.copy(devFreeApplyHeadParam, userInfo);
    }

    /**
     * @param userInfo
     * @return com.xdo.domain.ResultObject<java.lang.String>
     * <AUTHOR>
     * @date 2020/5/20 9:29
     */
    @ApiOperation("生成ERP提单内部编号")
    @PostMapping("getErpEmsListNo")
    public ResultObject<String> getErpEmsListNo(UserInfoToken userInfo) {
        return devFreeApplyHeadService.getErpEmsListNo(userInfo);
    }

    /**
     * 功能描述: grid查询
     */
    @ApiOperation("减免税-待审核查询接口")
    @PostMapping("selectListForAudit")
    public ResultObject<List<DevFreeApplyHeadDto>> getListAuditPaged(@RequestBody DevFreeApplyHeadParam devFreeApplyHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        return devFreeApplyHeadService.getListAuditPaged(devFreeApplyHeadParam, pageParam, userInfo);
    }

    /**
     * @param userInfo
     * @return com.xdo.domain.ResultObject<java.util.List < java.util.Map < java.lang.String, java.lang.String>>>
     * <AUTHOR>
     * @date 2020/5/20 9:29
     */
    @ApiOperation("提单模板数据源")
    @PostMapping("getErpTemp")
    public ResultObject<List<Map<String, String>>> getErpTemp(UserInfoToken userInfo) {
        return devFreeApplyHeadService.getErpTemp(userInfo);
    }

    /**
     * @param devFreeApplyHeadParam
     * @param userInfo
     * @return com.xdo.domain.ResultObject
     * <AUTHOR>
     * @date 2020/5/20 9:29
     */
    @ApiOperation("生成进口预录入单")
    @PostMapping("customsGenerated")
    public ResultObject createCustoms(@RequestBody DevFreeApplyHeadParam devFreeApplyHeadParam, UserInfoToken userInfo) throws Exception {
        return devFreeApplyHeadService.createCustoms(devFreeApplyHeadParam, userInfo);
    }

    /**
     * @param paramsType
     * @param userInfo
     * @return com.xdo.domain.ResultObject<java.util.Map < java.lang.String, java.util.Map < java.lang.String, java.lang.String>>>
     * <AUTHOR>
     * @date 2020/5/23 15:16
     */
    @ApiOperation("免表参数下拉框值获取")
    @PostMapping("freeParams/{paramsType}")
    public ResultObject<Map<String, Map<String, String>>> getFreeParams(@ApiParam(value = "参数类型") @PathVariable("paramsType") String paramsType,
                                                                        UserInfoToken userInfo) {
        return devFreeApplyHeadService.getFreeParams(paramsType, userInfo);
    }

    /**
     * @param type
     * @param sid
     * @return com.xdo.domain.ResultObject
     * <AUTHOR>
     * @date 2020/6/8 15:59
     */
    @ApiOperation("检查表头表体数据")
    @PostMapping("checkData/{type}/{sid}")
    public ResultObject checkData(@PathVariable String type, @PathVariable String sid) {
        if ("GUARANTEE".equals(type) || "APPLY".equals(type)) {
            DevFreeApplyHead devFreeApplyHead = devFreeApplyHeadMapper.selectByPrimaryKey(sid);
            if (null == devFreeApplyHead) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("表头数据不存在"));
            }
        }

        List<DevFreeApplyList> devFreeApplyLists = devFreeApplyListMapper.selectByHeadId(sid);
        if (CollectionUtils.isEmpty(devFreeApplyLists)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("表体数据不存在"));
        }
        return ResultObject.createInstance(true);
    }

    /**
     * @param sid
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR>
     * @date 2020/5/23 15:16
     */
    @ApiOperation("导出用途说明书")
    @PostMapping("exportInstructions/{sid}")
    public ResponseEntity exportInstructions(@PathVariable String sid) throws Exception {

        String templateName = "dev_instructions.xlsx";

        List<DevFreeApplyList> devFreeApplyLists = devFreeApplyListMapper.selectByHeadId(sid);
        for (DevFreeApplyList devFreeApplyList : devFreeApplyLists) {
            // 获取图片地址
            List<Map<String, String>> maps = attachedMapper.selectForFree(devFreeApplyList.getBondSid());
            if (CollectionUtils.isNotEmpty(maps)) {
                devFreeApplyList.setFileName(maps.get(0).get("FILE_NAME"));
                devFreeApplyList.setOriginFileName(maps.get(0).get("ORIGIN_FILE_NAME"));
            }
        }

        String outName = xdoi18n.XdoI18nUtil.t("功能用途说明书") + ".xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";

        String exportFileName = exportService.exportForInstructions(devFreeApplyLists, fileName, templateName);

        //写入图片
        if (StringUtils.isNotBlank(exportFileName)) {
            insertImageIntoExcel(exportFileName, devFreeApplyLists);
        }

        HttpHeaders h = new HttpHeaders();
        //解决空格变成+号问题
        outName = URLEncoder.encode(outName, CommonVariable.UTF8);
        outName = outName.replaceAll("\\+","%20");
        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(exportFileName)), h, HttpStatus.OK);
    }

    /**
     * @param exportFileName
     * @param devFreeApplyLists
     * @return void
     * @description 往excel写入图片
     */
    private void insertImageIntoExcel(String exportFileName, List<DevFreeApplyList> devFreeApplyLists) {
        FileOutputStream fileOut = null;
        try {
            XSSFWorkbook wb = new XSSFWorkbook(new FileInputStream(exportFileName));
            for (int i = 0; i < devFreeApplyLists.size(); i++) {
                // 修改sheet名称为料号
                wb.setSheetName(i, devFreeApplyLists.get(i).getFacGNo());
                // 无图片文件则跳过
                if (StringUtils.isBlank(devFreeApplyLists.get(i).getFileName())) {
                    continue;
                }
                //modify by jlzhang 支持云环境上fastFds文件下载
                byte[] bytes = fastdFsService.getByteFromFastDFS(devFreeApplyLists.get(i).getFileName());
                //画图的顶级管理器，一个sheet只能获取一个（一定要注意这点）
                XSSFDrawing patriarch = wb.getSheetAt(i).createDrawingPatriarch();
                //anchor主要用于设置图片的属性
                XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, 1, 5, 2, 6);
                anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
                //插入图片
                patriarch.createPicture(anchor, wb.addPicture(bytes, XSSFWorkbook.PICTURE_TYPE_JPEG));
            }

            fileOut = new FileOutputStream(exportFileName);
            // 写入excel文件
            wb.write(fileOut);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fileOut != null) {
                try {
                    fileOut.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * @param sid
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR>
     * @date 2020/5/23 15:16
     */
    @ApiOperation("导出担保申请表")
    @PostMapping("exportGuarantee/{sid}")
    public ResponseEntity exportGuarantee(@PathVariable String sid) throws IOException {

        String templateName = "dev_guarantee_apply.xlsx";

        DevFreeApplyHead devFreeApplyHead = devFreeApplyHeadMapper.selectByPrimaryKey(sid);
        List<DevFreeApplyHead> devFreeApplyHeadList = new ArrayList<>();
        devFreeApplyHeadList.add(convertFreeForPrint(devFreeApplyHead));

        List<DevFreeApplyList> devFreeApplyLists =
                convertFreeBodyForPrint(devFreeApplyListMapper.selectBodyByHeadId(sid));

        String outName = xdoi18n.XdoI18nUtil.t("担保申请表") + devFreeApplyHead.getEmsListNo() + ".xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";

        String exportFileName = "";
        if (CollectionUtils.isNotEmpty(devFreeApplyLists)) {
            exportFileName = exportService.exportForFree(devFreeApplyHeadList, devFreeApplyLists, fileName, templateName);
        }

        HttpHeaders h = new HttpHeaders();
        //解决空格变成+号问题
        outName = URLEncoder.encode(outName, CommonVariable.UTF8);
        outName = outName.replaceAll("\\+","%20");
        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(exportFileName)), h, HttpStatus.OK);
    }

    /**
     * @param sid
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR>
     * @date 2020/5/23 15:16
     */
    @ApiOperation("导出免税申请表")
    @PostMapping("exportApply/{sid}")
    public ResponseEntity exportApply(@PathVariable String sid) throws IOException {

        String templateName = "dev_free_apply.xlsx";

        DevFreeApplyHead devFreeApplyHead = devFreeApplyHeadMapper.selectByPrimaryKey(sid);
        List<DevFreeApplyHead> devFreeApplyHeadList = new ArrayList<>();
        devFreeApplyHeadList.add(convertFreeForPrint(devFreeApplyHead));

        List<DevFreeApplyList> devFreeApplyLists =
                convertFreeBodyForPrint(devFreeApplyListMapper.selectBodyByHeadId(sid));

        String outName = xdoi18n.XdoI18nUtil.t("免表申请表") + devFreeApplyHead.getEmsListNo() + ".xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";

        String exportFileName = "";
        if (CollectionUtils.isNotEmpty(devFreeApplyLists)) {
            exportFileName = exportService.export(devFreeApplyHeadList, devFreeApplyLists, fileName,
                    templateName);
        }

        HttpHeaders h = new HttpHeaders();
        //解决空格变成+号问题
        outName = URLEncoder.encode(outName, CommonVariable.UTF8);
        outName = outName.replaceAll("\\+","%20");
        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(exportFileName)), h, HttpStatus.OK);
    }

    /**
     * @param list
     * @return java.util.List<com.dcjet.cs.dev.model.DevFreeApplyList>
     * @description 减免税表体字段转换
     * <AUTHOR>
     * @date 2020/5/23 15:18
     */
    private List<DevFreeApplyList> convertFreeBodyForPrint(List<DevFreeApplyList> list) {
        for (DevFreeApplyList item : list) {
            if (StringUtils.isNotBlank(item.getCurr())) {
                item.setCurr(pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurr()) + "\r\n" +
                        "(" + item.getCurr() + ")");
            }
            if (StringUtils.isNotBlank(item.getOriginCountry())) {
                item.setOriginCountry(pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getOriginCountry()) + "\r\n" +
                        "(" + item.getOriginCountry() + ")");
            }
            if (StringUtils.isNotBlank(item.getUnit1())) {
                item.setUnit1(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit1()));
            }
            if (StringUtils.isNotBlank(item.getUnit())) {
                item.setUnit(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit()));
            }
        }
        return list;

    }

    /**
     * @param devFreeApplyHead
     * @return com.dcjet.cs.dev.model.DevFreeApplyHead
     * @description 减免税表头字段转换
     * <AUTHOR>
     * @date 2020/5/23 15:18
     */
    private DevFreeApplyHead convertFreeForPrint(DevFreeApplyHead devFreeApplyHead) {
        devFreeApplyHead.setBusinessUnits(devFreeApplyHead.getTradeName() + "/" + devFreeApplyHead.getTradeCode());
        devFreeApplyHead.setCutMode(pCodeHolder.getValue(PCodeType.LEVYTYPE, devFreeApplyHead.getCutMode())
                + "/" + devFreeApplyHead.getCutMode());
        if (StringUtils.isNotBlank(devFreeApplyHead.getEntryPort())) {
            devFreeApplyHead.setEntryPort(devFreeApplyHead.getEntryPort()
                    + "(" + pCodeHolder.getValue(PCodeType.CUSTOMS_REL, devFreeApplyHead.getEntryPort()) + ")");
        }
        if (StringUtils.isNotBlank(devFreeApplyHead.getIsDeclare())) {
            if (ConstantsStatus.STATUS_0.equals(devFreeApplyHead.getIsDeclare())) {
                devFreeApplyHead.setIsDeclare("否");
            } else if (ConstantsStatus.STATUS_1.equals(devFreeApplyHead.getIsDeclare())) {
                devFreeApplyHead.setIsDeclare("是");
            }
        }
        if (StringUtils.isNotBlank(devFreeApplyHead.getIEMark())) {
            if (ConstantsStatus.TYPE_I.equals(devFreeApplyHead.getIEMark())) {
                devFreeApplyHead.setIEMark("I 进口");
            } else if (ConstantsStatus.TYPE_E.equals(devFreeApplyHead.getIEMark())) {
                devFreeApplyHead.setIEMark("E 出口");
            }
        }
        if (StringUtils.isNotBlank(devFreeApplyHead.getMasterCustoms())) {
            devFreeApplyHead.setMasterCustoms(devFreeApplyHead.getMasterCustoms()
                    + "(" + pCodeHolder.getValue(PCodeType.CUSTOMS_REL, devFreeApplyHead.getMasterCustoms()) + ")");
        }
        if (StringUtils.isNotBlank(devFreeApplyHead.getTransMode())) {
            devFreeApplyHead.setTransMode(pCodeHolder.getValue(PCodeType.TRANSAC, devFreeApplyHead.getTransMode()));
        }
        return devFreeApplyHead;
    }

    @ApiOperation("资料生成")
    @PostMapping("buildFiles")
    public ResponseEntity buildFiles(@RequestBody GwstdSealParam gwstdSealParam, UserInfoToken userInfo) throws Exception {

        Map<String, String> mapFile = devFreeApplyHeadService.buildFiles(gwstdSealParam, userInfo);
        String returnFile = mapFile.get("returnFile");
        String returnFileName = mapFile.get("returnFileName");
        HttpHeaders h = new HttpHeaders();
        h.setContentDispositionFormData("attachment", URLEncoder.encode(returnFileName, CommonVariable.UTF8));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<>(FileUtils.readFileToByteArray(new File(returnFile)), h, HttpStatus.OK);
    }

    @ApiOperation("进出口货物征免税申请表打印")
    @PostMapping("taxApplyForm/{sid}")
    public ResponseEntity printTaxApplyForm(@PathVariable String sid, UserInfoToken userInfo) throws Exception {

        String outName = xdoi18n.XdoI18nUtil.t("进出口货物征免税申请表") + ".pdf";
        String exportFileName = devFreeApplyHeadService.printTaxApplyForm(sid, userInfo);

        HttpHeaders h = new HttpHeaders();
        //解决空格变成+号问题
        outName = URLEncoder.encode(outName, CommonVariable.UTF8);
        outName = outName.replaceAll("\\+","%20");
        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<>(FileUtils.readFileToByteArray(new File(exportFileName)), h, HttpStatus.OK);
    }

    /**
     * 发送概要申报
     */
    @ApiOperation("发送概要申报")
    @PostMapping("sendOutline/{headId}")
    public ResultObject sendOutline(@PathVariable("headId") List<String> headIds,@RequestBody DevFreeApplyHeadParam devFreeApplyHeadParam, UserInfoToken userInfo) {
        return devFreeApplyHeadService.sendOutline(headIds,devFreeApplyHeadParam, userInfo);
    }

    /**
     * 申请完整申报
     */
    @ApiOperation("申请完整申报")
    @PostMapping("applyComplete")
    public ResultObject applyComplete(@RequestBody DevFreeApplyHeadParam devFreeApplyHeadParam, UserInfoToken userInfo) throws Exception {
        return devFreeApplyHeadService.applyComplete(devFreeApplyHeadParam, userInfo);
    }

    /**
     * 概要强制清空
     */
    @ApiOperation("概要强制清空")
    @PostMapping("summaryClear/{sid}")
    public ResultObject summaryClear(@PathVariable String sid, UserInfoToken userInfo) throws Exception {
        return devFreeApplyHeadService.summaryClear(sid, userInfo);
    }

    /**
     * 回执详情
     */
    @ApiOperation("回执详情")
    @PostMapping("getReceiptDetail")
    public ResultObject getReceiptDetail(@RequestBody DevFreeApplyHeadParam devFreeApplyHeadParam) {
        return devFreeApplyHeadService.getReceiptDetail(devFreeApplyHeadParam);
    }
}
