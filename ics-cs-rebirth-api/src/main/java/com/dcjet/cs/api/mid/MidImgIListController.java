package com.dcjet.cs.api.mid;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.mid.MidImgIListDto;
import com.dcjet.cs.dto.mid.MidImgIListParam;
import com.dcjet.cs.mid.service.MidImgIListService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-6-16
 */
@RestController
@RequestMapping("v1/midImgIList")
@Api(tags = "中期核查-入库明细接口")
public class MidImgIListController {
    @Resource
    private MidImgIListService midImgIListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param midImgIListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<MidImgIListDto>> getListPaged(@RequestBody MidImgIListParam midImgIListParam, PageParam pageParam, UserInfoToken userInfo) {
        midImgIListParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<MidImgIListDto>> paged = midImgIListService.getListPaged(midImgIListParam, pageParam);
        return paged;
    }

    /**
     * @param midImgIListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口(入库明细追溯)")
    @PostMapping("list/{midType}")
    public ResultObject<List<MidImgIListDto>> getReviewListPaged(@PathVariable String midType, @RequestBody MidImgIListParam midImgIListParam, PageParam pageParam, UserInfoToken userInfo) {
        midImgIListParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<MidImgIListDto>> paged = midImgIListService.getReviewListPaged(midType, midImgIListParam, pageParam);
        return paged;
    }

    /**
     * @param midImgIListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<MidImgIListDto> insert(@Valid @RequestBody MidImgIListParam midImgIListParam, UserInfoToken userInfo) {
        ResultObject<MidImgIListDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        MidImgIListDto midImgIListDto = midImgIListService.insert(midImgIListParam, userInfo);
        if (midImgIListDto != null) {
            resultObject.setData(midImgIListDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param midImgIListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<MidImgIListDto> update(@PathVariable String sid, @Valid @RequestBody MidImgIListParam midImgIListParam, UserInfoToken userInfo) {
        midImgIListParam.setSid(sid);
        MidImgIListDto midImgIListDto = midImgIListService.update(midImgIListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (midImgIListDto != null) {
            resultObject.setData(midImgIListDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("更新失败!"));
        }
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        midImgIListService.delete(sids);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<MidImgIListParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<MidImgIListDto> midImgIListDtos = midImgIListService.selectAll(exportParam.getExportColumns(), userInfo);
        midImgIListDtos = convertForPrint(midImgIListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), midImgIListDtos);
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出入库追溯接口")
    @PostMapping(value = "/export/{midType}")
    public ResponseEntity export(@PathVariable String midType, @Valid @RequestBody BasicExportParam<MidImgIListParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<MidImgIListDto> midImgIListDtos = midImgIListService.selectAll2(midType, exportParam.getExportColumns(), userInfo);
        midImgIListDtos = convertForPrint(midImgIListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), midImgIListDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<MidImgIListDto> convertForPrint(List<MidImgIListDto> list) {
        for (MidImgIListDto item : list) {
            if (StringUtils.isNotBlank(item.getBondMark())) {
                item.setBondMark(CommonEnum.BondMarkEnum.getValue(item.getBondMark()));
            }
            if (StringUtils.isNotBlank(item.getGMark())) {
                item.setGMark(CommonEnum.GMarkEnum.getValue(item.getGMark()));
            }
            if (StringUtils.isNotBlank(item.getWhUnit())) {
                String value = pCodeHolder.getValue(item.getWhUnit(), PCodeType.UNIT);
                item.setUnit(StringUtils.isNotBlank(value) ? item.getWhUnit() + " " + value : item.getWhUnit());
            }
            if (StringUtils.isNotBlank(item.getUnit())) {
                String value = pCodeHolder.getValue(item.getUnit(), PCodeType.UNIT);
                item.setUnit(StringUtils.isNotBlank(value) ? item.getUnit() + " " + value : item.getUnit());
            }
            if (StringUtils.isNotBlank(item.getInWay())) {
                item.setInWay(StringUtils.isNotBlank(CommonEnum.InOutWayEnum.getValue(item.getInWay())) ?
                        item.getInWay() + " " + CommonEnum.InOutWayEnum.getValue(item.getInWay()) : item.getInWay());
            }
        }
        return list;
    }
}
