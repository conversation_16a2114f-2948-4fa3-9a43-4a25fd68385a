package com.dcjet.cs.api.repair;

import com.dcjet.cs.dto.repair.*;
import com.dcjet.cs.repair.service.RepairEIListService;
import com.dcjet.cs.repair.service.RepairEListService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 修理复运进境表体
 * <p>
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-04-16
 */
@Api(tags = "修理物品-修理复运进境表体")
@RestController
@RequestMapping("v1/repair/eilist")
public class RepairEIListController {

    @Resource
    private RepairEIListService repairEIListService;

    @Resource
    private RepairEListService repairelistService;

    /**
     * 查询接口
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    @ApiOperation("列表")
    @PostMapping("list")
    public ResultObject<List<RepairEIListDto>> list(@RequestBody RepairEIListSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<RepairEIListDto>> paged = repairEIListService.list(searchParam, pageParam, token);
        return paged;
    }

    /***
     * 获取详细信息
     *
     * @param sid
     * @param token
     * @return
     */
    @ApiModelProperty("详细信息")
    @GetMapping("/{sid}")
    public ResultObject<RepairEIListDto> get(@PathVariable String sid, UserInfoToken token) {
        RepairEIListDto dto = repairEIListService.get(sid, token);
        return ResultObject.createInstance(dto, 1);
    }

    @ApiOperation("待提取的数据")
    @PostMapping("/unextraction")
    public ResultObject<List<RepairEListDto>> unextraction(@RequestBody RepairEIListExtractionParam searchParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<RepairEListDto>> resultObject = repairelistService.unextraction(searchParam, pageParam, token);
        return resultObject;
    }

    @ApiOperation("提取数据")
    @PostMapping("/extraction")
    public ResultObject<Boolean> extraction(@RequestBody @Valid RepairEIListExtractionParam param, UserInfoToken token) {
        repairEIListService.extraction(param, token);
        return ResultObject.createInstance(true, 1);
    }

    /**
     * 新增
     *
     * @param param
     * @param token
     * @return
     */
    @ApiOperation("新增")
    @PostMapping()
    public ResultObject<RepairEIListDto> insert(@Valid @RequestBody RepairEIListParam param, UserInfoToken token) {
        ResultObject<RepairEIListDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        RepairEIListDto dto = repairEIListService.insert(param, token);
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * 修改
     *
     * @param sid
     * @param param
     * @param token
     * @return
     */
    @ApiOperation("修改")
    @PutMapping("{sid}")
    public ResultObject<RepairEIListDto> update(@PathVariable String sid, @Valid @RequestBody RepairEIListParam param, UserInfoToken token) {
        RepairEIListDto dto = repairEIListService.update(sid, param, token);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * 删除
     *
     * @param sids
     * @return
     */
    @ApiOperation("删除")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        repairEIListService.delete(sids, token);
        return resultObject;
    }
}
