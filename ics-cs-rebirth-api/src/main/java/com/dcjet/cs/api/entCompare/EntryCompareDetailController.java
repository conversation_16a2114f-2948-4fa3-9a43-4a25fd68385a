package com.dcjet.cs.api.entCompare;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.entCompare.EntryCompareDetailDto;
import com.dcjet.cs.dto.entCompare.EntryCompareDetailParam;
import com.dcjet.cs.entCompare.service.EntryCompareDetailService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.checkerframework.checker.units.qual.A;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.xml.transform.Result;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-10-14
 */
@RestController
@RequestMapping("v1/entryCompareDetail")
@Api(tags = "报关复核异常信息接口")
public class EntryCompareDetailController {
    @Resource
    private EntryCompareDetailService entryCompareDetailService;
    @Resource
    private ExcelService excelService;

    /**
     * @param entryCompareDetailParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<EntryCompareDetailDto>> getListPaged(@RequestBody EntryCompareDetailParam entryCompareDetailParam, PageParam pageParam, UserInfoToken userInfo) {
        entryCompareDetailParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<EntryCompareDetailDto>> paged = entryCompareDetailService.getListPaged(entryCompareDetailParam, pageParam);
        return paged;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<EntryCompareDetailParam>  exportParam, UserInfoToken userInfo) throws Exception {
        List<EntryCompareDetailDto> entryCompareDetailDtos = entryCompareDetailService.selectAll(exportParam.getExportColumns(), userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), entryCompareDetailDtos);
    }

    /**
     * 手工复核
     */
    @ApiOperation("手工复核")
    @PostMapping(value = "manualCompare/{entryNo}")
    public ResultObject manualCompare(@PathVariable("entryNo") String entryNo,UserInfoToken userInfo){
        return entryCompareDetailService.manualCompare(entryNo,userInfo);
    }
}
