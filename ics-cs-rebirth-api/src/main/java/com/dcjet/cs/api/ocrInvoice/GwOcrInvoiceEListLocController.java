package com.dcjet.cs.api.ocrInvoice;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListLocDto;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEListLocParam;
import com.dcjet.cs.ocrInvoice.service.GwOcrInvoiceEListLocService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@RestController
@RequestMapping("v1/gwOcrInvoiceEListLoc")
@Api(tags = "OCR-出口发票箱单表体 Loc接口")
public class GwOcrInvoiceEListLocController extends BaseController {
    @Resource
    private GwOcrInvoiceEListLocService service;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param gwOcrInvoiceEListLocParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<GwOcrInvoiceEListLocDto>> getListPaged(@RequestBody GwOcrInvoiceEListLocParam gwOcrInvoiceEListLocParam, PageParam pageParam, UserInfoToken userInfo) {
        gwOcrInvoiceEListLocParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<GwOcrInvoiceEListLocDto>> paged = service.getListPaged(gwOcrInvoiceEListLocParam, pageParam);
        return paged;
    }

    @ApiOperation("getOne")
    @GetMapping("getOne/{sid}")
    public ResultObject<GwOcrInvoiceEListLocDto> getOne(@PathVariable String sid) {
        GwOcrInvoiceEListLocDto dto = service.getOne(sid);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("获取成功"), dto);
    }
}
