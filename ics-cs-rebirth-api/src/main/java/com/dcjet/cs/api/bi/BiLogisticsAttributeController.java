package com.dcjet.cs.api.bi;

import com.dcjet.cs.bi.service.BiLogisticsAttributeService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.bi.BiLogisticsAttributeDto;
import com.dcjet.cs.dto.bi.BiLogisticsAttributeExportParam;
import com.dcjet.cs.dto.bi.BiLogisticsAttributeParam;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-9-5
 */
@RestController
@RequestMapping("v1/biLogisticsAttribute")
@Api(tags = "企业参数库-物流属性接口")
public class BiLogisticsAttributeController extends BaseController {
    @Resource
    private BiLogisticsAttributeService biLogisticsAttributeService;
    @Resource
    private ExcelService excelService;

    /**
     * @param biLogisticsAttributeParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BiLogisticsAttributeDto>> getListPaged(@RequestBody BiLogisticsAttributeParam biLogisticsAttributeParam, PageParam pageParam, UserInfoToken userInfo) {
        biLogisticsAttributeParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BiLogisticsAttributeDto>> paged = biLogisticsAttributeService.getListPaged(biLogisticsAttributeParam, pageParam);
        for(BiLogisticsAttributeDto biLogisticsAttributeDto : paged.getData()){
            biLogisticsAttributeDto.setUserName(biLogisticsAttributeDto.getInsertUser() + "(" + biLogisticsAttributeDto.getInsertUserName() + ")");
        }
        return paged;
    }

    /**
     * @param biLogisticsAttributeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BiLogisticsAttributeDto> insert(@Valid @RequestBody BiLogisticsAttributeParam biLogisticsAttributeParam, UserInfoToken userInfo) {
        ResultObject<BiLogisticsAttributeDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        BiLogisticsAttributeDto biLogisticsAttributeDto = biLogisticsAttributeService.insert(biLogisticsAttributeParam, userInfo);
        if (biLogisticsAttributeDto != null) {
            resultObject.setData(biLogisticsAttributeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param biLogisticsAttributeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BiLogisticsAttributeDto> update(@PathVariable String sid, @Valid @RequestBody BiLogisticsAttributeParam biLogisticsAttributeParam, UserInfoToken userInfo) {
        biLogisticsAttributeParam.setSid(sid);
        BiLogisticsAttributeDto biLogisticsAttributeDto = biLogisticsAttributeService.update(biLogisticsAttributeParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (biLogisticsAttributeDto != null) {
            resultObject.setData(biLogisticsAttributeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        biLogisticsAttributeService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BiLogisticsAttributeExportParam exportParam, UserInfoToken userInfo) throws Exception {
        List<BiLogisticsAttributeDto> biLogisticsAttributeDtos = biLogisticsAttributeService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(biLogisticsAttributeDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), biLogisticsAttributeDtos);
    }

    /**
     * 导出，pCode转换中文名称
     *
     * @param list
     * @return
     */
    public void convertForPrint(List<BiLogisticsAttributeDto> list) {
        for (BiLogisticsAttributeDto item : list) {
            switch (item.getLogisticsType()) {
                case "1":
                    item.setLogisticsType("1 国内段");
                    break;
                case "2":
                    item.setLogisticsType("2 国际段");
                    break;
            }
            item.setUserName(item.getInsertUser() + "(" + item.getInsertUserName() + ")");
        }
    }
}
