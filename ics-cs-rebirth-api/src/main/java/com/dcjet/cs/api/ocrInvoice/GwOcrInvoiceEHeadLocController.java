package com.dcjet.cs.api.ocrInvoice;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEHeadLocDto;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEHeadLocParam;
import com.dcjet.cs.ocrInvoice.service.GwOcrInvoiceEHeadLocService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@RestController
@RequestMapping("v1/gwOcrInvoiceEHeadLoc")
@Api(tags = "OCR-出口发票箱单表头 Loc接口")
public class GwOcrInvoiceEHeadLocController extends BaseController {
    @Resource
    private GwOcrInvoiceEHeadLocService service;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param gwOcrInvoiceIHeadLocParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<GwOcrInvoiceEHeadLocDto>> getListPaged(@RequestBody GwOcrInvoiceEHeadLocParam gwOcrInvoiceIHeadLocParam, PageParam pageParam, UserInfoToken userInfo) {
        gwOcrInvoiceIHeadLocParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<GwOcrInvoiceEHeadLocDto>> paged = service.getListPaged(gwOcrInvoiceIHeadLocParam, pageParam);
        return paged;
    }

    @ApiOperation("getOne")
    @GetMapping("getOne/{sid}")
    public ResultObject<GwOcrInvoiceEHeadLocDto> getOne(@PathVariable String sid) {
        GwOcrInvoiceEHeadLocDto dto = service.getOne(sid);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("获取成功"), dto);
    }
}
