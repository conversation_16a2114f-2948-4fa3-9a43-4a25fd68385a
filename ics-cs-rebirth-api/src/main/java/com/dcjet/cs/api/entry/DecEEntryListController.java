package com.dcjet.cs.api.entry;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.entry.DecEEntryListDto;
import com.dcjet.cs.dto.entry.DecEEntryListParam;
import com.dcjet.cs.dto.entry.DecIEntryListDto;
import com.dcjet.cs.dto.entry.DecIEntryListParam;
import com.dcjet.cs.entry.model.DecEEntryList;
import com.dcjet.cs.entry.service.DecEEntryListService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-7-19
 */
@RestController
@RequestMapping("v1/decEEntryList")
@Api(tags = "出口报关单(LG)-出口报关单表体接口")
public class DecEEntryListController extends BaseController {
    @Resource
    private DecEEntryListService decEEntryListService;
    @Resource
    private ExcelService excelService;
    @Resource
    private CommonService commonService;
    /**
     * @param decEEntryListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("出口报关单表体分页查询接口")
    @PostMapping("list")
    public ResultObject<List<DecEEntryListDto>> getListPaged(@RequestBody DecEEntryListParam decEEntryListParam, PageParam pageParam, UserInfoToken userInfo) {
        // decEEntryListParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<DecEEntryListDto>> paged = decEEntryListService.getListPaged(decEEntryListParam, pageParam, userInfo);
        return paged;
    }

    /**
     * 功能描述:修改  维护检验信息
     * JJL ADD 2020-03-20
     * @param sid
     * @param decEEntryListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("进口报关单表体检验信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<DecEEntryList> update(@PathVariable String sid, @Valid @RequestBody DecEEntryListParam decEEntryListParam, UserInfoToken userInfo) {
        decEEntryListParam.setSid(sid);
        DecEEntryListDto decEEntryListDto = decEEntryListService.update(decEEntryListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (decEEntryListDto != null) {
            resultObject.setData(decEEntryListDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }


    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("商品检验检疫货物Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<DecEEntryListParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<DecEEntryListDto> decEEntryListDtos = decEEntryListService.selectAll(exportParam.getExportColumns(), userInfo);
        //该页面导出特殊，导入功能需要根据导出模板做修改导入，转换成中文的话，做导入，系统不识别
        //decEEntryListDtos = convertForPrint(decEEntryListDtos);
        String sheetName = xdoi18n.XdoI18nUtil.t("检验信息");
        return excelService.getExcelHeadersMat(sheetName, URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), decEEntryListDtos, false);
    }

    /**
     * 导出pCode转换中文名称sheetName,
     *
     * @param list
     * @return
     */
    public List<DecEEntryListDto> convertForPrint(List<DecEEntryListDto> list) {
        for (DecEEntryListDto item : list) {
            item.setOriginCountry(commonService.convertPCode(item.getOriginCountry(),PCodeType.COUNTRY_OUTDATED));
            item.setPurPose(commonService.convertPCode(item.getPurPose(),PCodeType.USE_TO_CIQ));
        }
        return list;
    }

    /**
     * 批量修改导出
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("出口-批量修改规格型号导出接口")
    @PostMapping("exportList")
    public ResponseEntity exportList(@Valid @RequestBody BasicExportParam<DecEEntryListParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<DecEEntryListDto> decEEntryListDtos = decEEntryListService.selectAll(exportParam.getExportColumns(), userInfo);
        String sheetName = xdoi18n.XdoI18nUtil.t("规格型号");
        return excelService.getExcelHeadersBillListAndEntryList(sheetName, URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), decEEntryListDtos);
    }
}
