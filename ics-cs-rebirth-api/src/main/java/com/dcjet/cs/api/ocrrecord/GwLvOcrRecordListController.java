package com.dcjet.cs.api.ocrrecord;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.ocrrecord.GwLvOcrRecordListDto;
import com.dcjet.cs.dto.ocrrecord.GwLvOcrRecordListExportParam;
import com.dcjet.cs.dto.ocrrecord.GwLvOcrRecordListParam;
import com.dcjet.cs.ocrrecord.service.GwLvOcrRecordListService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.XdoMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2022-4-26
 */
@RestController
@RequestMapping("v1/gwLvOcrRecordList")
@Api(tags = "接口")
public class GwLvOcrRecordListController extends BaseController {
    @Resource
    private GwLvOcrRecordListService gwLvOcrRecordListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param gwLvOcrRecordListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @XdoMenuAuthentication(value = "/cs/ocr/IdentifyInfoOcrMain")
    public ResultObject<List<GwLvOcrRecordListDto>> getListPaged(@RequestBody GwLvOcrRecordListParam gwLvOcrRecordListParam, PageParam pageParam, UserInfoToken userInfo) {
        gwLvOcrRecordListParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<GwLvOcrRecordListDto>> paged = gwLvOcrRecordListService.getListPaged(gwLvOcrRecordListParam, pageParam);
        return paged;
    }

    /**
     * @param gwLvOcrRecordListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<GwLvOcrRecordListDto> insert(@Valid @RequestBody GwLvOcrRecordListParam gwLvOcrRecordListParam, UserInfoToken userInfo) {
        ResultObject<GwLvOcrRecordListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        GwLvOcrRecordListDto gwLvOcrRecordListDto = gwLvOcrRecordListService.insert(gwLvOcrRecordListParam, userInfo);
        if (gwLvOcrRecordListDto != null) {
            resultObject.setData(gwLvOcrRecordListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param gwLvOcrRecordListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<GwLvOcrRecordListDto> update(@PathVariable String sid, @Valid @RequestBody GwLvOcrRecordListParam gwLvOcrRecordListParam, UserInfoToken userInfo) {
        gwLvOcrRecordListParam.setSid(sid);
        GwLvOcrRecordListDto gwLvOcrRecordListDto = gwLvOcrRecordListService.update(gwLvOcrRecordListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (gwLvOcrRecordListDto != null) {
            resultObject.setData(gwLvOcrRecordListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
        gwLvOcrRecordListService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody GwLvOcrRecordListExportParam exportParam, UserInfoToken userInfo) throws Exception {
        List<GwLvOcrRecordListDto> gwLvOcrRecordListDtos = gwLvOcrRecordListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(gwLvOcrRecordListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), gwLvOcrRecordListDtos);
    }

    /**
     * 导出，pCode转换中文名称
     *
     * @param list
     * @return
     */
    public void convertForPrint(List<GwLvOcrRecordListDto> list) {
        for (GwLvOcrRecordListDto item : list) {
        }
    }
}
