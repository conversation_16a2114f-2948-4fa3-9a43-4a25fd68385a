package com.dcjet.cs.api.statRptGroup.erp;

import com.dcjet.cs.api.statRptGroup.GroupBaseController;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.company.model.GwCompany;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.statRpt.erp.dto.PercentOfAgent;
import com.dcjet.cs.dto.statRpt.erp.dto.PercentOfForward;
import com.dcjet.cs.dto.statRpt.erp.param.PercentOfAgentParam;
import com.dcjet.cs.statRpt.erp.service.PercentOfAgentService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/statRptGroup/erp/agentPercent")
@Api(tags = "统计报表-报关单统计-通关占比-进出口统计")
public class GroupPercentOfAgentController extends GroupBaseController {

    @Resource
    private PercentOfAgentService service;

    @Resource
    private ExcelService excelService;

    @ApiOperation("分页查询")
    @PostMapping("list")
    public ResultObject<List<PercentOfAgent>> getListPaged(@Valid @RequestBody PercentOfAgentParam param, PageParam pageParam, UserInfoToken token) {

        StringBuilder companyCode = new StringBuilder();
        List<GwCompany> companyList = getCompanyListByToken(token.getAccessToken());
        if (CollectionUtils.isNotEmpty(companyList)) {
            for (GwCompany company : companyList) {
                companyCode.append(company.getCorpCode() + ",");
            }
        }

        if (StringUtils.isBlank(param.getTradeCode())) {
            param.setTradeCode(companyCode.toString());
        }

        return service.getGroupListPaged(companyList, param);
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<PercentOfAgentParam> exportParam, UserInfoToken userInfo) throws Exception {

        StringBuilder companyCode = new StringBuilder();
        List<GwCompany> companyList = getCompanyListByToken(userInfo.getAccessToken());
        if (CollectionUtils.isNotEmpty(companyList)) {
            for (GwCompany company : companyList) {
                companyCode.append(company.getCorpCode() + ",");
            }
        }

        if (StringUtils.isBlank(exportParam.getExportColumns().getTradeCode())) {
            exportParam.getExportColumns().setTradeCode(companyCode.toString());
        }

        List<PercentOfAgent> percentOfAgents = service.getGroupList(companyList, exportParam.getExportColumns(), userInfo);

        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), percentOfAgents);
    }
}
