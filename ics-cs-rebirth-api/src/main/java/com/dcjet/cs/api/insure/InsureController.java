package com.dcjet.cs.api.insure;

import com.dcjet.cs.dto.insure.InsureRateDto;
import com.dcjet.cs.insure.InsureService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("v1/insureController")
@Api(tags = "投保接口")
public class InsureController extends BaseController {
    @Resource
    private InsureService insureService;

    @ApiOperation("获取企业级保司费率接口")
    @PostMapping("/getCompanyRateList")
    public ResultObject<List<InsureRateDto>> getCompanyRateList(PageParam pageParam, UserInfoToken token) {
        return insureService.getCompanyRateList(pageParam, token);
    }

    @ApiOperation("投保接口")
    @PostMapping("/insure/{ieMark}/{sid}")
    public ResultObject<String> insure(@PathVariable("ieMark") String ieMark, @PathVariable("sid") String sid, UserInfoToken token) {
        return insureService.insure(ieMark, sid, token);
    }

    @ApiOperation("获取投保弹出页面地址")
    @GetMapping("/getInsurePageUrl")
    public ResultObject<String> getInsurePageUrl(UserInfoToken token) {
        return insureService.getInsurePageUrl(token);
    }
}
