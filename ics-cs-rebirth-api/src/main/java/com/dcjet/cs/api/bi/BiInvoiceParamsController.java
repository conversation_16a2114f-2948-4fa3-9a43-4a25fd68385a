package com.dcjet.cs.api.bi;

import com.dcjet.cs.bi.service.BiInvoiceParamsService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.BiInvoiceParamsDto;
import com.dcjet.cs.dto.bi.BiInvoiceParamsParam;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/biInvoiceParams")
@Api(tags = "发票管理-发票参数接口")
public class BiInvoiceParamsController {
    @Resource
    private BiInvoiceParamsService biInvoiceParamsService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param biInvoiceParamsParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("发票参数分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BiInvoiceParamsDto>> getListPaged(@RequestBody BiInvoiceParamsParam biInvoiceParamsParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BiInvoiceParamsDto>> paged = biInvoiceParamsService.getListPaged(biInvoiceParamsParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param biInvoiceParamsParam
     * @param userInfo
     * @return
     */
    @ApiOperation("发票参数新增接口")
    @PostMapping()
    public ResultObject<BiInvoiceParamsDto> insert(@Valid @RequestBody BiInvoiceParamsParam biInvoiceParamsParam, UserInfoToken userInfo) {
        ResultObject<BiInvoiceParamsDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        BiInvoiceParamsDto biInvoiceParamsDto = biInvoiceParamsService.insert(biInvoiceParamsParam, userInfo);
        if (biInvoiceParamsDto != null) {
            resultObject.setData(biInvoiceParamsDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param biInvoiceParamsParam
     * @param userInfo
     * @return
     */
    @ApiOperation("发票参数修改接口")
    @PutMapping("{sid}")
    public ResultObject<BiInvoiceParamsDto> update(@PathVariable String sid, @Valid @RequestBody BiInvoiceParamsParam biInvoiceParamsParam, UserInfoToken userInfo) {
        biInvoiceParamsParam.setSid(sid);
        BiInvoiceParamsDto biInvoiceParamsDto = biInvoiceParamsService.update(biInvoiceParamsParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (biInvoiceParamsDto != null) {
            resultObject.setData(biInvoiceParamsDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("更新失败!"));
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("发票参数删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        biInvoiceParamsService.delete(sids);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("发票参数Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<BiInvoiceParamsParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<BiInvoiceParamsDto> biInvoiceParamsDtos = biInvoiceParamsService.selectAll(exportParam.getExportColumns(), userInfo);
        biInvoiceParamsDtos = convertForPrint(biInvoiceParamsDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), biInvoiceParamsDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<BiInvoiceParamsDto> convertForPrint(List<BiInvoiceParamsDto> list) {
        for (BiInvoiceParamsDto item : list) {
        }
        return list;
    }
}
