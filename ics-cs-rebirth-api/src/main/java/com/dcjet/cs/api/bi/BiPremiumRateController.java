package com.dcjet.cs.api.bi;

import com.dcjet.cs.bi.service.BiPremiumRateService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.bi.BiPremiumRateDto;
import com.dcjet.cs.dto.bi.BiPremiumRateParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-9-5
 */
@RestController
@RequestMapping("v1/biPremiumRate")
@Api(tags = "企业参数库-保费率接口")
public class BiPremiumRateController extends BaseController {
    @Resource
    private BiPremiumRateService biPremiumRateService;
    @Resource
    private ExcelService excelService;

    /**
     * @param biPremiumRateParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BiPremiumRateDto>> getListPaged(@RequestBody BiPremiumRateParam biPremiumRateParam, PageParam pageParam, UserInfoToken userInfo) {
        biPremiumRateParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BiPremiumRateDto>> paged = biPremiumRateService.getListPaged(biPremiumRateParam, pageParam);
        return paged;
    }

    /**
     * @param biPremiumRateParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BiPremiumRateDto> insert(@Valid @RequestBody BiPremiumRateParam biPremiumRateParam, UserInfoToken userInfo) {
        ResultObject<BiPremiumRateDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        BiPremiumRateDto biPremiumRateDto = biPremiumRateService.insert(biPremiumRateParam, userInfo);
        if (biPremiumRateDto != null) {
            resultObject.setData(biPremiumRateDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param biPremiumRateParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BiPremiumRateDto> update(@PathVariable String sid, @Valid @RequestBody BiPremiumRateParam biPremiumRateParam, UserInfoToken userInfo) {
        biPremiumRateParam.setSid(sid);
        BiPremiumRateDto biPremiumRateDto = biPremiumRateService.update(biPremiumRateParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (biPremiumRateDto != null) {
            resultObject.setData(biPremiumRateDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        biPremiumRateService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("启用/停用")
    @PostMapping("/changeStatus/{sids}/{status}")
    public ResultObject changeStatus(@PathVariable List<String> sids, @PathVariable String status, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("状态修改成功!"));
        biPremiumRateService.changeStatus(sids, status);
        return resultObject;
    }

}
