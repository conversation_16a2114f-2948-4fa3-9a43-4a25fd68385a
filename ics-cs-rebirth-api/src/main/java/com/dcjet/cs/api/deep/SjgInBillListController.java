package com.dcjet.cs.api.deep;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.deep.model.SjgInBillHead;
import com.dcjet.cs.deep.model.SjgInBillTdList;
import com.dcjet.cs.deep.service.SjgInBillHeadService;
import com.dcjet.cs.deep.service.SjgInBillListService;
import com.dcjet.cs.deep.service.SjgInBillTdListService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.deep.SjgFacGNoInfo;
import com.dcjet.cs.dto.deep.SjgInBillListDto;
import com.dcjet.cs.dto.deep.SjgInBillListParam;
import com.dcjet.cs.dto.deep.SjgInCheckListParam;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.File;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
/**
 * generated by Generate dc
 *
 *
 * <AUTHOR>
 * @date: 2019-11-5
 */
@RestController
@RequestMapping("v1/sjgInBillList")
@Api(tags = "新深加工管理-深加工转入数据管理表体")
public class SjgInBillListController {
    @Resource
    private SjgInBillListService sjgInBillListService;
    @Resource
    private PCodeHolder pCodeServiceHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private SjgInBillTdListService sjgInBillTdListService;
    @Resource
    private SjgInBillHeadService sjgInBillHeadService;
    @Resource
    private ExportService exportService;
    /**
     * @param sjgInBillListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<SjgInBillListDto>> getListPaged(@RequestBody SjgInBillListParam sjgInBillListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<SjgInBillListDto>> paged = sjgInBillListService.getListPaged(sjgInBillListParam, pageParam);
        return paged;
    }

    @ApiOperation("汇总数量，金额接口")
    @PostMapping("sum")
    public ResultObject getSum(@RequestBody SjgInBillListParam sjgInBillListParam, UserInfoToken userInfo) {
        Pair<BigDecimal, BigDecimal> result = sjgInBillListService.getSum(sjgInBillListParam);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("汇总完成"), Arrays.asList(result.getLeft(), result.getRight()));
    }

    /**
     * @param sjgInBillListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<SjgInBillListDto> insert(@Valid @RequestBody SjgInBillListParam sjgInBillListParam, UserInfoToken userInfo) {
        return sjgInBillListService.insert(sjgInBillListParam, userInfo);
    }
    /**
     * @param sid
     * @param sjgInBillListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<SjgInBillListDto> update(@PathVariable String sid, @Valid @RequestBody SjgInBillListParam sjgInBillListParam, UserInfoToken userInfo) {
        sjgInBillListParam.setSid(sid);
        return sjgInBillListService.update(sjgInBillListParam, userInfo);
    }

    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        // 检查是否存在表体数据
        Pair<Boolean, String> result = sjgInBillListService.delete(sids, userInfo);
        return ResultObject.createInstance(result.getLeft(), result.getRight());
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<SjgInBillListParam>  exportParam, UserInfoToken userInfo) throws Exception{
        List<SjgInBillListDto> sjgInBillListDtos = sjgInBillListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(sjgInBillListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), sjgInBillListDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<SjgInBillListDto> list) {
        for(SjgInBillListDto item : list) {
            if(item.getUnit()!=null) {
                item.setUnit(item.getUnit() + " " +  pCodeServiceHolder.getValue(PCodeType.UNIT, item.getUnit()));
            }
            if(item.getCurr()!=null) {
                item.setCurr(item.getCurr() + " " +  pCodeServiceHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurr()));
            }

        }
    }
    /**
     * 提取数据-条件筛选
     * @param headId
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("提取数据-条件筛选")
    @PostMapping(value = "/extractDataByQuery/{headId}")
    public ResultObject extractDataByQuery(@PathVariable("headId")String headId, @RequestBody SjgInCheckListParam param, UserInfoToken userInfo) {
        Pair<Boolean, String> result = sjgInBillListService.extractCheckDataByQuery(param, headId, userInfo);
        return ResultObject.createInstance(result.getLeft(), result.getRight());
    }
    /**
     * 提取数据-勾选
     * @param headId
     * @param checkIds
     * @param userInfo
     * @return
     */
    @ApiOperation("提取数据-勾选")
    @PostMapping(value = "/extractDataByChoose/{headId}")
    public ResultObject extractDataByChoose(@PathVariable("headId")String headId, @RequestBody List<String> checkIds, UserInfoToken userInfo) {
        Pair<Boolean, String> result = sjgInBillListService.extractCheckDataByChoose(checkIds, headId, userInfo);
        return ResultObject.createInstance(result.getLeft(), result.getRight());
    }
    /**
     * 匹配备案序号
     * @param headId
     * @param userInfo
     * @return
     */
    @ApiOperation("匹配备案序号")
    @GetMapping(value="/matchOrgSerialNo/{headId}")
    public ResultObject matchOrgSerialNo(@PathVariable("headId")String headId, UserInfoToken userInfo) {
        Pair<Boolean, String> result = sjgInBillListService.matchOrgSerialNo(headId, userInfo);
        return ResultObject.createInstance(result.getLeft(), result.getRight());
    }

    /**
     * 生成清单序号
     * @param headId
     * @param userInfo
     * @return
     */
    @ApiOperation("生成清单序号")
    @GetMapping("/generateBillSerialNo/{headId}")
    public ResultObject generateBillSerialNo(@PathVariable("headId")String headId, UserInfoToken userInfo) {
        Pair<Boolean, String> result = sjgInBillListService.generateBillSerialNo(headId, userInfo);
        return ResultObject.createInstance(result.getLeft(), result.getRight());
    }

//    /**
//     * 打印发票箱单
//     * @param headId
//     * @param userInfo
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation("打印发票箱单")
//    @GetMapping(value = "/printInvBox/{headId}")
//    public ResponseEntity printInvBox(@PathVariable("headId")String headId, UserInfoToken userInfo) throws Exception {
//        return sjgInBillListService.printInvBox(headId, userInfo);
//    }

    /**
     * 生成报关信息
     * @param headId
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("生成报关信息")
    @GetMapping(value="/generateErpDecIHeadN/{headId}")
    public ResultObject generateErpDecIHeadN(@PathVariable("headId")String headId, UserInfoToken userInfo) throws Exception{
        Pair<Boolean, String> result = sjgInBillTdListService.generateDecErpIHeadN(headId, userInfo);
        return ResultObject.createInstance(result.getLeft(), result.getRight());
    }

    @ApiOperation("获取指定企业料号的备案关系列表")
    @GetMapping(value = "/getRelationByFacGNo/{headId}/{facGNoIn}")
    public ResultObject<SjgFacGNoInfo> getRelationByFacGNo(@PathVariable("headId")String headId,
                                                                 @PathVariable("facGNoIn")String facGNoIn,
                                                                 UserInfoToken userInfo) {
        Pair<Boolean, Object> res = sjgInBillListService.getRelationByFacGNo(headId, facGNoIn, userInfo);
        if(res.getLeft()) {
            return ResultObject.createInstance(res.getLeft(), xdoi18n.XdoI18nUtil.t("获取成功"), res.getRight());
        }
        return ResultObject.createInstance(res.getLeft(), res.getRight().toString());
    }
    /**
     * 发票箱单打印
     * @Prarm
     */
    @ApiOperation(value="发票箱单打印pdf")
    @GetMapping("printInvBox/{sid}")
    public ResponseEntity printEntry(@PathVariable("sid") String sid, UserInfoToken userinfo, HttpServletRequest request) throws Exception{
        String lang = request.getParameter("lang");
        sid = URLDecoder.decode(sid,CommonVariable.UTF8);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMDD");
        String newDate = formatter.format(new Date());
        String templateName = StringUtils.isNotEmpty(lang) && lang.equals("zh-CN") ? "sjg_in_bill.xlsx" : "sjg_in_bill_en.xlsx";
        String outName = xdoi18n.XdoI18nUtil.t("发票箱单打印") + newDate + ".pdf";
        String fileName = java.util.UUID.randomUUID().toString() + ".pdf";

        List<SjgInBillHead> sjgInBillHeads = convertBillForPrint(sjgInBillHeadService.selectBySid(sid,userinfo.getCompany()),userinfo);
        List<SjgInBillTdList> sjgInBillLists = convertBillForListPrint(sjgInBillHeadService.selectListAll(sid),userinfo);
        String exportFileName =  "";
        if(sjgInBillHeads.size()>0){
            exportFileName = exportService.export(sjgInBillHeads,sjgInBillLists,fileName,templateName);
        }
        HttpHeaders headers = new HttpHeaders();
        //解决空格变成+号问题
        outName = URLEncoder.encode(outName, CommonVariable.UTF8);
        outName = outName.replaceAll("\\+","%20");
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(exportFileName)),headers, HttpStatus.OK);
    }
    /**
     * 发票箱单表头参数转换
     * @param list
     * @param userinfo
     * <AUTHOR>
    public List<SjgInBillHead> convertBillForPrint(List<SjgInBillHead> list,UserInfoToken userinfo){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMDD");
        String newDate = formatter.format(new Date());
        for(SjgInBillHead sjgInBillHead:list){
            sjgInBillHead.setEndDateTo(newDate);
        }
        return list;
    }
    /**
     * 发票箱单标题参数转换
     * @param list
     * @param userinfo
     */
    public List<SjgInBillTdList> convertBillForListPrint(List<SjgInBillTdList> list,UserInfoToken userinfo){
        for(SjgInBillTdList sjgInBillTList : list){
            sjgInBillTList.setCurr(pCodeServiceHolder.getValue(PCodeType.CURR_OUTDATED,sjgInBillTList.getCurr()));
            sjgInBillTList.setUnit(pCodeServiceHolder.getValue(PCodeType.UNIT,sjgInBillTList.getUnit()));
        }
        return list;
    }

}
