package com.dcjet.cs.api.eml;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.eml.GwBalanceListDto;
import com.dcjet.cs.dto.eml.GwEmlBalanceDto;
import com.dcjet.cs.dto.eml.GwEmlBalanceParam;
import com.dcjet.cs.dto.erp.DecErpIHeadNDto;
import com.dcjet.cs.dto.erp.DecErpIHeadNParam;
import com.dcjet.cs.dto.erp.sap.ErpWipStockDto;
import com.dcjet.cs.dto.tax.TaxListIDto;
import com.dcjet.cs.eml.service.GwEmlBalanceService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2023-2-27
 */
@RestController
@RequestMapping("v1/gwEmlBalance")
@Api(tags = "手册平衡数据接口")
public class GwEmlBalanceController extends BaseController {
    @Resource
    private GwEmlBalanceService gwEmlBalanceService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    /**
     * @param gwEmlBalanceParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("获取明细信息查询接口")
    @PostMapping("list")
    public ResultObject<List<GwEmlBalanceDto>> getListPaged(@RequestBody GwEmlBalanceParam gwEmlBalanceParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<GwEmlBalanceDto>> paged = gwEmlBalanceService.getListPaged(gwEmlBalanceParam, pageParam,userInfo);
        return paged;
    }
    @ApiOperation("手册平衡查询接口")
    @PostMapping("/getBalanceIn")
    public ResultObject<List<GwBalanceListDto>> getBalanceIn(@RequestBody GwEmlBalanceParam gwEmlBalanceParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<GwBalanceListDto>> paged = gwEmlBalanceService.getBalanceIn(gwEmlBalanceParam, pageParam,userInfo);
        return paged;
    }
    @ApiOperation("出口汇总查询接口")
    @PostMapping("/getBalanceOut")
    public ResultObject<List<GwBalanceListDto>> getBalanceOut(@RequestBody GwEmlBalanceParam gwEmlBalanceParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<GwBalanceListDto>> paged = gwEmlBalanceService.getBalanceOut(gwEmlBalanceParam, pageParam,userInfo);
        return paged;
    }
    @ApiOperation("出口耗用明细查询接口")
    @PostMapping("/getBalanceListOut")
    public ResultObject<List<GwEmlBalanceDto>> getBalanceListOut(@RequestBody GwEmlBalanceParam gwEmlBalanceParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<GwEmlBalanceDto>> paged = gwEmlBalanceService.getBalanceListOut(gwEmlBalanceParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("出口无BOM(单损耗)接口")
    @PostMapping("/getBalanceNoBom")
    public ResultObject<List<GwEmlBalanceDto>> getBalanceNoBom(@RequestBody GwEmlBalanceParam gwEmlBalanceParam, PageParam pageParam, UserInfoToken userInfo) {
        gwEmlBalanceParam.setIsBom("0");
        ResultObject<List<GwEmlBalanceDto>> paged = gwEmlBalanceService.getBalanceListOut(gwEmlBalanceParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("获取明细信息导出接口")
    @PostMapping("exportList")
    public ResponseEntity exportList(@Valid @RequestBody BasicExportParam<GwEmlBalanceParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<GwEmlBalanceDto> gwEmlBalanceDtoList = gwEmlBalanceService.selectAll(exportParam.getExportColumns(), userInfo);
        gwEmlBalanceDtoList = convertForPrint(gwEmlBalanceDtoList);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), gwEmlBalanceDtoList);
    }
    @ApiOperation("手册平衡查询导出接口")
    @PostMapping("exportBalanceIn")
    public ResponseEntity exportBalanceIn(@Valid @RequestBody BasicExportParam<GwEmlBalanceParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<GwBalanceListDto> gwEmlBalanceDtoList = gwEmlBalanceService.selectBalanceInAll(exportParam.getExportColumns(), userInfo);
        gwEmlBalanceDtoList = convertListForPrint(gwEmlBalanceDtoList);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), gwEmlBalanceDtoList);
    }
    @ApiOperation("出口汇总查询导出接口")
    @PostMapping("exportBalanceOut")
    public ResponseEntity exportBalanceOut(@Valid @RequestBody BasicExportParam<GwEmlBalanceParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<GwBalanceListDto> gwEmlBalanceDtoList = gwEmlBalanceService.selectBalanceOutAll(exportParam.getExportColumns(), userInfo);
        gwEmlBalanceDtoList = convertListForPrint(gwEmlBalanceDtoList);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), gwEmlBalanceDtoList);
    }
    @ApiOperation("出口耗用明细查询导出接口")
    @PostMapping("exportBalanceListOut")
    public ResponseEntity exportBalanceListOut(@Valid @RequestBody BasicExportParam<GwEmlBalanceParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<GwEmlBalanceDto> gwEmlBalanceDtoList = gwEmlBalanceService.selectBalanceListOutAll(exportParam.getExportColumns(), userInfo);
        gwEmlBalanceDtoList = convertForPrint(gwEmlBalanceDtoList);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), gwEmlBalanceDtoList);
    }
    @ApiOperation("出口无BOM(单损耗)导出接口")
    @PostMapping("exportBalanceNoBom")
    public ResponseEntity exportBalanceNoBom(@Valid @RequestBody BasicExportParam<GwEmlBalanceParam> exportParam, UserInfoToken userInfo) throws Exception {
        exportParam.getExportColumns().setIsBom("0");
        List<GwEmlBalanceDto> gwEmlBalanceDtoList = gwEmlBalanceService.selectBalanceListOutAll(exportParam.getExportColumns(), userInfo);
        gwEmlBalanceDtoList = convertForPrint(gwEmlBalanceDtoList);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), gwEmlBalanceDtoList);
    }

    private List<GwEmlBalanceDto> convertForPrint(List<GwEmlBalanceDto> list) {
        for (GwEmlBalanceDto item : list) {
            item.setImgUnit(commonService.convertPCode(item.getImgUnit(),PCodeType.UNIT));
            item.setExgUnit(commonService.convertPCode(item.getExgUnit(),PCodeType.UNIT));
            item.setTradeMode(commonService.convertPCode(item.getTradeMode(), PCodeType.TRADE));
        }
        return list;
    }
    private List<GwBalanceListDto> convertListForPrint(List<GwBalanceListDto> list) {
        for (GwBalanceListDto item : list) {
            item.setImgUnit(commonService.convertPCode(item.getImgUnit(),PCodeType.UNIT));
            item.setExgUnit(commonService.convertPCode(item.getExgUnit(),PCodeType.UNIT));
        }
        return list;
    }
}
