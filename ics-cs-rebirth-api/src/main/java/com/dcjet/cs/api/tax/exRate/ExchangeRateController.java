package com.dcjet.cs.api.tax.exRate;

import com.dcjet.cs.dto.tax.exRate.ExchangeRateDto;
import com.dcjet.cs.dto.tax.exRate.ExchangeRateParam;
import com.dcjet.cs.tax.exRate.service.ExchangeRateService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-12-5
 */
@RestController
@RequestMapping("v1/exchangeRate")
@Api(tags = "税金管理-汇率")
public class ExchangeRateController {
    @Resource
    private ExchangeRateService exchangeRateService;

    /**
     * @param exchangeRateParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<ExchangeRateDto>> getListPaged(@RequestBody ExchangeRateParam exchangeRateParam, PageParam pageParam, UserInfoToken userInfo) {
        // exchangeRateParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<ExchangeRateDto>> paged = exchangeRateService.getListPaged(exchangeRateParam, pageParam);
        return paged;
    }

    /**
     * @param exchangeRateParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<ExchangeRateDto> insert(@Valid @RequestBody ExchangeRateParam exchangeRateParam, UserInfoToken userInfo) {
        ResultObject<ExchangeRateDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        ExchangeRateDto exchangeRateDto = exchangeRateService.insert(exchangeRateParam, userInfo);
        if (exchangeRateDto != null) {
            resultObject.setData(exchangeRateDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param exchangeRateParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<ExchangeRateDto> update(@PathVariable String sid, @Valid @RequestBody ExchangeRateParam exchangeRateParam, UserInfoToken userInfo) {
        exchangeRateParam.setSid(sid);
        ExchangeRateDto exchangeRateDto = exchangeRateService.update(exchangeRateParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (exchangeRateDto != null) {
            resultObject.setData(exchangeRateDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("更新失败!"));
        }
        return resultObject;
    }
}
