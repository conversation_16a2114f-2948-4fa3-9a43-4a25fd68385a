package com.dcjet.cs.api.gwstdentry;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.gwstdentry.GwstdEntryHeadDto;
import com.dcjet.cs.dto.gwstdentry.GwstdEntryHeadParam;
import com.dcjet.cs.dto.gwstdentry.GwstdEntryListDto;
import com.dcjet.cs.dto.gwstdentry.GwstdEntryListParam;
import com.dcjet.cs.gwstdentry.service.GwstdEntryListService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-3-10
 */
@RestController
@RequestMapping("v1/gwstdEntryList")
@Api(tags = "正式报关单-正式报关单表体接口")
public class GwstdEntryListController {
    @Resource
    private ExcelService excelService;

    @Resource
    private CommonService commonService;

    @Resource
    private GwstdEntryListService gwstdEntryListService;

    /**
     * @param gwstdEntryListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<GwstdEntryListDto>> getListPaged(@RequestBody GwstdEntryListParam gwstdEntryListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<GwstdEntryListDto>> paged = gwstdEntryListService.getListPaged(gwstdEntryListParam, pageParam);
        return paged;
    }

    /**
     * @param gwstdEntryHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("税金管理分页查询接口")
    @PostMapping("getTaxManageList")
    public ResultObject<List<GwstdEntryHeadDto>> getTaxManageList(@RequestBody GwstdEntryHeadParam gwstdEntryHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        gwstdEntryHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<GwstdEntryHeadDto>> paged = gwstdEntryListService.getTaxManageList(gwstdEntryHeadParam, pageParam);
        return paged;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("税金管理数据导出接口")
    @PostMapping("exportTaxManage")
    public ResponseEntity exportTaxManage(@Valid @RequestBody BasicExportParam<GwstdEntryHeadParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<GwstdEntryHeadDto> entryHeadDtos = gwstdEntryListService.selectAllTaxManageList(exportParam.getExportColumns(), userInfo);
        entryHeadDtos = convertForPrint(entryHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), entryHeadDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<GwstdEntryHeadDto> convertForPrint(List<GwstdEntryHeadDto> list) {
        for (GwstdEntryHeadDto item : list) {
            item.setIEMark(CommonEnum.I_E_MARK_ENUM.getValue(item.getIEMark()));
            item.setIEPort(commonService.convertPCode(item.getIEPort(), PCodeType.CUSTOMS_REL));
            item.setDistinatePort(commonService.convertPCode(item.getDistinatePort(), PCodeType.PORT_LIN));
            item.setTrafMode(commonService.convertPCode(item.getTrafMode(), PCodeType.TRANSF));
            item.setTradeAreaCode(commonService.convertPCode(item.getTradeAreaCode(), PCodeType.COUNTRY));
            item.setTradeMode(commonService.convertPCode(item.getTradeMode(), PCodeType.TRADE));
            item.setCutMode(commonService.convertPCode(item.getCutMode(), PCodeType.LEVYTYPE));
            item.setTransMode(commonService.convertPCode(item.getTransMode(), PCodeType.TRANSAC));
            // 运费标志
            item.setFeeMark(CommonEnum.FeeMarkEnumNoCode.getValue(item.getFeeMark()));
            item.setFeeCurr(commonService.convertPCode(item.getFeeCurr(), PCodeType.CURR));
            // 保费标志
            item.setInsurMark(CommonEnum.FeeMarkEnumNoCode.getValue(item.getInsurMark()));
            item.setInsurCurr(commonService.convertPCode(item.getInsurCurr(), PCodeType.CURR));
            // 杂费标志
            item.setOtherMark(CommonEnum.FeeMarkEnumNoCode.getValue(item.getOtherMark()));
            item.setOtherCurr(commonService.convertPCode(item.getOtherCurr(), PCodeType.CURR));
            item.setWrapType(commonService.convertPCode(item.getWrapType(), PCodeType.WRAP));
            item.setOriginCountry(commonService.convertPCode(item.getOriginCountry(), PCodeType.COUNTRY));
            item.setDestinationCountry(commonService.convertPCode(item.getDestinationCountry(), PCodeType.COUNTRY));
            item.setCurr(commonService.convertPCode(item.getCurr(), PCodeType.CURR));
            item.setGUnit(commonService.convertPCode(item.getGUnit(), PCodeType.UNIT));
            item.setUnit1(commonService.convertPCode(item.getUnit1(), PCodeType.UNIT));
            item.setUnit2(commonService.convertPCode(item.getUnit2(), PCodeType.UNIT));
        }
        return list;
    }
}
