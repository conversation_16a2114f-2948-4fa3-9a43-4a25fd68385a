package com.dcjet.cs.api.bi;

import com.dcjet.cs.bi.service.BiCustomerParamsService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.BiCountryRegionParam;
import com.dcjet.cs.dto.bi.BiCustomerParamsDto;
import com.dcjet.cs.dto.bi.BiCustomerParamsParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/biCustomerParams")
@Api(tags = "基础管理-企业参数库接口")
public class BiCustomerParamsController extends BaseController {
    @Resource
    private BiCustomerParamsService biCustomerParamsService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param paramType 自定义参数类型
     * @return
     */
    @ApiOperation("根据参数类型获取自定义参数值")
    @PostMapping("/getParamValues/{paramType}")
    public ResultObject<List<KeyValuePair>> getListPaged(@ApiParam(value = "参数类型") @PathVariable("paramType") String paramType, UserInfoToken userInfo) {
        ResultObject<List<KeyValuePair>> resultObject = ResultObject.createInstance(true);
        resultObject.setData(biCustomerParamsService.selectKeyValuesByParamType(paramType.toUpperCase(), userInfo));
        return resultObject;
    }

    /**
     * @param biCustomerParamsParam 自定义参数类型
     * @return
     */
    @ApiOperation("根据参数类型和自定义获取自定义参数值")
    @PostMapping("/getParamValues")
    public ResultObject<List<BiCustomerParamsDto>> getParamValues(@RequestBody BiCustomerParamsParam biCustomerParamsParam, UserInfoToken userInfo) {
        return biCustomerParamsService.getCustomerParamsData(biCustomerParamsParam, userInfo);
    }

    /**
     * @param biCustomerParamsParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业参数库分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BiCustomerParamsDto>> getListPaged(@RequestBody BiCustomerParamsParam biCustomerParamsParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BiCustomerParamsDto>> paged = biCustomerParamsService.getListPaged(biCustomerParamsParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param biCustomerParamsParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业参数库新增接口")
    @PostMapping()
    public ResultObject<BiCustomerParamsDto> insert(@Valid @RequestBody BiCustomerParamsParam biCustomerParamsParam, UserInfoToken userInfo) {
        ResultObject<BiCustomerParamsDto> resultObject = biCustomerParamsService.insert(biCustomerParamsParam, userInfo);
        return resultObject;
    }

    /**
     * @param sid
     * @param biCustomerParamsParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业参数库修改接口")
    @PutMapping("{sid}")
    public ResultObject<BiCustomerParamsDto> update(@PathVariable String sid, @Valid @RequestBody BiCustomerParamsParam biCustomerParamsParam, UserInfoToken userInfo) {
        biCustomerParamsParam.setSid(sid);
        ResultObject resultObject = biCustomerParamsService.update(biCustomerParamsParam, userInfo);
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("企业参数库删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        biCustomerParamsService.delete(sids);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("企业参数库Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<BiCustomerParamsParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<BiCustomerParamsDto> biCustomerParamsDtos = biCustomerParamsService.selectAll(exportParam.getExportColumns(), userInfo);
        biCustomerParamsDtos = convertForPrint(biCustomerParamsDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), biCustomerParamsDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<BiCustomerParamsDto> convertForPrint(List<BiCustomerParamsDto> list) {
        for (BiCustomerParamsDto item : list) {
            if ("UNIT".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + pCodeHolder.getValue(PCodeType.UNIT, item.getCustomParamCode()));
                }
            } else if ("CURR".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCustomParamCode()));
                }
            } else if ("COUNTRY".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getCustomParamCode()));
                }
            } else if ("BONDMARK".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(CommonEnum.BondMarkEnum.getValue(item.getCustomParamCode()));
                }
            } else if ("GMARK".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(CommonEnum.GMarkEnum.getValue(item.getCustomParamCode()));
                }
            } else if ("OUT_WAY".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + CommonEnum.InOutWayEnum.getValue(item.getCustomParamCode()));
                }
            } else if ("IN_WAY".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + CommonEnum.InOutWayEnum.getValue(item.getCustomParamCode()));
                }
            } else if ("INV_UNIT".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + pCodeHolder.getValue(PCodeType.UNIT, item.getCustomParamCode()));
                }
            } else if ("AVOID_PARAMS".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + CommonEnum.avoidEnum.getValue(item.getCustomParamCode()));
                }
            } else if ("INV_CURR".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCustomParamCode()));
                }
            } else if ("INV_STATE".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getCustomParamCode()));
                }
            } else if ("INV_TRAF".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + pCodeHolder.getValue(PCodeType.TRANSF, item.getCustomParamCode()));
                }
            } else if ("INV_WRAP".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + pCodeHolder.getValue(PCodeType.WRAP, item.getCustomParamCode()));
                }
            } else if ("PORT".equals(item.getParamsType())) {
                if (!StringUtils.isBlank(item.getCustomParamCode())) {
                    item.setCustomParamCode(item.getCustomParamCode() + " " + pCodeHolder.getValue(PCodeType.PORT_LIN, item.getCustomParamCode()));
                }
                if (!StringUtils.isBlank(item.getNote())) {
                    item.setNote(item.getNote() + " " + pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getNote()));
                }
            }
        }
        return list;
    }

    /**
     * @param biCustomerParamsParam 自定义参数类型
     * @return
     */
    @ApiOperation("校验ERP相关转换是否成功")
    @PostMapping("/checkParam")
    public ResultObject checkParam(@RequestBody BiCustomerParamsParam biCustomerParamsParam, UserInfoToken userInfo) {
        return biCustomerParamsService.checkParam(biCustomerParamsParam, userInfo);
    }

    /**
     * 获取企业参数库中国家地区分区信息
     */
    @ApiOperation("国家地区分区信息")
    @PostMapping("/getCountryRegion")
    public ResultObject getCountryRegion(@RequestBody BiCountryRegionParam biCountryRegionParam, PageParam pageParam) {
        return biCustomerParamsService.getCountryRegion(biCountryRegionParam, pageParam);
    }

    /**
     * 获取车辆类型列表
     */
    @ApiOperation("获取车辆类型列表")
    @PostMapping("/vehicle/list")
    public ResultObject<List<BiCustomerParamsDto>> getVehicleList(@RequestBody BiCustomerParamsParam biCustomerParamsParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BiCustomerParamsDto>> paged = biCustomerParamsService.getVehicleList(biCustomerParamsParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("车辆类型Excel数据导出接口")
    @PostMapping("/vehicle/export")
    public ResponseEntity exportVehicle(@Valid @RequestBody BasicExportParam<BiCustomerParamsParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<BiCustomerParamsDto> biCustomerParamsDtos = biCustomerParamsService.selectVehicleAll(exportParam.getExportColumns(), userInfo);
        biCustomerParamsDtos = convertForPrint(biCustomerParamsDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), biCustomerParamsDtos);
    }

    @ApiOperation("获取转换信息")
    @GetMapping("/getConvertedValue/{type}/{code}")
    public ResultObject getConvertedValue(@PathVariable String type, @PathVariable String code, UserInfoToken token) {
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("已获取"), biCustomerParamsService.getConvertedValue(type, code, token));
    }

    @ApiOperation("获取种类参数")
    @PostMapping("/getCategoryParameter")
    public ResultObject<BiCustomerParamsDto> getCategoryParameter(UserInfoToken token) {
        ResultObject<BiCustomerParamsDto> list = biCustomerParamsService.getCategoryParameter(token);
        return list;
    }
    @ApiOperation("获取种类参数")
    @PostMapping("/getBodyTypeParameters")
    public ResultObject<BiCustomerParamsDto> getBodyTypeParameters(UserInfoToken token) {
        ResultObject<BiCustomerParamsDto> list = biCustomerParamsService.getBodyTypeParameters(token);
        return list;
    }
}
