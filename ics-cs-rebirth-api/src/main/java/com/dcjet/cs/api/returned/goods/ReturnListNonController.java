package com.dcjet.cs.api.returned.goods;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.returned.GwReturnListDto;
import com.dcjet.cs.dto.returned.GwReturnListMatchDto;
import com.dcjet.cs.dto.returned.GwReturnListParam;
import com.dcjet.cs.returned.service.goods.ReturnListNonService;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-2-5
 */
@RestController
@RequestMapping("v1/returnListNonExg")
@Api(tags = "退运货物-表体接口")
public class ReturnListNonController extends BaseController {
    @Resource
    private ReturnListNonService returnListNonService;
    @Resource
    private ExcelService excelService;
    @Resource
    private CommonService commonService;

    /**
     * @param gwReturnListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<GwReturnListDto>> getListPaged(@RequestBody GwReturnListParam gwReturnListParam, PageParam pageParam, UserInfoToken userInfo) {
        gwReturnListParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<GwReturnListDto>> paged = returnListNonService.getListPaged(gwReturnListParam, pageParam);
        return paged;
    }

    /**
     * @param gwReturnListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<GwReturnListDto> insert(@Valid @RequestBody GwReturnListParam gwReturnListParam, UserInfoToken userInfo) {
        ResultObject<GwReturnListDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        GwReturnListDto gwReturnListDto = returnListNonService.insert(gwReturnListParam, userInfo);
        if (gwReturnListDto != null) {
            resultObject.setData(gwReturnListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param gwReturnListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<GwReturnListDto> update(@PathVariable String sid, @Valid @RequestBody GwReturnListParam gwReturnListParam, UserInfoToken userInfo) {
        gwReturnListParam.setSid(sid);
        GwReturnListDto gwReturnListDto = returnListNonService.update(gwReturnListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (gwReturnListDto != null) {
            resultObject.setData(gwReturnListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        returnListNonService.delete(sids);
        return resultObject;
    }

    /**
     * 匹配报关数据
     *
     * @param gwReturnListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("匹配报关数据-查询")
    @PostMapping("matchCustoms")
    public ResultObject matchCustoms(@RequestBody GwReturnListParam gwReturnListParam, PageParam pageParam,
                                     UserInfoToken userInfo) {
        return returnListNonService.matchCustoms(gwReturnListParam, pageParam, userInfo);
    }

    @ApiOperation("获取备案号")
    @PostMapping("getEmsNo")
    public ResultObject getEmsNo(UserInfoToken userInfo) {
        return returnListNonService.getEmsNo(userInfo);
    }

    /**
     * @param sid 表体sid
     * @param gwReturnListMatchDtoList
     * @param userInfo
     * @return
     */
    @ApiOperation("匹配报关数据-保存")
    @PostMapping("saveMatchCustoms/{sid}")
    public ResultObject saveMatchCustoms(@PathVariable String sid,
                                         @RequestBody List<GwReturnListMatchDto> gwReturnListMatchDtoList, UserInfoToken userInfo) {
        return returnListNonService.saveMatchCustoms(sid, gwReturnListMatchDtoList, userInfo);
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<GwReturnListParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<GwReturnListDto> gwReturnListDtos = returnListNonService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(gwReturnListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), gwReturnListDtos);
    }

    /**
     * 导出, pCode转换中文名称
     *
     * @param list
     * @return
     */
    public void convertForPrint(List<GwReturnListDto> list) {
        for (GwReturnListDto dto : list) {
            if (ConstantsStatus.STATUS_0.equals(dto.getStatus())) {
                dto.setStatus("0 未匹配");
            }
            if (ConstantsStatus.STATUS_1.equals(dto.getStatus())) {
                dto.setStatus("1 已匹配");
            }
            if (StringUtils.isNotBlank(dto.getUnit())) {
                dto.setUnit(commonService.convertPCode(dto.getUnit(), PCodeType.UNIT));
            }
            if (StringUtils.isNotBlank(dto.getOriginCountry())) {
                dto.setOriginCountry(commonService.convertPCode(dto.getOriginCountry(), PCodeType.COUNTRY_OUTDATED));
            }
            if (StringUtils.isNotBlank(dto.getDestinationCountry())) {
                dto.setDestinationCountry(commonService.convertPCode(dto.getDestinationCountry(), PCodeType.COUNTRY_OUTDATED));
            }
            if (StringUtils.isNotBlank(dto.getCurr())) {
                dto.setCurr(commonService.convertPCode(dto.getCurr(), PCodeType.CURR_OUTDATED));
            }
        }
    }
}
