package com.dcjet.cs.api.ocrInvoice;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.dev.DevFreeApplyHeadDto;
import com.dcjet.cs.dto.dev.DevFreeApplyHeadParam;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListDto;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListParam;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListSumInfo;
import com.dcjet.cs.ocrInvoice.service.GwOcrInvoiceIListService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@RestController
@RequestMapping("v1/gwOcrInvoiceIList")
@Api(tags = "OCR-进口发票箱单表体接口")
public class GwOcrInvoiceIListController extends BaseController {
    @Resource
    private GwOcrInvoiceIListService service;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private CommonService commonService;

    /**
     * @param gwOcrInvoiceIListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<GwOcrInvoiceIListDto>> getListPaged(@RequestBody GwOcrInvoiceIListParam gwOcrInvoiceIListParam, PageParam pageParam, UserInfoToken userInfo) {
        gwOcrInvoiceIListParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<GwOcrInvoiceIListDto>> paged = service.getListPaged(gwOcrInvoiceIListParam, pageParam);
        return paged;
    }

    @ApiOperation("表体汇总")
    @GetMapping("sumData/{headId}")
    public ResultObject<GwOcrInvoiceIListSumInfo> sumData(@PathVariable String headId, UserInfoToken token) {
        GwOcrInvoiceIListSumInfo result = service.sumByHeadId(headId, token);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("汇总完成"), service.sumByHeadId(headId, token));
    }

//    /**
//     * @param gwOcrInvoiceIListParam
//     * @param userInfo
//     * @return
//     */
//    @ApiOperation("新增接口")
//    @PostMapping()
//    public ResultObject<GwOcrInvoiceIListDto> insert(@Valid @RequestBody GwOcrInvoiceIListParam gwOcrInvoiceIListParam, UserInfoToken userInfo) {
//        ResultObject<GwOcrInvoiceIListDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
//        GwOcrInvoiceIListDto gwOcrInvoiceIListDto = gwOcrInvoiceIListService.insert(gwOcrInvoiceIListParam, userInfo);
//        if (gwOcrInvoiceIListDto != null) {
//            resultObject.setData(gwOcrInvoiceIListDto);
//        } else {
//            resultObject.setSuccess(false);
//            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
//        }
//        return resultObject;
//    }

    /**
     * @param sid
     * @param gwOcrInvoiceIListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<GwOcrInvoiceIListDto> update(@PathVariable String sid, @Valid @RequestBody GwOcrInvoiceIListParam gwOcrInvoiceIListParam, UserInfoToken userInfo) {
        gwOcrInvoiceIListParam.setSid(sid);
        GwOcrInvoiceIListDto gwOcrInvoiceIListDto = service.update(gwOcrInvoiceIListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (gwOcrInvoiceIListDto != null) {
            resultObject.setData(gwOcrInvoiceIListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }
//    // TODO patch api
//    /**
//     * @param sids
//     * @return
//     */
//    @ApiOperation("删除接口")
//    @DeleteMapping("{sids}")
//    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
//        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
//        // 检查是否存在表体数据
//		 gwOcrInvoiceIListService.delete(sids, userInfo);
//        return resultObject;
//    }



    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<GwOcrInvoiceIListParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<GwOcrInvoiceIListDto> gwOcrInvoiceIListDtos = service.selectAll(exportParam.getExportColumns(), userInfo);
        gwOcrInvoiceIListDtos = convertForPrint(gwOcrInvoiceIListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), gwOcrInvoiceIListDtos);
    }

    private List<GwOcrInvoiceIListDto> convertForPrint(List<GwOcrInvoiceIListDto> gwOcrInvoiceIListDtos) {
        for (GwOcrInvoiceIListDto item : gwOcrInvoiceIListDtos){
            item.setUnit(commonService.convertPCode(item.getUnit(), PCodeType.UNIT));
            item.setUnitConvert(commonService.convertPCode(item.getUnitConvert(), PCodeType.UNIT));
            item.setCurrConvert(commonService.convertPCode(item.getCurrConvert(), PCodeType.CURR_OUTDATED));
            item.setCurr(commonService.convertPCode(item.getCurr(), PCodeType.CURR_OUTDATED));
            item.setNetWtUnit(commonService.convertPCode(item.getNetWtUnit(), PCodeType.UNIT));
            item.setNetWtUnitConvert(commonService.convertPCode(item.getNetWtUnitConvert(), PCodeType.UNIT));
            item.setGrossWtUnit(commonService.convertPCode(item.getGrossWtUnit(), PCodeType.UNIT));
            item.setGrossWtUnitConvert(commonService.convertPCode(item.getGrossWtUnitConvert(), PCodeType.UNIT));
            item.setOriginCountry(commonService.convertPCode(item.getOriginCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setOriginCountryConvert(commonService.convertPCode(item.getOriginCountryConvert(), PCodeType.COUNTRY_OUTDATED));
        }
        return gwOcrInvoiceIListDtos;
    }


}
