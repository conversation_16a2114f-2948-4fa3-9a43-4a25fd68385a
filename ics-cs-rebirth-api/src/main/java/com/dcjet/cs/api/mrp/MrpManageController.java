package com.dcjet.cs.api.mrp;

import com.dcjet.cs.api.base.BasicController;
import com.dcjet.cs.dto.mrp.*;
import com.dcjet.cs.mrp.component.BillDcrFetchComponent;
import com.dcjet.cs.mrp.service.MrpManageService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 */
@Api(tags = "内销-试算管理")
@RestController
@RequestMapping("v1/mrp/manage")
public class Mrp<PERSON>anageController extends BasicController<MrpManageService, MrpManageDto, MrpManageParam, MrpManageSearchParam> {

    @Resource
    private BillDcrFetchComponent billDcrFetchComponent;

    @PostMapping("generate")
    @ApiOperation("生成进口提单管理")
    public ResultObject<MrpManageDto> generateEntry(@RequestBody @Valid MrpManageGenerateEntryParam param, UserInfoToken token) throws Exception {
        ResultObject<MrpManageDto> resultObject = ResultObject.createInstance();
        resultObject.setData(service.generateEntry(param, token));
        return resultObject;
    }

    @ApiOperation("刷新报核周期")
    @PostMapping("refresh/dcr")
    public ResultObject<Void> refresh(@RequestBody @Valid MrpBillDcrFetchParam param, UserInfoToken token) {
        billDcrFetchComponent.refresh(param, token);
        return ResultObject.createInstance(true);
    }
}