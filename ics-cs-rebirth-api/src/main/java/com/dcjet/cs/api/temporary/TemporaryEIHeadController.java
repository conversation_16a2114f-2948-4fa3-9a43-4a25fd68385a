package com.dcjet.cs.api.temporary;

import com.dcjet.cs.dto.temporary.TemporaryEIHeadDecParam;
import com.dcjet.cs.dto.temporary.TemporaryEIHeadDto;
import com.dcjet.cs.dto.temporary.TemporaryEIHeadParam;
import com.dcjet.cs.dto.temporary.TemporaryEIHeadSearchParam;
import com.dcjet.cs.temporary.service.TemporaryEIHeadService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * 复运进境表头
 * <p>
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-04-16
 */
@Api(tags = "暂时进出境-复运进境表头")
@RestController
@RequestMapping("v1/temporary/eihead")
public class TemporaryEIHeadController {

    @Resource
    private TemporaryEIHeadService temporaryEIHeadService;

    /**
     * 查询接口
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    @ApiOperation("列表")
    @PostMapping("list")
    public ResultObject<List<TemporaryEIHeadDto>> list(@RequestBody TemporaryEIHeadSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<TemporaryEIHeadDto>> paged = temporaryEIHeadService.list(searchParam, pageParam, token);
        return paged;
    }

    /***
     * 获取详细信息
     *
     * @param sid
     * @param token
     * @return
     */
    @ApiModelProperty("详细信息")
    @GetMapping("/{sid}")
    public ResultObject<TemporaryEIHeadDto> get(@PathVariable String sid, UserInfoToken token) {
        TemporaryEIHeadDto dto = temporaryEIHeadService.get(sid, token);
        return ResultObject.createInstance(dto, 1);
    }

    /**
     * 新增
     *
     * @param param
     * @param token
     * @return
     */
    @ApiOperation("新增")
    @PostMapping()
    public ResultObject<TemporaryEIHeadDto> insert(@Valid @RequestBody TemporaryEIHeadParam param, UserInfoToken token) {
        ResultObject<TemporaryEIHeadDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        TemporaryEIHeadDto dto = temporaryEIHeadService.insert(param, token);
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * 修改
     *
     * @param sid
     * @param param
     * @param token
     * @return
     */
    @ApiOperation("修改")
    @PutMapping("{sid}")
    public ResultObject<TemporaryEIHeadDto> update(@PathVariable String sid, @Valid @RequestBody TemporaryEIHeadParam param, UserInfoToken token) {
        TemporaryEIHeadDto dto = temporaryEIHeadService.update(sid, param, token);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * 删除
     *
     * @param sids
     * @return
     */
    @ApiOperation("删除")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        temporaryEIHeadService.delete(sids, token);
        return resultObject;
    }

    @ApiOperation("报关生成")
    @PostMapping("dec")
    public ResultObject dec(@Valid @RequestBody TemporaryEIHeadDecParam param, UserInfoToken token) throws Exception {
        temporaryEIHeadService.dec(param, token);
        return ResultObject.createInstance(true, 0);
    }
}
