package com.dcjet.cs.api.dec;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.service.DecICustomsTrackService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.dec.DecICustomsTrackDto;
import com.dcjet.cs.dto.dec.DecICustomsTrackMParam;
import com.dcjet.cs.dto.dec.DecICustomsTrackNoticeDto;
import com.dcjet.cs.dto.dec.DecICustomsTrackParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@RestController
@RequestMapping("v1/decICustomsTrack")
@Api(tags = "进口管理-进口报关追踪接口")
public class DecICustomsTrackController extends BaseController {
    @Resource
    private DecICustomsTrackService decICustomsTrackService;

    @Resource
    private ExcelService excelService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExportService exportService;

    //region 进口报关追踪基础接口
    /**
     * @param decICustomsTrackParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("进口报关追踪分页查询接口")
    @PostMapping("list")
    public ResultObject<List<DecICustomsTrackDto>> getListPaged(@RequestBody DecICustomsTrackParam decICustomsTrackParam, PageParam pageParam, UserInfoToken userInfo) {

        ResultObject<List<DecICustomsTrackDto>> paged = decICustomsTrackService.getListPaged(decICustomsTrackParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param decICustomsTrackParam
     * @param userInfo
     * @return
     */
    @ApiOperation("进口报关追踪新增接口")
    @PostMapping()
    public ResultObject<DecICustomsTrackDto> insert(@Valid @RequestBody DecICustomsTrackParam decICustomsTrackParam, UserInfoToken userInfo) {
        ResultObject<DecICustomsTrackDto> resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功"));
        DecICustomsTrackDto decICustomsTrackDto = decICustomsTrackService.insert(decICustomsTrackParam, userInfo);
        if (decICustomsTrackDto != null) {
            resultObject.setData(decICustomsTrackDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param decICustomsTrackParam
     * @param userInfo
     * @return
     */
    @ApiOperation("进口报关追踪修改接口")
    @PutMapping("{sid}")
    public ResultObject<DecICustomsTrackDto> update(@PathVariable String sid, @Valid @RequestBody DecICustomsTrackParam decICustomsTrackParam, UserInfoToken userInfo) {
        decICustomsTrackParam.setSid(sid);
        DecICustomsTrackDto decICustomsTrackDto = decICustomsTrackService.update(decICustomsTrackParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("修改成功"));
        if (decICustomsTrackDto != null) {
            resultObject.setData(decICustomsTrackDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("进口报关追踪删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        decICustomsTrackService.delete(sids,userInfo);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("进口报关追踪Excel数据导出接口")
    @PostMapping("export/{status}")
    public ResponseEntity export(@PathVariable String status,@Valid @RequestBody BasicExportParam<DecICustomsTrackParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<DecICustomsTrackDto> decICustomsTrackDtos = decICustomsTrackService.selectAll(exportParam.getExportColumns(), userInfo);
        String sheetName = xdoi18n.XdoI18nUtil.t("进口报关追踪批量修改");
        if (DecVariable.STATUS_0.equals(status)) {
            decICustomsTrackDtos = convertForPrint(decICustomsTrackDtos);
            return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), decICustomsTrackDtos);
        }else {
            return excelService.getExcelDecTrack(sheetName, URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), decICustomsTrackDtos);
        }
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<DecICustomsTrackDto> convertForPrint(List<DecICustomsTrackDto> list) {
        for (DecICustomsTrackDto item : list) {
            item.setTradeMode(commonService.convertPCode(item.getTradeMode(),PCodeType.TRADE));
            item.setTrafMode(commonService.convertPCode(item.getTrafMode(),PCodeType.TRANSF));
            item.setDeclarePort(commonService.convertPCode(item.getDeclarePort(),PCodeType.CUSTOMS_REL));

            if (StringUtils.isNotBlank(item.getComplete())) {
                if (DecVariable.STATUS_1.equals(item.getComplete())) {
                    item.setComplete("1|进行中");
                }
                if (DecVariable.STATUS_2.equals(item.getComplete())) {
                    item.setComplete("2|延迟");
                }
                if (DecVariable.STATUS_3.equals(item.getComplete())) {
                    item.setComplete("3|完成");
                }
            }

            if (StringUtils.isNotBlank(item.getDutyType())) {
                if (ConstantsStatus.STATUS_0.equals(item.getDutyType())) {
                    item.setDutyType("0 单票缴税");
                }
                if (ConstantsStatus.STATUS_1.equals(item.getDutyType())) {
                    item.setDutyType("1 汇总征税");
                }
                if (ConstantsStatus.STATUS_2.equals(item.getDutyType())) {
                    item.setDutyType("2 免税");
                }
            }
            if (StringUtils.isNotBlank(item.getPurchaseConfirm())) {
                if (ConstantsStatus.STATUS_0.equals(item.getPurchaseConfirm())) {
                    item.setPurchaseConfirm("0 否");
                }
                if (ConstantsStatus.STATUS_1.equals(item.getPurchaseConfirm())) {
                    item.setPurchaseConfirm("1 是");
                }
            }
            if (StringUtils.isNotBlank(item.getFinanceConfirm())) {
                if (ConstantsStatus.STATUS_0.equals(item.getFinanceConfirm())) {
                    item.setFinanceConfirm("0 否");
                }
                if (ConstantsStatus.STATUS_1.equals(item.getFinanceConfirm())) {
                    item.setFinanceConfirm("1 是");
                }
            }
            if (StringUtils.isNotBlank(item.getGoodsStatus())) {
                item.setGoodsStatus(item.getGoodsStatus() + " " + item.getGoodsStatusName());
            }
        }
        return list;
    }
    //endregion

    /**
     * 功能描述: 根据sid查询数据
     *
     * <AUTHOR>
     * @version :   1.0
     * @date 2019-02-19
     */
    @ApiOperation("进口报关追踪根据sid查询数据")
    @PostMapping("selectBySId/{sid}")
    public ResultObject<DecICustomsTrackDto> selectByHeadId(@PathVariable String sid, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, "",
                decICustomsTrackService.selectBySId(sid, userInfo));
        return result;
    }

    /**
     * 批量更新出口报关追踪
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("批量更新出口报关追踪")
    @PostMapping("updateM")
    public ResultObject updateM(@RequestBody DecICustomsTrackMParam param, UserInfoToken userInfo) {
        ResultObject result = decICustomsTrackService.updateM(param, userInfo);
        return result;
    }

    /**
     * 获取下拉同
     * @param userInfo
     * @return
     */
    @ApiOperation("获取下拉通知人")
    @PostMapping("getAlertOthers")
    public ResultObject getAlertOthers(UserInfoToken userInfo) {
        return decICustomsTrackService.getAlertOthers(userInfo);

    }
    /**
     * 功能描述 到货通知导出
     * <AUTHOR>
     * @date 2020/3/6
     * @version 1.0
     * @param sids 1
     * @param userInfo 2
     * @return org.springframework.http.ResponseEntity
    */
    @ApiOperation("出口提单Excel数据导出接口")
    @PostMapping("arrivalNotice/{sids}")
    public ResponseEntity arrivalNotice(@PathVariable List<String> sids, UserInfoToken userInfo) throws Exception{

        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String newDate = formatter.format(new Date());
        String templateName = "arrival_notice.xlsx";
        String outName = xdoi18n.XdoI18nUtil.t("到货通知") + newDate + ".xlsx";
        String fileName = java.util.UUID.randomUUID().toString() + ".xlsx";

        List<DecICustomsTrackNoticeDto> decICustomsTrackDtos =decICustomsTrackService.ArrivalNotice(sids, userInfo);
        decICustomsTrackDtos = convertForPrintNotice(decICustomsTrackDtos);
        String exportFileName = "";
        if (decICustomsTrackDtos.size() > 0) {
            exportFileName = exportService.export( decICustomsTrackDtos, fileName, templateName);
        }

        HttpHeaders h = new HttpHeaders();
        //解决空格变成+号问题
        outName = URLEncoder.encode(outName, CommonVariable.UTF8);
        outName = outName.replaceAll("\\+","%20");
        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(exportFileName)), h, HttpStatus.OK);
    }

    /**
     * 功能描述
     * <AUTHOR>
     * @date 2020/3/6
     * @version 1.0
     * @param list 1
     * @return java.util.List<com.dcjet.cs.dto.dec.DecICustomsTrackNoticeDto>
    */
    public List<DecICustomsTrackNoticeDto> convertForPrintNotice(List<DecICustomsTrackNoticeDto> list) {
        for (DecICustomsTrackNoticeDto item : list) {
            item.setTradeMode(commonService.convertPCode(item.getTradeMode(),PCodeType.TRADE));
            item.setTrafMode(commonService.convertPCode(item.getTrafMode(),PCodeType.TRANSF));
            item.setWrapType(commonService.convertPCode(item.getWrapType(),PCodeType.WRAP));
            if(StringUtils.isNotBlank(item.getBondMark())) {
                item.setBondMark(CommonEnum.BondMarkEnum.getValue(item.getBondMark()));
            }
        }
        return list;
    }

    /**
     * 报关追踪完成进度
     * @param sids
     * @param status
     * @return
     */
    @ApiOperation("报关达成接口")
    @PostMapping("reach/{sids}/{status}")
    public ResultObject reach(@PathVariable List<String> sids,@PathVariable String status){
        return decICustomsTrackService.reach(sids,status);
    }


    /**
     * 采购确认/取消 0:确认 1:取消
     * @param sids
     * @param type 3:采购 4:财务
     * @return
     */
    @ApiOperation("采购确认/取消校验")
    @PostMapping("purchaseCheck/{sids}/{type}")
    public ResultObject purchaseCheck(@PathVariable List<String> sids,@PathVariable String type) {
        ResultObject result = ResultObject.createInstance(true, "");
        result = decICustomsTrackService.purchaseCheck(sids,type);
        return result;
    }

    /**
     * 采购确认/取消 0:确认 1:取消
     * @param sids
     * @return
     */
    @ApiOperation("采购确认/取消")
    @PostMapping("purchase/{sids}")
    public ResultObject purchase(@PathVariable List<String> sids,@RequestBody DecICustomsTrackParam decICustomsTrackParam) {
        ResultObject result = ResultObject.createInstance(true, "");
        decICustomsTrackParam.setType(ConstantsStatus.STATUS_3);
        result = decICustomsTrackService.purchase(sids, decICustomsTrackParam);
        return result;
    }


    /**
     * 采购确认/取消 0:确认 1:取消
     * @param sids
     * @return
     */
    @ApiOperation("财务确认/取消校验")
    @PostMapping("financeeCheck/{sids}")
    public ResultObject financeeCheck(@PathVariable List<String> sids) {
        ResultObject result = ResultObject.createInstance(true, "");
//        result = decICustomsTrackService.financeeCheck(sids);
        return result;
    }

    /**
     * 采购确认/取消 0:确认 1:取消
     * @param sids
     * @param decICustomsTrackParam
     * @return
     */
    @ApiOperation("财务确认/取消")
    @PostMapping("financee/{sids}")
    public ResultObject financee(@PathVariable List<String> sids,@RequestBody DecICustomsTrackParam decICustomsTrackParam) {
        ResultObject result = ResultObject.createInstance(true, "");
        decICustomsTrackParam.setType(ConstantsStatus.STATUS_4);
        result = decICustomsTrackService.purchase(sids,decICustomsTrackParam);
        return result;
    }


    /**
     * 到货通知发送邮件
     * @param sids
     * @param
     * @return
     */
    @ApiOperation("到货通知发送邮件")
    @PostMapping("sendEmail/{sids}")
    public ResultObject sendEmail(@PathVariable List<String> sids, UserInfoToken userInfo) throws Exception {
        decICustomsTrackService.sendEmail(sids,userInfo);
        return  ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("已发送"));
    }


}
