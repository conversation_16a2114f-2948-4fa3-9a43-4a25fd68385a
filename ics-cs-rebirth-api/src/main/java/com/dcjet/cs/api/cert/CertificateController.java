package com.dcjet.cs.api.cert;

import com.dcjet.cs.api.base.ImportBasicController;
import com.dcjet.cs.cert.service.CertificateService;
import com.dcjet.cs.dto.cert.CertificateDto;
import com.dcjet.cs.dto.cert.CertificateParam;
import com.dcjet.cs.dto.cert.CertificateSearchParam;
import com.dcjet.cs.util.ConstantsStatus;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 证件台账
 * 
 * generated by Generate dc
 * <AUTHOR>
 * @date: 2020-04-28
 */
@Api(tags = "证件管理-证件台账")
@RestController
@RequestMapping("v1/cert/book")
public class CertificateController
        extends ImportBasicController<CertificateService, CertificateDto, CertificateParam, CertificateParam, CertificateSearchParam> {


    @ApiOperation("置为有效、无效")
    @PostMapping("/enableCert/{certIds}/{status}")
    public ResultObject enableCert(@PathVariable("certIds")List<String> certIds, @PathVariable("status")String status, UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance();
        Boolean res = service.enableCert(certIds, status, token);
        resultObject.setMessage(res ? (ConstantsStatus.STATUS_0.equals(status)? xdoi18n.XdoI18nUtil.t("已停用") : xdoi18n.XdoI18nUtil.t("已启用") ) : xdoi18n.XdoI18nUtil.t("数据不存在"));
        return resultObject;
    }
}