package com.dcjet.cs.api.mat;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.aeo.AeoAuditInfoParam;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.mat.EmsHeadDto;
import com.dcjet.cs.dto.mat.EmsHeadParam;
import com.dcjet.cs.mat.service.EmsHeadService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-5-15
 */
@RestController
@RequestMapping("v1/emsHead")
@Api(tags = "单损耗表头接口")
public class EmsHeadController {
    @Resource
    private EmsHeadService emsHeadService;
    @Resource
    private ExcelService excelService;

    /**
     * @param emsHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("单损耗分页查询接口(内审列表查询共用)")
    @PostMapping("list")
    public ResultObject<List<EmsHeadDto>> getListPaged(@RequestBody EmsHeadParam emsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        emsHeadParam.setTradeCode(userInfo.getCompany());
        emsHeadParam.setUserNo(userInfo.getUserNo());
        ResultObject<List<EmsHeadDto>> paged = emsHeadService.getListPaged(emsHeadParam, pageParam);
        return paged;
    }

    /**
     * @param emsHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("单损耗内审记录分页查询接口")
    @PostMapping("auditRecordList")
    public ResultObject<List<EmsHeadDto>> auditRecordList(@RequestBody EmsHeadParam emsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        emsHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<EmsHeadDto>> paged = emsHeadService.selectListForConsumeAudit(emsHeadParam, pageParam);
        return paged;
    }

    /**
     * @param emsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<EmsHeadDto> insert(@Valid @RequestBody EmsHeadParam emsHeadParam, UserInfoToken userInfo) {
        emsHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<EmsHeadDto> resultObject = emsHeadService.insert(emsHeadParam, userInfo);
        return resultObject;
    }

    /**
     * @param sid
     * @param emsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<EmsHeadDto> update(@PathVariable String sid, @Valid @RequestBody EmsHeadParam emsHeadParam, UserInfoToken userInfo) {
        emsHeadParam.setSid(sid);
        ResultObject<EmsHeadDto> resultObject = emsHeadService.update(emsHeadParam, userInfo);
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        return emsHeadService.delete(sids, userInfo);
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("内审列表Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<EmsHeadParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<EmsHeadDto> emsHeadDtos = emsHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        emsHeadDtos = convertForPrint(emsHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), emsHeadDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<EmsHeadDto> convertForPrint(List<EmsHeadDto> list) {
        for (EmsHeadDto item : list) {
            if (item.getApprStatus() != null) {
                item.setApprStatus(CommonEnum.APPR_STATUS_MAP.getValue(item.getApprStatus()));
            }
            if (item.getBillFlag() != null) {
                if (Arrays.asList("11", "12").contains(item.getBillFlag())) {
                    item.setBillFlag(item.getBillFlag() + "手册");
                }
                if (Arrays.asList("21", "22").contains(item.getBillFlag())) {
                    item.setBillFlag(item.getBillFlag() + "账册");
                }
            }
        }
        return list;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("内审记录Excel数据导出接口")
    @PostMapping(value = "/exportRecord")
    public ResponseEntity exportRecord(@Valid @RequestBody BasicExportParam<EmsHeadParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<EmsHeadDto> emsHeadDtos = emsHeadService.selectRecordAll(exportParam.getExportColumns(), userInfo);
        emsHeadDtos = convertRecordForPrint(emsHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), emsHeadDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<EmsHeadDto> convertRecordForPrint(List<EmsHeadDto> list) {
        for (EmsHeadDto item : list) {
            if (item.getApprStatus() != null) {
                if (CommonVariable.APPR_STATUS_B.equals(item.getApprStatus())) {
                    item.setApprStatus("-1|内审退回");
                } else if (CommonVariable.APPR_STATUS_2.equals(item.getApprStatus())) {
                    item.setApprStatus("2|初审通过");
                } else if (CommonVariable.APPR_STATUS_8.equals(item.getApprStatus())) {
                    item.setApprStatus("8|终审通过");
                } else if (CommonVariable.APPR_STATUS_3.equals(item.getApprStatus())) {
                    item.setApprStatus("3|变更");
                } else {
                    item.setApprStatus("0|制单");
                }
            }
            item.setBillFlag(item.getBillFlag() + " " + item.getBillFlagName());
        }
        return list;
    }

    /**
     * @param sids
     * @param userInfo
     * @return
     */
    @ApiOperation("发送内审接口")
    @PostMapping("sendDataByList/{sids}")
    public ResultObject sendDataByList(@PathVariable List<String> sids, UserInfoToken userInfo) {
        return emsHeadService.sendDataByList(sids, userInfo);
    }

    /**
     * 内审通过接口(单个&&批量)
     *
     * @param aeoAuditInfoParam
     * @param userInfo
     * @return
     */
    @ApiOperation("内审通过接口(单个&&批量)")
    @PostMapping("auditDataM")
    public ResultObject auditDataM(@RequestBody AeoAuditInfoParam aeoAuditInfoParam, UserInfoToken userInfo) {
        return emsHeadService.auditDataM(aeoAuditInfoParam, userInfo);
    }

    /**
     * 内审通过接口(全部)
     *
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("内审通过接口(全部)")
    @PostMapping("auditDataByList")
    public ResultObject auditDataByList(@RequestBody EmsHeadParam param, UserInfoToken userInfo) {
        return emsHeadService.auditDataByList(param, userInfo);
    }

    /**
     * 内审退回接口(单个&&批量)
     *
     * @param aeoAuditInfoParam
     * @param userInfo
     * @return
     */
    @ApiOperation("内审退回接口(单个&&批量)")
    @PostMapping("returnDataM")
    public ResultObject returnDataM(@RequestBody AeoAuditInfoParam aeoAuditInfoParam, UserInfoToken userInfo) {
        return emsHeadService.returnDataM(aeoAuditInfoParam, userInfo);
    }

    /**
     * 内审退回接口(全部)
     *
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("内审退回接口(全部)")
    @PostMapping("returnDataByList")
    public ResultObject returnDataByList(@RequestBody EmsHeadParam param, UserInfoToken userInfo) {
        return emsHeadService.returnDataByList(param, userInfo);
    }

    /**
     * @param sids
     * @param userInfo
     * @return
     */
    @ApiOperation("发送备案接口")
    @PostMapping("sendRecord/{sids}")
    public ResultObject sendRecord(@PathVariable List<String> sids, UserInfoToken userInfo) {
        return emsHeadService.sendRecord(sids, userInfo);
    }

    /**
     * @param sids
     * @param userInfo
     * @return com.xdo.domain.ResultObject
     * @description 单损耗备案通过
     * <AUTHOR>
     * @date 2020/7/1 16:20
     */
    @ApiOperation("单损耗-备案通过")
    @PostMapping("recordPass/{sids}")
    public ResultObject recordPass(@PathVariable List<String> sids, UserInfoToken userInfo) {
        return emsHeadService.recordPass(sids, userInfo);
    }

    @ApiOperation("单损耗-备案通过")
    @PostMapping("recordPassList")
    public ResultObject recordPassList(@RequestBody EmsHeadParam param, UserInfoToken userInfo) {
        return emsHeadService.recordPassList(param, userInfo);
    }
}
