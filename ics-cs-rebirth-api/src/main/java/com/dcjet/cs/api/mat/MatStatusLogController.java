package com.dcjet.cs.api.mat;

import com.dcjet.cs.dto.mat.MatStatusLogDto;
import com.dcjet.cs.dto.mat.MatStatusLogParam;
import com.dcjet.cs.mat.service.MatStatusLogService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-5-20
 */
@RestController
@RequestMapping("v1/matStatusLog")
@Api(tags = "启用/停用日志接口")
public class MatStatusLogController {
    @Resource
    private MatStatusLogService matStatusLogService;

    /**
     * @param matStatusLogParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<MatStatusLogDto>> getListPaged(@RequestBody MatStatusLogParam matStatusLogParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<MatStatusLogDto>> paged = matStatusLogService.getListPaged(matStatusLogParam, pageParam, userInfo);
        return paged;
    }
}
