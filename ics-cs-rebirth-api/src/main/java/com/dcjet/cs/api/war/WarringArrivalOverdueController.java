package com.dcjet.cs.api.war;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.war.WarringArrivalOverdueDto;
import com.dcjet.cs.dto.war.WarringArrivalOverdueParam;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.war.service.WarringArrivalOverdueService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/warringArrivalOverdue")
@Api(tags = "预警管理-进口未到货预警")
public class WarringArrivalOverdueController {

    @Resource
    private WarringArrivalOverdueService warringArrivalOverdueService;
    @Resource
    private ExcelService excelService;

    /**
     * @param warringArrivalOverdueParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("档案管理预警分页查询接口")
    @PostMapping("list")
    public ResultObject<List<WarringArrivalOverdueDto>> getListPaged(@RequestBody WarringArrivalOverdueParam warringArrivalOverdueParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<WarringArrivalOverdueDto>> paged = warringArrivalOverdueService.getListPaged(warringArrivalOverdueParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("档案管理预警导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<WarringArrivalOverdueParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<WarringArrivalOverdueDto> warringArchivesDtos = warringArrivalOverdueService.selectAll(exportParam.getExportColumns(), userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), warringArchivesDtos);
    }

    /**
     * 触发进口制单超期预警分析
     */
    @ApiOperation("触发进口制单超期预警分析")
    @PostMapping("checkData")
    public ResultObject checkData(UserInfoToken userInfo) {
        warringArrivalOverdueService.checkData(userInfo.getCompany());
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("分析成功！"));
    }
}
