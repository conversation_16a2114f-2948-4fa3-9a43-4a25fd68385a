package com.dcjet.cs.api.mat;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.mat.MatIeInfoDto;
import com.dcjet.cs.dto.mat.MatIeInfoParam;
import com.dcjet.cs.dto.mat.MatImgexgOrgDto;
import com.dcjet.cs.dto.mat.MatImgexgOrgParam;
import com.dcjet.cs.dto.notice.NoticeTaskEventParam;
import com.dcjet.cs.mat.model.MatImgexgOrgPass;
import com.dcjet.cs.mat.service.MatImgexgOrgService;
import com.dcjet.cs.notice.service.event.NoticeTaskEvent;
import com.dcjet.cs.tariff.component.PCodeTariffComponent;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.MatVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@RestController
@RequestMapping("v1/matImgexgOrg")
@Api(tags = "物料管理-备案物料接口")
public class MatImgexgOrgController extends BaseController {
    @Resource
    private MatImgexgOrgService matImgexgOrgService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    @Resource
    PCodeTariffComponent pCodeTariffComponent;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ApplicationContext applicationContext;
    //region 备案物料基础接口

    /**
     * @param matImgexgOrgParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("物料中心-备案分页查询接口")
    @PostMapping("list")
    public ResultObject<List<MatImgexgOrgDto>> getListPaged(@RequestBody MatImgexgOrgParam matImgexgOrgParam, PageParam pageParam, UserInfoToken userInfo) {
        String copGNo = matImgexgOrgParam.getCopGNo();
        if (StringUtils.isNotBlank(copGNo)) {
            if (copGNo.contains(",")) {
                List<String> copGNoList = new ArrayList<>();
                List<String> tmpList = new ArrayList<>(Arrays.asList(copGNo.split(",")));
                if (CollectionUtils.isNotEmpty(tmpList)) {
                    tmpList.stream().forEach(cgNo -> {
                        if (StringUtils.isNotBlank(cgNo)) {
                            copGNoList.add(cgNo.trim());
                        }
                    });
                }
                if (CollectionUtils.isNotEmpty(copGNoList)) {
                    matImgexgOrgParam.setCopGNos(copGNoList);
                    matImgexgOrgParam.setCopGNo("");
                }
            }
        }
        ResultObject<List<MatImgexgOrgDto>> paged = matImgexgOrgService.getListPaged(matImgexgOrgParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param matImgexgOrgParam
     * @param userInfo
     * @return
     */
    @ApiOperation("物料中心-物料备案新增接口")
    @PostMapping()
    public ResultObject<MatImgexgOrgDto> insert(@Valid @RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) throws Exception {
        ResultObject<MatImgexgOrgDto> resultObject = matImgexgOrgService.insert(matImgexgOrgParam, userInfo);
        return resultObject;
    }

    /**
     * @param sid
     * @param matImgexgOrgParam
     * @param userInfo
     * @return
     */
    @ApiOperation("物料中心-物料备案修改接口")
    @PutMapping("{sid}")
    public ResultObject<MatImgexgOrgDto> update(@PathVariable String sid, @Valid @RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        matImgexgOrgParam.setSid(sid);
        ResultObject resultObject = matImgexgOrgService.update(matImgexgOrgParam, userInfo);
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("物料中心-物料备案删除接口")
    @PostMapping("delete")
    public ResultObject delete(@RequestBody List<String> sids, UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = matImgexgOrgService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("物料中心-物料备案Excel数据导出接口")
    @PostMapping("export/{status}")
    public ResponseEntity export(@PathVariable String status, @Valid @RequestBody BasicExportParam<MatImgexgOrgParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<MatImgexgOrgDto> matImgexgOrgDtos = matImgexgOrgService.selectAll(exportParam.getExportColumns(), userInfo);

        String sheetName = xdoi18n.XdoI18nUtil.t("备案成品");
        if (MatVariable.STATUS_0.equals(status)) {
            matImgexgOrgDtos = convertForPrint(matImgexgOrgDtos);
            return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), matImgexgOrgDtos);
        } else {
            if (CommonEnum.GMarkEnum.IMG.getCode().equals(matImgexgOrgDtos.get(0).getGMark())) {
                sheetName = xdoi18n.XdoI18nUtil.t("备案料件");
            }
            return excelService.getExcelHeadersMatOrg(sheetName, URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), matImgexgOrgDtos);
        }
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<MatImgexgOrgDto> convertForPrint(List<MatImgexgOrgDto> list) {
        for (MatImgexgOrgDto item : list) {
            item.setSendApiStatus(item.getSendApiStatus() + " " + CommonEnum.BillEnum.getValue(item.getSendApiStatus()));
            item.setCurr(commonService.convertPCode(item.getCurr(), PCodeType.CURR_OUTDATED));
            item.setUnit(commonService.convertPCode(item.getUnit(), PCodeType.UNIT));
            item.setUnit1(commonService.convertPCode(item.getUnit1(), PCodeType.UNIT));
            item.setUnit2(commonService.convertPCode(item.getUnit2(), PCodeType.UNIT));
            item.setCountry(commonService.convertPCode(item.getCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setDutyMode(commonService.convertPCode(item.getDutyMode(), PCodeType.LEVYMODE));
            item.setDataSource(item.getDataSource() + " " + CommonEnum.matOrgSourceEnum.getValue(item.getDataSource()));
            if (StringUtils.isNotBlank(item.getModifyMark())) {
                item.setModifyMark(CommonEnum.modifyMarkEnum.getValue(item.getModifyMark()));
            }
        }
        return list;
    }

    //endregion

    //region 进出口获取相关物料信息接口

    /**
     * @param matIeInfoParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("进出口-获取物料企业料号信息")
    @PostMapping("getFacGNolist")
    public ResultObject<List<String>> getFacGNolistPaged(@RequestBody MatIeInfoParam matIeInfoParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<String>> paged = matImgexgOrgService.getFacGNolistPaged(matIeInfoParam, pageParam, userInfo);
        return paged;
    }

    /**
     * 功能描述: 进出口获取物料数据
     *
     * <AUTHOR>
     * @version :   1.0
     * @date 2019-02-23
     * @param:
     * @return: DecErpIListDto
     */
    @ApiOperation("进出口-根据企业料号获取物料信息")
    @PostMapping("getIeInfo")
    public ResultObject<MatIeInfoDto> getIeInfo(@RequestBody MatIeInfoParam matIeInfoParam, UserInfoToken userInfo) {
        return matImgexgOrgService.getIeInfo(matIeInfoParam, userInfo, "F");
    }

    /**
     * 功能描述 进出口获取物料数据
     *
     * @param matIeInfoParam 1
     * @param userInfo       2
     * @return com.xdo.domain.ResultObject<com.dcjet.cs.dto.mat.MatIeInfoDto>
     * <AUTHOR>
     * @date 2020/2/17
     * @version 1.0
     */

    @ApiOperation("进出口-根据备案料号获取物料信息")
    @PostMapping("getIeInfoByCopGNo")
    public ResultObject<MatIeInfoDto> getIeInfoByCopGNo(@RequestBody MatIeInfoParam matIeInfoParam, UserInfoToken userInfo) {
        return matImgexgOrgService.getIeInfo(matIeInfoParam, userInfo, "C");
    }
    //endregion

    //region 备案物料页面相关接口

    /**
     * 功能描述:根据当前登录用户的企业信息获取成品最大序号
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date ： 2019-1-15
     * @param: map
     * @param: session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("根据当前登录用户的企业信息和备案号获取成品最大序号")
    @PostMapping("getExgMaxNo")
    public ResultObject getExgMaxNo(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, "");
        if (StringUtils.isBlank(matImgexgOrgParam.getCopEmsNo())) {
            result.setSuccess(false);
            result.setMessage(xdoi18n.XdoI18nUtil.t("企业备案内部编号不能为空"));
            return result;
        }
        result.setData(matImgexgOrgService.getMaxNo(matImgexgOrgParam.getCopEmsNo(), userInfo, CommonEnum.GMarkEnum.EXG.getCode()));

        return result;
    }

    /**
     * 功能描述:根据当前登录用户的企业信息获取料件最大序号
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date ： 2019-1-15
     * @param: map
     * @param: session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("根据当前登录用户的企业信息和备案号获取料件最大序号")
    @PostMapping("getImgMaxNo")
    public ResultObject getImgMaxNo(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, "");
        if (StringUtils.isBlank(matImgexgOrgParam.getCopEmsNo())) {
            result.setSuccess(false);
            result.setMessage(xdoi18n.XdoI18nUtil.t("企业备案内部编号不能为空"));
            return result;
        }
        result.setData(matImgexgOrgService.getMaxNo(matImgexgOrgParam.getCopEmsNo(), userInfo, CommonEnum.GMarkEnum.IMG.getCode()));

        return result;
    }

    /**
     * 功能描述:根据当前登录用户的企业信息获取料件最大序号
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date ： 2019-1-15
     * @param: map
     * @param: session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("根据当前登录用户的企业信息和备案号获取备案序号列表")
    @PostMapping("getImgSerialNoList")
    public ResultObject getImgSerialNoList(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {

        ResultObject result = ResultObject.createInstance(true, "");
        if (StringUtils.isBlank(matImgexgOrgParam.getCopEmsNo())) {
            result.setSuccess(false);
            result.setMessage(xdoi18n.XdoI18nUtil.t("企业备案内部编号不能为空"));
            return result;
        }
        result.setData(matImgexgOrgService.getSerialNoList(matImgexgOrgParam.getCopEmsNo(), userInfo, CommonEnum.GMarkEnum.IMG.getCode()));

        return result;
    }

    /**
     * 功能描述:根据当前登录用户的企业信息获取料件最大序号
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date ： 2019-1-15
     * @param: map
     * @param: session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("根据当前登录用户的企业信息和备案号获取备案序号列表")
    @PostMapping("getExgSerialNoList")
    public ResultObject getExgSerialNoList(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, "");
        if (StringUtils.isBlank(matImgexgOrgParam.getCopEmsNo())) {
            result.setSuccess(false);
            result.setMessage(xdoi18n.XdoI18nUtil.t("企业备案内部编号不能为空"));
            return result;
        }
        result.setData(matImgexgOrgService.getSerialNoList(matImgexgOrgParam.getCopEmsNo(), userInfo, CommonEnum.GMarkEnum.EXG.getCode()));

        return result;
    }

    /**
     * 功能描述:根据当前登录用户的企业信息和备案号获取备案信息列表
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date ： 2019-1-15
     * @param: map
     * @param: session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("根据当前登录用户的企业信息和备案号获取备案信息列表")
    @PostMapping("getOrgList")
    public ResultObject getOrgList(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        List<MatImgexgOrgDto> list = matImgexgOrgService.getOrgList(matImgexgOrgParam, userInfo);
        if (list.size() > 0) {
            result.setData(list);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("没有对应备案数据"));
        }
        return result;
    }

    /**
     * 查询当前企业的成品、料件备案通过的备案序号列表
     *
     * @param emsNo
     * @param GMark
     * @param userInfo
     * @return ResultObject
     * @throws Exception
     */
    @ApiOperation("查询当前企业的成品、料件备案通过的备案序号列表")
    @PostMapping("getSerialNoList/{emsNo}/{GMark}")
    public ResultObject getSerialNoList(@NotNull @PathVariable @ApiParam(name = "emsNo", value = "备案号") String emsNo, @NotNull @PathVariable @ApiParam(name = "GMark", value = "物料类型 I 料件  E 成品") String GMark, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        List<MatImgexgOrgPass> matImgexgOrgPassList = matImgexgOrgService.getPassSerialNoList(emsNo, userInfo, GMark);
        List<BigDecimal> list = new ArrayList<>();
        matImgexgOrgPassList.stream().sorted(Comparator.comparing(MatImgexgOrgPass::getSerialNo)).collect(Collectors.toList()).forEach(s -> {
            list.add(s.getSerialNo());
        });
        resultObject.setData(list);
        resultObject.setMessage(xdoi18n.XdoI18nUtil.t("查询成功"));
        return resultObject;
    }

    /**
     * 根据备案序号查询当前企业的成品、料件备案通过关联数据 外发加工使用
     *
     * @param emsNo
     * @param serialNo
     * @param GMark
     * @param userInfo
     * @return ResultObject
     * @throws Exception
     */
    @ApiOperation("根据备案序号查询当前企业的成品、料件备案通过关联数据")
    @PostMapping("getDataBySerialNo/{emsNo}/{serialNo}/{GMark}")
    public ResultObject getDataBySerialNo(@NotNull @PathVariable @ApiParam(name = "emsNo", value = "备案号") String emsNo, @NotNull @PathVariable @ApiParam(name = "serialNo", value = "备案序号") BigDecimal serialNo, @NotNull @PathVariable @ApiParam(name = "GMark", value = "物料类型 I 料件  E 成品") String GMark, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        MatImgexgOrgPass matImgexgOrgPass = matImgexgOrgService.getDataBySerialNo(emsNo, serialNo, GMark, userInfo.getCompany());
        resultObject.setData(matImgexgOrgPass);
        resultObject.setMessage(xdoi18n.XdoI18nUtil.t("查询成功"));
        return resultObject;
    }

    /**
     * 功能描述:获取备案号、备案序号列表
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date ： 2019-1-15
     * @param: map
     * @param: session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("获取备案号、备案序号列表")
    @PostMapping("getMatNonCopEmsNo/{emsNo}/{GMark}")
    public ResultObject getMatNonCopEmsNo(@PathVariable String emsNo, @PathVariable String GMark, UserInfoToken userInfo) {

        ResultObject result = ResultObject.createInstance(true, "");
        if (StringUtils.isBlank(emsNo)) {
            result.setSuccess(false);
            result.setMessage(xdoi18n.XdoI18nUtil.t("企业备案内部编号不能为空"));
            return result;
        }
        result.setData(matImgexgOrgService.getMatNonCopEmsNo(emsNo, GMark, userInfo));

        return result;
    }
    //endregion

    //region 备案物料内审相关接口

    /**
     * 功能描述: AEO审核获取审核列表功能
     *
     * <AUTHOR> 朱正东
     * @version :   1.0
     * @date：2018-11-20
     * @param: rpObj 系统封装查询分页参数
     * @param: session
     * @return: ResultObject
     */
    @PostMapping("listForMatAudit")
    public ResultObject<List<MatImgexgOrgDto>> getListForMatAuditPaged(@RequestBody MatImgexgOrgParam matImgexgParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<MatImgexgOrgDto>> paged = matImgexgOrgService.selectListForMatAudit(matImgexgParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param matImgexgOrgParam
     * @param userInfo
     * @return
     */
    @ApiOperation("物料备案根据查询条件发送内审接口")
    @PostMapping("sendDateByList")
    public ResultObject sendDateByList(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("发送成功"));
        //只有未发送、内审核通过数据 、非备案失败的数据 可发送
        String[] arrValidStatus = {"0", "-1", "JD"};
        List<MatImgexgOrgDto> matImgexgOrgDtoList = matImgexgOrgService.selectAll(matImgexgOrgParam, userInfo);
        List<MatImgexgOrgDto> canNotSendDatas = matImgexgOrgDtoList.stream()
                .filter(x -> !Arrays.asList(arrValidStatus).contains(x.getApprStatus()))
                .collect(Collectors.toList());
        if (canNotSendDatas.size() > 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有 ”暂存”、“内审退回”、“备案退回” 状态数据可发送内审!"));
        }

        matImgexgOrgService.updateAuditByList(matImgexgOrgParam, matImgexgOrgDtoList, "2", userInfo);
        String apprType = matImgexgOrgParam.getGMark();
        if ("I".equals(apprType)) {
            applicationContext.publishEvent(new NoticeTaskEvent(new NoticeTaskEventParam("ORG_I_AUDIT", userInfo.getCompany(), null)));
        } else if ("E".equals(apprType)) {
            applicationContext.publishEvent(new NoticeTaskEvent(new NoticeTaskEventParam("ORG_E_AUDIT", userInfo.getCompany(), null)));
        }
        return resultObject;
    }

    /**
     * @param matImgexgOrgParam
     * @param userInfo
     * @return
     */
    @ApiOperation("物料备案根据查询条件内审退回接口")
    @PostMapping("returnDataByList")
    public ResultObject returnDataByList(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("发送成功"));

        List<MatImgexgOrgDto> paged = matImgexgOrgService.selectAllForMatAudit(matImgexgOrgParam, userInfo);
        String[] arrValidStatus = {"2", "8"};
        List<MatImgexgOrgDto> matList = paged.stream()
                .filter(x -> !Arrays.asList(arrValidStatus).contains(x.getApprStatus()))
                .collect(Collectors.toList());
        if (matList.size() > 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有 “待审核”、“内审通过” 状态数据可内审退回!"));
        }
        matImgexgOrgService.updateAuditByList(matImgexgOrgParam, paged, "-1", userInfo);
        return result;
    }

    /**
     * @param matImgexgOrgParam
     * @param userInfo
     * @return
     */
    @ApiOperation("物料备案根据查询条件内审通过接口")
    @PostMapping("auditDateByList")
    public ResultObject auditDateByList(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("内审通过成功"));

        String[] arrValidStatus = {"2"};
        List<MatImgexgOrgDto> matImgexgOrgDtoList = matImgexgOrgService.selectAllForMatAudit(matImgexgOrgParam, userInfo);
        List<MatImgexgOrgDto> canNotSendDatas = matImgexgOrgDtoList.stream()
                .filter(x -> !Arrays.asList(arrValidStatus).contains(x.getApprStatus()))
                .collect(Collectors.toList());
        if (canNotSendDatas.size() > 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有 ”待审核” 状态数据可内审通过!"));
        }
        matImgexgOrgService.updateAuditByList(matImgexgOrgParam, matImgexgOrgDtoList, "8", userInfo);

        return resultObject;
    }
    //endregion

    //region 备案物料备案通过相关接口

    /**
     * 功能描述:物料信息备案
     *
     * <AUTHOR> 朱正东
     * @version :   1.0
     * @date ： 2019-2-19
     * @param: modelObj List<String>
     * @param: session session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("物料备案-备案通过")
    @PostMapping("recordPass")
    public ResultObject recordPass(@RequestBody List<String> modelObj, UserInfoToken userInfo) {
        return matImgexgOrgService.recordPass(modelObj, userInfo);
    }

    /**
     * 功能描述:物料信息备案(查询条件)
     *
     * <AUTHOR> 朱正东
     * @version :   1.0
     * @date ： 2019-2-19
     * @param: modelObj List<MatImgexg>
     * @param: session session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("物料备案-备案通过(查询条件)")
    @PostMapping("recordPassList")
    public ResultObject recordPassList(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        return matImgexgOrgService.recordPassList(matImgexgOrgParam, userInfo);
    }

    /**
     * 功能描述 列表页面按钮恢复功能
     *
     * @param sids     1
     * @param userInfo 2
     * @return com.xdo.domain.ResultObject
     * <AUTHOR>
     * @date 2020/2/18
     * @version 1.0
     */
    @ApiOperation("物料备案-按钮恢复功能")
    @PostMapping("rollback")
    public ResultObject rollback(@RequestBody List<String> sids, UserInfoToken userInfo) throws Exception {
        return matImgexgOrgService.rollback(sids, userInfo);
    }

    /**
     * 功能描述 企业物料提取备案物料功能
     *
     * @param matImgexgOrgParam 1
     * @param userInfo          2
     * @return com.xdo.domain.ResultObject
     * <AUTHOR>
     * @date 2020/2/18
     * @version 1.0
     */
    @ApiOperation("物料备案-企业物料提取备案物料功能")
    @PostMapping(value = "takePassedImgExgInfo")
    public ResultObject takePassedImgExgInfo(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        return matImgexgOrgService.takePassedImgExgInfo(userInfo.getCompany(), matImgexgOrgParam.getGMark(), matImgexgOrgParam.getCopEmsNo());
    }

    /**
     * 功能描述 备案物料提取账册信息
     *
     * @param emsNo    1
     * @param userInfo 2
     * @return com.xdo.domain.ResultObject
     * <AUTHOR>
     * @date 2020/2/18
     * @version 1.0
     */
    @ApiOperation("物料备案-备案物料提取账册信息")
    @PostMapping("pickUp/{emsNo}")
    public ResultObject pickUp(@PathVariable String emsNo, UserInfoToken userInfo) throws Exception {
        return matImgexgOrgService.pickUp(emsNo, userInfo);
    }
    //endregion

    //region LG定制代码

    /**
     * 功能描述 备案料件成品从ERP物料中提取数据接口
     *
     * @param gMark    1    物料类型 I 料件 E 成品 2 设备 3 其他
     * @param bondMark 0 保完税标识 0 保税 1 非保税
     * @param userInfo 3
     * @return com.xdo.domain.ResultObject
     * <AUTHOR> ADD
     * @date 2020/7/06
     * @version 1.0
     */
    @ApiOperation("备案料件成品从ERP物料中提取数据接口")
    @PostMapping("getExtraction/{gMark}/{bondMark}")
    public ResultObject getExtraction(@PathVariable String gMark, @PathVariable String bondMark, UserInfoToken userInfo) {
        // 检查是否存在表体数据
        return matImgexgOrgService.getExtraction(gMark, bondMark, userInfo);
    }
    //endregion

    @ApiOperation("根据手帐册号获取当前企业下的备案物料")
    @PostMapping("getIeInfoForEmsNo")
    public ResultObject<Map<String, List<MatIeInfoDto>>> getIeInfoForEmsNo(@RequestBody MatIeInfoParam matIeInfoParam, UserInfoToken userInfo) {
        return matImgexgOrgService.getIeInfoForEmsNo(matIeInfoParam, userInfo);
    }

    @ApiOperation("同步金二手账册号信息至关务系统")
    @PostMapping("getEmsNoAsyc")
    public ResultObject<Map<String, List<MatIeInfoDto>>> getEmsNoAsyc(UserInfoToken userInfo) {
        return matImgexgOrgService.getEmsNoAsyc(userInfo);
    }

    @ApiOperation("校验手册号是否存在")
    @PostMapping("checkCopEmsNo")
    public ResultObject checkCopEmsNo(@RequestBody MatImgexgOrgParam matImgexgOrgParam, UserInfoToken userInfo) {
        return matImgexgOrgService.checkCopEmsNo(matImgexgOrgParam.getCopEmsNo(), userInfo);
    }

    /**
     * 查询所有企业内部编号
     *
     * @param userInfo
     * @return
     */
    @ApiOperation("查询所有企业内部编号")
    @PostMapping("listcopEmsNo")
    public List<String> listcopEmsNo(UserInfoToken userInfo) {
        return matImgexgOrgService.listcopEmsNo(userInfo);
    }

    /**
     * 查询所有企业内部编号
     *
     * @param userInfo
     * @return
     */
    @ApiOperation("查询所有企业内部编号")
    @PostMapping("getAllFacGNo")
    public List<MatIeInfoDto> getAllFacGNo(@RequestBody MatIeInfoParam matIeInfoParam, UserInfoToken userInfo) {
        return matImgexgOrgService.getAllFacGNo(matIeInfoParam, userInfo);
    }
}
