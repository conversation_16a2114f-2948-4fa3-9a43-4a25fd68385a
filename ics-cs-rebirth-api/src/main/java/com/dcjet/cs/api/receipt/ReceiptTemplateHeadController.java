package com.dcjet.cs.api.receipt;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.receipt.ReceiptTemplateHeadDto;
import com.dcjet.cs.dto.receipt.ReceiptTemplateHeadParam;
import com.dcjet.cs.receipt.service.ReceiptTemplateHeadService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-5-21
 */
@RestController
@RequestMapping("v1/receiptTemplateHead")
@Api(tags = "发票管理-通用发票表头接口层")
public class ReceiptTemplateHeadController {
    @Resource
    private ReceiptTemplateHeadService receiptTemplateHeadService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param receiptTemplateHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<ReceiptTemplateHeadDto>> getListPaged(@RequestBody ReceiptTemplateHeadParam receiptTemplateHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // receiptTemplateHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<ReceiptTemplateHeadDto>> paged = receiptTemplateHeadService.getListPaged(receiptTemplateHeadParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param receiptTemplateHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<ReceiptTemplateHeadDto> insert(@Valid @RequestBody ReceiptTemplateHeadParam receiptTemplateHeadParam, UserInfoToken userInfo) {
        ResultObject<ReceiptTemplateHeadDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        ReceiptTemplateHeadDto receiptTemplateHeadDto = receiptTemplateHeadService.insert(receiptTemplateHeadParam, userInfo);
        if (resultObject != null) {
            resultObject.setData(receiptTemplateHeadDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param receiptTemplateHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<ReceiptTemplateHeadDto> update(@PathVariable String sid, @Valid @RequestBody ReceiptTemplateHeadParam receiptTemplateHeadParam, UserInfoToken userInfo) {
        receiptTemplateHeadParam.setSid(sid);
        ReceiptTemplateHeadDto receiptTemplateHeadDto = receiptTemplateHeadService.update(receiptTemplateHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (receiptTemplateHeadDto != null) {
            resultObject.setData(receiptTemplateHeadDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        receiptTemplateHeadService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<ReceiptTemplateHeadParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<ReceiptTemplateHeadDto> receiptTemplateHeadDtos = receiptTemplateHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        receiptTemplateHeadDtos = convertForPrint(receiptTemplateHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), receiptTemplateHeadDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<ReceiptTemplateHeadDto> convertForPrint(List<ReceiptTemplateHeadDto> list) {
        for (ReceiptTemplateHeadDto item : list) {
            if (item.getTrafMode() != null) {
                item.setTrafMode(item.getTrafMode() + "|" + pCodeHolder.getValue(PCodeType.TRANSF, item.getTrafMode()));
            }
        }
        return list;
    }
}
