package com.dcjet.cs.api.bi;

import com.dcjet.cs.bi.service.BiClientDecService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.BiClientDecDto;
import com.dcjet.cs.dto.bi.BiClientDecParam;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-8-17
 */
@RestController
@RequestMapping("v1/biClientDec")
@Api(tags = "基础管理-消费使用/生成销售单位接口")
public class BiClientDecController {
    @Resource
    private BiClientDecService biClientDecService;
    @Resource
    private ExcelService excelService;

    /**
     * @param biClientDecParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("单位分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BiClientDecDto>> getListPaged(@RequestBody BiClientDecParam biClientDecParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BiClientDecDto>> paged = biClientDecService.getListPaged(biClientDecParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param biClientDecParam
     * @param userInfo
     * @return
     */
    @ApiOperation("单位新增接口")
    @PostMapping()
    public ResultObject<BiClientDecDto> insert(@Valid @RequestBody BiClientDecParam biClientDecParam, UserInfoToken userInfo) {
        ResultObject<BiClientDecDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        BiClientDecDto biClientDecDto = biClientDecService.insert(biClientDecParam, userInfo);
        if (biClientDecDto != null) {
            resultObject.setData(biClientDecDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param biClientDecParam
     * @param userInfo
     * @return
     */
    @ApiOperation("单位修改接口")
    @PutMapping("{sid}")
    public ResultObject<BiClientDecDto> update(@PathVariable String sid, @Valid @RequestBody BiClientDecParam biClientDecParam, UserInfoToken userInfo) {
        biClientDecParam.setSid(sid);
        BiClientDecDto biClientDecDto = biClientDecService.update(biClientDecParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (biClientDecDto != null) {
            resultObject.setData(biClientDecDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("更新失败!"));
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("单位删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        biClientDecService.delete(sids,userInfo);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("单位Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<BiClientDecParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<BiClientDecDto> biClientDecDtos = biClientDecService.selectAll(exportParam.getExportColumns(), userInfo);
        biClientDecDtos = convertForPrint(biClientDecDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), biClientDecDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<BiClientDecDto> convertForPrint(List<BiClientDecDto> list) {
        for (BiClientDecDto item : list) {
        }
        return list;
    }

    /**
     * 查询下拉列表信息
     * @param userInfo
     * @return
     */
    @ApiOperation("查询下拉列表信息")
    @PostMapping(value = "/selectList")
    public ResultObject<BiClientDecDto> selectList(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("查询成功"),biClientDecService.selectList(userInfo));
        return resultObject;
    }
}
