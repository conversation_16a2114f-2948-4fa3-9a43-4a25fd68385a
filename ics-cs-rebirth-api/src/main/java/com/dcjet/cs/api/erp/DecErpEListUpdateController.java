package com.dcjet.cs.api.erp;

import com.dcjet.cs.api.base.BasicController;
import com.dcjet.cs.common.service.ExcelImportService;
import com.dcjet.cs.dto.base.ImportResult;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto.erp.DecErpEListNDto;
import com.dcjet.cs.dto.erp.DecErpEListNParam;
import com.dcjet.cs.dto.erp.DecErpEListNUpdateParam;
import com.dcjet.cs.erp.service.DecErpEListNUpdateService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Api(tags = "进口表体修改导入")
@RestController
@RequestMapping("v1/decErpEListUpdate/return")
public class DecErpEListUpdateController extends BasicController<DecErpEListNUpdateService, DecErpEListNDto, DecErpEListNParam, DecErpEListNUpdateParam> {

    @Resource
    private ExcelImportService<DecErpEListNUpdateParam> excelImportService;

    @Resource
    private DecErpEListNUpdateService decErpEListNUpdateService;

    @ApiModelProperty("汇总导入修改调整数量")
    @PostMapping("/import/summary/adjustmentqty")
    public ResultObject<ImportResult> importUpdate(MultipartFile file, ExcelImportParam param, UserInfoToken token) throws IOException, IllegalAccessException, InstantiationException {
        ImportResult result = excelImportService.importExcel(file.getInputStream(),decErpEListNUpdateService,param, DecErpEListNUpdateParam.class,token);
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(result);
        return resultObject;

    }


    @ApiModelProperty("导入正确的数据")
    @PostMapping("/import/summary/{key}")
    public ResultObject<Integer>imporCorrect(@PathVariable String key, UserInfoToken token){
        int count = excelImportService.importCorrectData(decErpEListNUpdateService,key,token, DecErpEListNUpdateParam.class);
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("修改成功"));
        resultObject.setData(count);
        return resultObject;

    }

    @ApiModelProperty("导出错误的数据")
    @PostMapping("/export/summary/{key}")
    public ResponseEntity exportError(@PathVariable String key) throws Exception {
        return excelImportService.exportErrorData(key);
    }


}
