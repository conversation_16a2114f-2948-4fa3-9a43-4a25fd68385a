package com.dcjet.cs.api.war;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.war.WarringBailDto;
import com.dcjet.cs.dto.war.WarringBailParam;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.war.service.WarringBailService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@RestController
@RequestMapping("v1/warringBail")
@Api(tags = "预警管理-保金保函预警接口")
public class WarringBailController {
    @Resource
    private WarringBailService warringBailService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param warringBailParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("保金保函分页查询接口")
    @PostMapping("list")
    public ResultObject<List<WarringBailDto>> getListPaged(@RequestBody WarringBailParam warringBailParam, PageParam pageParam, UserInfoToken userInfo) {

        ResultObject<List<WarringBailDto>> paged = warringBailService.getListPaged(warringBailParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param warringBailParam
     * @param userInfo
     * @return
     */
    @ApiOperation("保金保函新增接口")
    @PostMapping()
    public ResultObject<WarringBailDto> insert(@Valid @RequestBody WarringBailParam warringBailParam, UserInfoToken userInfo) {
        ResultObject<WarringBailDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        WarringBailDto warringBailDto = warringBailService.insert(warringBailParam, userInfo);
        if (warringBailDto != null) {
            resultObject.setData(warringBailDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param warringBailParam
     * @param userInfo
     * @return
     */
    @ApiOperation("保金保函修改接口")
    @PutMapping("{sid}")
    public ResultObject<WarringBailDto> update(@PathVariable String sid, @Valid @RequestBody WarringBailParam warringBailParam, UserInfoToken userInfo) {
        warringBailParam.setSid(sid);
        WarringBailDto warringBailDto = warringBailService.update(warringBailParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (warringBailDto != null) {
            resultObject.setData(warringBailDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("保金保函删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        warringBailService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("保金保函Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<WarringBailParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<WarringBailDto> warringBailDtos = warringBailService.selectAll(exportParam.getExportColumns(), userInfo);
        warringBailDtos = convertForPrint(warringBailDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), warringBailDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<WarringBailDto> convertForPrint(List<WarringBailDto> list) {
        for (WarringBailDto item : list) {
        }
        return list;
    }

    /**
     * 功能描述:根据当前登录用户的企业信息获取预警最大序号
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date ： 2019-3-6
     * @param: map
     * @param: session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("根据当前预警类型获取预警最大序号")
    @PostMapping("getMaxNo")
    public ResultObject getMaxNo(UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, "",
                warringBailService.getMaxNo(userInfo));
        return result;
    }

    /**
     * 功能描述: 注销预警
     *
     * <AUTHOR>
     * @version :   1.0
     * @date 2019-01-08
     * @param:
     * @return: WarringSetDto 预警发送设置表
     */
    @ApiOperation("注销预警")
    @PostMapping(value = "/setStatus")
    public ResultObject setStatus(@RequestBody List<String> modelObj, UserInfoToken userInfo) {
        ResultObject ro = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("注销成功"));
        warringBailService.setStatus(modelObj, userInfo);
        return ro;
    }
}
