package com.dcjet.cs.api.gwstd;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.gwstd.GwstdPageConfigDto;
import com.dcjet.cs.dto.gwstd.GwstdPageConfigParam;
import com.dcjet.cs.gwstd.service.GwstdPageConfigService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-9-15
 */
@RestController
@RequestMapping("v1/gwstdPageConfig")
@Api(tags = "首页配置-首页配置项接口")
public class GwstdPageConfigController {
    @Resource
    private GwstdPageConfigService gwstdPageConfigService;
    @Resource
    private ExcelService excelService;

    /**
     * @param gwstdPageConfigParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("首页配置项分页查询接口")
    @PostMapping("list")
    public ResultObject<List<GwstdPageConfigDto>> getListPaged(@RequestBody GwstdPageConfigParam gwstdPageConfigParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<GwstdPageConfigDto>> paged = gwstdPageConfigService.getListPaged(gwstdPageConfigParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param gwstdPageConfigParam
     * @param userInfo
     * @return
     */
    @ApiOperation("首页配置项新增接口")
    @PostMapping()
    public ResultObject<GwstdPageConfigDto> insert(@Valid @RequestBody GwstdPageConfigParam gwstdPageConfigParam, UserInfoToken userInfo) {
        ResultObject<GwstdPageConfigDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        GwstdPageConfigDto gwstdPageConfigDto = gwstdPageConfigService.insert(gwstdPageConfigParam, userInfo);
        if (gwstdPageConfigDto != null) {
            resultObject.setData(gwstdPageConfigDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param gwstdPageConfigParam
     * @param userInfo
     * @return
     */
    @ApiOperation("首页配置项修改接口")
    @PutMapping("{sid}")
    public ResultObject<GwstdPageConfigDto> update(@PathVariable String sid, @Valid @RequestBody GwstdPageConfigParam gwstdPageConfigParam, UserInfoToken userInfo) {
        gwstdPageConfigParam.setSid(sid);
        GwstdPageConfigDto gwstdPageConfigDto = gwstdPageConfigService.update(gwstdPageConfigParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (gwstdPageConfigDto != null) {
            resultObject.setData(gwstdPageConfigDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("更新失败!"));
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("首页配置项删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        gwstdPageConfigService.delete(sids);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("首页配置项Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<GwstdPageConfigParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<GwstdPageConfigDto> gwstdPageConfigDtos = gwstdPageConfigService.selectAll(exportParam.getExportColumns(), userInfo);
        gwstdPageConfigDtos = convertForPrint(gwstdPageConfigDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), gwstdPageConfigDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<GwstdPageConfigDto> convertForPrint(List<GwstdPageConfigDto> list) {
        for (GwstdPageConfigDto item : list) {
        }
        return list;
    }
}
