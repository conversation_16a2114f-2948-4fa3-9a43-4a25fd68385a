package com.dcjet.cs.api.mrp;

import com.dcjet.cs.api.base.ImportBasicController;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.dec.DecECustomsTrackDto;
import com.dcjet.cs.dto.dec.DecECustomsTrackParam;
import com.dcjet.cs.dto.erp.DecIBillHeadDto;
import com.dcjet.cs.dto.mrp.*;
import com.dcjet.cs.mrp.component.MrpCalcPriceComponent;
import com.dcjet.cs.mrp.model.MrpPrice;
import com.dcjet.cs.mrp.model.MrpReturnSummary;
import com.dcjet.cs.mrp.service.MrpPriceService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * 内销单价计算及审核
 * <p>
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-03-16
 */
@Api(tags = "内销-单价计算及审核")
@RestController
@RequestMapping("v1/mrp/price")
public class MrpPriceController
        extends ImportBasicController<MrpPriceService, MrpPriceDto, MrpPriceParam, MrpPriceParam, MrpPriceSearchParam> {

    @Resource
    private MrpCalcPriceComponent mrpCalcPriceComponent;
    @Resource
    private ExcelService excelService;

    @Override
    public ResultObject<MrpPriceDto> insert(@Valid @RequestBody MrpPriceParam param, UserInfoToken token) {
        throw new ArgumentException(xdoi18n.XdoI18nUtil.t("不能支持新增操作"));
    }

    @Override
    public ResultObject<MrpPriceDto> update(@PathVariable String sid, @Valid @RequestBody MrpPriceParam param, UserInfoToken token) {
        throw new ArgumentException(xdoi18n.XdoI18nUtil.t("不能支持修改操作"));
    }

    @Override
    public ResultObject<MrpPriceDto> delete(String sid, UserInfoToken token) {
        throw new ArgumentException(xdoi18n.XdoI18nUtil.t("不能支持删除操作"));
    }

    @Override
    public ResultObject<MrpPriceDto> batchDelete(List<String> ids, UserInfoToken token) {
        throw new ArgumentException(xdoi18n.XdoI18nUtil.t("不能支持删除操作"));
    }

    @ApiOperation(value = "开始计算单价", notes = "计算完成后，返回清单运费保费校验未通过的数据")
    @PostMapping(value = "/start")
    public ResultObject<List<DecIBillHeadDto>> start(@RequestBody @Valid MrpPriceStartParam param, UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(mrpCalcPriceComponent.calc(param, token));
        return resultObject;
    }

    @ApiOperation("对账数据拆分")
    @PostMapping(value = "/split")
    public ResultObject<Void> split(@RequestBody @Valid MrpPriceSplitParam param, UserInfoToken userInfoToken) {
        service.split(param, userInfoToken);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("拆分成功"));
    }

    @ApiOperation("还原拆分数据")
    @DeleteMapping(value = {"/split/{headId}", "/split/{headId}/{sid}"})
    public ResultObject<Void> restoreSplit(@PathVariable String headId, @PathVariable(required = false) String sid, UserInfoToken token) {
        service.restoreSplit(headId, sid, token);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("还原成功"));
    }

    @ApiOperation(value = "获取核销标志数据源")
    @GetMapping("/cav/{headId}")
    public ResultObject<List<MrpPriceCavDto>> priceCav(@PathVariable String headId, UserInfoToken token) {
        ResultObject<List<MrpPriceCavDto>> resultObject = ResultObject.createInstance();
        resultObject.setData(service.getCavMark(headId, token));
        return resultObject;
    }

    @ApiOperation(value = "获取单价")
    @PostMapping(value = "/cav/fetch")
    public ResultObject<Boolean> fetchPrice(@RequestBody @Valid MrpPriceFetchParam param, UserInfoToken token) {
        service.fetch(param, token);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("获取单价成功"));
    }

    @ApiOperation(value = "计算单机的范围")
    @GetMapping(value = "calc/range/{headId}/{copGNo}")
    public ResultObject<List<MrpPriceCalcRangeDto>> calcRange(@PathVariable String headId, @PathVariable String copGNo) {
        List<MrpPriceCalcRangeDto> dtoList = service.calcRange(headId, copGNo);
        return ResultObject.createInstance(dtoList, dtoList.size());
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping("exportPrice")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<MrpReturnSummary> exportParam, UserInfoToken userInfo) throws Exception {
        List<MrpPriceDto> mrpPriceDtos = service.selectAll(exportParam.getExportColumns(), userInfo);
        String sheetName = xdoi18n.XdoI18nUtil.t("单价计算批量修改");
        return excelService.getExcelPrice(sheetName, URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), mrpPriceDtos);
    }
}
