package com.dcjet.cs.api.base;

import com.dcjet.cs.base.service.FirstPageService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2019-7-26
 */
@RestController
@RequestMapping("v1/firstPage")
@Api(tags = "首页信息-首页信息接口")
public class FirstPageController extends BaseController {
    @Resource
    private FirstPageService firstPageService;
    @Resource
    private ExcelService excelService;
    /**
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("auditList")
    public ResultObject getAuditList(UserInfoToken userInfo) {
        ResultObject paged = firstPageService.getAuditList(userInfo);
        return paged;
    }

    @ApiOperation("分页查询接口")
    @PostMapping("warringList/{warringList}")
    public ResultObject getWarringList(@PathVariable String warringList, UserInfoToken userInfo) {
        ResultObject paged = firstPageService.getWarringList(warringList,userInfo);
        return paged;
    }

    @ApiOperation("分页查询接口")
    @PostMapping("ticketList")
    public ResultObject getTicketList(UserInfoToken userInfo) {
        ResultObject paged = firstPageService.getTicketList(userInfo);
        return paged;
    }
    @ApiOperation("分页查询接口")
    @PostMapping("decTotalList")
    public ResultObject getDecTotalList(UserInfoToken userInfo) {
        ResultObject paged = firstPageService.getDecTotalList(userInfo);
        return paged;
    }

    /**
     * @param businessType
     * @return
     * @throws Exception
     */
    @ApiOperation("查询报关单删改单/错误/查验记录接口(供首页使用)")
    @PostMapping("queryEntryErrorCheckCount")
    public ResultObject<Integer> queryEntryErrorCheckCount(String businessType,UserInfoToken userInfo) {
        return firstPageService.queryEntryErrorCheckCount(businessType,userInfo);
    }

    /**
     * @param userInfo
     * @return
     */
    @ApiOperation("物流追踪查询接口")
    @PostMapping("logistics/{IEmark}")
    public ResultObject getLogistics(@PathVariable String IEmark,UserInfoToken userInfo) {
        ResultObject paged = null;
        if (StringUtils.equals(CommonVariable.IE_MARK_I,IEmark)){
            paged = firstPageService.getLogisticsI(userInfo);
        }else {
            paged = firstPageService.getLogisticsE(userInfo);
        }
        return paged;
    }
    /**
     * @param userInfo
     * @return
     */
    @ApiOperation("待审核事项查询接口")
    @PostMapping("auditInfo")
    public ResultObject getAuditInfo(UserInfoToken userInfo) {
        ResultObject paged = firstPageService.getAuditInfo(userInfo);
        return paged;
    }
    /**
     * @param userInfo
     * @return
     */
    @ApiOperation("预警提醒查询接口")
    @PostMapping("warring")
    public ResultObject getWarring(UserInfoToken userInfo) {
        ResultObject paged = firstPageService.getWarring(userInfo);
        return paged;
    }
}
