package com.dcjet.cs.api.temporary;

import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.temporary.*;
import com.dcjet.cs.temporary.service.TemporaryEHeadService;
import com.dcjet.cs.temporary.service.TemporaryEIHeadService;
import com.dcjet.cs.temporary.service.TemporaryWarningService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 出境表头
 * <p>
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-04-16
 */
@Api(tags = "暂时进出境-出境表头")
@RestController
@RequestMapping("v1/temporary/ehead")
public class TemporaryEHeadController {

    @Resource
    private TemporaryEHeadService temporaryeheadService;

    @Resource
    private TemporaryEIHeadService temporaryEIHeadService;

    @Resource
    private TemporaryWarningService temporaryWarningService;

    @ApiOperation("拉取数据")
    @PostMapping("fetch")
    public ResultObject<List<TemporaryEHeadDto>> fetch(PageParam pageParam, UserInfoToken token) {
        temporaryeheadService.fetch(token);
        return list(new TemporaryEHeadSearchParam(), pageParam, token);
    }

    /**
     * 查询接口
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    @ApiOperation("列表")
    @PostMapping("list")
    public ResultObject<List<TemporaryEHeadDto>> list(@RequestBody TemporaryEHeadSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<TemporaryEHeadDto>> paged = temporaryeheadService.list(searchParam, pageParam, token);
        return paged;
    }

    /***
     * 获取详细信息
     *
     * @param sid
     * @param token
     * @return
     */
    @ApiModelProperty("详细信息")
    @GetMapping("/{sid}")
    public ResultObject<TemporaryEHeadDto> get(@PathVariable String sid, UserInfoToken token) {
        TemporaryEHeadDto dto = temporaryeheadService.get(sid, token);
        return ResultObject.createInstance(dto, 1);
    }

    /**
     * 新增
     *
     * @param param
     * @param token
     * @return
     */
    @ApiOperation("新增")
    @PostMapping()
    public ResultObject<TemporaryEHeadDto> insert(@Valid @RequestBody TemporaryEHeadParam param, UserInfoToken token) {
        ResultObject<TemporaryEHeadDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        TemporaryEHeadDto dto = temporaryeheadService.insert(param, token);
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * 修改
     *
     * @param sid
     * @param param
     * @param token
     * @return
     */
    @ApiOperation("修改")
    @PutMapping("{sid}")
    public ResultObject<TemporaryEHeadDto> update(@PathVariable String sid, @Valid @RequestBody TemporaryEHeadParam param, UserInfoToken token) {
        TemporaryEHeadDto dto = temporaryeheadService.update(sid, param, token);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * 删除
     *
     * @param sids
     * @return
     */
    @ApiOperation("删除")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        temporaryeheadService.delete(sids, token);
        return resultObject;
    }

    /***
     *
     * @param param
     * @param token
     * @return
     */
    @ApiOperation("复运")
    @PostMapping("I")
    public ResultObject generateEIHead(@RequestBody TemporaryEHeadGenerateIParam param, UserInfoToken token) {
        temporaryEIHeadService.generateEIHead(param, token);
        return ResultObject.createInstance(true, 1);
    }

    @PostMapping("warning")
    @ApiOperation(value = "获取预警数据")
    public ResultObject<List<TemporaryWarningDto>> warningList(@RequestBody TemporaryWarningSearchParam param, PageParam pageParam, UserInfoToken token) {
        return temporaryWarningService.warningList(CommonVariable.IE_MARK_E, param, pageParam, token);
    }

    @PostMapping("warning/export")
    @ApiOperation(value = "预警数据导出")
    public ResponseEntity warningExport(@RequestBody BasicExportParam<TemporaryWarningSearchParam> exportParam, UserInfoToken token) throws IOException {
        return temporaryWarningService.export(CommonVariable.IE_MARK_E, exportParam, token);
    }

    @PostMapping("warning/off")
    @ApiOperation(value = "预警注销")
    public ResultObject warningOn(@RequestBody List<String> idList, UserInfoToken token) {
        temporaryWarningService.warningOn(CommonVariable.IE_MARK_E, idList, token);
        return ResultObject.createInstance(true);
    }

    @PostMapping("warning/on")
    @ApiOperation(value = "预警恢复")
    public ResultObject warningOff(@RequestBody List<String> idList, UserInfoToken token) {
        temporaryWarningService.warningOff(CommonVariable.IE_MARK_E, idList, token);
        return ResultObject.createInstance(true);
    }

    @PostMapping("warning/setting")
    @ApiOperation(value = "设置预警天数")
    public ResultObject settingWarningDay(@Valid @RequestBody TemporaryWarningSettingParam param, UserInfoToken token) {
        temporaryWarningService.settingWarningDay(CommonVariable.IE_MARK_E, param, token);
        return ResultObject.createInstance(true);
    }
}
