package com.dcjet.cs.api.war;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.war.WarringWtDto;
import com.dcjet.cs.dto.war.WarringWtParam;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.war.service.WarringWtService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@RestController
@RequestMapping("v1/warringWt")
@Api(tags = "预警管理-净重预警接口")
public class WarringWtController {
    @Resource
    private WarringWtService warringWtService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param warringWtParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("净重预警分页查询接口")
    @PostMapping("list")
    public ResultObject<List<WarringWtDto>> getListPaged(@RequestBody WarringWtParam warringWtParam, PageParam pageParam, UserInfoToken userInfo) {

        ResultObject<List<WarringWtDto>> paged = warringWtService.getListPaged(warringWtParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param warringWtParam
     * @param userInfo
     * @return
     */
    @ApiOperation("净重预警新增接口")
    @PostMapping()
    public ResultObject<WarringWtDto> insert(@Valid @RequestBody WarringWtParam warringWtParam, UserInfoToken userInfo) {
        ResultObject<WarringWtDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        WarringWtDto warringWtDto = warringWtService.insert(warringWtParam, userInfo);
        if (warringWtDto != null) {
            resultObject.setData(warringWtDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param warringWtParam
     * @param userInfo
     * @return
     */
    @ApiOperation("净重预警修改接口")
    @PutMapping("{sid}")
    public ResultObject<WarringWtDto> update(@PathVariable String sid, @Valid @RequestBody WarringWtParam warringWtParam, UserInfoToken userInfo) {
        warringWtParam.setSid(sid);
        WarringWtDto warringWtDto = warringWtService.update(warringWtParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (warringWtDto != null) {
            resultObject.setData(warringWtDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("净重预警删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        warringWtService.delete(sids);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("净重预警Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<WarringWtParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<WarringWtDto> warringWtDtos = warringWtService.selectAll(exportParam.getExportColumns(), userInfo);
        warringWtDtos = convertForPrint(warringWtDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), warringWtDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<WarringWtDto> convertForPrint(List<WarringWtDto> list) {
        for (WarringWtDto item : list) {
        }
        return list;
    }

    /**
     * 功能描述:根据当前登录用户的企业信息获取预警最大序号
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date ： 2019-3-6
     * @param: map
     * @param: session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("根据当前预警类型获取预警最大序号")
    @PostMapping("getMaxNo")
    public ResultObject getMaxNo(UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, "",
                warringWtService.getMaxNo(userInfo));
        return result;
    }

    /**
     * 功能描述: 注销预警
     *
     * <AUTHOR>
     * @version :   1.0
     * @date 2019-01-08
     * @param:
     * @return: WarringSetDto 预警发送设置表
     */
    @ApiOperation("注销预警")
    @PostMapping("setStatus")
    public ResultObject setStatus(@RequestBody List<String> modelObj, UserInfoToken userInfo) {
        ResultObject ro = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("注销成功"));
        warringWtService.setStatus(modelObj, userInfo);
        return ro;
    }
}
