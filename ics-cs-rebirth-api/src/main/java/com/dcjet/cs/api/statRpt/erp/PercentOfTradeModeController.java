package com.dcjet.cs.api.statRpt.erp;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.statRpt.erp.dto.PercentOfTradeMode;
import com.dcjet.cs.dto.statRpt.erp.param.PercentOfTradeModeParam;
import com.dcjet.cs.statRpt.erp.service.PercentOfTradeModeService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/statRpt/erp/tradeModePercent")
@Api(tags = "统计报表-报关单统计-通关占比-监管方式")
public class PercentOfTradeModeController {

    @Resource
    private PercentOfTradeModeService service;

    @Resource
    private ExcelService excelService;

    @Resource
    private CommonService commonService;

    @ApiOperation("分页查询")
    @PostMapping("list")
    public ResultObject<List<PercentOfTradeMode>> getListPaged(@Valid @RequestBody PercentOfTradeModeParam param, PageParam pageParam, UserInfoToken token) {
        return service.getListPaged(param, pageParam, token);
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<PercentOfTradeModeParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<PercentOfTradeMode> percentOfTradeModes = service.getList(exportParam.getExportColumns(), userInfo);
        convertForPrint(percentOfTradeModes);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), percentOfTradeModes);
    }

    public void convertForPrint(List<PercentOfTradeMode> list) {
        list.stream().forEach(item -> {
            item.setTradeMode(commonService.convertPCode(item.getTradeMode(), PCodeType.TRADE));
        });
    }
}
