package com.dcjet.cs.api.invoice;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.invoice.DecInvoiceBoxTemplateIDto;
import com.dcjet.cs.dto.invoice.DecInvoiceBoxTemplateIParam;
import com.dcjet.cs.invoice.service.DecInvoiceBoxTemplateIService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-8-13
 */
@RestController
@RequestMapping("v1/decInvoiceBoxTemplateI")
@Api(tags = "发票管理-进口发票箱单模板接口")
public class DecInvoiceBoxTemplateIController extends BaseController {
    @Resource
    private DecInvoiceBoxTemplateIService decInvoiceBoxTemplateIService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param decInvoiceBoxTemplateIParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<DecInvoiceBoxTemplateIDto>> getListPaged(@RequestBody DecInvoiceBoxTemplateIParam decInvoiceBoxTemplateIParam, PageParam pageParam, UserInfoToken userInfo) {
        // decInvoiceBoxTemplateIParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<DecInvoiceBoxTemplateIDto>> paged = decInvoiceBoxTemplateIService.getListPaged(decInvoiceBoxTemplateIParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param decInvoiceBoxTemplateIParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<DecInvoiceBoxTemplateIDto> insert(@Valid @RequestBody DecInvoiceBoxTemplateIParam decInvoiceBoxTemplateIParam, UserInfoToken userInfo) {
        return decInvoiceBoxTemplateIService.insert(decInvoiceBoxTemplateIParam, userInfo);
    }

    /**
     * @param sid
     * @param decInvoiceBoxTemplateIParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<DecInvoiceBoxTemplateIDto> update(@PathVariable String sid, @Valid @RequestBody DecInvoiceBoxTemplateIParam decInvoiceBoxTemplateIParam, UserInfoToken userInfo) {
        decInvoiceBoxTemplateIParam.setSid(sid);
        DecInvoiceBoxTemplateIDto decInvoiceBoxTemplateIDto = decInvoiceBoxTemplateIService.update(decInvoiceBoxTemplateIParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (decInvoiceBoxTemplateIDto != null) {
            resultObject.setData(decInvoiceBoxTemplateIDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    @Deprecated
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        decInvoiceBoxTemplateIService.delete(sids);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<DecInvoiceBoxTemplateIParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<DecInvoiceBoxTemplateIDto> decInvoiceBoxTemplateIDtos = decInvoiceBoxTemplateIService.selectAll(exportParam.getExportColumns(), userInfo);
        decInvoiceBoxTemplateIDtos = convertForPrint(decInvoiceBoxTemplateIDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), decInvoiceBoxTemplateIDtos);
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<DecInvoiceBoxTemplateIDto> convertForPrint(List<DecInvoiceBoxTemplateIDto> list) {
        for (DecInvoiceBoxTemplateIDto item : list) {
        }
        return list;
    }
}
