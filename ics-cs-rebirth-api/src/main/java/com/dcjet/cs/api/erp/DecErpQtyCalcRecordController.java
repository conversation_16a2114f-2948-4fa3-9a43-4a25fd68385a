package com.dcjet.cs.api.erp;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.erp.DecErpQtyCalcRecordDto;
import com.dcjet.cs.dto.erp.DecErpQtyCalcRecordParam;
import com.dcjet.cs.erp.service.DecErpQtyCalcRecordService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 *
 *
 * <AUTHOR>
 * @date: 2019-4-24
 */
@RestController
@RequestMapping("v1/decErpQtyCalcRecord")
@Api(tags = "物料管理-物料余量接口")
public class DecErpQtyCalcRecordController extends BaseController {
    @Resource
    private DecErpQtyCalcRecordService decErpQtyCalcRecordService;

    @Resource
    private ExcelService excelService;
    /**
     * @param decErpQtyCalcRecordParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<DecErpQtyCalcRecordDto>> getListPaged(@RequestBody DecErpQtyCalcRecordParam decErpQtyCalcRecordParam, PageParam pageParam, UserInfoToken userInfo) {
        // decErpQtyCalcRecordParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<DecErpQtyCalcRecordDto>> paged = decErpQtyCalcRecordService.getListPaged(decErpQtyCalcRecordParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param decErpQtyCalcRecordParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<DecErpQtyCalcRecordDto> insert(@Valid @RequestBody DecErpQtyCalcRecordParam decErpQtyCalcRecordParam, UserInfoToken userInfo) {
        ResultObject<DecErpQtyCalcRecordDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        DecErpQtyCalcRecordDto decErpQtyCalcRecordDto = decErpQtyCalcRecordService.insert(decErpQtyCalcRecordParam, userInfo);
        if (decErpQtyCalcRecordDto != null) {
            resultObject.setData(decErpQtyCalcRecordDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param decErpQtyCalcRecordParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<DecErpQtyCalcRecordDto> update(@PathVariable String sid, @Valid @RequestBody DecErpQtyCalcRecordParam decErpQtyCalcRecordParam, UserInfoToken userInfo) {
        decErpQtyCalcRecordParam.setSid(sid);
        DecErpQtyCalcRecordDto decErpQtyCalcRecordDto = decErpQtyCalcRecordService.update(decErpQtyCalcRecordParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("修改成功"));
        if (decErpQtyCalcRecordDto != null) {
            resultObject.setData(decErpQtyCalcRecordDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
		 decErpQtyCalcRecordService.delete(sids);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<DecErpQtyCalcRecordParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<DecErpQtyCalcRecordDto> decErpQtyCalcRecordDtos = decErpQtyCalcRecordService.selectAll(exportParam.getExportColumns(), userInfo);
        decErpQtyCalcRecordDtos = convertForPrint(decErpQtyCalcRecordDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), decErpQtyCalcRecordDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<DecErpQtyCalcRecordDto> convertForPrint(List<DecErpQtyCalcRecordDto> list) {
        for(DecErpQtyCalcRecordDto item : list) {
        }
        return list;
    }
}
