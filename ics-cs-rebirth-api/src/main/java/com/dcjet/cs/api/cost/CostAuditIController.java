package com.dcjet.cs.api.cost;

import com.dcjet.cs.cost.service.CostAuditIService;
import com.dcjet.cs.dto.cost.CostCompareIHeadDto;
import com.dcjet.cs.dto.cost.CostCompareIHeadParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:进口费用审核
 * @author: WJ
 * @createDate: 2021/8/23 14:28
 */
@Api(tags = "费用管理-进口费用审核")
@RestController
@RequestMapping("v1/cost/auditI")
public class CostAuditIController {

    @Resource
    private CostAuditIService costAuditIService;

    /**
     * 进口费用审核表头查询
     * @param costCompareIHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("auditHead")
    public ResultObject<List<CostCompareIHeadDto>> getAuditPaged(@RequestBody CostCompareIHeadParam costCompareIHeadParam,
                                                                       PageParam pageParam, UserInfoToken userInfo) {
        return costAuditIService.getAuditPaged(costCompareIHeadParam, pageParam, userInfo);
    }

    /**
     * 审核通过 2、审核退回 3
     * @param paramList
     * @param status
     * @param userInfo
     * @return
     */
    @ApiOperation("审核")
    @PostMapping("audit/{status}")
    public ResultObject<List<CostCompareIHeadDto>> audit(@RequestBody List<CostCompareIHeadParam> paramList,
                                                         @PathVariable String status, UserInfoToken userInfo) {
        return costAuditIService.audit(paramList, status, userInfo);
    }

}
