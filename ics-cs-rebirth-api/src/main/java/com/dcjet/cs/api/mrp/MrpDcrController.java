package com.dcjet.cs.api.mrp;

import com.dcjet.cs.common.service.ExcelExportService;
import com.dcjet.cs.common.service.ExcelImportService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.base.ImportResult;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto.mrp.MrpDcrDto;
import com.dcjet.cs.dto.mrp.MrpDcrImportParam;
import com.dcjet.cs.dto.mrp.MrpDcrParam;
import com.dcjet.cs.dto.mrp.MrpDcrSearchParam;
import com.dcjet.cs.mrp.service.MrpDcrService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 内销报核初期
 * <p>
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-06-24
 */
@Api(tags = "内销报核初期")
@RestController
@RequestMapping("v1/mrp/dcr")
public class MrpDcrController {

    @Resource
    private MrpDcrService mrpDcrService;

    @Autowired
    protected ExcelExportService excelService;

    @Resource
    private ExcelImportService<MrpDcrImportParam> excelImportService;

    /**
     * 查询接口
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    @ApiOperation("列表")
    @PostMapping("list")
    public ResultObject<List<MrpDcrDto>> list(@RequestBody MrpDcrSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<MrpDcrDto>> paged = mrpDcrService.list(searchParam, pageParam, token);
        return paged;
    }

    /***
     * 匹配汇总列表
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    @ApiOperation("匹配汇总列表")
    @PostMapping("summary/list")
    public ResultObject<List<MrpDcrDto>> summaryList(@RequestBody MrpDcrSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<MrpDcrDto>> paged = mrpDcrService.summaryList(searchParam, pageParam, token);
        return paged;
    }

    /***
     * 匹配汇总列表导出
     *
     * @param exportParam
     * @param page
     * @param token
     * @return
     */
    @ApiOperation("匹配汇总列表导出")
    @PostMapping("summary/export")
    public ResponseEntity summaryExport(@RequestBody BasicExportParam<MrpDcrSearchParam> exportParam, PageParam page, UserInfoToken token) throws IOException {
        return excelService.exportExcelByPage(exportParam.getName(), exportParam.getHeader(), page, (pageParam) -> mrpDcrService.summaryList(exportParam.getExportColumns(), pageParam, token));
    }

    /***
     * 匹配明细列表
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    @ApiOperation("匹配明细列表")
    @PostMapping("detail/list")
    public ResultObject<List<MrpDcrDto>> detailList(@RequestBody MrpDcrSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        ResultObject<List<MrpDcrDto>> paged = mrpDcrService.detailList(searchParam, pageParam, token);
        return paged;
    }

    /***
     * 匹配明细列表导出
     *
     * @param exportParam
     * @param page
     * @param token
     * @return
     */
    @ApiOperation("匹配明细列表导出")
    @PostMapping("detail/export")
    public ResponseEntity detailExport(@RequestBody BasicExportParam<MrpDcrSearchParam> exportParam, PageParam page, UserInfoToken token) throws IOException {
        return excelService.exportExcelByPage(exportParam.getName(), exportParam.getHeader(), page, (pageParam) -> mrpDcrService.detailList(exportParam.getExportColumns(), pageParam, token));
    }

    @ApiOperation("统计之前提去的报核数据")
    @PostMapping("prerefresh")
    public ResultObject<Integer> preFefresh(@RequestBody MrpDcrParam param, UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance(true);
        resultObject.setData(mrpDcrService.countByDcrTimes(param, token));
        return resultObject;
    }

    @ApiOperation("刷新报核初期数据")
    @PostMapping("refresh")
    public ResultObject<Void> refresh(@RequestBody MrpDcrParam param, UserInfoToken token) {
        mrpDcrService.refresh(param, token);
        return ResultObject.createInstance(true);
    }

    @ApiModelProperty("汇总导入修改调整数量")
    @PostMapping("/import")
    public ResultObject<ImportResult> importExcel(MultipartFile file, ExcelImportParam param, UserInfoToken token) throws IOException, InstantiationException, IllegalAccessException {
        ImportResult result = excelImportService.importExcel(file.getInputStream(), mrpDcrService, param, MrpDcrImportParam.class, token);
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(result);
        return resultObject;
    }

    @ApiModelProperty("导入正确的数据")
    @PostMapping("/import/{key}")
    public ResultObject<Integer> importCorrect(@PathVariable String key, UserInfoToken token) {
        int count = excelImportService.importCorrectData(mrpDcrService, key, token, MrpDcrImportParam.class);
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(count);
        return resultObject;
    }

    @ApiModelProperty("导出错误的数据")
    @PostMapping("/export/{key}")
    public ResponseEntity exportError(@PathVariable String key) throws Exception {
        return excelImportService.exportErrorData(key);
    }

    @ApiModelProperty("导出导入的模版")
    @PostMapping("/export/tpl")
    public ResponseEntity exportTpl(@RequestParam(defaultValue = "导入模版") String name) throws Exception {
        return excelImportService.exportTpl(name, MrpDcrImportParam.class);
    }

    @ApiOperation("删除")
    @DeleteMapping("/{idList}")
    public ResultObject<Void> delete(@PathVariable List<String> idList, UserInfoToken token) throws Exception {
        mrpDcrService.deleteByIdList(idList, token);
        return ResultObject.createInstance();
    }
}