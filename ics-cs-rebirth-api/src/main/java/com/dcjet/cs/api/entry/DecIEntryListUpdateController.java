package com.dcjet.cs.api.entry;

import com.dcjet.cs.api.base.BasicController;
import com.dcjet.cs.common.service.ExcelImportService;
import com.dcjet.cs.dto.base.ImportResult;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto.entry.DecIEEntryListUpdateParam;
import com.dcjet.cs.dto.entry.DecIEntryListDto;
import com.dcjet.cs.dto.entry.DecIEntryListParam;
import com.dcjet.cs.entry.service.DecIEntryListUpdateService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-10-20
 */
@RestController
@RequestMapping("v1/decIEntryListUpdate")
@Api(tags = "报关单草单表体信息接口")
public class DecIEntryListUpdateController extends BasicController<DecIEntryListUpdateService, DecIEntryListDto, DecIEntryListParam, DecIEEntryListUpdateParam> {

    @Resource
    private DecIEntryListUpdateService decEBillListUpdateService;

    @Resource
    private ExcelImportService<DecIEEntryListUpdateParam> excelImportService;

    @ApiModelProperty("修改导入规格型号")
    @PostMapping("/import/gmodel")
    public ResultObject<ImportResult> importUpdate(MultipartFile file, ExcelImportParam param, UserInfoToken token) throws IOException, IllegalAccessException, InstantiationException {
        ImportResult result = excelImportService.importExcel(file.getInputStream(), decEBillListUpdateService, param, DecIEEntryListUpdateParam.class, token);
        ResultObject resultObject = ResultObject.createInstance();
        resultObject.setData(result);
        return resultObject;
    }

    @ApiModelProperty("导入正确的数据")
    @PostMapping("/import/{key}")
    public ResultObject<Integer> imporCorrect(@PathVariable String key, UserInfoToken token) {
        int count = excelImportService.importCorrectData(decEBillListUpdateService, key, token, DecIEEntryListUpdateParam.class);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        resultObject.setData(count);
        return resultObject;
    }

    @ApiModelProperty("导出错误的数据")
    @PostMapping("/export/{key}")
    public ResponseEntity exportError(@PathVariable String key) throws Exception {
        return excelImportService.exportErrorData(key);
    }
}
