package com.dcjet.cs.api.returned.bonded;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.erp.sap.ErpDecEListParam;
import com.dcjet.cs.dto.returned.GwReturnListDto;
import com.dcjet.cs.dto.returned.GwReturnListMatchDto;
import com.dcjet.cs.dto.returned.GwReturnListParam;
import com.dcjet.cs.returned.service.bonded.GwReturnListBondedService;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2021-2-5
 */
@RestController
@RequestMapping("v1/gwReturnListBonded")
@Api(tags = "进料成品退换-表体接口")
public class GwReturnListController extends BaseController {
    @Resource
    private GwReturnListBondedService gwReturnListService;
    @Resource
    private ExcelService excelService;
    @Resource
    private CommonService commonService;

    /**
     * @param gwReturnListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<GwReturnListDto>> getListPaged(@RequestBody GwReturnListParam gwReturnListParam, PageParam pageParam, UserInfoToken userInfo) {
        gwReturnListParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<GwReturnListDto>> paged = gwReturnListService.getListPaged(gwReturnListParam, pageParam);
        return paged;
    }

    /**
     * @param gwReturnListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<GwReturnListDto> insert(@Valid @RequestBody GwReturnListParam gwReturnListParam, UserInfoToken userInfo) {
        ResultObject<GwReturnListDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        GwReturnListDto gwReturnListDto = gwReturnListService.insert(gwReturnListParam, userInfo);
        if (gwReturnListDto != null) {
            resultObject.setData(gwReturnListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param gwReturnListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<GwReturnListDto> update(@PathVariable String sid, @Valid @RequestBody GwReturnListParam gwReturnListParam, UserInfoToken userInfo) {
        gwReturnListParam.setSid(sid);
        GwReturnListDto gwReturnListDto = gwReturnListService.update(gwReturnListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        if (gwReturnListDto != null) {
            resultObject.setData(gwReturnListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        gwReturnListService.delete(sids, userInfo);
        return resultObject;
    }

    /**
     * 当表头为进口，提取erp出口明细数据
     *
     * @param erpDecEListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("进口提取-查询接口")
    @PostMapping("extractIQuery")
    public ResultObject extractIQuery(@RequestBody ErpDecEListParam erpDecEListParam,
                                      PageParam pageParam, UserInfoToken userInfo) {
        return gwReturnListService.extractIData(erpDecEListParam, pageParam, userInfo);
    }

    /**
     * @param linkedNoParam
     * @param userInfo
     * @return
     */
    @ApiOperation("进口数据提取-条件筛选")
    @PostMapping("checkLinkedNo/{headId}")
    public ResultObject checkLinkedNo(@RequestBody ErpDecEListParam linkedNoParam, @PathVariable String headId,
                                      UserInfoToken userInfo) {
        ResultObject resultObject = gwReturnListService.checkLinkedNo(linkedNoParam, headId, userInfo);
        return resultObject;
    }

    /**
     * @param sids
     * @param userInfo
     * @return
     */
    @ApiOperation("进口数据提取-勾选")
    @PostMapping("checkLinkedNoBySid/{headId}/{sids}")
    public ResultObject checkLinkedNoBySid(@PathVariable List<String> sids, @PathVariable String headId,
                                           UserInfoToken userInfo) {
        ResultObject resultObject = gwReturnListService.checkLinkedNoBySid(sids, headId, userInfo);
        return resultObject;
    }

//    /**
//     * 当表头进出口标志为出口，则提取出口计划-发货单数据（发货单明细中发票号已生成的数据）
//     * @param decESendList
//     * @param pageParam
//     * @param userInfo
//     * @return
//     */
//    @ApiOperation("出口提取-查询接口")
//    @PostMapping("extractEQuery")
//    public ResultObject extractEQuery(@RequestBody DecESendList decESendList,
//                                PageParam pageParam, UserInfoToken userInfo) {
//        return gwReturnListService.extractEData(decESendList, pageParam, userInfo);
//    }
//
//    /**
//     *
//     * @param sid
//     * @param headId
//     * @param userInfo
//     * @return
//     */
//    @ApiOperation("出口计划提取-勾选")
//    @PostMapping("checkBySid/{headId}/{sid}")
//    public ResultObject checkBySid(@PathVariable String sid, @PathVariable String headId,
//                                           UserInfoToken userInfo) {
//        ResultObject resultObject = gwReturnListService.checkBySid(sid,headId,userInfo);
//        return resultObject;
//    }

    /**
     * 匹配报关数据
     *
     * @param gwReturnListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("匹配报关数据-查询")
    @PostMapping("matchCustoms")
    public ResultObject matchCustoms(@RequestBody GwReturnListParam gwReturnListParam, PageParam pageParam,
                                     UserInfoToken userInfo) {
        ResultObject resultObject = gwReturnListService.matchCustoms(gwReturnListParam, pageParam,
                userInfo);
        return resultObject;
    }

    /**
     * @param sid 表体sid
     * @param gwReturnListMatchDtoList
     * @param userInfo
     * @return
     */
    @ApiOperation("匹配报关数据-保存")
    @PostMapping("saveMatchCustoms/{sid}")
    public ResultObject saveMatchCustoms(@PathVariable String sid,
                                         @RequestBody List<GwReturnListMatchDto> gwReturnListMatchDtoList, UserInfoToken userInfo) {
        ResultObject resultObject = gwReturnListService.saveMatchCustoms(sid, gwReturnListMatchDtoList, userInfo);
        return resultObject;
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<GwReturnListParam> exportParam, UserInfoToken userInfo) throws Exception {
        List<GwReturnListDto> gwReturnListDtos = gwReturnListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(gwReturnListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), gwReturnListDtos);
    }

    /**
     * 导出, pCode转换中文名称
     *
     * @param list
     * @return
     */
    public void convertForPrint(List<GwReturnListDto> list) {
        for (GwReturnListDto dto : list) {
            if (ConstantsStatus.STATUS_0.equals(dto.getStatus())) {
                dto.setStatus("0 未匹配");
            }
            if (ConstantsStatus.STATUS_1.equals(dto.getStatus())) {
                dto.setStatus("1 已匹配");
            }
            if (StringUtils.isNotBlank(dto.getUnit())) {
                dto.setUnit(commonService.convertPCode(dto.getUnit(), PCodeType.UNIT));
            }
            if (StringUtils.isNotBlank(dto.getOriginCountry())) {
                dto.setOriginCountry(commonService.convertPCode(dto.getOriginCountry(), PCodeType.COUNTRY_OUTDATED));
            }
            if (StringUtils.isNotBlank(dto.getDestinationCountry())) {
                dto.setDestinationCountry(commonService.convertPCode(dto.getDestinationCountry(), PCodeType.COUNTRY_OUTDATED));
            }
            if (StringUtils.isNotBlank(dto.getCurr())) {
                dto.setCurr(commonService.convertPCode(dto.getCurr(), PCodeType.CURR_OUTDATED));
            }
        }
    }
}
