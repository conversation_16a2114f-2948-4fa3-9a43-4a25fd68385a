package com.dcjet.cs.api.plani;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.plani.*;
import com.dcjet.cs.plani.model.GwDecIPlanHead;
import com.dcjet.cs.plani.service.GwDecIPlanHeadService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.dcjet.cs.dto.erp.DecErpIHeadNParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 * 进口计划表头
 * <AUTHOR>
 * @date: 2021-1-20
 */
@RestController
@RequestMapping("v1/gwDecIPlanHead")
@Api(tags = "进口计划表头接口")
public class GwDecIPlanHeadController extends BaseController {
    @Resource
    private GwDecIPlanHeadService decIPlanHeadService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param decIPlanHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<GwDecIPlanHeadDto>> getListPaged(@RequestBody GwDecIPlanHeadParam decIPlanHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        decIPlanHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<GwDecIPlanHeadDto>> paged = decIPlanHeadService.getListPaged(decIPlanHeadParam, pageParam);
        return paged;
    }

    /**
     * @param decIPlanHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<GwDecIPlanHeadDto> insert(@Valid @RequestBody GwDecIPlanHeadParam decIPlanHeadParam, UserInfoToken userInfo) {
        ResultObject<GwDecIPlanHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        GwDecIPlanHeadDto decIPlanHeadDto = decIPlanHeadService.insert(decIPlanHeadParam, userInfo);
        if (decIPlanHeadDto != null) {
            resultObject.setData(decIPlanHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }

    /**
     * @param sid
     * @param decIPlanHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<GwDecIPlanHeadDto> update(@PathVariable String sid, @Valid @RequestBody GwDecIPlanHeadParam decIPlanHeadParam, UserInfoToken userInfo) {
        decIPlanHeadParam.setSid(sid);
        GwDecIPlanHeadDto decIPlanHeadDto = decIPlanHeadService.update(decIPlanHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (decIPlanHeadDto != null) {
            resultObject.setData(decIPlanHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }

    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) throws Exception {
        // 检查是否存在表体数据
        return decIPlanHeadService.delete(sids);
    }

    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<GwDecIPlanHeadParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<GwDecIPlanHeadDto> decIPlanHeadDtos = decIPlanHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(decIPlanHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), decIPlanHeadDtos);
    }

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<GwDecIPlanHeadDto> list) {
        for(GwDecIPlanHeadDto item : list) {
            if("0".equals(item.getStatus())) {
                item.setStatus(item.getStatus() + "|" + "暂存");
            } else if("1".equals(item.getStatus())) {
                item.setStatus(item.getStatus() + "|" + "已确认");
            } else if("2".equals(item.getStatus())) {
                item.setStatus(item.getStatus() + "|" + "已制单");
            }
            item.setTrafMode(item.getTrafMode() + "|" + CommonEnum.TRAF_MODE_CUSTOM_ENUM.getValue(item.getTrafMode()));
            item.setContainerType(item.getContainerType() + "|" + CommonEnum.CONTAINER_TYPE_CUSTOM_ENUM.getValue(item.getContainerType()));
            item.setForwardCode((StringUtils.isBlank(item.getForwardCode())) ? "" : item.getForwardCode() + "|" + (StringUtils.isBlank(item.getForwardName()) ? "" : item.getForwardName()));
        }
    }

    @ApiOperation("查询运输方式下拉(进口清关页面专用)")
    @GetMapping("trafMode/list")
    public ResultObject<List<TrafModeCustom>> selectTrafModeList() {
        return decIPlanHeadService.selectTrafModeList();
    }

    @ApiOperation("查询柜型下拉(进口清关页面专用)")
    @GetMapping("containerType/list")
    public ResultObject<List<GwContainerTypeCustom>> selectContainerTypeList() {
        return decIPlanHeadService.selectContainerTypeList();
    }

    /**
     * 锁定
     * @param sids
     * @param userInfoToken
     * @return
     */
    @ApiOperation("锁定")
    @PostMapping("lock/{sids}")
    public ResultObject<GwDecIPoQtyDto> lock(@PathVariable List<String> sids, UserInfoToken userInfoToken) {
        return decIPlanHeadService.lock(sids,userInfoToken.getCompany());
    }

    /**
     * 功能描述: 获取所有进口批次号
     *
     * <AUTHOR> 陈凯
     * @version :   1.0
     * @date：2024-02-01 < java.util.Map <java.lang.String,java.lang.Object>>>
     */
    @ApiOperation("获取所有进口批次号")
    @PostMapping("getInBatchNos")
    public ResultObject<GwDecIPlanHeadDto> getInBatchNos(UserInfoToken userInfo) {
        return decIPlanHeadService.getInBatchNos(userInfo);
    }

    /**
     * 提交任务
     *
     * @param tempSid
     * @param decErpIHeadNParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("进口任务生成")
    @PostMapping("taskSubmit/{tempSid}")
    public ResultObject taskSubmit(@PathVariable String tempSid, @Valid @RequestBody DecErpIHeadNParam decErpIHeadNParam, UserInfoToken userInfo) throws Exception {
        String sid = decErpIHeadNParam.getSid();
        String emsNo = decErpIHeadNParam.getEmsNo();
        String emsListNo = decErpIHeadNParam.getEmsListNo();
        String tradeMode = decErpIHeadNParam.getTradeMode();
        return decIPlanHeadService.taskSubmit(tempSid, sid, emsListNo, emsNo, tradeMode, userInfo);
    }
}
