package com.dcjet.cs.api.open.customize;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.returnEntry.EntryQueryParam;
import com.dcjet.cs.dto.returnEntry.ReturnEntryDto;
import com.dcjet.cs.dto.returnEntry.solar.SolarReturnEntryDto;
import com.dcjet.cs.dto.returnEntry.solar.SolarReturnEntryHead;
import com.dcjet.cs.entry.dao.DecEEntryHeadMapper;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 光洋定制-报关单查询开放接口
 * @author: WJ
 * @createDate: 2022/03/15 13:50
 */
@RestController
@RequestMapping("v1/public/customize/decEntry/solar")
@Api(tags = "提供给企业-报关单信息查询开放接口(光洋)")
public class SolarDecEntryController extends BaseController {

    @Resource
    private DecEEntryHeadMapper decEEntryHeadMapper;
    @Resource
    private PCodeHolder pCodeHolder;

    @ApiOperation("查询报关信息-光洋")
    @PostMapping("returnEntryInfo")
    public ResultObject<List<SolarReturnEntryDto>> returnEntryInfo(@RequestBody EntryQueryParam param, UserInfoToken userInfo) {
        ResultObject<List<SolarReturnEntryDto>> ro = ResultObject.createInstance(true, ReturnEntryDto.QUERY_SUCCESS);
        String contrNo = param.getContrNo();
        if(StringUtils.isBlank(contrNo)) {
            ro.setSuccess(false);
            ro.setCode(400);
            ro.setMessage(ReturnEntryDto.QUERY_FAIL + xdoi18n.XdoI18nUtil.t(",【合同协议号】不能为空"));
            return ro;
        }
        List<SolarReturnEntryHead> list = decEEntryHeadMapper.selectEntryDataByContrNoForSolar(contrNo, userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(list)) {
            List<SolarReturnEntryDto> dtoList = list.stream().map(s ->{
                SolarReturnEntryDto dto = new SolarReturnEntryDto();
                convertData(s);
                dto.setEntryHead(s);
                dto.setEntryList(s.getEntryList());
                return dto;
            }).collect(Collectors.toList());
            ro.setData(dtoList);
            ro.setTotal(dtoList.size());
        }else {
            ro.setSuccess(false);
            ro.setCode(400);
            ro.setMessage(ReturnEntryDto.QUERY_FAIL + xdoi18n.XdoI18nUtil.t("(不存在合同协议号为【") + contrNo + xdoi18n.XdoI18nUtil.t("】的报关单或状态【未结关】！)"));
        }
        return ro;
    }

    //格式化
    private void convertData(SolarReturnEntryHead head) {
        if(head != null) {
            //出境关别
            head.setIeport(pCodeHolder.getValue(PCodeType.CUSTOMS_REL,head.getIeport()));
            //运输方式
            head.setTrafMode(pCodeHolder.getValue(PCodeType.TRANSF,head.getTrafMode()));
            //监管方式，组装成【（0110）一般贸易】格式
            head.setTradeMode(String.format("（%s）%s",head.getTradeMode(),pCodeHolder.getValue(PCodeType.TRADE,head.getTradeMode())));
            //征免性质
            head.setCutMode(pCodeHolder.getValue(PCodeType.LEVYTYPE,head.getCutMode()));
            //贸易国
            head.setTradeNation(pCodeHolder.getValue(PCodeType.COUNTRY,head.getTradeNation()));
            //运抵国
            head.setTradeCountry(pCodeHolder.getValue(PCodeType.COUNTRY,head.getTradeCountry()));
            //指运港
            head.setDestPort(pCodeHolder.getValue(PCodeType.PORT_LIN,head.getDestPort()));
            //离境口岸
            head.setEntryPort(pCodeHolder.getValue(PCodeType.CIQ_ENTY_PORT,head.getEntryPort()));
            //包装种类
            head.setWrapType(pCodeHolder.getValue(PCodeType.WRAP,head.getWrapType()));
            //成交方式
            head.setTransMode(pCodeHolder.getValue(PCodeType.TRANSAC,head.getTransMode()));
            //运费标志
            if(StringUtils.isNotBlank(head.getFeeMark())) {
                head.setFeeMark(CommonEnum.FeeMarkEnumNoCode.getValue(head.getFeeMark()));
            }
            //保费标志
            if(StringUtils.isNotBlank(head.getInsurMark())) {
                head.setInsurMark(CommonEnum.FeeMarkEnumNoCode.getValue(head.getInsurMark()));
            }
            //杂费标志
            if(StringUtils.isNotBlank(head.getOtherMark())) {
                head.setOtherMark(CommonEnum.FeeMarkEnumNoCode.getValue(head.getOtherMark()));
            }
        }
        if(CollectionUtils.isNotEmpty(head.getEntryList())) {
            head.getEntryList().forEach(s ->{
                //申报单位
                s.setUnit(pCodeHolder.getValue(PCodeType.UNIT, s.getUnit()));
                //原产地
                s.setOriginCountry(pCodeHolder.getValue(PCodeType.COUNTRY, s.getOriginCountry()));
                //最终目的国
                s.setDestinationCountry(pCodeHolder.getValue(PCodeType.COUNTRY, s.getDestinationCountry()));
                //境内货源地
                s.setDistrictName(pCodeHolder.getValue(PCodeType.AREA, s.getDistrictCode()));
                //征免方式
                s.setDutyMode(pCodeHolder.getValue(PCodeType.LEVYMODE, s.getDutyMode()));
            });
        }
    }
}
