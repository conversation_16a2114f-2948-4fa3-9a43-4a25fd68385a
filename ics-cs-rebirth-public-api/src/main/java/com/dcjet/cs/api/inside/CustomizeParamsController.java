package com.dcjet.cs.api.inside;

import com.dcjet.cs.bi.service.BiCustomerParamsService;
import com.dcjet.cs.dto.bi.BiCustomerParamsDto;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 企业自定义参数库标准代码转换信息查询接口
 */
@RestController
@RequestMapping("v1/inside/customizeParams")
@Api(tags = "企业自定义参数库标准代码转换信息开放接口")
public class CustomizeParamsController {
    @Resource
    private BiCustomerParamsService biCustomerParamsService;

    @ApiOperation("获取所有企业自定义参数数据")
    @PostMapping("getParamValues")
    public ResultObject<List<BiCustomerParamsDto>> getParamValues(UserInfoToken userInfo) {
        return biCustomerParamsService.getAllCustomerParamsData(userInfo);
    }
}
