apply plugin: 'org.springframework.boot'
apply plugin: 'java'
apply plugin: 'org.liquibase.gradle'

ext {
    springfoxVersion = '2.9.2'
}

springBoot{
    mainClassName = 'com.dcjet.cs.Application'
}

dependencies {
    compile "io.springfox:springfox-swagger2:$springfoxVersion"
    compile "io.springfox:springfox-swagger-ui:$springfoxVersion"
    compile 'org.apache.commons:commons-io:1.3.2'
    testImplementation "org.springframework.boot:spring-boot-starter-test"
    liquibaseRuntime 'com.oracle:ojdbc6:11.2.0.1.0'  //liquibase
    liquibaseRuntime 'org.liquibase:liquibase-core:3.7.0'  //liquibase
    liquibaseRuntime 'org.liquibase:liquibase-groovy-dsl:2.0.3' //liquibase
    liquibaseRuntime 'ch.qos.logback:logback-core:1.2.3'  //liquibase
    liquibaseRuntime 'ch.qos.logback:logback-classic:1.2.3' //liquibase
    liquibaseRuntime 'org.postgresql:postgresql:42.2.10'  //liquibase
    compile 'org.apache.logging.log4j:log4j-core:2.17.1'
    compile 'org.apache.logging.log4j:log4j-api:2.17.1'
    compile 'org.apache.logging.log4j:log4j-to-slf4j:2.17.1'
    compile 'ch.qos.logback:logback-core:1.2.10'
    compile 'ch.qos.logback:logback-classic:1.2.10'
}
