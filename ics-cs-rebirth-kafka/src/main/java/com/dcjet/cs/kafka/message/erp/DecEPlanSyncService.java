package com.dcjet.cs.kafka.message.erp;

import com.dcjet.cs.erp.model.sap.ErpEPlanHead;
import com.dcjet.cs.erp.model.sap.ErpEPlanList;
import com.dcjet.cs.erp.model.sap.ErpSyncMsg;
import com.dcjet.cs.erp.service.mqsync.ErpDecEPlanHead4MqService;
import com.dcjet.cs.kafka.message.erp.mapper.JsonMapper;
import com.dcjet.cs.mqsync.core.annotation.DoSync;
import com.dcjet.cs.mqsync.core.annotation.MqSync;
import com.dcjet.cs.mqsync.core.annotation.MqSyncAutoAck;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * ERP出口计划消息 -- 数据先落地再通过异步调度执行消费
 * 2022/04/12
 */
@Slf4j
@MqSync
@Component
public class DecEPlanSyncService {
    @Resource
    ErpDecEPlanHead4MqService erpDecEPlanHead4MqService;

    @KafkaListener(topics = "#{'${dc.topics.eplan}'.split(',')}", groupId = "${spring.kafka.groupId}", autoStartup = "${spring.kafka.edi.autoStartup:true}")
    @DoSync(topics = "${dc.topics.eplan}", groupId = "${spring.kafka.groupId}")
    public void onMessageSync(ConsumerRecord<String, String> record, @MqSyncAutoAck Acknowledgment acknowledgment, @Headers Map<String,Object> headers) {
        log.info("消费出口计划消息 Thread [{}], partition[{}]", Thread.currentThread().getId(), record.partition());
        log.debug("消费出口计划消息 Thread [{}], partition[{}],  Kafka message:[{}] ", Thread.currentThread().getId(), record.partition(), record.value());
        try {
            ErpSyncMsg<Object, ErpEPlanHead, ErpEPlanList> message = JsonMapper.fromJson(record.value(), new TypeReference<ErpSyncMsg<Object, ErpEPlanHead, ErpEPlanList>>() {});
            if(message != null) {
                erpDecEPlanHead4MqService.saveKafkaData(message,record);
            }
        } catch (Exception ex) {
            log.error("同步erp出口计划表发生错误",ex);
            throw new RuntimeException(ex);
        }
    }

}
