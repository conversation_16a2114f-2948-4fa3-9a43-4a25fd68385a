package com.dcjet.cs.kafka.message;

import com.dcjet.cs.imp.model.KafkaReceiveMessage;
import com.dcjet.cs.imp.service.KafkaReturnInfoService;
import com.dcjet.cs.kafka.message.erp.mapper.JsonMapper;
import com.dcjet.cs.mqsync.core.annotation.DoSync;
import com.dcjet.cs.mqsync.core.annotation.MqSync;
import com.dcjet.cs.mqsync.core.annotation.MqSyncAutoAck;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 清单回执 -- 数据先落地再通过异步调度执行消费
 * 2022/04/12
 */
@Slf4j
@MqSync
@Component
public class MQComponent {
    @Resource
    private KafkaReturnInfoService kafkaReturnInfoService;

    @KafkaListener(topics = "#{'${dc.topics.bill}'.split(',')}", groupId = "${spring.kafka.groupId}", autoStartup = "${spring.kafka.autoStartup:true}")
    @DoSync(topics = "${dc.topics.bill}", groupId = "${spring.kafka.groupId}")
    public void onMessageSync(ConsumerRecord<String, String> record, @MqSyncAutoAck Acknowledgment acknowledgment, @Headers Map<String,Object> headers) {
        log.info("消费清单回执  topic [{}],offset [{}], partition[{}]", record.topic(),record.offset(), record.partition());
        log.debug("消费清单回执  topic [{}],offset [{}], partition[{}],  Kafka message:[{}] ", record.topic(),record.offset(), record.partition(), record.value());
        try {
            KafkaReceiveMessage message = JsonMapper.fromJson(record.value(), KafkaReceiveMessage.class);
            if (message != null && message.getData() != null) {
                kafkaReturnInfoService.saveKafkaData(message,record);
            }
        } catch (Exception ex) {
            log.error("解析回执发生错误",ex);
            throw new RuntimeException(ex);
        }
    }
}
