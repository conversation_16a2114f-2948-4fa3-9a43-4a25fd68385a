<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog/1.7"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog/1.7
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-1.7.xsd">


    <changeSet id="1" author="zyshen">
        <createProcedure>
            CREATE OR REPLACE PROCEDURE P_DEC_I_MERGE_BILLS_NEW(P_ERP_HEAD_SID VARCHAR2,P_IS_SUCC OUT INTEGER,P_ERR_MSG OUT VARCHAR2)
            IS

            V_TRADE_CODE VARCHAR2(20);--企业编码
            V_ENTRY_LIST_MAX_COUNT INT:=50;--报关单表体最大量
            V_BILL_LIST_MAX_COUNT NUMERIC(5) := 1000;--清单单据最大行数
            V_BILL_LIST_ONE_COUNT NUMERIC(5) := 800;--清单单据单条归并项最大行数

            V_BILL_COUNT_LAST INT; --清单剩余未归并数
            V_LIST_EMS_NO VARCHAR2(15);--表体备案号(生成表头使用)
            V_LIST_TRADE_MODE VARCHAR2(5);--表体监管方式(生成表头使用)
            V_LIST_CUT_MODE VARCHAR2(5);--表体征免性质(生成表头使用)
            V_LIST_BOND_MARK VARCHAR2(1);--表体保税标志(生成表头使用)
            V_LIST_G_MARK VARCHAR2(1);--表体物料类型(生成表头使用)
            V_EXEMPTS_NO VARCHAR2(20);--免表编号
            V_DCLCUS_MARK VARCHAR2(1);--报关标志(判断非报关使用)

            V_BILL_HEAD_SID VARCHAR2(36);--清单表头SID
            V_ENTRY_HEAD_SID VARCHAR2(36);--报关单表头SID

            V_NOW_BILL_GNO INT :=1 ;--最大清单序号
            V_MAX_ENTRY_G_NO INT:=0;--单据最大报关单归并序号
            V_MAX_BILL_G_NO INT:=0;--单据归并后最大清单单据序号
            V_IS_SPLIT VARCHAR2(1):=1;--单据是否拆分标记
            V_MAX_BILL_GNO_OLD INT :=0 ;--原有最大清单序号
            V_MAX_BILL_GNO_NEW INT :=1 ;--现有最大清单序号
            V_BODY_COUNT NUMBER(10);--提单表体数据COUNT
            V_INSERT_USER VARCHAR2(50);
            V_INSERT_USER_NAME VARCHAR2(50);

            V_MAX_LIST_BILL_GNO INT;--本次循环最大清单序号
            V_MAX_LIST_ENTRY_GNO INT;--本次循环最大报关单序号
            V_MIN_LIST_BILL_GNO INT;--下次循环最大清单序号
            V_MIN_LIST_ENTRY_GNO INT;--下次循环最大报关单序号

            V_MAX_QTY_1_COUNT INT;--最大法一数量数
            V_MAX_UNIT_COUNT INT;--申报单位一致，法一数量不一致数

            V_IS_BILLED INT;--是否已生成清单

            V_START_ENTRY_G_NO NUMBER(19);--开始归并序号
            V_MERGE_TYPE INT ;--归并标识 0：自动归并 1：人工归并
            V_NO_VALID_MERGE_DATA_COUNT NUMBER(10);--无效的人工归并数
            V_TD_EMS_LIST_NO VARCHAR2(100);--提单内部编号
            V_EMS_LIST_NO VARCHAR2(100);--清单内部编号
            V_BILL_TYPE VARCHAR2(1);--清单归并类型

            V_ENTRY_G_NO VARCHAR2(100);

            V_ARRIVAL_PORT_DATE DATE;
            V_ARRIVAL_DATE DATE;
            V_DAMAGE_MARK VARCHAR2(5);
            V_DAMAGE_REMARK VARCHAR2(255);
            V_PLATE_NUM VARCHAR2(100);
            V_DELIVERY_PERSON VARCHAR2(20);
            V_LINK_MAN_TEL VARCHAR2(20);
            V_EMSLISTNO_LENGTH NUMBER(10) := 0;
            V_EMSLISTNO_SUB NUMBER(10) := 0;

            BEGIN
            P_IS_SUCC:=1;

            --获取预归类表头基本信息
            SELECT T.MERGE_TYPE,T.EMS_LIST_NO,T.BILL_TYPE,T.TRADE_CODE,T.EXEMPTS_NO,T.DCLCUS_MARK,T.INSERT_USER,T.INSERT_USER_NAME,LENGTH(T.EMS_LIST_NO) INTO V_MERGE_TYPE,V_TD_EMS_LIST_NO,V_BILL_TYPE,V_TRADE_CODE,V_EXEMPTS_NO,V_DCLCUS_MARK,V_INSERT_USER,V_INSERT_USER_NAME,V_EMSLISTNO_LENGTH  FROM T_DEC_ERP_I_HEAD_N T WHERE T.SID=P_ERP_HEAD_SID;

            --校验非报关状态 ,强制状态为自动归并，报关单归并项1000项(变通)
            IF V_DCLCUS_MARK='2' THEN
            BEGIN
            V_MERGE_TYPE:=9;
            V_ENTRY_LIST_MAX_COUNT:=1000;
            END;
            END IF;

            P_DEC_I_MERGE_BILLS(P_ERP_HEAD_SID,P_IS_SUCC,P_ERR_MSG);
            IF P_IS_SUCC=0 THEN
            GOTO END_POINT;
            END IF;

            --查看表体是否有数据
            SELECT COUNT(1) INTO V_BODY_COUNT  FROM T_DEC_ERP_I_LIST_BILL T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS='0';
            IF V_BODY_COUNT=0 THEN
            P_ERR_MSG:='请先维护提单表体数据！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;

            ---人工归并判断
            IF V_MERGE_TYPE=1 THEN
            BEGIN
            ---是否含有重复项
            SELECT SUM(NO_VALID_COUNT),WM_CONCAT(ENTRY_G_NO) INTO V_NO_VALID_MERGE_DATA_COUNT,V_ENTRY_G_NO FROM (
            SELECT COUNT(1) NO_VALID_COUNT,R.ENTRY_G_NO FROM (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.BOND_MARK,DECODE(T.BOND_MARK,'0',T.G_MARK,'') AS G_MARK,T.ENTRY_G_NO,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE FROM T_DEC_ERP_I_LIST_BILL T  WHERE T.HEAD_ID=P_ERP_HEAD_SID  AND T.STATUS='0'
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,DECODE(T.BOND_MARK,'0',T.G_MARK,''),T.BOND_MARK,T.ENTRY_G_NO,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE
            ) R
            GROUP BY  R.ENTRY_G_NO
            HAVING COUNT(1) >1
            );
            IF V_NO_VALID_MERGE_DATA_COUNT>0 THEN
            P_ERR_MSG:=P_ERR_MSG||'报关单归并序号'||V_ENTRY_G_NO||'不符合归并原则！';
            P_IS_SUCC:=0;
            END IF;
            ---开始归并序号
            SELECT NVL(MIN(T.ENTRY_G_NO),0) INTO V_START_ENTRY_G_NO FROM T_DEC_ERP_I_LIST_BILL T WHERE  T.HEAD_ID=P_ERP_HEAD_SID  AND T.STATUS='0';
            --是否从从1开始
            IF V_START_ENTRY_G_NO!=1 THEN
            P_ERR_MSG:=P_ERR_MSG||'报关单商品序号不正确，请重新填写！';
            P_IS_SUCC:=0;
            END IF;
            ---是否连续
            SELECT COUNT(1) INTO V_NO_VALID_MERGE_DATA_COUNT FROM (
            SELECT T.ENTRY_G_NO,LEAD(T.ENTRY_G_NO,1,0) OVER (ORDER BY T.ENTRY_G_NO ) NEXT_ENTRY_G_NO,T.COP_G_NO,T.G_NAME FROM  T_DEC_ERP_I_LIST_BILL T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS='0'
            ) R
            WHERE R.NEXT_ENTRY_G_NO-R.ENTRY_G_NO>1;
            IF V_NO_VALID_MERGE_DATA_COUNT>0 THEN
            P_ERR_MSG:=P_ERR_MSG||'归并序号不连续，请重新归并！';
            P_IS_SUCC:=0;
            END IF;

            IF P_IS_SUCC=0 THEN
            GOTO END_POINT;
            END IF;
            END;
            END IF;
            --获取是否生成清单
            SELECT DECODE(COUNT(1),0,0,1) INTO V_IS_BILLED FROM T_DEC_I_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS='0';

            IF V_IS_BILLED=1 THEN
            ---清除生成的清单,报关单,报关单追中数据

            ---删除报关单追踪数据
            DELETE FROM T_DEC_I_CUSTOMS_TRACK H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0';
            DELETE FROM T_DEC_I_CUSTOMS_TRACK H WHERE H.HEAD_ID IN (SELECT SID FROM T_DEC_I_BILL_HEAD H WHERE H.HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0');
            /* 删除集装箱信息 */
            DELETE FROM T_DC_ENTRY_CONTAINER H WHERE H.HEAD_ID IN (SELECT H.SID FROM T_DEC_I_ENTRY_HEAD H WHERE H.ERP_HEAD_ID = P_ERP_HEAD_SID  AND H.STATUS='0');
            --删除报关单数据
            DELETE FROM T_DEC_I_ENTRY_LIST T WHERE EXISTS( SELECT 1 FROM T_DEC_I_ENTRY_HEAD H WHERE H.SID=T.HEAD_ID AND H.ERP_HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0');
            DELETE FROM T_DEC_I_ENTRY_HEAD H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0';
            --删除清单数据
            DELETE FROM T_DEC_I_BILL_LIST T WHERE EXISTS( SELECT 1 FROM T_DEC_I_BILL_HEAD H WHERE H.SID=T.HEAD_ID AND H.HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0');
            DELETE FROM T_DEC_I_BILL_HEAD H WHERE H.HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0';
            ---删除税金数据
            DELETE FROM T_TAX_I_LIST T  WHERE LIST_SID IN ( SELECT T.SID FROM T_DEC_I_ENTRY_LIST T,T_DEC_I_ENTRY_HEAD H WHERE H.SID=T.HEAD_ID AND H.ERP_HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0');
            DELETE FROM T_TAX_I_HEAD H WHERE HEAD_ID IN ( SELECT H.SID FROM T_DEC_I_ENTRY_HEAD H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0');

            END IF;
            -- 获取当前提单中未退回状态清单的最大值
            /*            SELECT CASE WHEN NVL(INSTR(MAX(T.EMS_LIST_NO), '-', -1, 1),0)= 0 THEN 0 ELSE NVL(TO_NUMBER(SUBSTR(MAX(T.EMS_LIST_NO),NVL(INSTR(MAX(T.EMS_LIST_NO), '-', -1, 1),0)+1,LENGTH(MAX(T.EMS_LIST_NO)))),0)  END   INTO V_MAX_BILL_GNO_OLD FROM T_DEC_I_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS!='0';*/

            /* 获取当前提单中未退回状态清单的最大值 */
            SELECT NVL(MAX(instr(EMS_LIST_NO,'-',-1)),0) INTO V_EMSLISTNO_SUB
            FROM T_DEC_I_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS!='0';

            IF(V_EMSLISTNO_LENGTH&lt;=V_EMSLISTNO_SUB) THEN
            select
            case when max(SUBSTR(EMS_LIST_NO, instr(EMS_LIST_NO,'-',-1)+1)) is null then 0 else max(to_number(SUBSTR(EMS_LIST_NO, instr(EMS_LIST_NO,'-',-1)+1))) end into V_MAX_BILL_GNO_OLD
            FROM T_DEC_I_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS!='0';
            end if;
            BEGIN

            --查询预录入报关单归并原则，根据具体报关单归并原则初次归并
            --自动归并
            IF V_MERGE_TYPE=0 THEN
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,FIRST_G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,COP_G_NAME,FIRST_COP_G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            FIRST_VALUE ( A.SERIAL_NO ) OVER (
            PARTITION BY A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.UNIT_1,
            A.UNIT_2,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_SERIAL_NO,
            FIRST_VALUE ( A.G_MODEL ) OVER (
            PARTITION BY A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.UNIT_1,
            A.UNIT_2,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_G_MODEL,
            FIRST_VALUE ( A.COP_G_MODEL ) OVER (
            PARTITION BY A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.UNIT_1,
            A.UNIT_2,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_COP_G_MODEL
            FROM T_DEC_ERP_I_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;
            --根据备案料号归并
            ELSIF V_MERGE_TYPE=4 THEN
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,FIRST_G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,COP_G_NAME,FIRST_COP_G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            FIRST_VALUE ( A.SERIAL_NO ) OVER (
            PARTITION BY
            A.G_NO,
            A.COP_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_SERIAL_NO,
            FIRST_VALUE ( A.G_MODEL ) OVER (
            PARTITION BY
            A.G_NO,
            A.COP_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_G_MODEL,
            FIRST_VALUE ( A.COP_G_MODEL ) OVER (
            PARTITION BY
            A.G_NO,
            A.COP_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_COP_G_MODEL
            FROM T_DEC_ERP_I_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;
            --根据模拟项号归并
            ELSIF V_MERGE_TYPE=5 THEN
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,FIRST_G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,COP_G_NAME,FIRST_COP_G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            FIRST_VALUE ( A.SERIAL_NO ) OVER (
            PARTITION BY
            A.ITEM_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_SERIAL_NO,
            FIRST_VALUE ( A.G_MODEL ) OVER (
            PARTITION BY
            A.ITEM_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_G_MODEL,
            FIRST_VALUE ( A.COP_G_MODEL ) OVER (
            PARTITION BY
            A.ITEM_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_COP_G_MODEL
            FROM T_DEC_ERP_I_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;
            --根据同型号归并
            ELSIF V_MERGE_TYPE=3 THEN
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,G_MODEL,COP_G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            FIRST_VALUE ( A.SERIAL_NO ) OVER (
            PARTITION BY
            A.G_MODEL,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_SERIAL_NO
            FROM T_DEC_ERP_I_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;

            ---人工归并
            ELSIF V_MERGE_TYPE=1 THEN
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,FIRST_G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,COP_G_NAME,FIRST_COP_G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            FIRST_VALUE ( A.SERIAL_NO ) OVER (
            PARTITION BY A.ENTRY_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_SERIAL_NO,
            FIRST_VALUE ( A.G_MODEL ) OVER (
            PARTITION BY A.ENTRY_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_G_MODEL,
            FIRST_VALUE ( A.COP_G_MODEL ) OVER (
            PARTITION BY A.ENTRY_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_COP_G_MODEL
            FROM T_DEC_ERP_I_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;

            ELSE
            --不归并
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,COP_G_NAME,G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            A.SERIAL_NO FIRST_SERIAL_NO
            FROM T_DEC_ERP_I_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;

            END IF;

            --获取最大行数
            SELECT COUNT(1) INTO V_BODY_COUNT FROM T_GWSTD_BILL_TEMP;
            --获取最大归并清单数
            SELECT MAX(BILL_GNO),MAX(NEW_ENTRY_G_NO) INTO V_MAX_BILL_G_NO,V_MAX_ENTRY_G_NO FROM T_GWSTD_BILL_TEMP;
            --获取法一数量大于1000000000的数据
            SELECT COUNT(1) INTO V_MAX_QTY_1_COUNT FROM T_GWSTD_BILL_TEMP WHERE QTY_1>1000000000;
            --获取申报单位一致，数量不一致的数据
            SELECT COUNT(1) INTO V_MAX_UNIT_COUNT FROM T_GWSTD_BILL_TEMP WHERE UNIT=UNIT_1 AND QTY!=QTY_1 and BOND_MARK='0';

            --校验非报关状态 ,报关单不生成
            IF V_DCLCUS_MARK!='2' THEN
            IF V_MAX_QTY_1_COUNT>1 THEN
            P_ERR_MSG:='报关单表体中存在法一数量大于1000000000数据！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;
            IF V_MAX_UNIT_COUNT>1 THEN
            P_ERR_MSG:='申报单位与法定单位一致时，申报数量需与法定数量一致！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;
            END IF;

            --判断是否有拆分的情况,清单单据序号为1，并且报关单归并后序号小于50，并且行数小余1000，认为是不需要拆分的
            IF(V_MAX_BILL_G_NO=1 AND V_MAX_ENTRY_G_NO&lt;=V_ENTRY_LIST_MAX_COUNT AND V_BODY_COUNT&lt;=V_BILL_LIST_MAX_COUNT) THEN
            V_IS_SPLIT:=0;
            END IF;

            &lt;&lt;REPEAT_LOOP&gt;&gt; --清单单据循环起始

            --原有清单最大序号+现有排序序号=本次清单内部编号序号
            V_MAX_BILL_GNO_NEW:=V_MAX_BILL_GNO_OLD+V_NOW_BILL_GNO;

            -- 生成当前清单单据的清单内部编号
            IF V_MAX_BILL_GNO_NEW>1 OR V_IS_SPLIT='1' THEN
            SELECT V_TD_EMS_LIST_NO ||'-'||V_MAX_BILL_GNO_NEW INTO  V_EMS_LIST_NO FROM DUAL;
            ELSE
            SELECT V_TD_EMS_LIST_NO INTO  V_EMS_LIST_NO FROM DUAL;
            END IF;

            V_BILL_HEAD_SID:=SYS_GUID();
            V_ENTRY_HEAD_SID:=SYS_GUID();

            DELETE FROM T_GWSTD_LIST_TEMP;
            --每次取出一票单据的所有表体数据插入临时表,小余1000行
            INSERT INTO T_GWSTD_LIST_TEMP
            SELECT * FROM (SELECT SID,NEW_ENTRY_G_NO,ROWNUM as SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,EMS_NO,BOND_MARK,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE,BILL_GNO,NULL,COP_G_MODEL FROM T_GWSTD_BILL_TEMP A WHERE BILL_GNO=V_NOW_BILL_GNO and NEW_ENTRY_G_NO&lt;=V_ENTRY_LIST_MAX_COUNT ORDER BY A.SERIAL_NO) WHERE ROWNUM&lt;=V_BILL_LIST_MAX_COUNT ORDER BY NEW_ENTRY_G_NO,SERIAL_NO ;

            --获取基本表头使用数据
            SELECT EMS_NO,TRADE_MODE,CUT_MODE,BOND_MARK,G_MARK INTO V_LIST_EMS_NO,V_LIST_TRADE_MODE,V_LIST_CUT_MODE,V_LIST_BOND_MARK,V_LIST_G_MARK FROM T_GWSTD_LIST_TEMP  WHERE ROWNUM=1;

            ---插入清单表头
            INSERT INTO T_DEC_I_BILL_HEAD (SID,HEAD_ID,EMS_LIST_NO,EMS_NO,TRADE_MODE,TRAF_MODE,TRANS_MODE,MASTER_CUSTOMS,OVERSEAS_SHIPPER,OVERSEAS_SHIPPER_NAME,HAWB,
            WAREHOUSE,RECEIVE_CODE,RECEIVE_NAME,TRADE_NATION,TRADE_NAME,TRADE_CODE,ENTRY_PORT,DEST_PORT,CUT_MODE,TRAF_NAME,VOYAGE_NO,TRADE_COUNTRY,I_E_PORT,CONTR_NO,LICENSE_NO,WRAP_TYPE,WRAP_TYPE2,PACK_NUM,NET_WT,GROSS_WT,FEE_MARK,FEE_RATE,FEE_CURR,INSUR_MARK,INSUR_RATE,INSUR_CURR,OTHER_MARK,OTHER_RATE,OTHER_CURR,NOTE,DESP_PORT,BOND_MARK,G_MARK,I_E_MARK,BILL_LIST_TYPE,DCLCUS_MARK,DCLCUS_TYPE,ENTRY_TYPE,CONFIRM_SPECIAL,CONFIRM_PRICE,CONFIRM_ROYALTIES,DUTY_SELF,MERGE_TYPE,TRADE_CREDIT_CODE,DATA_MARK,REL_EMS_NO,REL_LIST_NO,INSERT_USER,INSERT_USER_NAME,APPLY_NO,DECLARE_CODE,DECLARE_NAME,DECLARE_CREDIT_CODE,AGENT_CODE,AGENT_NAME,AGENT_CREDIT_CODE,REL_ENTRY_RECEIVE_CODE,REL_ENTRY_RECEIVE_NAME,REL_ENTRY_RECEIVE_CREDIT_CODE,CONFIRM_FORMULA_PRICE,CONFIRM_TEMP_PRICE)
            SELECT V_BILL_HEAD_SID,T.SID,V_EMS_LIST_NO,NVL(V_LIST_EMS_NO,V_EXEMPTS_NO),V_LIST_TRADE_MODE,T.TRAF_MODE,T.TRANS_MODE,T.MASTER_CUSTOMS,T.OVERSEAS_SHIPPER_AEO,T.OVERSEAS_SHIPPER_NAME,
            case when T.MAWB is not null and T.HAWB is not null then T.MAWB||'_'||T.HAWB when T.MAWB is not null and T.HAWB is null then T.MAWB when T.MAWB is null and T.HAWB is not null then T.HAWB end,
            T.WAREHOUSE,T.RECEIVE_CODE,T.RECEIVE_NAME,T.TRADE_NATION,T.TRADE_NAME,T.TRADE_CODE,T.ENTRY_PORT,T.DEST_PORT,V_LIST_CUT_MODE,T.TRAF_NAME,T.VOYAGE_NO,T.TRADE_COUNTRY,T.I_E_PORT,T.CONTR_NO,T.LICENSE_NO,T.WRAP_TYPE,T.WRAP_TYPE2,T.PACK_NUM,T.NET_WT,T.GROSS_WT,T.FEE_MARK,T.FEE_RATE,T.FEE_CURR,T.INSUR_MARK,T.INSUR_RATE,T.INSUR_CURR,T.OTHER_MARK,T.OTHER_RATE,T.OTHER_CURR,T.NOTE,DESP_PORT,V_LIST_BOND_MARK,V_LIST_G_MARK,'I',NVL(T.BILL_LIST_TYPE,'0'),T.DCLCUS_MARK,CASE WHEN T.DCLCUS_MARK='2' THEN NULL ELSE T.DCLCUS_TYPE END,CASE WHEN T.DCLCUS_MARK='2' THEN NULL ELSE NVL(T.ENTRY_TYPE,'1') END,
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '1' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '9' )), 1, 'N', '' )),
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '2' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '8' )), 1, 'N', '' )),
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '3' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '7' )), 1, 'N', '' )),
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '4' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '6' )), 1, 'N', '' )),
            T.MERGE_TYPE,T.TRADE_CREDIT_CODE,'1',REL_EMS_NO,REL_LIST_NO,INSERT_USER,INSERT_USER_NAME,T.APPLY_NO,T.DECLARE_CODE,T.DECLARE_NAME,T.DECLARE_CREDIT_CODE,T.AGENT_CODE,T.AGENT_NAME,T.AGENT_CREDIT_CODE,T.REL_ENTRY_RECEIVE_CODE,T.REL_ENTRY_RECEIVE_NAME,T.REL_ENTRY_RECEIVE_CREDIT_CODE,
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'A' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'Z' )), 1, 'N', '' )),
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'B' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'Y' )), 1, 'N', '' ))
            FROM T_DEC_ERP_I_HEAD_N T
            WHERE T.SID=P_ERP_HEAD_SID;

            --校验非报关状态,报关单不生成
            IF V_DCLCUS_MARK!='2' THEN
            --插入报关单表头
            INSERT INTO T_DEC_I_ENTRY_HEAD (BILL_HEAD_ID,SID,ERP_HEAD_ID,EMS_LIST_NO,EMS_NO,TRADE_MODE,TRAF_MODE,TRANS_MODE,DECLARE_CODE,DECLARE_NAME,MASTER_CUSTOMS,OVERSEAS_SHIPPER,OVERSEAS_SHIPPER_NAME,OWNER_CODE,OWNER_NAME, WAREHOUSE,TRADE_NATION,TRADE_NAME,TRADE_CODE,ENTRY_PORT,DEST_PORT,CUT_MODE,HAWB,TRAF_NAME,VOYAGE_NO,TRADE_COUNTRY,I_E_PORT,CONTR_NO,WRAP_TYPE,WRAP_TYPE2,PACK_NUM,NET_WT,GROSS_WT,FEE_MARK,FEE_RATE,FEE_CURR,INSUR_MARK,INSUR_RATE,INSUR_CURR,OTHER_MARK,OTHER_RATE,OTHER_CURR,NOTE,PROMISE_ITEMS,LICENSE_NO,DESP_PORT,BOND_MARK,DECLARE_CREDIT_CODE,TRADE_CREDIT_CODE,OWNER_CREDIT_CODE,INSERT_USER,I_E_DATE,INSERT_USER_NAME,ENTRY_STATUS,I_E_MARK,CARD_MARK,CHECK_MARK,TAX_MARK,ENTRY_TYPE,DECL_TRNREL)
            SELECT V_BILL_HEAD_SID,V_ENTRY_HEAD_SID,T.SID,V_EMS_LIST_NO,NVL(V_LIST_EMS_NO,V_EXEMPTS_NO),V_LIST_TRADE_MODE,T.TRAF_MODE,T.TRANS_MODE,T.DECLARE_CODE,T.DECLARE_NAME,T.MASTER_CUSTOMS,T.OVERSEAS_SHIPPER_AEO,T.OVERSEAS_SHIPPER_NAME,T.OWNER_CODE,T.OWNER_NAME,T.WAREHOUSE,T.TRADE_NATION,T.TRADE_NAME,T.TRADE_CODE,T.ENTRY_PORT,T.DEST_PORT,V_LIST_CUT_MODE,
            case when T.MAWB is not null and T.HAWB is not null then T.MAWB||'_'||T.HAWB when T.MAWB is not null and T.HAWB is null then T.MAWB when T.MAWB is null and T.HAWB is not null then T.HAWB end,
            T.TRAF_NAME,T.VOYAGE_NO,T.TRADE_COUNTRY,T.I_E_PORT,T.CONTR_NO,T.WRAP_TYPE,T.WRAP_TYPE2,T.PACK_NUM,T.NET_WT,T.GROSS_WT,T.FEE_MARK,T.FEE_RATE,T.FEE_CURR,T.INSUR_MARK,T.INSUR_RATE,T.INSUR_CURR,T.OTHER_MARK,T.OTHER_RATE,T.OTHER_CURR,T.NOTE,PROMISE_ITEMS,LICENSE_NO,DESP_PORT,V_LIST_BOND_MARK,T.DECLARE_CREDIT_CODE,T.TRADE_CREDIT_CODE,T.OWNER_CREDIT_CODE,T.INSERT_USER,I_E_DATE,INSERT_USER_NAME,'A0','I',CARD_MARK,CHECK_MARK,TAX_MARK,ENTRY_CLEARANCE_TYPE,DECL_TRNREL
            FROM T_DEC_ERP_I_HEAD_N T
            WHERE T.SID=P_ERP_HEAD_SID ;
            END IF;

            --插入清单表体
            INSERT INTO T_DEC_I_BILL_LIST (HEAD_ID,SID,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,CIQ_NO,G_NAME,G_MODEL,ORIGIN_COUNTRY,DESTINATION_COUNTRY,CURR,DEC_PRICE,QTY,UNIT,DEC_TOTAL,QTY_1,UNIT_1,QTY_2,UNIT_2,DUTY_MODE,EXG_VERSION,FAC_G_NO,DISTRICT_CODE,DISTRICT_POST_CODE,ENTRY_G_NO,NOTE,NOTE_1,NOTE_2,NOTE_3,GROSS_WT,NET_WT,EMS_LIST_NO,COP_G_NAME,COP_G_MODEL,LINKED_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_CODE)
            SELECT V_BILL_HEAD_SID,T.SID,E.SERIAL_NO,T.G_NO,T.COP_G_NO,T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.G_MODEL,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.CURR,
            CASE WHEN T.BOND_MARK='0' THEN ROUND(T.DEC_PRICE,4) ELSE T.DEC_PRICE END ,T.QTY,T.UNIT,CASE WHEN T.BOND_MARK='0' THEN ROUND(T.DEC_TOTAL,2) ELSE T.DEC_TOTAL END,T.QTY_1,T.UNIT_1,T.QTY_2,T.UNIT_2,T.DUTY_MODE,T.EXG_VERSION,T.FAC_G_NO,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,E.ENTRY_G_NO,T.NOTE,T.NOTE_1,T.NOTE_2,T.NOTE_3,T.GROSS_WT,T.NET_WT,V_EMS_LIST_NO,T.COP_G_NAME,T.COP_G_MODEL,T.LINKED_NO,T.SUPPLIER_CODE,T.SUPPLIER_NAME,V_TRADE_CODE FROM
            T_GWSTD_LIST_TEMP E LEFT JOIN T_DEC_ERP_I_LIST_BILL T ON E.SID=T.SID;

            --校验非报关状态 ,报关单不生成
            IF V_DCLCUS_MARK!='2' THEN
            --插入报关单表体
            INSERT INTO T_DEC_I_ENTRY_LIST (HEAD_ID,SID,SERIAL_NO, CODE_T_S,CIQ_CODE, G_NAME,G_MODEL,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE,UNIT_1,QTY_1,UNIT_2,QTY_2,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,TRADE_CODE)
            SELECT  V_ENTRY_HEAD_SID,SYS_GUID(),ENTRY_G_NO, CODE_T_S, CIQ_NO,G_NAME,case when count ( 1 ) > 1 then cop_g_model else g_model end,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,SUM(NVL(QTY,0)) ,ROUND(SUM(NVL(DEC_TOTAL,0)),2) ,DECODE(SUM(NVL(QTY,0)),0,0,ROUND(SUM(NVL(DEC_TOTAL,0))/SUM(NVL(QTY,0)),4)),UNIT_1,SUM(QTY_1),UNIT_2,DECODE(UNIT_2,NULL,NULL,SUM(NVL(QTY_2,0))),DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,V_TRADE_CODE FROM T_GWSTD_LIST_TEMP T GROUP BY T.ENTRY_G_NO,T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.G_MODEL,T.COP_G_MODEL,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE;
            END IF;

            UPDATE T_GWSTD_BILL_TEMP SET BILL_HEAD_ID=V_BILL_HEAD_SID WHERE SID IN (SELECT SID FROM T_GWSTD_LIST_TEMP);
            --更新关联信息表中数据
            IF V_DCLCUS_MARK!='2' THEN
            UPDATE T_GWSTD_BILL_TEMP SET ENTRY_LIST_SID=(SELECT SID FROM T_DEC_I_ENTRY_LIST L WHERE L.TRADE_CODE=TRADE_CODE AND L.HEAD_ID=V_ENTRY_HEAD_SID AND L.SERIAL_NO=NEW_ENTRY_G_NO) WHERE SID IN(SELECT SID FROM T_GWSTD_LIST_TEMP);
            UPDATE T_DEC_ERP_I_RELATION T SET T.EMS_LIST_NO=V_EMS_LIST_NO,T.BILL_SID=V_BILL_HEAD_SID,T.BILL_LIST_SID=CLIST_SID,T.ENTRY_SID=V_ENTRY_HEAD_SID,T.ENTRY_LIST_SID=(SELECT ENTRY_LIST_SID FROM T_GWSTD_BILL_TEMP WHERE CLIST_SID=SID) WHERE T.CLIST_SID IN (SELECT SID FROM  T_GWSTD_LIST_TEMP );
            ELSE
            UPDATE T_DEC_ERP_I_RELATION T SET T.EMS_LIST_NO=V_EMS_LIST_NO,T.BILL_SID=V_BILL_HEAD_SID,T.BILL_LIST_SID=CLIST_SID WHERE T.CLIST_SID IN (SELECT SID FROM  T_GWSTD_LIST_TEMP );
            END IF;

            SELECT COUNT(1) INTO V_BILL_COUNT_LAST FROM T_GWSTD_BILL_TEMP T WHERE  BILL_HEAD_ID IS NULL;
            --判断是否有为归并的数据，如有，则循环
            IF V_BILL_COUNT_LAST>0 THEN
            --获取本次最大栏位信息和下次最小栏位信息
            SELECT MAX(BILL_GNO),MAX(ENTRY_G_NO) INTO V_MAX_LIST_BILL_GNO,V_MAX_LIST_ENTRY_GNO FROM T_GWSTD_LIST_TEMP A ;
            SELECT MIN(BILL_GNO),MIN(NEW_ENTRY_G_NO) INTO V_MIN_LIST_BILL_GNO,V_MIN_LIST_ENTRY_GNO FROM T_GWSTD_BILL_TEMP A WHERE BILL_HEAD_ID IS NULL;

            if v_min_list_bill_gno=v_max_list_bill_gno  then
            if V_MAX_LIST_ENTRY_GNO=V_MIN_LIST_ENTRY_GNO then
            --当下次的最小清单序号和本次一样时候，表体单据超过1000行，调整遗留数据到下个清单序号，报关单归并序号-上次最下报关单归并序号
            UPDATE T_GWSTD_BILL_TEMP SET BILL_GNO=BILL_GNO+1,NEW_ENTRY_G_NO=NEW_ENTRY_G_NO-V_MAX_LIST_ENTRY_GNO+1 WHERE BILL_HEAD_ID IS NULL;
            else
            UPDATE T_GWSTD_BILL_TEMP SET BILL_GNO=BILL_GNO+1,NEW_ENTRY_G_NO=NEW_ENTRY_G_NO-V_MAX_LIST_ENTRY_GNO WHERE BILL_HEAD_ID IS NULL;
            end if;
            else
            --否则按照正常循环走
            UPDATE T_GWSTD_BILL_TEMP SET NEW_ENTRY_G_NO=NEW_ENTRY_G_NO-V_MAX_LIST_ENTRY_GNO WHERE BILL_HEAD_ID IS NULL;
            end if;

            V_NOW_BILL_GNO:=V_NOW_BILL_GNO+1;
            GOTO REPEAT_LOOP;
            END IF;

            --             IF V_DCLCUS_MARK!='2' THEN
            ---整体插入报关单追踪
            INSERT INTO T_DEC_I_CUSTOMS_TRACK(SID,ERP_HEAD_ID,HEAD_ID,EMS_LIST_NO,DECLARE_PORT,INSERT_USER,TRADE_CODE)
            SELECT SYS_GUID(),P_ERP_HEAD_SID,T.SID,T.EMS_LIST_NO,T.I_E_PORT,T.INSERT_USER,T.TRADE_CODE FROM T_DEC_I_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS='0';
            --             end if;

            ---整体插入税金表头
            INSERT INTO T_TAX_I_HEAD (SID,HEAD_ID,TRADE_CODE,INSERT_USER,INSERT_TIME,INSERT_USER_NAME)
            SELECT SID,SID,T.TRADE_CODE,T.INSERT_USER,T.INSERT_TIME,T.INSERT_USER_NAME FROM T_DEC_I_ENTRY_HEAD T WHERE T.ERP_HEAD_ID=P_ERP_HEAD_SID AND T.STATUS='0'
            and INSTR('0245,0844,0845,0644,0446,9700,0110,0815,1300,1500,1523,1616,2025,2225,2600,2700,3010,3039,3100,3339,4500,4539,4561',TRADE_MODE)>0;

            ---整体插入税金表体
            INSERT INTO T_TAX_I_LIST (SID,HEAD_ID,LIST_SID,TRADE_CODE,INSERT_USER,INSERT_TIME,INSERT_USER_NAME)
            SELECT SYS_GUID(), HEAD_ID,SID,V_TRADE_CODE,V_INSERT_USER,SYSDATE(),V_INSERT_USER_NAME FROM T_DEC_I_ENTRY_LIST T WHERE T.HEAD_ID IN (SELECT SID FROM T_DEC_I_ENTRY_HEAD T WHERE T.ERP_HEAD_ID=P_ERP_HEAD_SID AND T.STATUS='0'
            and INSTR('0245,0844,0845,0644,0446,9700,0110,0815,1300,1500,1523,1616,2025,2225,2600,2700,3010,3039,3100,3339,4500,4539,4561',T.TRADE_MODE)>0);

            UPDATE T_DEC_ERP_I_LIST_N T SET T.BILL_HEAD_ID=(SELECT R.BILL_SID FROM T_DEC_ERP_I_RELATION R WHERE T.SID=R.LIST_SID) WHERE T.HEAD_ID=P_ERP_HEAD_SID;
            UPDATE T_DEC_ERP_I_HEAD_N T SET T.APPR_STATUS='9' WHERE T.SID=P_ERP_HEAD_SID;

            select ARRIVAL_PORT_DATE,ARRIVAL_DATE,DAMAGE_MARK,DAMAGE_REMARK,PLATE_NUM,DELIVERY_PERSON,LINK_MAN_TEL into V_ARRIVAL_PORT_DATE,V_ARRIVAL_DATE,V_DAMAGE_MARK,V_DAMAGE_REMARK,V_PLATE_NUM,V_DELIVERY_PERSON,V_LINK_MAN_TEL  from T_DEC_I_LOGISTICS_TRACK LOG where SID = P_ERP_HEAD_SID;

            UPDATE T_DEC_I_CUSTOMS_TRACK TRACK SET ARRIVAL_PORT_DATE=V_ARRIVAL_PORT_DATE,DELIVERY_DATE=V_ARRIVAL_DATE,DAMAGE_MARK=V_DAMAGE_MARK,DAMAGE_REMARK=V_DAMAGE_REMARK,PLATE_NUM=V_PLATE_NUM,DELIVERY_PERSON=V_DELIVERY_PERSON,LINK_MAN_TEL=V_LINK_MAN_TEL  WHERE TRACK.ERP_HEAD_ID = P_ERP_HEAD_SID;

            END;
            COMMIT;
            &lt;&lt;END_POINT&gt;&gt;
            NULL;
            EXCEPTION
            WHEN OTHERS THEN
            BEGIN
            ROLLBACK;
            P_IS_SUCC:=0;
            P_ERR_MSG:=SQLCODE||'--'||SQLERRM;
            END;
            END;
        </createProcedure>
    </changeSet>
    <changeSet id="2" author="zyshen">
        <createProcedure>
            CREATE OR REPLACE PROCEDURE P_DEC_E_MERGE_BILLS_NEW(P_ERP_HEAD_SID VARCHAR2,P_IS_SUCC OUT INTEGER,P_ERR_MSG OUT VARCHAR2)
            IS

            V_TRADE_CODE VARCHAR2(20);--企业编码
            V_ENTRY_LIST_MAX_COUNT INT:=50;--报关单表体最大量
            V_BILL_LIST_MAX_COUNT NUMERIC(5) := 1000;--清单单据最大行数
            V_BILL_LIST_ONE_COUNT NUMERIC(5) := 800;--清单单据单条归并项最大行数

            V_BILL_COUNT_LAST INT; --清单剩余未归并数
            V_LIST_EMS_NO VARCHAR2(15);--表体备案号(生成表头使用)
            V_LIST_TRADE_MODE VARCHAR2(5);--表体监管方式(生成表头使用)
            V_LIST_CUT_MODE VARCHAR2(5);--表体征免性质(生成表头使用)
            V_LIST_BOND_MARK VARCHAR2(1);--表体保税标志(生成表头使用)
            V_LIST_G_MARK VARCHAR2(1);--表体物料类型(生成表头使用)
            V_DCLCUS_MARK VARCHAR2(1);--报关标志(判断非报关使用)

            V_BILL_HEAD_SID VARCHAR2(36);--清单表头SID
            V_ENTRY_HEAD_SID VARCHAR2(36);--报关单表头SID

            V_NOW_BILL_GNO INT :=1 ;--最大清单序号
            V_MAX_ENTRY_G_NO INT:=0;--单据最大报关单归并序号
            V_MAX_BILL_G_NO INT:=0;--单据归并后最大清单单据序号
            V_IS_SPLIT VARCHAR2(1):=1;--单据是否拆分标记
            V_MAX_BILL_GNO_OLD INT :=0 ;--原有最大清单序号
            V_MAX_BILL_GNO_NEW INT :=1 ;--现有最大清单序号
            V_BODY_COUNT NUMBER(10);--提单表体数据COUNT
            V_INSERT_USER VARCHAR2(50);
            V_INSERT_USER_NAME VARCHAR2(50);

            V_MAX_LIST_BILL_GNO INT;--本次循环最大清单序号
            V_MAX_LIST_ENTRY_GNO INT;--本次循环最大报关单序号
            V_MIN_LIST_BILL_GNO INT;--下次循环最大清单序号
            V_MIN_LIST_ENTRY_GNO INT;--下次循环最大报关单序号

            V_MAX_QTY_1_COUNT INT;--最大法一数量数
            V_MAX_UNIT_COUNT INT;--申报单位一致，法一数量不一致数

            V_IS_BILLED INT;--是否已生成清单

            V_START_ENTRY_G_NO NUMBER(19);--开始归并序号
            V_MERGE_TYPE INT ;--归并标识 0：自动归并 1：人工归并
            V_NO_VALID_MERGE_DATA_COUNT NUMBER(10);--无效的人工归并数
            V_TD_EMS_LIST_NO VARCHAR2(100);--提单内部编号
            V_EMS_LIST_NO VARCHAR2(100);--清单内部编号
            V_BILL_TYPE VARCHAR2(1);--清单归并类型

            V_ENTRY_G_NO VARCHAR2(100);

            V_EMSLISTNO_LENGTH NUMBER(10) := 0;
            V_EMSLISTNO_SUB NUMBER(10) := 0;

            BEGIN
            P_IS_SUCC:=1;

            --获取预归类表头基本信息
            SELECT T.MERGE_TYPE,T.EMS_LIST_NO,T.BILL_TYPE,T.TRADE_CODE,T.DCLCUS_MARK,T.INSERT_USER,T.INSERT_USER_NAME,LENGTH(T.EMS_LIST_NO) INTO V_MERGE_TYPE,V_TD_EMS_LIST_NO,V_BILL_TYPE,V_TRADE_CODE,V_DCLCUS_MARK,V_INSERT_USER,V_INSERT_USER_NAME,V_EMSLISTNO_LENGTH  FROM T_DEC_ERP_E_HEAD_N T WHERE T.SID=P_ERP_HEAD_SID;

            --校验非报关状态 ,强制状态为自动归并，报关单归并项1000项(变通)
            IF V_DCLCUS_MARK='2' THEN
            BEGIN
            V_MERGE_TYPE:=9;
            V_ENTRY_LIST_MAX_COUNT:=1000;
            END;
            END IF;

            P_DEC_E_MERGE_BILLS(P_ERP_HEAD_SID,P_IS_SUCC,P_ERR_MSG);
            IF P_IS_SUCC=0 THEN
            GOTO END_POINT;
            END IF;

            --查看表体是否有数据
            SELECT COUNT(1) INTO V_BODY_COUNT  FROM T_DEC_ERP_E_LIST_BILL T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS='0';
            IF V_BODY_COUNT=0 THEN
            P_ERR_MSG:='请先维护提单表体数据！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;

            ---人工归并判断
            IF V_MERGE_TYPE=1 THEN
            BEGIN
            ---是否含有重复项
            SELECT SUM(NO_VALID_COUNT),WM_CONCAT(ENTRY_G_NO) INTO V_NO_VALID_MERGE_DATA_COUNT,V_ENTRY_G_NO FROM (
            SELECT COUNT(1) NO_VALID_COUNT,R.ENTRY_G_NO FROM (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.BOND_MARK,DECODE(T.BOND_MARK,'0',T.G_MARK,'') AS G_MARK,T.ENTRY_G_NO,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE FROM T_DEC_ERP_E_LIST_BILL T  WHERE T.HEAD_ID=P_ERP_HEAD_SID  AND T.STATUS='0'
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,DECODE(T.BOND_MARK,'0',T.G_MARK,''),T.BOND_MARK,T.ENTRY_G_NO,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE
            ) R
            GROUP BY  R.ENTRY_G_NO
            HAVING COUNT(1) >1
            );
            IF V_NO_VALID_MERGE_DATA_COUNT>0 THEN
            P_ERR_MSG:=P_ERR_MSG||'报关单归并序号'||V_ENTRY_G_NO||'不符合归并原则！';
            P_IS_SUCC:=0;
            END IF;
            ---开始归并序号
            SELECT NVL(MIN(T.ENTRY_G_NO),0) INTO V_START_ENTRY_G_NO FROM T_DEC_ERP_E_LIST_BILL T WHERE  T.HEAD_ID=P_ERP_HEAD_SID  AND T.STATUS='0';
            --是否从从1开始
            IF V_START_ENTRY_G_NO!=1 THEN
            P_ERR_MSG:=P_ERR_MSG||'报关单商品序号不正确，请重新填写！';
            P_IS_SUCC:=0;
            END IF;
            ---是否连续
            SELECT COUNT(1) INTO V_NO_VALID_MERGE_DATA_COUNT FROM (
            SELECT T.ENTRY_G_NO,LEAD(T.ENTRY_G_NO,1,0) OVER (ORDER BY T.ENTRY_G_NO ) NEXT_ENTRY_G_NO,T.COP_G_NO,T.G_NAME FROM  T_DEC_ERP_E_LIST_BILL T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS='0'
            ) R
            WHERE R.NEXT_ENTRY_G_NO-R.ENTRY_G_NO>1;
            IF V_NO_VALID_MERGE_DATA_COUNT>0 THEN
            P_ERR_MSG:=P_ERR_MSG||'归并序号不连续，请重新归并！';
            P_IS_SUCC:=0;
            END IF;

            /*SELECT COUNT(1) INTO V_BILL_COUNT_LAST FROM T_DEC_ERP_E_LIST_BILL T WHERE  T.HEAD_ID=P_ERP_HEAD_SID  AND T.STATUS='0';

            IF V_BILL_COUNT_LAST>V_BILL_LIST_MAX_COUNT THEN
            P_ERR_MSG:=P_ERR_MSG||'清单行数超过'||V_BILL_LIST_MAX_COUNT||'！';
            P_IS_SUCC:=0;
            END IF;*/


            IF P_IS_SUCC=0 THEN
            GOTO END_POINT;
            END IF;
            END;
            END IF;
            --获取是否生成清单
            SELECT DECODE(COUNT(1),0,0,1) INTO V_IS_BILLED FROM T_DEC_E_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS='0';

            IF V_IS_BILLED=1 THEN
            ---清除生成的清单,报关单,报关单追中数据

            ---删除报关单追踪数据
            DELETE FROM T_DEC_E_CUSTOMS_TRACK H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0';
            DELETE FROM T_DEC_E_CUSTOMS_TRACK H WHERE H.HEAD_ID IN (SELECT SID FROM T_DEC_E_BILL_HEAD H WHERE H.HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0');
            /* 删除集装箱信息 */
            DELETE FROM T_DC_ENTRY_CONTAINER H WHERE H.HEAD_ID IN (SELECT H.SID FROM T_DEC_E_ENTRY_HEAD H WHERE H.ERP_HEAD_ID = P_ERP_HEAD_SID  AND H.STATUS='0');
            --删除报关单数据
            DELETE FROM T_DEC_E_ENTRY_LIST T WHERE EXISTS( SELECT 1 FROM T_DEC_E_ENTRY_HEAD H WHERE H.SID=T.HEAD_ID AND H.ERP_HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0');
            DELETE FROM T_DEC_E_ENTRY_HEAD H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0';
            --删除清单数据
            DELETE FROM T_DEC_E_BILL_LIST T WHERE EXISTS( SELECT 1 FROM T_DEC_E_BILL_HEAD H WHERE H.SID=T.HEAD_ID AND H.HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0');
            DELETE FROM T_DEC_E_BILL_HEAD H WHERE H.HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0';
            ---删除税金数据
            DELETE FROM T_TAX_E_LIST T  WHERE LIST_SID IN ( SELECT T.SID FROM T_DEC_E_ENTRY_LIST T,T_DEC_E_ENTRY_HEAD H WHERE H.SID=T.HEAD_ID AND H.ERP_HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0');
            DELETE FROM T_TAX_E_HEAD H WHERE HEAD_ID IN ( SELECT H.SID FROM T_DEC_E_ENTRY_HEAD H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID AND H.STATUS='0');

            END IF;
            -- 获取当前提单中未退回状态清单的最大值
            /* 获取当前提单中未退回状态清单的最大值 */
            SELECT NVL(MAX(instr(EMS_LIST_NO,'-',-1)),0) INTO V_EMSLISTNO_SUB
            FROM T_DEC_E_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS!='0';

            IF(V_EMSLISTNO_LENGTH&lt;=V_EMSLISTNO_SUB) THEN
            select
            case when max(SUBSTR(EMS_LIST_NO, instr(EMS_LIST_NO,'-',-1)+1)) is null then 0 else max(to_number(SUBSTR(EMS_LIST_NO, instr(EMS_LIST_NO,'-',-1)+1))) end into V_MAX_BILL_GNO_OLD
            FROM T_DEC_E_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS!='0';
            end if;
            BEGIN

            --查询预录入报关单归并原则，根据具体报关单归并原则初次归并
            --自动归并
            IF V_MERGE_TYPE=0 THEN
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,FIRST_G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,COP_G_NAME,FIRST_COP_G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            FIRST_VALUE ( A.SERIAL_NO ) OVER (
            PARTITION BY A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.UNIT_1,
            A.UNIT_2,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_SERIAL_NO,
            FIRST_VALUE ( A.G_MODEL ) OVER (
            PARTITION BY A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.UNIT_1,
            A.UNIT_2,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_G_MODEL,
            FIRST_VALUE ( A.COP_G_MODEL ) OVER (
            PARTITION BY A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.UNIT_1,
            A.UNIT_2,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_COP_G_MODEL
            FROM T_DEC_ERP_E_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;
            --根据备案料号归并
            ELSIF V_MERGE_TYPE=4 THEN
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,FIRST_G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,COP_G_NAME,FIRST_COP_G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            FIRST_VALUE ( A.SERIAL_NO ) OVER (
            PARTITION BY
            A.G_NO,
            A.COP_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_SERIAL_NO,
            FIRST_VALUE ( A.G_MODEL ) OVER (
            PARTITION BY
            A.G_NO,
            A.COP_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_G_MODEL,
            FIRST_VALUE ( A.COP_G_MODEL ) OVER (
            PARTITION BY
            A.G_NO,
            A.COP_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_COP_G_MODEL
            FROM T_DEC_ERP_E_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;
            --根据模拟项号归并
            ELSIF V_MERGE_TYPE=5 THEN
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,FIRST_G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,COP_G_NAME,FIRST_COP_G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            FIRST_VALUE ( A.SERIAL_NO ) OVER (
            PARTITION BY
            A.ITEM_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_SERIAL_NO,
            FIRST_VALUE ( A.G_MODEL ) OVER (
            PARTITION BY
            A.ITEM_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_G_MODEL,
            FIRST_VALUE ( A.COP_G_MODEL ) OVER (
            PARTITION BY
            A.ITEM_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_COP_G_MODEL
            FROM T_DEC_ERP_E_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;
            --根据同型号归并
            ELSIF V_MERGE_TYPE=3 THEN
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,G_MODEL,COP_G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            FIRST_VALUE ( A.SERIAL_NO ) OVER (
            PARTITION BY
            A.G_MODEL,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_SERIAL_NO
            FROM T_DEC_ERP_E_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;

            ---人工归并
            ELSIF V_MERGE_TYPE=1 THEN
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,FIRST_G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,COP_G_NAME,FIRST_COP_G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            FIRST_VALUE ( A.SERIAL_NO ) OVER (
            PARTITION BY A.ENTRY_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_SERIAL_NO,
            FIRST_VALUE ( A.G_MODEL ) OVER (
            PARTITION BY A.ENTRY_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_G_MODEL,
            FIRST_VALUE ( A.COP_G_MODEL ) OVER (
            PARTITION BY A.ENTRY_G_NO,
            A.CODE_T_S,
            A.CIQ_NO,
            A.G_NAME,
            A.UNIT,
            A.CURR,
            A.ORIGIN_COUNTRY,
            A.DESTINATION_COUNTRY,
            A.EMS_NO,
            DECODE( A.BOND_MARK, '0', A.G_MARK, '' ),
            A.UNIT_1,
            A.UNIT_2,
            A.BOND_MARK,
            A.TRADE_MODE,
            A.DUTY_MODE,
            A.DISTRICT_CODE,
            A.DISTRICT_POST_CODE,
            A.CUT_MODE
            ORDER BY
            A.INSERT_TIME,
            A.SERIAL_NO
            ) FIRST_COP_G_MODEL
            FROM T_DEC_ERP_E_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;

            ELSE
            --不归并
            INSERT INTO T_GWSTD_BILL_TEMP
            SELECT SID,HEAD_ID,EMS_LIST_NO,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,G_NAME,G_MODEL,UNIT,UNIT_1,UNIT_2,ORIGIN_COUNTRY,DESTINATION_COUNTRY,DEC_PRICE,DEC_TOTAL,CURR,QTY_1,QTY_2,QTY,GROSS_WT,NET_WT,USE_TYPE,DUTY_MODE,EXG_VERSION,ENTRY_G_NO,NOTE,BOND_MARK,G_MARK,EMS_NO,COP_G_NAME,G_MODEL,ORDER_NO,INVOICE_NO,SUPPLIER_CODE,SUPPLIER_NAME,TRADE_MODE,FAC_G_NO,LINKED_NO,NULL,NULL,NOTE_1,NOTE_2,NOTE_3,DISTRICT_CODE,DISTRICT_POST_CODE,BILL_HEAD_ID,ITEM_NO,COST_CENTER,LINE_NO,TRADE_CODE,BILL_G_NO,CUT_MODE,CIQ_NO,CERT_NO1,CERT_NO2,FIRST_SERIAL_NO,
            DENSE_RANK ( ) OVER ( ORDER BY R.BILL_GNO,R.FIRST_SERIAL_NO,CEIL(R.ROWSID/V_BILL_LIST_ONE_COUNT)),
            DENSE_RANK() OVER(ORDER BY BILL_GNO) ,NULL
            FROM
            (
            SELECT
            E.* ,ROW_NUMBER() OVER (PARTITION BY BILL_GNO,FIRST_SERIAL_NO ORDER BY FIRST_SERIAL_NO,SERIAL_NO) ROWSID
            FROM
            (
            SELECT A.*,
            FIRST_VALUE (A.SERIAL_NO ) OVER (
            PARTITION BY
            A.EMS_NO,
            A.BOND_MARK,
            A.TRADE_MODE,
            DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.CUT_MODE
            order by
            A.SERIAL_NO) BILL_GNO,
            A.SERIAL_NO FIRST_SERIAL_NO
            FROM T_DEC_ERP_E_LIST_BILL A WHERE  A.HEAD_ID = P_ERP_HEAD_SID
            AND A.STATUS = '0'
            ORDER BY
            A.SERIAL_NO )
            E ORDER BY
            E.SERIAL_NO)
            R ORDER BY
            R.SERIAL_NO;

            END IF;

            --获取最大行数
            SELECT COUNT(1) INTO V_BODY_COUNT FROM T_GWSTD_BILL_TEMP;
            --获取最大归并清单数
            SELECT MAX(BILL_GNO),MAX(NEW_ENTRY_G_NO) INTO V_MAX_BILL_G_NO,V_MAX_ENTRY_G_NO FROM T_GWSTD_BILL_TEMP;
            --获取法一数量大于1000000000的数据
            SELECT COUNT(1) INTO V_MAX_QTY_1_COUNT FROM T_GWSTD_BILL_TEMP WHERE QTY_1>1000000000;
            --获取申报单位一致，数量不一致的数据 保税校验
            SELECT COUNT(1) INTO V_MAX_UNIT_COUNT FROM T_GWSTD_BILL_TEMP WHERE UNIT=UNIT_1 AND QTY!=QTY_1 and BOND_MARK='0';

            --校验非报关状态 ,报关单不生成
            IF V_DCLCUS_MARK!='2' THEN
            IF V_MAX_QTY_1_COUNT>1 THEN
            P_ERR_MSG:='报关单表体中存在法一数量大于1000000000数据！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;
            IF V_MAX_UNIT_COUNT>1 THEN
            P_ERR_MSG:='申报单位与法定单位一致时，申报数量需与法定数量一致！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;
            END IF;

            --判断是否有拆分的情况,清单单据序号为1，并且报关单归并后序号小于50，并且行数小余1000，认为是不需要拆分的
            IF(V_MAX_BILL_G_NO=1 AND V_MAX_ENTRY_G_NO&lt;=V_ENTRY_LIST_MAX_COUNT AND V_BODY_COUNT&lt;=V_BILL_LIST_MAX_COUNT) THEN
            V_IS_SPLIT:=0;
            END IF;

            &lt;&lt;REPEAT_LOOP&gt;&gt; --清单单据循环起始

            --原有清单最大序号+现有排序序号=本次清单内部编号序号
            V_MAX_BILL_GNO_NEW:=V_MAX_BILL_GNO_OLD+V_NOW_BILL_GNO;

            -- 生成当前清单单据的清单内部编号
            IF V_MAX_BILL_GNO_NEW>1 OR V_IS_SPLIT='1' THEN
            SELECT V_TD_EMS_LIST_NO ||'-'||V_MAX_BILL_GNO_NEW INTO  V_EMS_LIST_NO FROM DUAL;
            ELSE
            SELECT V_TD_EMS_LIST_NO INTO  V_EMS_LIST_NO FROM DUAL;
            END IF;

            V_BILL_HEAD_SID:=SYS_GUID();
            V_ENTRY_HEAD_SID:=SYS_GUID();

            --获取基本表头使用数据
            SELECT EMS_NO,TRADE_MODE,CUT_MODE,BOND_MARK,G_MARK INTO V_LIST_EMS_NO,V_LIST_TRADE_MODE,V_LIST_CUT_MODE,V_LIST_BOND_MARK,V_LIST_G_MARK FROM T_GWSTD_LIST_TEMP  WHERE BILL_GNO=V_NOW_BILL_GNO and ROWNUM=1;

            DELETE FROM T_GWSTD_LIST_TEMP;
            --每次取出一票单据的所有表体数据插入临时表,小于50项，小于1000行
            INSERT INTO T_GWSTD_LIST_TEMP
            SELECT * FROM (SELECT SID,NEW_ENTRY_G_NO,ROWNUM as SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,EMS_NO,BOND_MARK,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE,BILL_GNO,NULL,COP_G_MODEL FROM T_GWSTD_BILL_TEMP A WHERE BILL_GNO=V_NOW_BILL_GNO and NEW_ENTRY_G_NO&lt;=V_ENTRY_LIST_MAX_COUNT ORDER BY A.SERIAL_NO)  WHERE ROWNUM&lt;=V_BILL_LIST_MAX_COUNT ORDER BY NEW_ENTRY_G_NO,SERIAL_NO;



            ---插入清单表头
            INSERT INTO T_DEC_E_BILL_HEAD (SID,HEAD_ID,EMS_LIST_NO,EMS_NO,TRADE_MODE,TRAF_MODE,TRANS_MODE,MASTER_CUSTOMS,OVERSEAS_SHIPPER,OVERSEAS_SHIPPER_NAME,HAWB,
            WAREHOUSE,RECEIVE_CODE,RECEIVE_NAME,TRADE_NATION,TRADE_NAME,TRADE_CODE,ENTRY_PORT,DEST_PORT,CUT_MODE,TRAF_NAME,VOYAGE_NO,TRADE_COUNTRY,I_E_PORT,CONTR_NO,LICENSE_NO,WRAP_TYPE,WRAP_TYPE2,PACK_NUM,NET_WT,GROSS_WT,FEE_MARK,FEE_RATE,FEE_CURR,INSUR_MARK,INSUR_RATE,INSUR_CURR,OTHER_MARK,OTHER_RATE,OTHER_CURR,NOTE,DESP_PORT,BOND_MARK,G_MARK,I_E_MARK,BILL_LIST_TYPE,DCLCUS_MARK,DCLCUS_TYPE,ENTRY_TYPE,CONFIRM_SPECIAL,CONFIRM_PRICE,CONFIRM_ROYALTIES,DUTY_SELF,MERGE_TYPE,TRADE_CREDIT_CODE,DATA_MARK,REL_EMS_NO,REL_LIST_NO,INSERT_USER,INSERT_USER_NAME,APPLY_NO,DECLARE_CODE,DECLARE_NAME,DECLARE_CREDIT_CODE,AGENT_CODE,AGENT_NAME,AGENT_CREDIT_CODE,REL_ENTRY_RECEIVE_CODE,REL_ENTRY_RECEIVE_NAME,REL_ENTRY_RECEIVE_CREDIT_CODE,CONFIRM_FORMULA_PRICE,CONFIRM_TEMP_PRICE)
            SELECT V_BILL_HEAD_SID,T.SID,V_EMS_LIST_NO,V_LIST_EMS_NO,V_LIST_TRADE_MODE,T.TRAF_MODE,T.TRANS_MODE,T.MASTER_CUSTOMS,T.OVERSEAS_SHIPPER_AEO,T.OVERSEAS_SHIPPER_NAME,
            case when T.MAWB is not null and T.HAWB is not null then T.MAWB||'_'||T.HAWB when T.MAWB is not null and T.HAWB is null then T.MAWB when T.MAWB is null and T.HAWB is not null then T.HAWB end,
            T.WAREHOUSE,T.RECEIVE_CODE,T.RECEIVE_NAME,T.TRADE_NATION,T.TRADE_NAME,T.TRADE_CODE,T.ENTRY_PORT,T.DEST_PORT,V_LIST_CUT_MODE,T.TRAF_NAME,T.VOYAGE_NO,T.TRADE_COUNTRY,T.I_E_PORT,T.CONTR_NO,T.LICENSE_NO,T.WRAP_TYPE,T.WRAP_TYPE2,T.PACK_NUM,T.NET_WT,T.GROSS_WT,T.FEE_MARK,T.FEE_RATE,T.FEE_CURR,T.INSUR_MARK,T.INSUR_RATE,T.INSUR_CURR,T.OTHER_MARK,T.OTHER_RATE,T.OTHER_CURR,T.NOTE,DESP_PORT,V_LIST_BOND_MARK,V_LIST_G_MARK,'E',NVL(T.BILL_LIST_TYPE,'0'),T.DCLCUS_MARK,
            CASE WHEN T.DCLCUS_MARK='2' THEN NULL ELSE T.DCLCUS_TYPE END,CASE WHEN T.DCLCUS_MARK='2' THEN NULL ELSE NVL(T.ENTRY_TYPE,'2') END,
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '1' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '9' )), 1, 'N', '' )),
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '2' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '8' )), 1, 'N', '' )),
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '3' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '7' )), 1, 'N', '' )),
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '4' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, '6' )), 1, 'N', '' )),
            T.MERGE_TYPE,T.TRADE_CREDIT_CODE,'1',REL_EMS_NO,REL_LIST_NO,INSERT_USER,INSERT_USER_NAME,T.APPLY_NO,T.DECLARE_CODE,T.DECLARE_NAME,T.DECLARE_CREDIT_CODE,T.AGENT_CODE,T.AGENT_NAME,T.AGENT_CREDIT_CODE,T.REL_ENTRY_RECEIVE_CODE,T.REL_ENTRY_RECEIVE_NAME,T.REL_ENTRY_RECEIVE_CREDIT_CODE,
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'A' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'Z' )), 1, 'N', '' )),
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'B' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'Y' )), 1, 'N', '' ))
            FROM T_DEC_ERP_E_HEAD_N T
            WHERE T.SID=P_ERP_HEAD_SID;

            --校验非报关状态,报关单不生成
            IF V_DCLCUS_MARK!='2' THEN
            --插入报关单表头
            INSERT INTO T_DEC_E_ENTRY_HEAD (BILL_HEAD_ID,SID,ERP_HEAD_ID,EMS_LIST_NO,EMS_NO,TRADE_MODE,TRAF_MODE,TRANS_MODE,DECLARE_CODE,DECLARE_NAME,MASTER_CUSTOMS,OVERSEAS_SHIPPER,OVERSEAS_SHIPPER_NAME,OWNER_CODE,OWNER_NAME, WAREHOUSE,TRADE_NATION,TRADE_NAME,TRADE_CODE,ENTRY_PORT,DEST_PORT,CUT_MODE,HAWB,TRAF_NAME,VOYAGE_NO,TRADE_COUNTRY,I_E_PORT,CONTR_NO,WRAP_TYPE,WRAP_TYPE2,PACK_NUM,NET_WT,GROSS_WT,FEE_MARK,FEE_RATE,FEE_CURR,INSUR_MARK,INSUR_RATE,INSUR_CURR,OTHER_MARK,OTHER_RATE,OTHER_CURR,NOTE,PROMISE_ITEMS,LICENSE_NO,DESP_PORT,BOND_MARK,DECLARE_CREDIT_CODE,TRADE_CREDIT_CODE,OWNER_CREDIT_CODE,INSERT_USER,I_E_DATE,INSERT_USER_NAME,ENTRY_STATUS,I_E_MARK,DECL_TRNREL,ENTRY_TYPE)
            SELECT V_BILL_HEAD_SID,V_ENTRY_HEAD_SID,T.SID,V_EMS_LIST_NO,V_LIST_EMS_NO,V_LIST_TRADE_MODE,T.TRAF_MODE,T.TRANS_MODE,T.DECLARE_CODE,T.DECLARE_NAME,T.MASTER_CUSTOMS,T.OVERSEAS_SHIPPER_AEO,T.OVERSEAS_SHIPPER_NAME,T.OWNER_CODE,T.OWNER_NAME,T.WAREHOUSE,T.TRADE_NATION,T.TRADE_NAME,T.TRADE_CODE,T.ENTRY_PORT,T.DEST_PORT,V_LIST_CUT_MODE,
            case when T.MAWB is not null and T.HAWB is not null then T.MAWB||'_'||T.HAWB when T.MAWB is not null and T.HAWB is null then T.MAWB when T.MAWB is null and T.HAWB is not null then T.HAWB end,
            T.TRAF_NAME,T.VOYAGE_NO,T.TRADE_COUNTRY,T.I_E_PORT,T.CONTR_NO,T.WRAP_TYPE,T.WRAP_TYPE2,T.PACK_NUM,T.NET_WT,T.GROSS_WT,T.FEE_MARK,T.FEE_RATE,T.FEE_CURR,T.INSUR_MARK,T.INSUR_RATE,T.INSUR_CURR,T.OTHER_MARK,T.OTHER_RATE,T.OTHER_CURR,T.NOTE,PROMISE_ITEMS,LICENSE_NO,DESP_PORT,V_LIST_BOND_MARK,T.DECLARE_CREDIT_CODE,T.TRADE_CREDIT_CODE,T.OWNER_CREDIT_CODE,T.INSERT_USER,I_E_DATE,INSERT_USER_NAME,'A0','E',DECL_TRNREL,T.ENTRY_CLEARANCE_TYPE
            FROM T_DEC_ERP_E_HEAD_N T
            WHERE T.SID=P_ERP_HEAD_SID ;
            END IF;

            --插入清单表体
            INSERT INTO T_DEC_E_BILL_LIST (HEAD_ID,SID,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,CIQ_NO,G_NAME,G_MODEL,ORIGIN_COUNTRY,DESTINATION_COUNTRY,CURR,DEC_PRICE,QTY,UNIT,DEC_TOTAL,QTY_1,UNIT_1,QTY_2,UNIT_2,DUTY_MODE,EXG_VERSION,FAC_G_NO,DISTRICT_CODE,DISTRICT_POST_CODE,ENTRY_G_NO,NOTE,NOTE_1,NOTE_2,NOTE_3,GROSS_WT,NET_WT,EMS_LIST_NO,COP_G_NAME,COP_G_MODEL,LINKED_NO,CLIENT_CODE,CLIENT_NAME,TRADE_CODE)
            SELECT V_BILL_HEAD_SID,T.SID,E.SERIAL_NO,T.G_NO,T.COP_G_NO,T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.G_MODEL,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.CURR,
            CASE WHEN T.BOND_MARK='0' THEN ROUND(T.DEC_PRICE,4) ELSE T.DEC_PRICE END ,T.QTY,T.UNIT,CASE WHEN T.BOND_MARK='0' THEN ROUND(T.DEC_TOTAL,2) ELSE T.DEC_TOTAL END,T.QTY_1,T.UNIT_1,T.QTY_2,T.UNIT_2,T.DUTY_MODE,T.EXG_VERSION,T.FAC_G_NO,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,E.ENTRY_G_NO,T.NOTE,T.NOTE_1,T.NOTE_2,T.NOTE_3,T.GROSS_WT,T.NET_WT,V_EMS_LIST_NO,T.COP_G_NAME,T.COP_G_MODEL,T.LINKED_NO,T.SUPPLIER_CODE,T.SUPPLIER_NAME,V_TRADE_CODE FROM
            T_GWSTD_LIST_TEMP E LEFT JOIN T_DEC_ERP_E_LIST_BILL T ON E.SID=T.SID;

            --校验非报关状态 ,报关单不生成
            IF V_DCLCUS_MARK!='2' THEN
            --插入报关单表体
            INSERT INTO T_DEC_E_ENTRY_LIST (HEAD_ID,SID,SERIAL_NO, CODE_T_S,CIQ_CODE, G_NAME,G_MODEL,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE,UNIT_1,QTY_1,UNIT_2,QTY_2,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,TRADE_CODE)
            SELECT  V_ENTRY_HEAD_SID,SYS_GUID(),ENTRY_G_NO, CODE_T_S, CIQ_NO,G_NAME,case when count ( 1 ) > 1 then cop_g_model else g_model end,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,SUM(NVL(QTY,0)) ,ROUND(SUM(NVL(DEC_TOTAL,0)),2) ,DECODE(SUM(NVL(QTY,0)),0,0,ROUND(SUM(NVL(DEC_TOTAL,0))/SUM(NVL(QTY,0)),4)),UNIT_1,SUM(QTY_1),UNIT_2,DECODE(UNIT_2,NULL,NULL,SUM(NVL(QTY_2,0))),DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,V_TRADE_CODE FROM T_GWSTD_LIST_TEMP T GROUP BY T.ENTRY_G_NO,T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.G_MODEL,T.COP_G_MODEL,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE;
            END IF;

            UPDATE T_GWSTD_BILL_TEMP SET BILL_HEAD_ID=V_BILL_HEAD_SID WHERE SID IN (SELECT SID FROM T_GWSTD_LIST_TEMP);
            --更新关联信息表中数据
            IF V_DCLCUS_MARK!='2' THEN
            UPDATE T_GWSTD_BILL_TEMP SET ENTRY_LIST_SID=(SELECT SID FROM T_DEC_E_ENTRY_LIST L WHERE L.TRADE_CODE=TRADE_CODE AND L.HEAD_ID=V_ENTRY_HEAD_SID AND L.SERIAL_NO=NEW_ENTRY_G_NO) WHERE SID IN(SELECT SID FROM T_GWSTD_LIST_TEMP);
            UPDATE T_DEC_ERP_E_RELATION T SET T.EMS_LIST_NO=V_EMS_LIST_NO,T.BILL_SID=V_BILL_HEAD_SID,T.BILL_LIST_SID=CLIST_SID,T.ENTRY_SID=V_ENTRY_HEAD_SID,T.ENTRY_LIST_SID=(SELECT ENTRY_LIST_SID FROM T_GWSTD_BILL_TEMP WHERE CLIST_SID=SID) WHERE T.CLIST_SID IN (SELECT SID FROM  T_GWSTD_LIST_TEMP );
            ELSE
            UPDATE T_DEC_ERP_E_RELATION T SET T.EMS_LIST_NO=V_EMS_LIST_NO,T.BILL_SID=V_BILL_HEAD_SID,T.BILL_LIST_SID=CLIST_SID WHERE T.CLIST_SID IN (SELECT SID FROM  T_GWSTD_LIST_TEMP );
            END IF;

            SELECT COUNT(1) INTO V_BILL_COUNT_LAST FROM T_GWSTD_BILL_TEMP T WHERE  BILL_HEAD_ID IS NULL;
            --判断是否有为归并的数据，如有，则循环
            IF V_BILL_COUNT_LAST>0 THEN
            --获取本次最大栏位信息和下次最小栏位信息
            SELECT MAX(BILL_GNO),MAX(ENTRY_G_NO) INTO V_MAX_LIST_BILL_GNO,V_MAX_LIST_ENTRY_GNO FROM T_GWSTD_LIST_TEMP A ;
            SELECT MIN(BILL_GNO),MIN(NEW_ENTRY_G_NO) INTO V_MIN_LIST_BILL_GNO,V_MIN_LIST_ENTRY_GNO FROM T_GWSTD_BILL_TEMP A WHERE BILL_HEAD_ID IS NULL;

            --当下次的最小清单序号和本次一样时候，表体单据超过1000行，调整遗留数据到下个清单序号，报关单归并序号-上次最下报关单归并序号
            if v_min_list_bill_gno=v_max_list_bill_gno then
            if V_MAX_LIST_ENTRY_GNO=V_MIN_LIST_ENTRY_GNO then
            --当最大序号和下次最小序号相等，认为是项号数据强制分拆，顺序号序号+1
            UPDATE T_GWSTD_BILL_TEMP SET BILL_GNO=BILL_GNO+1,NEW_ENTRY_G_NO=NEW_ENTRY_G_NO-V_MAX_LIST_ENTRY_GNO+1 WHERE BILL_HEAD_ID IS NULL;
            else
            --当最大序号和下次最小序号不想打，认为是项号数据正常，顺序号序号正常扣除
            UPDATE T_GWSTD_BILL_TEMP SET BILL_GNO=BILL_GNO+1,NEW_ENTRY_G_NO=NEW_ENTRY_G_NO-V_MAX_LIST_ENTRY_GNO WHERE BILL_HEAD_ID IS NULL;
            end if;
            else
            --否则按照正常循环走
            UPDATE T_GWSTD_BILL_TEMP SET NEW_ENTRY_G_NO=NEW_ENTRY_G_NO-V_MAX_LIST_ENTRY_GNO WHERE BILL_HEAD_ID IS NULL;
            end if;

            V_NOW_BILL_GNO:=V_NOW_BILL_GNO+1;
            GOTO REPEAT_LOOP;
            END IF;

            ---整体插入报关单追踪
            INSERT INTO T_DEC_E_CUSTOMS_TRACK(SID,ERP_HEAD_ID,HEAD_ID,EMS_LIST_NO,DECLARE_PORT,INSERT_USER,TRADE_CODE)
            SELECT SYS_GUID(),P_ERP_HEAD_SID,T.SID,T.EMS_LIST_NO,T.I_E_PORT,T.INSERT_USER,T.TRADE_CODE FROM T_DEC_E_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS='0';

            --更新预录入单表体关联信息和表头状态·
            UPDATE T_DEC_ERP_E_LIST_N T SET T.BILL_HEAD_ID=(SELECT R.BILL_SID FROM T_DEC_ERP_E_RELATION R WHERE T.SID=R.LIST_SID) WHERE T.HEAD_ID=P_ERP_HEAD_SID;
            UPDATE T_DEC_ERP_E_HEAD_N T SET T.APPR_STATUS='9' WHERE T.SID=P_ERP_HEAD_SID;

            END;
            COMMIT;
            &lt;&lt;END_POINT&gt;&gt;
            NULL;
            EXCEPTION
            WHEN OTHERS THEN
            BEGIN
            ROLLBACK;
            P_IS_SUCC:=0;
            P_ERR_MSG:=SQLCODE||'--'||SQLERRM;
            END;
            END;
        </createProcedure>
    </changeSet>
    <changeSet id="3" author="zyshen">
        <createProcedure>
            CREATE OR REPLACE PROCEDURE P_DEC_E_MERGE_GOLDEN_BILLS(P_ERP_HEAD_SID VARCHAR2,P_IS_SUCC OUT INTEGER,P_ERR_MSG OUT VARCHAR2)
            IS
            TYPE ENTRY_LIST_TEMP IS RECORD
            (
            SERIAL_NO  T_DEC_E_ENTRY_LIST.SERIAL_NO%TYPE,
            CODE_T_S  T_DEC_E_ENTRY_LIST.CODE_T_S%TYPE,
            CIQ_NO  T_DEC_E_ENTRY_LIST.CIQ_CODE%TYPE,
            G_NAME  T_DEC_E_ENTRY_LIST.G_NAME%TYPE,
            G_MODEL    T_DEC_E_ENTRY_LIST.G_MODEL%TYPE,
            G_MARK  VARCHAR2(1),
            UNIT  T_DEC_E_ENTRY_LIST.UNIT%TYPE,
            CURR  T_DEC_E_ENTRY_LIST.CURR%TYPE,
            ORIGIN_COUNTRY  T_DEC_E_ENTRY_LIST.ORIGIN_COUNTRY%TYPE,
            DESTINATION_COUNTRY  T_DEC_E_ENTRY_LIST.DESTINATION_COUNTRY%TYPE,
            QTY  T_DEC_E_ENTRY_LIST.QTY%TYPE,
            DEC_TOTAL  T_DEC_E_ENTRY_LIST.DEC_TOTAL%TYPE,
            DEC_PRICE  T_DEC_E_ENTRY_LIST.DEC_PRICE%TYPE,
            UNIT_1 T_DEC_E_ENTRY_LIST.UNIT_1%TYPE,
            QTY_1 T_DEC_E_ENTRY_LIST.QTY_1%TYPE,
            UNIT_2 T_DEC_E_ENTRY_LIST.UNIT_2%TYPE,
            QTY_2 T_DEC_E_ENTRY_LIST.QTY_2%TYPE,
            BILL_GNO varchar2(80),
            EMS_NO  varchar2(12),
            BOND_MARK  varchar2(1),
            LIST_SIDS  CLOB,
            TRADE_MODE T_DEC_E_ENTRY_HEAD.TRADE_MODE%TYPE,
            DUTY_MODE  T_DEC_E_ENTRY_LIST.DUTY_MODE%TYPE,
            DISTRICT_CODE  T_DEC_E_ENTRY_LIST.DISTRICT_CODE%TYPE,
            DISTRICT_POST_CODE   T_DEC_E_ENTRY_LIST.DISTRICT_POST_CODE%TYPE,
            CUT_MODE varchar2(5)
            );
            TYPE ENTRY_LIST_TYPE  IS TABLE OF ENTRY_LIST_TEMP;
            V_ENTRY_LIST_ARRYS ENTRY_LIST_TYPE;--数据集合
            V_ENTRY_LIST_ROW  ENTRY_LIST_TEMP;--单行数据
            V_ENTRY_LIST_MAX_COUNT INT:=50;--报关单表体最大量
            V_MAX_BILL_GNO INT :=1 ;--最大清单序号
            V_BILL_HEAD_SID VARCHAR2(36);--清单表头oid
            V_ENTRY_HEAD_SID VARCHAR2(36);--报关单表头oid
            V_BILL_SERIAL_NO NUMBER(11):=0;--清单表体流水号
            V_IS_BILLED INT;--是否已生成清单
            V_BODY_COUNT NUMBER(10);--提单表体数据量
            V_START_ENTRY_G_NO NUMBER(19);--开始归并序号
            V_MERGE_TYPE INT ;--归并标识 0：自动归并 1：人工归并
            V_NO_VALID_MERGE_DATA_COUNT NUMBER(10);--无效的人工归并数
            V_TD_EMS_LIST_NO VARCHAR2(100);--提单内部编号
            V_EMS_LIST_NO VARCHAR2(100);--清单内部编号
            V_BILL_TYPE VARCHAR2(1);--清单归并类型
            V_MAX_BILL_GNO_OLD INT :=0 ;--原有最大清单序号

            V_TRADE_CODE VARCHAR2(20);--企业编码
            V_LIST_REQUIRED_FIELD VARCHAR2(10);
            V_ENTRY_LIST_SID VARCHAR2(40);
            V_ENTRY_G_NO VARCHAR2(100);
            V_DCLCUS_MARK VARCHAR2(1);
            V_EMSLISTNO_LENGTH NUMBER(10) := 0;
            V_EMSLISTNO_SUB NUMBER(10) := 0;

            BEGIN
            P_IS_SUCC:=1;
            V_LIST_REQUIRED_FIELD:='0';
            /* ---余量校验
            P_DEC_E_QTY_CALC(P_ERP_HEAD_SID,P_IS_SUCC, P_ERR_MSG);
            IF P_IS_SUCC=0 THEN
            GOTO END_POINT;
            END IF;*/

            --获取归并类型,提单内部编号
            SELECT T.MERGE_TYPE,T.EMS_LIST_NO,T.BILL_TYPE,t.TRADE_CODE,t.DCLCUS_MARK,LENGTH(T.EMS_LIST_NO) INTO V_MERGE_TYPE,V_TD_EMS_LIST_NO,V_BILL_TYPE,V_TRADE_CODE,V_DCLCUS_MARK,V_EMSLISTNO_LENGTH  FROM T_DEC_ERP_E_HEAD_N T WHERE T.SID=P_ERP_HEAD_SID;

            --校验非报关状态 ,强制状态为自动归并，报关单归并项1000项(变通)
            if V_DCLCUS_MARK='2' then
            begin
            V_MERGE_TYPE:=9;
            V_ENTRY_LIST_MAX_COUNT:=1000;
            end;
            end if;

            P_DEC_E_MERGE_BILLS(P_ERP_HEAD_SID,P_IS_SUCC,P_ERR_MSG);
            IF P_IS_SUCC=0 THEN
            GOTO END_POINT;
            END IF;

            --查看表体是否有数据
            SELECT COUNT(1) INTO V_BODY_COUNT  FROM T_DEC_ERP_E_LIST_BILL T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            IF V_BODY_COUNT=0 THEN
            P_ERR_MSG:='请先维护提单表体数据！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;

            ---人工归并判断
            IF V_MERGE_TYPE=1 THEN
            BEGIN
            SELECT SUM(NO_VALID_COUNT),wm_concat(ENTRY_G_NO) INTO V_NO_VALID_MERGE_DATA_COUNT,V_ENTRY_G_NO FROM (
            SELECT COUNT(1) NO_VALID_COUNT,R.ENTRY_G_NO FROM (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.BOND_MARK,DECODE(T.BOND_MARK,'0',T.G_MARK,'') AS G_MARK,T.ENTRY_G_NO,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE
            FROM T_DEC_ERP_E_LIST_BILL T  WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0'
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,DECODE(T.BOND_MARK,'0',T.G_MARK,''),T.BOND_MARK,T.ENTRY_G_NO,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE
            ) R
            GROUP BY  R.ENTRY_G_NO
            HAVING COUNT(1) >1
            );
            IF V_NO_VALID_MERGE_DATA_COUNT>0 THEN
            P_ERR_MSG:=P_ERR_MSG||'报关单归并序号'||V_ENTRY_G_NO||'不符合归并原则！';
            P_IS_SUCC:=0;
            END IF;
            ---开始归并序号
            SELECT NVL(MIN(T.ENTRY_G_NO),0) INTO V_START_ENTRY_G_NO FROM T_DEC_ERP_E_LIST_BILL T WHERE  T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --是否从从1开始
            IF V_START_ENTRY_G_NO!=1 THEN
            P_ERR_MSG:=P_ERR_MSG||'报关单商品序号不正确，请重新填写！';
            P_IS_SUCC:=0;
            END IF;
            ---是否连续
            SELECT COUNT(1) INTO V_NO_VALID_MERGE_DATA_COUNT FROM (
            SELECT T.ENTRY_G_NO,LEAD(T.ENTRY_G_NO,1,0) OVER (ORDER BY T.ENTRY_G_NO ) NEXT_ENTRY_G_NO,T.COP_G_NO,T.G_NAME FROM  T_DEC_ERP_E_LIST_BILL T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0'
            ) R
            WHERE R.NEXT_ENTRY_G_NO-R.ENTRY_G_NO>1;
            IF V_NO_VALID_MERGE_DATA_COUNT>0 THEN
            P_ERR_MSG:=P_ERR_MSG||'归并序号不连续，请重新归并！';
            P_IS_SUCC:=0;
            END IF;
            IF P_IS_SUCC=0 THEN
            GOTO END_POINT;
            END IF;
            END;
            END IF;
            --获取是否生成清单
            SELECT DECODE(COUNT(1),0,0,1) INTO V_IS_BILLED FROM T_DEC_E_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';

            IF V_IS_BILLED=1 THEN

            ---删除报关单追踪数据
            DELETE FROM T_DEC_E_CUSTOMS_TRACK H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0';
            ---删除费用数据
            DELETE FROM T_TAX_E_LIST T  WHERE LIST_SID in ( SELECT T.sid FROM T_DEC_E_ENTRY_LIST T,T_DEC_E_ENTRY_HEAD H WHERE H.SID=T.HEAD_ID AND H.ERP_HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0');
            DELETE FROM T_TAX_E_HEAD H WHERE HEAD_ID in ( SELECT H.sid FROM T_DEC_E_ENTRY_HEAD H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0');

            ---清除生成的清单、报关单、报关单追中数据
            --删除报关单数据
            DELETE FROM T_DEC_E_ENTRY_LIST T WHERE EXISTS( SELECT 1 FROM T_DEC_E_ENTRY_HEAD H WHERE H.SID=T.HEAD_ID AND H.ERP_HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0');
            DELETE FROM T_DEC_E_ENTRY_HEAD H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0';
            --删除清单数据
            DELETE FROM T_DEC_E_BILL_LIST T WHERE EXISTS( SELECT 1 FROM T_DEC_E_BILL_HEAD H WHERE H.SID=T.HEAD_ID AND H.HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0');
            DELETE FROM T_DEC_E_BILL_HEAD H WHERE H.HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0';

            END IF;

            -- 获取当前提单中未退回状态清单的最大值
            /*            SELECT case when nvl(instr(MAX(T.EMS_LIST_NO), '-', -1, 1),0)= 0 then 0 else nvl(to_number(substr(MAX(T.EMS_LIST_NO),nvl(instr(MAX(T.EMS_LIST_NO), '-', -1, 1),0)+1,length(MAX(T.EMS_LIST_NO)))),0)  end   into V_MAX_BILL_GNO_OLD FROM T_DEC_E_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS!='0';*/

            /* 获取当前提单中未退回状态清单的最大值 */
            SELECT NVL(MAX(instr(EMS_LIST_NO,'-',-1)),0) INTO V_EMSLISTNO_SUB
            FROM T_DEC_E_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS!='0';

            IF(V_EMSLISTNO_LENGTH&lt;=V_EMSLISTNO_SUB) THEN
            select
            case when max(SUBSTR(EMS_LIST_NO, instr(EMS_LIST_NO,'-',-1)+1)) is null then 0 else max(to_number(SUBSTR(EMS_LIST_NO, instr(EMS_LIST_NO,'-',-1)+1))) end into V_MAX_BILL_GNO_OLD
            FROM T_DEC_E_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS!='0';
            end if;
            BEGIN  ---保税
            --重置排序
            UPDATE T_DEC_ERP_E_LIST_BILL T SET T.SERIAL_NO=(SELECT R.RN FROM (SELECT ROW_NUMBER() OVER(partition by S.EMS_NO,S.BOND_MARK,S.TRADE_MODE,DECODE(S.BOND_MARK,'0',S.G_MARK,''),S.CUT_MODE ORDER BY S.BILL_G_NO
            ,S.ENTRY_G_NO,S.INSERT_TIME,S.SERIAL_NO) RN,S.SID FROM T_DEC_ERP_E_LIST_BILL S WHERE S.HEAD_ID=P_ERP_HEAD_SID and S.STATUS='0') R WHERE R.SID=T.SID)
            WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';

            --归并结果信息
            IF V_MERGE_TYPE=0 THEN
            --自动归并 将归并序号清空
            UPDATE T_DEC_ERP_E_LIST_BILL T SET T.ENTRY_G_NO=NULL WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --自动归并
            SELECT SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,BILL_GNO ,EMS_NO,BOND_MARK,LIST_SIDS,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM
            (SELECT DECODE( MOD(E.RN,V_ENTRY_LIST_MAX_COUNT),0,V_ENTRY_LIST_MAX_COUNT, MOD(E.RN,V_ENTRY_LIST_MAX_COUNT)) SERIAL_NO,DENSE_RANK() OVER ( ORDER BY E.EMS_NO,E.BOND_MARK,E.TRADE_MODE,E.G_MARK,E.CUT_MODE,ceil(E.RN/V_ENTRY_LIST_MAX_COUNT)) BILL_GNO,E.CODE_T_S,E.CIQ_NO,E.G_NAME,E.G_MODEL,E.UNIT,E.CURR,E.ORIGIN_COUNTRY,E.DESTINATION_COUNTRY,E.QTY,E.DEC_TOTAL,E.DEC_PRICE,
            E.UNIT_1,E.QTY_1,E.UNIT_2,E.QTY_2,E.EMS_NO,E.BOND_MARK,E.G_MARK,LIST_SIDS,E.TRADE_MODE,E.DUTY_MODE,E.DISTRICT_CODE,E.DISTRICT_POST_CODE,E.CUT_MODE FROM
            (
            SELECT ROW_NUMBER() OVER (PARTITION BY R.EMS_NO,R.BOND_MARK,R.TRADE_MODE,R.G_MARK,R.CUT_MODE ORDER BY R.SERIAL_NO) RN,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE,
            R.CODE_T_S,R.CIQ_NO,R.G_NAME,R.G_MARK,R.G_MODEL,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,R.EMS_NO,R.BOND_MARK,R.QTY,ROUND(R.DEC_TOTAL,2) DEC_TOTAL,DECODE(R.QTY,0,0,ROUND(R.DEC_TOTAL/R.QTY,4)) DEC_PRICE,LIST_SIDS,R.TRADE_MODE,R.DUTY_MODE FROM
            (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,SUM(T.QTY_1) QTY_1,T.UNIT_2,SUM(T.QTY_2) QTY_2,T.EMS_NO,T.BOND_MARK,T.G_MARK,
            case when count(distinct t.cop_g_model)>1 then  T.COP_G_MODEL else T.G_MODEL end  G_MODEL,
            T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,
            SUM(NVL(T.QTY,0)) QTY,SUM(NVL(T.DEC_TOTAL,0)) DEC_TOTAL,
            XMLAGG(XMLELEMENT(E,T.SID|| ',')).EXTRACT('//text()').getclobval() LIST_SIDS,MIN(SERIAL_NO) SERIAL_NO
            FROM (
            SELECT A.SID,A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.QTY_1,A.UNIT_2,A.QTY_2,A.EMS_NO,A.BOND_MARK,DECODE(A.BOND_MARK,'0',A.G_MARK,'') AS  G_MARK,A.QTY,A.DEC_TOTAL,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE,
            FIRST_VALUE(A.G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) G_MODEL,
            FIRST_VALUE(A.COP_G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) COP_G_MODEL,A.SERIAL_NO
            FROM T_DEC_ERP_E_LIST_BILL A  WHERE A.HEAD_ID=P_ERP_HEAD_SID and A.STATUS='0'
            ) T
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.G_MODEL,T.COP_G_MODEL,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE) R
            ) E) ORDER BY EMS_NO,BOND_MARK,TRADE_MODE,G_MARK,BILL_GNO,SERIAL_NO;

            ELSIF V_MERGE_TYPE=4 THEN
            --根据备案序号(备案料号)归并 将归并序号清空
            UPDATE T_DEC_ERP_E_LIST_BILL T SET T.ENTRY_G_NO=NULL WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --自动归并
            SELECT SERIAL_NO, CODE_T_S, CIQ_NO,G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,BILL_GNO ,EMS_NO,BOND_MARK,LIST_SIDS,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM
            (SELECT DECODE( MOD(E.RN,V_ENTRY_LIST_MAX_COUNT),0,V_ENTRY_LIST_MAX_COUNT, MOD(E.RN,V_ENTRY_LIST_MAX_COUNT)) SERIAL_NO,DENSE_RANK() OVER ( ORDER BY E.EMS_NO,E.BOND_MARK,E.TRADE_MODE,E.G_MARK,E.CUT_MODE,ceil(E.RN/V_ENTRY_LIST_MAX_COUNT)) BILL_GNO,
            E.CODE_T_S,E.CIQ_NO,E.G_NO,E.G_NAME,E.G_MODEL,E.UNIT,E.CURR,E.ORIGIN_COUNTRY,E.DESTINATION_COUNTRY,E.QTY,E.DEC_TOTAL,E.DEC_PRICE,
            E.UNIT_1,E.QTY_1,E.UNIT_2,E.QTY_2,E.EMS_NO,E.BOND_MARK,E.G_MARK,LIST_SIDS,E.TRADE_MODE,E.DUTY_MODE,E.DISTRICT_CODE,E.DISTRICT_POST_CODE,E.CUT_MODE FROM
            (
            SELECT ROW_NUMBER() OVER (PARTITION BY R.EMS_NO,R.BOND_MARK,R.TRADE_MODE,R.G_MARK,R.CUT_MODE ORDER BY R.SERIAL_NO) RN,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE,
            R.CODE_T_S,R.CIQ_NO,R.G_NO,R.G_NAME,R.G_MARK,R.G_MODEL,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,R.EMS_NO,R.BOND_MARK,R.QTY,ROUND(R.DEC_TOTAL,2) DEC_TOTAL,DECODE(R.QTY,0,0,ROUND(R.DEC_TOTAL/R.QTY,4)) DEC_PRICE,LIST_SIDS,R.TRADE_MODE,R.DUTY_MODE FROM
            (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,SUM(T.QTY_1) QTY_1,T.UNIT_2,SUM(T.QTY_2) QTY_2,T.EMS_NO,T.BOND_MARK,T.G_MARK,
            case when count(distinct t.cop_g_model)>1 then  T.COP_G_MODEL else T.G_MODEL end  G_MODEL,
            T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,
            SUM(NVL(T.QTY,0)) QTY,SUM(NVL(T.DEC_TOTAL,0)) DEC_TOTAL,
            XMLAGG(XMLELEMENT(E,T.SID|| ',')).EXTRACT('//text()').getclobval() LIST_SIDS,MIN(SERIAL_NO) SERIAL_NO
            FROM (
            SELECT A.SID,A.CODE_T_S,A.CIQ_NO,A.G_NO,A.COP_G_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.QTY_1,A.UNIT_2,A.QTY_2,A.EMS_NO,A.BOND_MARK,DECODE(A.BOND_MARK,'0',A.G_MARK,'') AS  G_MARK,A.QTY,A.DEC_TOTAL,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE,
            FIRST_VALUE(A.G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.G_NO,A.COP_G_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) G_MODEL,
            FIRST_VALUE(A.COP_G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.G_NO,A.COP_G_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) COP_G_MODEL,A.SERIAL_NO
            FROM T_DEC_ERP_E_LIST_BILL A  WHERE A.HEAD_ID=P_ERP_HEAD_SID and A.STATUS='0'
            ) T
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.G_NO,T.COP_G_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.G_MODEL,T.COP_G_MODEL,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE) R
            ) E) ORDER BY EMS_NO,BOND_MARK,TRADE_MODE,G_MARK,BILL_GNO,SERIAL_NO;

            ELSIF V_MERGE_TYPE=5 THEN
            --根据模拟项号归并 将归并序号清空
            UPDATE T_DEC_ERP_E_LIST_BILL T SET T.ENTRY_G_NO=NULL WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --自动归并
            SELECT SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,BILL_GNO ,EMS_NO,BOND_MARK,LIST_SIDS,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM
            (SELECT DECODE( MOD(E.RN,V_ENTRY_LIST_MAX_COUNT),0,V_ENTRY_LIST_MAX_COUNT, MOD(E.RN,V_ENTRY_LIST_MAX_COUNT)) SERIAL_NO,DENSE_RANK() OVER ( ORDER BY E.EMS_NO,E.BOND_MARK,E.TRADE_MODE,E.G_MARK,E.CUT_MODE,ceil(E.RN/V_ENTRY_LIST_MAX_COUNT)) BILL_GNO,
            E.CODE_T_S,E.CIQ_NO,E.ITEM_NO,E.G_NAME,E.G_MODEL,E.UNIT,E.CURR,E.ORIGIN_COUNTRY,E.DESTINATION_COUNTRY,E.QTY,E.DEC_TOTAL,E.DEC_PRICE,
            E.UNIT_1,E.QTY_1,E.UNIT_2,E.QTY_2,E.EMS_NO,E.BOND_MARK,E.G_MARK,LIST_SIDS,E.TRADE_MODE,E.DUTY_MODE,E.DISTRICT_CODE,E.DISTRICT_POST_CODE,E.CUT_MODE FROM
            (
            SELECT ROW_NUMBER() OVER (PARTITION BY R.EMS_NO,R.BOND_MARK,R.TRADE_MODE,R.G_MARK,R.CUT_MODE ORDER BY R.SERIAL_NO) RN,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE,
            R.CODE_T_S,R.CIQ_NO,R.ITEM_NO,R.G_NAME,R.G_MARK,R.G_MODEL,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,R.EMS_NO,R.BOND_MARK,R.QTY,ROUND(R.DEC_TOTAL,2) DEC_TOTAL,DECODE(R.QTY,0,0,ROUND(R.DEC_TOTAL/R.QTY,4)) DEC_PRICE,LIST_SIDS,R.TRADE_MODE,R.DUTY_MODE FROM
            (
            SELECT T.CODE_T_S,T.CIQ_NO,T.ITEM_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,SUM(T.QTY_1) QTY_1,T.UNIT_2,SUM(T.QTY_2) QTY_2,T.EMS_NO,T.BOND_MARK,T.G_MARK,
            case when count(distinct t.cop_g_model)>1 then  T.COP_G_MODEL else T.G_MODEL end  G_MODEL,
            T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,
            SUM(NVL(T.QTY,0)) QTY,SUM(NVL(T.DEC_TOTAL,0)) DEC_TOTAL,
            XMLAGG(XMLELEMENT(E,T.SID|| ',')).EXTRACT('//text()').getclobval() LIST_SIDS,MIN(SERIAL_NO) SERIAL_NO
            FROM (
            SELECT A.SID,A.CODE_T_S,A.CIQ_NO,A.ITEM_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.QTY_1,A.UNIT_2,A.QTY_2,A.EMS_NO,A.BOND_MARK,DECODE(A.BOND_MARK,'0',A.G_MARK,'') AS  G_MARK,A.QTY,A.DEC_TOTAL,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE,
            FIRST_VALUE(A.G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.ITEM_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) G_MODEL,
            FIRST_VALUE(A.COP_G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.ITEM_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE ORDER BY A.INSERT_TIME,A.SERIAL_NO) COP_G_MODEL,A.SERIAL_NO
            FROM T_DEC_ERP_E_LIST_BILL A  WHERE A.HEAD_ID=P_ERP_HEAD_SID and A.STATUS='0'
            ) T
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.ITEM_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.G_MODEL,T.COP_G_MODEL,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE) R
            ) E) ORDER BY EMS_NO,BOND_MARK,TRADE_MODE,G_MARK,BILL_GNO,SERIAL_NO;

            ELSIF V_MERGE_TYPE=3 THEN
            --自动归并 将归并序号清空
            UPDATE T_DEC_ERP_E_LIST_BILL T SET T.ENTRY_G_NO=NULL WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --自动归并 只归并同型号的
            SELECT SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,BILL_GNO ,EMS_NO,BOND_MARK,LIST_SIDS,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM
            (SELECT DECODE( MOD(E.RN,V_ENTRY_LIST_MAX_COUNT),0,V_ENTRY_LIST_MAX_COUNT, MOD(E.RN,V_ENTRY_LIST_MAX_COUNT)) SERIAL_NO,DENSE_RANK() OVER ( ORDER BY E.EMS_NO,E.BOND_MARK,E.TRADE_MODE,E.G_MARK,E.CUT_MODE,ceil(E.RN/V_ENTRY_LIST_MAX_COUNT)) BILL_GNO,E.CODE_T_S,E.CIQ_NO,E.G_NAME,E.G_MODEL,E.UNIT,E.CURR,E.ORIGIN_COUNTRY,E.DESTINATION_COUNTRY,E.QTY,E.DEC_TOTAL,E.DEC_PRICE,
            E.UNIT_1,E.QTY_1,E.UNIT_2,E.QTY_2,E.EMS_NO,E.BOND_MARK,E.G_MARK,LIST_SIDS,E.TRADE_MODE,E.DUTY_MODE,E.DISTRICT_CODE,E.DISTRICT_POST_CODE,E.CUT_MODE FROM
            (
            SELECT ROW_NUMBER() OVER (PARTITION BY R.EMS_NO,R.BOND_MARK,R.TRADE_MODE,R.G_MARK,R.CUT_MODE ORDER BY R.SERIAL_NO) RN,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE,
            R.CODE_T_S,R.CIQ_NO,R.G_NAME,R.G_MARK,R.G_MODEL,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,R.EMS_NO,R.BOND_MARK,R.QTY,ROUND(R.DEC_TOTAL,2) DEC_TOTAL,DECODE(R.QTY,0,0,ROUND(R.DEC_TOTAL/R.QTY,4)) DEC_PRICE,LIST_SIDS,R.TRADE_MODE,R.DUTY_MODE FROM
            (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,SUM(T.QTY_1) QTY_1,T.UNIT_2,SUM(T.QTY_2) QTY_2,T.EMS_NO,T.BOND_MARK,T.G_MARK,T.G_MODEL,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,
            SUM(NVL(T.QTY,0)) QTY,SUM(NVL(T.DEC_TOTAL,0)) DEC_TOTAL,
            XMLAGG(XMLELEMENT(E,T.SID|| ',')).EXTRACT('//text()').getclobval() LIST_SIDS,MIN(SERIAL_NO) SERIAL_NO
            FROM (
            SELECT A.SID,A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.QTY_1,A.UNIT_2,A.QTY_2,A.EMS_NO,A.BOND_MARK,DECODE(A.BOND_MARK,'0',A.G_MARK,'') AS  G_MARK,A.QTY,A.DEC_TOTAL,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE,
            A.G_MODEL,A.SERIAL_NO
            FROM T_DEC_ERP_E_LIST_BILL A  WHERE A.HEAD_ID=P_ERP_HEAD_SID and A.STATUS='0'
            ) T
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.G_MODEL,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE) R
            ) E) ORDER BY EMS_NO,BOND_MARK,TRADE_MODE,G_MARK,BILL_GNO,SERIAL_NO;

            ELSIF V_MERGE_TYPE=1 THEN
            ---人工归并判断
            SELECT R.ENTRY_G_NO,R.CODE_T_S,R.CIQ_NO,R.G_NAME,R.G_MODEL,R.G_MARK,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY ,R.QTY ,ROUND(R.DEC_TOTAL,2) DEC_TOTAL ,DECODE(R.QTY,0,0,ROUND(R.DEC_TOTAL/R.QTY,4)) DEC_PRICE,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,1 AS  BILL_GNO,R.EMS_NO,R.BOND_MARK,R.LIST_SIDS,R.TRADE_MODE
            ,R.DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM (
            SELECT T.ENTRY_G_NO,T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,SUM(T.QTY_1) QTY_1,T.UNIT_2,SUM(T.QTY_2) QTY_2,T.EMS_NO,T.BOND_MARK,T.G_MARK,
            case when count(distinct t.cop_g_model)>1 then  T.COP_G_MODEL else T.G_MODEL end  G_MODEL,T.TRADE_MODE,T.DUTY_MODE,
            SUM(NVL(T.QTY,0)) QTY,SUM(NVL(T.DEC_TOTAL,0)) DEC_TOTAL,XMLAGG(XMLELEMENT(E,T.SID|| ',')).EXTRACT('//text()').getclobval() LIST_SIDS,MIN(SERIAL_NO) SERIAL_NO,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE
            FROM (
            SELECT
            A.SID,A.ENTRY_G_NO,A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.QTY_1,A.UNIT_2,A.QTY_2,A.EMS_NO,A.BOND_MARK,DECODE(A.BOND_MARK,'0',A.G_MARK,'') AS  G_MARK,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE,
            FIRST_VALUE(A.G_MODEL) OVER (PARTITION BY A.ENTRY_G_NO,A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE ORDER BY A.Insert_Time,A.Serial_No) G_MODEL,
            FIRST_VALUE(A.COP_G_MODEL) OVER (PARTITION BY A.ENTRY_G_NO,A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE ORDER BY A.Insert_Time,A.Serial_No) COP_G_MODEL,
            A.TRADE_MODE,A.DUTY_MODE,A.QTY,A.DEC_TOTAL,
            A.SERIAL_NO
            FROM T_DEC_ERP_E_LIST_BILL A WHERE A.HEAD_ID=P_ERP_HEAD_SID and A.STATUS='0'
            ) T
            GROUP BY T.ENTRY_G_NO,T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.G_MODEL,T.COP_G_MODEL,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE
            ) R ORDER BY R.ENTRY_G_NO;
            ELSE
            --不归并 将归并序号清空
            UPDATE T_DEC_ERP_E_LIST_BILL T SET T.ENTRY_G_NO=NULL WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --不归并
            SELECT SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,BILL_GNO ,EMS_NO,BOND_MARK,LIST_SIDS,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM
            (SELECT DECODE( MOD(E.RN,V_ENTRY_LIST_MAX_COUNT),0,V_ENTRY_LIST_MAX_COUNT, MOD(E.RN,V_ENTRY_LIST_MAX_COUNT)) SERIAL_NO,DENSE_RANK() OVER ( ORDER BY E.EMS_NO,E.BOND_MARK,E.TRADE_MODE,E.G_MARK,E.CUT_MODE,ceil(E.RN/V_ENTRY_LIST_MAX_COUNT)) BILL_GNO,E.CODE_T_S,E.CIQ_NO,E.G_NAME,E.G_MODEL,E.UNIT,E.CURR,E.ORIGIN_COUNTRY,E.DESTINATION_COUNTRY,E.QTY,E.DEC_TOTAL,E.DEC_PRICE,
            E.UNIT_1,E.QTY_1,E.UNIT_2,E.QTY_2,E.EMS_NO,E.BOND_MARK,E.G_MARK,LIST_SIDS,E.TRADE_MODE,E.DUTY_MODE,E.DISTRICT_CODE,E.DISTRICT_POST_CODE,E.CUT_MODE FROM
            (
            SELECT ROW_NUMBER() OVER (PARTITION BY R.EMS_NO,R.BOND_MARK,R.TRADE_MODE,R.G_MARK,R.CUT_MODE ORDER BY R.SERIAL_NO) RN,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE,
            R.CODE_T_S,R.CIQ_NO,R.G_NAME,R.G_MARK,R.G_MODEL,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,R.EMS_NO,R.BOND_MARK,R.QTY,ROUND(R.DEC_TOTAL,2) DEC_TOTAL,ROUND(R.DEC_PRICE,4) DEC_PRICE,LIST_SIDS,R.TRADE_MODE,R.DUTY_MODE FROM
            (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,T.QTY_1,T.UNIT_2,T.QTY_2,T.EMS_NO,T.BOND_MARK,DECODE(T.BOND_MARK,'0',T.G_MARK,'') AS  G_MARK,
            T.G_MODEL,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,NVL(T.QTY,0) QTY,NVL(T.DEC_PRICE,0) DEC_PRICE,NVL(T.DEC_TOTAL,0) DEC_TOTAL,T.SID LIST_SIDS,T.SERIAL_NO
            FROM  T_DEC_ERP_E_LIST_BILL T  WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0'
            ) R
            ) E) ORDER BY EMS_NO,BOND_MARK,TRADE_MODE,G_MARK,BILL_GNO,SERIAL_NO;
            END IF;

            --校验非报关状态 ,报关单不生成
            if V_DCLCUS_MARK!='2' then
            /*先循环判断归类的清单是否有单条超过1000的数据*/
            FOR V_INDEX  IN  V_ENTRY_LIST_ARRYS.FIRST .. V_ENTRY_LIST_ARRYS.LAST
            LOOP
            --获取当前数据
            V_ENTRY_LIST_ROW :=V_ENTRY_LIST_ARRYS(V_INDEX);
            /*if(V_ENTRY_LIST_ROW.BOND_MARK='0' and V_ENTRY_LIST_ROW.QTY_1 is not null and V_ENTRY_LIST_ROW.QTY_1&lt;0.001) then
            P_ERR_MSG:='报关单表体中存在法一数量小于0.001数据！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;*/
            if(V_ENTRY_LIST_ROW.QTY_1>=1000000000) then
            P_ERR_MSG:='报关单表体中存在法一数量大于1000000000数据！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;
            if(V_ENTRY_LIST_ROW.BOND_MARK='0' and V_ENTRY_LIST_ROW.UNIT=V_ENTRY_LIST_ROW.UNIT_1 and V_ENTRY_LIST_ROW.QTY!=V_ENTRY_LIST_ROW.QTY_1) then
            P_ERR_MSG:='申报单位与法定单位一致时，申报数量需与法定数量一致！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;
            end loop;
            end if;

            --获取最大清单号
            IF  V_ENTRY_LIST_ARRYS.LAST > 0 THEN
            V_MAX_BILL_GNO:=V_MAX_BILL_GNO_OLD+V_ENTRY_LIST_ARRYS(V_ENTRY_LIST_ARRYS.LAST).BILL_GNO;
            FOR V_INDEX   IN  V_ENTRY_LIST_ARRYS.FIRST .. V_ENTRY_LIST_ARRYS.LAST
            LOOP
            --获取当前数据
            V_ENTRY_LIST_ROW :=V_ENTRY_LIST_ARRYS(V_INDEX);

            --将每次循环的list的SID插入到临时表
            --execute immediate 'truncate table t_list_sid_temp';
            insert into t_list_sid_temp(SID,COLUMN_VALUE) select SYS_GUID(),COLUMN_VALUE from TABLE(F_SPLITCLOB(V_ENTRY_LIST_ROW.LIST_SIDS));

            --处理报关单表体第一条数据时插入对应的报关单表头及清单表头
            IF (V_ENTRY_LIST_ROW.SERIAL_NO = 1) THEN
            BEGIN
            V_BILL_SERIAL_NO:=0;
            V_ENTRY_HEAD_SID:=SYS_GUID();
            V_BILL_HEAD_SID:=SYS_GUID();
            --获取当前清单内部编号
            SELECT V_TD_EMS_LIST_NO ||DECODE(V_MAX_BILL_GNO,1,'','-'||(V_MAX_BILL_GNO_OLD+V_ENTRY_LIST_ROW.BILL_GNO)) INTO  V_EMS_LIST_NO FROM DUAL;

            --校验非报关状态 ,报关单不生成
            if V_DCLCUS_MARK!='2' then
            --插入报关单表头
            INSERT INTO T_DEC_E_ENTRY_HEAD (BILL_HEAD_ID,SID,ERP_HEAD_ID,EMS_LIST_NO,EMS_NO,TRADE_MODE,TRAF_MODE,TRANS_MODE,DECLARE_CODE,DECLARE_NAME,MASTER_CUSTOMS,OVERSEAS_SHIPPER,OVERSEAS_SHIPPER_NAME,OWNER_CODE,OWNER_NAME,
            WAREHOUSE,TRADE_NATION,TRADE_NAME,TRADE_CODE,ENTRY_PORT,DEST_PORT,CUT_MODE,
            HAWB,TRAF_NAME,VOYAGE_NO,TRADE_COUNTRY,I_E_PORT,CONTR_NO,WRAP_TYPE,WRAP_TYPE2,PACK_NUM,NET_WT,GROSS_WT,FEE_MARK,FEE_RATE,FEE_CURR,INSUR_MARK,INSUR_RATE,INSUR_CURR,
            OTHER_MARK,OTHER_RATE,OTHER_CURR,NOTE,PROMISE_ITEMS,LICENSE_NO,DESP_PORT,BOND_MARK,DECLARE_CREDIT_CODE,TRADE_CREDIT_CODE,OWNER_CREDIT_CODE,INSERT_USER,I_E_DATE,INSERT_USER_NAME,Entry_Status,I_E_MARK,DECL_TRNREL,ENTRY_TYPE)
            SELECT V_BILL_HEAD_SID,V_ENTRY_HEAD_SID,T.SID,V_EMS_LIST_NO AS EMS_LIST_NO,V_ENTRY_LIST_ROW.EMS_NO,V_ENTRY_LIST_ROW.TRADE_MODE,T.TRAF_MODE,T.TRANS_MODE,T.DECLARE_CODE,T.DECLARE_NAME,T.MASTER_CUSTOMS,T.OVERSEAS_SHIPPER_AEO,T.OVERSEAS_SHIPPER_NAME,
            T.OWNER_CODE,T.OWNER_NAME,T.WAREHOUSE,T.TRADE_NATION,T.TRADE_NAME,T.TRADE_CODE,T.ENTRY_PORT,T.DEST_PORT,V_ENTRY_LIST_ROW.CUT_MODE,
            case when T.MAWB is not null and T.HAWB is not null then T.MAWB||'_'||T.HAWB when T.MAWB is not null and T.HAWB is null then T.MAWB when T.MAWB is null and T.HAWB is not null then T.HAWB end,
            T.TRAF_NAME,T.VOYAGE_NO,T.TRADE_COUNTRY,T.I_E_PORT,T.CONTR_NO,T.WRAP_TYPE,T.WRAP_TYPE2,T.PACK_NUM,T.NET_WT,T.GROSS_WT,T.FEE_MARK,T.FEE_RATE,T.FEE_CURR,T.INSUR_MARK,T.INSUR_RATE,T.INSUR_CURR,
            T.OTHER_MARK,T.OTHER_RATE,T.OTHER_CURR,T.NOTE,PROMISE_ITEMS,LICENSE_NO,DESP_PORT,V_ENTRY_LIST_ROW.BOND_MARK,T.DECLARE_CREDIT_CODE,T.TRADE_CREDIT_CODE,T.OWNER_CREDIT_CODE,T.INSERT_USER,I_E_DATE,INSERT_USER_NAME,'A0','E',DECL_TRNREL,ENTRY_CLEARANCE_TYPE
            FROM T_DEC_ERP_E_HEAD_N T
            WHERE T.SID=P_ERP_HEAD_SID ;
            end if;

            ---插入报关单追踪
            INSERT INTO T_DEC_E_CUSTOMS_TRACK(SID,ERP_HEAD_ID,HEAD_ID,EMS_LIST_NO,DECLARE_PORT,INSERT_USER,TRADE_CODE)
            SELECT SYS_GUID(),T.SID,V_BILL_HEAD_SID,V_EMS_LIST_NO AS EMS_LIST_NO,T.I_E_PORT,T.INSERT_USER,T.TRADE_CODE FROM T_DEC_ERP_E_HEAD_N T WHERE T.SID=P_ERP_HEAD_SID;

            ---插入清单表头
            INSERT INTO T_DEC_E_BILL_HEAD (SID,HEAD_ID,EMS_LIST_NO,EMS_NO,TRADE_MODE,TRAF_MODE,TRANS_MODE,MASTER_CUSTOMS,OVERSEAS_SHIPPER,OVERSEAS_SHIPPER_NAME,HAWB,
            WAREHOUSE,RECEIVE_CODE,RECEIVE_NAME,TRADE_NATION,TRADE_NAME,TRADE_CODE,ENTRY_PORT,DEST_PORT,CUT_MODE,
            TRAF_NAME,VOYAGE_NO,TRADE_COUNTRY,I_E_PORT,CONTR_NO,LICENSE_NO,WRAP_TYPE,WRAP_TYPE2,PACK_NUM,NET_WT,GROSS_WT,FEE_MARK,FEE_RATE,FEE_CURR,INSUR_MARK,INSUR_RATE,INSUR_CURR,
            OTHER_MARK,OTHER_RATE,OTHER_CURR,NOTE,DESP_PORT,BOND_MARK,G_MARK,I_E_MARK,BILL_LIST_TYPE,DCLCUS_MARK,DCLCUS_TYPE,ENTRY_TYPE,CONFIRM_SPECIAL,CONFIRM_PRICE,CONFIRM_ROYALTIES,DUTY_SELF,MERGE_TYPE,TRADE_CREDIT_CODE,DATA_MARK,REL_EMS_NO,REL_LIST_NO,INSERT_USER,INSERT_USER_NAME,APPLY_NO,DECLARE_CODE,DECLARE_NAME,DECLARE_CREDIT_CODE,AGENT_CODE,AGENT_NAME,AGENT_CREDIT_CODE,REL_ENTRY_RECEIVE_CODE,REL_ENTRY_RECEIVE_NAME,REL_ENTRY_RECEIVE_CREDIT_CODE,CONFIRM_FORMULA_PRICE,CONFIRM_TEMP_PRICE)
            SELECT V_BILL_HEAD_SID,T.SID,V_EMS_LIST_NO AS EMS_LIST_NO,V_ENTRY_LIST_ROW.EMS_NO,V_ENTRY_LIST_ROW.TRADE_MODE,T.TRAF_MODE,T.TRANS_MODE,T.MASTER_CUSTOMS,T.OVERSEAS_SHIPPER_AEO,T.OVERSEAS_SHIPPER_NAME,
            case when T.MAWB is not null and T.HAWB is not null then T.MAWB||'_'||T.HAWB when T.MAWB is not null and T.HAWB is null then T.MAWB when T.MAWB is null and T.HAWB is not null then T.HAWB end,
            T.WAREHOUSE,T.RECEIVE_CODE,T.RECEIVE_NAME,T.TRADE_NATION,T.TRADE_NAME,T.TRADE_CODE,T.ENTRY_PORT,T.DEST_PORT,V_ENTRY_LIST_ROW.CUT_MODE,
            T.TRAF_NAME,T.VOYAGE_NO,T.TRADE_COUNTRY,T.I_E_PORT,T.CONTR_NO,T.LICENSE_NO,T.WRAP_TYPE,T.WRAP_TYPE2,T.PACK_NUM,T.NET_WT,T.GROSS_WT,T.FEE_MARK,T.FEE_RATE,T.FEE_CURR,T.INSUR_MARK,T.INSUR_RATE,T.INSUR_CURR,
            T.OTHER_MARK,T.OTHER_RATE,T.OTHER_CURR,T.NOTE,DESP_PORT,V_ENTRY_LIST_ROW.BOND_MARK,V_ENTRY_LIST_ROW.G_MARK,'E',NVL(T.Bill_List_Type,'0'),T.DCLCUS_MARK,
            case when T.DCLCUS_MARK='2' then null else T.DCLCUS_TYPE end,case when T.DCLCUS_MARK='2' then null else NVL(T.Entry_Type,'2') end,
            DECODE( sign(INSTR( T.PROMISE_ITEMS, '1' )), 1, 'Y', DECODE( sign(INSTR( T.PROMISE_ITEMS, '9' )), 1, 'N', '' )),
            DECODE( sign(INSTR( T.PROMISE_ITEMS, '2' )), 1, 'Y', DECODE( sign(INSTR( T.PROMISE_ITEMS, '8' )), 1, 'N', '' )),
            DECODE( sign(INSTR( T.PROMISE_ITEMS, '3' )), 1, 'Y', DECODE( sign(INSTR( T.PROMISE_ITEMS, '7' )), 1, 'N', '' )),
            DECODE( sign(INSTR( T.PROMISE_ITEMS, '4' )), 1, 'Y', DECODE( sign(INSTR( T.PROMISE_ITEMS, '6' )), 1, 'N', '' )),T.MERGE_TYPE
            ,T.TRADE_CREDIT_CODE,'1',REL_EMS_NO,REL_LIST_NO,INSERT_USER,INSERT_USER_NAME,t.APPLY_NO,
            t.DECLARE_CODE,t.DECLARE_NAME,t.DECLARE_CREDIT_CODE,T.AGENT_CODE,T.AGENT_NAME,T.AGENT_CREDIT_CODE,T.REL_ENTRY_RECEIVE_CODE,T.REL_ENTRY_RECEIVE_NAME,T.REL_ENTRY_RECEIVE_CREDIT_CODE,
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'A' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'Z' )), 1, 'N', '' )),
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'B' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'Y' )), 1, 'N', '' ))
            FROM T_DEC_ERP_E_HEAD_N T
            WHERE T.SID=P_ERP_HEAD_SID;
            END;
            END IF;

            --校验非报关状态 ,报关单不生成
            if V_DCLCUS_MARK!='2' then
            --插入报关单表体
            V_ENTRY_LIST_SID:=SYS_GUID();
            INSERT INTO T_DEC_E_ENTRY_LIST (HEAD_ID,SID,SERIAL_NO, CODE_T_S,CIQ_CODE, G_NAME,G_MODEL,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE,UNIT_1,QTY_1,UNIT_2,QTY_2,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,TRADE_CODE)
            SELECT  V_ENTRY_HEAD_SID,V_ENTRY_LIST_SID,V_ENTRY_LIST_ROW.SERIAL_NO, V_ENTRY_LIST_ROW.CODE_T_S,V_ENTRY_LIST_ROW.CIQ_NO, V_ENTRY_LIST_ROW.G_NAME, V_ENTRY_LIST_ROW.G_MODEL,V_ENTRY_LIST_ROW.UNIT ,V_ENTRY_LIST_ROW.CURR ,V_ENTRY_LIST_ROW.ORIGIN_COUNTRY ,
            V_ENTRY_LIST_ROW.DESTINATION_COUNTRY ,V_ENTRY_LIST_ROW.QTY ,V_ENTRY_LIST_ROW.DEC_TOTAL ,V_ENTRY_LIST_ROW.DEC_PRICE,V_ENTRY_LIST_ROW.UNIT_1,V_ENTRY_LIST_ROW.QTY_1,V_ENTRY_LIST_ROW.UNIT_2,
            DECODE(V_ENTRY_LIST_ROW.UNIT_2,NULL,NULL,V_ENTRY_LIST_ROW.QTY_2),V_ENTRY_LIST_ROW.DUTY_MODE,V_ENTRY_LIST_ROW.DISTRICT_CODE,V_ENTRY_LIST_ROW.DISTRICT_POST_CODE,T.TRADE_CODE FROM T_DEC_ERP_E_HEAD_N T  WHERE T.SID=P_ERP_HEAD_SID ;

            end if;

            BEGIN
            --插入清单表体
            --V_BILL_LIST_SID:=SYS_GUID();
            INSERT INTO T_DEC_E_BILL_LIST (HEAD_ID,SID,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,CIQ_NO,G_NAME,G_MODEL,ORIGIN_COUNTRY,DESTINATION_COUNTRY,CURR,Dec_Price,QTY,UNIT,DEC_TOTAL,QTY_1,UNIT_1,
            QTY_2,UNIT_2,DUTY_MODE,EXG_VERSION,FAC_G_NO,DISTRICT_CODE,DISTRICT_POST_CODE,ENTRY_G_NO,NOTE,NOTE_1,NOTE_2,NOTE_3,Gross_Wt,net_wt,EMS_LIST_NO,COP_G_NAME,COP_G_MODEL,Linked_No,Client_Code,Client_Name,TRADE_CODE)
            SELECT V_BILL_HEAD_SID,E.SID,T.SERIAL_NO,T.G_NO,T.COP_G_NO,T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.G_MODEL,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.CURR,
            CASE WHEN T.BOND_MARK='0' THEN ROUND(T.DEC_PRICE,4) ELSE T.DEC_PRICE END ,T.QTY,T.UNIT,CASE WHEN T.BOND_MARK='0' THEN ROUND(T.DEC_TOTAL,2) ELSE T.DEC_TOTAL END,T.QTY_1,T.UNIT_1,
            T.QTY_2,T.UNIT_2,T.DUTY_MODE,T.EXG_VERSION,T.FAC_G_NO,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,
            V_ENTRY_LIST_ROW.SERIAL_NO,
            T.NOTE,T.NOTE_1,T.NOTE_2,T.NOTE_3,T.Gross_Wt,t.net_wt,
            V_EMS_LIST_NO,T.COP_G_NAME,T.COP_G_MODEL,T.Linked_No,T.Supplier_Code,t.supplier_name,t.TRADE_CODE  FROM T_DEC_ERP_E_LIST_BILL T,t_list_sid_temp E WHERE T.SID=E.COLUMN_VALUE AND T.HEAD_ID= P_ERP_HEAD_SID and T.STATUS='0';

            if V_DCLCUS_MARK!='2' then
            UPDATE T_DEC_ERP_E_RELATION t set t.ems_list_no=V_EMS_LIST_NO,t.bill_sid=V_BILL_HEAD_SID,t.bill_list_sid=(select SID from t_list_sid_temp where COLUMN_VALUE=T.CLIST_SID),t.entry_sid=V_ENTRY_HEAD_SID,t.entry_list_sid=V_ENTRY_LIST_SID where T.CLIST_SID in (SELECT COLUMN_VALUE FROM  t_list_sid_temp ) and T.STATUS='0';
            else
            UPDATE T_DEC_ERP_E_RELATION t set t.ems_list_no=V_EMS_LIST_NO,t.bill_sid=V_BILL_HEAD_SID,t.bill_list_sid=(select SID from t_list_sid_temp where COLUMN_VALUE=T.CLIST_SID) where T.CLIST_SID in (SELECT COLUMN_VALUE FROM  t_list_sid_temp ) and T.STATUS='0';
            end if;

            COMMIT;

            --返回插入行数，并累加值清单列表编号
            V_BILL_SERIAL_NO:=V_BILL_SERIAL_NO+SQL%ROWCOUNT;
            END ;
            END LOOP;
            update T_DEC_ERP_E_LIST_N t set t.BILL_HEAD_ID=(select r.BILL_SID from T_DEC_ERP_E_RELATION r where t.sid=r.list_sid) WHERE T.HEAD_ID=P_ERP_HEAD_SID;
            UPDATE  T_DEC_ERP_E_HEAD_N T SET T.APPR_STATUS='9' WHERE T.SID=P_ERP_HEAD_SID;
            END IF;
            END;
            COMMIT;
            &lt;&lt;END_POINT&gt;&gt;
            NULL;
            EXCEPTION
            WHEN OTHERS THEN
            BEGIN
            ROLLBACK;
            P_IS_SUCC:=0;
            P_ERR_MSG:=SQLCODE||'--'||SQLERRM;
            END;
            end;
        </createProcedure>
    </changeSet>
    <changeSet id="4" author="zyshen">
        <createProcedure>
            CREATE OR REPLACE PROCEDURE P_DEC_I_MERGE_GOLDEN_BILLS(P_ERP_HEAD_SID VARCHAR2,P_IS_SUCC OUT INTEGER,P_ERR_MSG OUT VARCHAR2)
            IS
            TYPE ENTRY_LIST_TEMP IS RECORD
            (
            SERIAL_NO  T_DEC_I_ENTRY_LIST.SERIAL_NO%TYPE,
            CODE_T_S  T_DEC_I_ENTRY_LIST.CODE_T_S%TYPE,
            CIQ_NO  T_DEC_I_ENTRY_LIST.CIQ_CODE%TYPE,
            G_NAME  T_DEC_I_ENTRY_LIST.G_NAME%TYPE,
            G_MODEL    T_DEC_I_ENTRY_LIST.G_MODEL%TYPE,
            G_MARK  VARCHAR2(1),
            UNIT  T_DEC_I_ENTRY_LIST.UNIT%TYPE,
            CURR  T_DEC_I_ENTRY_LIST.CURR%TYPE,
            ORIGIN_COUNTRY  T_DEC_I_ENTRY_LIST.ORIGIN_COUNTRY%TYPE,
            DESTINATION_COUNTRY  T_DEC_I_ENTRY_LIST.DESTINATION_COUNTRY%TYPE,
            QTY  T_DEC_I_ENTRY_LIST.QTY%TYPE,
            DEC_TOTAL  T_DEC_I_ENTRY_LIST.DEC_TOTAL%TYPE,
            DEC_PRICE  T_DEC_I_ENTRY_LIST.DEC_PRICE%TYPE,
            UNIT_1 T_DEC_I_ENTRY_LIST.UNIT_1%TYPE,
            QTY_1 T_DEC_I_ENTRY_LIST.QTY_1%TYPE,
            UNIT_2 T_DEC_I_ENTRY_LIST.UNIT_2%TYPE,
            QTY_2 T_DEC_I_ENTRY_LIST.QTY_2%TYPE,
            BILL_GNO varchar2(80),
            EMS_NO  varchar2(12),
            BOND_MARK  varchar2(1),
            LIST_SIDS  CLOB,
            TRADE_MODE T_DEC_I_ENTRY_HEAD.TRADE_MODE%TYPE,
            DUTY_MODE  T_DEC_I_ENTRY_LIST.DUTY_MODE%TYPE,
            DISTRICT_CODE  T_DEC_I_ENTRY_LIST.DISTRICT_CODE%TYPE,
            DISTRICT_POST_CODE   T_DEC_I_ENTRY_LIST.DISTRICT_POST_CODE%TYPE,
            CUT_MODE varchar2(5)
            );
            TYPE ENTRY_LIST_TYPE  IS TABLE OF ENTRY_LIST_TEMP;
            V_ENTRY_LIST_ARRYS ENTRY_LIST_TYPE;--数据集合
            V_ENTRY_LIST_ROW  ENTRY_LIST_TEMP;--单行数据
            V_ENTRY_LIST_MAX_COUNT INT:=50;--报关单表体最大量
            V_MAX_BILL_GNO INT :=1 ;--最大清单序号
            V_BILL_HEAD_SID VARCHAR2(36);--清单表头oid
            V_ENTRY_HEAD_SID VARCHAR2(36);--报关单表头oid
            V_BILL_SERIAL_NO NUMBER(11):=0;--清单表体流水号
            V_IS_BILLED INT;--是否已生成清单
            V_BODY_COUNT NUMBER(10);--提单表体数据量
            V_START_ENTRY_G_NO NUMBER(19);--开始归并序号
            V_MERGE_TYPE INT ;--归并标识 0：自动归并 1：人工归并
            V_NO_VALID_MERGE_DATA_COUNT NUMBER(10);--无效的人工归并数
            V_TD_EMS_LIST_NO VARCHAR2(100);--提单内部编号
            V_EMS_LIST_NO VARCHAR2(100);--清单内部编号
            V_BILL_TYPE VARCHAR2(1);--清单归并类型
            V_MAX_BILL_GNO_OLD INT :=0 ;--原有最大清单序号
            V_TRADE_CODE VARCHAR2(20);--企业编码
            V_LIST_REQUIRED_FIELD VARCHAR2(10);
            V_TAX_HEAD_SID VARCHAR2(40);
            V_ENTRY_LIST_SID VARCHAR2(40);
            V_BILL_LIST_SID VARCHAR2(40);
            V_ENTRY_G_NO VARCHAR2(100);
            V_EXEMPTS_NO VARCHAR2(20);
            V_DCLCUS_MARK VARCHAR2(1);

            V_ARRIVAL_PORT_DATE DATE;
            V_ARRIVAL_DATE DATE;
            V_DAMAGE_MARK VARCHAR2(5);
            V_DAMAGE_REMARK VARCHAR2(255);
            V_PLATE_NUM VARCHAR2(100);
            V_DELIVERY_PERSON VARCHAR2(20);
            V_LINK_MAN_TEL VARCHAR2(20);
            V_EMSLISTNO_LENGTH NUMBER(10) := 0;
            V_EMSLISTNO_SUB NUMBER(10) := 0;

            BEGIN
            P_IS_SUCC:=1;
            V_LIST_REQUIRED_FIELD:='0';
            /* ---余量校验
            P_DEC_I_QTY_CALC(P_ERP_HEAD_SID,P_IS_SUCC, P_ERR_MSG);
            IF P_IS_SUCC=0 THEN
            GOTO END_POINT;
            END IF;*/

            --获取归并类型,提单内部编号
            SELECT T.MERGE_TYPE,T.EMS_LIST_NO,t.BILL_TYPE,t.TRADE_CODE,t.Exempts_No,t.DCLCUS_MARK,LENGTH(T.EMS_LIST_NO) INTO V_MERGE_TYPE,V_TD_EMS_LIST_NO,V_BILL_TYPE,V_TRADE_CODE,V_EXEMPTS_NO,V_DCLCUS_MARK,V_EMSLISTNO_LENGTH  FROM T_DEC_ERP_I_HEAD_N T WHERE T.SID=P_ERP_HEAD_SID;

            --校验非报关状态 ,强制状态为自动归并，报关单归并项1000项(变通)
            if V_DCLCUS_MARK='2' then
            begin
            V_MERGE_TYPE:=9;
            V_ENTRY_LIST_MAX_COUNT:=1000;
            end;
            end if;

            P_DEC_I_MERGE_BILLS(P_ERP_HEAD_SID,P_IS_SUCC,P_ERR_MSG);
            IF P_IS_SUCC=0 THEN
            GOTO END_POINT;
            END IF;

            --查看表体是否有数据
            SELECT COUNT(1) INTO V_BODY_COUNT  FROM T_DEC_ERP_I_LIST_BILL T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            IF V_BODY_COUNT=0 THEN
            P_ERR_MSG:='请先维护提单表体数据！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;

            ---人工归并判断
            IF V_MERGE_TYPE=1 THEN
            BEGIN
            ---是否含有重复项
            SELECT SUM(NO_VALID_COUNT),wm_concat(ENTRY_G_NO) INTO V_NO_VALID_MERGE_DATA_COUNT,V_ENTRY_G_NO FROM (
            SELECT COUNT(1) NO_VALID_COUNT,R.ENTRY_G_NO FROM (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.BOND_MARK,DECODE(T.BOND_MARK,'0',T.G_MARK,'') AS G_MARK,T.ENTRY_G_NO,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE
            FROM T_DEC_ERP_I_LIST_BILL T  WHERE T.HEAD_ID=P_ERP_HEAD_SID  and T.STATUS='0'
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,DECODE(T.BOND_MARK,'0',T.G_MARK,''),T.BOND_MARK,T.ENTRY_G_NO,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE
            ) R
            GROUP BY  R.ENTRY_G_NO
            HAVING COUNT(1) >1
            );
            IF V_NO_VALID_MERGE_DATA_COUNT>0 THEN
            P_ERR_MSG:=P_ERR_MSG||'报关单归并序号'||V_ENTRY_G_NO||'不符合归并原则！';
            P_IS_SUCC:=0;
            END IF;
            ---开始归并序号
            SELECT NVL(MIN(T.ENTRY_G_NO),0) INTO V_START_ENTRY_G_NO FROM T_DEC_ERP_I_LIST_BILL T WHERE  T.HEAD_ID=P_ERP_HEAD_SID  and T.STATUS='0';
            --是否从从1开始
            IF V_START_ENTRY_G_NO!=1 THEN
            P_ERR_MSG:=P_ERR_MSG||'报关单商品序号不正确，请重新填写！';
            P_IS_SUCC:=0;
            END IF;
            ---是否连续
            SELECT COUNT(1) INTO V_NO_VALID_MERGE_DATA_COUNT FROM (
            SELECT T.ENTRY_G_NO,LEAD(T.ENTRY_G_NO,1,0) OVER (ORDER BY T.ENTRY_G_NO ) NEXT_ENTRY_G_NO,T.COP_G_NO,T.G_NAME FROM  T_DEC_ERP_I_LIST_BILL T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0'
            ) R
            WHERE R.NEXT_ENTRY_G_NO-R.ENTRY_G_NO>1;
            IF V_NO_VALID_MERGE_DATA_COUNT>0 THEN
            P_ERR_MSG:=P_ERR_MSG||'归并序号不连续，请重新归并！';
            P_IS_SUCC:=0;
            END IF;
            IF P_IS_SUCC=0 THEN
            GOTO END_POINT;
            END IF;
            END;
            END IF;
            --获取是否生成清单
            SELECT DECODE(COUNT(1),0,0,1) INTO V_IS_BILLED FROM T_DEC_I_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';

            IF V_IS_BILLED=1 THEN
            ---清除生成的清单,报关单,报关单追中数据

            ---删除报关单追踪数据
            DELETE FROM T_DEC_I_CUSTOMS_TRACK H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0';
            DELETE FROM T_DEC_I_CUSTOMS_TRACK H WHERE H.HEAD_ID in (select SID from T_DEC_I_BILL_HEAD H WHERE H.HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0');
            /* 删除集装箱信息 */
            DELETE FROM t_dc_entry_container h WHERE h.head_id in (select h.sid from t_dec_i_entry_head h WHERE h.erp_head_id = p_erp_head_sid  and H.STATUS='0');
            ---删除税金数据
            DELETE FROM T_TAX_I_LIST T  WHERE LIST_SID in ( SELECT T.sid FROM T_DEC_I_ENTRY_LIST T,T_DEC_I_ENTRY_HEAD H WHERE H.SID=T.HEAD_ID AND H.ERP_HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0');
            DELETE FROM T_TAX_I_HEAD H WHERE HEAD_ID in ( SELECT H.sid FROM T_DEC_I_ENTRY_HEAD H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0');

            --删除报关单数据
            DELETE FROM T_DEC_I_ENTRY_LIST T WHERE EXISTS( SELECT 1 FROM T_DEC_I_ENTRY_HEAD H WHERE H.SID=T.HEAD_ID AND H.ERP_HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0');
            DELETE FROM T_DEC_I_ENTRY_HEAD H WHERE H.ERP_HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0';
            --删除清单数据
            DELETE FROM T_DEC_I_BILL_LIST T WHERE EXISTS( SELECT 1 FROM T_DEC_I_BILL_HEAD H WHERE H.SID=T.HEAD_ID AND H.HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0');
            DELETE FROM T_DEC_I_BILL_HEAD H WHERE H.HEAD_ID=P_ERP_HEAD_SID and H.STATUS='0';


            END IF;
            -- 获取当前提单中未退回状态清单的最大值
            /*            SELECT case when nvl(instr(MAX(T.EMS_LIST_NO), '-', -1, 1),0)= 0 then 0 else nvl(to_number(substr(MAX(T.EMS_LIST_NO),nvl(instr(MAX(T.EMS_LIST_NO), '-', -1, 1),0)+1,length(MAX(T.EMS_LIST_NO)))),0)  end into V_MAX_BILL_GNO_OLD FROM T_DEC_I_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS!='0';*/

            /* 获取当前提单中未退回状态清单的最大值 */
            SELECT NVL(MAX(instr(EMS_LIST_NO,'-',-1)),0) INTO V_EMSLISTNO_SUB
            FROM T_DEC_I_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID AND T.STATUS!='0';

            IF(V_EMSLISTNO_LENGTH&lt;=V_EMSLISTNO_SUB) THEN
            select
            case when max(SUBSTR(EMS_LIST_NO, instr(EMS_LIST_NO,'-',-1)+1)) is null then 0 else max(to_number(SUBSTR(EMS_LIST_NO, instr(EMS_LIST_NO,'-',-1)+1))) end into V_MAX_BILL_GNO_OLD
            FROM T_DEC_I_BILL_HEAD T WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS!='0';
            end if;
            BEGIN  ---保税
            --重置排序
            UPDATE T_DEC_ERP_I_LIST_BILL T SET T.SERIAL_NO=(SELECT R.RN FROM (SELECT ROW_NUMBER() OVER(partition by S.EMS_NO,S.BOND_MARK,S.TRADE_MODE,DECODE(S.BOND_MARK,'0',S.G_MARK,''),S.CUT_MODE ORDER BY S.BILL_G_NO
            ,S.ENTRY_G_NO,S.INSERT_TIME,S.SERIAL_NO,S.BOND_MARK) RN,S.SID FROM T_DEC_ERP_I_LIST_BILL S WHERE S.HEAD_ID=P_ERP_HEAD_SID and S.STATUS='0') R WHERE R.SID=T.SID)
            WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --归并结果信息
            IF V_MERGE_TYPE=0 THEN
            --自动归并 将归并序号清空
            UPDATE T_DEC_ERP_I_LIST_BILL T SET T.ENTRY_G_NO=NULL WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --自动归并
            SELECT SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,BILL_GNO ,EMS_NO,BOND_MARK,LIST_SIDS,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM
            (SELECT DECODE( MOD(E.RN,V_ENTRY_LIST_MAX_COUNT),0,V_ENTRY_LIST_MAX_COUNT, MOD(E.RN,V_ENTRY_LIST_MAX_COUNT)) SERIAL_NO,DENSE_RANK() OVER ( ORDER BY E.EMS_NO,E.BOND_MARK,E.TRADE_MODE,
            E.G_MARK,E.CUT_MODE,CEIL(E.RN/V_ENTRY_LIST_MAX_COUNT)) BILL_GNO,E.CODE_T_S,E.CIQ_NO,E.G_NAME,E.G_MODEL,E.UNIT,E.CURR,E.ORIGIN_COUNTRY,E.DESTINATION_COUNTRY,E.QTY,E.DEC_TOTAL,E.DEC_PRICE,
            E.UNIT_1,E.QTY_1,E.UNIT_2,E.QTY_2,E.EMS_NO,E.BOND_MARK,E.G_MARK,LIST_SIDS,E.TRADE_MODE,E.DUTY_MODE,E.DISTRICT_CODE,E.DISTRICT_POST_CODE,E.CUT_MODE FROM
            (
            SELECT ROW_NUMBER() OVER (PARTITION BY R.EMS_NO,R.BOND_MARK,R.TRADE_MODE,R.G_MARK,R.CUT_MODE ORDER BY R.SERIAL_NO) RN,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE,
            R.CODE_T_S,R.CIQ_NO,R.G_NAME,R.G_MARK,R.G_MODEL,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,R.EMS_NO,R.BOND_MARK,R.QTY,ROUND(R.DEC_TOTAL,2) DEC_TOTAL,
            DECODE(R.QTY,0,0,ROUND(R.DEC_TOTAL/R.QTY,4)) DEC_PRICE,LIST_SIDS,R.TRADE_MODE,R.DUTY_MODE FROM
            (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,SUM(T.QTY_1) QTY_1,T.UNIT_2,SUM(T.QTY_2) QTY_2,T.EMS_NO,T.BOND_MARK,T.G_MARK,
            case when count(distinct t.cop_g_model)>1 then  T.COP_G_MODEL else T.G_MODEL end  G_MODEL,
            T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,
            SUM(NVL(T.QTY,0)) QTY,SUM(NVL(T.DEC_TOTAL,0)) DEC_TOTAL,XMLAGG(XMLELEMENT(E,T.SID|| ',')).EXTRACT('//text()').getclobval() LIST_SIDS,MIN(SERIAL_NO) SERIAL_NO  FROM (
            SELECT A.SID,A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.QTY_1,A.UNIT_2,A.QTY_2,A.EMS_NO,A.BOND_MARK,DECODE(A.BOND_MARK,'0',A.G_MARK,'') AS  G_MARK,
            A.QTY,A.DEC_TOTAL,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE,
            FIRST_VALUE(A.G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),A.BOND_MARK,
            A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) G_MODEL,
            FIRST_VALUE(A.COP_G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) COP_G_MODEL,A.SERIAL_NO
            FROM T_DEC_ERP_I_LIST_BILL A  WHERE A.HEAD_ID=P_ERP_HEAD_SID and A.STATUS='0'
            ) T
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.G_MODEL,T.COP_G_MODEL,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE ) R
            ) E) ORDER BY EMS_NO,BOND_MARK,TRADE_MODE,G_MARK,BILL_GNO,SERIAL_NO ;

            ELSIF V_MERGE_TYPE=4 THEN
            --根据备案序号归并 将归并序号清空
            UPDATE T_DEC_ERP_I_LIST_BILL T SET T.ENTRY_G_NO=NULL WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --自动归并
            SELECT SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,BILL_GNO ,EMS_NO,BOND_MARK,LIST_SIDS,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM
            (SELECT DECODE( MOD(E.RN,V_ENTRY_LIST_MAX_COUNT),0,V_ENTRY_LIST_MAX_COUNT, MOD(E.RN,V_ENTRY_LIST_MAX_COUNT)) SERIAL_NO,DENSE_RANK() OVER ( ORDER BY E.EMS_NO,E.BOND_MARK,E.TRADE_MODE,E.G_MARK,E.CUT_MODE,ceil(E.RN/V_ENTRY_LIST_MAX_COUNT)) BILL_GNO,
            E.CODE_T_S,E.CIQ_NO,E.G_NO,E.G_NAME,E.G_MODEL,E.UNIT,E.CURR,E.ORIGIN_COUNTRY,E.DESTINATION_COUNTRY,E.QTY,E.DEC_TOTAL,E.DEC_PRICE,
            E.UNIT_1,E.QTY_1,E.UNIT_2,E.QTY_2,E.EMS_NO,E.BOND_MARK,E.G_MARK,LIST_SIDS,E.TRADE_MODE,E.DUTY_MODE,E.DISTRICT_CODE,E.DISTRICT_POST_CODE,E.CUT_MODE FROM
            (
            SELECT ROW_NUMBER() OVER (PARTITION BY R.EMS_NO,R.BOND_MARK,R.TRADE_MODE,R.G_MARK,R.CUT_MODE ORDER BY R.SERIAL_NO) RN,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE,
            R.CODE_T_S,R.CIQ_NO,R.G_NO,R.G_NAME,R.G_MARK,R.G_MODEL,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,R.EMS_NO,R.BOND_MARK,R.QTY,ROUND(R.DEC_TOTAL,2) DEC_TOTAL,DECODE(R.QTY,0,0,ROUND(R.DEC_TOTAL/R.QTY,4)) DEC_PRICE,LIST_SIDS,R.TRADE_MODE,R.DUTY_MODE FROM
            (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,SUM(T.QTY_1) QTY_1,T.UNIT_2,SUM(NVL(T.QTY_2,0)) QTY_2,T.EMS_NO,T.BOND_MARK,T.G_MARK,
            case when count(distinct t.cop_g_model)>1 then  T.COP_G_MODEL else T.G_MODEL end  G_MODEL,
            T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,
            SUM(NVL(T.QTY,0)) QTY,SUM(NVL(T.DEC_TOTAL,0)) DEC_TOTAL,
            XMLAGG(XMLELEMENT(E,T.SID|| ',')).EXTRACT('//text()').getclobval() LIST_SIDS,MIN(SERIAL_NO) SERIAL_NO
            FROM (
            SELECT A.SID,A.CODE_T_S,A.CIQ_NO,A.G_NO,A.COP_G_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.QTY_1,A.UNIT_2,A.QTY_2,A.EMS_NO,A.BOND_MARK,DECODE(A.BOND_MARK,'0',A.G_MARK,'') AS  G_MARK,A.QTY,A.DEC_TOTAL,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE,
            FIRST_VALUE(A.G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.G_NO,A.COP_G_NO,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) G_MODEL,
            FIRST_VALUE(A.COP_G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.G_NO,A.COP_G_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) COP_G_MODEL,A.SERIAL_NO
            FROM T_DEC_ERP_I_LIST_BILL A  WHERE A.HEAD_ID=P_ERP_HEAD_SID and A.STATUS='0'
            ) T
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.G_NO,T.COP_G_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.G_MODEL,T.COP_G_MODEL,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE) R
            ) E) ORDER BY EMS_NO,BOND_MARK,TRADE_MODE,G_MARK,BILL_GNO,SERIAL_NO;

            ELSIF V_MERGE_TYPE=5 THEN
            --根据模拟项号归并 将归并序号清空
            UPDATE T_DEC_ERP_I_LIST_BILL T SET T.ENTRY_G_NO=NULL WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --自动归并
            SELECT SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,BILL_GNO ,EMS_NO,BOND_MARK,LIST_SIDS,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM
            (SELECT DECODE( MOD(E.RN,V_ENTRY_LIST_MAX_COUNT),0,V_ENTRY_LIST_MAX_COUNT, MOD(E.RN,V_ENTRY_LIST_MAX_COUNT)) SERIAL_NO,DENSE_RANK() OVER ( ORDER BY E.EMS_NO,E.BOND_MARK,E.TRADE_MODE,E.G_MARK,E.CUT_MODE,ceil(E.RN/V_ENTRY_LIST_MAX_COUNT)) BILL_GNO,
            E.CODE_T_S,E.CIQ_NO,E.ITEM_NO,E.G_NAME,E.G_MODEL,E.UNIT,E.CURR,E.ORIGIN_COUNTRY,E.DESTINATION_COUNTRY,E.QTY,E.DEC_TOTAL,E.DEC_PRICE,
            E.UNIT_1,E.QTY_1,E.UNIT_2,E.QTY_2,E.EMS_NO,E.BOND_MARK,E.G_MARK,LIST_SIDS,E.TRADE_MODE,E.DUTY_MODE,E.DISTRICT_CODE,E.DISTRICT_POST_CODE,E.CUT_MODE FROM
            (
            SELECT ROW_NUMBER() OVER (PARTITION BY R.EMS_NO,R.BOND_MARK,R.TRADE_MODE,R.G_MARK,R.CUT_MODE ORDER BY R.SERIAL_NO) RN,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE,
            R.CODE_T_S,R.CIQ_NO,R.ITEM_NO,R.G_NAME,R.G_MARK,R.G_MODEL,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,R.EMS_NO,R.BOND_MARK,R.QTY,ROUND(R.DEC_TOTAL,2) DEC_TOTAL,DECODE(R.QTY,0,0,ROUND(R.DEC_TOTAL/R.QTY,4)) DEC_PRICE,LIST_SIDS,R.TRADE_MODE,R.DUTY_MODE FROM
            (
            SELECT T.CODE_T_S,T.CIQ_NO,T.ITEM_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,SUM(T.QTY_1) QTY_1,T.UNIT_2,SUM(NVL(T.QTY_2,0)) QTY_2,T.EMS_NO,T.BOND_MARK,T.G_MARK,
            case when count(distinct t.cop_g_model)>1 then  T.COP_G_MODEL else T.G_MODEL end  G_MODEL,
            T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,
            SUM(NVL(T.QTY,0)) QTY,SUM(NVL(T.DEC_TOTAL,0)) DEC_TOTAL,
            XMLAGG(XMLELEMENT(E,T.SID|| ',')).EXTRACT('//text()').getclobval() LIST_SIDS,MIN(SERIAL_NO) SERIAL_NO
            FROM (
            SELECT A.SID,A.CODE_T_S,A.CIQ_NO,A.ITEM_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.QTY_1,A.UNIT_2,A.QTY_2,A.EMS_NO,A.BOND_MARK,DECODE(A.BOND_MARK,'0',A.G_MARK,'') AS  G_MARK,A.QTY,A.DEC_TOTAL,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE,
            FIRST_VALUE(A.G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.ITEM_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) G_MODEL,
            FIRST_VALUE(A.COP_G_MODEL) OVER (PARTITION BY A.CODE_T_S,A.CIQ_NO,A.ITEM_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),
            A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE  ORDER BY A.INSERT_TIME,A.SERIAL_NO) COP_G_MODEL,A.SERIAL_NO
            FROM T_DEC_ERP_I_LIST_BILL A  WHERE A.HEAD_ID=P_ERP_HEAD_SID and A.STATUS='0'
            ) T
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.ITEM_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.G_MODEL,T.COP_G_MODEL,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE) R
            ) E) ORDER BY EMS_NO,BOND_MARK,TRADE_MODE,G_MARK,BILL_GNO,SERIAL_NO;

            ELSIF V_MERGE_TYPE=3 THEN
            --自动归并 将归并序号清空
            UPDATE T_DEC_ERP_I_LIST_BILL T SET T.ENTRY_G_NO=NULL WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --自动归并
            SELECT SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,BILL_GNO ,EMS_NO,BOND_MARK,LIST_SIDS,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM
            (SELECT DECODE( MOD(E.RN,V_ENTRY_LIST_MAX_COUNT),0,V_ENTRY_LIST_MAX_COUNT, MOD(E.RN,V_ENTRY_LIST_MAX_COUNT)) SERIAL_NO,DENSE_RANK() OVER ( ORDER BY E.EMS_NO,E.BOND_MARK,E.TRADE_MODE,E.G_MARK,E.CUT_MODE,CEIL(E.RN/V_ENTRY_LIST_MAX_COUNT)) BILL_GNO,E.CODE_T_S,E.CIQ_NO,E.G_NAME,E.G_MODEL,E.UNIT,E.CURR,E.ORIGIN_COUNTRY,E.DESTINATION_COUNTRY,E.QTY,E.DEC_TOTAL,E.DEC_PRICE,
            E.UNIT_1,E.QTY_1,E.UNIT_2,E.QTY_2,E.EMS_NO,E.BOND_MARK,E.G_MARK,LIST_SIDS,E.TRADE_MODE,E.DUTY_MODE,E.DISTRICT_CODE,E.DISTRICT_POST_CODE,E.CUT_MODE FROM
            (
            SELECT ROW_NUMBER() OVER (PARTITION BY R.EMS_NO,R.BOND_MARK,R.TRADE_MODE,R.G_MARK,R.CUT_MODE ORDER BY R.SERIAL_NO) RN,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE,
            R.CODE_T_S,R.CIQ_NO,R.G_NAME,R.G_MARK,R.G_MODEL,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,R.EMS_NO,R.BOND_MARK,R.QTY,ROUND(R.DEC_TOTAL,2) DEC_TOTAL,DECODE(R.QTY,0,0,ROUND(R.DEC_TOTAL/R.QTY,4)) DEC_PRICE,LIST_SIDS,R.TRADE_MODE,R.DUTY_MODE FROM
            (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,SUM(T.QTY_1) QTY_1,T.UNIT_2,SUM(T.QTY_2) QTY_2,T.EMS_NO,T.BOND_MARK,T.G_MARK,T.G_MODEL,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,
            SUM(NVL(T.QTY,0)) QTY,SUM(NVL(T.DEC_TOTAL,0)) DEC_TOTAL,XMLAGG(XMLELEMENT(E,T.SID|| ',')).EXTRACT('//text()').getclobval() LIST_SIDS,MIN(SERIAL_NO) SERIAL_NO  FROM (
            SELECT A.SID,A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.QTY_1,A.UNIT_2,A.QTY_2,A.EMS_NO,A.BOND_MARK,DECODE(A.BOND_MARK,'0',A.G_MARK,'') AS  G_MARK,A.QTY,A.DEC_TOTAL,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE,
            A.G_MODEL,A.SERIAL_NO
            FROM T_DEC_ERP_I_LIST_BILL A  WHERE A.HEAD_ID=P_ERP_HEAD_SID and A.STATUS='0'
            ) T
            GROUP BY T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.G_MODEL,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE ) R
            ) E) ORDER BY EMS_NO,BOND_MARK,TRADE_MODE,G_MARK,BILL_GNO,SERIAL_NO ;

            ELSIF V_MERGE_TYPE=1 THEN
            ---人工归并
            SELECT R.ENTRY_G_NO,R.CODE_T_S,R.CIQ_NO,R.G_NAME,R.G_MODEL,R.G_MARK,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY ,R.QTY ,ROUND(R.DEC_TOTAL,2) DEC_TOTAL ,DECODE(R.QTY,0,0,ROUND(R.DEC_TOTAL/R.QTY,4)) DEC_PRICE,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,1 AS  BILL_GNO,R.EMS_NO,R.BOND_MARK,R.LIST_SIDS
            ,R.TRADE_MODE,R.DUTY_MODE,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM (
            SELECT T.ENTRY_G_NO,T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,SUM(T.QTY_1) QTY_1,T.UNIT_2,SUM(NVL(T.QTY_2,0)) QTY_2,T.EMS_NO,T.BOND_MARK,T.G_MARK,
            case when count(distinct t.cop_g_model)>1 then  T.COP_G_MODEL else T.G_MODEL end  G_MODEL,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,
            SUM(NVL(T.QTY,0)) QTY,SUM(NVL(T.DEC_TOTAL,0)) DEC_TOTAL,XMLAGG(XMLELEMENT(E,T.SID|| ',')).EXTRACT('//text()').getclobval() LIST_SIDS ,MIN(SERIAL_NO) SERIAL_NO
            FROM (
            SELECT
            A.SID,A.ENTRY_G_NO,A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.QTY_1,A.UNIT_2,A.QTY_2,A.EMS_NO,A.BOND_MARK,DECODE(A.BOND_MARK,'0',A.G_MARK,'') AS  G_MARK,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE,
            FIRST_VALUE(A.G_MODEL) OVER (PARTITION BY A.ENTRY_G_NO,A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE ORDER BY A.Insert_Time,A.Serial_No) G_MODEL,
            FIRST_VALUE(A.COP_G_MODEL) OVER (PARTITION BY A.ENTRY_G_NO,A.CODE_T_S,A.CIQ_NO,A.G_NAME,A.UNIT,A.CURR,A.ORIGIN_COUNTRY,A.DESTINATION_COUNTRY,A.UNIT_1,A.UNIT_2,A.EMS_NO,DECODE(A.BOND_MARK,'0',A.G_MARK,''),A.BOND_MARK,A.TRADE_MODE,A.DUTY_MODE,A.DISTRICT_CODE,A.DISTRICT_POST_CODE,A.CUT_MODE ORDER BY A.Insert_Time,A.Serial_No) COP_G_MODEL,
            A.TRADE_MODE,A.DUTY_MODE,A.QTY,A.DEC_TOTAL,
            A.SERIAL_NO
            FROM T_DEC_ERP_I_LIST_BILL A WHERE A.HEAD_ID=P_ERP_HEAD_SID and A.STATUS='0'
            ) T
            GROUP BY T.ENTRY_G_NO,T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.EMS_NO,T.UNIT_1,T.UNIT_2,T.G_MARK,T.G_MODEL,T.COP_G_MODEL,T.BOND_MARK,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE
            ) R ORDER BY R.ENTRY_G_NO;
            ELSE
            --不归并 将归并序号清空
            UPDATE T_DEC_ERP_I_LIST_BILL T SET T.ENTRY_G_NO=NULL WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0';
            --不归并
            SELECT SERIAL_NO, CODE_T_S,CIQ_NO, G_NAME,G_MODEL,G_MARK,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE ,UNIT_1,QTY_1,UNIT_2,QTY_2,BILL_GNO ,EMS_NO,BOND_MARK,LIST_SIDS,TRADE_MODE,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,CUT_MODE BULK COLLECT INTO V_ENTRY_LIST_ARRYS FROM
            (SELECT DECODE( MOD(E.RN,V_ENTRY_LIST_MAX_COUNT),0,V_ENTRY_LIST_MAX_COUNT, MOD(E.RN,V_ENTRY_LIST_MAX_COUNT)) SERIAL_NO,DENSE_RANK() OVER ( ORDER BY E.EMS_NO,E.BOND_MARK,E.TRADE_MODE,E.G_MARK,E.CUT_MODE,CEIL(E.RN/V_ENTRY_LIST_MAX_COUNT)) BILL_GNO,E.CODE_T_S,E.CIQ_NO,E.G_NAME,E.G_MODEL,E.UNIT,E.CURR,E.ORIGIN_COUNTRY,E.DESTINATION_COUNTRY,E.QTY,E.DEC_TOTAL,E.DEC_PRICE,
            E.UNIT_1,E.QTY_1,E.UNIT_2,E.QTY_2,E.EMS_NO,E.BOND_MARK,E.G_MARK,LIST_SIDS,E.TRADE_MODE,E.DUTY_MODE,E.DISTRICT_CODE,E.DISTRICT_POST_CODE,E.CUT_MODE FROM
            (
            SELECT ROW_NUMBER() OVER (PARTITION BY R.EMS_NO,R.BOND_MARK,R.TRADE_MODE,R.G_MARK,R.CUT_MODE ORDER BY R.SERIAL_NO) RN,R.DISTRICT_CODE,R.DISTRICT_POST_CODE,R.CUT_MODE,
            R.CODE_T_S,R.CIQ_NO,R.G_NAME,R.G_MARK,R.G_MODEL,R.UNIT,R.CURR,R.ORIGIN_COUNTRY,R.DESTINATION_COUNTRY,R.UNIT_1,R.QTY_1,R.UNIT_2,R.QTY_2,R.EMS_NO,R.BOND_MARK,R.QTY,ROUND(R.DEC_TOTAL,2) DEC_TOTAL,ROUND(R.DEC_PRICE,4) DEC_PRICE,LIST_SIDS,R.TRADE_MODE,R.DUTY_MODE FROM
            (
            SELECT T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.UNIT,T.CURR,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.UNIT_1,T.QTY_1 QTY_1,T.UNIT_2,T.QTY_2,T.EMS_NO,T.BOND_MARK,DECODE(T.BOND_MARK,'0',T.G_MARK,'') AS  G_MARK,T.G_MODEL,T.TRADE_MODE,T.DUTY_MODE,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,T.CUT_MODE,
            NVL(T.QTY,0) QTY,NVL(T.DEC_PRICE,0) DEC_PRICE,NVL(T.DEC_TOTAL,0) DEC_TOTAL,T.SID LIST_SIDS,T.SERIAL_NO
            FROM T_DEC_ERP_I_LIST_BILL T  WHERE T.HEAD_ID=P_ERP_HEAD_SID and T.STATUS='0'
            ) R
            ) E) ORDER BY EMS_NO,BOND_MARK,TRADE_MODE,G_MARK,BILL_GNO,SERIAL_NO ;
            END IF;

            --校验非报关状态 ,报关单不生成
            if V_DCLCUS_MARK!='2' then
            /*先循环判断归类的清单是否有单条超过1000的数据*/
            FOR V_INDEX  IN  V_ENTRY_LIST_ARRYS.FIRST .. V_ENTRY_LIST_ARRYS.LAST
            LOOP
            --获取当前数据
            V_ENTRY_LIST_ROW :=V_ENTRY_LIST_ARRYS(V_INDEX);
            /*if(V_ENTRY_LIST_ROW.BOND_MARK='0' and  V_ENTRY_LIST_ROW.QTY_1 is not null and V_ENTRY_LIST_ROW.QTY_1&lt;0.001) then
            P_ERR_MSG:='报关单表体中存在法一数量小于0.001数据！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;*/
            if(V_ENTRY_LIST_ROW.QTY_1>=1000000000) then
            P_ERR_MSG:='报关单表体中存在法一数量大于1000000000数据！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;
            if(V_ENTRY_LIST_ROW.BOND_MARK='0' and V_ENTRY_LIST_ROW.UNIT=V_ENTRY_LIST_ROW.UNIT_1 and V_ENTRY_LIST_ROW.QTY!=V_ENTRY_LIST_ROW.QTY_1) then
            P_ERR_MSG:='申报单位与法定单位一致时，申报数量需与法定数量一致！';
            P_IS_SUCC:=0;
            GOTO END_POINT;
            END IF;
            end loop;
            end if;


            --获取最大清单号
            IF  V_ENTRY_LIST_ARRYS.LAST > 0 THEN
            V_MAX_BILL_GNO:=V_MAX_BILL_GNO_OLD+V_ENTRY_LIST_ARRYS(V_ENTRY_LIST_ARRYS.LAST).BILL_GNO;
            FOR V_INDEX  IN  V_ENTRY_LIST_ARRYS.FIRST .. V_ENTRY_LIST_ARRYS.LAST
            LOOP
            --获取当前数据
            V_ENTRY_LIST_ROW :=V_ENTRY_LIST_ARRYS(V_INDEX);

            --将每次循环的list的SID插入到临时表
            --execute immediate 'truncate table t_list_sid_temp';
            insert into t_list_sid_temp(SID,COLUMN_VALUE) select SYS_GUID(),COLUMN_VALUE from TABLE(F_SPLITCLOB(V_ENTRY_LIST_ROW.LIST_SIDS));


            --处理报关单表体第一条数据时插入对应的报关单表头及清单表头
            IF (V_ENTRY_LIST_ROW.SERIAL_NO = 1) THEN
            BEGIN
            V_BILL_SERIAL_NO:=0;
            V_ENTRY_HEAD_SID:=SYS_GUID();
            V_BILL_HEAD_SID:=SYS_GUID();
            --获取当前清单内部编号
            SELECT V_TD_EMS_LIST_NO ||DECODE(V_MAX_BILL_GNO,1,'','-'||(V_MAX_BILL_GNO_OLD+V_ENTRY_LIST_ROW.BILL_GNO)) INTO  V_EMS_LIST_NO FROM DUAL;

            --校验非报关状态 ,报关单不生成
            if V_DCLCUS_MARK!='2' then
            --插入报关单表头
            INSERT INTO T_DEC_I_ENTRY_HEAD (BILL_HEAD_ID,SID,ERP_HEAD_ID,EMS_LIST_NO,EMS_NO,TRADE_MODE,TRAF_MODE,TRANS_MODE,DECLARE_CODE,DECLARE_NAME,MASTER_CUSTOMS,OVERSEAS_SHIPPER,OVERSEAS_SHIPPER_NAME,OWNER_CODE,OWNER_NAME,
            WAREHOUSE,TRADE_NATION,TRADE_NAME,TRADE_CODE,ENTRY_PORT,DEST_PORT,CUT_MODE,
            HAWB,TRAF_NAME,VOYAGE_NO,TRADE_COUNTRY,I_E_PORT,CONTR_NO,WRAP_TYPE,WRAP_TYPE2,PACK_NUM,NET_WT,GROSS_WT,FEE_MARK,FEE_RATE,FEE_CURR,INSUR_MARK,INSUR_RATE,INSUR_CURR,
            OTHER_MARK,OTHER_RATE,OTHER_CURR,NOTE,PROMISE_ITEMS,LICENSE_NO,DESP_PORT,BOND_MARK,DECLARE_CREDIT_CODE,TRADE_CREDIT_CODE,OWNER_CREDIT_CODE,INSERT_USER,I_E_DATE,INSERT_USER_NAME,ENTRY_STATUS,I_E_MARK,CARD_MARK,CHECK_MARK,TAX_MARK,ENTRY_TYPE,DECL_TRNREL)
            SELECT V_BILL_HEAD_SID,V_ENTRY_HEAD_SID,T.SID,V_EMS_LIST_NO AS EMS_LIST_NO,NVL(V_ENTRY_LIST_ROW.EMS_NO,V_EXEMPTS_NO),V_ENTRY_LIST_ROW.TRADE_MODE,T.TRAF_MODE,T.TRANS_MODE,T.DECLARE_CODE,T.DECLARE_NAME,T.MASTER_CUSTOMS,T.OVERSEAS_SHIPPER_AEO,T.OVERSEAS_SHIPPER_NAME,
            T.OWNER_CODE,T.OWNER_NAME,T.WAREHOUSE,T.TRADE_NATION,T.TRADE_NAME,T.TRADE_CODE,T.ENTRY_PORT,T.DEST_PORT,V_ENTRY_LIST_ROW.CUT_MODE,
            case when T.MAWB is not null and T.HAWB is not null then T.MAWB||'_'||T.HAWB when T.MAWB is not null and T.HAWB is null then T.MAWB when T.MAWB is null and T.HAWB is not null then T.HAWB end,
            T.TRAF_NAME,T.VOYAGE_NO,T.TRADE_COUNTRY,T.I_E_PORT,T.CONTR_NO,T.WRAP_TYPE,T.WRAP_TYPE2,T.PACK_NUM,T.NET_WT,T.GROSS_WT,T.FEE_MARK,T.FEE_RATE,T.FEE_CURR,T.INSUR_MARK,T.INSUR_RATE,T.INSUR_CURR,
            T.OTHER_MARK,T.OTHER_RATE,T.OTHER_CURR,T.NOTE,PROMISE_ITEMS,LICENSE_NO,DESP_PORT,V_ENTRY_LIST_ROW.BOND_MARK,T.DECLARE_CREDIT_CODE,T.TRADE_CREDIT_CODE,T.OWNER_CREDIT_CODE,T.INSERT_USER,I_E_DATE,INSERT_USER_NAME,'A0','I',CARD_MARK,CHECK_MARK,TAX_MARK,ENTRY_CLEARANCE_TYPE,DECL_TRNREL
            FROM T_DEC_ERP_I_HEAD_N T
            WHERE T.SID=P_ERP_HEAD_SID ;
            end if;

            ---插入报关单追踪
            INSERT INTO T_DEC_I_CUSTOMS_TRACK(SID,ERP_HEAD_ID,HEAD_ID,EMS_LIST_NO,DECLARE_PORT,INSERT_USER,TRADE_CODE)
            SELECT SYS_GUID(),T.SID,V_BILL_HEAD_SID,V_EMS_LIST_NO AS EMS_LIST_NO,T.I_E_PORT,T.INSERT_USER,T.TRADE_CODE FROM T_DEC_ERP_I_HEAD_N T WHERE T.SID=P_ERP_HEAD_SID;

            if (instr('0245,0844,0845,0644,0446,9700,0110,0815,1300,1500,1523,1616,2025,2225,2600,2700,3010,3039,3100,3339,4500,4539,4561',V_ENTRY_LIST_ROW.TRADE_MODE)>0) then
            begin
            ---插入税金
            V_TAX_HEAD_SID:=SYS_GUID();
            INSERT INTO T_TAX_I_HEAD (SID,HEAD_ID,TRADE_CODE,INSERT_USER,INSERT_TIME,INSERT_USER_NAME)
            SELECT V_TAX_HEAD_SID,V_ENTRY_HEAD_SID,T.TRADE_CODE,T.INSERT_USER,T.INSERT_TIME,T.INSERT_USER_NAME FROM T_DEC_ERP_I_HEAD_N T WHERE T.SID=P_ERP_HEAD_SID;
            end;
            end if;


            ---插入清单表头
            INSERT INTO T_DEC_I_BILL_HEAD (SID,HEAD_ID,EMS_LIST_NO,EMS_NO,TRADE_MODE,TRAF_MODE,TRANS_MODE,MASTER_CUSTOMS,OVERSEAS_SHIPPER,OVERSEAS_SHIPPER_NAME,HAWB,
            WAREHOUSE,RECEIVE_CODE,RECEIVE_NAME,TRADE_NATION,TRADE_NAME,TRADE_CODE,ENTRY_PORT,DEST_PORT,CUT_MODE,
            TRAF_NAME,VOYAGE_NO,TRADE_COUNTRY,I_E_PORT,CONTR_NO,LICENSE_NO,WRAP_TYPE,WRAP_TYPE2,PACK_NUM,NET_WT,GROSS_WT,FEE_MARK,FEE_RATE,FEE_CURR,INSUR_MARK,INSUR_RATE,INSUR_CURR,
            OTHER_MARK,OTHER_RATE,OTHER_CURR,NOTE,DESP_PORT,BOND_MARK,G_MARK,I_E_MARK,BILL_LIST_TYPE,DCLCUS_MARK,DCLCUS_TYPE,ENTRY_TYPE,CONFIRM_SPECIAL,CONFIRM_PRICE,CONFIRM_ROYALTIES,DUTY_SELF,MERGE_TYPE,TRADE_CREDIT_CODE,DATA_MARK,REL_EMS_NO,REL_LIST_NO,INSERT_USER,INSERT_USER_NAME,APPLY_NO,DECLARE_CODE,DECLARE_NAME,DECLARE_CREDIT_CODE,AGENT_CODE,AGENT_NAME,AGENT_CREDIT_CODE,REL_ENTRY_RECEIVE_CODE,REL_ENTRY_RECEIVE_NAME,REL_ENTRY_RECEIVE_CREDIT_CODE,CONFIRM_FORMULA_PRICE,CONFIRM_TEMP_PRICE)
            SELECT V_BILL_HEAD_SID,T.SID,V_EMS_LIST_NO AS EMS_LIST_NO,NVL(V_ENTRY_LIST_ROW.EMS_NO,V_EXEMPTS_NO),V_ENTRY_LIST_ROW.TRADE_MODE,T.TRAF_MODE,T.TRANS_MODE,T.MASTER_CUSTOMS,T.OVERSEAS_SHIPPER_AEO,T.OVERSEAS_SHIPPER_NAME,
            case when T.MAWB is not null and T.HAWB is not null then T.MAWB||'_'||T.HAWB when T.MAWB is not null and T.HAWB is null then T.MAWB when T.MAWB is null and T.HAWB is not null then T.HAWB end,
            T.WAREHOUSE,T.RECEIVE_CODE,T.RECEIVE_NAME,T.TRADE_NATION,T.TRADE_NAME,T.TRADE_CODE,T.ENTRY_PORT,T.DEST_PORT,V_ENTRY_LIST_ROW.CUT_MODE,
            T.TRAF_NAME,T.VOYAGE_NO,T.TRADE_COUNTRY,T.I_E_PORT,T.CONTR_NO,T.LICENSE_NO,T.WRAP_TYPE,T.WRAP_TYPE2,T.PACK_NUM,T.NET_WT,T.GROSS_WT,T.FEE_MARK,T.FEE_RATE,T.FEE_CURR,T.INSUR_MARK,T.INSUR_RATE,T.INSUR_CURR,
            T.OTHER_MARK,T.OTHER_RATE,T.OTHER_CURR,T.NOTE,DESP_PORT,V_ENTRY_LIST_ROW.BOND_MARK,V_ENTRY_LIST_ROW.G_MARK,'I',NVL(T.Bill_List_Type,'0'),T.DCLCUS_MARK,
            case when T.DCLCUS_MARK='2' then null else T.DCLCUS_TYPE end,case when T.DCLCUS_MARK='2' then null else NVL(T.Entry_Type,'1') end,
            DECODE( sign(INSTR( T.PROMISE_ITEMS, '1' )), 1, 'Y', DECODE( sign(INSTR( T.PROMISE_ITEMS, '9' )), 1, 'N', '' )),
            DECODE( sign(INSTR( T.PROMISE_ITEMS, '2' )), 1, 'Y', DECODE( sign(INSTR( T.PROMISE_ITEMS, '8' )), 1, 'N', '' )),
            DECODE( sign(INSTR( T.PROMISE_ITEMS, '3' )), 1, 'Y', DECODE( sign(INSTR( T.PROMISE_ITEMS, '7' )), 1, 'N', '' )),
            DECODE( sign(INSTR( T.PROMISE_ITEMS, '4' )), 1, 'Y', DECODE( sign(INSTR( T.PROMISE_ITEMS, '6' )), 1, 'N', '' )),T.MERGE_TYPE
            ,T.TRADE_CREDIT_CODE,'1',REL_EMS_NO,REL_LIST_NO,INSERT_USER,INSERT_USER_NAME,t.APPLY_NO,
            t.DECLARE_CODE,t.DECLARE_NAME,t.DECLARE_CREDIT_CODE,T.AGENT_CODE,T.AGENT_NAME,T.AGENT_CREDIT_CODE,T.REL_ENTRY_RECEIVE_CODE,T.REL_ENTRY_RECEIVE_NAME,T.REL_ENTRY_RECEIVE_CREDIT_CODE,
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'A' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'Z' )), 1, 'N', '' )),
            DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'B' )), 1, 'Y', DECODE( SIGN(INSTR( T.PROMISE_ITEMS, 'Y' )), 1, 'N', '' ))
            FROM T_DEC_ERP_I_HEAD_N T
            WHERE T.SID=P_ERP_HEAD_SID;
            END;
            END IF;

            --校验非报关状态 ,报关单不生成
            if V_DCLCUS_MARK!='2' then
            --插入报关单表体
            V_ENTRY_LIST_SID:=SYS_GUID();
            INSERT INTO T_DEC_I_ENTRY_LIST (HEAD_ID,SID,SERIAL_NO, CODE_T_S, CIQ_CODE,G_NAME,G_MODEL,UNIT ,CURR ,ORIGIN_COUNTRY ,DESTINATION_COUNTRY ,QTY ,DEC_TOTAL ,DEC_PRICE,UNIT_1,QTY_1,UNIT_2,QTY_2,DUTY_MODE,DISTRICT_CODE,DISTRICT_POST_CODE,TRADE_CODE)
            SELECT  V_ENTRY_HEAD_SID,V_ENTRY_LIST_SID,V_ENTRY_LIST_ROW.SERIAL_NO, V_ENTRY_LIST_ROW.CODE_T_S, V_ENTRY_LIST_ROW.CIQ_NO,V_ENTRY_LIST_ROW.G_NAME, V_ENTRY_LIST_ROW.G_MODEL,V_ENTRY_LIST_ROW.UNIT ,V_ENTRY_LIST_ROW.CURR ,V_ENTRY_LIST_ROW.ORIGIN_COUNTRY ,
            V_ENTRY_LIST_ROW.DESTINATION_COUNTRY ,V_ENTRY_LIST_ROW.QTY ,V_ENTRY_LIST_ROW.DEC_TOTAL ,V_ENTRY_LIST_ROW.DEC_PRICE,V_ENTRY_LIST_ROW.UNIT_1,V_ENTRY_LIST_ROW.QTY_1,V_ENTRY_LIST_ROW.UNIT_2,
            DECODE(V_ENTRY_LIST_ROW.UNIT_2,NULL,NULL,V_ENTRY_LIST_ROW.QTY_2),V_ENTRY_LIST_ROW.DUTY_MODE,V_ENTRY_LIST_ROW.DISTRICT_CODE,V_ENTRY_LIST_ROW.DISTRICT_POST_CODE,T.TRADE_CODE FROM T_DEC_ERP_I_HEAD_N T WHERE T.SID=P_ERP_HEAD_SID;
            end if;

            if (instr('0245,0844,0845,0644,0446,9700,0110,0815,1300,1500,1523,1616,2025,2225,2600,2700,3010,3039,3100,3339,4500,4539,4561',V_ENTRY_LIST_ROW.TRADE_MODE)>0) then
            begin
            ---插入税金
            INSERT INTO T_TAX_I_LIST (SID,HEAD_ID,LIST_SID,TRADE_CODE,INSERT_USER,INSERT_TIME,INSERT_USER_NAME)
            SELECT SYS_GUID(), V_TAX_HEAD_SID,V_ENTRY_LIST_SID,T.TRADE_CODE,T.INSERT_USER,T.INSERT_TIME,T.INSERT_USER_NAME FROM T_DEC_ERP_I_HEAD_N T WHERE T.SID=P_ERP_HEAD_SID;
            end;
            end if;


            BEGIN
            --插入清单表体
            V_BILL_LIST_SID:=SYS_GUID();
            INSERT INTO T_DEC_I_BILL_LIST (HEAD_ID,SID,SERIAL_NO,G_NO,COP_G_NO,CODE_T_S,CIQ_NO,G_NAME,G_MODEL,ORIGIN_COUNTRY,DESTINATION_COUNTRY,CURR,Dec_Price,QTY,UNIT,DEC_TOTAL,QTY_1,UNIT_1,
            QTY_2,UNIT_2,DUTY_MODE,EXG_VERSION,FAC_G_NO,DISTRICT_CODE,DISTRICT_POST_CODE,ENTRY_G_NO,NOTE,NOTE_1,NOTE_2,NOTE_3,Gross_Wt,net_wt,EMS_LIST_NO,Cop_g_Name,Cop_g_Model,Linked_No,Supplier_Code,Supplier_Name,TRADE_CODE)
            SELECT V_BILL_HEAD_SID,E.SID,T.SERIAL_NO,T.G_NO,T.COP_G_NO,T.CODE_T_S,T.CIQ_NO,T.G_NAME,T.G_MODEL,T.ORIGIN_COUNTRY,T.DESTINATION_COUNTRY,T.CURR,
            CASE WHEN T.BOND_MARK='0' THEN ROUND(T.DEC_PRICE,4) ELSE T.DEC_PRICE END ,T.QTY,T.UNIT,CASE WHEN T.BOND_MARK='0' THEN ROUND(T.DEC_TOTAL,2) ELSE T.DEC_TOTAL END,T.QTY_1,T.UNIT_1,
            T.QTY_2,T.UNIT_2,T.DUTY_MODE,T.EXG_VERSION,T.FAC_G_NO,T.DISTRICT_CODE,T.DISTRICT_POST_CODE,
            V_ENTRY_LIST_ROW.SERIAL_NO,
            T.NOTE,T.NOTE_1,T.NOTE_2,T.NOTE_3,T.Gross_Wt,t.net_wt,
            V_EMS_LIST_NO,T.COP_G_NAME,T.COP_G_MODEL,T.Linked_No,T.Supplier_Code,T.Supplier_Name,T.TRADE_CODE FROM T_DEC_ERP_I_LIST_BILL T,t_list_sid_temp E WHERE T.SID=E.COLUMN_VALUE AND T.HEAD_ID= P_ERP_HEAD_SID and T.STATUS='0';

            if V_DCLCUS_MARK!='2' then
            UPDATE T_DEC_ERP_I_RELATION t set t.ems_list_no=V_EMS_LIST_NO,t.bill_sid=V_BILL_HEAD_SID,t.bill_list_sid=(select SID from t_list_sid_temp where COLUMN_VALUE=T.CLIST_SID),t.entry_sid=V_ENTRY_HEAD_SID,t.entry_list_sid=V_ENTRY_LIST_SID where T.CLIST_SID in (SELECT COLUMN_VALUE FROM  t_list_sid_temp ) and T.STATUS='0';
            else
            UPDATE T_DEC_ERP_I_RELATION t set t.ems_list_no=V_EMS_LIST_NO,t.bill_sid=V_BILL_HEAD_SID,t.bill_list_sid=(select SID from t_list_sid_temp where COLUMN_VALUE=T.CLIST_SID) where T.CLIST_SID in (SELECT COLUMN_VALUE FROM  t_list_sid_temp ) and T.STATUS='0';
            end if;

            COMMIT;

            --返回插入行数，并累加值清单列表编号
            V_BILL_SERIAL_NO:=V_BILL_SERIAL_NO+SQL%ROWCOUNT;
            END ;
            END LOOP;

            update T_DEC_ERP_I_LIST_N t set t.BILL_HEAD_ID=(select r.BILL_SID from T_DEC_ERP_I_RELATION r where t.sid=r.list_sid) WHERE T.HEAD_ID=P_ERP_HEAD_SID;
            UPDATE  T_DEC_ERP_I_HEAD_N T SET T.APPR_STATUS='9' WHERE T.SID=P_ERP_HEAD_SID;

            select ARRIVAL_PORT_DATE,ARRIVAL_DATE,DAMAGE_MARK,DAMAGE_REMARK,PLATE_NUM,DELIVERY_PERSON,LINK_MAN_TEL into V_ARRIVAL_PORT_DATE,V_ARRIVAL_DATE,V_DAMAGE_MARK,V_DAMAGE_REMARK,V_PLATE_NUM,V_DELIVERY_PERSON,V_LINK_MAN_TEL  from T_DEC_I_LOGISTICS_TRACK LOG where SID = P_ERP_HEAD_SID;

            UPDATE T_DEC_I_CUSTOMS_TRACK TRACK SET ARRIVAL_PORT_DATE=V_ARRIVAL_PORT_DATE,DELIVERY_DATE=V_ARRIVAL_DATE,DAMAGE_MARK=V_DAMAGE_MARK,DAMAGE_REMARK=V_DAMAGE_REMARK,PLATE_NUM=V_PLATE_NUM,DELIVERY_PERSON=V_DELIVERY_PERSON,LINK_MAN_TEL=V_LINK_MAN_TEL  WHERE TRACK.ERP_HEAD_ID = P_ERP_HEAD_SID;
            END IF;
            END;
            COMMIT;
            &lt;&lt;END_POINT&gt;&gt;
            NULL;
            EXCEPTION
            WHEN OTHERS THEN
            BEGIN
            ROLLBACK;
            P_IS_SUCC:=0;
            P_ERR_MSG:=SQLCODE||'--'||SQLERRM;
            END;
            end;
        </createProcedure>
    </changeSet>
</databaseChangeLog>