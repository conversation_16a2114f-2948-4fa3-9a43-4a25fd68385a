--liquibase formatted sql

--changeset heenyu:1
ALTER TABLE t_dev_free_apply_head
  ADD entry_status varchar(2);
ALTER TABLE t_dev_free_apply_head
  ADD  entry_seq_no varchar(60);
ALTER TABLE t_dev_free_apply_head
  ADD  entry_declare_date timestamp;
ALTER TABLE t_dev_free_apply_head
  ADD  traf_mode varchar(2);
ALTER TABLE t_dev_free_apply_head
  ADD  traf_name varchar(50);
ALTER TABLE t_dev_free_apply_head
  ADD  voyage_no varchar(50);
ALTER TABLE t_dev_free_apply_head
  ADD  hwab varchar(50);
ALTER TABLE t_dev_free_apply_head
  ADD  trade_mode varchar(10);
ALTER TABLE t_dev_free_apply_head
  ADD  gross_wt numeric(19,5);
ALTER TABLE t_dev_free_apply_head
  ADD  card_mark varchar(2);
ALTER TABLE t_dev_free_apply_head
  ADD  check_mark varchar(2);
ALTER TABLE t_dev_free_apply_head
  ADD  tax_mark varchar(2);
ALTER TABLE t_dev_free_apply_head
  ADD  entry_customs varchar(10);
ALTER TABLE t_dev_free_apply_head
  ADD  is_declare_success varchar(1) default '0';

comment on column  t_dev_free_apply_head.entry_status IS '报关状态';

comment on column  t_dev_free_apply_head.entry_seq_no IS '报关单统一编号';

comment on column  t_dev_free_apply_head.entry_declare_date IS '概要发送日期';

comment on column  t_dev_free_apply_head.traf_mode IS '运输方式';

comment on column  t_dev_free_apply_head.traf_name IS '运输工具名称';

comment on column  t_dev_free_apply_head.voyage_no IS '航次号';

comment on column  t_dev_free_apply_head.hwab IS '提运单号';

comment on column  t_dev_free_apply_head.trade_mode IS '监管方式';

comment on column  t_dev_free_apply_head.gross_wt IS '毛重';

comment on column  t_dev_free_apply_head.card_mark IS '是否涉证 0-否，1-是';

comment on column  t_dev_free_apply_head.check_mark IS '是否涉检 0-否，1-是';

comment on column  t_dev_free_apply_head.tax_mark IS '是否涉税 0-否，1-是';

comment on column  t_dev_free_apply_head.entry_customs IS '报关单申报地海关';

comment on column  t_dev_free_apply_head.is_declare_success IS '是否申报完成 0 未申报 1 概要申报失败 2 概要申报成功 3 完整申报成功';

ALTER TABLE t_dec_erp_i_head_n
  ADD  entry_no varchar(10);
ALTER TABLE t_dec_erp_i_head_n
  ADD  seq_no varchar(1);

comment on column  t_dec_erp_i_head_n.entry_no IS '报关单号';

comment on column  t_dec_erp_i_head_n.seq_no IS '报关单统一编号';
