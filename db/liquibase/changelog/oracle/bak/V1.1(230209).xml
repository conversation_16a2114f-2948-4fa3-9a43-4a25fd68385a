<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog/1.7"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog/1.7
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-1.7.xsd">

    <changeSet id="1" author="zyshen">
        <createView replaceIfExists="true" viewName="V_DEC_I_CUSTOMS_TRACK">
            SELECT
            t.SID,
            t.ENTRUST_DATE,
            t.ENTRUST_PERSON,
            t.BE_ENTRUST_PERSON,
            t.DECLARE_DATE,
            t.DECLARE_PORT,
            t.DECLARE_PERSON,
            t.CHECK_DATE,
            t.CHECK_REASON,
            t.COMPLETE_CHECK_DATE,
            t.CHECK_RESULT,
            t.ENTOURAGE,
            t.CHECK_FILE,
            t.DUTY_DATE,
            t.DUTY_TYPE,
            t.DUTY_TOTAL,
            t.DUTY_PRICE,
            t.TAX_PRICE,
            t.OTHER_PRICE,
            t.PASS_DATE,
            t.DELIVERY_DATE,
            t.INSERT_USER,
            t.INSERT_USER_NAME,
            t.INSERT_TIME,
            t.UPDATE_USER,
            t.UPDATE_USER_NAME,
            t.UPDATE_TIME,
            t.TRADE_CODE,
            t.HEAD_ID,
            t.EMS_LIST_NO,
            hm.INSERT_TIME AS HEAD_INSERT_TIME,
            t.CUSTOMS_CHECK_DATE,
            t.CUSTOMS_CHECK_REASON,
            t.CUSTOMS_COMPLETE_CHECK_DATE,
            t.CUSTOMS_CHECK_RESULT,
            t.CUSTOMS_ENTOURAGE,
            t.CUSTOMS_CHECK_FILE,
            t.ENTRY_NO,
            t.CHECK_PERSON,
            t.CUSTOMS_CHECK_PERSON,
            t.STATUS,
            t.ERP_HEAD_ID,
            t.NOTE_1,
            t.LINKED_NO,
            t.NOTE_3,
            t.FINANCE_NO,
            t.ARRIVAL_DATE,
            t.PLATE_NUM,
            t.DELIVERY_PERSON,
            t.LINK_MAN_TEL,
            t.IN_OUT_NO,
            t.NOTICE_DATE,
            t.PLAN_ARRIVAL_DATE,
            t.DAMAGE_MARK,
            t.DAMAGE_REMARK,
            t.PAY_DATE,
            t.LOGISTICS_FEE,
            t.CUSTOMS_FEE,
            t.DELIVERY_NO,
            t.arrival_port_date,
            t.inspection_no,
            t.DUTY_INTEREST,
            t.TAX_INTEREST,
            t.PAYMENT_DATE,
            t.ADD_TAX_PRICE,
            t.NOTE_PASS,
            CASE
            WHEN t.FINANCE_NO IS NOT NULL THEN
            '9'
            WHEN t.DELIVERY_DATE IS NOT NULL THEN
            '8'
            WHEN t.NOTICE_DATE IS NOT NULL THEN
            '7'
            WHEN t.PASS_DATE IS NOT NULL THEN
            '6'
            WHEN t.DUTY_DATE IS NOT NULL THEN
            '5'
            WHEN (t.CHECK_DATE IS NOT NULL
            OR t.CUSTOMS_CHECK_DATE IS NOT NULL) THEN
            '4'
            WHEN bill.ENTRY_DECLARE_DATE IS NOT NULL THEN
            '3'
            WHEN bill.DECLARE_DATE IS NOT NULL THEN
            '2'
            WHEN t.ARRIVAL_PORT_DATE IS NOT NULL THEN
            '1' ELSE ''
            END GOODS_STATUS,
            CASE
            WHEN t.FINANCE_NO IS NOT NULL THEN
            '已结算'
            WHEN t.DELIVERY_DATE IS NOT NULL THEN
            '已到货'
            WHEN t.NOTICE_DATE IS NOT NULL THEN
            '已通知'
            WHEN t.PASS_DATE IS NOT NULL THEN
            '已放行'
            WHEN t.DUTY_DATE IS NOT NULL THEN
            '已完税'
            WHEN (t.CHECK_DATE IS NOT NULL
            OR t.CUSTOMS_CHECK_DATE IS NOT NULL) THEN
            '查验中'
            WHEN bill.ENTRY_DECLARE_DATE IS NOT NULL THEN
            '已报关'
            WHEN bill.DECLARE_DATE IS NOT NULL THEN
            '已申报'
            WHEN t.ARRIVAL_PORT_DATE IS NOT NULL THEN
            '已到港' ELSE ''
            END GOODS_STATUS_NAME,
            CASE
            WHEN t.PASS_DATE IS NULL THEN
            '0' ELSE '1'
            END PASS_FLAG,
            CASE
            WHEN t.DELIVERY_DATE IS NULL THEN
            '0' ELSE '1'
            END DELIVERY_FLAG,
            CASE
            WHEN t.CHECK_DATE IS NULL THEN
            '0' ELSE '1'
            END CHECK_FLAG,
            CASE
            WHEN t.CUSTOMS_CHECK_DATE IS NULL THEN
            '0' ELSE '1'
            END CUSTOMS_CHECK_FLAG,
            CASE
            WHEN t.DUTY_DATE IS NULL THEN
            '0' ELSE '1'
            END DUTY_FLAG,
            CASE
            WHEN t.PASS_DATE IS NULL THEN
            '否' ELSE '是'
            END PASS_FLAG_NAME,
            CASE
            WHEN t.DELIVERY_DATE IS NULL THEN
            '否' ELSE '是'
            END DELIVERY_FLAG_NAME,
            CASE
            WHEN t.CHECK_DATE IS NULL THEN
            '否' ELSE '是'
            END CHECK_FLAG_NAME,
            CASE
            WHEN t.CUSTOMS_CHECK_DATE IS NULL THEN
            '否' ELSE '是'
            END CUSTOMS_CHECK_FLAG_NAME,
            CASE
            WHEN t.DUTY_DATE IS NULL THEN
            '否' ELSE '是'
            END DUTY_FLAG_NAME,
            hm.TRAF_MODE,
            bill.TRADE_MODE,
            hm.WRAP_TYPE,
            hm.VOYAGE_DATE,
            bill.DECLARE_DATE AS BILL_DECLARE_DATE,
            bill.ENTRY_DECLARE_DATE,
            bill.ems_no,
            bill.list_no,
            hm.DECLARE_CODE,
            bill.BOND_MARK,
            CONCAT(bi.CUSTOMER_CODE ||' ',bi.COMPANY_NAME)AS DECLARE_CODE_CUSTOMS,
            hm.CONTR_NO,
            t.TAX_TOTAL,
            t.OTHER_TAX,
            hm.HAWB,
            t.EXCISE_TAX,
            t.COMPLETE,
            t.excise_interest,
            t.PURCHASE_CONFIRM,
            t.FINANCE_CONFIRM,
            hm.forward_code,
            entry.entry_status,
            hm.INVOICE_NO,
            t.ANTI_DUMPING_DUTY
            FROM
            T_DEC_I_CUSTOMS_TRACK t
            LEFT JOIN T_DEC_I_BILL_HEAD bill ON bill.SID = t.HEAD_ID
            LEFT JOIN t_dec_i_entry_head entry ON entry.bill_head_id = t.head_id
            LEFT JOIN T_DEC_ERP_I_HEAD_N hm ON hm.SID = bill.HEAD_ID
            LEFT JOIN T_BI_CLIENT_INFORMATION bi ON t.TRADE_CODE = bi.TRADE_CODE AND hm.DECLARE_CODE_CUSTOMS =
            bi.CUSTOMER_CODE and bi.customer_type='CUT'
        </createView>
    </changeSet>
</databaseChangeLog>