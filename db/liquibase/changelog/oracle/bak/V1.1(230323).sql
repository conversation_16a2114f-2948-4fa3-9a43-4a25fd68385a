--liquibase formatted sql

--changeset zyshen:1
alter table T_GW_PACKING_MAINTAIN
    modify CARTON_NUM NUMBER(5);

--changeset zyshen:2
alter table T_DEC_I_LOGISTICS_TRACK
    modify LINK_MAN_TEL VARCHAR2(100);

--changeset zyshen:3
alter table T_GW_PACKING_MAINTAIN_TMP
    modify CARTON_NUM NUMBER(5);

--changeset zyshen:4
alter table T_GW_REPORT_DEC_I_HEAD add LINK_MAN_TEL VARCHAR2(100);
comment on column T_GW_REPORT_DEC_I_HEAD.LINK_MAN_TEL is '联系电话';
alter table T_GW_REPORT_DEC_I_HEAD_LIST add LINK_MAN_TEL VARCHAR2(100);
comment on column T_GW_REPORT_DEC_I_HEAD_LIST.LINK_MAN_TEL is '联系电话';

alter table T_DEC_I_CUSTOMS_T<PERSON><PERSON><PERSON> modify LINK_MAN_TEL VARCHAR2(100);
alter table T_DEC_I_CUSTOMS_TRACK modify DELIVERY_PERSON VARCHAR2(100);

--changeset chenkai:1
alter table t_gwstd_entry_head_third add modify_status varchar2(1) default 1;
comment on column t_gwstd_entry_head_third.modify_status is '修改状态: 1: 未修改; 2: 已修改';

--changeset chenkai:2
ALTER TABLE T_GWSTD_ENTRY_HEAD_THIRD MODIFY MASTER_CUSTOMS VARCHAR2(20);
ALTER TABLE T_GWSTD_ENTRY_HEAD_THIRD MODIFY ACMP_NO VARCHAR2(255);

--changeset wj:1
alter table t_gwstd_entry_head_third add desp_port_name VARCHAR2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.desp_port_name IS '启运港(进口)名称';
COMMENT ON COLUMN t_gwstd_entry_head_third.desp_port_code IS '启运港(进口)';
alter table t_gwstd_entry_head_third_loc add desp_port_name_loc VARCHAR2(400);
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.desp_port_name_loc IS '启运港(进口)名称';
alter table t_gwstd_entry_head_third_loc add goodsplace_loc VARCHAR2(400);
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.goodsplace_loc IS '货物存放地点';