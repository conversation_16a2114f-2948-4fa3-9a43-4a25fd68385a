--liquibase formatted sql

--changeset chenkai:1
ALTER TABLE T_WF_DECLARE_FORM MODIFY APPLY_NO VARCHAR2(14) NULL;
ALTER TABLE T_WF_DECLARE_FORM MODIFY DECLARE_DATE DATE NULL;

--changeset chenkai:2
CREATE TABLE "T_WF_DECLARE_FORM_APPROVED"
   ("SID" VARCHAR2(40) NOT NULL ENABLE,
	"APPLY_NO" VARCHAR2(14),
	"EMS_COP_NO" VARCHAR2(20) NOT NULL ENABLE,
	"DECLARE_DATE" DATE,
	"APPLY_VAILD_DATE" DATE NOT NULL ENABLE,
	"CONTRACTOR_TRADE_NAME" VARCHAR2(100) NOT NULL ENABLE,
	"CONTRACTOR_MASTER_CUSTOMS" VARCHAR2(4) NOT NULL ENABLE,
	"CONTRACTOR_DISTRICT_CODE" VARCHAR2(5) NOT NULL ENABLE,
	"CONTRACTOR_TRADE_CODE" VARCHAR2(10),
	"CONTRACTOR_CREDIT_CODE" VARCHAR2(20),
	"CONTRACTOR_MOBILE_PHONE" VARCHAR2(256) NOT NULL ENABLE,
	"CONTRACTOR_TELEPHONE_NO" VARCHAR2(256) NOT NULL ENABLE,
	"ENTRUST_EMS_NO" VARCHAR2(12) NOT NULL ENABLE,
	"TRADE_CODE" VARCHAR2(10) NOT NULL ENABLE,
	"ENTRUST_MASTER_CUSTOMS" VARCHAR2(4) NOT NULL ENABLE,
	"ENTRUST_CREDIT_CODE" VARCHAR2(20),
	"ENTRUST_TRADE_NAME" VARCHAR2(100) NOT NULL ENABLE,
	"ENTRUST_MOBILE_PHONE" VARCHAR2(256) NOT NULL ENABLE,
	"ENTRUST_CONTR_NO" VARCHAR2(64),
	"OUT_SEND_FLAG" NUMBER(1,0) NOT NULL ENABLE,
	"OUT_SEND_DECLARE_AMOUNT" NUMBER(18,5),
	"OUT_SEND_GOODS" VARCHAR2(512) NOT NULL ENABLE,
	"DECLARE_TRADE_CODE" VARCHAR2(10) NOT NULL ENABLE,
	"DECLARE_TRADE_NAME" VARCHAR2(100) NOT NULL ENABLE,
	"DECLARE_CREDIT_CODE" VARCHAR2(20),
	"RECEIVE_GOODS" VARCHAR2(512) NOT NULL ENABLE,
	"REMARK" VARCHAR2(255),
	"INSERT_USER" VARCHAR2(50) NOT NULL ENABLE,
	"INSERT_TIME" DATE DEFAULT sysdate NOT NULL ENABLE,
	"UPDATE_USER" VARCHAR2(50),
	"UPDATE_TIME" DATE,
	"DATA_SOURCE" VARCHAR2(1) DEFAULT '1' NOT NULL ENABLE,
	"CONTRACTOR_COMPANY_CODE" VARCHAR2(50) NOT NULL ENABLE,
	"CONTRACTOR_DISTRICT_NAME" VARCHAR2(100) NOT NULL ENABLE,
	"INSERT_USER_NAME" VARCHAR2(50),
	"UPDATE_USER_NAME" VARCHAR2(50),
	"ENTRUST_REGION_CODE" VARCHAR2(5),
	"ENTRUST_LEGAL_TEL_NO" VARCHAR2(256),
	"ENTRUST_CONTACTS_TEL_NO" VARCHAR2(256),
	"ACTUAL_SHIPMENT_AMOUNT" NUMBER(18,5),
	"ACTUAL_RECEIPT_AMOUNT" NUMBER(18,5),
	"APP_TYPE" VARCHAR2(1) DEFAULT '1',
	"APP_STATUS" VARCHAR2(2),
	"SEND_STATUS" VARCHAR2(1),
	"STATUS" VARCHAR2(2),
	"PRE_SEQ_NO" VARCHAR2(20),
	"CHANGE_TIMES" NUMBER(11,0),
	"FILINGS_APPROVAL_DATE" DATE,
	"CHANGE_APPROVAL_DATE" DATE,
	"GUARANTEE_NO" VARCHAR2(50),
	 CONSTRAINT "IDX_WF_DECLARE_FORM_APP_PK" PRIMARY KEY ("SID"));

--changeset chenkai:3
COMMENT ON TABLE T_WF_DECLARE_FORM_APPROVED IS '外发加工申报表';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.SID IS '主键';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.APPLY_NO IS '申报表编号';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.EMS_COP_NO IS '企业内部编号';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.DECLARE_DATE IS '申报日期';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.APPLY_VAILD_DATE IS '申报表有效期';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CONTRACTOR_TRADE_NAME IS '承揽者名称';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CONTRACTOR_MASTER_CUSTOMS IS '承揽者所在地主管海关';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CONTRACTOR_DISTRICT_CODE IS '承揽者所在地地区代码';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CONTRACTOR_TRADE_CODE IS '承揽者海关代码';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CONTRACTOR_CREDIT_CODE IS '承揽者社会信用代码';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CONTRACTOR_MOBILE_PHONE IS '承揽者联系人/电话';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CONTRACTOR_TELEPHONE_NO IS '承揽者法人/电话';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ENTRUST_EMS_NO IS '委托方手账册号';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.TRADE_CODE IS '委托方海关代码';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ENTRUST_MASTER_CUSTOMS IS '委托方主管海关';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ENTRUST_CREDIT_CODE IS '委托方信用代码';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ENTRUST_TRADE_NAME IS '委托方企业名称';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ENTRUST_MOBILE_PHONE IS '委托方申报人/电话';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ENTRUST_CONTR_NO IS '委托加工合同号';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.OUT_SEND_FLAG IS '全工序外发标志(0-否，1-是)';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.OUT_SEND_DECLARE_AMOUNT IS '外发申报金额（人民币）';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.OUT_SEND_GOODS IS '外发主要货物';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.DECLARE_TRADE_CODE IS '申报单位编码';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.DECLARE_TRADE_NAME IS '申报单位名称';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.DECLARE_CREDIT_CODE IS '申报单位社会信用代码';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.RECEIVE_GOODS IS '收回主要货物';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.REMARK IS '备注';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.INSERT_USER IS '创建人';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.INSERT_TIME IS '创建日期';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.UPDATE_USER IS '修改人';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.UPDATE_TIME IS '修改时间';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.DATA_SOURCE IS '数据来源{1.界面维护/导入 2.接口导入}';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CONTRACTOR_COMPANY_CODE IS '承揽者企业代码';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CONTRACTOR_DISTRICT_NAME IS '承揽者所在地地区名称';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.INSERT_USER_NAME IS '制单人姓名';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.UPDATE_USER_NAME IS '修改人姓名';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ENTRUST_REGION_CODE IS '委托方地区代码';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ENTRUST_LEGAL_TEL_NO IS '委托方法人/电话';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ENTRUST_CONTACTS_TEL_NO IS '委托方联系人/电话';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ACTUAL_SHIPMENT_AMOUNT IS '实际发货总金额(人民币)';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ACTUAL_RECEIPT_AMOUNT IS '实际收货总金额(人民币)';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.APP_TYPE IS '申报类型(1-备案; 2-变更)';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.APP_STATUS IS '申报状态(0暂存 A申报中 R退单 B转人工 P审批通过)';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.SEND_STATUS IS '发送状态(0未发送 1发送中 2发送成功 3发送失败)';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.STATUS IS '启用状态(1启用 -1停用)';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.PRE_SEQ_NO IS '预录入统一编号';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CHANGE_TIMES IS '变更次数';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.FILINGS_APPROVAL_DATE IS '备案批准日期';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.CHANGE_APPROVAL_DATE IS '变更批准日期';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.GUARANTEE_NO IS '担保编号';

--changeset chenkai:4
INSERT INTO t_gwstd_http_config
(sid, base_url, service_url, token, type, extend_filed1, extend_filed2, extend_filed3, note, trade_code, insert_user, insert_time, update_user, update_time, insert_user_name, update_user_name)
VALUES('6B0950E1-932F-4E21-9A39-D701F93D9BC5', 'http://192.168.10.175', '/pr/api/v1/GwDataReceive/declare', NULL, 'WF_DEC_FORM_URL', NULL, NULL, NULL, '外发加工申报表申报方法', '9999999999', 'SYSTEM', to_date('2023-05-24 09:50:00', 'yyyy-MM-dd hh24:mi:ss'), NULL, NULL, NULL, NULL);

--changeset chenkai:5
ALTER TABLE T_WF_DECLARE_FORM ADD ENTRUST_TRADE_CODE VARCHAR2(10);
COMMENT ON COLUMN T_WF_DECLARE_FORM.ENTRUST_TRADE_CODE IS '委托方海关代码';
COMMENT ON COLUMN T_WF_DECLARE_FORM.TRADE_CODE IS '企业代码';
ALTER TABLE T_WF_DECLARE_FORM_APPROVED ADD ENTRUST_TRADE_CODE VARCHAR2(10);
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.ENTRUST_TRADE_CODE IS '委托方海关代码';
COMMENT ON COLUMN T_WF_DECLARE_FORM_APPROVED.TRADE_CODE IS '企业代码';

--changeset chenkai:6
ALTER TABLE T_WF_DECLARE_FORM MODIFY EMS_COP_NO VARCHAR2(64);
ALTER TABLE T_WF_DECLARE_FORM_APPROVED MODIFY EMS_COP_NO VARCHAR2(64);
