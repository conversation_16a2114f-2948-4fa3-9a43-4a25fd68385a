--liquibase formatted sql

--changeset zyshen:1
alter table T_DEC_LOGISTICS_BOX
    modify PACK_NUM NUMBER(15, 5);

--changeset zyshen:2
alter table T_WARRING_PASS_EXPAND_CONFIG add PACKING_MAINTENANCE_CHECK VARCHAR2(1) default 0;
comment on column T_WARRING_PASS_EXPAND_CONFIG.PACKING_MAINTENANCE_CHECK is '箱单维护校验 0：否 1：是';

--changeset zyshen:3
alter table T_DEC_E_ENTRY_HEAD add SEND_ENTRY_DATE DATE;
comment on column T_DEC_E_ENTRY_HEAD.SEND_ENTRY_DATE is '发送报关单时间';

alter table T_DEC_I_ENTRY_HEAD add SEND_ENTRY_DATE DATE;
comment on column T_DEC_I_ENTRY_HEAD.SEND_ENTRY_DATE is '发送报关单时间';

--changeset zyshen:4
alter table T_WARRING_PASS_EXPAND_CONFIG
    modify PACKING_MAINTENANCE_CHECK default 1;

--changeset jlzhang:1
alter table T_GWSTD_ENTRY_HEAD add DECL_TOTAL_USD NUMBER(19,4);
comment on column T_GWSTD_ENTRY_HEAD.DECL_TOTAL_USD is '美元申报总价';
