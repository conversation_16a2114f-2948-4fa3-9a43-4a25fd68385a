--liquibase formatted sql

--changeset jlzhang:1
CREATE TABLE T_GW_DB_SCHEMA_CONFIG (
  SID NVARCHAR2(50) NOT NULL,
  BUSINESS_TYPE NVARCHAR2(50) NOT NULL,
  DB_SCHEMA NVARCHAR2(100) NOT NULL,
  STATUS NVARCHAR2(1) NOT NULL,
  NOTE NVARCHAR2(255),
  TRADE_CODE NVARCHAR2(10) NOT NULL,
  INSERT_USER NVARCHAR2(50),
  INSERT_TIME DATE NOT NULL
);
COMMENT ON COLUMN T_GW_DB_SCHEMA_CONFIG.SID IS '唯一键';
COMMENT ON COLUMN T_GW_DB_SCHEMA_CONFIG.BUSINESS_TYPE IS '业务类型,SUB_ENTRY-订阅报关单；目前只有一个后续待补充';
COMMENT ON COLUMN T_GW_DB_SCHEMA_CONFIG.DB_SCHEMA IS '数据库schema';
COMMENT ON COLUMN T_GW_DB_SCHEMA_CONFIG.STATUS IS '状态，0-停用；1-启用';
COMMENT ON COLUMN T_GW_DB_SCHEMA_CONFIG.NOTE IS '备注';
COMMENT ON COLUMN T_GW_DB_SCHEMA_CONFIG.TRADE_CODE IS '企业编码，默认9999999999';
COMMENT ON COLUMN T_GW_DB_SCHEMA_CONFIG.INSERT_USER IS '维护人';
COMMENT ON COLUMN T_GW_DB_SCHEMA_CONFIG.INSERT_TIME IS '维护时间';
COMMENT ON TABLE T_GW_DB_SCHEMA_CONFIG IS '关务从第三方数据库获取业务数据schema配置表+';

-- ----------------------------
-- Primary Key structure for table T_GW_DB_SCHEMA_CONFIG
-- ----------------------------
ALTER TABLE T_GW_DB_SCHEMA_CONFIG ADD CONSTRAINT T_GW_DB_SCHEMA_PKEY PRIMARY KEY (SID);

-- ----------------------------
-- Checks structure for table T_GW_DB_SCHEMA_CONFIG
-- ----------------------------
ALTER TABLE T_GW_DB_SCHEMA_CONFIG ADD CONSTRAINT SYS_C00499377 CHECK (SID IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE T_GW_DB_SCHEMA_CONFIG ADD CONSTRAINT SYS_C00499378 CHECK (BUSINESS_TYPE IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE T_GW_DB_SCHEMA_CONFIG ADD CONSTRAINT SYS_C00499379 CHECK (DB_SCHEMA IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE T_GW_DB_SCHEMA_CONFIG ADD CONSTRAINT SYS_C00499380 CHECK (STATUS IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE T_GW_DB_SCHEMA_CONFIG ADD CONSTRAINT SYS_C00499381 CHECK (TRADE_CODE IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE T_GW_DB_SCHEMA_CONFIG ADD CONSTRAINT SYS_C00499382 CHECK (INSERT_TIME IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

--changeset chenkai:1
ALTER TABLE T_GW_DEC_I_PLAN_DETAILS MODIFY PURCHASE_NUM NUMBER(19,5);
ALTER TABLE T_GW_DEC_I_PLAN_DETAILS MODIFY DEC_PRICE NUMBER(19,5);
ALTER TABLE T_GW_DEC_I_PLAN_DETAILS MODIFY DEC_TOTAL NUMBER(20,4);

ALTER TABLE T_GW_DEC_I_PLAN_LIST MODIFY QTY NUMBER(19,5);
ALTER TABLE T_GW_DEC_I_PLAN_LIST MODIFY DEC_PRICE NUMBER(19,5);
ALTER TABLE T_GW_DEC_I_PLAN_LIST MODIFY DEC_TOTAL NUMBER(20,4);
