<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog/1.7"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog/1.7
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-1.7.xsd">

    <changeSet id="1" author="zyshen">
        <createProcedure>
            create OR REPLACE procedure P_IMP_DEC_ERP_E_LIST(P_TEMP_OWNER in varchar2,
            P_FLAG       in out varchar2,
            P_RET_CODE   in out varchar2,
            P_RET_STR    in out varchar2) is
            P_COP_EMS_NO     varchar2(50);
            P_TRADE_CODE     varchar2(50);
            P_HEAD_ID        varchar2(50);
            P_G_MARK         varchar2(50); -- 料件成品标记
            P_MERGE_TYPE     varchar2(10); -- 人工/自动归并标记
            P_EMS_NO         varchar2(50); --
            P_EMS_NO_NEW     varchar2(50); --账册号
            P_TRADE_MODE     varchar2(50); --监管方式
            P_TRADE_MODE_NEW varchar2(50); --监管方式
            P_BILL_TYPE     varchar2(10); -- 清单归并方式

            P_DISTRICT_CODE      varchar2(50); --境内目的地
            P_DISTRICT_POST_CODE varchar2(50); --境内目的地-行政

            P_COUNTRY_MODE varchar2(1);
            P_DESTINATION_COUNTRY varchar2(10);
            P_EXG_VERSION varchar2(1);
            P_DEC_TYPE varchar2(1); --大小提单标记  0大提单 1 小提单
            P_AUTO_NET_WT varchar2(1);
            P_AUTO_NET_WT_DIGIT INTEGER;
            P_AUTO_GROSS_WT varchar2(1);
            P_AUTO_GROSS_WT_DIGIT INTEGER;

            P_NET_WT_DIGIT INTEGER;
            P_QTY_DIGIT INTEGER;
            P_QTY1_DIGIT INTEGER;
            P_QTY2_DIGIT INTEGER;
            P_DEC_TOTAL_DIGIT INTEGER;
            P_VOLUME_DIGIT INTEGER;
            P_AUTO_VOLUME varchar2(1);
            P_COUNT INTEGER;
            P_MAT_DATA_E varchar2(1);

            P_INSERT_TIME DATE;

            P_INVOICE_NO_TYPE varchar2(1);
            P_INVOICE_NO_SPLIT varchar2(10);
            P_CONTR_NO_TYPE varchar2(1);
            P_CONTR_NO_SPLIT varchar2(10);
            P_FRIST_INVOICE_NO varchar2(100);
            P_FRIST_CONTR_NO varchar2(100);
            P_INVOICE_NO_COUNT INTEGER;
            P_CONTR_NO_COUNT INTEGER;
            P_HEAD_INVOICE_NO varchar2(100);
            P_HEAD_CONTR_NO varchar(100);
            P_LIST_RE_FIELD_E INTEGER;
            P_LIST_RE_FIELD_E_NO_BOND INTEGER;

            P_IMPORT_TYPE varchar2(1);
            BEGIN

            SELECT HEAD_ID, TMP.COP_EMS_NO, EMS_NO, TRADE_MODE
            INTO P_HEAD_ID, P_COP_EMS_NO, P_EMS_NO_NEW, P_TRADE_MODE_NEW
            FROM T_IMP_TMP_DEC_ERP_E_LIST tmp
            WHERE tmp.TEMP_OWNER = P_TEMP_OWNER
            and ROWNUM = 1;


            SELECT H.TRADE_CODE,
            H.TRADE_MODE,
            H.EMS_NO,
            H.MERGE_TYPE,
            DISTRICT_CODE,
            DISTRICT_POST_CODE,
            G_MARK,
            BILL_TYPE,
            DESTINATION_COUNTRY,
            DEC_TYPE,INSERT_TIME,
            INVOICE_NO,
            CONTR_NO
            INTO P_TRADE_CODE,
            P_TRADE_MODE,
            P_EMS_NO,
            P_MERGE_TYPE,
            P_DISTRICT_CODE,
            P_DISTRICT_POST_CODE,
            P_G_MARK,
            P_BILL_TYPE,
            P_DESTINATION_COUNTRY,
            P_DEC_TYPE,P_INSERT_TIME,
            P_HEAD_INVOICE_NO,
            P_HEAD_CONTR_NO
            FROM T_DEC_ERP_E_HEAD_N H
            WHERE H.SID = P_HEAD_ID;

            SELECT
            nvl ( MAX ( COUNTRY_MODE ), 0 ),
            nvl ( MAX ( CHECK_EXG_VERSION ), 0 ),
            nvl ( MAX ( AUTO_NET_WT_E ), 0 ),
            nvl ( MAX ( AUTO_NET_WT_DIGIT_E ), 0 ),
            nvl ( MAX ( AUTO_GROSS_WT_E ), 0 ),
            nvl ( MAX ( AUTO_GROSS_WT_DIGIT_E ), 0 ),
            nvl ( MAX ( NET_WT_DIGIT ), 0 ),
            nvl ( MAX ( QTY_DIGIT ), 0 ),
            nvl ( MAX ( QTY1_DIGIT ), 0 ),
            nvl ( MAX ( QTY2_DIGIT ), 0 ),
            nvl ( MAX ( AUTO_VOLUME_E ), 0 ),
            nvl ( MAX ( MAT_DATA_E ), 0 ),
            nvl ( MAX ( INVOICE_NO_TYPE ), 0 ) ,
            nvl(MAX (INVOICE_NO_SPLIT),'|') ,
            nvl ( MAX ( CONTR_NO_TYPE ), 0 ) ,
            nvl(MAX (CONTR_NO_SPLIT),'|'),
            instr( MAX (LIST_RE_FIELD_E), 'qty2'),
            instr( MAX (LIST_RE_FIELD_E_NO_BOND), 'qty2'),
            f_xdo_nvl(max(DEC_TOTAL_DIGIT_E),4),
            f_xdo_nvl(max(VOLUME_DIGIT_E),5),
            f_xdo_nvl(max(IMPORT_TYPE),'0')
            INTO P_COUNTRY_MODE,
            P_EXG_VERSION,
            P_AUTO_NET_WT,
            P_AUTO_NET_WT_DIGIT,
            P_AUTO_GROSS_WT,
            P_AUTO_GROSS_WT_DIGIT,
            P_NET_WT_DIGIT,
            P_QTY_DIGIT,
            P_QTY1_DIGIT,
            P_QTY2_DIGIT,
            P_AUTO_VOLUME,
            P_MAT_DATA_E,
            P_INVOICE_NO_TYPE ,
            P_INVOICE_NO_SPLIT ,
            P_CONTR_NO_TYPE ,
            P_CONTR_NO_SPLIT,
            P_LIST_RE_FIELD_E,
            P_LIST_RE_FIELD_E_NO_BOND,
            P_DEC_TOTAL_DIGIT,
            P_VOLUME_DIGIT,
            P_IMPORT_TYPE
            FROM
            t_warring_pass_config w
            WHERE
            w.TRADE_CODE = P_TRADE_CODE;

            IF P_FLAG = 0 THEN

            -- 初始化清单企业内部编号
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST
            SET EMS_LIST_NO =
            (SELECT EMS_LIST_NO FROM T_DEC_ERP_E_HEAD_N WHERE SID = P_HEAD_ID)
            WHERE TEMP_OWNER = P_TEMP_OWNER;

            IF P_DEC_TYPE='1' then
            --小提单时候，表体备案号，监管方式清除，只取表头
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.TRADE_MODE = null
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER;

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.EMS_NO = null
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER;
            end if;

            --先判断表体有EMSNo,则bondMark置0
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.BOND_MARK = '0'
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.EMS_NO is not null
            and TMP.BOND_MARK is null;

            if P_EMS_NO is null then
            --如果表头账册不填写 为非保
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.BOND_MARK = '1'
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.BOND_MARK is null;
            else
            --如果表头账册填写 为保
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.BOND_MARK = '0'
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.BOND_MARK is null;
            end if;

            --表头emsNo为空时，自动回填
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.EMS_NO = P_EMS_NO
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.EMS_NO is null
            and (TMP.BOND_MARK = '0' or TMP.BOND_MARK is null);

            --表头监管方式为空时，自动回填
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.TRADE_MODE = P_TRADE_MODE
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TRADE_MODE is null;

            --境内目的地为空时，自动回填
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.DISTRICT_CODE = P_DISTRICT_CODE
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.DISTRICT_CODE is null;

            --境内目的地-行政为空时，自动回填
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.DISTRICT_POST_CODE = P_DISTRICT_POST_CODE
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.DISTRICT_POST_CODE is null;

            --物料标识先默认取表头的
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.G_MARK =P_G_MARK
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.G_MARK is null;

            --物料标识如果表头为空，默认E
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.G_MARK = 'E'
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.G_MARK is null;

            if P_COUNTRY_MODE='1' then
            --表头最终目的国不为空时更新
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.Destination_Country = P_DESTINATION_COUNTRY
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.Destination_Country is null;
            end if;

            --监管方式为0265/0300/0664/0700时，物料类型为I，征免方式为3
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.G_MARK = 'I',
            TMP.Duty_Mode='3'
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TRADE_MODE in ('0265','0300','0664','0700');

            --保税时， B\C手册必须为空
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '手册企业,单耗版本号必须为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            and TMP.BOND_MARK = '0'
            and TMP.G_MARK = 'E'
            and (SUBSTR(TMP.EMS_NO,1,1)='B' or SUBSTR(TMP.EMS_NO,1,1)='C')
            AND EXG_VERSION IS not NULL ;

            if P_EXG_VERSION='1' then
            --保税时， E账册成品版本号控制
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET EXG_VERSION   = to_char(P_INSERT_TIME, 'yyyymm')||'01'
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            and TMP.BOND_MARK = '0'
            and TMP.G_MARK = 'E'
            and SUBSTR(TMP.EMS_NO,1,1)='E'
            AND EXG_VERSION IS NULL;
            end if;

            --保税时，成品单耗版本号不能为空 B\C手册除外
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '保税时,成品单耗版本号不能为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            and TMP.BOND_MARK = '0'
            and TMP.G_MARK = 'E'
            and (SUBSTR(TMP.EMS_NO,1,1)!='B' and SUBSTR(TMP.EMS_NO,1,1)!='C')
            AND (EXG_VERSION IS NULL OR EXG_VERSION = '');
            if P_MERGE_TYPE = '1' then

            --人工归并时候，备案号必须一致
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '人工归并时,备案号必须唯一|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND (select count(distinct nvl(L.EMS_NO, 0))
            from T_IMP_TMP_DEC_ERP_E_LIST L
            where L.TEMP_OWNER = P_TEMP_OWNER) > 1;

            if P_EMS_NO is null then
            --人工归并时候，备案号必须一致
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '人工归并时,备案号必须和表头一致|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.EMS_NO is not null;

            else
            --人工归并时候，备案号必须一致
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '人工归并时,备案号必须和表头一致|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.BOND_MARK = '0'
            AND (TMP.EMS_NO != P_EMS_NO or TMP.EMS_NO is null);

            end if;

            if P_TRADE_MODE is null then
            --人工归并时候，监管方式必须一致
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '人工归并时,监管方式必须和表头一致|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TRADE_MODE is not null;

            else
            --人工归并时候，监管方式必须一致
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '人工归并时,监管方式必须和表头一致|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND (TMP.TRADE_MODE != P_TRADE_MODE or TMP.TRADE_MODE is null);
            end if;

            --人工归并时候，归并序号必填
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '人工归并时，归并序号不能为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND (ENTRY_G_NO IS NULL OR ENTRY_G_NO = '');

            END IF;
            if P_BILL_TYPE = '4' then
            --人工归并时候，清单归并序号必填
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '清单人工归并时，清单归并序号不能为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND (BILL_G_NO IS NULL OR BILL_G_NO = '');
            END IF;

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.TEMP_FLAG   = 1,
            TMP.TEMP_REMARK = CONCAT(TMP.TEMP_REMARK,
            '保完税标识只能为0或者1|')
            WHERE (TMP.BOND_MARK !='0' AND TMP.BOND_MARK != '1')
            AND TEMP_OWNER = P_TEMP_OWNER;

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.TEMP_FLAG   = 1,
            TMP.TEMP_REMARK = CONCAT(TMP.TEMP_REMARK,
            '保税物料类型只能为I或者E|')
            WHERE (TMP.G_MARK != 'I' AND TMP.G_MARK !='E')
            AND TMP.BOND_MARK = '0'
            and TMP.G_MARK is not null
            AND TEMP_OWNER = P_TEMP_OWNER;

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.TEMP_FLAG   = 1,
            TMP.TEMP_REMARK = CONCAT(TMP.TEMP_REMARK,
            '非保税物料类型只能为I、E、2、3、4|')
            WHERE (TMP.G_MARK not in ('I', 'E', '2', '3', '4'))
            AND TMP.BOND_MARK = '1'
            and TMP.G_MARK is not null
            AND TEMP_OWNER = P_TEMP_OWNER;

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '保税数据备案号不能为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.BOND_MARK = '0'
            AND (TMP.EMS_NO IS NULL OR TMP.EMS_NO = '');

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '监管方式不能为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND (TMP.TRADE_MODE IS NULL OR TMP.TRADE_MODE = '');

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '备案号不存在当前物料中心|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.BOND_MARK = '0'
            AND (TMP.EMS_NO IS not NULL and TMP.EMS_NO != '')
            and TMP.EMS_NO not in (select T.EMS_NO from T_MAT_IMGEXG T);

            --非保税不存在备案号
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '非保税不应有备案号|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.BOND_MARK = '1'
            AND TMP.EMS_NO IS NOT NULL;

            --境外收货人不存在
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '境外收货人不存在|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND (SUPPLIER_CODE IS not NULL)
            and NOT EXISTS (select 1
            from T_BI_CLIENT_INFORMATION T
            where t.TRADE_CODE = P_TRADE_CODE
            AND T.CUSTOMER_CODE = TMP.SUPPLIER_CODE
            and T.CUSTOMER_TYPE = 'CLI');

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.TEMP_FLAG   = 1,
            TMP.TEMP_REMARK = CONCAT(TMP.TEMP_REMARK,
            '申报总价或者申报单价不能同时为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND ((TMP.DEC_TOTAL IS NULL or TMP.DEC_TOTAL = '') and
            (TMP.DEC_PRICE IS NULL or TMP.DEC_PRICE = ''));

            --             UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            --             SET TMP.DEC_PRICE   = round(TMP.DEC_PRICE,5)
            --             WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            --             AND TMP.DEC_PRICE IS not null;
            --
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.DEC_PRICE = ROUND(TMP.DEC_TOTAL / TMP.QTY, 5)
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG = 0
            and TMP.DEC_PRICE IS NULL;

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.DEC_TOTAL = ROUND(TMP.DEC_PRICE * TMP.QTY, P_DEC_TOTAL_DIGIT)
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG = 0
            and TMP.DEC_TOTAL IS NULL;

            ---保税企业料号校验
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '该企业料号在保税物料中心中不存在或未备案通过|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG=0
            AND TMP.BOND_MARK = '0'
            AND (TMP.FAC_G_NO IS not NULL or TMP.FAC_G_NO != '')
            and TMP.FAC_G_NO not in
            (select T.FAC_G_NO
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND t.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and t.G_MARK = TMP.G_MARK
            and T.Status='0' and t.appr_status='8'
            and (T.TRADE_MODE is null
            or ((case when (select count(CUSTOM_PARAM_CODE) from T_BI_CUSTOMER_PARAMS where PARAMS_TYPE='TRADE_MODE' and PARAMS_CODE=TMP.TRADE_MODE)>0 then TRADE_MODE else 'A' end )
            in ((select CUSTOM_PARAM_CODE from T_BI_CUSTOMER_PARAMS where PARAMS_TYPE='TRADE_MODE' and PARAMS_CODE=TMP.TRADE_MODE),'A'))));

            --非保税企业料号校验
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '该企业料号在非保税物料中心中不存在或未内审通过|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG=0
            AND TMP.BOND_MARK = '1'
            AND NOT EXISTS (select 1
            from T_MAT_NON_BONDED T
            where t.TRADE_CODE = P_TRADE_CODE
            AND T.Appr_Status = '8'
            and t.G_MARK = TMP.G_MARK
            AND T.COP_G_NO = TMP.FAC_G_NO
            and T.Status='0');

            if P_MAT_DATA_E='1' then
            --更新保税成品、料件信息
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            set (CURR,
            DESTINATION_COUNTRY,
            DEC_PRICE) =
            (select
            NVL(TMP.CURR,H.CURR),
            NVL(TMP.DESTINATION_COUNTRY,H.COUNTRY),
            NVL(TMP.DEC_PRICE,H.DEC_PRICE)
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND t.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and t.FAC_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            WHERE TEMP_FLAG = 0
            AND TMP.BOND_MARK = '0'
            and exists (select 1
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND t.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and t.FAC_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            and TMP.TEMP_OWNER = P_TEMP_OWNER;

            ---更新非保税成品、料件信息
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            set (CURR,
            DESTINATION_COUNTRY,
            DEC_PRICE) =
            (select
            NVL(TMP.CURR,T.CURR),
            NVL(TMP.DESTINATION_COUNTRY,T.COUNTRY),
            NVL(TMP.DEC_PRICE,T.DEC_PRICE)
            from T_MAT_NON_BONDED T
            WHERE t.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            WHERE TEMP_FLAG = 0
            AND TMP.BOND_MARK = '1'
            and exists (select 1
            from T_MAT_NON_BONDED T
            WHERE t.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            and TMP.TEMP_OWNER = P_TEMP_OWNER;
            end if;

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK,
            '币制不能为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND (CURR IS NULL OR CURR = '');

            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '目的国不能为空|')
            WHERE
            TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.BOND_MARK = '0'
            AND (DESTINATION_COUNTRY IS NULL
            OR DESTINATION_COUNTRY = '');

            --更新保税成品、料件信息
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            set (G_NO,
            COP_G_NAME,
            COP_G_MODEL,
            COP_G_NO,
            ITEM_NO,
            CODE_T_S,
            G_NAME,
            G_MODEL,
            UNIT,
            COST_CENTER) =
            (select H.SERIAL_NO,
            T.COP_G_NAME,
            NVL(TMP.COP_G_MODEL,T.COP_G_MODEL),
            H.COP_G_NO,
            T.ITEM_NO,
            NVL(TMP.CODE_T_S,H.CODE_T_S),
            NVL(TMP.G_NAME,H.G_NAME),
            NVL(TMP.G_MODEL,H.G_MODEL),
            NVL(TMP.UNIT,H.UNIT),
            NVL(TMP.COST_CENTER,T.COST_CENTER)
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND t.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and t.FAC_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            WHERE TEMP_FLAG = 0
            AND TMP.BOND_MARK = '0'
            and exists (select 1
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND t.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and t.FAC_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            and TMP.TEMP_OWNER = P_TEMP_OWNER
            and TMP.TRADE_MODE in (
            --边角料内销
            '0844',
            --来料边角料内销
            '0845',
            --区内边角调出
            '5200',
            --边角料销毁
            '0400',
            --进料边角料复出
            '0864',
            --来料边角料复出
            '0865');

            --更新保税成品、料件信息,如果unit1为null，需要重新获取法一、法二
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            set (
            UNIT_1,
            UNIT_2) =
            (select
            H.UNIT_1,
            H.UNIT_2
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND t.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and t.FAC_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            WHERE TEMP_FLAG = 0
            AND TMP.BOND_MARK = '0'
            and exists (select 1
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND t.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and t.FAC_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            and TMP.TEMP_OWNER = P_TEMP_OWNER
            and TMP.UNIT_1 is null
            and TMP.TRADE_MODE in (
            --边角料内销
            '0844',
            --来料边角料内销
            '0845',
            --区内边角调出
            '5200',
            --边角料销毁
            '0400',
            --进料边角料复出
            '0864',
            --来料边角料复出
            '0865');

            --更新保税成品、料件信息
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            set (G_NO,
            CODE_T_S,
            G_NAME,
            G_MODEL,
            UNIT,
            UNIT_1,
            UNIT_2,
            COP_G_NAME,
            COP_G_MODEL,
            COP_G_NO,
            ITEM_NO,
            COST_CENTER,
            CIQ_NO) =
            (select H.SERIAL_NO,
            H.CODE_T_S,
            H.G_NAME,
            NVL(TMP.G_MODEL,H.G_MODEL),
            H.UNIT,
            H.UNIT_1,
            H.UNIT_2,
            T.COP_G_NAME,
            NVL(TMP.COP_G_MODEL,T.COP_G_MODEL),
            H.COP_G_NO,
            T.ITEM_NO,
            NVL(TMP.COST_CENTER,T.COST_CENTER),
            CIQ_NO
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND t.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and t.FAC_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            WHERE TEMP_FLAG = 0
            AND TMP.BOND_MARK = '0'
            and exists (select 1
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND t.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and t.FAC_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            and TMP.TEMP_OWNER = P_TEMP_OWNER
            and TMP.TRADE_MODE not in (
            --边角料内销
            '0844',
            --来料边角料内销
            '0845',
            --区内边角调出
            '5200',
            --边角料销毁
            '0400',
            --进料边角料复出
            '0864',
            --来料边角料复出
            '0865');

            ---更新非保税成品、料件信息
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            set (CODE_T_S,
            G_NAME,
            G_MODEL,
            UNIT,
            UNIT_1,
            UNIT_2,
            COP_G_NAME,
            COP_G_MODEL,
            COP_G_NO,
            cost_center,
            CIQ_NO) =
            (select T.CODE_T_S,
            T.G_NAME,
            NVL(TMP.G_MODEL,T.G_MODEL),
            T.UNIT,
            T.UNIT_1,
            T.UNIT_2,
            T.COP_G_NAME,
            NVL(TMP.COP_G_MODEL,T.COP_G_MODEL),
            NVL(TMP.COP_G_NO,TMP.FAC_G_NO),
            NVL(TMP.cost_center,T.cost_center),
            CIQ_NO
            from T_MAT_NON_BONDED T
            WHERE t.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            WHERE TEMP_FLAG = 0
            AND TMP.BOND_MARK = '1'
            and exists (select 1
            from T_MAT_NON_BONDED T
            WHERE t.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and t.G_MARK = TMP.G_MARK
            and t.status = '0')
            and TMP.TEMP_OWNER = P_TEMP_OWNER;



            ---境外收货人更新name
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            set SUPPLIER_NAME =
            (select t.COMPANY_NAME
            from T_BI_CLIENT_INFORMATION T
            where t.TRADE_CODE = P_TRADE_CODE
            AND T.CUSTOMER_CODE = TMP.SUPPLIER_CODE
            and T.CUSTOMER_TYPE = 'CLI')
            WHERE TEMP_FLAG = 0
            and exists (select 1
            from T_BI_CLIENT_INFORMATION T
            where t.TRADE_CODE = P_TRADE_CODE
            AND T.CUSTOMER_CODE = TMP.SUPPLIER_CODE
            and T.CUSTOMER_TYPE = 'CLI')
            and TMP.TEMP_OWNER = P_TEMP_OWNER;


            --保税，如果净重为空，则从物料中带出
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.Net_Wt = ROUND(TMP.Qty*
            (select T.Net_Wt/NVL(t.qty_wt,1)
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND T.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and T.FAC_G_NO = TMP.FAC_G_NO
            and H.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.Net_Wt IS NOT NULL),
            P_NET_WT_DIGIT)
            WHERE TMP.Net_Wt IS NULL
            AND TMP.TEMP_FLAG = 0
            AND TMP.BOND_MARK = '0'
            AND EXISTS (select 1
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND T.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and T.FAC_G_NO = TMP.FAC_G_NO
            and H.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.Net_Wt IS NOT NULL)
            AND TMP.TEMP_OWNER = P_TEMP_OWNER;

            --非保税，如果净重为空，则从物料中带出
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.Net_Wt = ROUND(TMP.Qty*
            (select T.Net_Wt/NVL(t.qty_wt,1)
            from T_MAT_NON_BONDED T
            where T.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and T.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.Net_Wt IS NOT NULL),
            P_NET_WT_DIGIT)
            WHERE TMP.Net_Wt IS NULL
            AND TMP.TEMP_FLAG = 0
            AND TMP.BOND_MARK = '1'
            AND EXISTS (select 1
            from T_MAT_NON_BONDED T
            where T.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and T.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.Net_Wt IS NOT NULL)
            AND TMP.TEMP_OWNER = P_TEMP_OWNER;

            --保税，单重生成，保证二次计算
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET single_weight = ROUND((select T.Net_Wt/NVL(t.qty_wt,1) from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND T.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and T.FAC_G_NO = TMP.FAC_G_NO
            and H.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.Net_Wt IS NOT NULL),8)
            WHERE TMP.single_weight IS NULL
            AND TMP.TEMP_FLAG = 0
            AND TMP.BOND_MARK = '0'
            AND EXISTS (select 1
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND T.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and T.FAC_G_NO = TMP.FAC_G_NO
            and H.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.Net_Wt IS NOT NULL)
            AND TMP.TEMP_OWNER = P_TEMP_OWNER;

            --非保税，单重生成，保证二次计算
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET single_weight = ROUND((select T.Net_Wt/NVL(t.qty_wt,1)
            from T_MAT_NON_BONDED T
            where T.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and T.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.Net_Wt IS NOT NULL),P_NET_WT_DIGIT)
            WHERE TMP.single_weight IS NULL
            AND TMP.TEMP_FLAG = 0
            AND TMP.BOND_MARK = '1'
            AND EXISTS (select 1
            from T_MAT_NON_BONDED T
            where T.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and T.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.Net_Wt IS NOT NULL)
            AND TMP.TEMP_OWNER = P_TEMP_OWNER;

            --保税更新法一数量
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.QTY_1 = ROUND(TMP.QTY *
            (select T.FACTOR_1
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND T.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and T.FAC_G_NO = TMP.FAC_G_NO
            and H.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and H.UNIT = TMP.UNIT
            and H.UNIT_1 = TMP.UNIT_1
            and T.FACTOR_1 IS NOT NULL),
            P_QTY1_DIGIT)
            WHERE TMP.UNIT IS NOT NULL
            AND TMP.UNIT_1 IS NOT NULL
            AND TMP.QTY IS NOT NULL
            AND TMP.QTY_1 IS NULL
            AND TMP.TEMP_FLAG = 0
            AND TMP.BOND_MARK = '0'
            AND EXISTS (select 1
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND T.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and T.FAC_G_NO = TMP.FAC_G_NO
            and H.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and H.UNIT = TMP.UNIT
            and H.UNIT_1 = TMP.UNIT_1
            and T.FACTOR_1 IS NOT NULL)
            AND TMP.TEMP_OWNER = P_TEMP_OWNER;

            --保税更新法二数量
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.QTY_2 = ROUND(TMP.QTY *
            (select T.FACTOR_2
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND T.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and T.FAC_G_NO = TMP.FAC_G_NO
            and H.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and H.UNIT = TMP.UNIT
            and H.UNIT_2 = TMP.UNIT_2
            and T.FACTOR_2 IS NOT NULL),
            P_QTY2_DIGIT)
            WHERE TMP.UNIT IS NOT NULL
            AND TMP.UNIT_2 IS NOT NULL
            AND TMP.QTY IS NOT NULL
            AND TMP.QTY_2 IS NULL
            AND TMP.TEMP_FLAG = 0
            AND TMP.BOND_MARK = '0'
            AND EXISTS (select 1
            from T_MAT_IMGEXG T, T_MAT_IMGEXG_ORG_PASS H
            where T.HEAD_ID = H.SID
            AND T.TRADE_CODE = P_TRADE_CODE
            and H.EMS_NO = TMP.EMS_NO
            and T.FAC_G_NO = TMP.FAC_G_NO
            and H.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and H.UNIT = TMP.UNIT
            and H.UNIT_2 = TMP.UNIT_2
            and T.FACTOR_2 IS NOT NULL)
            AND TMP.TEMP_OWNER = P_TEMP_OWNER;

            --非保税更新法一数量
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.QTY_1 = ROUND(TMP.QTY *
            (select T.FACTOR_1
            from T_MAT_NON_BONDED T
            where T.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and T.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.UNIT = TMP.UNIT
            and T.UNIT_1 = TMP.UNIT_1
            and T.FACTOR_1 IS NOT NULL),
            P_QTY1_DIGIT)
            WHERE TMP.UNIT IS NOT NULL
            AND TMP.UNIT_1 IS NOT NULL
            AND TMP.QTY IS NOT NULL
            AND TMP.QTY_1 IS NULL
            AND TMP.TEMP_FLAG = 0
            AND TMP.BOND_MARK = '1'
            AND EXISTS (select 1
            from T_MAT_NON_BONDED T
            where T.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and T.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.UNIT = TMP.UNIT
            and T.UNIT_1 = TMP.UNIT_1
            and T.FACTOR_1 IS NOT NULL)
            AND TMP.TEMP_OWNER = P_TEMP_OWNER;

            --非保税更新法二数量
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.QTY_2 = ROUND(TMP.QTY *
            (select T.FACTOR_2
            from T_MAT_NON_BONDED T
            where T.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and T.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.UNIT = TMP.UNIT
            and T.UNIT_2 = TMP.UNIT_2
            and T.FACTOR_2 IS NOT NULL),
            P_QTY2_DIGIT)
            WHERE TMP.UNIT IS NOT NULL
            AND TMP.UNIT_2 IS NOT NULL
            AND TMP.QTY IS NOT NULL
            AND TMP.QTY_2 IS NULL
            AND TMP.TEMP_FLAG = 0
            AND TMP.BOND_MARK = '1'
            AND EXISTS (select 1
            from T_MAT_NON_BONDED T
            where T.TRADE_CODE = P_TRADE_CODE
            and T.COP_G_NO = TMP.FAC_G_NO
            and T.G_MARK = TMP.G_MARK
            and T.APPR_STATUS = '8'
            and T.STATUS = '0'
            and T.UNIT = TMP.UNIT
            and T.UNIT_2 = TMP.UNIT_2
            and T.FACTOR_2 IS NOT NULL)
            AND TMP.TEMP_OWNER = P_TEMP_OWNER;

            --申报单位、法一单位非空且相等时，并且申报数量非空，导入的法一数量为空时，将申报数量赋给法一数量
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.QTY_1 = TMP.QTY
            WHERE TMP.UNIT IS NOT NULL
            AND TMP.UNIT_1 IS NOT NULL
            AND TMP.UNIT_1 = TMP.UNIT
            AND TMP.QTY IS NOT NULL
            AND TMP.QTY_1 IS NULL
            AND TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG = 0;

            --申报单位、法二单位非空且相等时，并且申报数量非空，导入的法二数量为空时，将申报数量赋给法二数量
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.QTY_2 = TMP.QTY
            WHERE TMP.UNIT IS NOT NULL
            AND TMP.UNIT_2 IS NOT NULL
            AND TMP.UNIT_2 = TMP.UNIT
            AND TMP.QTY IS NOT NULL
            AND TMP.QTY_2 IS NULL
            AND TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG = 0;

            --申报单位、法一单位非空时，并且申报数量非空，导入的法一数量为空时，
            --并且可以找到对应关系，将申报数量与比例因子计算后赋给法一数量
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.QTY_1 = ROUND(TMP.QTY *
            (SELECT RATE
            FROM T_BI_TRANSFORM BI
            WHERE BI.ORIGIN = TMP.UNIT
            AND BI.DEST = TMP.UNIT_1
            AND BI.TYPE = 'UNIT'
            AND (BI.TRADE_CODE = P_TRADE_CODE OR
            BI.TRADE_CODE = '9999999999')),
            P_QTY1_DIGIT)
            WHERE TMP.UNIT IS NOT NULL
            AND TMP.UNIT_1 IS NOT NULL
            AND TMP.QTY IS NOT NULL
            AND TMP.QTY_1 IS NULL
            AND EXISTS (SELECT 1
            FROM T_BI_TRANSFORM BI
            WHERE BI.ORIGIN = TMP.UNIT
            AND BI.DEST = TMP.UNIT_1
            AND BI.TYPE = 'UNIT'
            AND (BI.TRADE_CODE = P_TRADE_CODE OR
            BI.TRADE_CODE = '9999999999'))
            AND TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG = 0;

            --申报单位、法二单位非空时，并且申报数量非空，导入的法二数量为空时，
            --并且可以找到对应关系，将申报数量与比例因子计算后赋给法二数量
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.QTY_2 = ROUND(TMP.QTY *
            (SELECT RATE
            FROM T_BI_TRANSFORM BI
            WHERE BI.ORIGIN = TMP.UNIT
            AND BI.DEST = TMP.UNIT_2
            AND BI.TYPE = 'UNIT'
            AND (BI.TRADE_CODE = P_TRADE_CODE OR
            BI.TRADE_CODE = '9999999999')),
            P_QTY2_DIGIT)
            WHERE TMP.UNIT IS NOT NULL
            AND TMP.UNIT_2 IS NOT NULL
            AND TMP.QTY IS NOT NULL
            AND TMP.QTY_2 IS NULL
            AND EXISTS (SELECT 1
            FROM T_BI_TRANSFORM BI
            WHERE BI.ORIGIN = TMP.UNIT
            AND BI.DEST = TMP.UNIT_2
            AND BI.TYPE = 'UNIT'
            AND (BI.TRADE_CODE = P_TRADE_CODE OR
            BI.TRADE_CODE = '9999999999'))
            AND TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG = 0;

            --申报单位、法一单位非空时，并且法一数量为空，说明未找到对应关系，
            --如果法一单位为035，净重不为空，则将净重赋给法一数量
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.Qty_1 = TMP.NET_WT
            WHERE TMP.UNIT_1 = '035'
            AND TMP.Net_Wt IS NOT NULL
            AND TMP.UNIT IS NOT NULL
            AND TMP.UNIT_1 IS NOT NULL
            AND TMP.QTY_1 IS NULL
            AND TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG = 0;

            --申报单位、法二单位非空时，并且法二数量为空，说明未找到对应关系，
            --如果法二单位为035，净重不为空，则将净重赋给法二数量
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TMP.Qty_2 = TMP.NET_WT
            WHERE TMP.UNIT_2 = '035'
            AND TMP.Net_Wt IS NOT NULL
            AND TMP.UNIT IS NOT NULL
            AND TMP.UNIT_2 IS NOT NULL
            AND TMP.QTY_2 IS NULL
            AND TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG = 0;

            ---###保税数据,法一数量必填
            if P_IMPORT_TYPE !='4' then
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '保税数据的法一数量不能为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            AND TMP.TEMP_FLAG = 0
            AND TMP.QTY_1 is null
            AND TMP.BOND_MARK = '0';
            end if;

            if P_LIST_RE_FIELD_E_NO_BOND>0 and P_DEC_TYPE='1' then
            --非保税小提单校验，校验法二数量不能为空
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '法二数量不能为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            and TMP.UNIT_2 is not null
            AND TMP.QTY_2 is null
            AND TMP.BOND_MARK = '1';
            end if;

            if P_LIST_RE_FIELD_E>0 and P_DEC_TYPE='1' then
            --保税小提单验法二数量不能为空
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '法二数量不能为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            and TMP.UNIT_2 is not null
            AND TMP.QTY_2 is null
            AND TMP.BOND_MARK = '0';
            end if;

            if P_LIST_RE_FIELD_E>0 and P_DEC_TYPE!='1' then
            --保税小提单、大提单校验法二数量不能为空
            UPDATE T_IMP_TMP_DEC_ERP_E_LIST TMP
            SET TEMP_FLAG   = 1,
            TEMP_REMARK = CONCAT(TEMP_REMARK, '法二数量不能为空|')
            WHERE TMP.TEMP_OWNER = P_TEMP_OWNER
            and TMP.UNIT_2 is not null
            AND TMP.QTY_2 is null;
            end if;

            --法二法定单位为空清除法二数量
            UPDATE T_IMP_TMP_DEC_ERP_I_LIST TMP
            SET TMP.QTY_2 = NULL
            WHERE TMP.UNIT_2 IS NULL
            AND TMP.TEMP_OWNER = P_TEMP_OWNER;

            END IF;
            IF P_FLAG = 1 THEN
            /*--校验并导入*/

            INSERT INTO T_DEC_ERP_E_LIST_N
            (SID,
            HEAD_ID,
            EMS_LIST_NO,
            SERIAL_NO,
            G_NO,
            COP_G_NO,
            CODE_T_S,
            G_NAME,
            G_MODEL,
            UNIT,
            UNIT_1,
            UNIT_2,
            ORIGIN_COUNTRY,
            DESTINATION_COUNTRY,
            DEC_PRICE,
            DEC_TOTAL,
            USD_PRICE,
            CURR,
            QTY_1,
            QTY_2,
            FACTOR_WT,
            FACTOR_1,
            FACTOR_2,
            QTY,
            GROSS_WT,
            NET_WT,
            USE_TYPE,
            DUTY_MODE,
            EXG_VERSION,
            ENTRY_G_NO,
            NOTE,
            COP_EMS_NO,
            CLASS_MARK,
            ARRIVAL_DATE,
            INSERT_USER,
            INSERT_TIME,
            UPDATE_USER,
            UPDATE_TIME,
            BOND_MARK,
            G_MARK,
            EMS_NO,
            COP_G_NAME,
            COP_G_MODEL,
            ORDER_NO,
            INVOICE_NO,
            VOLUME,
            SUPPLIER_CODE,
            SUPPLIER_NAME,
            TRADE_MODE,
            FAC_G_NO,
            CUSTOMER_ORDER_NO,
            CUSTOMER_G_NO,
            NOTE_1,
            NOTE_2,
            NOTE_3,
            DISTRICT_CODE,
            DISTRICT_POST_CODE,
            LINKED_NO,
            LINE_NO,
            COST_CENTER,
            ITEM_NO,
            BILL_G_NO,
            IN_OUT_NO,
            data_source,
            TRADE_CODE,
            INSERT_USER_NAME,
            confirm_royalties_no,
            confirm_royalties,
            CIQ_NO,
            CUT_MODE,
            CLIENT_TOTAL,
            LABOR_COST,
            MAT_COST,
            PAY_TOTAL,
            single_weight,
            PACK_NUM,
            BATCH_NO,
            ORDER_LINE_NO,
            LIST_CARTON_NUM)
            select SID,
            HEAD_ID,
            EMS_LIST_NO,
            SERIAL_NO,
            G_NO,
            COP_G_NO,
            CODE_T_S,
            G_NAME,
            G_MODEL,
            UNIT,
            UNIT_1,
            UNIT_2,
            ORIGIN_COUNTRY,
            DESTINATION_COUNTRY,
            DEC_PRICE,
            DEC_TOTAL,
            USD_PRICE,
            CURR,
            QTY_1,
            QTY_2,
            FACTOR_WT,
            FACTOR_1,
            FACTOR_2,
            QTY,
            GROSS_WT,
            NET_WT,
            USE_TYPE,
            DUTY_MODE,
            EXG_VERSION,
            ENTRY_G_NO,
            NOTE,
            COP_EMS_NO,
            CLASS_MARK,
            ARRIVAL_DATE,
            INSERT_USER,
            INSERT_TIME,
            UPDATE_USER,
            UPDATE_TIME,
            BOND_MARK,
            G_MARK,
            EMS_NO,
            COP_G_NAME,
            COP_G_MODEL,
            ORDER_NO,
            INVOICE_NO,
            VOLUME,
            SUPPLIER_CODE,
            SUPPLIER_NAME,
            TRADE_MODE,
            FAC_G_NO,
            CUSTOMER_ORDER_NO,
            CUSTOMER_G_NO,
            NOTE_1,
            NOTE_2,
            NOTE_3,
            DISTRICT_CODE,
            DISTRICT_POST_CODE,
            LINKED_NO,
            LINE_NO,
            COST_CENTER,
            ITEM_NO,
            BILL_G_NO,
            IN_OUT_NO,
            '2',
            P_TRADE_CODE,
            INSERT_USER_NAME,
            confirm_royalties_no,
            confirm_royalties,
            CIQ_NO,
            CUT_MODE,
            CLIENT_TOTAL,
            LABOR_COST,
            MAT_COST,
            PAY_TOTAL,
            single_weight,
            PACK_NUM,
            BATCH_NO,
            ORDER_LINE_NO,
            LIST_CARTON_NUM
            FROM T_IMP_TMP_DEC_ERP_E_LIST tmp
            WHERE tmp.TEMP_FLAG = 0
            AND tmp.TEMP_OWNER = P_TEMP_OWNER;

            IF (P_AUTO_NET_WT='1') then
            UPDATE T_DEC_ERP_E_HEAD_N t set NET_WT=(select NVL (ROUND( SUM( NVL( L.NET_WT,0) ), P_AUTO_NET_WT_DIGIT), 0 )
            from T_DEC_ERP_E_LIST_N L where L.HEAD_ID=P_HEAD_ID) where SID = P_HEAD_ID;
            END IF;

            IF (P_AUTO_GROSS_WT='1') then
            UPDATE T_DEC_ERP_E_HEAD_N t set GROSS_WT=(select NVL (ROUND( SUM( NVL( L.GROSS_WT,0) ), P_AUTO_GROSS_WT_DIGIT), 0 )
            from T_DEC_ERP_E_LIST_N L where L.HEAD_ID=P_HEAD_ID) where SID = P_HEAD_ID;
            END IF;

            --计算体积
            IF(P_AUTO_VOLUME = '1') THEN
            UPDATE T_DEC_ERP_E_HEAD_N t SET t.VOLUME = ( SELECT NVL( SUM( L.VOLUME ), 0 ) FROM T_DEC_ERP_E_LIST_N L WHERE L.HEAD_ID = P_HEAD_ID ) WHERE	SID = P_HEAD_ID;
            END IF;

            SELECT COUNT(DISTINCT CURR) INTO P_COUNT FROM T_DEC_ERP_E_LIST_N WHERE HEAD_ID = P_HEAD_ID AND CURR IS NOT NULL ;
            --更新表头币制
            IF ( P_COUNT = 1 ) THEN
            UPDATE T_DEC_ERP_E_HEAD_N t
            SET CURR = ( SELECT DISTINCT CURR AS curr FROM T_DEC_ERP_E_LIST_N WHERE HEAD_ID = P_HEAD_ID AND CURR IS NOT NULL )
            WHERE t.SID = P_HEAD_ID ;
            END IF;

            --根据配置更新表头订单号栏位
            SELECT count(distinct INVOICE_NO) into P_INVOICE_NO_COUNT FROM T_DEC_ERP_E_LIST_N where HEAD_ID = P_HEAD_ID and INVOICE_NO is not null;

            if P_INVOICE_NO_COUNT>0 then
            SELECT INVOICE_NO into P_FRIST_INVOICE_NO FROM (SELECT INVOICE_NO FROM T_DEC_ERP_E_LIST_N where HEAD_ID = P_HEAD_ID and INVOICE_NO is not null ORDER BY SERIAL_NO) WHERE ROWNUM = 1;
            end if;


            if P_INVOICE_NO_COUNT>0 and P_INVOICE_NO_TYPE='1' then
            UPDATE T_DEC_ERP_E_HEAD_N t SET INVOICE_NO = P_FRIST_INVOICE_NO  WHERE t.SID = P_HEAD_ID ;
            end if;

            if P_INVOICE_NO_COUNT>0 and P_INVOICE_NO_TYPE='2' then
            UPDATE T_DEC_ERP_E_HEAD_N t
            SET INVOICE_NO = (case when P_INVOICE_NO_COUNT>1 then P_FRIST_INVOICE_NO||'等' else P_FRIST_INVOICE_NO end )
            WHERE t.SID = P_HEAD_ID ;
            end if;

            if P_INVOICE_NO_COUNT>0 and P_INVOICE_NO_TYPE='3' then
            UPDATE T_DEC_ERP_E_HEAD_N t
            SET INVOICE_NO = (SELECT SUBSTR(LISTAGG( INVOICE_NO,P_INVOICE_NO_SPLIT ) WITHIN GROUP (ORDER BY null), 1, 100) FROM (select distinct INVOICE_NO from T_DEC_ERP_E_LIST_N  WHERE HEAD_ID = P_HEAD_ID ORDER BY serial_no))
            WHERE t.SID = P_HEAD_ID ;
            end if;

            --根据配置更新表头合同号栏位
            SELECT count(distinct ORDER_NO) into P_CONTR_NO_COUNT FROM T_DEC_ERP_E_LIST_N where HEAD_ID = P_HEAD_ID and ORDER_NO is not null;
            if P_CONTR_NO_COUNT>0 then
            SELECT ORDER_NO into P_FRIST_CONTR_NO FROM (SELECT ORDER_NO FROM T_DEC_ERP_E_LIST_N where HEAD_ID = P_HEAD_ID and ORDER_NO is not null ORDER BY SERIAL_NO) WHERE ROWNUM = 1;
            end if;
            if P_CONTR_NO_COUNT>0 and P_CONTR_NO_TYPE='1' then
            UPDATE T_DEC_ERP_E_HEAD_N t
            SET CONTR_NO = SUBSTR(P_FRIST_CONTR_NO, 1, 30)
            WHERE t.SID = P_HEAD_ID ;
            end if;

            if P_CONTR_NO_COUNT>0 and P_CONTR_NO_TYPE='2' then
            UPDATE T_DEC_ERP_E_HEAD_N t
            SET CONTR_NO = (case when P_CONTR_NO_COUNT>1 then SUBSTR(P_FRIST_CONTR_NO, 1, 30)||'等' else SUBSTR(P_FRIST_CONTR_NO, 1, 32) end )
            WHERE t.SID = P_HEAD_ID ;
            end if;

            if P_CONTR_NO_COUNT>0 and P_CONTR_NO_TYPE='3' then
            UPDATE T_DEC_ERP_E_HEAD_N t
            SET CONTR_NO = (SELECT SUBSTR(LISTAGG( ORDER_NO,P_CONTR_NO_SPLIT ) WITHIN GROUP (ORDER BY null), 1, 32) FROM (select distinct ORDER_NO from T_DEC_ERP_E_LIST_N  WHERE HEAD_ID = P_HEAD_ID ORDER BY serial_no))
            WHERE t.SID = P_HEAD_ID ;
            end if;

            DELETE FROM T_IMP_TMP_DEC_ERP_E_LIST WHERE TEMP_OWNER = P_TEMP_OWNER;
            COMMIT;
            END IF;
            -- Exception
            EXCEPTION
            WHEN OTHERS THEN
            ROLLBACK;
            P_RET_CODE := SQLCODE;
            P_RET_STR  := SUBSTR(SQLERRM, 1, 200);
            end P_IMP_DEC_ERP_E_LIST;
        </createProcedure>
    </changeSet>


</databaseChangeLog>