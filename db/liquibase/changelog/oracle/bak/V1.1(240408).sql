--liquibase formatted sql

--changeset zyshen:1
alter table T_WARRING_PASS_EXPAND_CONFIG add CODE_CHECK VARCHAR2(1) default 0;
comment on column T_WARRING_PASS_EXPAND_CONFIG.CODE_CHECK is 'HSCODE关联校验 0：不校验 1：校验';

--changeset zyshen:2
comment on column T_WARRING_PASS_EXPAND_CONFIG.CODE_CHECK is '企业物料发送内审时校验监管条件或检验检疫';
alter table T_WARRING_PASS_EXPAND_CONFIG add CODE_BILL_CHECK VARCHAR2(1) default 0;
comment on column T_WARRING_PASS_EXPAND_CONFIG.CODE_BILL_CHECK is '预录入单生成草单时校验监管条件或检验检疫 0：不校验 1：校验';

--changeset zyshen:3
alter table T_WARRING_PASS_EXPAND_CONFIG add DEC_PRICE_DIGIT NUMBER default 4;
comment on column T_WARRING_PASS_EXPAND_CONFIG.DEC_PRICE_DIGIT is '物料中心-企业料件/成品申报单价小数设置';

--changeset zyshen:4
alter table T_MAT_IMGEXG  modify DEC_PRICE NUMBER(19, 8);
