--liquibase formatted sql


--changeset zyshen:1
ALTER TABLE T_DEC_ERP_E_LIST_N
    MODIFY (CUSTOMER_ORDER_NO NVARCHAR2(100));

--changeset zhuzd:1
ALTER TABLE t_mrp_price ADD rmb_dec_price numeric(19,5);
ALTER TABLE t_mrp_price ADD rmb_fac_dec_price numeric(19,5);
COMMENT ON COLUMN t_mrp_price.rmb_dec_price IS '单价RMB';
COMMENT ON COLUMN t_mrp_price.rmb_fac_dec_price IS '企业料号单价RMB';

--changeset wj:1
ALTER TABLE t_gwstd_entry_head_third ADD i_e_port_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.i_e_port_name IS '进出境关别名称';
ALTER TABLE t_gwstd_entry_head_third ADD cut_mode_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.cut_mode_name IS '征免性质名称';
ALTER TABLE t_gwstd_entry_head_third ADD trade_mode_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.trade_mode_name IS '监管方式名称';
ALTER TABLE t_gwstd_entry_head_third ADD trade_area_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.trade_area_name IS '贸易国名称';
ALTER TABLE t_gwstd_entry_head_third ADD trade_country_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.trade_country_name IS '启运国/运抵国名称';
ALTER TABLE t_gwstd_entry_head_third ADD distinate_port_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.distinate_port_name IS '指运港/经停港名称';
ALTER TABLE t_gwstd_entry_head_third ADD entry_port_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.entry_port_name IS '进出境口岸';
ALTER TABLE t_gwstd_entry_head_third ADD wrap_type_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.wrap_type_name IS '包装种类名称';
ALTER TABLE t_gwstd_entry_head_third ADD trans_mode_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.trans_mode_name IS '成交方式名称';
ALTER TABLE t_gwstd_entry_head_third ADD confirm_formula_price varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.confirm_formula_price IS '公式定价确认';
ALTER TABLE t_gwstd_entry_head_third ADD confirm_temp_price varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.confirm_temp_price IS '暂定价格确认';
ALTER TABLE t_gwstd_entry_head_third ADD duty_self varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.duty_self IS '自报自缴';
ALTER TABLE t_gwstd_entry_head_third ADD acmp_no varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_head_third.acmp_no IS '随附单证及编号';
ALTER TABLE t_gwstd_entry_head_third ADD ocr_status varchar2(1);
COMMENT ON COLUMN t_gwstd_entry_head_third.ocr_status IS '识别状态';

ALTER TABLE t_gwstd_entry_list_third ADD origin_country_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_list_third.origin_country_name IS '产销国名称';
ALTER TABLE t_gwstd_entry_list_third ADD destination_country_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_list_third.destination_country_name IS '最终目的国名称';
ALTER TABLE t_gwstd_entry_list_third ADD duty_mode_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_list_third.duty_mode_name IS '征减免税方式名称';
ALTER TABLE t_gwstd_entry_list_third ADD district_code_name varchar2(100);
COMMENT ON COLUMN t_gwstd_entry_list_third.district_code_name IS '境内货源地/境内目的地名称';

ALTER TABLE t_gw_ocr_log ADD extend_filed1 varchar2(100);
COMMENT ON COLUMN t_gw_ocr_log.extend_filed1 IS '备用字段1(预留)';

--changeset wj:2
CREATE TABLE t_gwstd_entry_head_third_loc (
                                              sid varchar2(40) NOT NULL,
                                              trade_code varchar2(10),
                                              agent_code_scc_loc varchar2(400),
                                              agent_name_loc varchar2(400),
                                              bill_no_loc varchar2(400),
                                              trade_code_scc_loc varchar2(400),
                                              trade_name_loc varchar2(400),
                                              overseas_trade_code_loc varchar2(400),
                                              contr_no_loc varchar2(400),
                                              cut_mode_loc varchar2(400),
                                              desp_port_code_loc varchar2(400),
                                              distinate_port_loc varchar2(400),
                                              d_date_loc varchar2(400),
                                              entry_id_loc varchar2(400),
                                              freight_loc varchar2(400),
                                              gross_wt_loc varchar2(400),
                                              insurance_loc varchar2(400),
                                              i_e_port_loc varchar2(400),
                                              i_e_date_loc varchar2(400),
                                              manual_no_loc varchar2(400),
                                              master_customs_loc varchar2(400),
                                              net_wt_loc varchar2(400),
                                              note_loc varchar2(400),
                                              other_loc varchar2(400),
                                              owner_scc_loc varchar2(400),
                                              owner_name_loc varchar2(400),
                                              pack_no_loc varchar2(400),
                                              trade_mode_loc varchar2(400),
                                              trade_country_loc varchar2(400),
                                              traf_name_loc varchar2(400),
                                              trans_mode_loc varchar2(400),
                                              voyage_no_loc varchar2(400),
                                              wrap_type_loc varchar2(400),
                                              license_no_loc varchar2(400),
                                              traf_mode_loc varchar2(400),
                                              trade_area_code_loc varchar2(400),
                                              confirm_special_loc varchar2(400),
                                              confirm_price_loc varchar2(400),
                                              confirm_royalties_loc varchar2(400),
                                              entry_port_code_loc varchar2(400),
                                              insert_user varchar2(30),
                                              insert_user_name varchar2(30),
                                              insert_time Date,
                                              update_user varchar2(30),
                                              update_user_name varchar2(30),
                                              update_time Date,
                                              i_e_port_name_loc varchar2(400),
                                              cut_mode_name_loc varchar2(400),
                                              trade_mode_name_loc varchar2(400),
                                              trade_area_name_loc varchar2(400),
                                              trade_country_name_loc varchar2(400),
                                              distinate_port_name_loc varchar2(400),
                                              entry_port_name_loc varchar2(400),
                                              wrap_type_name_loc varchar2(400),
                                              trans_mode_name_loc varchar2(400),
                                              confirm_formula_price_loc varchar2(400),
                                              confirm_temp_price_loc varchar2(400),
                                              duty_self_loc varchar2(400),
                                              acmp_no_loc varchar2(400)
)
;
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.sid IS 'ID';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trade_code IS '企业编码';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.agent_code_scc_loc IS '申报单位社会信用代码';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.agent_name_loc IS '申报单位名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.bill_no_loc IS '提运单号码';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trade_code_scc_loc IS '境内收发货人社会信用代码';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trade_name_loc IS '境内收发货人名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.overseas_trade_code_loc IS '境外收发货人编码';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.contr_no_loc IS '合同协议号';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.cut_mode_loc IS '征免性质编码';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.desp_port_code_loc IS '启运港(进口)/离境口岸(出口)';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.distinate_port_loc IS '指运港/经停港(名称）';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.d_date_loc IS '申报日期';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.entry_id_loc IS '海关编号';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.freight_loc IS '运费';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.gross_wt_loc IS '毛重';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.insurance_loc IS '保费';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.i_e_port_loc IS '进出口岸代码（进境关别（出境关别））';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.i_e_date_loc IS '进出口日期';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.manual_no_loc IS '手册号码（备案号）';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.master_customs_loc IS '申报地海关';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.net_wt_loc IS '净重';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.note_loc IS '备注';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.other_loc IS '杂费';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.owner_scc_loc IS '货主单位/消费使用单位（18位社会信用编码）--进口 \生产销售单位（18位社会信用编码）--出口';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.owner_name_loc IS '货主单位名称（消费使用单位）';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.pack_no_loc IS '件数';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trade_mode_loc IS '监管方式';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trade_country_loc IS '启运国/运抵国';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.traf_name_loc IS '运输工具名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trans_mode_loc IS '成交方式';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.voyage_no_loc IS '运输工具航次(班)号';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.wrap_type_loc IS '包装种类';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.license_no_loc IS '许可证编号';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.traf_mode_loc IS '运输方式代码';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trade_area_code_loc IS '贸易国别（地区）代码';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.confirm_special_loc IS '特殊关系确认';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.confirm_price_loc IS '价格影响确认';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.confirm_royalties_loc IS '支付特许权使用费确认';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.entry_port_code_loc IS '入境口岸(进口专用)';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.insert_user IS '创建人';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.insert_user_name IS '创建人名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.insert_time IS '创建时间';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.update_user IS '修改人';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.update_user_name IS '修改人名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.update_time IS '修改时间';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.i_e_port_name_loc IS '进出境关别名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.cut_mode_name_loc IS '征免性质名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trade_mode_name_loc IS '监管方式名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trade_area_name_loc IS '贸易国名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trade_country_name_loc IS '启运国/运抵国名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.distinate_port_name_loc IS '指运港/经停港名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.entry_port_name_loc IS '进出境口岸';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.wrap_type_name_loc IS '包装种类名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.trans_mode_name_loc IS '成交方式名称';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.confirm_formula_price_loc IS '公式定价确认';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.confirm_temp_price_loc IS '暂定价格确认';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.duty_self_loc IS '自报自缴';
COMMENT ON COLUMN t_gwstd_entry_head_third_loc.acmp_no_loc IS '随附单证及编号';
COMMENT ON TABLE t_gwstd_entry_head_third_loc IS '报关单表头(第三方系统向单一窗口申报前的草单数据)';

ALTER TABLE t_gwstd_entry_head_third_loc add PRIMARY KEY (sid) using index;

CREATE TABLE t_gwstd_entry_list_third_loc (
                                              sid varchar2(40) NOT NULL,
                                              trade_code varchar2(10),
                                              head_id varchar2(40),
                                              code_ts_loc varchar2(400),
                                              curr_loc varchar2(400),
                                              decl_price_loc varchar2(400),
                                              decl_total_loc varchar2(400),
                                              destination_country_loc varchar2(400),
                                              district_code_loc varchar2(400),
                                              g_name_loc varchar2(400),
                                              g_model_loc varchar2(400),
                                              g_no_loc varchar2(400),
                                              g_qty_loc varchar2(400),
                                              g_unit_loc varchar2(400),
                                              origin_country_loc varchar2(400),
                                              qty_1_loc varchar2(400),
                                              unit_1_loc varchar2(400),
                                              qty_2_loc varchar2(400),
                                              unit_2_loc varchar2(400),
                                              duty_mode_loc varchar2(400),
                                              insert_user varchar2(30),
                                              insert_user_name varchar2(30),
                                              insert_time Date,
                                              update_user varchar2(30),
                                              update_user_name varchar2(30),
                                              update_time Date,
                                              origin_country_name_loc varchar2(400),
                                              destination_country_name_loc varchar2(400),
                                              duty_mode_name_loc varchar2(400),
                                              district_code_name_loc varchar2(400)
);
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.sid IS 'ID';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.trade_code IS '企业编码';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.head_id IS '表头主键';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.code_ts_loc IS '商品编号';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.curr_loc IS '成交币制';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.decl_price_loc IS '申报单价';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.decl_total_loc IS '申报总价';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.destination_country_loc IS '最终目的国';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.district_code_loc IS '目的地（产地）代码-行政区域';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.g_name_loc IS '商品名称';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.g_model_loc IS '商品规格、型号';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.g_no_loc IS '商品序号';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.g_qty_loc IS '申报数量';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.g_unit_loc IS '申报计量单位';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.origin_country_loc IS '产销国';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.qty_1_loc IS '第一（法定）数量';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.unit_1_loc IS '第一(法定)计量单位';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.qty_2_loc IS '第二数量';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.unit_2_loc IS '第二计量单位';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.duty_mode_loc IS '征减免税方式';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.insert_user IS '创建人';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.insert_user_name IS '创建人名称';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.insert_time IS '创建时间';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.update_user IS '修改人';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.update_user_name IS '修改人名称';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.update_time IS '修改时间';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.origin_country_name_loc IS '产销国名称';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.destination_country_name_loc IS '最终目的国名称';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.duty_mode_name_loc IS '征减免税方式名称';
COMMENT ON COLUMN t_gwstd_entry_list_third_loc.district_code_name_loc IS '境内货源地/境内目的地名称';
COMMENT ON TABLE t_gwstd_entry_list_third_loc IS '报关单表体(第三方系统向单一窗口申报前的草单数据)';

ALTER TABLE t_gwstd_entry_list_third_loc add PRIMARY KEY (sid) using index;

--changeset zyshen:2
ALTER TABLE T_IMP_TMP_DEC_ERP_E_LIST
    ADD (BATCH_NO VARCHAR2(150));
COMMENT ON COLUMN T_IMP_TMP_DEC_ERP_E_LIST.BATCH_NO IS '批次号'

--changeset chencheng:1
alter table T_MRP_PRICE
    modify rmb_dec_total NUMBER(19, 6);

alter table T_MRP_PRICE
    modify rmb_dec_price NUMBER(19, 6);

alter table T_MRP_PRICE
    modify rmb_fac_dec_price NUMBER(19, 6);
