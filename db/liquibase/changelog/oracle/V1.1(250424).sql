--liquibase formatted sql

--changeset jp:1
ALTER TABLE "T_MAT_IMGEXG_ORG_PASS"
ADD ("INSPMONITORCOND" VARCHAR2(50 BYTE));

COMMENT ON COLUMN "T_MAT_IMGEXG_ORG_PASS"."INSPMONITORCOND" IS '检验检疫';

ALTER TABLE "T_MAT_IMGEXG_ORG"
ADD ("INSPMONITORCOND" VARCHAR2(50 BYTE));

COMMENT ON COLUMN "T_MAT_IMGEXG_ORG"."INSPMONITORCOND" IS '检验检疫';

ALTER TABLE "T_IMP_TMP_MAT_IMGEXG_ORG"
ADD ("INSPMONITORCOND" VARCHAR2(50 BYTE));

COMMENT ON COLUMN "T_IMP_TMP_MAT_IMGEXG_ORG"."INSPMONITORCOND" IS '检验检疫';

--changeset jp:2
ALTER TABLE "T_WARRING_PASS_EXPAND_CONFIG" 
ADD ("TWO_STEP_DECLARATION_MODE" VARCHAR2(1));

COMMENT ON COLUMN "T_WARRING_PASS_EXPAND_CONFIG"."TWO_STEP_DECLARATION_MODE" IS '两步申报新模式 1新 0旧';

update t_warring_pass_expand_config set two_step_declaration_mode = '0';