--liquibase formatted sql

--changeset jp:1
INSERT INTO t_gwstd_http_config ("SID", "BASE_URL", "SERVICE_URL", "TOKEN", "TYPE", "EXTEND_FILED1", "EXTEND_FILED2", "EXTEND_FILED3", "NOTE", "TRADE_CODE", "INSERT_USER", "INSERT_TIME", "UPDATE_USER", "UPDATE_TIME", "INSERT_USER_NAME", "UPDATE_USER_NAME") VALUES ('798FE60CAE0A459FB69D1FB343333344', 'http://192.168.10.134:28885', '/ems/api/v1/cus/getImgLockedList', NULL, 'EMS_IMG_LOCKED_URL', NULL, NULL, NULL, '帐册料件备案锁定信息', '9999999999', 'system', NULL, NULL, NULL, NULL, NULL);
INSERT INTO t_gwstd_http_config ("SID", "BASE_URL", "SERVICE_URL", "TOKEN", "TYPE", "EXTEND_FILED1", "EXTEND_FILED2", "EXTEND_FILED3", "NOTE", "TRADE_CODE", "INSERT_USER", "INSERT_TIME", "UPDATE_USER", "UPDATE_TIME", "INSERT_USER_NAME", "UPDATE_USER_NAME") VALUES ('798FE60CAE0A459FB69D1FB343333355', 'http://192.168.10.134:28885', '/ems/api/v1/cus/getExgLockedList', NULL, 'EMS_EXG_LOCKED_URL', NULL, NULL, NULL, '帐册成品备案锁定信息', '9999999999', 'system', NULL, NULL, NULL, NULL, NULL);