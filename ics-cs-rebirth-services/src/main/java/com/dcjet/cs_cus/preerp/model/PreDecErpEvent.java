package com.dcjet.cs_cus.preerp.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import javax.annotation.Nonnull;

@Setter
@Getter
@ToString
public class PreDecErpEvent extends ApplicationEvent {


    private PreDecErpApprove approve;


    /***
     *
     * @param approve
     */
    public PreDecErpEvent(@Nonnull PreDecErpApprove approve) {
        super(approve);
        this.approve = approve;
    }

}
