package com.dcjet.cs_cus.preerp.mapper;

import com.dcjet.cs.dto.erp.DecErpIHeadNParam;
import com.dcjet.cs.dto.erp.DecInsertListI;
import com.dcjet.cs.dto_cus.preerp.PreDecErpHeadDto;
import com.dcjet.cs.dto_cus.preerp.PreDecErpHeadParam;
import com.dcjet.cs.dto_cus.preerp.PreDecErpHeadSearchParam;
import com.dcjet.cs_cus.preerp.model.PreDecErpHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-3-8
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PreDecErpHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    PreDecErpHeadDto toDto(PreDecErpHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    PreDecErpHead toPo(PreDecErpHeadParam param);

    /***
     *
     * @param param
     * @return
     */
    PreDecErpHead toPo(PreDecErpHeadSearchParam param);
    /**
     * 数据库原始数据更新
     * @param preDecErpHeadParam
     * @param preDecErpHead
     */
    void updatePo(PreDecErpHeadParam preDecErpHeadParam, @MappingTarget PreDecErpHead preDecErpHead);


    default DecInsertListI toDec(PreDecErpHead head) {
        DecInsertListI dec = new DecInsertListI();
        dec.setTradeCode(head.getTradeCode());
        dec.setAttachedHeadId(head.getSid());
        DecErpIHeadNParam decHeadParam = new DecErpIHeadNParam();
        dec.setDecErpIHeadNParam(decHeadParam);
        decHeadParam.setPreEmsListNo(head.getPreEmsListNo());
        decHeadParam.setBondMark("1");
        decHeadParam.setOwnerCode(head.getTradeCode());
        decHeadParam.setReceiveCode(head.getTradeCode());
        decHeadParam.setForwardCode(head.getForwardCode());
        decHeadParam.setTradeCode(head.getTradeCode());
        decHeadParam.setOverseasShipper(head.getOverseasShipper());
        /*if (!Strings.isNullOrEmpty(head.getMawb())) {
            decHeadParam.setHawb(head.getMawb() + "_" + head.getHawb());
        } else {
            decHeadParam.setHawb(head.getHawb());
        }*/
        decHeadParam.setMawb(head.getMawb());
        decHeadParam.setHawb(head.getHawb());

        decHeadParam.setTrafMode(head.getTrafMode());
        decHeadParam.setWrapType(head.getWrapType());
        decHeadParam.setPackNum(head.getPackNum());
        decHeadParam.setCweight(head.getWeight());
        decHeadParam.setVolume(head.getVolume());
        decHeadParam.setGrossWt(head.getGrossWt());
        decHeadParam.setNetWt(head.getNetWt());
        decHeadParam.setDeclareCode(head.getCustomerCode());
        decHeadParam.setTradeCountry(head.getTradeCountry());
        decHeadParam.setFeeMark("3");
        decHeadParam.setFeeCurr(head.getFeeCurr());
        decHeadParam.setFeeRate(head.getFeeRate());
        decHeadParam.setOtherMark("3");
        decHeadParam.setOtherCurr(head.getOtherCurr());
        decHeadParam.setOtherRate(head.getOtherRate());
        decHeadParam.setDespPort(head.getDespPort());
        decHeadParam.setDestPort(head.getDestPort());
        decHeadParam.setEntryPort(head.getEntryPort());
        decHeadParam.setSa(head.getSa());
        decHeadParam.setNote(head.getNote());
        decHeadParam.setArrivalPortDate(head.getEtaDate());

        return dec;
    }
}
