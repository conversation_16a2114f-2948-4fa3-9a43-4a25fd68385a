package com.dcjet.cs_cus.panasonic.mapper;

import com.dcjet.cs.dto_cus.panasonic.DecErpEFreightListDto;
import com.dcjet.cs.dto_cus.panasonic.DecErpEFreightListParam;
import com.dcjet.cs_cus.panasonic.model.DecErpEFreightList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-7-13
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecErpEFreightListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecErpEFreightListDto toDto(DecErpEFreightList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecErpEFreightList toPo(DecErpEFreightListParam param);
    /**
     * 数据库原始数据更新
     * @param decErpEFreightListParam
     * @param decErpEFreightList
     */
    void updatePo(DecErpEFreightListParam decErpEFreightListParam, @MappingTarget DecErpEFreightList decErpEFreightList);
    default void patchPo(DecErpEFreightListParam decErpEFreightListParam, DecErpEFreightList decErpEFreightList) {
        // TODO 自行实现局部更新
    }
}
