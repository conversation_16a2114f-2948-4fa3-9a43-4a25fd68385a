<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs_cus.sj.dao.GwstdRequisitionManagementMapper">
    <resultMap id="gwstdRequisitionManagementResultMap" type="com.dcjet.cs_cus.sj.model.GwstdRequisitionManagement">
        <id column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="use_date" property="useDate" jdbcType="TIMESTAMP"/>
        <result column="use_type" property="useType" jdbcType="VARCHAR"/>
        <result column="fac_g_no" property="facGNo" jdbcType="VARCHAR"/>
        <result column="g_name" property="gName" jdbcType="VARCHAR"/>
        <result column="qty" property="qty" jdbcType="NUMERIC"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="note" property="note" jdbcType="VARCHAR"/>
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        sid
        ,trade_code
     ,use_date
     ,use_type
     ,fac_g_no
     ,g_name
     ,qty
     ,unit
     ,note
     ,insert_user
     ,insert_user_name
     ,insert_time
     ,update_user
     ,update_user_name
     ,update_time
    </sql>
    <sql id="condition">
        trade_code =#{tradeCode}
        <if test="facGNo != null and facGNo != ''">
            and fac_g_no like concat(concat('%', #{facGNo}), '%')
        </if>
        <if test="useType != null and useType != ''">
            and use_type = #{useType}
        </if>
        <if test='_databaseId == "postgresql" '>
            <if test="useDateFrom != null and useDateFrom != ''">
                <![CDATA[ and use_date >= to_timestamp(#{useDateFrom}, 'yyyy-MM-dd')]]>
            </if>
            <if test="useDateTo != null and useDateTo != ''">
                <![CDATA[ and use_date < to_timestamp(#{useDateTo},'yyyy-MM-dd') + INTERVAL '1 day']]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="useDateFrom != null and useDateFrom != ''">
                <![CDATA[ and use_date >= to_date(#{useDateFrom}, 'yyyy-MM-dd')]]>
            </if>
            <if test="useDateTo != null and useDateTo != ''">
                <![CDATA[ and use_date < to_date(#{useDateTo}, 'yyyy-MM-dd')+1]]>
            </if>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwstdRequisitionManagementResultMap"
            parameterType="com.dcjet.cs_cus.sj.model.GwstdRequisitionManagement">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_gwstd_requisition_management t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.use_date desc
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_gwstd_requisition_management t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>
