package com.dcjet.cs_cus.cusSt.service;

import com.dcjet.cs.dto_cus.cusSt.VTaxCipSelectDto;
import com.dcjet.cs.dto_cus.cusSt.VTaxCipSelectParam;
import com.dcjet.cs_cus.cusSt.dao.VTaxCipSelectMapper;
import com.dcjet.cs_cus.cusSt.mapper.VTaxCipSelectDtoMapper;
import com.dcjet.cs_cus.cusSt.model.VTaxCipSelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-7-27
 */
@Service
public class VTaxCipSelectService extends BaseService<VTaxCipSelect> {
    @Resource
    private VTaxCipSelectMapper vTaxCipSelectMapper;
    @Resource
    private VTaxCipSelectDtoMapper vTaxCipSelectDtoMapper;
    @Override
    public Mapper<VTaxCipSelect> getMapper() {
        return vTaxCipSelectMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param vTaxCipSelectParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<VTaxCipSelectDto>> getListPaged(VTaxCipSelectParam vTaxCipSelectParam, PageParam pageParam) {
        // 启用分页查询
        VTaxCipSelect vTaxCipSelect = vTaxCipSelectDtoMapper.toPo(vTaxCipSelectParam);
        Page<VTaxCipSelect> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vTaxCipSelectMapper.getList(vTaxCipSelect));
        List<VTaxCipSelectDto> vTaxCipSelectDtos = page.getResult().stream().map(head -> {
            VTaxCipSelectDto dto = vTaxCipSelectDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<VTaxCipSelectDto>> paged = ResultObject.createInstance(vTaxCipSelectDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<VTaxCipSelectDto> selectAll(VTaxCipSelectParam exportParam, UserInfoToken userInfo) {
        VTaxCipSelect vTaxCipSelect = vTaxCipSelectDtoMapper.toPo(exportParam);
        // vTaxCipSelect.setTradeCode(userInfo.getCompany());
        List<VTaxCipSelectDto> vTaxCipSelectDtos = new ArrayList<>();
        List<VTaxCipSelect> vTaxCipSelects = vTaxCipSelectMapper.getList(vTaxCipSelect);
        if (CollectionUtils.isNotEmpty(vTaxCipSelects)) {
            vTaxCipSelectDtos = vTaxCipSelects.stream().map(head -> {
                VTaxCipSelectDto dto = vTaxCipSelectDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return vTaxCipSelectDtos;
    }
}
