<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs_cus.cusSt.dao.ImpTaxConInvPackMapper">
    <resultMap id="impTaxConInvPackResultMap" type="com.dcjet.cs_cus.cusSt.model.ImpTaxConInvPack">
		<result column="M_DATE" property="mDate" jdbcType="TIMESTAMP" />
		<result column="BILL_NO" property="billNo" jdbcType="VARCHAR" />
		<result column="PO" property="po" jdbcType="VARCHAR" />
		<result column="K_QTY" property="kQty" jdbcType="NUMERIC" />
		<result column="C_COP_G_NO" property="cCopGNo" jdbcType="VARCHAR" />
		<result column="C_G_NO" property="cGNo" jdbcType="NUMERIC" />
		<result column="G_MARK" property="gMark" jdbcType="VARCHAR" />
		<result column="QTY1" property="qty1" jdbcType="NUMERIC" />
		<result column="UNIT" property="unit" jdbcType="VARCHAR" />
		<result column="NET" property="net" jdbcType="NUMERIC" />
		<result column="GROSS" property="gross" jdbcType="NUMERIC" />
		<result column="DEC_TOTAL" property="decTotal" jdbcType="NUMERIC" />
		<result column="DEC_CURR" property="decCurr" jdbcType="VARCHAR" />
		<result column="SAP_COP_G_NO" property="sapCopGNo" jdbcType="VARCHAR" />
		<result column="COP_G_NO" property="copGNo" jdbcType="VARCHAR" />
		<result column="QTY2" property="qty2" jdbcType="NUMERIC" />
		<result column="CUST_ID" property="custId" jdbcType="VARCHAR" />
		<result column="CUST_NAME" property="custName" jdbcType="VARCHAR" />
		<result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR" />
		<result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR" />
		<result column="PACKING_NO" property="packingNo" jdbcType="VARCHAR" />
		<result column="G_NAME_CH" property="gNameCh" jdbcType="VARCHAR" />
		<result column="DEC_P1" property="decP1" jdbcType="NUMERIC" />
		<result column="DEC_P2" property="decP2" jdbcType="NUMERIC" />
		<result column="G_NAME_EN" property="gNameEn" jdbcType="VARCHAR" />
		<result column="MERGE_ID" property="mergeId" jdbcType="NUMERIC" />
		<result column="SEQ" property="seq" jdbcType="NUMERIC" />
		<result column="IS_XMY" property="isXmy" jdbcType="VARCHAR" />
		<result column="C_COP_G_NO_B" property="cCopGNoB" jdbcType="VARCHAR" />
		<result column="C_G_NO_B" property="cGNoB" jdbcType="NUMERIC" />
		<result column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
		<result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="EMS_NO" property="emsNo" jdbcType="VARCHAR" />
		<result column="TEMP_FLAG" property="tempFlag" jdbcType="NUMERIC" />
		<result column="TEMP_INDEX" property="tempIndex" jdbcType="NUMERIC" />
		<result column="TEMP_OWNER" property="tempOwner" jdbcType="VARCHAR" />
		<result column="TEMP_MARK" property="tempMark" jdbcType="NUMERIC" />
		<result column="TEMP_REMARK" property="tempRemark" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     M_DATE
     ,BILL_NO
     ,PO
     ,K_QTY
     ,C_COP_G_NO
     ,C_G_NO
     ,G_MARK
     ,QTY1
     ,UNIT
     ,NET
     ,GROSS
     ,DEC_TOTAL
     ,DEC_CURR
     ,SAP_COP_G_NO
     ,COP_G_NO
     ,QTY2
     ,CUST_ID
     ,CUST_NAME
     ,CONTRACT_NO
     ,INVOICE_NO
     ,PACKING_NO
     ,G_NAME_CH
     ,DEC_P1
     ,DEC_P2
     ,G_NAME_EN
     ,MERGE_ID
     ,SEQ
     ,IS_XMY
     ,C_COP_G_NO_B
     ,C_G_NO_B
     ,SID
     ,TRADE_CODE
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,EMS_NO
     ,TEMP_FLAG
     ,TEMP_INDEX
     ,TEMP_OWNER
     ,TEMP_MARK
     ,TEMP_REMARK
    </sql>
    <sql id="condition">
     <if test="billNo != null and billNo != ''">
		and BILL_NO = #{billNo}
	</if>
    <if test="po != null and po != ''"> 
		and PO = #{po}
	</if>
    <if test="kQty != null and kQty != ''"> 
		and K_QTY = #{kQty}
	</if>
    <if test="cCopGNo != null and cCopGNo != ''"> 
		and C_COP_G_NO = #{cCopGNo}
	</if>
    <if test="cGNo != null and cGNo != ''"> 
		and C_G_NO = #{cGNo}
	</if>
    <if test="gMark != null and gMark != ''"> 
		and G_MARK = #{gMark}
	</if>
    <if test="qty1 != null and qty1 != ''"> 
		and QTY1 = #{qty1}
	</if>
    <if test="unit != null and unit != ''"> 
		and UNIT = #{unit}
	</if>
    <if test="net != null and net != ''"> 
		and NET = #{net}
	</if>
    <if test="gross != null and gross != ''"> 
		and GROSS = #{gross}
	</if>
    <if test="decTotal != null and decTotal != ''"> 
		and DEC_TOTAL = #{decTotal}
	</if>
    <if test="decCurr != null and decCurr != ''"> 
		and DEC_CURR = #{decCurr}
	</if>
    <if test="sapCopGNo != null and sapCopGNo != ''"> 
		and SAP_COP_G_NO = #{sapCopGNo}
	</if>
    <if test="copGNo != null and copGNo != ''"> 
		and COP_G_NO = #{copGNo}
	</if>
    <if test="qty2 != null and qty2 != ''"> 
		and QTY2 = #{qty2}
	</if>
    <if test="custId != null and custId != ''"> 
		and CUST_ID = #{custId}
	</if>
    <if test="custName != null and custName != ''"> 
		and CUST_NAME = #{custName}
	</if>
    <if test="contractNo != null and contractNo != ''"> 
		and CONTRACT_NO = #{contractNo}
	</if>
    <if test="invoiceNo != null and invoiceNo != ''"> 
		and INVOICE_NO = #{invoiceNo}
	</if>
    <if test="packingNo != null and packingNo != ''"> 
		and PACKING_NO = #{packingNo}
	</if>
    <if test="gNameCh != null and gNameCh != ''"> 
		and G_NAME_CH = #{gNameCh}
	</if>
    <if test="decP1 != null and decP1 != ''"> 
		and DEC_P1 = #{decP1}
	</if>
    <if test="decP2 != null and decP2 != ''"> 
		and DEC_P2 = #{decP2}
	</if>
    <if test="gNameEn != null and gNameEn != ''"> 
		and G_NAME_EN = #{gNameEn}
	</if>
    <if test="mergeId != null and mergeId != ''"> 
		and MERGE_ID = #{mergeId}
	</if>
    <if test="seq != null and seq != ''"> 
		and SEQ = #{seq}
	</if>
    <if test="isXmy != null and isXmy != ''"> 
		and IS_XMY = #{isXmy}
	</if>
    <if test="cCopGNoB != null and cCopGNoB != ''"> 
		and C_COP_G_NO_B = #{cCopGNoB}
	</if>
    <if test="cGNoB != null and cGNoB != ''"> 
		and C_G_NO_B = #{cGNoB}
	</if>
    <if test="tradeCode != null and tradeCode != ''"> 
		and TRADE_CODE = #{tradeCode}
	</if>
    <if test="emsNo != null and emsNo != ''"> 
		and EMS_NO = #{emsNo}
	</if>
    <if test="tempFlag != null and tempFlag != ''"> 
		and TEMP_FLAG = #{tempFlag}
	</if>
    <if test="tempIndex != null and tempIndex != ''"> 
		and TEMP_INDEX = #{tempIndex}
	</if>
    <if test="tempOwner != null and tempOwner != ''"> 
		and TEMP_OWNER = #{tempOwner}
	</if>
    <if test="tempMark != null and tempMark != ''"> 
		and TEMP_MARK = #{tempMark}
	</if>
    <if test="tempRemark != null and tempRemark != ''"> 
		and TEMP_REMARK = #{tempRemark}
	</if>
    </sql>
	<sql id="condition_pg">
		<if test="mDateFrom != null and mDateFrom != ''">
			<![CDATA[ and M_DATE >=  to_timestamp(#{mDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
		</if>
		<if test="mDateTo != null and mDateTo != ''">
			<![CDATA[ and M_DATE < to_timestamp(#{mDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day' ]]>
		</if>
	</sql>
	<sql id="condition_oracle">
		<if test="mDateFrom != null and mDateFrom != ''">
			<![CDATA[ and M_DATE >= to_date(#{mDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
		</if>
		<if test="mDateTo != null and mDateTo != ''">
			<![CDATA[ and M_DATE <= to_date(#{mDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
		</if>
	</sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="impTaxConInvPackResultMap" parameterType="com.dcjet.cs_cus.cusSt.model.ImpTaxConInvPack">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_IMP_TAX_CON_INV_PACK t
        <where>
            <include refid="condition"></include>
			<if test='_databaseId == "postgresql" '>
				<include refid="condition_pg"></include>
			</if>
			<if test='_databaseId != "postgresql" '>
				<include refid="condition_oracle"></include>
			</if>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_IMP_TAX_CON_INV_PACK t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

	<select id="checkTmpByParam" parameterMap="checkTmpMap" statementType="CALLABLE" resultType="java.util.Map" >
      CALL P_IMP_TAX_CON_INV_PACK_SY(?,?,?,?)
    </select>
	<parameterMap type="java.util.Map" id="checkTmpMap">
		<parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
		<parameter property="P_FLAG" mode="INOUT" jdbcType="VARCHAR"/>
		<parameter property="P_RET_CODE" mode="INOUT" jdbcType="VARCHAR"/>
		<parameter property="P_RET_STR" mode="INOUT" jdbcType="VARCHAR"/>
	</parameterMap>

	<select id="checkTmpByParam" parameterMap="checkTmpMap2" statementType="CALLABLE" resultType="java.util.Map" databaseId="postgresql">
      CALL P_IMP_TAX_CON_INV_PACK_SY(?,?,?,?)
    </select>
	<parameterMap type="java.util.Map" id="checkTmpMap2">
		<parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
		<parameter property="P_FLAG" mode="IN" jdbcType="VARCHAR"/>
		<parameter property="P_RET_CODE" mode="IN" jdbcType="VARCHAR"/>
		<parameter property="P_RET_STR" mode="IN" jdbcType="VARCHAR"/>
	</parameterMap>

	<!--   查询  正确数据包含警告数据，因此要把警告数据添加到正确数据列表中，否则警告数据无法导入-->
	<select id="selectByFlag" resultMap="impTaxConInvPackResultMap" parameterType="map">
		select t.* from T_IMP_TAX_CON_INV_PACK t
		<where>
			and t.TEMP_OWNER = #{TEMP_OWNER,jdbcType=VARCHAR}
			AND t.TEMP_FLAG IN
			<foreach item="item" index="index" collection="TEMP_FLAG" open="(" separator="," close=")">
				#{item}
			</foreach>
		</where>
		order by t.TEMP_INDEX ASC
	</select>
</mapper>
