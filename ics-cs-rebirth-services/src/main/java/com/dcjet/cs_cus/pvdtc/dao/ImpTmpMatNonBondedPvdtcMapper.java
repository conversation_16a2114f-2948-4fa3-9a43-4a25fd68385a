package com.dcjet.cs_cus.pvdtc.dao;

import com.dcjet.cs_cus.pvdtc.model.ImpTmpMatNonBondedPvdtc;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate dcits
 * 物料中心-非保税物料数据
 *
 * @author: 朱正东
 * @date: 2019-02-27
 */
public interface ImpTmpMatNonBondedPvdtcMapper extends Mapper<ImpTmpMatNonBondedPvdtc> {
    /***
     * 调用存储过程
     * @param param
     * @return
     */
    Map<String, String> checkTmpByParam(Map<String, Object> param);

    /***
     * 调用存储过程
     * @param param
     * @return
     */
    Map<String, String> checkTmpByParamUpdate(Map<String, Object> param);

    /**
     * 根据传入的数据类型获取相应数据
     *
     * @param param
     * @return 返回结果
     */
    List<ImpTmpMatNonBondedPvdtc> selectByFlag(Map<String, Object> param);

    void updateImporStatus(@Param("tempOwner") String tempOwner);
}
