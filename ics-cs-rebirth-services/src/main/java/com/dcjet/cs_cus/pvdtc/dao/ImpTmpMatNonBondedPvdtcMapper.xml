<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs_cus.pvdtc.dao.ImpTmpMatNonBondedPvdtcMapper">
    <resultMap id="impTmpMatNonBondedResultPvdtcMap" type="com.dcjet.cs_cus.pvdtc.model.ImpTmpMatNonBondedPvdtc">
        <result column="SID" jdbcType="VARCHAR" property="sid"/>
        <result column="EMS_NO" jdbcType="VARCHAR" property="emsNo"/>
        <result column="G_MARK" jdbcType="VARCHAR" property="GMark"/>
        <!--<result column="G_MARK_NAME" jdbcType="VARCHAR" property="gMarkName" />-->
        <result column="SERIAL_NO" jdbcType="DECIMAL" property="serialNo"/>
        <result column="COP_G_NO" jdbcType="VARCHAR" property="copGNo"/>
        <result column="CODE_T_S" jdbcType="VARCHAR" property="codeTS"/>
        <result column="G_NAME" jdbcType="VARCHAR" property="GName"/>
        <result column="G_MODEL" jdbcType="VARCHAR" property="GModel"/>
        <result column="UNIT" jdbcType="VARCHAR" property="unit"/>
        <result column="UNIT_1" jdbcType="VARCHAR" property="unit1"/>
        <result column="UNIT_2" jdbcType="VARCHAR" property="unit2"/>
        <result column="QTY" jdbcType="DECIMAL" property="qty"/>
        <result column="DECLARE_ELEMENTS" jdbcType="VARCHAR" property="declareElements"/>
        <result column="DEC_PRICE" jdbcType="VARCHAR" property="decPrice"/>
        <result column="DEC_TOTAL" jdbcType="DECIMAL" property="decTotal"/>
        <result column="CURR" jdbcType="VARCHAR" property="curr"/>
        <result column="DUTY_MODE" jdbcType="VARCHAR" property="dutyMode"/>
        <result column="COUNTRY" jdbcType="VARCHAR" property="countryCode"/>
        <result column="CLASS_MARK" jdbcType="VARCHAR" property="classMark"/>
        <result column="FACTOR_1" jdbcType="DECIMAL" property="factor1"/>
        <result column="FACTOR_2" jdbcType="DECIMAL" property="factor2"/>
        <result column="FACTOR_WT" jdbcType="DECIMAL" property="factorWt"/>
        <result column="CLASS_REMARK" jdbcType="VARCHAR" property="classRemark"/>
        <result column="MODIFY_MARK" jdbcType="VARCHAR" property="modifyMark"/>
        <result column="ETPS_EXE_MARK" jdbcType="VARCHAR" property="comImplementSign"/>
        <result column="COP_G_NAME" jdbcType="VARCHAR" property="copGName"/>
        <result column="COP_G_MODEL" jdbcType="VARCHAR" property="copGModel"/>
        <result column="FACTORY" jdbcType="VARCHAR" property="factory"/>
        <result column="COST_CENTER" jdbcType="VARCHAR" property="costCenter"/>
        <result column="TRADE_CODE" jdbcType="VARCHAR" property="tradeCode"/>
        <result column="NET_WT" jdbcType="DECIMAL" property="netWt"/>
        <result column="UNIT_WT" jdbcType="VARCHAR" property="unitWt"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="NOTE" jdbcType="VARCHAR" property="note"/>
        <result column="INSERT_USER" jdbcType="VARCHAR" property="insertUser"/>
        <result column="INSERT_TIME" jdbcType="DATE" property="insertTime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="DATE" property="updateTime"/>
        <result column="SEND_USER" jdbcType="VARCHAR" property="sendUser"/>
        <result column="DECLARE_DATE" jdbcType="DATE" property="declareDate"/>
        <result column="APPR_USER" jdbcType="VARCHAR" property="auditUser"/>
        <result column="APPR_DATE" jdbcType="DATE" property="auditTime"/>
        <result column="APPR_STATUS" jdbcType="VARCHAR" property="apprStatus"/>
        <result column="COP_EMS_NO" jdbcType="VARCHAR" property="copEmsNo"/>
        <result column="RECORD_DATE_START" jdbcType="VARCHAR" property="recordDateStart"/>
        <result column="RECORD_DATE_END" jdbcType="VARCHAR" property="recordDateEnd"/>
        <result column="TEMP_OWNER" jdbcType="VARCHAR" property="tempOwner"/>
        <result column="TEMP_MARK" jdbcType="DECIMAL" property="tempMark"/>
        <result column="TEMP_REMARK" jdbcType="VARCHAR" property="tempRemark"/>
        <result column="TEMP_FLAG" jdbcType="DECIMAL" property="tempFlag"/>
        <result column="TEMP_INDEX" jdbcType="DECIMAL" property="tempIndex"/>
        <result column="COP_G_NAME_EN" jdbcType="VARCHAR" property="copGNameEn"/>
        <result column="COP_G_MODEL_EN" jdbcType="VARCHAR" property="copGModelEn"/>
        <result column="UNIT_ERP" jdbcType="VARCHAR" property="unitErp"/>
        <result column="FACTOR_ERP" jdbcType="DECIMAL" property="factorErp"/>
        <result column="QTY_WT" jdbcType="DECIMAL" property="qtyWt"/>
        <result column="ORIGINAL_G_NO" jdbcType="VARCHAR" property="originalGNo"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="CREDENTIALS" property="credentials" jdbcType="VARCHAR"/>
        <result column="use_to" property="useTo" jdbcType="VARCHAR"/>
        <result column="structure" property="structure" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="checkTmpByParam" parameterMap="checkTmpMap" statementType="CALLABLE"
            resultType="java.util.Map">
        CALL P_IMP_MAT_NO_BONDED_PVDTC(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpMap">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="INOUT" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="checkTmpByParam" parameterMap="checkTmpMap2" statementType="CALLABLE"
            resultType="java.util.Map" databaseId="postgresql">
        CALL P_IMP_MAT_NO_BONDED_PVDTC(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpMap2">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="checkTmpByParamUpdate" parameterMap="checkTmpUpdateMap" statementType="CALLABLE"
            resultType="java.util.Map">
        CALL P_IMP_MAT_NO_BONDED_PVDTCU(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpUpdateMap">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="INOUT" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="checkTmpByParamUpdate" parameterMap="checkTmpUpdateMap2" statementType="CALLABLE"
            resultType="java.util.Map" databaseId="postgresql">
        CALL P_IMP_MAT_NO_BONDED_PVDTCU(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpUpdateMap2">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="selectByFlag" resultMap="impTmpMatNonBondedResultPvdtcMap" parameterType="map">
        select t.* from T_IMP_TMP_MAT_NON_BONDED t
        <where>
            and t.TEMP_OWNER = #{TEMP_OWNER,jdbcType=VARCHAR}
            AND t.TEMP_FLAG IN
            <foreach item="item" index="index" collection="TEMP_FLAG" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        order by t.TEMP_INDEX ASC
    </select>

    <update id="updateImporStatus">
        <if test='_databaseId != "postgresql" '>
            begin
        </if>
        update t_imp_tmp_mat_non_bonded
        set import_status = '1'
        where cop_g_no in (select cop_g_no
        from t_mat_non_bonded
        where cop_g_no in (select cop_g_no
        from t_imp_tmp_mat_non_bonded
        where temp_owner = #{tempOwner}));

        update t_imp_tmp_mat_non_bonded
        set import_status = '0'
        where cop_g_no in (select cop_g_no
        from t_imp_tmp_mat_non_bonded
        where temp_owner = '1234'
        and import_status is null)
        and import_status is null
        and temp_owner = #{tempOwner};
        <if test='_databaseId != "postgresql" '>
            end;
        </if>
    </update>

</mapper>
