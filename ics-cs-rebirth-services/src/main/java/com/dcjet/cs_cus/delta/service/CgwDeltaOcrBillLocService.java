package com.dcjet.cs_cus.delta.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto_cus.delta.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs_cus.delta.dao.CgwDeltaOcrBillLocMapper;
import com.dcjet.cs_cus.delta.mapper.CgwDeltaOcrBillLocDtoMapper;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBillLoc;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Service
public class CgwDeltaOcrBillLocService extends BaseService<CgwDeltaOcrBillLoc> {
    @Resource
    private CgwDeltaOcrBillLocMapper mapper;
    @Resource
    private CgwDeltaOcrBillLocDtoMapper dtoMapper;
    @Override
    public Mapper<CgwDeltaOcrBillLoc> getMapper() {
        return mapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param cgwDeltaOcrBillLocParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<CgwDeltaOcrBillLocDto>> getListPaged(CgwDeltaOcrBillLocParam cgwDeltaOcrBillLocParam, PageParam pageParam) {
        // 启用分页查询
        CgwDeltaOcrBillLoc cgwDeltaOcrBillLoc = dtoMapper.toPo(cgwDeltaOcrBillLocParam);
        Page<CgwDeltaOcrBillLoc> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(cgwDeltaOcrBillLoc));
        List<CgwDeltaOcrBillLocDto> cgwDeltaOcrBillLocDtos = page.getResult().stream().map(head -> {
            CgwDeltaOcrBillLocDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<CgwDeltaOcrBillLocDto>> paged = ResultObject.createInstance(cgwDeltaOcrBillLocDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CgwDeltaOcrBillLocDto> selectAll(CgwDeltaOcrBillLocParam exportParam, UserInfoToken userInfo) {
        CgwDeltaOcrBillLoc cgwDeltaOcrBillLoc = dtoMapper.toPo(exportParam);
        // cgwDeltaOcrBillLoc.setTradeCode(userInfo.getCompany());
        List<CgwDeltaOcrBillLocDto> cgwDeltaOcrBillLocDtos = new ArrayList<>();
        List<CgwDeltaOcrBillLoc> cgwDeltaOcrBillLocs = mapper.getList(cgwDeltaOcrBillLoc);
        if (CollectionUtils.isNotEmpty(cgwDeltaOcrBillLocs)) {
            cgwDeltaOcrBillLocDtos = cgwDeltaOcrBillLocs.stream().map(head -> {
                CgwDeltaOcrBillLocDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return cgwDeltaOcrBillLocDtos;
    }
}
