package com.dcjet.cs_cus.delta.mapper;
import com.dcjet.cs.dto_cus.delta.*;
import com.dcjet.cs_cus.delta.model.CgwDeltaOcrBillLoc;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-11-25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CgwDeltaOcrBillLocDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CgwDeltaOcrBillLocDto toDto(CgwDeltaOcrBillLoc po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CgwDeltaOcrBillLoc toPo(CgwDeltaOcrBillLocParam param);
    default void patchPo(CgwDeltaOcrBillLocParam cgwDeltaOcrBillLocParam, CgwDeltaOcrBillLoc cgwDeltaOcrBillLoc) {
        // TODO 自行实现局部更新
    }
}
