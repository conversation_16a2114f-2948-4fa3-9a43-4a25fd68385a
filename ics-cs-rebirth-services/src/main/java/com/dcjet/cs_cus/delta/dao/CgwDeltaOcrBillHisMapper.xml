<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs_cus.delta.dao.CgwDeltaOcrBillHisMapper">
    <resultMap id="cgwDeltaOcrBillHisResultMap" type="com.dcjet.cs_cus.delta.model.CgwDeltaOcrBillHis">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="en_no" property="enNo" jdbcType="VARCHAR" />
		<result column="ship_to" property="shipTo" jdbcType="VARCHAR" />
		<result column="consignee" property="consignee" jdbcType="VARCHAR" />
		<result column="notify_party" property="notifyParty" jdbcType="VARCHAR" />
		<result column="desp_port" property="despPort" jdbcType="VARCHAR" />
		<result column="mark_no" property="markNo" jdbcType="VARCHAR" />
		<result column="gross_wt" property="grossWt" jdbcType="VARCHAR" />
		<result column="volume" property="volume" jdbcType="VARCHAR" />
		<result column="outer_packaging" property="outerPackaging" jdbcType="VARCHAR" />
		<result column="g_name" property="gName" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="scan_time" property="scanTime" jdbcType="TIMESTAMP" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,en_no
     ,ship_to
     ,consignee
     ,notify_party
     ,desp_port
     ,mark_no
     ,gross_wt
     ,volume
     ,outer_packaging
     ,g_name
     ,head_id
     ,trade_code
     ,insert_user
     ,insert_user_name
     ,insert_time
     ,update_user
     ,update_user_name
     ,update_time
     ,scan_time
    </sql>
    <sql id="condition">
        and trade_code = #{tradeCode}
        <if test="enNo != null and enNo != ''">
	        and en_no like '%'|| #{enNo} || '%'
	    </if>
        <if test="_databaseId == 'oracle' and scanTimeFrom != null and scanTimeFrom != ''">
            <![CDATA[ and scan_time >= to_date(#{scanTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and scanTimeTo != null and scanTimeTo != ''">
            <![CDATA[ and scan_time < to_date(#{scanTimeTo}, 'yyyy-MM-dd hh24:mi:ss') + 1]]>
        </if>
        <if test="_databaseId == 'postgresql' and scanTimeFrom != null and scanTimeFrom != ''">
            <![CDATA[ and scan_time >= to_timestamp(#{scanTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="_databaseId == 'postgresql' and scanTimeTo != null and scanTimeTo != ''">
            <![CDATA[ and scan_time < to_timestamp(#{scanTimeTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp + INTERVAL '1 day' ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="cgwDeltaOcrBillHisResultMap" parameterType="com.dcjet.cs_cus.delta.model.CgwDeltaOcrBillHis">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_cgw_delta_ocr_bill_his t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.insert_time desc
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_cgw_delta_ocr_bill_his t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
