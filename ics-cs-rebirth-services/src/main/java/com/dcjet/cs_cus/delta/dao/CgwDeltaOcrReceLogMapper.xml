<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs_cus.delta.dao.CgwDeltaOcrReceLogMapper">
    <resultMap id="cgwDeltaOcrReceLogResultMap" type="com.dcjet.cs_cus.delta.model.CgwDeltaOcrReceLog">
		<result column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="receive_data" property="receiveData" jdbcType="VARCHAR" />
		<result column="bussiness_type" property="bussinessType" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,receive_data
     ,bussiness_type
     ,trade_code
     ,insert_user
     ,insert_user_name
     ,insert_time
     ,update_user
     ,update_user_name
     ,update_time
    </sql>
    <sql id="condition">
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="cgwDeltaOcrReceLogResultMap" parameterType="com.dcjet.cs_cus.delta.model.CgwDeltaOcrReceLog">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_cgw_delta_ocr_rece_log t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_cgw_delta_ocr_rece_log t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
