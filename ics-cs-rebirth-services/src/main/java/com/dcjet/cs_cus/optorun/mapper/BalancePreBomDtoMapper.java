package com.dcjet.cs_cus.optorun.mapper;
import com.dcjet.cs.dto_cus.optorun.BalancePreBomDto;
import com.dcjet.cs.dto_cus.optorun.BalancePreBomParam;
import com.dcjet.cs_cus.optorun.model.BalancePreBom;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BalancePreBomDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BalancePreBomDto toDto(BalancePreBom po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BalancePreBom toPo(BalancePreBomParam param);
    default void patchPo(BalancePreBomParam balancePreBomParam, BalancePreBom balancePreBom) {
        // TODO 自行实现局部更新
    }
}
