package com.dcjet.cs_cus.tbc.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
@Table(name = "T_DEC_ERP_IE_HEAD")
public class DecErpIeHead implements Serializable {
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 单据内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 进出口标记，I进口 E出口
     */
    @Column(name = "I_E_MARK")
    private String IEMark;
    /**
     * 预录入单数据
     */
    @Column(name = "HEDA_DATA")
    private String headData;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private  String insertUser;
    /**
     * 创建人姓名
     */
    @Column(name = "INSERT_USER_NAME")
    private  String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
}
