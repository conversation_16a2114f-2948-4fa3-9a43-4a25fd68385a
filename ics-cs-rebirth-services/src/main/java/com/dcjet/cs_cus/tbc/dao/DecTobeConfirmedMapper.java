package com.dcjet.cs_cus.tbc.dao;

import com.dcjet.cs.base.repository.BatchMapper;
import com.dcjet.cs.base.repository.UpdateByPrimaryKeySelectiveColumnMapper;
import com.dcjet.cs.dto_cus.tbc.DecTobeConfirmedParam;
import com.dcjet.cs_cus.tbc.model.DecInvoiceStatistics;
import com.dcjet.cs_cus.tbc.model.DecTobeConfirmed;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.additional.idlist.IdListMapper;
import tk.mybatis.mapper.additional.update.differ.UpdateByDifferMapper;
import tk.mybatis.mapper.additional.update.force.UpdateByPrimaryKeySelectiveForceMapper;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
/**
* generated by Generate 神码
* DecTobeConfirmed
* <AUTHOR>
* @date: 2021-8-20
*/
public interface DecTobeConfirmedMapper
        extends Mapper<DecTobeConfirmed>, BatchMapper<DecTobeConfirmed>, IdListMapper<DecTobeConfirmed, String>,
        UpdateByDifferMapper<DecTobeConfirmed>, UpdateByPrimaryKeySelectiveForceMapper<DecTobeConfirmed>, UpdateByPrimaryKeySelectiveColumnMapper<DecTobeConfirmed> {
    /**
     * 查询获取数据
     * @param decTobeConfirmed
     * @return
     */
    List<DecTobeConfirmed> getList(DecTobeConfirmedParam decTobeConfirmed);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    List<DecTobeConfirmed> selectBySids(@Param("sids") List<String> sids,@Param("tradeCode") String tradeCode);

    @Select({"SELECT * FROM T_DEC_INVOICE_STATISTICS WHERE HEAD_ID=#{headId} AND TRADE_CODE =#{tradeCode}"})
    List<DecInvoiceStatistics> getDecInvoice(@Param("headId") String headId, @Param("tradeCode") String tradeCode);

    int deleteHeadId(@Param("sid") String sid);

    //获取发票号
    String getInvoiceNo(@Param("sid") String sid);

    void updateTotal(@Param("sid") String sid, @Param("totalInvoiceAmount") BigDecimal totalInvoiceAmount, @Param("vatInvoiceNo") String vatInvoiceNo);

    void deleteIdList(@Param("sid") String sid);

    void deleteHeadIds(List<String> sids);
}
