package com.dcjet.cs_cus.tbc.service;
import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.dto_cus.tbc.DecInvoiceStatisticsDto;
import com.dcjet.cs.dto_cus.tbc.DecInvoiceStatisticsParam;
import com.dcjet.cs.dto_cus.tbc.DecTobeConfirmedDto;
import com.dcjet.cs.dto_cus.tbc.DecTobeConfirmedParam;
import com.dcjet.cs.entry.dao.DecEEntryHeadMapper;
import com.dcjet.cs.entry.dao.DecIEntryHeadMapper;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs_cus.tbc.dao.DecErpIeHeadMapper;
import com.dcjet.cs_cus.tbc.dao.DecTobeConfirmedMapper;
import com.dcjet.cs_cus.tbc.mapper.DecInvoiceStatisticsDtoMapper;
import com.dcjet.cs_cus.tbc.mapper.DecTobeConfirmedDtoMapper;
import com.dcjet.cs_cus.tbc.model.DecErpIeHead;
import com.dcjet.cs_cus.tbc.model.DecInvoiceStatistics;
import com.dcjet.cs_cus.tbc.model.DecTobeConfirmed;
import com.dcjet.cs_cus.tbc.model.FinanceComfirmParam;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-8-20
 */
@Service
public class DecTobeConfirmedService extends BaseService<DecTobeConfirmed> {
    @Resource
    private DecTobeConfirmedMapper mapper;
    @Resource
    private DecTobeConfirmedDtoMapper dtoMapper;
    @Override
    public Mapper<DecTobeConfirmed> getMapper() {
        return mapper;
    }

    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private DecIEntryHeadMapper decIEntryHeadMapper;
    @Resource
    private DecEEntryHeadMapper decEEntryHeadMapper;
    @Resource
    private DecInvoiceStatisticsDtoMapper decInvoiceStatisticsDtoMapper;

    @Resource
    private BulkInsertConfig bulkInsertConfig;
    public IBulkInsert getBulkInsertInstance() throws Exception{
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    @Resource
    private DecErpIeHeadMapper decErpIeHeadMapper;
    /**  可流转的状态 */
    private static final List<String> canFlowStatus = Arrays.asList(
            DecTobeConfirmed.CONFIRM_STATUS_NO_FLOW,
            DecTobeConfirmed.CONFIRM_STATUS_PURCHASE_REFUSED,
            DecTobeConfirmed.CONFIRM_STATUS_FINANCE_REFUSED
    );

    /** 采购可确认的状态 */
    private static final List<String> purchaseCanConfirmStatus = Arrays.asList(
            DecTobeConfirmed.CONFIRM_STATUS_PURCHASE_TO_BE_CONFIRMED
    );

    /** 采购可取消的状态 */
    private static final List<String> purchaseCanCancelStatus = Arrays.asList(
            DecTobeConfirmed.CONFIRM_STATUS_FINANCE_TO_BE_CONFIRMED
    );

    /** 采购可退回的状态 */
    private static final List<String> purchaseCanRefuseStatus = Arrays.asList(
            DecTobeConfirmed.CONFIRM_STATUS_PURCHASE_TO_BE_CONFIRMED
    );

    /** 财务可确认的状态 */
    private static final List<String> financeCanConfirmStatus = Arrays.asList(
            DecTobeConfirmed.CONFIRM_STATUS_FINANCE_TO_BE_CONFIRMED,
            DecTobeConfirmed.CONFIRM_STATUS_CONFIRMED // 财务确认过的数据 可以二次确认（用于修改发票号）
    );

    /** 财务可取消的状态 */
    private static final List<String> financeCanCancelStatus = Arrays.asList(
            DecTobeConfirmed.CONFIRM_STATUS_CONFIRMED
    );

    /** 财务可退回的状态 */
    private static final List<String> financeCanRefuseStatus = Arrays.asList(
            DecTobeConfirmed.CONFIRM_STATUS_FINANCE_TO_BE_CONFIRMED
    );


    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param decTobeConfirmedParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<DecTobeConfirmedDto>> getListPaged(DecTobeConfirmedParam decTobeConfirmedParam, PageParam pageParam) {
        // 启用分页查询
//        DecTobeConfirmed decTobeConfirmed = decTobeConfirmedDtoMapper.toPo(decTobeConfirmedParam);
        Page<DecTobeConfirmed> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(decTobeConfirmedParam));
        List<DecTobeConfirmedDto> decTobeConfirmedDtos = page.getResult().stream().map(head -> {
            DecTobeConfirmedDto dto = dtoMapper.toDto(head);
            dto.setUserFormat();
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<DecTobeConfirmedDto>> paged = ResultObject.createInstance(decTobeConfirmedDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }


    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecTobeConfirmedDto> selectAll(DecTobeConfirmedParam exportParam, UserInfoToken userInfo) {
//        DecTobeConfirmed decTobeConfirmed = decTobeConfirmedDtoMapper.toPo(exportParam);
        exportParam.setTradeCode(userInfo.getCompany());
        List<DecTobeConfirmedDto> decTobeConfirmedDtos = new ArrayList<>();
        List<DecTobeConfirmed> decTobeConfirmeds = mapper.getList(exportParam);
        if (CollectionUtils.isNotEmpty(decTobeConfirmeds)) {
            decTobeConfirmedDtos = decTobeConfirmeds.stream().map(head -> {
                DecTobeConfirmedDto dto = dtoMapper.toDto(head);
                dto.setUserFormat();
                return dto;
            }).collect(Collectors.toList());
        }
        return decTobeConfirmedDtos;
    }

    /**
     * 过滤
     * @param sids
     * @param token
     * @return
     */
    public void filter(List<String> sids,String status, UserInfoToken token) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        Example example  = new Example(DecTobeConfirmed.class);
        Example.Criteria criteriaForCheck = example.createCriteria();

        criteriaForCheck.andIn("sid", sids);
        criteriaForCheck.andNotIn("confirmStatus",Arrays.asList("0", "-1","-2"));
        int cnt = mapper.selectCountByExample(example);
        if (cnt > 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只可以过滤未流转、采购退回、财务退回数据"));
        }

        example.clear();
        Example.Criteria criteriaForUpdate = example.createCriteria();
        criteriaForUpdate.andIn("sid", sids);

        DecTobeConfirmed tobeUpdated = new DecTobeConfirmed();
        tobeUpdated.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_FILTERED);
        mapper.updateByExampleSelective(tobeUpdated, example);
    }

    /**
     * 取消过滤
     * @param sids
     * @param token
     */
    public void cancelFilter(List<String> sids, UserInfoToken token) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        Example example = new Example(DecTobeConfirmed.class);
        Example.Criteria criteriaForCheck = example.createCriteria();

        criteriaForCheck.andIn("sid", sids);
        criteriaForCheck.andNotEqualTo("confirmStatus", DecTobeConfirmed.CONFIRM_STATUS_FILTERED);
        int cnt = mapper.selectCountByExample(example);
        if (cnt > 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只可以取消已过滤的数据"));
        }

        example.clear();
        Example.Criteria criteriaForUpdate = example.createCriteria();
        criteriaForUpdate.andIn("sid", sids);

        DecTobeConfirmed tobeUpdated = new DecTobeConfirmed();
        tobeUpdated.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_NO_FLOW);
        mapper.updateByExampleSelective(tobeUpdated, example);
    }


    /**
     * 生成待确认列表
     *  （发送内审时调用）
     * @param ieMark
     * @param erpHeadId
     */
    public void generate(String ieMark, String erpHeadId, UserInfoToken token) {

        Assert.notEmpty(erpHeadId, xdoi18n.XdoI18nUtil.t("预录入单ID不可为空"));

        delete(erpHeadId);

        switch (ieMark) {
            case CommonVariable.IE_MARK_I :
                generateForI(erpHeadId, token);
                break;
            case CommonVariable.IE_MARK_E :
                generateForE(erpHeadId, token);
                break;
        }

    }

    private void generateForI(String erpHeadId, UserInfoToken token) {

        DecErpIHeadN iHead = decErpIHeadNMapper.selectByPrimaryKey(erpHeadId);
        Assert.notNull(iHead, xdoi18n.XdoI18nUtil.t("预录入单不存在"));

        DecTobeConfirmed decTobeConfirmed = new DecTobeConfirmed();
        dtoMapper.updatePo(iHead, decTobeConfirmed);
        decTobeConfirmed.setIEMark(CommonVariable.IE_MARK_I);
        decTobeConfirmed.setDecErpId(erpHeadId);
        decTobeConfirmed.newRecord(token);

        if (iHead!=null) {
            // 加快照
            DecErpIeHead decErpIeHead = new DecErpIeHead();
            decErpIeHead.setSid(UUID.randomUUID().toString());
            decErpIeHead.setEmsListNo(iHead.getEmsListNo());
            decErpIeHead.setIEMark(CommonVariable.IE_MARK_I);
            decErpIeHead.setTradeCode(iHead.getTradeCode());
            decErpIeHead.setInsertUser(token.getUserNo());
            decErpIeHead.setInsertUserName(token.getUserName());
            decErpIeHead.setInsertTime(new Date());
            decErpIeHead.setHeadData(JsonObjectMapper.getInstance().toJson(iHead));
            decErpIeHeadMapper.insert(decErpIeHead);
        }
        mapper.insert(decTobeConfirmed);

    }

    private void generateForE(String erpHeadId, UserInfoToken token) {

        DecErpEHeadN eHead = decErpEHeadNMapper.selectByPrimaryKey(erpHeadId);
        Assert.notNull(eHead, xdoi18n.XdoI18nUtil.t("预录入单不存在"));

        DecTobeConfirmed decTobeConfirmed = new DecTobeConfirmed();
        dtoMapper.updatePo(eHead, decTobeConfirmed);
        decTobeConfirmed.setIEMark(CommonVariable.IE_MARK_E);
        decTobeConfirmed.setDecErpId(erpHeadId);

        decTobeConfirmed.newRecord(token);

        if (eHead!=null) {
            // 加快照
            DecErpIeHead decErpIeHead = new DecErpIeHead();
            decErpIeHead.setSid(UUID.randomUUID().toString());
            decErpIeHead.setEmsListNo(eHead.getEmsListNo());
            decErpIeHead.setIEMark(CommonVariable.IE_MARK_E);
            decErpIeHead.setTradeCode(eHead.getTradeCode());
            decErpIeHead.setInsertUser(token.getUserNo());
            decErpIeHead.setInsertUserName(token.getUserName());
            decErpIeHead.setInsertTime(new Date());
            decErpIeHead.setHeadData(JsonObjectMapper.getInstance().toJson(eHead));
            decErpIeHeadMapper.insert(decErpIeHead);
        }
        mapper.insert(decTobeConfirmed);

    }

    /**
     * 删除待确认列表
     * @param erpHeadId
     */
    public void delete(String erpHeadId) {
        DecTobeConfirmed param = new DecTobeConfirmed();
        param.setDecErpId(erpHeadId);
        mapper.delete(param);
    }

    /**
     * 流转到采购
     */
    public Boolean transferToPurchase(List<String> sids, UserInfoToken token) {
        //
        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectByIdList(sids);
        Assert.notEmpty(decTobeConfirmeds, xdoi18n.XdoI18nUtil.t("记录已被删除，请刷新列表"));

        // 状态校验
        Set<String> statusCollection =
                decTobeConfirmeds.stream().map(e->e.getConfirmStatus()).collect(Collectors.toSet());
        Assert.isTrue(CollectionUtils.containsAll(canFlowStatus, statusCollection), xdoi18n.XdoI18nUtil.t("存在不可流转至采购的单据"));

        decTobeConfirmeds.stream().forEach(e -> {
            e.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_PURCHASE_TO_BE_CONFIRMED);
            e.cleanConfirmInfo();
        });

        int cnt = mapper.updateListByPrimaryKey(decTobeConfirmeds);
        return cnt>0;
    }

    /**
     * 流转到财务
     */
    public Boolean transferToFinance(List<String> sids, UserInfoToken token) {
        //
        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectByIdList(sids);
        Assert.notEmpty(decTobeConfirmeds, xdoi18n.XdoI18nUtil.t("记录已被删除，请刷新列表"));

        // 状态校验
        Set<String> statusCollection =
                decTobeConfirmeds.stream().map(e->e.getConfirmStatus()).collect(Collectors.toSet());
        Assert.isTrue(CollectionUtils.containsAll(canFlowStatus, statusCollection), xdoi18n.XdoI18nUtil.t("存在不可流转至财务的单据"));

        decTobeConfirmeds.stream().forEach(e -> {
            e.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_FINANCE_TO_BE_CONFIRMED);
            e.cleanConfirmInfo();
        });

        int cnt = mapper.updateListByPrimaryKey(decTobeConfirmeds);
        return cnt>0;
    }

    /**
     * 采购确认
     * @param sids
     * @param token
     */
    public Boolean purchaseConfirm(List<String> sids, UserInfoToken token) {
        //
        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectByIdList(sids);
        Assert.notEmpty(decTobeConfirmeds, xdoi18n.XdoI18nUtil.t("记录已被删除，请刷新列表"));

        // 状态校验
        Set<String> statusCollection =
                decTobeConfirmeds.stream().map(e->e.getConfirmStatus()).collect(Collectors.toSet());
        Assert.isTrue(CollectionUtils.containsAll(purchaseCanConfirmStatus, statusCollection), xdoi18n.XdoI18nUtil.t("存在非采购待确认状态的单据"));

        //
        Date currentDate = new Date();
        decTobeConfirmeds.stream().forEach(decTobeConfirmed -> {
            decTobeConfirmed.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_FINANCE_TO_BE_CONFIRMED);
            decTobeConfirmed.setPurchaseComfirmer(token.getUserNo());
            decTobeConfirmed.setPurchaseComfirmerName(token.getUserName());
            decTobeConfirmed.setPurchaseComfirmTime(currentDate);
        });

        int cnt = mapper.updateListByPrimaryKey(decTobeConfirmeds);
        return cnt>0;

    }

    /**
     * 采购取消
     * @param sids
     * @param token
     * @return
     */
    public Boolean purchaseCancel(List<String> sids, UserInfoToken token) {
        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectByIdList(sids);
        Assert.notEmpty(decTobeConfirmeds, xdoi18n.XdoI18nUtil.t("记录已被删除，请刷新列表"));

        // 状态校验
        Set<String> statusCollection =
                decTobeConfirmeds.stream().map(e->e.getConfirmStatus()).collect(Collectors.toSet());
        Assert.isTrue(CollectionUtils.containsAll(purchaseCanCancelStatus, statusCollection), xdoi18n.XdoI18nUtil.t("存在采购不可取消的单据"));

        decTobeConfirmeds.stream().forEach(e -> {
            e.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_PURCHASE_TO_BE_CONFIRMED);
            e.setPurchaseComfirmer(null);
            e.setPurchaseComfirmerName(null);
            e.setPurchaseComfirmTime(null);
        });

        int cnt = mapper.updateListByPrimaryKey(decTobeConfirmeds);

        return cnt > 0;

    }

    /**
     * 采购退回/拒绝
     * @param sids
     * @param refuseReason
     * @param token
     */
    public Boolean purchaseRefuse(List<String> sids, String refuseReason, UserInfoToken token) {
        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectByIdList(sids);
        Assert.notEmpty(decTobeConfirmeds, xdoi18n.XdoI18nUtil.t("记录已被删除，请刷新列表"));

        // 状态校验
        Set<String> statusCollection =
                decTobeConfirmeds.stream().map(e->e.getConfirmStatus()).collect(Collectors.toSet());
        Assert.isTrue(CollectionUtils.containsAll(purchaseCanRefuseStatus, statusCollection), xdoi18n.XdoI18nUtil.t("存在采购不可退回的单据"));

        DecTobeConfirmed decTobeConfirmed = new DecTobeConfirmed();
        decTobeConfirmed.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_PURCHASE_REFUSED);
        decTobeConfirmed.setRefuseReason(refuseReason);

        Date currentDate = new Date();
        decTobeConfirmeds.stream().forEach(e -> {
            e.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_PURCHASE_REFUSED);
            e.setPurchaseComfirmer(token.getUserNo());
            e.setPurchaseComfirmerName(token.getUserName());
            e.setPurchaseComfirmTime(currentDate);
            e.setRefuseReason(refuseReason);
        });

        int cnt = mapper.updateListByPrimaryKey(decTobeConfirmeds);
        return cnt>0;

    }

    /**
     * 采购确认校验
     * @param params
     * @return
     */
    public Pair<Boolean, String> checkForFinanceConfirm(List<FinanceComfirmParam> params, UserInfoToken token) {

        List<String> sids = params.stream().map(e->e.getSid()).collect(Collectors.toList());
        Set<String> vatInvoiceNos = params.stream().map(e->e.getVatInvoiceNo()).collect(Collectors.toSet());

        if(vatInvoiceNos.size() != params.size()) {
            return Pair.of(false, xdoi18n.XdoI18nUtil.t("发票号重复"));
        }

//        Example example = new Example(DecTobeConfirmed.class);
//        Example.Criteria criteria = example.createCriteria();
//
//        criteria.andEqualTo("tradeCode", token.getCompany());
//        criteria.andNotIn("sid", sids);
//        criteria.andIn("vatInvoiceNo", vatInvoiceNos);
//
//        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectByExample(example);
//
//        if (CollectionUtils.isNotEmpty(decTobeConfirmeds)) {
//
//            String warningMsg = decTobeConfirmeds.stream().map(e->e.getVatInvoiceNo()).collect(Collectors.joining(","));
//            warningMsg = String.format("单据编号 %s 数据已存在", warningMsg);
//
//            return Pair.of(false, warningMsg);
//        }

        return Pair.of(true, xdoi18n.XdoI18nUtil.t("校验通过"));
    }

    /**
     * 财务确认
     * @param params
     * @param token
     * @return
     */
    public Boolean financeConfirm(List<FinanceComfirmParam> params, UserInfoToken token) {

        Assert.notEmpty(params, xdoi18n.XdoI18nUtil.t("参数不能为空"));
        Map<String, FinanceComfirmParam> map = params.stream().collect(Collectors.toMap(e->e.getSid(), Function.identity(), (oldData, newData) -> oldData));
        //
        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectByIdList(new ArrayList<>(map.keySet()));
        Assert.notEmpty(decTobeConfirmeds, xdoi18n.XdoI18nUtil.t("记录已被删除，请刷新列表"));

        // 状态校验
        Set<String> statusCollection =
                decTobeConfirmeds.stream().map(e->e.getConfirmStatus()).collect(Collectors.toSet());
        Assert.isTrue(CollectionUtils.containsAll(financeCanConfirmStatus, statusCollection), xdoi18n.XdoI18nUtil.t("存在非财务待确认状态的单据"));

        Date currentDate = new Date();
        decTobeConfirmeds.stream().forEach(model -> {
//            model.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_CONFIRMED);
//            model.setVatInvoiceNo(map.get(model.getSid()).getVatInvoiceNo());
            model.setVatInvoiceNo(model.getVatInvoiceNo());
            model.setFinanceComfirmer(token.getUserNo());
            model.setFinanceComfirmerName(token.getUserName());
            model.setFinanceComfirmTime(currentDate);
            if (model.getSumDecTotal() != null && model.getTotalInvoiceAmount() != null) {
                if (model.getTotalInvoiceAmount().compareTo(model.getSumDecTotal()) == 0) {
                    model.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_CONFIRMED);
                }
            }

        });
        int cnt =  bulkSqlOpt.batchUpdate(decTobeConfirmeds, DecTobeConfirmedMapper.class);
//        int cnt = mapper.updateListByPrimaryKey(decTobeConfirmeds);

        return cnt == 0;
    }

    /**
     * 财务取消
     * @param sids
     * @param token
     * @return
     */
    public Boolean financeCancel(List<String> sids,String status, UserInfoToken token) {

        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectByIdList(sids);
        Assert.notEmpty(decTobeConfirmeds, xdoi18n.XdoI18nUtil.t("记录已被删除，请刷新列表"));

        // 状态校验
//        Set<String> statusCollection =
//                decTobeConfirmeds.stream().map(e->e.getConfirmStatus()).collect(Collectors.toSet());
//        Assert.isTrue(CollectionUtils.containsAll(financeCanCancelStatus, statusCollection), "存在财务不可取消的单据");

            decTobeConfirmeds.stream().forEach(e -> {
                e.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_FINANCE_TO_BE_CONFIRMED);
                e.setFinanceComfirmer(null);
                e.setFinanceComfirmerName(null);
                e.setFinanceComfirmTime(null);
                e.setInvoiceNo(null);
                if (StringUtils.equals(status,ConstantsStatus.STATUS_1)) {
                    e.setVatInvoiceNo(null);
                    e.setTotalInvoiceAmount(null);
                }
            });

        int cnt = 0;
        if (StringUtils.equals(status, ConstantsStatus.STATUS_0)) {
            cnt = mapper.updateListByPrimaryKey(decTobeConfirmeds);
        } else
            if (StringUtils.equals(status, ConstantsStatus.STATUS_1)) {
            cnt = mapper.updateListByPrimaryKey(decTobeConfirmeds);
            mapper.deleteHeadIds(sids);
        }

        return cnt > 0;
    }

    /**
     * 财务取消
     * @param sids
     * @return
     */
    public boolean financeCancelCheck(List<String> sids) {
        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectByIdList(sids);
        Assert.notEmpty(decTobeConfirmeds, xdoi18n.XdoI18nUtil.t("记录已被删除，请刷新列表"));

        // 状态校验
        Set<String> statusCollection =
                decTobeConfirmeds.stream().map(e->e.getConfirmStatus()).collect(Collectors.toSet());
        if (!CollectionUtils.containsAll(financeCanCancelStatus,statusCollection)){
            decTobeConfirmeds.stream().forEach(head->{
                if (head.getTotalInvoiceAmount()==null){
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("存在财务不可取消的单据"));
                }
                /*if (head.getSumDecTotal().compareTo(head.getTotalInvoiceAmount())==0){

                }else {
                    throw new ErrorException(400, "存在财务不可取消的单据");
                }*/
            });
        }
        return true;
    }


    /**
     * 财务退回/拒绝
     * @param sids
     * @param refuseReason
     * @param token
     * @return
     */
    public Boolean financeRefuse(List<String> sids, String refuseReason, UserInfoToken token) {
        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectByIdList(sids);
        Assert.notEmpty(decTobeConfirmeds, xdoi18n.XdoI18nUtil.t("记录已被删除，请刷新列表"));

        // 状态校验
        Set<String> statusCollection =
                decTobeConfirmeds.stream().map(e->e.getConfirmStatus()).collect(Collectors.toSet());
        Assert.isTrue(CollectionUtils.containsAll(financeCanRefuseStatus, statusCollection), xdoi18n.XdoI18nUtil.t("存在财务不可退回的单据"));

        DecTobeConfirmed decTobeConfirmed = new DecTobeConfirmed();
        decTobeConfirmed.setConfirmStatus(DecTobeConfirmed.CONFIRM_STATUS_FINANCE_REFUSED);
        decTobeConfirmed.setFinanceComfirmer(token.getUserNo());
        decTobeConfirmed.setFinanceComfirmerName(token.getUserName());
        decTobeConfirmed.setFinanceComfirmTime(new Date());
        decTobeConfirmed.setRefuseReason(refuseReason);

        int cnt = batchUpdateBySids(sids, decTobeConfirmed);
        return cnt>0;
    }


    /**
     * 根据sid 批量更新
     * @param sids
     * @param decTobeConfirmed
     * @return
     */
    private int batchUpdateBySids(List<String> sids, DecTobeConfirmed decTobeConfirmed) {
        Example example = new Example(DecTobeConfirmed.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sid", sids);

        //
        int cnt = mapper.updateByExampleSelective(decTobeConfirmed, example);
        return cnt;
    }


    /**
     * 查询所有数据
     * @param sid
     * @param userInfo
     * @return
     */
    public List<DecInvoiceStatisticsDto> getBillMaintainList(String sid, UserInfoToken userInfo) {
        //根据sid查询数据
        DecTobeConfirmed decTobeConfirmed = mapper.selectByPrimaryKey(sid);
        if (decTobeConfirmed!=null){
            if (StringUtils.equals(decTobeConfirmed.getFinanceConfirmStatus(), ConstantsStatus.STATUS_1)){
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("财务已确认，不可修改"));
            }
        }
        List<DecInvoiceStatisticsDto>decInvoiceStatisticsDtos = new ArrayList<>();
        List<DecInvoiceStatistics>decINvoices = mapper.getDecInvoice(sid,userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(decINvoices)){
            decInvoiceStatisticsDtos=decINvoices.stream().map(head->{
                DecInvoiceStatisticsDto dto = decInvoiceStatisticsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decInvoiceStatisticsDtos;
    }

    /**
     * 发票维护
     * @param decInvoiceStatisticsParams
     * @param sid
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject getBillMaintain(List<DecInvoiceStatisticsParam> decInvoiceStatisticsParams, String sid, UserInfoToken userInfo) {

        ResultObject resultObject = ResultObject.createInstance(true, "");
        //校验传入参数是否为空
        if (CollectionUtils.isNotEmpty(decInvoiceStatisticsParams)) {
            //根据sid查询数据
            DecTobeConfirmed decTobeConfirmed = mapper.selectByPrimaryKey(sid);
            if (decTobeConfirmed != null) {

                List<DecInvoiceStatistics> decINvoices = new ArrayList<>();
                if (StringUtils.equals(decTobeConfirmed.getConfirmStatus(), ConstantsStatus.STATUS_2)) {

                    //校验发票号是否相同
                    List<String> invoiceNos = decInvoiceStatisticsParams.stream().map(e->e.getInvoiceNo()).collect(Collectors.toList());
                    invoiceNos=invoiceNos.stream().distinct().collect(Collectors.toList());
                    if (decInvoiceStatisticsParams.size()>invoiceNos.size()){
                        throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("发票号不能相同"));
                    }

                    //校验是否是数字
                    decInvoiceStatisticsParams.stream().forEach(head->{
                        if (Strings.isNotBlank(head.getInvoiceAmount())) {
                            boolean invoiceaAmount = isNumber(head.getInvoiceAmount());
                            /*if (!invoiceaAmount) {
                                throw new ErrorException(400, "发票金额只能输入数字!");
                            }*/
                            if (invoiceaAmount){
                                BigDecimal str1 = new BigDecimal(head.getInvoiceAmount());
                                if (str1.compareTo(new BigDecimal(0))==-1){
                                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("发票金额不能小于0!"));
                                }
                            }else {
                                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("发票金额只能输入数字!"));
                            }
                        }
                    });
                    //
                    BigDecimal total = new BigDecimal("0.00");
                    for (int i = 0; i < decInvoiceStatisticsParams.size(); i++) {
                        DecInvoiceStatistics dec = decInvoiceStatisticsDtoMapper.toPo(decInvoiceStatisticsParams.get(i));
                        if (dec.getInvoiceNo() != null || dec.getInvoiceAmount() != null) {
                            dec.setSid(UUID.randomUUID().toString());
                            dec.setTradeCode(userInfo.getCompany());
                            dec.setHeadId(sid);
                            //判断发票号是否为空
                            if (Strings.isBlank(dec.getInvoiceNo())) {
                                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("请维护发票号"));
                            }

                            //判断发票金额是否为空，计算发票金额
                            if (dec.getInvoiceAmount() == null) {
                                dec.setInvoiceAmount(new BigDecimal("0"));
                                total = total.add(dec.getInvoiceAmount());
                            } else {
                                total = total.add(dec.getInvoiceAmount());
                            }
                            decINvoices.add(dec);
                        }
                    }
                    //校验发票金额
                    if (decTobeConfirmed.getSumDecTotal() != null) {
                        if (total.compareTo(new BigDecimal("0.0")) == 1) {
                            if (total.compareTo(decTobeConfirmed.getSumDecTotal()) == 1) {
                                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("发票总金额大于报关金额"));
                            }
                        }
                        /*else {
                            throw new ErrorException(400, "发票总金额为零!");
                        }*/
                    } else {
                        throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("报关金额不能为空!"));
                    }

                    try {
                        //根据表头sid删除所有数据
                        int numberOf = mapper.deleteHeadId(decTobeConfirmed.getSid());
                        //批量插入所有数据
                        IBulkInsert bulkInsert = getBulkInsertInstance();
                        bulkInsert.fastImport(decINvoices);
                    } catch (BulkInsertException ex) {
                        XdoImportLogger.log(ex);
                    } catch (Exception ex) {
                        XdoImportLogger.log(ex);
                    }

                    //根据headid查询拼接所有发票号
                    String invoiceNo = mapper.getInvoiceNo(sid);
                    mapper.updateTotal(decTobeConfirmed.getSid(), total, invoiceNo);
                }
            }

        } else {
            mapper.deleteIdList(sid);
        }

        return resultObject;
    }

    //校验是否是数字
    private boolean isNumber(String invoiceAmount) {
        if (Strings.isNotBlank(invoiceAmount)) {
            return invoiceAmount.matches("[-+]?[0-9]*\\.?[0-9]+");
        } else {
            return false;
        }
    }

    /**
     * 根据sid查询所有数据
     * @param sids
     * @param userInfo
     * @return
     */
    public List<DecTobeConfirmedDto> getSidList(List<String> sids, UserInfoToken userInfo) {
        List<DecTobeConfirmedDto> decTobeConfirmedDtos = new ArrayList<>();
        List<DecTobeConfirmed> decTobeConfirmeds = mapper.selectBySids(sids,userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(decTobeConfirmeds)) {
            decTobeConfirmedDtos = decTobeConfirmeds.stream().map(head -> {
                DecTobeConfirmedDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decTobeConfirmedDtos;

    }
}
