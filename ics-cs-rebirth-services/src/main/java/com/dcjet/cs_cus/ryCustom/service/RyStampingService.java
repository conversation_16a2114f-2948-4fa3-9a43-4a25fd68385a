package com.dcjet.cs_cus.ryCustom.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.mat.AttachedDto;
import com.dcjet.cs.dto_cus.ryCustom.RyStampingDto;
import com.dcjet.cs.dto_cus.ryCustom.RyStampingParam;
import com.dcjet.cs.dto_cus.ryCustom.RyStampingPrint;
import com.dcjet.cs.mat.service.AttachedService;
import com.dcjet.cs_cus.ryCustom.dao.RyStampingMapper;
import com.dcjet.cs_cus.ryCustom.mapper.RyStampingDtoMapper;
import com.dcjet.cs_cus.ryCustom.model.RyStamping;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-6-29
 */
@Service
public class RyStampingService extends BaseService<RyStamping> {
    @Resource
    private RyStampingMapper ryStampingMapper;
    @Resource
    private RyStampingDtoMapper ryStampingDtoMapper;
    @Resource
    private AttachedService attachedService;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource
    private PCodeHolder pCodeHolder;

    @Override
    public Mapper<RyStamping> getMapper() {
        return ryStampingMapper;
    }

    /**
     * 获取分页信息
     *
     * @param ryStampingParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<RyStampingDto>> getListPaged(RyStampingParam ryStampingParam, PageParam pageParam) {
        // 启用分页查询
        RyStamping ryStamping = ryStampingDtoMapper.toPo(ryStampingParam);
        Page<RyStamping> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> ryStampingMapper.getList(ryStamping));
        List<RyStampingDto> ryStampingDtos = page.getResult().stream().map(head -> {
            RyStampingDto dto = ryStampingDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<RyStampingDto>> paged = ResultObject.createInstance(ryStampingDtos,
                (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param ryStampingParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RyStampingDto insert(RyStampingParam ryStampingParam, UserInfoToken userInfo) {
        RyStamping ryStamping = ryStampingDtoMapper.toPo(ryStampingParam);
        Integer maxSerialNo = ryStampingMapper.getMaxSerialNo(userInfo.getCompany());
        if (maxSerialNo == null) {
            ryStamping.setSerialNo(1);
        } else {
            ryStamping.setSerialNo(++maxSerialNo);
        }
        ryStamping.setTradeCode(userInfo.getCompany());
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        ryStamping.setSid(sid);
        ryStamping.setInsertUser(userInfo.getUserNo());
        ryStamping.setInsertTime(new Date());
        // 新增数据
        int insertStatus = ryStampingMapper.insert(ryStamping);
        return insertStatus > 0 ? ryStampingDtoMapper.toDto(ryStamping) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param ryStampingParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RyStampingDto update(RyStampingParam ryStampingParam, UserInfoToken userInfo) {
        RyStamping ryStamping = ryStampingMapper.selectByPrimaryKey(ryStampingParam.getSid());
        ryStampingDtoMapper.updatePo(ryStampingParam, ryStamping);
        ryStamping.setUpdateUser(userInfo.getUserNo());
        ryStamping.setUpdateTime(new Date());
        // 更新数据
        int update = ryStampingMapper.updateByPrimaryKey(ryStamping);
        return update > 0 ? ryStampingDtoMapper.toDto(ryStamping) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param businessSids
     * @return
     */
    @Transient
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> businessSids, UserInfoToken userInfo) throws Exception {
        for (String businessSid : businessSids) {
            ResultObject<List<AttachedDto>> resultObject = attachedService.getAttechedList(businessSid, userInfo);
            List<AttachedDto> attachedDtos = resultObject.getData();
            if (CollectionUtils.isNotEmpty(attachedDtos)) {
                List<String> sids = attachedDtos.stream().map(e -> e.getSid()).collect(Collectors.toList());
                attachedService.delete(sids);
            }
        }
        ryStampingMapper.deleteBySids(businessSids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<RyStampingDto> selectAll(RyStampingParam exportParam, UserInfoToken userInfo) {
        RyStamping ryStamping = ryStampingDtoMapper.toPo(exportParam);
        ryStamping.setTradeCode(userInfo.getCompany());
        List<RyStampingDto> ryStampingDtos = new ArrayList<>();
        List<RyStamping> ryStampings = ryStampingMapper.getList(ryStamping);
        if (CollectionUtils.isNotEmpty(ryStampings)) {
            ryStampingDtos = ryStampings.stream().map(head -> {
                RyStampingDto dto = ryStampingDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return ryStampingDtos;
    }

    public RyStampingPrint toPrintBean(RyStamping ryStamping) {
        RyStampingPrint print = new RyStampingPrint();

        print.setEmsNo(ryStamping.getEmsNo());
        print.setExgSerialNo(ryStamping.getExgSerialNo());
        if (StringUtils.isNotBlank(ryStamping.getCopExgUnit())) {
            print.setCopExgUnit(pCodeHolder.getValue(PCodeType.UNIT, ryStamping.getCopExgUnit()));
        }
        print.setCopExgNo(ryStamping.getCopExgNo());
        print.setImgSerialNo(ryStamping.getImgSerialNo());
        if (StringUtils.isNotBlank(ryStamping.getCopImgUnit())) {
            print.setCopImgUnit(pCodeHolder.getValue(PCodeType.UNIT, ryStamping.getCopImgUnit()));
        }
        print.setCopImgName(ryStamping.getCopImgName());
        print.setMouldExpend(ryStamping.getMouldExpend());
        print.setActualWeigh(ryStamping.getActualWeigh());
        print.setDensity(ryStamping.getDensity());
        print.setThickNess(ryStamping.getThickNess());
        print.setFacGNo("B-" + ryStamping.getFacGNo());
        print.setLossRate(ryStamping.getLossRate());
        StringBuilder densityStr = new StringBuilder(xdoi18n.XdoI18nUtil.t("密度（"));
        String originalName = ryStamping.getOriginalName();
        if (StringUtils.isNotBlank(originalName) && originalName.contains(" ")) {
            densityStr.append(originalName.substring(0, originalName.indexOf(" ")));
        }
        densityStr.append("）");
        print.setDensityStr(densityStr.toString());
        print.setExgSerialNoStr(xdoi18n.XdoI18nUtil.t("成品序号 ") + ryStamping.getExgSerialNo() + xdoi18n.XdoI18nUtil.t(" 项"));
        print.setCopExgNameStr(xdoi18n.XdoI18nUtil.t("（品名：") + ryStamping.getCopExgName() + xdoi18n.XdoI18nUtil.t("）单、损耗情况如下表"));
        StringBuilder mouldExpendCal = new StringBuilder();
        StringBuilder lossRateCal = new StringBuilder();
        mouldExpendCal.append("(").append(ryStamping.getThickNess()).append("*").append(ryStamping.getWidth()).append("*")
                .append(ryStamping.getDistance()).append("*").append(ryStamping.getDensity()).append(")/")
                .append(ryStamping.getTouchPointNum()).append("/1000=").append(ryStamping.getMouldExpend());
        lossRateCal.append("(").append(ryStamping.getMouldExpend()).append("-").append(ryStamping.getActualWeigh())
                .append(")/").append(ryStamping.getMouldExpend()).append("=").append(ryStamping.getLossRate()).append("%");
        print.setMouldExpendCal(mouldExpendCal.toString());
        print.setLossRateCal(lossRateCal.toString());
        return print;
    }
}
