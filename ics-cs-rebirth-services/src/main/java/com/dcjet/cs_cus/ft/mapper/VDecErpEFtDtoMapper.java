package com.dcjet.cs_cus.ft.mapper;


import com.dcjet.cs.dto_cus.ft.VDecErpEHeadListFtDto;
import com.dcjet.cs.dto_cus.ft.VDecErpEHeadListFtParam;
import com.dcjet.cs.dto_cus.ft.VDecErpEHeadListHeadFtDto;
import com.dcjet.cs.dto_cus.ft.VDecErpEHeadListHeadFtParam;
import com.dcjet.cs_cus.ft.model.VDecErpEHeadFt;
import com.dcjet.cs_cus.ft.model.VDecErpEHeadListFt;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-03-14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VDecErpEFtDtoMapper {

    /**
     * 杞崲鏁版嵁搴撳璞″埌DTO
     *
     * @param po
     * @return
     */
    VDecErpEHeadListFtDto toDto(VDecErpEHeadListFt po);


    /**
     * 杞崲鏁版嵁搴撳璞″埌DTO
     *
     * @param po
     * @return
     */
    VDecErpEHeadListFt toPo(VDecErpEHeadListFtParam po);


    /**
     * 杞崲鏁版嵁搴撳璞″埌DTO
     *
     * @param po
     * @return
     */
    VDecErpEHeadListHeadFtDto toHeadDto(VDecErpEHeadFt po);

    /**
     * 杞崲鏁版嵁搴撳璞″埌DTO
     *
     * @param po
     * @return
     */
    VDecErpEHeadFt toHeadPo(VDecErpEHeadListHeadFtParam po);
}
