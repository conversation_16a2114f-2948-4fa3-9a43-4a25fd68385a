<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs_cus.daikin.dao.CustomerPaymentMapper">
    <resultMap id="customerPaymentResultMap" type="com.dcjet.cs_cus.daikin.model.CustomerPayment">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="SETTLEMENT_YEAR" property="settlementYear" jdbcType="VARCHAR"/>
        <result column="SETTLEMENT_DATE" property="settlementDate" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        SID
        ,CUSTOMER_CODE
     ,COMPANY_NAME
     ,SETTLEMENT_YEAR
     ,SETTLEMENT_DATE
     ,NOTE
     ,TRADE_CODE
     ,INSERT_USER
     ,INSERT_USER_NAME
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_USER_NAME
     ,UPDATE_TIME
    </sql>
    <sql id="condition">
        and TRADE_CODE = #{tradeCode}
        <if test="customerCode != null and customerCode != ''">
            and CUSTOMER_CODE like concat(concat('%',#{customerCode}),'%')
        </if>
        <if test="companyName != null and companyName != ''">
            and COMPANY_NAME like concat(concat('%',#{companyName}),'%')
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="customerPaymentResultMap"
            parameterType="com.dcjet.cs_cus.daikin.model.CustomerPayment">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_CGW_DAIKIN_CUSTOMER_PAYMENT t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_CGW_DAIKIN_CUSTOMER_PAYMENT t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <!--查询客户代码是否存在-->
    <select id="chekcustomerCode" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM T_CGW_DAIKIN_CUSTOMER_PAYMENT
        WHERE customer_code = #{ customerCode }
          AND trade_code = #{ tradeCode }
          AND sid !=#{ sid }
    </select>
</mapper>
