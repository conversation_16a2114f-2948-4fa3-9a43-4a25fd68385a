package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.dto_cus.daikin.OrderListParam;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.sap.ErpDecEList;
import com.dcjet.cs.erp.service.DecErpEListNService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.dcjet.cs_cus.daikin.dao.OrderListMapper;
import com.dcjet.cs_cus.daikin.mapper.OrderListDtoMapper;
import com.dcjet.cs_cus.daikin.model.OrderList;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class DecErpEListExtractService {

    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private OrderListDtoMapper orderListDtoMapper;
    @Resource
    private OrderListMapper orderListMapper;
    @Resource
    private DecErpEListNService decErpEListNService;

    public ResultObject extract(OrderListParam orderListParam, UserInfoToken userInfo) throws Exception {

        if (StringUtils.isNotBlank(orderListParam.getCustomerOrderNo())) {
            orderListParam.setCustomerOrderNo(orderListParam.getCustomerOrderNo().replace("，", ","));
            orderListParam.setCustomerOrderNo(orderListParam.getCustomerOrderNo().replaceAll(" ", ","));
            orderListParam.setCustomerOrderNo(orderListParam.getCustomerOrderNo().replaceAll("[',']+", ","));
        }
        if (StringUtils.isNotBlank(orderListParam.getFacGNo())) {
            orderListParam.setFacGNo(orderListParam.getFacGNo().replace("，", ","));
            orderListParam.setFacGNo(orderListParam.getFacGNo().replaceAll(" ", ","));
            orderListParam.setFacGNo(orderListParam.getFacGNo().replaceAll("[',']+", ","));
        }
        DecErpEHeadN decErpEHeadN = decErpEHeadNMapper.selectByPrimaryKey(orderListParam.getHeadId());

        //筛选可修改单
        if (!Arrays.asList(DecVariable.DEL_STATUS_IE).contains(decErpEHeadN.getApprStatus())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有 “暂存”、“清单生成”、“内审退回”、“清单撤回” 状态数据可表体提取!"));
        }

        orderListParam.setTradeCode(userInfo.getCompany());
        OrderList orderList = orderListDtoMapper.toPo(orderListParam);
        orderList.setHeadId(null);
        List<OrderList> orderLists = orderListMapper.getList(orderList);
        ResultObject ro = decErpEListNService.check(decErpEHeadN, orderToErp(decErpEHeadN, orderLists), orderListParam.getHeadId(), userInfo,CommonEnum.dataSourceEnum.SOURCE_14.getCode());
        return ro;
    }

    /**
     * 描述：将临时表数据插入正式表
     *
     * @param
     * @return
     */
    public ResultObject extractBySid(List<String> sids, String headId, UserInfoToken userInfo) throws Exception {
        DecErpEHeadN decErpEHeadN = decErpEHeadNMapper.selectByPrimaryKey(headId);
        //筛选可修改单
        if (!Arrays.asList(DecVariable.DEL_STATUS_IE).contains(decErpEHeadN.getApprStatus())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有 “暂存”、“清单生成”、“内审退回”、“清单撤回” 状态数据可表体提取!"));
        }
        List<OrderList> orderLists = orderListMapper.selectBySids(sids, userInfo.getCompany());
        ResultObject ro = decErpEListNService.check(decErpEHeadN, orderToErp(decErpEHeadN, orderLists), headId, userInfo,CommonEnum.dataSourceEnum.SOURCE_14.getCode());
        return ro;
    }

    private List<ErpDecEList> orderToErp(DecErpEHeadN decErpEHeadN, List<OrderList> orderLists) {
        List<ErpDecEList> erpDecELists = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderLists)) {
            if (orderLists.stream().anyMatch(e -> e.getSurplusQty().compareTo(BigDecimal.ZERO) <= 0)) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("提取数据中包含了剩余数量小于等于0的数据，提取失败！"));
            }
            orderLists.forEach(e -> {
                ErpDecEList erpDecEList = new ErpDecEList();
                erpDecEList.setCustomerGNo(e.getCustomerFacGNo());
                erpDecEList.setFacGNo(e.getFacGNo());
                erpDecEList.setCustomerPoNo(e.getCustomerOrderNo());
                erpDecEList.setQty(e.getSurplusQty());
                erpDecEList.setDecPrice(e.getDecPrice());
                erpDecEList.setCurr(e.getCurr());
                erpDecEList.setDecDataSource(CommonEnum.dataSourceEnum.SOURCE_14.getCode());
                erpDecEList.setOriginCountry(CommonVariable.COUNTRY_CHN);
                erpDecEList.setDestinationCountry(decErpEHeadN.getDestinationCountry());
                erpDecEList.setBondMark(decErpEHeadN.getBondMark());
                erpDecEList.setRemark1(e.getInternalOrderNo());
                erpDecEList.setRemark2(e.getCopGNameEn());
                erpDecEList.setCustomerPoNo(e.getCustomerOrderNo());
                erpDecEList.setCustomerGNo(e.getCustomerFacGNo());
                //大金订单提取 返回DTO
                erpDecEList.setCustomerFacGNo(e.getCustomerFacGNo());
                erpDecEList.setType(e.getType());
                erpDecEList.setCopGNameEn(e.getCopGNameEn());
                erpDecEList.setInternalOrderNo(e.getInternalOrderNo());
                if (erpDecEList.getDecPrice() != null) {
                    erpDecEList.setDecTotal(erpDecEList.getDecPrice().multiply(erpDecEList.getQty()).setScale(5, BigDecimal.ROUND_HALF_UP));
                }
                erpDecELists.add(erpDecEList);
            });
        }
        return erpDecELists;
    }
}
