package com.dcjet.cs_cus.daikin.mapper;

import com.dcjet.cs.dto_cus.daikin.CustomerPaymentDto;
import com.dcjet.cs.dto_cus.daikin.CustomerPaymentParam;
import com.dcjet.cs_cus.daikin.model.CustomerPayment;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomerPaymentDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CustomerPaymentDto toDto(CustomerPayment po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CustomerPayment toPo(CustomerPaymentParam param);

    /**
     * 数据库原始数据更新
     *
     * @param customerPaymentParam
     * @param customerPayment
     */
    void updatePo(CustomerPaymentParam customerPaymentParam, @MappingTarget CustomerPayment customerPayment);

    default void patchPo(CustomerPaymentParam customerPaymentParam, CustomerPayment customerPayment) {
        // TODO 自行实现局部更新
    }
}
