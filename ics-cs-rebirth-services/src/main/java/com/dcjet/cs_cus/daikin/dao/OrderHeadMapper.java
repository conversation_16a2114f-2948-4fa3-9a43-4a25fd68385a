package com.dcjet.cs_cus.daikin.dao;

import com.dcjet.cs_cus.daikin.model.OrderHead;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * OrderHead
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
public interface OrderHeadMapper extends Mapper<OrderHead> {
    /**
     * 查询获取数据
     *
     * @param orderHead
     * @return
     */
    List<OrderHead> getList(OrderHead orderHead);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
