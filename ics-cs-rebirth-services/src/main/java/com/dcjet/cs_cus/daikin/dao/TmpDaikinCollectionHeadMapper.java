package com.dcjet.cs_cus.daikin.dao;

import com.dcjet.cs_cus.daikin.model.TmpDaikinCollectionHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * MCgwDaikinCollectionHead
 *
 * <AUTHOR>
 * @date: 2021-12-13
 */
public interface TmpDaikinCollectionHeadMapper extends Mapper<TmpDaikinCollectionHead> {

    List<TmpDaikinCollectionHead> selectByFlag(Map<String, Object> param);

    TmpDaikinCollectionHead selectlist(@Param("invoiceNo") String invoiceNo, @Param("emsListNo") String emsListNo, @Param("tradeCode") String tradeCode);

    //删除临时表所有数据
    void deleteList();
}
