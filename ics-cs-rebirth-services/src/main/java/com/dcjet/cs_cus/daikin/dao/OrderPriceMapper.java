package com.dcjet.cs_cus.daikin.dao;

import com.dcjet.cs_cus.daikin.model.OrderPrice;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* OrderPrice
* <AUTHOR>
* @date: 2021-9-15
*/
public interface OrderPriceMapper extends Mapper<OrderPrice> {
    /**
     * 查询获取数据
     * @param orderPrice
     * @return
     */
    List<OrderPrice> getList(OrderPrice orderPrice);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 查询客户代码+企业料号下有效期是否已存在
     */
    List<String> checkRepeat(OrderPrice orderPrice);

    /**
     * 查询客户代码+企业料号下有效期是否已存在
     */
    int checkPerfectMatch(OrderPrice orderPrice);
}
