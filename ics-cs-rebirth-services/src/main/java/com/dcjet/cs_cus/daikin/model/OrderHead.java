package com.dcjet.cs_cus.daikin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
@Setter
@Getter
@Table(name = "T_CGW_DAIKIN_ORDER_HEAD")
public class OrderHead implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 订单管理号
     */
    @Column(name = "MANAGE_ORDER_NO")
    private String manageOrderNo;
    /**
     * 客户代码
     */
    @Column(name = "CUSTOMER_CODE")
    private String customerCode;
    /**
     * 客户中文名称
     */
    @Column(name = "CUSTOMER_NAME")
    private String customerName;
    /**
     * 接单日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "RECEIVE_DATE")
    private Date receiveDate;
    /**
     * 接单日期-开始
     */
    @Transient
    private String receiveDateFrom;
    /**
     * 接单日期-结束
     */
    @Transient
    private String receiveDateTo;
    /**
     * 类别
     */
    @Column(name = "TYPE")
    private String type;
    /**
     * 目的地(以企业自行导入的信息为准)
     */
    @Column(name = "DEST_NAME")
    private String destName;
    /**
     * 运输方式(以企业自行导入的信息为准)
     */
    @Column(name = "TRAF_MODE_NAME")
    private String trafModeName;
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建人名称
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 修改人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 修改人名称
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
}
