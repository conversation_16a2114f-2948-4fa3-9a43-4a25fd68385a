package com.dcjet.cs_cus.daikin.service;

import com.dcjet.cs.dto_cus.daikin.CollectionListDto;
import com.dcjet.cs.dto_cus.daikin.CollectionListParam;
import com.dcjet.cs_cus.daikin.dao.CollectionListMapper;
import com.dcjet.cs_cus.daikin.mapper.CollectionListDtoMapper;
import com.dcjet.cs_cus.daikin.model.CollectionList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
@Service
public class CollectionListService extends BaseService<CollectionList> {
    @Resource
    private CollectionListMapper collectionListMapper;
    @Resource
    private CollectionListDtoMapper collectionListDtoMapper;

    @Override
    public Mapper<CollectionList> getMapper() {
        return collectionListMapper;
    }

    /**
     * 获取分页信息
     *
     * @param collectionListParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<CollectionListDto>> getListPaged(CollectionListParam collectionListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        CollectionList collectionList = collectionListDtoMapper.toPo(collectionListParam);
        collectionList.setTradeCode(userInfo.getCompany());
        Page<CollectionList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> collectionListMapper.getList(collectionList));
        List<CollectionListDto> collectionListDtos = page.getResult().stream().map(head -> {
            CollectionListDto dto = collectionListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<CollectionListDto>> paged = ResultObject.createInstance(collectionListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param collectionListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CollectionListDto insert(CollectionListParam collectionListParam, UserInfoToken userInfo) {
        CollectionList collectionList = collectionListDtoMapper.toPo(collectionListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        collectionList.setSid(sid);
        collectionList.setInsertUser(userInfo.getUserNo());
        collectionList.setInsertUserName(userInfo.getUserName());
        collectionList.setTradeCode(userInfo.getCompany());
        collectionList.setInsertTime(new Date());
        // 新增数据
        int insertStatus = collectionListMapper.insert(collectionList);
        return insertStatus > 0 ? collectionListDtoMapper.toDto(collectionList) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param collectionListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CollectionListDto update(CollectionListParam collectionListParam, UserInfoToken userInfo) {
        CollectionList collectionList = collectionListMapper.selectByPrimaryKey(collectionListParam.getSid());
        collectionListDtoMapper.updatePo(collectionListParam, collectionList);
        collectionList.setUpdateUser(userInfo.getUserNo());
        collectionList.setUpdateUserName(userInfo.getUserName());
        collectionList.setUpdateTime(new Date());
        // 更新数据
        int update = collectionListMapper.updateByPrimaryKey(collectionList);
        return update > 0 ? collectionListDtoMapper.toDto(collectionList) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        collectionListMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CollectionListDto> selectAll(CollectionListParam exportParam, UserInfoToken userInfo) {
        CollectionList collectionList = collectionListDtoMapper.toPo(exportParam);
        collectionList.setTradeCode(userInfo.getCompany());
        List<CollectionListDto> collectionListDtos = new ArrayList<>();
        List<CollectionList> collectionLists = collectionListMapper.getList(collectionList);
        if (CollectionUtils.isNotEmpty(collectionLists)) {
            collectionListDtos = collectionLists.stream().map(head -> {
                CollectionListDto dto = collectionListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return collectionListDtos;
    }


}
