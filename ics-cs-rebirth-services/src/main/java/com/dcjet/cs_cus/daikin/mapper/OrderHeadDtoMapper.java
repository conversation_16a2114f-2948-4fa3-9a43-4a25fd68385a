package com.dcjet.cs_cus.daikin.mapper;

import com.dcjet.cs.dto_cus.daikin.OrderHeadDto;
import com.dcjet.cs.dto_cus.daikin.OrderHeadParam;
import com.dcjet.cs_cus.daikin.model.OrderHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-15
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    OrderHeadDto toDto(OrderHead po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    OrderHead toPo(OrderHeadParam param);

    /**
     * 数据库原始数据更新
     *
     * @param orderHeadParam
     * @param orderHead
     */
    void updatePo(OrderHeadParam orderHeadParam, @MappingTarget OrderHead orderHead);

    default void patchPo(OrderHeadParam orderHeadParam, OrderHead orderHead) {
        // TODO 自行实现局部更新
    }
}
