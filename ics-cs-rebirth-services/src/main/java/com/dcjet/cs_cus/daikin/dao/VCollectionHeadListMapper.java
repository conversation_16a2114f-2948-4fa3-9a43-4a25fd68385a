package com.dcjet.cs_cus.daikin.dao;

import com.dcjet.cs_cus.daikin.model.VCollectionHeadList;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * CollectionManagementHead
 *
 * <AUTHOR>
 * @date: 2021-9-6
 */
public interface VCollectionHeadListMapper extends Mapper<VCollectionHeadList> {
    /**
     * 查询获取数据
     *
     * @param vCollectionManagementHeadList
     * @return
     */
    List<VCollectionHeadList> getList(VCollectionHeadList vCollectionManagementHeadList);

}
