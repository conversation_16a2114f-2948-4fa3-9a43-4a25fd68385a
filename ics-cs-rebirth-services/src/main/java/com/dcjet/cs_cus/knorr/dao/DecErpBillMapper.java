package com.dcjet.cs_cus.knorr.dao;

import com.dcjet.cs_cus.knorr.model.DecErpBill;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-4-22
 */
public interface DecErpBillMapper extends Mapper<DecErpBill> {
    /**
     * 查询获取数据
     *
     * @param decErpBill
     * @return
     */
    List<DecErpBill> getList(DecErpBill decErpBill);

    Integer selectDataCountAll(DecErpBill decErpBill);
}
