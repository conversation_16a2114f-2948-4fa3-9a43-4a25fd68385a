package com.dcjet.cs_cus.knorr.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_DEC_ERP_E_LIST_N")
public class DecErpBill extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 预录单内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;
    /**
     * 进出口标志
     */
    @Transient
    private String IEMark;
    /**
     * SA
     */
    @Column(name = "SA")
    private String sa;
    /**
     * 件数
     */
    @Column(name = "PACK_NUM")
    private BigDecimal packNum;
    /**
     * DN
     */
    @Transient
    private String note1;
    /**
     * 料号
     */
    @Transient
    private String facGNo;
    /**
     * 申报数量
     */
    @Transient
    private BigDecimal qty;
    /**
     * 订单号
     */
    @Transient
    private String orderNo;

    @Transient
    private String invoiceNo;
    /**
     * 预计到货日期
     */
    @Transient
    @DateTimeFormat(pattern = "yyyy-MM-dd ")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planArrivalDate;

    /**
     * 预计到货日期-开始
     */
    @Transient
    private String planArrivalDateFrom;
    /**
     * 预计到货日期-结束
     */
    @Transient
    private String planArrivalDateTo;

}
