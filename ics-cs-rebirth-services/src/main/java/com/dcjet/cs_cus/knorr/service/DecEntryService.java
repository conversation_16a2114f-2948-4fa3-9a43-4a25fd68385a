package com.dcjet.cs_cus.knorr.service;

import com.dcjet.cs.dto_cus.knorr.DecEntryDto;
import com.dcjet.cs.dto_cus.knorr.DecEntryParam;
import com.dcjet.cs_cus.knorr.dao.DecEntryMapper;
import com.dcjet.cs_cus.knorr.mapper.DecEntryDtoMapper;
import com.dcjet.cs_cus.knorr.model.DecEntry;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Service
public class DecEntryService extends BaseService<DecEntry> {
    @Resource
    private DecEntryMapper decEntryMapper;
    @Resource
    private DecEntryDtoMapper decEntryDtoMapper;

    @Override
    public Mapper<DecEntry> getMapper() {
        return decEntryMapper;
    }

    /**
     * 功能描述 报关时效追踪信息分页查询
     * @version 1.0
     * @param decEntryParam 报关时效追踪信息分页查询传入参数
     * @param pageParam 分页参数
     * @param userInfo 当前用户信息
     * @return com.xdo.domain.ResultObject<java.util.List<com.dcjet.cs.dto.erp.DecEntryDto>>
     */
    public ResultObject<List<DecEntryDto>> getListPaged(DecEntryParam decEntryParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        DecEntry decEntry = decEntryDtoMapper.toPo(decEntryParam);
        decEntry.setTradeCode(userInfo.getCompany());
        Page<DecEntry> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decEntryMapper.getList(decEntry));
        List<DecEntryDto> decEntryDtos = page.getResult().stream().map(head -> {
            DecEntryDto dto = decEntryDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<DecEntryDto>> paged = ResultObject.createInstance(decEntryDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecEntryDto> entrySelectAll(DecEntryParam exportParam, UserInfoToken userInfo) {
        DecEntry decEntry = decEntryDtoMapper.toPo(exportParam);
        decEntry.setTradeCode(userInfo.getCompany());
        List<DecEntryDto> decEntryDtos = new ArrayList<>();
        List<DecEntry> decEntrys = decEntryMapper.getList(decEntry);
        if (CollectionUtils.isNotEmpty(decEntrys)) {
            decEntryDtos = decEntrys.stream().map(head -> {
                DecEntryDto dto = decEntryDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decEntryDtos;
    }


}
