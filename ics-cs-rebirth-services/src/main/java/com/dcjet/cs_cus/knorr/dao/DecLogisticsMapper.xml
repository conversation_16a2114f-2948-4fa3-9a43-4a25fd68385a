<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs_cus.knorr.dao.DecLogisticsMapper">
    <resultMap id="DecLogisticsResultMap" type="com.dcjet.cs_cus.knorr.model.DecLogistics">
        <result column="SA" property="sa" jdbcType="VARCHAR"/>
        <result column="EMS_LIST_NO" property="emsListNo" jdbcType="VARCHAR"/>
        <result column="BILL_EMS_LIST_NO" property="billEmsListNo" jdbcType="VARCHAR"/>
        <result column="HAWB" property="hawb" jdbcType="VARCHAR"/>
        <result column="OVERSEAS_SHIPPER_NAME" property="overseasShipperName" jdbcType="VARCHAR"/>
        <result column="NOTE_1" property="note1" jdbcType="VARCHAR"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="ENTRY_STATUS" property="entryStatus" jdbcType="VARCHAR"/>
        <result column="ENTRY_STATUS_NAME" property="entryStatusName" jdbcType="VARCHAR"/>
        <result column="PLAN_ARRIVAL_DATE" property="planArrivalDate" jdbcType="DATE"/>
        <result column="DELIVERY_DATE" property="deliveryDate" jdbcType="DATE"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="DELIVERY_DATE" property="deliveryDate" jdbcType="DATE"/>
    </resultMap>
    <sql id="condition">
        and T.TRADE_CODE = #{tradeCode}
        <if test="sa != null and sa != ''">
            and T.SA like concat(concat('%',#{sa}),'%')
        </if>
        <if test="emsListNo != null and emsListNo != ''">
            and T.EMS_LIST_NO like concat(concat('%',#{emsListNo}),'%')
        </if>
        <if test="billEmsListNo != null and billEmsListNo != ''">
            and T.EMS_LIST_NO like concat(concat('%',#{billEmsListNo}),'%')
        </if>
        <if test="hawb != null and hawb != ''">
            and T.HAWB like concat(concat('%',#{hawb}),'%')
        </if>
        <if test="overseasShipperName != null and overseasShipperName != ''">
            and T.OVERSEAS_SHIPPER_NAME like concat(concat('%',#{overseasShipperName}),'%')
        </if>
        <if test="note1 != null and note1 != ''">
            and T.NOTE_1 like concat(concat('%',#{note1}),'%')
        </if>
        <if test="orderNo != null and orderNo != ''">
            and T.ORDER_NO like concat(concat('%',#{orderNo}),'%')
        </if>
        <if test='_databaseId == "postgresql" '>
            <if test="planArrivalDateFrom != null and planArrivalDateFrom != ''">
                <![CDATA[ and T.PLAN_ARRIVAL_DATE >= to_timestamp(#{planArrivalDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="planArrivalDateTo != null and planArrivalDateTo != ''">
                <![CDATA[ and T.PLAN_ARRIVAL_DATE < to_timestamp(#{planArrivalDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="planArrivalDateFrom != null and planArrivalDateFrom != ''">
                <![CDATA[ and T.PLAN_ARRIVAL_DATE > to_date(#{planArrivalDateFrom},'yyyy-MM-dd hh24:mi:ss') ]]>
            </if>
            <if test="planArrivalDateTo != null and planArrivalDateTo != ''">
                <![CDATA[ and T.PLAN_ARRIVAL_DATE <= to_date(#{planArrivalDateTo},'yyyy-MM-dd hh24:mi:ss') +1 ]]>
            </if>
        </if>
        and T.BOND_MARK='1'
    </sql>

    <select id="getList" resultMap="DecLogisticsResultMap" parameterType="com.dcjet.cs_cus.knorr.model.DecLogistics">
        SELECT * FROM
        (SELECT
        T.SA,
        T.EMS_LIST_NO,
        BILL.EMS_LIST_NO AS BILL_EMS_LIST_NO,
        T.HAWB,
        T.OVERSEAS_SHIPPER_NAME,
        LIST.NOTE_1,
        LIST.ORDER_NO,
        ENTRY.ENTRY_STATUS,
        b.PARAMS_NAME as ENTRY_STATUS_NAME,
        TRACK.PLAN_ARRIVAL_DATE,
        TRACK.DELIVERY_DATE,
        T.INSERT_USER_NAME,
        T.ARRIVAL_PORT_DATE,
        T.TRADE_CODE,
        T.BOND_MARK
        FROM
        T_DEC_ERP_I_HEAD_N T

        <if test='_databaseId == "postgresql" '>
            LEFT JOIN (SELECT string_agg(distinct NOTE_1,',') AS NOTE_1,string_agg(distinct ORDER_NO,',') AS
            ORDER_NO,HEAD_ID FROM T_DEC_ERP_I_LIST_N where TRADE_CODE = #{tradeCode} GROUP BY HEAD_ID) LIST ON
            LIST.HEAD_ID = T.SID
        </if>

        <if test='_databaseId != "postgresql" '>
            LEFT JOIN (SELECT WMSYS.WM_CONCAT(distinct NOTE_1) AS NOTE_1,WMSYS.WM_CONCAT(distinct ORDER_NO) AS
            ORDER_NO,HEAD_ID FROM T_DEC_ERP_I_LIST_N where TRADE_CODE = #{tradeCode} GROUP BY HEAD_ID) LIST
            ON LIST.HEAD_ID = T.SID
        </if>
        LEFT JOIN T_DEC_I_BILL_HEAD BILL ON BILL.HEAD_ID = T.SID
        LEFT JOIN T_DEC_I_ENTRY_HEAD ENTRY ON ENTRY.ERP_HEAD_ID = T.SID
        LEFT JOIN T_DEC_I_CUSTOMS_TRACK TRACK ON TRACK.EMS_LIST_NO=BILL.EMS_LIST_NO
        LEFT JOIN T_BI_CUSTOMER_PARAMS b ON ENTRY.ENTRY_STATUS = b.PARAMS_CODE
        ) T
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="selectDataCountAll" resultType="java.lang.Integer">
        SELECT count (1) FROM
        ( SELECT
        T.SA,
        T.EMS_LIST_NO,
        BILL.EMS_LIST_NO AS BILL_EMS_LIST_NO,
        T.HAWB,
        T.OVERSEAS_SHIPPER_NAME,
        LIST.NOTE_1,
        LIST.ORDER_NO,
        ENTRY.ENTRY_STATUS,
        b.PARAMS_NAME as ENTRY_STATUS_NAME,
        TRACK.PLAN_ARRIVAL_DATE,
        TRACK.DELIVERY_DATE,
        T.INSERT_USER_NAME,
        T.ARRIVAL_PORT_DATE,
        T.TRADE_CODE,
        T.BOND_MARK
        FROM
        T_DEC_ERP_I_HEAD_N T

        <if test='_databaseId == "postgresql" '>
            LEFT JOIN (SELECT string_agg(distinct NOTE_1,',') AS NOTE_1,string_agg(distinct ORDER_NO,',') AS
            ORDER_NO,HEAD_ID FROM T_DEC_ERP_I_LIST_N where TRADE_CODE = #{tradeCode} GROUP BY HEAD_ID) LIST ON
            LIST.HEAD_ID = T.SID
        </if>

        <if test='_databaseId != "postgresql" '>
            LEFT JOIN (SELECT WMSYS.WM_CONCAT(distinct NOTE_1) AS NOTE_1,WMSYS.WM_CONCAT(distinct ORDER_NO) AS
            ORDER_NO,HEAD_ID FROM T_DEC_ERP_I_LIST_N where TRADE_CODE = #{tradeCode} GROUP BY HEAD_ID) LIST
            ON LIST.HEAD_ID = T.SID
        </if>
        LEFT JOIN T_DEC_I_BILL_HEAD BILL ON BILL.HEAD_ID = T.SID
        LEFT JOIN T_DEC_I_ENTRY_HEAD ENTRY ON ENTRY.ERP_HEAD_ID = T.SID
        LEFT JOIN T_DEC_I_CUSTOMS_TRACK TRACK ON TRACK.EMS_LIST_NO=BILL.EMS_LIST_NO
        LEFT JOIN T_BI_CUSTOMER_PARAMS b ON ENTRY.ENTRY_STATUS = b.PARAMS_CODE
        ) T
        <where>
            <include refid="condition"></include>
        </where>
    </select>
</mapper>
