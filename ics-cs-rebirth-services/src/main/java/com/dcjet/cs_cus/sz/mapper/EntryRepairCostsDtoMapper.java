package com.dcjet.cs_cus.sz.mapper;

import com.dcjet.cs.dto_cus.sz.EntryRepairCostsDto;
import com.dcjet.cs.dto_cus.sz.EntryRepairCostsParam;
import com.dcjet.cs_cus.sz.model.EntryRepairCosts;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EntryRepairCostsDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    EntryRepairCostsDto toDto(EntryRepairCosts po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    EntryRepairCosts toPo(EntryRepairCostsParam param);

    /**
     * 数据库原始数据更新
     *
     * @param entryRepairCostsParam
     * @param entryRepairCosts
     */
    void updatePo(EntryRepairCostsParam entryRepairCostsParam, @MappingTarget EntryRepairCosts entryRepairCosts);

    default void patchPo(EntryRepairCostsParam entryRepairCostsParam, EntryRepairCosts entryRepairCosts) {
        // TODO 自行实现局部更新
    }
}
