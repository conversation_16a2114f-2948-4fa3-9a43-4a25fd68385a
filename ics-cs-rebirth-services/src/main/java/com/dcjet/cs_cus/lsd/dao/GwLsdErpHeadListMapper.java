package com.dcjet.cs_cus.lsd.dao;

import com.dcjet.cs_cus.lsd.model.ErpHeadListForFtp;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * GwLsdFtpReadLog
 *
 * <AUTHOR>
 * @date: 9/4/2024
 */
public interface GwLsdErpHeadListMapper extends Mapper<ErpHeadListForFtp> {

    void checkData(String batchNo);

    @Select("select count(1) from t_gw_lsd_ftp_erp_head_list_temp where batch_no = #{batchNo} and temp_flag = '1'")
    int selectErrorCount(String batchNo);

    @Select("select * from t_gw_lsd_ftp_erp_head_list_temp where batch_no = #{batchNo} and temp_flag = '1' order by serial_no")
    List<ErpHeadListForFtp> getErrorData(String batchNo);

    void insertSelect(@Param("tradeCode") String tradeCode,@Param("batchNo") String batchNo);
}
