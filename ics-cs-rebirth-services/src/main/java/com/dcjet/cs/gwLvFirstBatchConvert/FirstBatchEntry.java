package com.dcjet.cs.gwLvFirstBatchConvert;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class FirstBatchEntry {
    /**
     * 品牌
     */
    private FirstBatchDic.BRAND brand;

    /**
     * 批号
     */
    private String firstBatch;

    /**
     * erp料号
     */
    private String erpCopGNo;

    /**
     * 货物名称（海关类别）（中文名称）
     * 品牌为：ADP，批号长度为7时需提供
     */
    private String goodsName;

    /**
     * 转换后的结果
     */
    private String convertResult;

    /**
     * 错误信息（验证不通过信息）
     */
    private List<String> errorMess;
}
