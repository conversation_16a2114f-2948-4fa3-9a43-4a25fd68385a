package com.dcjet.cs.dec.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_DEC_E_LOGISTICS_TRACK")
public class DecELogisticsTrack extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
	 * 订舱日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "BOOKING_DATE")
	private  Date bookingDate;

	@Column(name = "warehouse_name")
	private  String warehouseName;

	/**
	 * 订舱日期-开始
	 */
	@Transient
	private String bookingDateFrom;
	/**
	 * 订舱日期-结束
	 */
	@Transient
	private String bookingDateTo;
	/**
	 * 离境口岸
	 */
	@Column(name = "ENTRY_PORT")
	private  String entryPort;
	/**
	 * 运输方式
	 */
	@Column(name = "TRAF_MODE")
	private  String trafMode;
	/**
	 * 承运人
	 */
	@Column(name = "CARRIER")
	private  String carrier;
	/**
	 * 出货日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "SHIP_DATE")
	private  Date shipDate;
	/**
	 * 出货日期-开始
	 */
	@Transient
	private String shipDateFrom;
	/**
	 * 出货日期-结束
	 */
	@Transient
	private String shipDateTo;
	/**
	 * 车牌号
	 */
	@Column(name = "PLATE_NUM")
	private  String plateNum;
	/**
	 * 提货人
	 */
	@Column(name = "DELIVERY_PERSON")
	private  String deliveryPerson;
	/**
	 * 主提运单号
	 */
	@Column(name = "MAWB")
	private  String mawb;
	/**
	 * 提运单号
	 */
	@Column(name = "HAWB")
	private  String hawb;
	/**
	 * 离境日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "DEPARTURE_DATE")
	private  Date departureDate;
	/**
	 * 离境日期-开始
	 */
	@Transient
	private String departureDateFrom;
	/**
	 * 离境日期-结束
	 */
	@Transient
	private String departureDateTo;
	/**
	 * 到货日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "ARRIVAL_DATE")
	private  Date arrivalDate;
	/**
	 * 到货日期-开始
	 */
	@Transient
	private String arrivalDateFrom;
	/**
	 * 到货日期-结束
	 */
	@Transient
	private String arrivalDateTo;
	/**
	 * 企业代码
	 */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
	 * 数据状态
	 */
	@Column(name = "STATUS")
	private  String status;
	/**
	 * 单据内部编号
	 */
	@Column(name = "EMS_LIST_NO")
	private  String emsListNo;
	/**
	 * 运输费用
	 */
	@Column(name = "TRAF_COST")
	private  BigDecimal trafCost;
	/**
	 * 入/出库单号
	 */
	@Column(name = "IN_OUT_NO")
	private  String inOutNo;
	/**
	 * 进仓编号
	 */
	@Column(name = "STORAGE_NO")
	private  String storageNo;

	/**
	 * 国际费用
	 */
	@Column(name = "INTERNATIONAL_COST")
	private BigDecimal internationalCost;

	/**
	 * 国内费用
	 */
	@Column(name = "DOMESTIC_COST")
	private  BigDecimal domesticCost;

	/**
	 * 报关费
	 */
	@Column(name = "CUSTOMS_FEE")
	private BigDecimal customsFee;
	/**
	 * 运费凭据号
	 */
	@Column(name = "FREIGHT_NO")
	private  String freightNo;

	/**
	 * 订舱单号
	 */
	@Column(name = "ORDER_NO")
	private  String orderNo;
	/**
	 * 出货通知书号
	 */
	@Column(name = "SHIPMENT_NO")
	private  String shipmentNo;

	@Transient
	private String goodsStatus;
	@Transient
	private String goodsStatusName;

	@Transient
	private String wrapType;
	@Transient
	private String packNum;
	@Transient
	private String grossWt;
	@Transient
	private String headInsertTime;
	@Transient
	private String headInsertTimeFrom;
	@Transient
	private String headInsertTimeTo;
	@Transient
	private String invoiceNo;
	@Transient
	private String emsListNoTo;

	/**
	 * 递送日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "DELIVERY_DATE")
	private  Date deliveryDate;

	/**
	 * 合同协议号
	 */
	@Transient
	private  String contrNo;


	/**
	 * 预计离港日期（ETD）
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "ETD")
	private  Date etd;
	/**
	 * 预计离港日期（ETD）-开始
	 */
	@Transient
	private String etdFrom;
	/**
	 * 预计离港日期（ETD）-结束
	 */
	@Transient
	private String etdTo;
	/**
	 * 预计到港日期（ETA）
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "ETA")
	private  Date eta;
	/**
	 * 预计到港日期（ETA）-开始
	 */
	@Transient
	private String etaFrom;
	/**
	 * 预计到港日期（ETA）-结束
	 */
	@Transient
	private String etaTo;
	/**
	 * 实际离港日期（ATD）
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "ATD")
	private  Date atd;
	/**
	 * 实际离港日期（ATD）-开始
	 */
	@Transient
	private String atdFrom;
	/**
	 * 实际离港日期（ATD）-结束
	 */
	@Transient
	private String atdTo;
	/**
	 * 实际到港日期（ATA）
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "ATA")
	private  Date ata;
	/**
	 * 实际到港日期（ATA）-开始
	 */
	@Transient
	private String ataFrom;
	/**
	 * 实际到港日期（ATA）-结束
	 */
	@Transient
	private String ataTo;

	/**
	 * 仓储天数
	 */
	@Column(name = "STORE_TIME")
	private Integer storeTime;
	/**
	 * 国内物流属性
	 */
	@Column(name = "DOMESTIC_LOGISTICS")
	private String domesticLogistics;

	/**
	 * 国际物流属性
	 */
	@Column(name = "INTERNATIONAL_LOGISTICS")
	private String internationalLogistics;
	/**
	 * 境外收货人
	 */
	@Transient
	private String overseasShipper;
	/**
	 * 境外收货人名称
	 */
	@Transient
	private String overseasShipperName;
	/**
	 * 订舱异常备注
	 */
	@Column(name = "BOOKING_ERR_NOTE")
	private String bookingErrNote;
	/**
	 * 预计出货日期
	 */
	@Column(name = "ESTIMATE_SHIPMENT_DATE")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date estimateShipmentDate;
	/**
	 * 出货日期备注
	 */
	@Column(name = "SHIPMENT_ERR_NOTE")
	private String shipmentErrNote;
	/**
	 * 出货跟踪日期
	 */
	@Column(name = "SHIPMENT_TRACK_DATE")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date shipmentTrackDate;

	@Transient
	private String estimateShipmentDateFrom;
	@Transient
	private String estimateShipmentDateTo;
	@Transient
	private String tradeCountry;
	@Transient
	private String sumQty;
	@Transient
	private String forwardCode;
	@Transient
	private String forwardName;
	/**
	 * 车柜信息
	 */
	@Column(name = "CABINET_INFORMATION")
	private String cabinetInformation;
}
