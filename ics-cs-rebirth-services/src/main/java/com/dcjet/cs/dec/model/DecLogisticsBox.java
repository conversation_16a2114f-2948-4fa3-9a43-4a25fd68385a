package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-8-5
 */
@Setter
@Getter
@Table(name = "t_dec_logistics_box")
public class DecLogisticsBox implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     *
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     *
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     *
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     *
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     *
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     *
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     *
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 预录入SID
     */
    @Column(name = "head_id")
    private String headId;
    /**
     * 进出口标志
     */
    @Column(name = "i_e_mark")
    @JsonProperty("iEMark")
    private String iEMark;
    /**
     * 宽
     */
    @Column(name = "box_width")
    private BigDecimal boxWidth;
    /**
     * 长
     */
    @Column(name = "box_length")
    private BigDecimal boxLength;
    /**
     * 高
     */
    @Column(name = "box_height")
    private BigDecimal boxHeight;
    /**
     * 件数
     */
    @Column(name = "pack_num")
    private BigDecimal packNum;
    /**
     * 体积重
     */
    @Column(name = "volume_weight")
    private BigDecimal volumeWeight;
}
