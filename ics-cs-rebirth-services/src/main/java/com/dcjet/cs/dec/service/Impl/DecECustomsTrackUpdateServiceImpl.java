package com.dcjet.cs.dec.service.Impl;

import com.dcjet.cs.base.service.impl.BasicServiceImpl;
import com.dcjet.cs.common.model.ImportData;
import com.dcjet.cs.common.model.ImportValidation;
import com.dcjet.cs.dec.dao.DecECustomsTrackMapper;
import com.dcjet.cs.dec.dao.DecECustomsTrackUpdateMapper;
import com.dcjet.cs.dec.mapper.DecECustomsTrackUpdateDtoMapper;
import com.dcjet.cs.dec.model.DecECustomsTrack;
import com.dcjet.cs.dec.service.DecECustomsTrackUpdateService;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto.dec.DecECustomsTrackDto;
import com.dcjet.cs.dto.dec.DecECustomsTrackParam;
import com.dcjet.cs.dto.dec.DecECustomsTrackUpdateParam;
import com.dcjet.cs.erp.dao.DecEBillHeadMapper;
import com.dcjet.cs.erp.model.DecEBillHead;
import com.qiniu.util.StringUtils;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Service
public class DecECustomsTrackUpdateServiceImpl
        extends BasicServiceImpl<DecECustomsTrackUpdateMapper, DecECustomsTrack, DecECustomsTrackDto, DecECustomsTrackParam, DecECustomsTrackUpdateParam, DecECustomsTrackUpdateDtoMapper>
        implements DecECustomsTrackUpdateService {
    @Resource
    private DecECustomsTrackUpdateDtoMapper decECustomsTrackUpdateDtoMapper;
    @Resource
    private DecECustomsTrackMapper decECustomsTrackMapper;
    @Resource
    private DecECustomsTrackUpdateMapper decECustomsTrackUpdateMapper;
    @Resource
    private DecEBillHeadMapper decEBillHeadMapper;

    @Override
    public ImportValidation<DecECustomsTrackUpdateParam> importValidation(ImportData<DecECustomsTrackUpdateParam> importData, ExcelImportParam param, UserInfoToken token) {
        List<DecECustomsTrackUpdateParam> correctList = importData.getCorrectData();
        List<DecECustomsTrackUpdateParam> corrects = new ArrayList<>(correctList.size());
        List<DecECustomsTrackUpdateParam> errors = new ArrayList<>(correctList.size());
        for (DecECustomsTrackUpdateParam val : correctList) {
            if (val.getEmsListNo() != null) {
                if (decECustomsTrackMapper.selectSet(val.getEmsListNo(), token.getCompany()) > 1) {
                    val.setErrMsg(val.getEmsListNo() + xdoi18n.XdoI18nUtil.t("有多个清单内部编号"));
                } else {
                    DecECustomsTrack decECustomsTrack = decECustomsTrackMapper.selectList(val.getEmsListNo(), token.getCompany());
                    if (decECustomsTrack != null) {
                        val.setSid(decECustomsTrack.getSid());
                        val.setHeadId(decECustomsTrack.getHeadId());
                    } else {
                        val.setErrMsg(xdoi18n.XdoI18nUtil.t("清单内部编号不存在"));
                    }
                }
            } else {
                val.setErrMsg(xdoi18n.XdoI18nUtil.t("清单内部编号不能为空"));
            }

            if (StringUtils.isNullOrEmpty(val.getErrMsg())) {
                corrects.add(val);
            } else {
                errors.add(val);
            }
        }


        return new ImportValidation<>(corrects, errors);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importSave(ImportData<DecECustomsTrackUpdateParam> data, UserInfoToken token) {

        if (data.getBizParam() == null) {
            throw new ArgumentException();
        }

        Date date = new Date();
        int sum = 0;
        List<String> list = new ArrayList<>();
        data.getHeadFieldMap().keySet().forEach(its -> {
            String value = data.getHeadFieldMap().get(its);
            list.add(value);
        });

        for (DecECustomsTrackUpdateParam param : data.getCorrectData()) {
            DecECustomsTrack decECustomsTrack = decECustomsTrackUpdateDtoMapper.toPo(param);
            decECustomsTrack.setUpdateUser(token.getUserNo());
            decECustomsTrack.setUpdateTime(date);
            decECustomsTrack.setUpdateUserName(token.getUserName());
            List<String> updateProperties = decECustomsTrack.preUpdateProperties();
            list.addAll(updateProperties);

            //更新清单
            DecEBillHead decEBillHead = decEBillHeadMapper.selectByPrimaryKey(decECustomsTrack.getHeadId());
            if (decEBillHead != null) {
                decEBillHead.setEntryNo(decECustomsTrack.getEntryNo());
                decEBillHead.setEntryDeclareDate(decECustomsTrack.getDeclareDate());
                decEBillHead.setSeqNo(decECustomsTrack.getSeqNo());
                decEBillHeadMapper.updateByPrimaryKey(decEBillHead);

                String ddate = null;
                if (decECustomsTrack.getDeclareDate() != null) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    ddate = simpleDateFormat.format(decECustomsTrack.getDeclareDate());
                }
                decECustomsTrackMapper.updateEntryHead(decECustomsTrack.getEntryNo(), ddate, decEBillHead.getEmsListNo(), token.getCompany(),decECustomsTrack.getEntryStatus(), decECustomsTrack.getSeqNo());
            }
            //
            decECustomsTrackUpdateMapper.updateByPrimaryKeySelectiveColumns(decECustomsTrack, list);
            sum++;
        }

        return sum;
    }
}


