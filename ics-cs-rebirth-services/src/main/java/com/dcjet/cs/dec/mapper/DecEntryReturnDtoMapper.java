package com.dcjet.cs.dec.mapper;

import com.dcjet.cs.dec.model.DecEntryReturn;
import com.dcjet.cs.dto.dec.DecEntryReturnDto;
import com.dcjet.cs.dto.dec.DecEntryReturnParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-11-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecEntryReturnDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecEntryReturnDto toDto(DecEntryReturn po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecEntryReturn toPo(DecEntryReturnParam param);
    /**
     * 数据库原始数据更新
     * @param decEntryReturnParam
     * @param decEntryReturn
     */
    void updatePo(DecEntryReturnParam decEntryReturnParam, @MappingTarget DecEntryReturn decEntryReturn);

    default void patchPo(DecEntryReturnParam decEntryReturnParam, DecEntryReturn decEntryReturn) {
        // TODO 自行实现局部更新
    }
}
