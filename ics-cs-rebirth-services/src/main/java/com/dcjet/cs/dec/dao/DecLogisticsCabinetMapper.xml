<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.DecLogisticsCabinetMapper">
    <resultMap id="logisticsCabinetResultMap" type="com.dcjet.cs.dec.model.DecLogisticsCabinet">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
		<result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="IE_MARK" property="ieMark" jdbcType="VARCHAR" />
		<result column="CABINET_ATTR" property="cabinetAttr" jdbcType="VARCHAR" />
		<result column="CABINET_TYPE" property="cabinetType" jdbcType="VARCHAR" />
		<result column="CABINET_AMOUNT" property="cabinetAmount" jdbcType="NUMERIC" />
		<result column="CABINET_NAME" property="cabinetName" jdbcType="VARCHAR" />
        <result column="HEAD_ID" property="headId" jdbcType="VARCHAR"/>
 	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,TRADE_CODE
     ,INSERT_USER
     ,INSERT_TIME
     ,INSERT_USER_NAME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,UPDATE_USER_NAME
     ,IE_MARK
     ,CABINET_ATTR
     ,CABINET_TYPE
     ,CABINET_AMOUNT
     ,CABINET_NAME
     ,HEAD_ID
    </sql>
    <sql id="condition">
        and t.TRADE_CODE = #{tradeCode}
        <if test="headId != null and headId != ''">
            and t.HEAD_ID = #{headId}
        </if>
        <if test="cabinetAttr != null and cabinetAttr != ''">
            and t.CABINET_ATTR = #{cabinetAttr}
        </if>
        <if test="ieMark != null and ieMark != ''">
            and t.IE_MARK = #{ieMark}
        </if>
        <if test="cabinetType != null and cabinetType != ''">
            and t.CABINET_TYPE = #{cabinetType}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="logisticsCabinetResultMap" parameterType="com.dcjet.cs.dec.model.DecLogisticsCabinet">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_LOGISTICS_CABINET t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.insert_time
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_LOGISTICS_CABINET t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete from T_LOGISTICS_CABINET t where t.HEAD_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <update id="updateSelect">
        <if test="ieMark=='I'.toString()">
            UPDATE T_DEC_I_LOGISTICS_TRACK
            set CABINET_INFORMATION = (SELECT string_agg(concat_ws('*', CABINET_NAME, CABINET_AMOUNT), '/')
            FROM T_LOGISTICS_CABINET t
            WHERE t.TRADE_CODE = #{tradeCode}
            and t.HEAD_ID = #{headId}
            and t.IE_MARK = #{ieMark})
            where trade_code = #{tradeCode}
            and sid = #{headId};
        </if>
        <if test="ieMark=='E'.toString()">
            UPDATE T_DEC_E_LOGISTICS_TRACK
            set CABINET_INFORMATION = (SELECT string_agg(concat_ws('*', CABINET_NAME, CABINET_AMOUNT), '/')
            FROM T_LOGISTICS_CABINET t
            WHERE t.TRADE_CODE = #{tradeCode}
            and t.HEAD_ID = #{headId}
            and t.IE_MARK = #{ieMark})
            where trade_code = #{tradeCode}
            and sid = #{headId};
        </if>
    </update>
</mapper>
