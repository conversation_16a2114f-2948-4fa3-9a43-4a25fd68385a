package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.DecLogisticsBoxMapper;
import com.dcjet.cs.dec.mapper.DecLogisticsBoxDtoMapper;
import com.dcjet.cs.dec.model.DecLogisticsBox;
import com.dcjet.cs.dto.dec.DecLogisticsBoxDto;
import com.dcjet.cs.dto.dec.DecLogisticsBoxParam;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-8-5
 */
@Service
public class DecLogisticsBoxService extends BaseService<DecLogisticsBox> {
    @Resource
    private DecLogisticsBoxMapper decLogisticsBoxMapper;
    @Resource
    private DecLogisticsBoxDtoMapper decLogisticsBoxDtoMapper;

    @Override
    public Mapper<DecLogisticsBox> getMapper() {
        return decLogisticsBoxMapper;
    }

    /**
     * 获取分页信息
     *
     * @param decLogisticsBoxParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<DecLogisticsBoxDto>> getListPaged(DecLogisticsBoxParam decLogisticsBoxParam, PageParam pageParam) {
        // 启用分页查询
        DecLogisticsBox decLogisticsBox = decLogisticsBoxDtoMapper.toPo(decLogisticsBoxParam);
        Page<DecLogisticsBox> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decLogisticsBoxMapper.getList(decLogisticsBox));
        List<DecLogisticsBoxDto> decLogisticsBoxDtos = page.getResult().stream().map(head -> {
            DecLogisticsBoxDto dto = decLogisticsBoxDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<DecLogisticsBoxDto>> paged = ResultObject.createInstance(decLogisticsBoxDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param decLogisticsBoxParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecLogisticsBoxDto insert(DecLogisticsBoxParam decLogisticsBoxParam, UserInfoToken userInfo) {
        DecLogisticsBox decLogisticsBox = decLogisticsBoxDtoMapper.toPo(decLogisticsBoxParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        decLogisticsBox.setSid(sid);
        decLogisticsBox.setInsertUser(userInfo.getUserNo());
        decLogisticsBox.setInsertTime(new Date());
        decLogisticsBox.setInsertUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = decLogisticsBoxMapper.insert(decLogisticsBox);
        return insertStatus > 0 ? decLogisticsBoxDtoMapper.toDto(decLogisticsBox) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param decLogisticsBoxParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecLogisticsBoxDto update(DecLogisticsBoxParam decLogisticsBoxParam, UserInfoToken userInfo) {
        DecLogisticsBox decLogisticsBox = decLogisticsBoxMapper.selectByPrimaryKey(decLogisticsBoxParam.getSid());
        decLogisticsBoxDtoMapper.updatePo(decLogisticsBoxParam, decLogisticsBox);
        decLogisticsBox.setUpdateUser(userInfo.getUserNo());
        decLogisticsBox.setUpdateTime(new Date());
        decLogisticsBox.setUpdateUserName(userInfo.getUserName());
        // 更新数据
        int update = decLogisticsBoxMapper.updateByPrimaryKey(decLogisticsBox);
        return update > 0 ? decLogisticsBoxDtoMapper.toDto(decLogisticsBox) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        decLogisticsBoxMapper.deleteBySids(sids);
    }
}
