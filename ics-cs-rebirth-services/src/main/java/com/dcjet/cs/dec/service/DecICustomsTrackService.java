package com.dcjet.cs.dec.service;

import com.dcjet.cs.agent.CustomsTrackAgentService;
import com.dcjet.cs.common.dao.GwMailSendTaskAttachMapper;
import com.dcjet.cs.common.dao.GwMailSendTaskHeadMapper;
import com.dcjet.cs.common.model.GwMailSendTaskAttach;
import com.dcjet.cs.common.model.GwMailSendTaskHead;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.dao.DecICustomsTrackMapper;
import com.dcjet.cs.dec.mapper.DecICustomsTrackDtoMapper;
import com.dcjet.cs.dec.model.DecICustomsTrack;
import com.dcjet.cs.dec.model.DecICustomsTrackNotice;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.dto.war.WarringPassConfigDto;
import com.dcjet.cs.dto.war.WarringPassExpandConfigDto;
import com.dcjet.cs.erp.dao.DecIBillHeadMapper;
import com.dcjet.cs.erp.model.DecIBillHead;
import com.dcjet.cs.erp.service.DecErpIHeadNService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.WeekUnit;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.dcjet.cs.war.dao.WarringEmailConfigMapper;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.dcjet.cs.war.service.WarringPassExpandConfigService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.hsqldb.lib.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.beans.Transient;
import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Service
public class DecICustomsTrackService extends BaseService<DecICustomsTrack> {
    @Resource
    private DecICustomsTrackMapper decICustomsTrackMapper;
    @Resource
    private DecICustomsTrackDtoMapper decICustomsTrackDtoMapper;
    @Resource
    private GwMailSendTaskHeadMapper gwMailSendTaskHeadMapper;
    @Resource
    private GwMailSendTaskAttachMapper gwMailSendTaskAttachMapper;
    @Resource
    private WarringEmailConfigMapper warringEmailConfigMapper;

    @Override
    public Mapper<DecICustomsTrack> getMapper() {
        return decICustomsTrackMapper;
    }

    @Resource
    private DecIBillHeadMapper decIBillHeadMapper;
    @Resource
    private CustomsTrackAgentService agentService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExportService exportService;
    @Resource
    private AuditLogUtil auditLogUtil;

    @Resource(name = "tempXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource
    private DecEntryReturnService decEntryReturnService;
    @Resource
    private WarringPassConfigService warringPassConfigService;
    @Resource
    private Validator validator;
    @Resource
    private WarringPassExpandConfigService warringPassExpandConfigService;
    @Resource
    private DecErpIHeadNService decErpIHeadNService;
    /**
     * 获取分页信息
     *
     * @param decICustomsTrackParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<DecICustomsTrackDto>> getListPaged(DecICustomsTrackParam decICustomsTrackParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        DecICustomsTrack decICustomsTrack = decICustomsTrackDtoMapper.toPo(decICustomsTrackParam);
        decICustomsTrack.setTradeCode(userInfo.getCompany());

        decICustomsTrack.setInsertUser(decErpIHeadNService.getAuthority(userInfo));

        // 代理用户参数设置
        agentService.agentHandler(decICustomsTrack, userInfo);
        Page<DecICustomsTrack> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decICustomsTrackMapper.getList(decICustomsTrack));
        List<DecICustomsTrackDto> decICustomsTrackDtos = page.getResult().stream().map(head -> {
            DecICustomsTrackDto dto = decICustomsTrackDtoMapper.toDto(head);
            dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<DecICustomsTrackDto>> paged = ResultObject.createInstance(decICustomsTrackDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    private void getAuthority(DecICustomsTrack decICustomsTrack, UserInfoToken userInfo) {
        List<WarringPassExpandConfigDto> warringPassExpandConfigDtos = warringPassExpandConfigService.selectAll(userInfo);
        if (warringPassExpandConfigDtos.size()>0){
            WarringPassExpandConfigDto warringPassExpandConfigDto = warringPassExpandConfigDtos.get(0);
//            if (StringUtils.equals(warringPassExpandConfigDto.getImportAuthority(), ConstantsStatus.STATUS_1)){
//                decICustomsTrack.setInsertUser(userInfo.getUserNo());
//            }
        }
    }

    /**
     * 功能描述:新增
     *
     * @param decICustomsTrackParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecICustomsTrackDto insert(DecICustomsTrackParam decICustomsTrackParam, UserInfoToken userInfo) {
        DecICustomsTrack decICustomsTrack = decICustomsTrackDtoMapper.toPo(decICustomsTrackParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        decICustomsTrack.setTradeCode(userInfo.getCompany());
        decICustomsTrack.setSid(sid);
        decICustomsTrack.setInsertUser(userInfo.getUserNo());
        decICustomsTrack.setInsertUserName(userInfo.getUserName());
        decICustomsTrack.setInsertTime(new Date());
        if (!StringUtil.isEmpty(decICustomsTrack.getEntryNo())) {
            int length = decICustomsTrack.getEntryNo().length();
            if (length < 18 || length > 18) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("报关单号只允许维护18位字符"));
            }
        }

        /*
        进口报关追踪：点击保存时，{缴税}中的“总税款”栏位自动根据“关税”“增值税”“关税缓税利息”“增值税缓税利息”“附加税”“总税款”“其他进口环节税”“消费税(RMB)”这几个栏位得出汇总值。
            dutyPrice      关税
            taxPrice       增值税
            dutyInterest   关税缓征利息
            taxInterest    增值税缓征利息
            addTaxPrice    附加税
            otherTax       其他进口环节税
            exciseTax      消费税
            exciseInterest 消费税缓息
         */
        BigDecimal dutyPrice=decICustomsTrack.getDutyPrice()==null?BigDecimal.ZERO:decICustomsTrack.getDutyPrice();
        BigDecimal taxPrice=decICustomsTrack.getTaxPrice()==null?BigDecimal.ZERO:decICustomsTrack.getTaxPrice();
        BigDecimal dutyInterest=decICustomsTrack.getDutyInterest()==null?BigDecimal.ZERO:decICustomsTrack.getDutyInterest();
        BigDecimal taxInterest=decICustomsTrack.getTaxInterest()==null?BigDecimal.ZERO:decICustomsTrack.getTaxInterest();
        BigDecimal addTaxPrice=decICustomsTrack.getAddTaxPrice()==null?BigDecimal.ZERO:decICustomsTrack.getAddTaxPrice();
        BigDecimal otherTax=decICustomsTrack.getOtherTax()==null?BigDecimal.ZERO:decICustomsTrack.getOtherTax();
        BigDecimal exciseTax=decICustomsTrack.getExciseTax()==null?BigDecimal.ZERO:decICustomsTrack.getExciseTax();
        BigDecimal exciseInterest = decICustomsTrack.getExciseInterest() == null ?
                BigDecimal.ZERO: decICustomsTrack.getExciseInterest();
        BigDecimal antiDumpingDuty = decICustomsTrack.getAntiDumpingDuty() == null ?BigDecimal.ZERO: decICustomsTrack.getAntiDumpingDuty();

        decICustomsTrack.setTaxTotal(dutyPrice.add(taxPrice).add(dutyInterest).add(taxInterest).add(addTaxPrice).add(otherTax).add(exciseTax).add(exciseInterest).add(antiDumpingDuty));

        // 新增数据
        int insertStatus = decICustomsTrackMapper.insert(decICustomsTrack);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(),"进口管理-进口报关追踪",userInfo,decICustomsTrack);
        return insertStatus > 0 ? decICustomsTrackDtoMapper.toDto(decICustomsTrack) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param decICustomsTrackParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecICustomsTrackDto update(DecICustomsTrackParam decICustomsTrackParam, UserInfoToken userInfo) {
        DecICustomsTrack decICustomsTrack = decICustomsTrackMapper.selectByPrimaryKey(decICustomsTrackParam.getSid());
        if(decICustomsTrack!=null) {
            decICustomsTrackDtoMapper.updatePo(decICustomsTrackParam, decICustomsTrack);

            if (!StringUtil.isEmpty(decICustomsTrack.getEntryNo())) {
                int length = decICustomsTrack.getEntryNo().length();
                if (length < 18 || length > 18) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("报关单号只允许维护18位字符"));
                }
            }
            /*else if (length > 18) {
                throw new ErrorException(400, "报关单号只允许维护18位字符");
            }*/

            decICustomsTrack.setUpdateUser(userInfo.getUserNo());
            decICustomsTrack.setUpdateUserName(userInfo.getUserName());
            decICustomsTrack.setUpdateTime(new Date());
            decICustomsTrack.setDeclareDate(decICustomsTrack.getEntryDeclareDate());
             /*
        进口报关追踪：点击保存时，{缴税}中的“总税款”栏位自动根据“关税”“增值税”“关税缓税利息”“增值税缓税利息”“附加税”“总税款”“其他进口环节税”“消费税(RMB)”这几个栏位得出汇总值。
            dutyPrice      关税
            taxPrice       增值税
            dutyInterest   关税缓征利息
            taxInterest    增值税缓征利息
            addTaxPrice    附加税
            otherTax       其他进口环节税
            exciseTax      消费税
            exciseInterest 消费税缓息
         */
            BigDecimal dutyPrice=decICustomsTrack.getDutyPrice()==null?BigDecimal.ZERO:decICustomsTrack.getDutyPrice();
            BigDecimal taxPrice=decICustomsTrack.getTaxPrice()==null?BigDecimal.ZERO:decICustomsTrack.getTaxPrice();
            BigDecimal dutyInterest=decICustomsTrack.getDutyInterest()==null?BigDecimal.ZERO:decICustomsTrack.getDutyInterest();
            BigDecimal taxInterest=decICustomsTrack.getTaxInterest()==null?BigDecimal.ZERO:decICustomsTrack.getTaxInterest();
            BigDecimal addTaxPrice=decICustomsTrack.getAddTaxPrice()==null?BigDecimal.ZERO:decICustomsTrack.getAddTaxPrice();
            BigDecimal otherTax=decICustomsTrack.getOtherTax()==null?BigDecimal.ZERO:decICustomsTrack.getOtherTax();
            BigDecimal exciseTax=decICustomsTrack.getExciseTax()==null?BigDecimal.ZERO:decICustomsTrack.getExciseTax();
            BigDecimal exciseInterest = decICustomsTrack.getExciseInterest() == null ?
                    BigDecimal.ZERO: decICustomsTrack.getExciseInterest();
            BigDecimal antiDumpingDuty = decICustomsTrack.getAntiDumpingDuty() == null ?BigDecimal.ZERO: decICustomsTrack.getAntiDumpingDuty();

            decICustomsTrack.setTaxTotal(dutyPrice.add(taxPrice).add(dutyInterest).add(taxInterest).add(addTaxPrice).add(otherTax).add(exciseTax).add(exciseInterest).add(antiDumpingDuty));


            DecIBillHead decIBillHead = decIBillHeadMapper.selectByPrimaryKey(decICustomsTrack.getHeadId());
            if (decIBillHead != null) {
                decIBillHead.setEntryNo(decICustomsTrack.getEntryNo());
                decIBillHead.setEntryDeclareDate(decICustomsTrack.getEntryDeclareDate());
                decIBillHead.setSeqNo(decICustomsTrack.getSeqNo());
                decIBillHeadMapper.updateByPrimaryKey(decIBillHead);
                String ddate=null;
                if (decICustomsTrack.getEntryDeclareDate() != null) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    ddate=simpleDateFormat.format(decICustomsTrack.getEntryDeclareDate());
                }
                decICustomsTrackMapper.updateEntryHead(decICustomsTrack.getEntryNo(),ddate,decIBillHead.getEmsListNo(),userInfo.getCompany(),decICustomsTrack.getEntryStatus(),decICustomsTrack.getSeqNo());
            }
            //}
            // 更新数据
            int update = decICustomsTrackMapper.updateByPrimaryKey(decICustomsTrack);
            auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(),"进口管理-进口报关追踪",userInfo,decICustomsTrack);

            // 更新报关单回传信息表
            // 根据提单表头id获取所有报关单号,用逗号隔开
            DecEntryReturnParam decEntryReturnParam = new DecEntryReturnParam();
            decEntryReturnParam.setHeadId(decICustomsTrack.getErpHeadId());
            decEntryReturnParam.setPassDate(decICustomsTrack.getPassDate());

            boolean isSync = false;
            List<WarringPassConfigDto> configs = warringPassConfigService.selectAll(userInfo);
            if (CollectionUtils.isNotEmpty(configs)) {
                WarringPassConfigDto passConfig = configs.get(0);
                if (CommonVariable.CONFIG_1.equals(passConfig.getSyncInterLogistics())) {
                    isSync = true;
                }
            }
            if(isSync) {
                DecICustomsTrack entryTrack = decICustomsTrackMapper.selectEntryNosByErpHeadId(decICustomsTrack.getErpHeadId());
                //当报关单号、申报日期、放行日期都没有查到时忽略更新
                if(entryTrack != null) {
                    decEntryReturnParam.setEntryNo(entryTrack.getEntryNo());
                    decEntryReturnParam.setPassDate(entryTrack.getPassDate());
                    decEntryReturnParam.setEntryDeclareDate(entryTrack.getEntryDeclareDate());
                    decEntryReturnService.updateByHeadId(decEntryReturnParam,userInfo);
                }
            }
            return update > 0 ? decICustomsTrackDtoMapper.toDto(decICustomsTrack) : null;
        }
        else {
            return null;
        }
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        Example exampleList = new Example(DecICustomsTrack.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andIn("sid", sids);
        List<DecICustomsTrack> decICustomsTrackList = decICustomsTrackMapper.selectByExample(exampleList);
        decICustomsTrackMapper.deleteBySids(sids);
        for(DecICustomsTrack decICustomsTrack:decICustomsTrackList) {
            auditLogUtil.AuditLog(CommonEnum.operationsEnum.DELETE.getValue(),"进口管理-进口报关追踪",userInfo,decICustomsTrack);
        }
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecICustomsTrackDto> selectAll(DecICustomsTrackParam exportParam, UserInfoToken userInfo) {
        DecICustomsTrack decICustomsTrack = decICustomsTrackDtoMapper.toPo(exportParam);
        decICustomsTrack.setTradeCode(userInfo.getCompany());
        decICustomsTrack.setInsertUser(decErpIHeadNService.getAuthority(userInfo));
        // 代理用户参数设置
        agentService.agentHandler(decICustomsTrack, userInfo);
        List<DecICustomsTrackDto> decICustomsTrackDtos = new ArrayList<>();
        List<DecICustomsTrack> decICustomsTracks = decICustomsTrackMapper.getList(decICustomsTrack);
        if (CollectionUtils.isNotEmpty(decICustomsTracks)) {
            decICustomsTrackDtos = decICustomsTracks.stream().map(head -> {
                DecICustomsTrackDto dto = decICustomsTrackDtoMapper.toDto(head);
                dto.setInsertWeek(WeekUnit.getWeek(dto.getInsertTime()));
                return dto;
            }).collect(Collectors.toList());
        }
        return decICustomsTrackDtos;
    }

    /**
     * 功能描述:根据sid查询数据
     *
     * <AUTHOR> 朱正东
     * @version :   1.0
     * @date： 2018-11-20
     * @return:
     */
    public DecICustomsTrack selectBySId(String sid, UserInfoToken userInfo) {
        DecICustomsTrack decICustomsTrack =new DecICustomsTrack();
        decICustomsTrack.setSid(sid);
        decICustomsTrack.setTradeCode(userInfo.getCompany());
        // 代理用户参数设置
        agentService.agentHandler(decICustomsTrack, userInfo);
        List<DecICustomsTrack> decICustomsTracks = decICustomsTrackMapper.getList(decICustomsTrack);
        if (CollectionUtils.isNotEmpty(decICustomsTracks)) {
            return  decICustomsTracks.get(0);
        }
        return null;
    }


    /**
     * 功能描述:批量更新出口报关追踪
     *
     * <AUTHOR> 朱正东
     * @version :   1.0
     * @date： 2018-11-20
     * @param: model 实体
     * @param: optUserNo 当前用户
     * @param: tradeCode 当前企业code
     * @return:
     */
    public ResultObject updateM(DecICustomsTrackMParam model, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        List<String> sidList = model.getSidList();
        for (String s : sidList) {
            DecICustomsTrackParam decICustomsTrackParam = model.getParamData();
            decICustomsTrackParam.setSid(s);
            Set<ConstraintViolation<DecICustomsTrackParam>> constraintViolations = validator.validate(decICustomsTrackParam);
            if (constraintViolations != null && constraintViolations.size() > 0) {
                for (ConstraintViolation<DecICustomsTrackParam> cv : constraintViolations) {
                    result.setMessage(cv.getMessage());
                }
                result.setSuccess(false);
            }
            else {
                update(decICustomsTrackParam, userInfo);
            }
        }
        return result;
    }

    /**
     * 功能描述：获取到货通知人
     *
     * @param userInfo
     */
    public ResultObject getAlertOthers(UserInfoToken userInfo) {
        Map<String, Object> map = new HashMap<>(1);
        map.put("tradeCode", userInfo.getCompany());
        return ResultObject.createInstance(true, "", decICustomsTrackMapper.getAlertOthers(map));
    }

    public List<DecICustomsTrackNoticeDto> ArrivalNotice(List<String> sids,UserInfoToken userInfo) {

        //更新到货通知日期
        Example example = new Example(DecICustomsTrack.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sid", sids);
        DecICustomsTrack setModel = new DecICustomsTrack();
        setModel.setNoticeDate(new Date());
        decICustomsTrackMapper.updateByExampleSelective(setModel, example);

        List<DecICustomsTrackNoticeDto> decICustomsTrackNoticeDtos = new ArrayList<>();
        List<DecICustomsTrackNotice> decICustomsTrackNotices = decICustomsTrackMapper.ArrivalNotice(sids,userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(decICustomsTrackNotices)) {
            decICustomsTrackNoticeDtos = decICustomsTrackNotices.stream().map(head -> {
                DecICustomsTrackNoticeDto dto = decICustomsTrackDtoMapper.toNoticeDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decICustomsTrackNoticeDtos;
    }

    /**
     * 报关追踪完成进度
     * @param sids
     * @param status
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject reach(List<String> sids, String status) {
        ResultObject result = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("修改完成"));
        List<DecICustomsTrack> decICustomsTracks = decICustomsTrackMapper.getReach(sids);
        if (decICustomsTracks == null) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("没有查询到数据"));
        }
        decICustomsTracks.stream().forEach(s -> {
            if (status.equals(s.getComplete())) {
                switch (status) {
                    case DecVariable.STATUS_1:
                        throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("勾选数据中已存在“进行中”"));
                    case DecVariable.STATUS_2:
                        throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("勾选数据中已存在“延迟”"));
                    default:
                        throw new ErrorException(400,  xdoi18n.XdoI18nUtil.t("勾选数据中已存在“完成”"));
                }
            }
            s.setComplete(status);
        });
        for (DecICustomsTrack decICustomsTrack : decICustomsTracks) {
            decICustomsTrackMapper.updateComplete(decICustomsTrack);
        }
        return result;
    }

    /**
     * 采购确认/取消
     * @param sids
     * @param type 3:采购 4:财务
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<DecICustomsTrackDto> purchaseCheck(List<String> sids,String type) {
        ResultObject result = ResultObject.createInstance(true);
        //查询所有数据
        List<DecICustomsTrack> list = decICustomsTrackMapper.getReach(sids);

        List<DecICustomsTrack> checkResult = new ArrayList<>();
        for (DecICustomsTrack decICustomsTrack : list) {
            //判断是否是采购信息
            if (type.equals(DecVariable.STATUS_3)) {
                //判断报关单状态是否曾在
                if (StringUtils.isNotBlank(decICustomsTrack.getGoodsStatus())) {
                    if (decICustomsTrack.getGoodsStatus().equals(DecVariable.GOOD_STATUS_6) || decICustomsTrack.getGoodsStatus().equals(DecVariable.GOOD_STATUS_7) || decICustomsTrack.getGoodsStatus().equals(DecVariable.GOOD_STATUS_8)) {
                    } else {
                        checkResult.add(new DecICustomsTrack() {{
                            setWarningMark(DecVariable.STATUS_1);
                            setEmsListNo(decICustomsTrack.getEmsListNo());
                            setWarningMsg(xdoi18n.XdoI18nUtil.t("报关未完成"));
                        }});
                    }
                }else{
                    checkResult.add(new DecICustomsTrack() {{
                        setWarningMark(DecVariable.STATUS_1);
                        setEmsListNo(decICustomsTrack.getEmsListNo());
                        setWarningMsg(xdoi18n.XdoI18nUtil.t("报关未完成"));
                    }});
                }
                //判断是否是财务数据
            } else if (type.equals(DecVariable.STATUS_4)) {
                if (DecVariable.PURCHASE_CONFIRM_0.equals(decICustomsTrack.getPurchaseConfirm())) {
                    checkResult.add(new DecICustomsTrack(){{
                        setWarningMark(DecVariable.STATUS_1);
                        setEmsListNo(decICustomsTrack.getEmsListNo());
                        setWarningMsg(xdoi18n.XdoI18nUtil.t("采购未确认是否进行此操作|"));
                    }});
                }else {
                    if (DecVariable.FINANCE_CONFIRM_1.equals(decICustomsTrack.getFinanceConfirm())) {
                        checkResult.add(new DecICustomsTrack() {{
                            setWarningMark(DecVariable.STATUS_1);
                            setEmsListNo(decICustomsTrack.getEmsListNo());
                            setWarningMsg(xdoi18n.XdoI18nUtil.t("数据已存在是否继续|"));
                        }});
                    }
                }
            }
        }
        if (checkResult.size()>0){
            result.setData(checkResult);
        }
        if (CollectionUtils.isNotEmpty(checkResult)) {
            //限制性校验
            long waCount = checkResult.stream()
                    .filter(x -> x.getWarningMark().equals(DecVariable.STATUS_1)).count();
            if (waCount > 0) {
                result.setSuccess(false);
                result.setErrorField(DecVariable.STATUS_1);
            }
        }
        return result;
    }

    /**
     * 更新
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject purchase(List<String> sids, DecICustomsTrackParam decICustomsTrackParam) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("更新成功"));

        //查询所有数据
        List<DecICustomsTrack> list = decICustomsTrackMapper.getReach(sids);
        if (decICustomsTrackParam.getType().equals(DecVariable.STATUS_3)) {
            if (list.size() > 0) {
                for (DecICustomsTrack decICustomsTrack : list) {
                    if (StringUtils.isNotBlank(decICustomsTrack.getFinanceNo())) {
                        throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("财务已维护单据号不得再进行此操作"));
                    }
                }
            }
            decICustomsTrackMapper.updateStatus(sids, decICustomsTrackParam.getPurchaseConfirm());
        } else {
            for (DecICustomsTrack decICustomsTrack : list) {
                decICustomsTrack.setFinanceNo(decICustomsTrackParam.getFinanceNo());
                decICustomsTrack.setFinanceConfirm(decICustomsTrackParam.getFinanceConfirm());
                decICustomsTrackMapper.updateByPrimaryKey(decICustomsTrack);
            }
//            decICustomsTrackParam.setFinanceConfirm("1");
//            decICustomsTrackMapper.updateFinance(sids, decICustomsTrackParam.getFinanceNo(), decICustomsTrackParam.getFinanceConfirm());
        }
        return result;
    }

    /**
     * 到货通知发送邮件
     * @param sids
     * @param userInfo
     */
    public void sendEmail(List<String> sids,UserInfoToken userInfo) throws Exception {
        //判断邮箱是否为空
        List<String> emails = warringEmailConfigMapper.selectEmails(userInfo.getCompany());
        if (CollectionUtils.isEmpty(emails)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("发送邮箱不能为空"));
        }

        //获取文件路径
        String path = filePath(sids,userInfo);

        String email =String.join(";",emails);
        if (email.length()>100){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("当前邮箱过多,无法发送!"));
        }

        GwMailSendTaskHead gwMailSendTaskHead = new GwMailSendTaskHead();
        gwMailSendTaskHead.setSid(UUID.randomUUID().toString());
        gwMailSendTaskHead.setState("0");
        gwMailSendTaskHead.setSender("GW");
        gwMailSendTaskHead.setInsertUser(userInfo.getUserNo());
        gwMailSendTaskHead.setInsertTime(new Date());
        gwMailSendTaskHead.setInsertUserName(userInfo.getUserName());
        gwMailSendTaskHead.setIsCustomized("0");
        gwMailSendTaskHead.setTradeCode(userInfo.getCompany());
        gwMailSendTaskHead.setDatyType("THIF");
        gwMailSendTaskHead.setBussinessType("RISK_WARRING");
        gwMailSendTaskHead.setSubject(xdoi18n.XdoI18nUtil.t("到货通知"));
        gwMailSendTaskHead.setBody(xdoi18n.XdoI18nUtil.t("到货通知信息，内容见附件!"));
        gwMailSendTaskHead.setIsHtml("0");
        gwMailSendTaskHead.setRecipient(email);
        gwMailSendTaskHeadMapper.insert(gwMailSendTaskHead);

        GwMailSendTaskAttach gwMailSendTaskAttach = new GwMailSendTaskAttach();
        gwMailSendTaskAttach.setSid(UUID.randomUUID().toString());
        gwMailSendTaskAttach.setHeadId(gwMailSendTaskHead.getSid());
        gwMailSendTaskAttach.setUrl(path);
        gwMailSendTaskAttach.setTradeCode(userInfo.getCompany());
        gwMailSendTaskAttach.setInsertUser(userInfo.getUserNo());
        gwMailSendTaskAttach.setInsertTime(new Date());
        gwMailSendTaskAttach.setFileName("THIF.xlsx");
        gwMailSendTaskAttachMapper.insert(gwMailSendTaskAttach);

    }

    /**
     * 获取文件路径
     * @param sids
     * @param userInfo
     * @return
     */
    private String filePath(List<String> sids,UserInfoToken userInfo) throws Exception {
        String templateName = "arrival_notice.xlsx";
        String fileName = java.util.UUID.randomUUID().toString() + ".xlsx";
        List<DecICustomsTrackNoticeDto> decICustomsTrackDtos =ArrivalNotice(sids, userInfo);
        decICustomsTrackDtos.stream().forEach(head->{
            head.setTradeMode(commonService.convertPCode(head.getTradeMode(), PCodeType.TRADE));
            head.setTrafMode(commonService.convertPCode(head.getTrafMode(),PCodeType.TRANSF));
            head.setWrapType(commonService.convertPCode(head.getWrapType(),PCodeType.WRAP));
            if(StringUtils.isNotBlank(head.getBondMark())) {
                head.setBondMark(CommonEnum.BondMarkEnum.getValue(head.getBondMark()));
            }
        });
        String exportFileName = "";
        if (decICustomsTrackDtos.size() > 0) {
            byte[] bytes = FileUtils.readFileToByteArray(new File(exportService.export( decICustomsTrackDtos, fileName, templateName)));
            exportFileName = fileHandler.uploadFile(bytes, "xlsx");
        }
        return exportFileName;
    }
}
