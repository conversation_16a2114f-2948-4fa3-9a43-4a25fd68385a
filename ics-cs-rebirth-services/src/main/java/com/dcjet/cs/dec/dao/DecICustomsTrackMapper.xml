<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.DecICustomsTrackMapper">
    <sql id="Base_Column_List">
     SID
     ,ENTRUST_DATE
     ,ENTRUST_PERSON
     ,BE_ENTRUST_PERSON
     ,DECLARE_DATE
     ,DECLARE_PORT
     ,DECLARE_PERSON
     ,CHECK_DATE
     ,CHECK_REASON
     ,COMPLETE_CHECK_DATE
     ,CHECK_RESULT
     ,ENTOURAGE
     ,CHECK_FILE
     ,DUTY_DATE
     ,DUTY_TYPE
     ,DUTY_TOTAL
     ,DUTY_PRICE
     ,TAX_PRICE
     ,OTHER_PRICE
     ,PASS_DATE
     ,DELIVERY_DATE
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,TRADE_CODE
     ,HEAD_ID
     ,EMS_LIST_NO
     ,HEAD_INSERT_TIME
     ,CUSTOMS_CHECK_DATE
     ,CUSTOMS_CHECK_REASON
     ,CUSTOMS_COMPLETE_CHECK_DATE
     ,CUSTOMS_CHECK_RESULT
     ,CUSTOMS_ENTOURAGE
     ,CUSTOMS_CHECK_FILE
     ,ENTRY_NO
     ,CHECK_PERSON
     ,CUSTOMS_CHECK_PERSON
     ,STATUS
     ,ERP_HEAD_ID
     ,NOTE_1
     ,LINKED_NO
     ,NOTE_3
     ,GOODS_STATUS
     ,GOODS_STATUS_NAME
     ,PASS_FLAG
     ,DELIVERY_FLAG
     ,CHECK_FLAG
     ,CUSTOMS_CHECK_FLAG
     ,DUTY_FLAG
     ,PASS_FLAG_NAME
     ,DELIVERY_FLAG_NAME
     ,CHECK_FLAG_NAME
     ,CUSTOMS_CHECK_FLAG_NAME
     ,DUTY_FLAG_NAME
     ,TRAF_MODE
     ,TRADE_MODE
     ,WRAP_TYPE

     ,FINANCE_NO
     ,ARRIVAL_DATE
     ,PLATE_NUM
     ,DELIVERY_PERSON
     ,LINK_MAN_TEL
     ,IN_OUT_NO
     ,NOTICE_DATE
     ,PLAN_ARRIVAL_DATE
     ,DAMAGE_MARK
     ,DAMAGE_REMARK
     ,PAY_DATE
     ,LOGISTICS_FEE
     ,CUSTOMS_FEE
     ,DELIVERY_NO
     ,VOYAGE_DATE
     ,BILL_DECLARE_DATE
     ,ENTRY_DECLARE_DATE
     ,DECLARE_CODE_CUSTOMS
     ,DECLARE_CODE
     ,INSPECTION_NO
     ,ARRIVAL_PORT_DATE
     ,EMS_NO
     ,DUTY_INTEREST
     ,TAX_INTEREST
     ,PAYMENT_DATE
	 ,ADD_TAX_PRICE
	 ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
     ,BOND_MARK
     ,CONTR_NO
	 ,TAX_TOTAL
	 ,OTHER_TAX
	 ,'' as insert_week
	 ,NOTE_PASS
	 ,LIST_NO
	 ,HAWB
	 ,EXCISE_TAX
	 ,COMPLETE
	 ,EXCISE_INTEREST
	 ,PURCHASE_CONFIRM
	 ,FINANCE_CONFIRM
     ,ENTRY_STATUS
	 ,INVOICE_NO
	 ,ANTI_DUMPING_DUTY
	 ,SHIPPING_INSPECTION_DATE
     ,SHIPPING_INSPECTION_FINISHDATE
     ,SHIPPING_CHECK_CAUSE
     ,SHIPPING_CHECK_RESULT
     ,SHIPPING_CHECK_ENTOURAGE
     ,SHIPPING_CHECK_DOCUMENT
     ,COMPLETE_CHECK_DOCUMENT
     ,CUSTOMS_CHECK_DOCUMENT
     ,SEQ_NO
    </sql>
    <sql id="condition">
        <if test="sid != null and sid != ''">
            and SID = #{sid}
        </if>
        <if test="inspectionNo != null and inspectionNo != ''">
            and INSPECTION_NO like concat(concat('%',#{inspectionNo}),'%')
        </if>
        <if test="entrustDateFrom != null and entrustDateFrom != ''">
            <![CDATA[ and ENTRUST_DATE >= to_date(#{entrustDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="entrustDateTo != null and entrustDateTo != ''">
            <![CDATA[ and ENTRUST_DATE < to_date(#{entrustDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>

        <if test="addTaxPrice != null and addTaxPrice != ''">
            and ADD_TAX_PRICE = #{addTaxPrice}
        </if>
        <if test="paymentDateFrom != null and paymentDateFrom != ''">
            <![CDATA[ and PAYMENT_DATE >= to_date(#{paymentDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="paymentDateTo != null and paymentDateTo != ''">
            <![CDATA[ and PAYMENT_DATE < to_date(#{paymentDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>

        <if test="entrustPerson != null and entrustPerson != ''">
            and ENTRUST_PERSON = #{entrustPerson}
        </if>
        <if test="beEntrustPerson != null and beEntrustPerson != ''">
            and BE_ENTRUST_PERSON = #{beEntrustPerson}
        </if>
        <if test="declareDateFrom != null and declareDateFrom != ''">
            <![CDATA[ and ENTRY_DECLARE_DATE >= to_date(#{declareDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="declareDateTo != null and declareDateTo != ''">
            <![CDATA[ and ENTRY_DECLARE_DATE < to_date(#{declareDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
        <if test="declarePort != null and declarePort != ''">
            and DECLARE_PORT = #{declarePort}
        </if>
        <if test="declarePerson != null and declarePerson != ''">
            and DECLARE_PERSON = #{declarePerson}
        </if>
        <if test="checkDateFrom != null and checkDateFrom != ''">
            <![CDATA[ and CHECK_DATE >= to_date(#{checkDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="checkDateTo != null and checkDateTo != ''">
            <![CDATA[ and CHECK_DATE < to_date(#{checkDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
        <if test="checkReason != null and checkReason != ''">
            and CHECK_REASON = #{checkReason}
        </if>
        <if test="completeCheckDateFrom != null and completeCheckDateFrom != ''">
            <![CDATA[ and COMPLETE_CHECK_DATE >= to_date(#{completeCheckDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="completeCheckDateTo != null and completeCheckDateTo != ''">
            <![CDATA[ and COMPLETE_CHECK_DATE < to_date(#{completeCheckDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
        <if test="checkResult != null and checkResult != ''">
            and CHECK_RESULT = #{checkResult}
        </if>
        <if test="entourage != null and entourage != ''">
            and ENTOURAGE = #{entourage}
        </if>
        <if test="checkFile != null and checkFile != ''">
            and CHECK_FILE = #{checkFile}
        </if>
        <if test="dutyDateFrom != null and dutyDateFrom != ''">
            <![CDATA[ and DUTY_DATE >= to_date(#{dutyDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="dutyDateTo != null and dutyDateTo != ''">
            <![CDATA[ and DUTY_DATE < to_date(#{dutyDateTo},'yyyy-MM-dd hh24:mi:ss')+1 ]]>
        </if>
        <if test="dutyType != null and dutyType != ''">
            and DUTY_TYPE = #{dutyType}
        </if>
        <if test="dutyTotal != null and dutyTotal != ''">
            and DUTY_TOTAL = #{dutyTotal}
        </if>
        <if test="dutyPrice != null and dutyPrice != ''">
            and DUTY_PRICE = #{dutyPrice}
        </if>
        <if test="taxPrice != null and taxPrice != ''">
            and TAX_PRICE = #{taxPrice}
        </if>
        <if test="otherPrice != null and otherPrice != ''">
            and OTHER_PRICE = #{otherPrice}
        </if>
        <if test="passDateFrom != null and passDateFrom != ''">
            <![CDATA[ and PASS_DATE >= to_date(#{passDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="passDateTo != null and passDateTo != ''">
            <![CDATA[ and PASS_DATE < to_date(#{passDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
        <if test="deliveryDateFrom != null and deliveryDateFrom != ''">
            <![CDATA[ and DELIVERY_DATE >= to_date(#{deliveryDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="deliveryDateTo != null and deliveryDateTo != ''">
            <![CDATA[ and DELIVERY_DATE < to_date(#{deliveryDateTo},'yyyy-MM-dd hh24:mi:ss')+1 ]]>
        </if>
        and TRADE_CODE = #{tradeCode}
        <if test="headId != null and headId != ''">
            and HEAD_ID = #{headId}
        </if>
        <if test="emsListNoTo != null and emsListNoTo != ''">
            and EMS_LIST_NO like concat(concat('%',#{emsListNoTo,jdbcType=VARCHAR}),'%')
        </if>
        <if test="headInsertTimeFrom != null and headInsertTimeFrom != ''">
            <![CDATA[ and HEAD_INSERT_TIME >= to_date(#{headInsertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="headInsertTimeTo != null and headInsertTimeTo != ''">
            <![CDATA[ and HEAD_INSERT_TIME < to_date(#{headInsertTimeTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
        <if test="customsCheckDateFrom != null and customsCheckDateFrom != ''">
            <![CDATA[ and CUSTOMS_CHECK_DATE >= to_date(#{customsCheckDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="customsCheckDateTo != null and customsCheckDateTo != ''">
            <![CDATA[ and CUSTOMS_CHECK_DATE < to_date(#{customsCheckDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
        <if test="customsCheckReason != null and customsCheckReason != ''">
            and CUSTOMS_CHECK_REASON = #{customsCheckReason}
        </if>
        <if test="customsCompleteCheckDateFrom != null and customsCompleteCheckDateFrom != ''">
            <![CDATA[ and CUSTOMS_COMPLETE_CHECK_DATE >= to_date(#{customsCompleteCheckDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="customsCompleteCheckDateTo != null and customsCompleteCheckDateTo != ''">
            <![CDATA[ and CUSTOMS_COMPLETE_CHECK_DATE <to_date(#{customsCompleteCheckDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
        <if test="customsCheckResult != null and customsCheckResult != ''">
            and CUSTOMS_CHECK_RESULT = #{customsCheckResult}
        </if>
        <if test="customsEntourage != null and customsEntourage != ''">
            and CUSTOMS_ENTOURAGE = #{customsEntourage}
        </if>
        <if test="customsCheckFile != null and customsCheckFile != ''">
            and CUSTOMS_CHECK_FILE = #{customsCheckFile}
        </if>

        <choose>
            <when test='entryNo=="N"'>
                and ENTRY_NO IS NUll
            </when>
            <when test="entryNo != null and entryNo != ''">
                and t.ENTRY_NO like concat(concat('%',#{entryNo}),'%')
            </when>
        </choose>
        <!--<if test="entryNo != null and entryNo != ''">
            and ENTRY_NO like concat(concat('%',#{entryNo,jdbcType=VARCHAR}),'%')
        </if>-->

        <if test="checkPerson != null and checkPerson != ''">
            and CHECK_PERSON = #{checkPerson}
        </if>
        <if test="customsCheckPerson != null and customsCheckPerson != ''">
            and CUSTOMS_CHECK_PERSON = #{customsCheckPerson}
        </if>
        <if test="status != null and status != ''">
            and STATUS = #{status}
        </if>
        <if test="erpHeadId != null and erpHeadId != ''">
            and ERP_HEAD_ID = #{erpHeadId}
        </if>
        <if test="note1 != null and note1 != ''">
            and NOTE_1 = #{note1}
        </if>
        <if test="linkedNo != null and linkedNo != ''">
            and LINKED_NO = #{linkedNo}
        </if>
        <if test="note3 != null and note3 != ''">
            and NOTE_3 = #{note3}
        </if>
        <if test="tradeMode != null and tradeMode != ''">
            and TRADE_MODE = #{tradeMode}
        </if>
        <if test="trafMode != null and trafMode != ''">
            and TRAF_MODE = #{trafMode}
        </if>
        <if test="dutyFlag != null and dutyFlag != ''">
            and DUTY_FLAG = #{dutyFlag}
        </if>
        <if test="checkFlag != null and checkFlag!=''">
            <choose>
                <when test="checkFlag=='1'.toString()">
                    and CHECK_FLAG = '0' and CUSTOMS_CHECK_FLAG = '0'
                </when>
                <when test="checkFlag=='2'.toString()">
                    and CUSTOMS_CHECK_FLAG = '1'
                </when>
                <when test="checkFlag=='3'.toString()">
                    and CHECK_FLAG ='1'
                </when>
                <otherwise>
                    and 1=1
                </otherwise>
            </choose>
        </if>
        <if test="deliveryFlag != null and deliveryFlag != ''">
            and DELIVERY_FLAG = #{deliveryFlag}
        </if>
        <if test="passFlag != null and passFlag != ''">
            and PASS_FLAG = #{passFlag}
        </if>
        <if test="goodsStatus != null and goodsStatus != ''">
            and GOODS_STATUS in
            <foreach item="item" index="index" collection="goodsStatus.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--<if test="goodsStatus != null and goodsStatus != ''">
            and GOODS_STATUS = #{goodsStatus}
        </if>-->

        <if test="financeNo != null and financeNo != ''">
            and FINANCE_NO like concat(concat('%',#{financeNo}),'%')
        </if>
        <if test="arrivalDateFrom != null and arrivalDateFrom != ''">
            <![CDATA[ and ARRIVAL_DATE >= to_date(#{arrivalDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="arrivalDateTo != null and arrivalDateTo != ''">
            <![CDATA[ and ARRIVAL_DATE <= to_date(#{arrivalDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="plateNum != null and plateNum != ''">
            and PLATE_NUM = #{plateNum}
        </if>
        <if test="deliveryPerson != null and deliveryPerson != ''">
            and DELIVERY_PERSON = #{deliveryPerson}
        </if>
        <if test="linkManTel != null and linkManTel != ''">
            and LINK_MAN_TEL = #{linkManTel}
        </if>
        <if test="inOutNo != null and inOutNo != ''">
            and IN_OUT_NO = #{inOutNo}
        </if>
        <if test="noticeDateFrom != null and noticeDateFrom != ''">
            <![CDATA[ and NOTICE_DATE >= to_date(#{noticeDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="noticeDateTo != null and noticeDateTo != ''">
            <![CDATA[ and NOTICE_DATE <= to_date(#{noticeDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="planArrivalDateFrom != null and planArrivalDateFrom != ''">
            <![CDATA[ and PLAN_ARRIVAL_DATE >= to_date(#{planArrivalDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="planArrivalDateTo != null and planArrivalDateTo != ''">
            <![CDATA[ and PLAN_ARRIVAL_DATE <= to_date(#{planArrivalDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="damageMark != null and damageMark != ''">
            and DAMAGE_MARK = #{damageMark}
        </if>
        <if test="damageRemark != null and damageRemark != ''">
            and DAMAGE_REMARK = #{damageRemark}
        </if>
        <if test="payDateFrom != null and payDateFrom != ''">
            <![CDATA[ and PAY_DATE >= to_date(#{payDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="payDateTo != null and payDateTo != ''">
            <![CDATA[ and PAY_DATE <= to_date(#{payDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="logisticsFee != null and logisticsFee != ''">
            and LOGISTICS_FEE = #{logisticsFee}
        </if>
        <if test="customsFee != null and customsFee != ''">
            and CUSTOMS_FEE = #{customsFee}
        </if>
        <if test="deliveryNo != null and deliveryNo != ''">
            and DELIVERY_NO = #{deliveryNo}
        </if>

        <if test="declareCodeCustoms != null and declareCodeCustoms != ''">
            and DECLARE_CODE_CUSTOMS like concat(concat('%',#{declareCodeCustoms,jdbcType=VARCHAR}),'%')
        </if>
        <if test="declareCode != null and declareCode != ''">
            and DECLARE_CODE = #{declareCode,jdbcType=VARCHAR}
        </if>
        <if test="emsNo != null and emsNo != ''">
            and EMS_NO =#{emsNo,jdbcType=VARCHAR}
        </if>
        <if test="contrNo != null and contrNo != ''">
            and CONTR_NO like concat(concat('%',#{contrNo}),'%')
        </if>
        <if test="hawb != null and hawb != ''">
            and HAWB like concat(concat('%',#{hawb}),'%')
        </if>
        <if test="complete != null and complete != ''">
            and COMPLETE = #{complete,jdbcType=VARCHAR}
        </if>
        <if test="purchaseConfirm != null and purchaseConfirm != ''">
            and PURCHASE_CONFIRM = #{purchaseConfirm,jdbcType=VARCHAR}
        </if>
        <if test="financeConfirm != null and financeConfirm != ''">
            and FINANCE_CONFIRM = #{financeConfirm,jdbcType=VARCHAR}
        </if>
        <if test="entryStatus != null and entryStatus != ''">
            and ENTRY_STATUS =#{entryStatus}
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
            and INVOICE_NO like concat(concat('%',#{invoiceNo}),'%')
        </if>
        <if test="insertUser != null and insertUser!=''">
            and INSERT_USER in
            <foreach item="item" index="index" collection="insertUser.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <sql id="conditionPG">
        <if test="sid != null and sid != ''">
            and SID = #{sid}
        </if>
        <if test="inspectionNo != null and inspectionNo != ''">
            and INSPECTION_NO like concat(concat('%',#{inspectionNo}),'%')
        </if>
        <if test="entrustDateFrom != null and entrustDateFrom != ''">
            <![CDATA[ and ENTRUST_DATE >= to_timestamp(#{entrustDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="entrustDateTo != null and entrustDateTo != ''">
            <![CDATA[ and ENTRUST_DATE < to_timestamp(#{entrustDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>

        <if test="addTaxPrice != null and addTaxPrice != ''">
            and ADD_TAX_PRICE = #{addTaxPrice}
        </if>
        <if test="paymentDateFrom != null and paymentDateFrom != ''">
            <![CDATA[ and PAYMENT_DATE >= to_timestamp(#{paymentDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="paymentDateTo != null and paymentDateTo != ''">
            <![CDATA[ and PAYMENT_DATE < to_timestamp(#{paymentDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>

        <if test="entrustPerson != null and entrustPerson != ''">
            and ENTRUST_PERSON = #{entrustPerson}
        </if>
        <if test="beEntrustPerson != null and beEntrustPerson != ''">
            and BE_ENTRUST_PERSON = #{beEntrustPerson}
        </if>
        <if test="declareDateFrom != null and declareDateFrom != ''">
            <![CDATA[ and ENTRY_DECLARE_DATE >= to_timestamp(#{declareDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="declareDateTo != null and declareDateTo != ''">
            <![CDATA[ and ENTRY_DECLARE_DATE < to_timestamp(#{declareDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="declarePort != null and declarePort != ''">
            and DECLARE_PORT = #{declarePort}
        </if>
        <if test="declarePerson != null and declarePerson != ''">
            and DECLARE_PERSON = #{declarePerson}
        </if>
        <if test="checkDateFrom != null and checkDateFrom != ''">
            <![CDATA[ and CHECK_DATE >= to_timestamp(#{checkDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="checkDateTo != null and checkDateTo != ''">
            <![CDATA[ and CHECK_DATE < to_timestamp(#{checkDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="checkReason != null and checkReason != ''">
            and CHECK_REASON = #{checkReason}
        </if>
        <if test="completeCheckDateFrom != null and completeCheckDateFrom != ''">
            <![CDATA[ and COMPLETE_CHECK_DATE >= to_timestamp(#{completeCheckDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="completeCheckDateTo != null and completeCheckDateTo != ''">
            <![CDATA[ and COMPLETE_CHECK_DATE < to_timestamp(#{completeCheckDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="checkResult != null and checkResult != ''">
            and CHECK_RESULT = #{checkResult}
        </if>
        <if test="entourage != null and entourage != ''">
            and ENTOURAGE = #{entourage}
        </if>
        <if test="checkFile != null and checkFile != ''">
            and CHECK_FILE = #{checkFile}
        </if>
        <if test="dutyDateFrom != null and dutyDateFrom != ''">
            <![CDATA[ and DUTY_DATE >= to_timestamp(#{dutyDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="dutyDateTo != null and dutyDateTo != ''">
            <![CDATA[ and DUTY_DATE < to_timestamp(#{dutyDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="dutyType != null and dutyType != ''">
            and DUTY_TYPE = #{dutyType}
        </if>
        <if test="dutyTotal != null and dutyTotal != ''">
            and DUTY_TOTAL = #{dutyTotal}
        </if>
        <if test="dutyPrice != null and dutyPrice != ''">
            and DUTY_PRICE = #{dutyPrice}
        </if>
        <if test="taxPrice != null and taxPrice != ''">
            and TAX_PRICE = #{taxPrice}
        </if>
        <if test="otherPrice != null and otherPrice != ''">
            and OTHER_PRICE = #{otherPrice}
        </if>
        <if test="passDateFrom != null and passDateFrom != ''">
            <![CDATA[ and PASS_DATE >= to_timestamp(#{passDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="passDateTo != null and passDateTo != ''">
            <![CDATA[ and PASS_DATE < to_timestamp(#{passDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="deliveryDateFrom != null and deliveryDateFrom != ''">
            <![CDATA[ and DELIVERY_DATE >= to_timestamp(#{deliveryDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="deliveryDateTo != null and deliveryDateTo != ''">
            <![CDATA[ and DELIVERY_DATE < to_timestamp(#{deliveryDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        and TRADE_CODE = #{tradeCode}
        <if test="headId != null and headId != ''">
            and HEAD_ID = #{headId}
        </if>
        <if test="emsListNoTo != null and emsListNoTo != ''">
            and EMS_LIST_NO like concat(concat('%',#{emsListNoTo,jdbcType=VARCHAR}),'%')
        </if>
        <if test="headInsertTimeFrom != null and headInsertTimeFrom != ''">
            <![CDATA[ and HEAD_INSERT_TIME >= to_timestamp(#{headInsertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="headInsertTimeTo != null and headInsertTimeTo != ''">
            <![CDATA[ and HEAD_INSERT_TIME < to_timestamp(#{headInsertTimeTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="customsCheckDateFrom != null and customsCheckDateFrom != ''">
            <![CDATA[ and CUSTOMS_CHECK_DATE >= to_timestamp(#{customsCheckDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="customsCheckDateTo != null and customsCheckDateTo != ''">
            <![CDATA[ and CUSTOMS_CHECK_DATE < to_timestamp(#{customsCheckDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="customsCheckReason != null and customsCheckReason != ''">
            and CUSTOMS_CHECK_REASON = #{customsCheckReason}
        </if>
        <if test="customsCompleteCheckDateFrom != null and customsCompleteCheckDateFrom != ''">
            <![CDATA[ and CUSTOMS_COMPLETE_CHECK_DATE >= to_timestamp(#{customsCompleteCheckDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="customsCompleteCheckDateTo != null and customsCompleteCheckDateTo != ''">
            <![CDATA[ and CUSTOMS_COMPLETE_CHECK_DATE < to_timestamp(#{customsCompleteCheckDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="customsCheckResult != null and customsCheckResult != ''">
            and CUSTOMS_CHECK_RESULT = #{customsCheckResult}
        </if>
        <if test="customsEntourage != null and customsEntourage != ''">
            and CUSTOMS_ENTOURAGE = #{customsEntourage}
        </if>
        <if test="customsCheckFile != null and customsCheckFile != ''">
            and CUSTOMS_CHECK_FILE = #{customsCheckFile}
        </if>

        <choose>
            <when test='entryNo=="N"'>
                and ENTRY_NO IS NUll
            </when>
            <when test="entryNo != null and entryNo != ''">
                and t.ENTRY_NO like concat(concat('%',#{entryNo}),'%')
            </when>
        </choose>
        <!--<if test="entryNo != null and entryNo != ''">
            and ENTRY_NO like concat(concat('%',#{entryNo,jdbcType=VARCHAR}),'%')
        </if>-->

        <if test="checkPerson != null and checkPerson != ''">
            and CHECK_PERSON = #{checkPerson}
        </if>
        <if test="customsCheckPerson != null and customsCheckPerson != ''">
            and CUSTOMS_CHECK_PERSON = #{customsCheckPerson}
        </if>
        <if test="status != null and status != ''">
            and STATUS = #{status}
        </if>
        <if test="erpHeadId != null and erpHeadId != ''">
            and ERP_HEAD_ID = #{erpHeadId}
        </if>
        <if test="note1 != null and note1 != ''">
            and NOTE_1 = #{note1}
        </if>
        <if test="linkedNo != null and linkedNo != ''">
            and LINKED_NO = #{linkedNo}
        </if>
        <if test="note3 != null and note3 != ''">
            and NOTE_3 = #{note3}
        </if>
        <if test="tradeMode != null and tradeMode != ''">
            and TRADE_MODE = #{tradeMode}
        </if>
        <if test="trafMode != null and trafMode != ''">
            and TRAF_MODE = #{trafMode}
        </if>
        <if test="dutyFlag != null and dutyFlag != ''">
            and DUTY_FLAG = #{dutyFlag}
        </if>
        <if test="checkFlag != null and checkFlag!=''">
            <choose>
                <when test="checkFlag=='1'.toString()">
                    and CHECK_FLAG = '0' and CUSTOMS_CHECK_FLAG = '0'
                </when>
                <when test="checkFlag=='2'.toString()">
                    and CUSTOMS_CHECK_FLAG = '1'
                </when>
                <when test="checkFlag=='3'.toString()">
                    and CHECK_FLAG ='1'
                </when>
                <otherwise>
                    and 1=1
                </otherwise>
            </choose>
        </if>
        <if test="deliveryFlag != null and deliveryFlag != ''">
            and DELIVERY_FLAG = #{deliveryFlag}
        </if>
        <if test="passFlag != null and passFlag != ''">
            and PASS_FLAG = #{passFlag}
        </if>
        <if test="goodsStatus != null and goodsStatus != ''">
            and GOODS_STATUS in
            <foreach item="item" index="index" collection="goodsStatus.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--<if test="goodsStatus != null and goodsStatus != ''">
            and GOODS_STATUS = #{goodsStatus}
        </if>-->

        <if test="financeNo != null and financeNo != ''">
            and FINANCE_NO like concat(concat('%',#{financeNo}),'%')
        </if>
        <if test="arrivalDateFrom != null and arrivalDateFrom != ''">
            <![CDATA[ and ARRIVAL_DATE >= to_timestamp(#{arrivalDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="arrivalDateTo != null and arrivalDateTo != ''">
            <![CDATA[ and ARRIVAL_DATE <= to_timestamp(#{arrivalDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="plateNum != null and plateNum != ''">
            and PLATE_NUM = #{plateNum}
        </if>
        <if test="deliveryPerson != null and deliveryPerson != ''">
            and DELIVERY_PERSON = #{deliveryPerson}
        </if>
        <if test="linkManTel != null and linkManTel != ''">
            and LINK_MAN_TEL = #{linkManTel}
        </if>
        <if test="inOutNo != null and inOutNo != ''">
            and IN_OUT_NO = #{inOutNo}
        </if>
        <if test="noticeDateFrom != null and noticeDateFrom != ''">
            <![CDATA[ and NOTICE_DATE >= to_timestamp(#{noticeDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="noticeDateTo != null and noticeDateTo != ''">
            <![CDATA[ and NOTICE_DATE <= to_timestamp(#{noticeDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="planArrivalDateFrom != null and planArrivalDateFrom != ''">
            <![CDATA[ and PLAN_ARRIVAL_DATE >= to_timestamp(#{planArrivalDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="planArrivalDateTo != null and planArrivalDateTo != ''">
            <![CDATA[ and PLAN_ARRIVAL_DATE <= to_timestamp(#{planArrivalDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="damageMark != null and damageMark != ''">
            and DAMAGE_MARK = #{damageMark}
        </if>
        <if test="damageRemark != null and damageRemark != ''">
            and DAMAGE_REMARK = #{damageRemark}
        </if>
        <if test="payDateFrom != null and payDateFrom != ''">
            <![CDATA[ and PAY_DATE >= to_timestamp(#{payDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="payDateTo != null and payDateTo != ''">
            <![CDATA[ and PAY_DATE <= to_timestamp(#{payDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="logisticsFee != null and logisticsFee != ''">
            and LOGISTICS_FEE = #{logisticsFee}
        </if>
        <if test="customsFee != null and customsFee != ''">
            and CUSTOMS_FEE = #{customsFee}
        </if>
        <if test="deliveryNo != null and deliveryNo != ''">
            and DELIVERY_NO = #{deliveryNo}
        </if>

        <if test="declareCodeCustoms != null and declareCodeCustoms != ''">
            and DECLARE_CODE_CUSTOMS like concat(concat('%',#{declareCodeCustoms,jdbcType=VARCHAR}),'%')
        </if>
        <if test="declareCode != null and declareCode != ''">
            and DECLARE_CODE = #{declareCode,jdbcType=VARCHAR}
        </if>
        <if test="emsNo != null and emsNo != ''">
            and EMS_NO =#{emsNo,jdbcType=VARCHAR}
        </if>
        <if test="contrNo != null and contrNo != ''">
            and CONTR_NO like concat(concat('%',#{contrNo}),'%')
        </if>
        <if test="hawb != null and hawb != ''">
            and HAWB like concat(concat('%',#{hawb}),'%')
        </if>
        <if test="complete != null and complete != ''">
            and COMPLETE = #{complete,jdbcType=VARCHAR}
        </if>
        <if test="purchaseConfirm != null and purchaseConfirm != ''">
            and PURCHASE_CONFIRM = #{purchaseConfirm,jdbcType=VARCHAR}
        </if>
        <if test="financeConfirm != null and financeConfirm != ''">
            and FINANCE_CONFIRM = #{financeConfirm,jdbcType=VARCHAR}
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
            and INVOICE_NO like concat(concat('%',#{invoiceNo}),'%')
        </if>
        <if test="insertUser != null and insertUser!=''">
            and INSERT_USER in
            <foreach item="item" index="index" collection="insertUser.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultType="com.dcjet.cs.dec.model.DecICustomsTrack" parameterType="com.dcjet.cs.dec.model.DecICustomsTrack">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        V_DEC_I_CUSTOMS_TRACK t
        <where>
            <if test='_databaseId == "postgresql" '>
                <include refid="conditionPG"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="condition"></include>
            </if>
            <choose>
                <when test="agentAuthType == 'Y'.toString()">
                    and (t.DECLARE_CODE = #{agentDeclareCode} or t.FORWARD_CODE = #{agentForwardCode})
                </when>
                <otherwise>
                    <if test="agentDeclareCode != null and agentDeclareCode != ''">
                        and t.DECLARE_CODE = #{agentDeclareCode}
                    </if>
                    <if test="agentForwardCode != null and agentForwardCode != ''">
                        and t.FORWARD_CODE = #{agentForwardCode}
                    </if>
                </otherwise>
            </choose>
        </where>
        order by HEAD_INSERT_TIME desc,sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_DEC_I_CUSTOMS_TRACK t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getAlertOthers" parameterType="map" resultType="java.lang.String">
        SELECT USER_NAME FROM T_WARRING_EMAIL_CONFIG where TRADE_CODE=#{tradeCode,jdbcType=VARCHAR}
    </select>

    <resultMap id="decECustomsTrackNoticeResultMap" type="com.dcjet.cs.dec.model.DecICustomsTrackNotice">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="EMS_LIST_NO" property="emsListNo" jdbcType="VARCHAR"/>
        <result column="PLAN_ARRIVAL_DATE" property="planArrivalDate" jdbcType="VARCHAR"/>
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="HAWB" property="hawb" jdbcType="VARCHAR"/>
        <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR"/>
        <result column="DECLARE_NAME_CUSTOMS" property="declareNameCustoms" jdbcType="VARCHAR"/>
        <result column="BOND_MARK" property="bondMark" jdbcType="VARCHAR"/>
        <result column="G_NAME" property="GName" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="VARCHAR"/>
        <result column="PACK_NUM" property="packNum" jdbcType="VARCHAR"/>
        <result column="NET_WT" property="netWt" jdbcType="VARCHAR"/>
        <result column="GROSS_WT" property="grossWt" jdbcType="VARCHAR"/>
        <result column="TRADE_MODE" jdbcType="VARCHAR" property="tradeMode"/>
        <result column="TRAF_MODE" jdbcType="VARCHAR" property="trafMode"/>
        <result column="WRAP_TYPE" jdbcType="VARCHAR" property="wrapType"/>
        <result column="PLATE_NUM" property="plateNum" jdbcType="VARCHAR"/>
        <result column="DELIVERY_PERSON" property="deliveryPerson" jdbcType="VARCHAR"/>
        <result column="LINK_MAN_TEL" property="linkManTel" jdbcType="VARCHAR"/>
        <result column="DELIVERY_NO" property="deliveryNo" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="ArrivalNotice" parameterType="java.util.List" resultMap="decECustomsTrackNoticeResultMap">
        SELECT
        t.SID,
        n.EMS_LIST_NO,
        t.DELIVERY_NO,
        t.PLAN_ARRIVAL_DATE,
        n.INVOICE_NO,
        n.HAWB,
        n.overseas_shipper_name SUPPLIER_NAME,
        n.DECLARE_NAME DECLARE_NAME_CUSTOMS,
        n.TRAF_MODE,
        n.TRADE_MODE,
        s.BOND_MARK,
        s.G_NAME,
        s.QTY,
        n.PACK_NUM,
        n.WRAP_TYPE,
        s.NET_WT,
        s.GROSS_WT,
        t.PLATE_NUM,
        t.DELIVERY_PERSON,
        t.LINK_MAN_TEL,
        s.FAC_G_NO,
        m.COP_G_MODEL_EN
        FROM
        T_DEC_I_CUSTOMS_TRACK t
        left JOIN T_DEC_ERP_I_HEAD_N n ON t.ERP_HEAD_ID = n.SID
        LEFT JOIN T_DEC_ERP_I_LIST_N s ON s.HEAD_ID = n.sid
        LEFT JOIN (SELECT '0' AS BOND_MARK,FAC_G_NO,TRADE_CODE,COP_G_MODEL_EN,G_MARK,EMS_NO FROM T_MAT_IMGEXG where TRADE_CODE= #{tradeCode}   UNION ALL  SELECT '1' AS BOND_MARK,COP_G_NO,TRADE_CODE,COP_G_MODEL_EN,G_MARK,null FROM T_MAT_NON_BONDED where TRADE_CODE= #{tradeCode} )M
        ON M.FAC_G_NO = S.FAC_G_NO AND M.TRADE_CODE = N.TRADE_CODE AND S.BOND_MARK=M.BOND_MARK AND S.G_MARK=M.G_MARK AND S.EMS_NO=M.EMS_NO
        where T.SID in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateEntryHead">
        update t_dec_i_entry_head set D_DATE=to_char(to_date(#{entryDeclareDate},'yyyy-MM-dd'),'yyyy-MM-dd'),ENTRY_NO=#{entryNo}
        <if test="entryStatus != null and entryStatus != ''">
            ,ENTRY_STATUS=#{entryStatus}
        </if>
        <if test="seqNo != null and seqNo != ''">
            ,SEQ_NO=#{seqNo}
        </if>
        where EMS_LIST_NO=#{emsListNo} and TRADE_CODE=#{tradeCode,jdbcType=VARCHAR}
    </update>

    <select id="selectList" resultType="com.dcjet.cs.dec.model.DecECustomsTrack">
        SELECT <include refid="Base_Column_List"/> FROM V_DEC_I_CUSTOMS_TRACK t WHERE EMS_LIST_NO = #{emsListNo} and TRADE_CODE = #{tradeCode}
    </select>

    <select id="selectSet" resultType="int" parameterType="com.dcjet.cs.dec.model.DecECustomsTrack">
        SELECT COUNT(*) FROM T_DEC_I_CUSTOMS_TRACK t WHERE EMS_LIST_NO = #{emsListNo} and TRADE_CODE = #{tradeCode}
    </select>

    <!--根据sidc查询数据呢-->
    <select id="getReach" resultType="com.dcjet.cs.dec.model.DecICustomsTrack" parameterType="java.util.List">
        SELECT * FROM V_DEC_I_CUSTOMS_TRACK WHERE SID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!--根据sid更新完成进度-->
    <update id="updateComplete">
        UPDATE T_DEC_I_CUSTOMS_TRACK
        set COMPLETE = #{complete}
        WHERE SID = #{sid}
    </update>

    <update id="updateByEntryNo" parameterType="com.dcjet.cs.tax.model.dutyform.DutyFormHead">
        UPDATE T_DEC_I_CUSTOMS_TRACK
        <set>
            <choose>
                <when test='taxType == "A"'>
                    duty_price = #{paymentAmount},
                </when>
                <when test='taxType == "N"'>
                    duty_interest = #{paymentAmount},
                </when>
                <when test='taxType == "L"'>
                    tax_price = #{paymentAmount},
                    duty_total = (
                        select sum(duty_value)
                        from t_gwstd_dutyform_head a
                        inner join t_gwstd_dutyform_list b on b.tax_vou_no = a.tax_vou_no
                        where a.entry_id = #{entryId} and a.tax_type = 'L'
                    ),
                </when>
                <when test='taxType == "Q"'>
                    tax_interest = #{paymentAmount},
                </when>
                <when test='taxType == "Y"'>
                    excise_tax = #{paymentAmount},
                </when>
                <when test='taxType == "P"'>
                    excise_interest = #{paymentAmount},
                </when>
                <when test='taxType == "I"'>
                    ANTI_DUMPING_DUTY = #{paymentAmount},
                </when>
                <otherwise>
                    OTHER_TAX = #{paymentAmount},
                </otherwise>
            </choose>
            duty_date = case when duty_date is null then #{taxDate}
                             when #{taxDate} > duty_date then #{taxDate} else duty_date end,
            duty_type = case when #{sumTaxMark} = 'COLLPAY' then '1' else '0' end,
            update_time = CURRENT_TIMESTAMP(3),
            update_user = 'GW'
        </set>
        WHERE trade_code = #{tradeCode} and ENTRY_NO = #{entryId}
    </update>
    <update id="updateApprDate">
        <if test='_databaseId != "postgresql" '>
            begin
        </if>
        UPDATE T_DEC_I_CUSTOMS_TRACK
        SET PASS_DATE=coalesce(PASS_DATE,#{passDate}),
            DECLARE_DATE=coalesce(DECLARE_DATE,#{declareDate})
        WHERE EMS_LIST_NO = #{emsListNo}
        and TRADE_CODE = #{tradeCode};

        UPDATE T_DEC_I_ENTRY_HEAD
        SET
        D_DATE=#{entryDeclareDateStr}
        WHERE EMS_LIST_NO = #{emsListNo}
        and TRADE_CODE = #{tradeCode};

        UPDATE t_dec_i_bill_head
        SET ENTRY_DECLARE_DATE=coalesce(ENTRY_DECLARE_DATE,#{declareDate})
        WHERE EMS_LIST_NO = #{emsListNo}
        and TRADE_CODE = #{tradeCode};
        <if test='_databaseId != "postgresql" '>
            end;
        </if>
    </update>
    <update id="updateTotalByEntryNo" parameterType="com.dcjet.cs.tax.model.dutyform.DutyFormHead">
        UPDATE T_DEC_I_CUSTOMS_TRACK a
        SET
            TAX_TOTAL = COALESCE(DUTY_PRICE, 0) + COALESCE(TAX_PRICE, 0) + COALESCE(DUTY_INTEREST, 0) + COALESCE(TAX_INTEREST, 0)
             + COALESCE(ADD_TAX_PRICE, 0) + COALESCE(OTHER_TAX, 0) + COALESCE(EXCISE_TAX, 0) + COALESCE(EXCISE_INTEREST, 0) + COALESCE(ANTI_DUMPING_DUTY, 0)
        WHERE trade_code = #{tradeCode} and ENTRY_NO = #{entryId}
    </update>

    <update id="updateStatus" parameterType="list">
        UPDATE T_DEC_I_CUSTOMS_TRACK SET PURCHASE_CONFIRM=#{purchaseConfirm} WHERE SID IN
        <foreach collection="sids" item="item" open="(" separator="," close=")" index="index">
            #{item}
        </foreach>
    </update>

    <update id="updateEntryNo">
        <if test='_databaseId != "postgresql" '>
            begin
        </if>
        UPDATE t_dec_i_customs_track
        SET entry_no =#{ entryNo },
        declare_date=to_date(#{entryDeclareDate}, 'yyyy-MM-dd')
        WHERE trade_code = #{ tradeCode }
        AND head_id = #{ headId }
        AND ems_list_no = #{ emsListNo};

        update t_dec_i_entry_head
        set D_DATE=to_char(to_date(#{entryDeclareDate}, 'yyyy-MM-dd'), 'yyyy-MM-dd'),
        ENTRY_NO=#{entryNo}
        where EMS_LIST_NO = #{emsListNo}
        AND CONTR_NO = #{contrNo}
        and TRADE_CODE = #{tradeCode,jdbcType=VARCHAR};

        <if test='_databaseId != "postgresql" '>
            end;
        </if>
    </update>

    <select id="selectEntryNosByErpHeadId" resultType="com.dcjet.cs.dec.model.DecICustomsTrack">
        select
            string_agg(distinct ih.entry_no,',') as entryNo,
            max(it.pass_date) as passDate,
			min(ih.d_date) as entryDeclareDate
        from t_dec_i_customs_track it
		inner join t_dec_i_entry_head ih on it.erp_head_id = ih.erp_head_id
        where
        it.erp_head_id = #{erpHeadId}
    </select>
    <select id="selectByTaxVouNo" resultType="java.lang.String">
        select a.sid
        from t_dec_i_customs_track a
        inner join t_dec_i_entry_head b on b.erp_head_id = a.erp_head_id
        inner join t_gwstd_dutyform_head c on c.entry_id = b.entry_no and c.trade_code = b.trade_code
        where c.ie_flag = 'I' and c.entry_id = #{entryNo} and a.trade_code = #{tradeCode}
        limit 1
    </select>
</mapper>
