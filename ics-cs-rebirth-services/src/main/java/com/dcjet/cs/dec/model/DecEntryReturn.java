package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 待发送报关单信息数据表
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_DEC_ENTRY_RETURN")
public class DecEntryReturn implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 发票号
     */
    @Column(name = "invoice_no")
    private String invoiceNo;
    /**
     * 预录入单表头sid
     */
    @Column(name = "head_id")
    private String headId;
    /**
     * 预录入单内部编号
     */
    @Column(name = "ems_list_no")
    private String emsListNo;
    /**
     * 实际进场日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ate")
    private Date ate;
    /**
     * 预计到港日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "eta")
    private Date eta;
    /**
     * 实际到港日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ata")
    private Date ata;
    /**
     * 放行日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "pass_date")
    private Date passDate;
    /**
     * 预计到厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "eda")
    private Date eda;
    /**
     * 报关单号
     */
    @Column(name = "entry_no")
    private String entryNo;
    /**
     * 工厂编号
     */
    @Column(name = "factory")
    private String factory;
    /**
     * 报关单申报日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "entry_declare_date")
    private Date entryDeclareDate;

    //查询条件
    /**
     * 发票号
     */
    @Transient
    private List<String> invoiceNoList;
    /**
     * 申报日期(起始)
     */
    @Transient
    private String declareDateFrom;
    /**
     * 申报日期(截止)
     */
    @Transient
    private String declareDateTo;
    /**
     * 提运单号
     */
    @Column(name = "bill_no")
    private String billNo;
    /**
     * 运输方式
     */
    @Column(name = "traf_mode")
    private String trafMode;
    /**
     * 监管方式
     */
    @Column(name = "trade_mode")
    private String tradeMode;
    /**
     * 进境关别
     */
    @Column(name = "i_e_port")
    private String IEPort;
    /**
     * 船公司代码
     */
    @Column(name = "ship_comp_code")
    private String shipCompCode;
    /**
     * 同步国际物流信息时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "sync_logistics_time")
    private Date syncLogisticsTime;
    /**
     * 同步国际物流信息备注
     */
    @Column(name = "sync_logistics_note")
    private String syncLogisticsNote;
    /**
     * 数据锁，用于批量job执行
     */
    @Column(name = "lock_mark")
    private String lockMark;
}
