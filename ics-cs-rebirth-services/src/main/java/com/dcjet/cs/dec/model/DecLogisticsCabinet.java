package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-5
 */
@Setter
@Getter
@Table(name = "T_LOGISTICS_CABINET")
public class DecLogisticsCabinet implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 创建人用户名
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 更新人用户名
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;
    /**
     * 进出口标记
     */
    @Column(name = "IE_MARK")
    @JsonProperty("ieMark")
    private String ieMark;
    /**
     * 车辆属性（1 国内 2 国际）
     */
    @Column(name = "CABINET_ATTR")
    private String cabinetAttr;
    /**
     * 车柜类型
     */
    @Column(name = "CABINET_TYPE")
    private String cabinetType;
    /**
     * 车柜数量
     */
    @Column(name = "CABINET_AMOUNT")
    private Integer cabinetAmount;
    /**
     * 车柜类型名称
     */
    @Column(name = "CABINET_NAME")
    private String cabinetName;

    /**
     * 提单表头sid
     */
    @Column(name = "HEAD_ID")
    private String headId;
}
