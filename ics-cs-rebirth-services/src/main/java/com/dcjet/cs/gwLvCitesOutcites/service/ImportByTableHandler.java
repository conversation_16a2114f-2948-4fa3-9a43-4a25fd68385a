package com.dcjet.cs.gwLvCitesOutcites.service;

import com.alibaba.druid.util.StringUtils;
import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.dto.gwLvCitesOutcites.GwLvCitesOutcitesParam;
import com.dcjet.cs.gwLvCitesOutcitesTmp.dao.GwLvCitesOutcitesTmpMapper;
import com.dcjet.cs.gwLvCitesOutcitesTmp.mapper.GwLvCitesOutcitesTmpDtoMapper;
import com.dcjet.cs.gwLvCitesOutcitesTmp.model.GwLvCitesOutcitesTmp;
import com.dcjet.cs.gwLvCitesOutcitesTmp.service.GwLvCitesOutcitesTmpService;
import com.dcjet.cs.gwLvCitesProofMain.service.EnumLvDictionary;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.EnumCollection;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 业务系统校验数据、持久化数据的业务类（采用临时表校验，只要用到临时表就算临时表校验）
 * 临时表校验需要在校验方法checkData中返回TempOwnerId，并在持久化数据方法persistData中接收
 * 导入服务在发现业务采用了临时表校验（即checkData方法返回的结果包含TempOwnerId）时，
 * 在调用persistData方法时便不会把正确数据文件重新解析传递给业务方法，减少不必要的解析时间
 */
@Service
public class ImportByTableHandler extends BaseService<GwLvCitesOutcitesTmp> implements ImportHandlerInterface {

    @Resource
    private GwLvCitesOutcitesTmpMapper gwLvCitesOutcitesTmpMapper;

    @Override
    public Mapper<GwLvCitesOutcitesTmp> getMapper() {
        return gwLvCitesOutcitesTmpMapper;
    }
    @Resource
    private GwLvCitesOutcitesService gwLvCitesOutcitesService;
    @Resource
    private GwLvCitesOutcitesTmpDtoMapper gwLvCitesOutcitesTmpDtoMapper;
    @Resource
    GwLvCitesOutcitesTmpService gwLvCitesOutcitesTmpService;
    @Resource
    private BulkInsertConfig bulkInsertConfig;


    final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    // 0：正确数据 1：错误数据 2：警告数据 对应于EnumCollection.EntityListType的枚举的ordinal
    public final Integer CORRECT_FLAG = EnumCollection.EntityListType.CORRECT_LIST.ordinal();
    public final Integer WRONG_FLAG = EnumCollection.EntityListType.WRONG_LIST.ordinal();
    public final Integer WARN_FLAG = EnumCollection.EntityListType.WARN_LIST.ordinal();

    /**
     * 返回当前业务类对应的任务类型，导入服务执行器将根据此方法的返回值获取相应的业务类实例
     * @return 当前业务类对应的任务类型
     */
    @Override
    public String getTaskCode() {
        String strTaskCode = "cites-outcites";
        return strTaskCode;
    }

    /**
     * 业务校验方法（临时表校验）
     * @param mapBasicParam 任务基础参数(当前任务的sid、taskCode、tradeCode、importType、insertUserOid、insertUserName)
     * @param mapBusinessParam 业务自定义参数(比如insertUser、插入表体时用到的表头id等都可以在这里传入,需在前端导入时确定需要用到的参数并传递)
     * @param mapObjectList 实体列表map，可根据xmlContent的sheet的id获取某类实体列表
     * @return 业务校验结果，head根据xmlContent的sheet的field获取，无需设置；当采用临时表校验方式时tempOwnerId必须设置，否则插入正确数据时无法进行操作
     */
    @Override
    public List<ExcelImportDto> checkData(Map<String,Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        //获取任务基本参数
        String strEntityId = getTaskCode();
        List<ExcelImportDto> excelImportDtoList=new ArrayList<>();
        //根据配置文件的每个sheet的id获取特定sheet转化出来的实体列表，按配置顺序获取
        if(!mapObjectList.containsKey(strEntityId)){
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在" + strEntityId);
            return null;
        }

        List<Object> objectList=mapObjectList.get(strEntityId);
        mapBasicParam.get("importType");//获取任务的导入类型(新增、修改、删除)，如只有新增可不判断，否则根据类型做不同的业务操作
        //从自定义参数中获取需要的数据，此处是导入表体，需获取对应表头的id和当前操作者
        if(!mapBusinessParam.containsKey("insertUser")){
            XdoImportLogger.log("部分自定义参数未成功获取");
            return null;
        }

        String tradeCode = mapBasicParam.get("tradeCode").toString();
        String insertUser = mapBusinessParam.get("insertUser").toString();


        int intObjectListSize = objectList.size();
        XdoImportLogger.log("导入数据共：" + intObjectListSize);

        String tempOwnerId = UUID.randomUUID().toString();
        List<GwLvCitesOutcitesTmp> dcBillListTmps = Collections.synchronizedList(new ArrayList<>(intObjectListSize));
        try {
            Integer rowIndex = 0;
            Date insertDate = new Date();
            for(Object item : objectList) {
                GwLvCitesOutcitesTmp gwLvCitesOutcites = (GwLvCitesOutcitesTmp) item;
                gwLvCitesOutcites.setOrderBy(rowIndex);
                gwLvCitesOutcitesTmpService.initOutcitesTmp(gwLvCitesOutcites,tradeCode,insertUser,tempOwnerId,insertDate);

                //业务验证
                GwLvCitesOutcitesParam par = new GwLvCitesOutcitesParam();
                gwLvCitesOutcitesTmpDtoMapper.updatePo(gwLvCitesOutcites,par);
                List<Pair<String,String>> result = gwLvCitesOutcitesService.checkData(par, EnumLvDictionary.OperateType.IMPORT_ADD,insertUser);
                gwLvCitesOutcites.setBrandCode(par.getBrandCode());
                if(gwLvCitesOutcites.getDeclarePrice() == null)
                    gwLvCitesOutcites.setDeclarePrice(par.getDeclarePrice());
                gwLvCitesOutcites.setCurr(par.getCurr());
                if(result.size() > 0) {
                    gwLvCitesOutcites.setTempRemark(gwLvCitesOutcites.getTempRemark() + gwLvCitesOutcitesService.convert(result));
                }
                if(!StringUtils.isEmpty(gwLvCitesOutcites.getTempRemark())) {
                    gwLvCitesOutcites.setTempFlag(1);
                }
                dcBillListTmps.add(gwLvCitesOutcites);

                rowIndex++;
            }
            gwLvCitesOutcitesService.checkDataByImport(dcBillListTmps,insertUser);

        } catch (ErrorException err) {
            XdoImportLogger.log(err);
            return null;
        } catch (Exception ex) {
            XdoImportLogger.log(ex);
            return null;
        }

        XdoImportLogger.log("根据TEMP_OWNER删除临时表数据");
        gwLvCitesOutcitesTmpService.deleteByTempOwner(tempOwnerId);

        XdoImportLogger.log("共获取插入实体："+dcBillListTmps.size()+";执行快速入库操作");
        //初始化快速入库实例（在config文件夹下新建OracleBulkConfig用于读取配置文件中的数据库连接配置）
        //使用快速入库时，拼接的sql语句会根据当前实体进行，如果某个字段存在于实体定义中，但传入的值为空，那么这个字段在数据表存在的默认值将无效
        // 所以请显示赋值，或者，如果要使用数据表的默认值请将此字段从实体中去除
        IBulkInsert iBulkInsert = BulkInsertFactory.createInstance(bulkInsertConfig);
        int intEffectCount = 0;
        try{
            intEffectCount = iBulkInsert.fastImport(dcBillListTmps);

            //调用存储过程校验，对字段的基本校验(数据类型、非空、长度、精度等)尽量放在校验实体的注解上
            //如果有特殊情况必须在存储过程里做，不要用 !='' 来判断字段是否非空，因为oracle里''会被当作null处理，而判断null不能用不等于号，因此不会得到预想的结果
            //XdoImportLogger.log("执行存储过程校验");
        }
        catch (Exception ex){
            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
            XdoImportLogger.log(ex);
            return null;
        }

        ExcelImportDto<GwLvCitesOutcitesTmp> excelImportDto = new ExcelImportDto<>();
        excelImportDto.setTempOwnerId(tempOwnerId);

        //获取正确、错误、警告数据列表(警告数据根据各自业务需求设置,可不加)
        //正确数据包含警告数据，因此要把警告数据添加到正确数据列表中，否则警告数据无法导入
        excelImportDto.setWarnList(selectDataList(tempOwnerId,WARN_FLAG));
        excelImportDto.setCorrectList(selectDataList(tempOwnerId,CORRECT_FLAG));
        excelImportDto.setWrongList(selectDataList(tempOwnerId,WRONG_FLAG));

        XdoImportLogger.log("correct："+excelImportDto.getCorrectList().size()
                +";wrong："+excelImportDto.getWrongList().size()
                +";warn："+excelImportDto.getWarnList().size());

        excelImportDtoList.add(excelImportDto);
        //多sheet时其他sheet的校验，重复上述步骤，按配置顺序添加到excelImportDtoList，最后返回
        //continue。。。
        XdoImportLogger.log(strEntityId+"临时表校验结束");
        return excelImportDtoList;

    }


    public List<GwLvCitesOutcitesTmp> selectDataList(String tempOwnerId,Integer flag) {
        GwLvCitesOutcitesTmp gwLvCitesOutcitesTmp = new GwLvCitesOutcitesTmp();
        gwLvCitesOutcitesTmp.setTempOwner(tempOwnerId);
        gwLvCitesOutcitesTmp.setTempFlag(flag);

        List<GwLvCitesOutcitesTmp> arr = gwLvCitesOutcitesTmpMapper.getList(gwLvCitesOutcitesTmp);
        return arr;
    }

    /**
     * 业务持久化数据方法
     * @param mapBasicParam 任务基础参数(当前任务的sid、taskCode、tradeCode、importType、insertUserOid、insertUserName)
     * @param mapBusinessParam 业务自定义参数(比如insertUser、插入表体时用到的表头id等都可以在这里传入,需在前端导入时确定需要用到的参数并传递)
     * @param mapObjectList 正确数据序列化的实体列表集合，根据导入配置文件(xmlContent)的每个sheet配置的id获取相应实体列表（采用内存校验时传递）
     * @param tempOwnerIdList 临时表处理批次Id，单sheet时取第一条（采用临时表校验时传递）
     * @return 业务持久化数据结果，导入服务根据返回值设置任务状态
     */
    @Override
    public ResultObject persistData(Map<String,Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList)  throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, "导入成功");
        //获取任务基本参数
        mapBasicParam.get("importType");//获取任务的导入类型(新增、修改、删除)，如只有新增可不判断，否则根据类型做不同的业务操作
        //按配置顺序（同时也是业务校验时添加到excelImportDtoList的顺序）获取临时表批次Id
        if(tempOwnerIdList==null || tempOwnerIdList.size()<=0){
            resultObject.setSuccess(false);
            resultObject.setMessage("临时表处理批次Id为空，无法执行持久化数据方法");
            return resultObject;
        }
        String strTempOwnerId = tempOwnerIdList.get(0);
        XdoImportLogger.log(strTempOwnerId + "数据持久化执行开始");
        try {
            gwLvCitesOutcitesTmpService.importCriterionDB(strTempOwnerId);
            XdoImportLogger.log(strTempOwnerId + "导入结束");
        }catch (Exception ex) {
            resultObject.setSuccess(false);
            resultObject.setMessage(strTempOwnerId + "。" + ex.getMessage());
        }
        return resultObject;
    }
}