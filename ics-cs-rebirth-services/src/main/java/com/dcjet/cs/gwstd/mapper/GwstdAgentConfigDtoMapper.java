package com.dcjet.cs.gwstd.mapper;

import com.dcjet.cs.dto.gwstd.GwstdAgentConfigDto;
import com.dcjet.cs.dto.gwstd.GwstdAgentConfigParam;
import com.dcjet.cs.gwstd.model.GwstdAgentConfig;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-7-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwstdAgentConfigDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwstdAgentConfigDto toDto(GwstdAgentConfig po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwstdAgentConfig toPo(GwstdAgentConfigParam param);
    /**
     * 数据库原始数据更新
     * @param gwstdAgentConfigParam
     * @param gwstdAgentConfig
     */
    void updatePo(GwstdAgentConfigParam gwstdAgentConfigParam, @MappingTarget GwstdAgentConfig gwstdAgentConfig);
    default void patchPo(GwstdAgentConfigParam gwstdAgentConfigParam, GwstdAgentConfig gwstdAgentConfig) {
        // TODO 自行实现局部更新
    }
}
