<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.gwstd.dao.GwstdJobMapper">
    <resultMap id="gwstdJobResultMap" type="com.dcjet.cs.gwstd.model.GwstdJob">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="JOB_TYPE" property="jobType" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="ERROR_MESSAGE" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="BEGIN_TIME" property="beginTime" jdbcType="TIMESTAMP"/>
        <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="BUSINESS_ID" property="businessId" jdbcType="VARCHAR"/>
        <result column="EXTEND_FILED1" property="extendFiled1" jdbcType="VARCHAR"/>
        <result column="EXTEND_FILED2" property="extendFiled2" jdbcType="VARCHAR"/>
        <result column="EXTEND_FILED3" property="extendFiled3" jdbcType="VARCHAR"/>
        <result column="EXTEND_FILED4" property="extendFiled4" jdbcType="VARCHAR"/>
        <result column="EXTEND_FILED5" property="extendFiled5" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="BUSINESS_DATE" property="businessId" jdbcType="TIMESTAMP"/>
        <result column="RETRY_COUNT" property="retryCount" jdbcType="INTEGER"/>
        <result column="LOCK_MARK" property="lockMark" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,TRADE_CODE
     ,JOB_TYPE
     ,STATUS
     ,ERROR_MESSAGE
     ,INSERT_TIME
     ,INSERT_USER
     ,BEGIN_TIME
     ,END_TIME
     ,BUSINESS_ID
     ,EXTEND_FILED1
     ,EXTEND_FILED2
     ,EXTEND_FILED3
     ,EXTEND_FILED4
     ,EXTEND_FILED5
     ,LOCK_MARK
    </sql>
    <sql id="condition">
            and TRADE_CODE = #{tradeCode}
        <if test="jobType != null and jobType != ''">
            and JOB_TYPE = #{jobType}
        </if>
        <if test="status != null and status != ''">
            and STATUS = #{status}
        </if>
        <if test="errorMessage != null and errorMessage != ''">
            and ERROR_MESSAGE = #{errorMessage}
        </if>
        <if test="businessId != null and businessId != ''">
            and BUSINESS_ID = #{businessId}
        </if>
        <if test="extendFiled1 != null and extendFiled1 != ''">
            and EXTEND_FILED1 = #{extendFiled1}
        </if>
        <if test="extendFiled2 != null and extendFiled2 != ''">
            and EXTEND_FILED2 = #{extendFiled2}
        </if>
        <if test="extendFiled3 != null and extendFiled3 != ''">
            and EXTEND_FILED3 = #{extendFiled3}
        </if>
        <if test="extendFiled4 != null and extendFiled4 != ''">
            and EXTEND_FILED4 = #{extendFiled4}
        </if>
        <if test="extendFiled5 != null and extendFiled5 != ''">
            and EXTEND_FILED5 = #{extendFiled5}
        </if>
    </sql>
    <sql id="conditionORACLE">
        <if test="beginTimeFrom != null and beginTimeFrom != ''">
            <![CDATA[ and BEGIN_TIME >= to_date(#{beginTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="beginTimeTo != null and beginTimeTo != ''">
            <![CDATA[ and BEGIN_TIME <= to_date(#{beginTimeTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="endTimeFrom != null and endTimeFrom != ''">
            <![CDATA[ and END_TIME >= to_date(#{endTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="endTimeTo != null and endTimeTo != ''">
            <![CDATA[ and END_TIME <= to_date(#{endTimeTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
    </sql>
    <sql id="conditionPG">
        <if test="beginTimeFrom != null and beginTimeFrom != ''">
            <![CDATA[ and BEGIN_TIME >= to_timestamp(#{beginTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="beginTimeTo != null and beginTimeTo != ''">
            <![CDATA[ and BEGIN_TIME <= to_timestamp(#{beginTimeTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="endTimeFrom != null and endTimeFrom != ''">
            <![CDATA[ and END_TIME >= to_timestamp(#{endTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="endTimeTo != null and endTimeTo != ''">
            <![CDATA[ and END_TIME <= to_timestamp(#{endTimeTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwstdJobResultMap" parameterType="com.dcjet.cs.gwstd.model.GwstdJob">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_GWSTD_JOB t
        <where>
            <include refid="condition"></include>
            <if test='_databaseId == "postgresql" '>
                <include refid="conditionPG"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="conditionORACLE"></include>
            </if>
        </where>
        order by t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_GWSTD_JOB t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="getLastEntity" resultMap="gwstdJobResultMap" parameterType="com.dcjet.cs.gwstd.model.GwstdJob">
        SELECT
        <include refid="Base_Column_List"/>
        from
        (SELECT
        rownum rn,
        <include refid="Base_Column_List"/>
        FROM
        (select
        <include refid="Base_Column_List"/>
        from T_GWSTD_JOB t
        where TRADE_CODE = #{tradeCode} and JOB_TYPE = #{jobType} and EXTEND_FILED2 = #{extendFiled2}
        <if test="extendFiled1 != null and extendFiled1 != ''">
            and EXTEND_FILED1 = #{extendFiled1}
        </if>
        order by INSERT_TIME desc,sid) t1) t2
        where t2.rn=1
    </select>
    <select id="getLastEntity" resultMap="gwstdJobResultMap" parameterType="com.dcjet.cs.gwstd.model.GwstdJob" databaseId="postgresql">
        SELECT
        <include refid="Base_Column_List"/>
        from
        (SELECT
        row_number() over() rn,
        <include refid="Base_Column_List"/>
        FROM
        (select
        <include refid="Base_Column_List"/>
        from T_GWSTD_JOB t
        where TRADE_CODE = #{tradeCode} and JOB_TYPE = #{jobType} and EXTEND_FILED2 = #{extendFiled2}
        <if test="extendFiled1 != null and extendFiled1 != ''">
            and EXTEND_FILED1 = #{extendFiled1}
        </if>
        order by INSERT_TIME desc,sid) t1) t2
        where t2.rn=1
    </select>
    <update id="updateLockMark" parameterType="string" databaseId="postgresql">
        update T_GWSTD_JOB  set LOCK_MARK=#{uuid},begin_time = now() where STATUS='0' and JOB_TYPE=#{jobType} and (LOCK_MARK is null or LOCK_MARK='')
    </update>
    <update id="updateLockMark" parameterType="string" >
        update T_GWSTD_JOB  set LOCK_MARK=#{uuid},begin_time = sysdate where STATUS='0' and JOB_TYPE=#{jobType} and (LOCK_MARK is null or LOCK_MARK='')
    </update>
    <select id="getJobListByuuid" resultMap="gwstdJobResultMap" parameterType="string">
        select
          <include refid="Base_Column_List"/>
        from T_GWSTD_JOB where LOCK_MARK=#{uuid}
    </select>

    <select id="getEmlSyncJob" resultType="map" parameterType="string">
        select SID as "SID",STATUS AS "STATUS",END_TIME AS "END_TIME",ERROR_MESSAGE AS "ERROR_MESSAGE" from (
            select SID,STATUS,END_TIME,ERROR_MESSAGE
            from T_GWSTD_JOB
            where TRADE_CODE=#{compCode}
                and EXTEND_FILED1=#{copEmsNo}
                and JOB_TYPE=#{jobType}
            order by INSERT_TIME desc ) t
        <if test='_databaseId == "postgresql" '>
            limit 1
        </if>
        <if test='_databaseId != "postgresql" '>
            where rownum=1
        </if>
    </select>

    <select id="checkExistsJob" parameterType="com.dcjet.cs.gwstd.model.GwstdJob" resultMap="gwstdJobResultMap">
        select * from T_GWSTD_JOB where TRADE_CODE = #{tradeCode} and JOB_TYPE = #{jobType} and STATUS in ('0','1')
    </select>
    <select id="getLastMidInfo" resultMap="gwstdJobResultMap" parameterType="com.dcjet.cs.gwstd.model.GwstdJob">
        SELECT
        <include refid="Base_Column_List"/>
        from
        (SELECT
        rownum rn,
        <include refid="Base_Column_List"/>
        FROM
        (select
        <include refid="Base_Column_List"/>
        from T_GWSTD_JOB t
        where TRADE_CODE = #{tradeCode} and JOB_TYPE = #{jobType}
        order by INSERT_TIME desc,sid) t1) t2
        where t2.rn=1
    </select>
    <select id="getLastMidInfo" resultMap="gwstdJobResultMap" parameterType="com.dcjet.cs.gwstd.model.GwstdJob" databaseId="postgresql">
        SELECT
        <include refid="Base_Column_List"/>
        from
        (SELECT
        row_number() over() rn,
        <include refid="Base_Column_List"/>
        FROM
        (select
        <include refid="Base_Column_List"/>
        from T_GWSTD_JOB t
        where TRADE_CODE = #{tradeCode} and JOB_TYPE = #{jobType}
        order by INSERT_TIME desc,sid) t1) t2
        where t2.rn=1
    </select>
    <select id="getLastData" resultMap="gwstdJobResultMap" parameterType="com.dcjet.cs.util.pojo.StockParam">
        SELECT
          <include refid="Base_Column_List" />
        FROM
        (
          SELECT
            <include refid="Base_Column_List" />
          FROM
          T_GWSTD_JOB
          <where>
            TRADE_CODE = #{tradeCode}
            AND BUSINESS_TYPE = #{taskType}
            AND JOB_TYPE = #{jobType}
            AND BUSINESS_DATE = #{whDate}
          </where>
          ORDER BY INSERT_TIME DESC
        ) WHERE ROWNUM = 1
    </select>
    <select id="getLastData" resultMap="gwstdJobResultMap" parameterType="com.dcjet.cs.util.pojo.StockParam" databaseId="postgresql">
        SELECT
          <include refid="Base_Column_List" />
        FROM
        (
            SELECT
              <include refid="Base_Column_List" />
            FROM
            T_GWSTD_JOB
            <where>
                TRADE_CODE = #{tradeCode}
                AND BUSINESS_TYPE = #{taskType}
                AND JOB_TYPE = #{jobType}
                AND BUSINESS_DATE = #{whDate}
            </where>
            ORDER BY INSERT_TIME DESC
        ) a
        limit 1
    </select>

    <update id="resetStatusBySid">
        update T_GWSTD_JOB set status = #{status},lock_mark=null,insert_time = now() where sid = #{sid}
    </update>

</mapper>
