package com.dcjet.cs.gwstd.dao;
import com.dcjet.cs.gwstd.model.GwstdHscode;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;

/**
* generated by Generate 神码
* GwstdHscode
* <AUTHOR>
* @date: 2020-11-27
*/
public interface GwstdHscodeMapper extends Mapper<GwstdHscode> {

    List<String> selectDistinctHSCode();

    int deleteAll();

    List<GwstdHscode> getList(GwstdHscode gwstdHscode);

    List<String> getTradeCodeList();

    List<String> getEmsNoList(GwstdHscode gwstdHscode);

    Date selectTime();
}
