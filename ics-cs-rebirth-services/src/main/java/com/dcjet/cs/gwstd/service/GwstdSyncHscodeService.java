package com.dcjet.cs.gwstd.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.notice.NoticeTaskEventParam;
import com.dcjet.cs.gwstd.dao.GwstdHscodeMapper;
import com.dcjet.cs.gwstd.dao.GwstdJobAllotConfigMapper;
import com.dcjet.cs.gwstd.dao.GwstdJobMapper;
import com.dcjet.cs.gwstd.model.GwstdHscode;
import com.dcjet.cs.gwstd.model.GwstdJob;
import com.dcjet.cs.gwstd.model.GwstdJobAllotConfig;
import com.dcjet.cs.notice.service.NoticeInfoService;
import com.dcjet.cs.notice.service.event.NoticeTaskEvent;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.LoggerUtil;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 同步企业HSCode任务
 * @author: WJ
 * @createDate: 2020/11/27 17:38
 */
@Service
public class GwstdSyncHscodeService {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    private static final String JOB_TYPE = "SYNC_HS_CODE";
//    private Cache<String, GwstdJobAllotConfig> gwstdJobAllotCache;

    @Resource
    private GwstdJobMapper gwstdJobMapper;
    @Resource
    private GwstdHscodeMapper gwstdHscodeMapper;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    @Resource
    private CommonService commonService;
    @Resource
    private GwstdJobAllotConfigMapper gwstdJobAllotConfigMapper;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    NoticeInfoService noticeInfoService;


    public void syncHSCode() {
        LoggerUtil.logInfo(logger, "**************同步企业HSCode任务调度开始**************");
        //生成一个批次号UUID
        String uuid = UUID.randomUUID().toString();
        //查询任务状态为0且锁定标志为空的数据将其锁定标志改为该批次号
        LoggerUtil.logInfo(logger,"锁定待同步的企业HSCode任务");
        gwstdJobMapper.updateLockMark(uuid, JOB_TYPE);
        List<GwstdJob> gwstdJobs = gwstdJobMapper.getJobListByuuid(uuid);
        if (CollectionUtils.isEmpty(gwstdJobs)) {
            LoggerUtil.logInfo(logger, "**************当前无需要执行的同步企业HSCode调度任务**************");
            return;
        }

        GwstdJob gwstdJob = gwstdJobs.get(0);
        LoggerUtil.logInfo(logger, "**************获取关务HSCode（去重）开始**************");
        List<String> hsCodeList = gwstdHscodeMapper.selectDistinctHSCode();
        logger.debug("数据库获取到的关务HSCode（去重）：{}", hsCodeList);
        LoggerUtil.logInfo(logger, "**************获取关务HSCode（去重）结束**************");

        gwstdJob.setStatus(Constants.JOB_STATUS_1);
        gwstdJobMapper.updateByPrimaryKey(gwstdJob);

        List<Map<String, String>> list;
        try {
            LoggerUtil.logInfo(logger, "**************获取海关HSCode（去重）开始**************");
            list = pCodeHolder.getByCodes(PCodeType.COMPLEX, hsCodeList).orElse(null);
            List<String> a = list.stream().map(x -> x.get("CODE")).collect(Collectors.toList());
            logger.debug("pcode拉取到的商编：{}", a);
            LoggerUtil.logInfo(logger, "**************获取海关HSCode（去重）结束**************");
        } catch (Exception e) {
            int tryNum = 1;
            // 错误重试3次
            list = doTry(hsCodeList, tryNum, gwstdJob);
        }

        if (CollectionUtils.isNotEmpty(list)) {
            LoggerUtil.logInfo(logger, "**************map转实体开始**************");
            List<GwstdHscode> gwstdHscodeList = new ArrayList<>();
            list.stream().forEach(hs -> {
                GwstdHscode gwstdHscode = new GwstdHscode();
                gwstdHscode.setSid(UUID.randomUUID().toString());
                gwstdHscode.setCodeTS(hs.get("CODE"));
                gwstdHscode.setGName(hs.get("NAME"));
                gwstdHscode.setUnit1(hs.get("UNIT_1"));
                gwstdHscode.setUnit2(hs.get("UNIT_2"));
                gwstdHscode.setInsertUser(Constants.SYSTEM);
                gwstdHscode.setInsertTime(new Date());
                gwstdHscodeList.add(gwstdHscode);
            });
            LoggerUtil.logInfo(logger, "**************map转实体结束**************");

            if (CollectionUtils.isNotEmpty(gwstdHscodeList)) {
                LoggerUtil.logInfo(logger, "**************数据入库开始**************");
                // 先删后插
                gwstdHscodeMapper.deleteAll();
                bulkSqlOpt.batchInsert(gwstdHscodeList, GwstdHscodeMapper.class);
                LoggerUtil.logInfo(logger, "**************数据入库结束**************");
            }

            gwstdJob.setStatus(Constants.JOB_STATUS_2);
            gwstdJobMapper.updateByPrimaryKey(gwstdJob);

            //添加邮件通知任务
            addSendEmailTask();
        }
        LoggerUtil.logInfo(logger, "**************同步企业HSCode任务调度结束**************");
    }

    public void addSendEmailTask() {
        //添加邮件通知任务
        for(String tradeCode : gwstdHscodeMapper.getTradeCodeList()) {
            GwstdHscode gwstdHscode = new GwstdHscode();
            gwstdHscode.setTradeCode(tradeCode);
            Arrays.asList("MAT_NONBONDED_CHECK_WARN","MAT_EXGIMG_CHECK_WARN").stream().forEach(type-> {
                if (noticeInfoService.exists(tradeCode, type)) {
                    applicationContext.publishEvent(new NoticeTaskEvent(new NoticeTaskEventParam(type, tradeCode, null)));
                }
            });
        }
    }

    private List<Map<String, String>> doTry(List<String> hsCodeList, int tryNum, GwstdJob gwstdJob) {
        if (tryNum > 3) {
            return null;
        }
        try {
            LoggerUtil.logInfo(logger, "**************获取海关HSCode（去重）第{}次重试开始**************", tryNum);
            List<Map<String, String>> list = pCodeHolder.getByCodes(PCodeType.COMPLEX, hsCodeList).orElse(null);
            LoggerUtil.logInfo(logger, "**************获取海关HSCode（去重）第{}次重试结束**************", tryNum);
            return list;
        } catch (Exception e) {
            if (tryNum == 3) {
                gwstdJob.setStatus(Constants.JOB_STATUS_3);
                gwstdJobMapper.updateByPrimaryKey(gwstdJob);
                LoggerUtil.logError(logger, "**************同步企业HSCode任务失败**************", e);
            }
            tryNum++;
            return doTry(hsCodeList, tryNum, gwstdJob);
        }
    }

    /**
     * 添加同步企业HSCode任务
     * @return
     */
    public ResultObject assignTask() {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("任务分配成功！"));

//        gwstdJobAllotCache = gwstdJobAllotMapperConfig.getGwstdJobAllotCache();
//        if (0 == gwstdJobAllotCache.estimatedSize()) {
//            gwstdJobAllotCache = gwstdJobAllotMapperConfig.refreshCache();
//        }
//        GwstdJobAllotConfig config = gwstdJobAllotCache.getIfPresent(JOB_TYPE);
        GwstdJobAllotConfig config = new GwstdJobAllotConfig(){{
            setJobType(JOB_TYPE);
            setTradeCode(CommonVariable.TRADE_CODE_9999999999);
        }};

        config = gwstdJobAllotConfigMapper.selectOne(config);
        if (null == config) {
            LoggerUtil.logInfo(logger, String.format("未配置[%s]类型任务", JOB_TYPE));
            resultObject.setMessage(String.format("[%s]%s",JOB_TYPE,xdoi18n.XdoI18nUtil.t("该类型未配置任务")));
            resultObject.setSuccess(false);
            return resultObject;
        }
        if (null == config.getLastExecTime()) {
            long time = (new Date()).getTime()-3600*1000;
            config.setLastExecTime(new Date(time));
        }

        String msg = commonService.checkTime(config, JOB_TYPE);
        if (StringUtils.isNotBlank(msg)) {
            resultObject.setSuccess(false);
            resultObject.setMessage(msg);
            return resultObject;
        }

        // 插入新的任务
        GwstdJob gwstdJob = new GwstdJob() {{
            setJobType(JOB_TYPE);
            setSid(UUID.randomUUID().toString());
            setTradeCode(CommonVariable.TRADE_CODE_9999999999);
            setStatus(ConstantsStatus.STATUS_0);
            setInsertTime(new Date());
            setInsertUser(Constants.SYSTEM);
        }};

        gwstdJobMapper.insert(gwstdJob);
        LoggerUtil.logInfo(logger, String.format("**************插入新的任务,任务类型为：[%s]**************", JOB_TYPE));

        config.setLastExecTime(new Date());
        gwstdJobAllotConfigMapper.updateByPrimaryKey(config);
//        gwstdJobAllotCache.put(JOB_TYPE, config);
        LoggerUtil.logInfo(logger, String.format("**************更新任务执行时间,任务类型为：[%s]**************", JOB_TYPE));

        return resultObject;
    }
}
