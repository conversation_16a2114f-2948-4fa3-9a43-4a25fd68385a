package com.dcjet.cs.outward.mapper;

import com.dcjet.cs.dto.outward.WfDeclareFormDto;
import com.dcjet.cs.dto.outward.WfDeclareFormParam;
import com.dcjet.cs.outward.model.WfDeclareForm;
import com.dcjet.cs.outward.model.WfDeclareFormApproved;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-7-18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WfDeclareFormDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    WfDeclareFormDto toDto(WfDeclareForm po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    WfDeclareForm toPo(WfDeclareFormParam param);
    /**
     * 数据库原始数据更新
     * @param wfDeclareFormParam
     * @param wfDeclareForm
     */
    void updatePo(WfDeclareFormParam wfDeclareFormParam, @MappingTarget WfDeclareForm wfDeclareForm);
    default void patchPo(WfDeclareFormParam wfDeclareFormParam, WfDeclareForm wfDeclareForm) {
        // TODO 自行实现局部更新
    }

    WfDeclareForm toMo(WfDeclareFormApproved wfDeclareFormApproved);
}
