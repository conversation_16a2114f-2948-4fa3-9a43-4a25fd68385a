package com.dcjet.cs.outward.service;

import com.dcjet.cs.outward.dao.WfDeclareFormMapper;
import com.dcjet.cs.outward.model.WfDeclareForm;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.outward.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.outward.dao.WfDeclareFormApprovedMapper;
import com.dcjet.cs.outward.mapper.WfDeclareFormApprovedDtoMapper;
import com.dcjet.cs.outward.model.WfDeclareFormApproved;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2023-5-23
 */
@Service
public class WfDeclareFormApprovedService extends BaseService<WfDeclareFormApproved> {
    @Resource
    private WfDeclareFormMapper wfDeclareFormMapper;
    @Resource
    private WfDeclareFormApprovedMapper wfDeclareFormApprovedMapper;
    @Resource
    private WfDeclareFormApprovedDtoMapper wfDeclareFormApprovedDtoMapper;
    @Resource
    private AuditLogUtil auditLogUtil;
    private final static String MODEL = xdoi18n.XdoI18nUtil.t("外发加工-外发加工申请表(审核通过备份表)");

    @Override
    public Mapper<WfDeclareFormApproved> getMapper() {
        return wfDeclareFormApprovedMapper;
    }

    /**
     * 获取分页信息
     *
     * @param wfDeclareFormApprovedParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<WfDeclareFormApprovedDto>> getListPaged(WfDeclareFormApprovedParam wfDeclareFormApprovedParam, PageParam pageParam) {
        // 启用分页查询
        WfDeclareFormApproved wfDeclareFormApproved = wfDeclareFormApprovedDtoMapper.toPo(wfDeclareFormApprovedParam);
        Page<WfDeclareFormApproved> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> wfDeclareFormApprovedMapper.getList(wfDeclareFormApproved));
        List<WfDeclareFormApprovedDto> wfDeclareFormApprovedDtos = page.getResult().stream().map(head -> {
            WfDeclareFormApprovedDto dto = wfDeclareFormApprovedDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<WfDeclareFormApprovedDto>> paged = ResultObject.createInstance(wfDeclareFormApprovedDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param wfDeclareFormApprovedParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WfDeclareFormApprovedDto insert(WfDeclareFormApprovedParam wfDeclareFormApprovedParam, UserInfoToken userInfo) {
        WfDeclareFormApproved wfDeclareFormApproved = wfDeclareFormApprovedDtoMapper.toPo(wfDeclareFormApprovedParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        wfDeclareFormApproved.setSid(sid);
        wfDeclareFormApproved.setInsertUser(userInfo.getUserNo());
        wfDeclareFormApproved.setInsertTime(new Date());
        wfDeclareFormApproved.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = wfDeclareFormApprovedMapper.insert(wfDeclareFormApproved);
        return insertStatus > 0 ? wfDeclareFormApprovedDtoMapper.toDto(wfDeclareFormApproved) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param wfDeclareFormApprovedParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WfDeclareFormApprovedDto update(WfDeclareFormApprovedParam wfDeclareFormApprovedParam, UserInfoToken userInfo) {
        WfDeclareFormApproved wfDeclareFormApproved = wfDeclareFormApprovedMapper.selectByPrimaryKey(wfDeclareFormApprovedParam.getSid());
        wfDeclareFormApprovedDtoMapper.updatePo(wfDeclareFormApprovedParam, wfDeclareFormApproved);
        wfDeclareFormApproved.setUpdateUser(userInfo.getUserNo());
        wfDeclareFormApproved.setUpdateTime(new Date());
        // 更新数据
        int update = wfDeclareFormApprovedMapper.updateByPrimaryKey(wfDeclareFormApproved);
        return update > 0 ? wfDeclareFormApprovedDtoMapper.toDto(wfDeclareFormApproved) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        wfDeclareFormApprovedMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<WfDeclareFormApprovedDto> selectAll(WfDeclareFormApprovedParam exportParam, UserInfoToken userInfo) {
        WfDeclareFormApproved wfDeclareFormApproved = wfDeclareFormApprovedDtoMapper.toPo(exportParam);
        // wfDeclareFormApproved.setTradeCode(userInfo.getCompany());
        List<WfDeclareFormApprovedDto> wfDeclareFormApprovedDtos = new ArrayList<>();
        List<WfDeclareFormApproved> wfDeclareFormApproveds = wfDeclareFormApprovedMapper.getList(wfDeclareFormApproved);
        if (CollectionUtils.isNotEmpty(wfDeclareFormApproveds)) {
            wfDeclareFormApprovedDtos = wfDeclareFormApproveds.stream().map(head -> {
                WfDeclareFormApprovedDto dto = wfDeclareFormApprovedDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return wfDeclareFormApprovedDtos;
    }

    /**
     * (申报审批通过后)备份
     *
     * @param sid
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WfDeclareFormApprovedDto backups(String sid, UserInfoToken userInfo) {
        WfDeclareForm wfDeclareForm = wfDeclareFormMapper.selectByPrimaryKey(sid);
        if (wfDeclareForm != null) {
            // 先删除
            Example example = new Example(WfDeclareFormApproved.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("sid", sid);
            criteria.andEqualTo("tradeCode", userInfo.getCompany());
            int deleteStatus = wfDeclareFormApprovedMapper.deleteByExample(example);
            // 再新增
            WfDeclareFormApproved wfDeclareFormApproved = new WfDeclareFormApproved();
//            BeanUtils.copyProperties(wfDeclareForm, wfDeclareFormApproved);
            wfDeclareFormApproved = wfDeclareFormApprovedDtoMapper.toMo(wfDeclareForm);
            wfDeclareFormApproved.setAppStatus(ConstantsStatus.STATUS_1);
            int insertStatus = wfDeclareFormApprovedMapper.insert(wfDeclareFormApproved);
            if (deleteStatus > 0) {
                auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(), MODEL, userInfo, wfDeclareFormApproved);
            } else {
                auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(), MODEL, userInfo, wfDeclareFormApproved);
            }
            return insertStatus > 0 ? wfDeclareFormApprovedDtoMapper.toDto(wfDeclareFormApproved) : null;
        } else {
            return null;
        }
    }
}
