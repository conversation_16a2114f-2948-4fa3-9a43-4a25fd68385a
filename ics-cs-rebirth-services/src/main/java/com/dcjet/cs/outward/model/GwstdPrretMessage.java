package com.dcjet.cs.outward.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Table(name = "T_GWSTD_PRRET_MESSAGE")
public class GwstdPrretMessage extends BasicModel implements Serializable {

    /**
     * 企业内部编号
     */
    @Column(name = "cop_ems_no")
    private String copEmsNo;

    /**
     * 申报表编号
     */
    @Column(name = "gcd_no")
    private String gcdNo;

    /**
     * 预录入统一编号
     */
    @Column(name = "pre_seq_no")
    private String preSeqNo;

    /**
     * 审批结果
     */
    @Column(name = "manage_result")
    private String manageResult;

    /**
     * 审批时间
     */
    @Column(name = "manage_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date manageDate;

    /**
     * 状态
     */
    @Column(name = "status")
    private String status;
}
