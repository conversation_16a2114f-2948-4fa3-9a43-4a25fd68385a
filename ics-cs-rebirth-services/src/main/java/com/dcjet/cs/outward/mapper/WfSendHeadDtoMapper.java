package com.dcjet.cs.outward.mapper;

import com.dcjet.cs.dto.outward.WfSendHeadDto;
import com.dcjet.cs.dto.outward.WfSendHeadParam;
import com.dcjet.cs.outward.model.WfSendHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-7-18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WfSendHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    WfSendHeadDto toDto(WfSendHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    WfSendHead toPo(WfSendHeadParam param);
    /**
     * 数据库原始数据更新
     * @param wfSendHeadParam
     * @param wfSendHead
     */
    void updatePo(WfSendHeadParam wfSendHeadParam, @MappingTarget WfSendHead wfSendHead);
    default void patchPo(WfSendHeadParam wfSendHeadParam, WfSendHead wfSendHead) {
        // TODO 自行实现局部更新
    }
}
