package com.dcjet.cs.outward.service;

import com.dcjet.cs.dto.outward.WfReceiveListDto;
import com.dcjet.cs.dto.outward.WfReceiveListParam;
import com.dcjet.cs.outward.dao.WfReceiveListMapper;
import com.dcjet.cs.outward.mapper.WfReceiveListDtoMapper;
import com.dcjet.cs.outward.model.WfReceiveHead;
import com.dcjet.cs.outward.model.WfReceiveList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-7-18
 */
@Service
public class WfReceiveListService extends BaseService<WfReceiveList> {
    @Resource
    private WfReceiveListMapper wfReceiveListMapper;
    @Resource
    private WfReceiveListDtoMapper wfReceiveListDtoMapper;
    @Override
    public Mapper<WfReceiveList> getMapper() {
        return wfReceiveListMapper;
    }
    @Resource
    private AuditLogUtil auditLogUtil;
    private final static String MODEL = xdoi18n.XdoI18nUtil.t("外发加工-外发加工收货单表体");
    /**
     * 功能描述: grid分页查询
     *
     * @param wfReceiveListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<WfReceiveListDto>> selectAllPaged(WfReceiveListParam wfReceiveListParam, PageParam pageParam) {
        // 启用分页查询
        WfReceiveList wfReceiveList = wfReceiveListDtoMapper.toPo(wfReceiveListParam);
        Page<WfReceiveList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> wfReceiveListMapper.getList(wfReceiveList));
        List<WfReceiveListDto> wfReceiveListDtos = page.getResult().stream().map(head -> {
            WfReceiveListDto dto = wfReceiveListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		 ResultObject<List<WfReceiveListDto>> paged = ResultObject.createInstance(wfReceiveListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @return
     */
    public List<WfReceiveListDto> selectAll(WfReceiveListParam exportParam) {
        WfReceiveList wfReceiveList = wfReceiveListDtoMapper.toPo(exportParam);
        List<WfReceiveListDto> wfReceiveListDtos = new ArrayList<>();
        List<WfReceiveList> wfReceiveLists = wfReceiveListMapper.getList(wfReceiveList);
        if (CollectionUtils.isNotEmpty(wfReceiveLists)) {
            wfReceiveListDtos = wfReceiveLists.stream().map(item -> {
                WfReceiveListDto dto = wfReceiveListDtoMapper.toDto(item);
                return dto;
            }).collect(Collectors.toList());
        }
        return wfReceiveListDtos;
    }
    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public WfReceiveListDto insert(WfReceiveListParam model, UserInfoToken userInfo) {
        String sid = UUID.randomUUID().toString();
        WfReceiveList wfReceiveList = wfReceiveListDtoMapper.toPo(model);
        wfReceiveList.setSid(sid);
        wfReceiveList.setInsertUser(userInfo.getUserNo());
        wfReceiveList.setInsertUserName(userInfo.getUserName());
        wfReceiveList.setInsertTime(new Date());
        wfReceiveList.setTradeCode(userInfo.getCompany());
        Integer maxSerialNo = wfReceiveListMapper.getMaxSerialNo(wfReceiveList.getHeadId());
        if(maxSerialNo == null || maxSerialNo == 0) {
            maxSerialNo = 1;
        }else {
            maxSerialNo++;
        }
        wfReceiveList.setSerialNo(maxSerialNo);
        int insertStatus = wfReceiveListMapper.insert(wfReceiveList);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(),MODEL,userInfo,wfReceiveList);
       return insertStatus > 0 ? wfReceiveListDtoMapper.toDto(wfReceiveList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param wfReceiveListParam
     * @param userInfo
     * @return
     */
    public WfReceiveListDto update(WfReceiveListParam wfReceiveListParam, UserInfoToken userInfo) {
        WfReceiveList wfReceiveList = wfReceiveListMapper.selectByPrimaryKey(wfReceiveListParam.getSid());
        wfReceiveListDtoMapper.updatePo(wfReceiveListParam, wfReceiveList);
        wfReceiveList.setUpdateUser(userInfo.getUserNo());
        wfReceiveList.setUpdateTime(new Date());
        // 更新数据
        int update = wfReceiveListMapper.updateByPrimaryKey(wfReceiveList);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(),MODEL,userInfo,wfReceiveList);
        return update > 0 ? wfReceiveListDtoMapper.toDto(wfReceiveList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids, UserInfoToken userInfo) {
        Example exampleList = new Example(WfReceiveList.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andIn("sid", sids);
        List<WfReceiveList> wfReceiveListList = wfReceiveListMapper.selectByExample(exampleList);
        wfReceiveListMapper.deleteBySids(sids);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.DELETE.getValue(),MODEL,userInfo,wfReceiveListList);
    }
	public int getListNumByHeadIds(List<String> sids) {
        return wfReceiveListMapper.getListNumByHeadIds(sids);
    }
}
