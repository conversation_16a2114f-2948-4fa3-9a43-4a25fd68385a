package com.dcjet.cs.outward.mapper;

import com.dcjet.cs.dto.outward.*;
import com.dcjet.cs.outward.model.WfDeclareForm;
import com.dcjet.cs.outward.model.WfDeclareFormApproved;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2023-5-23
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WfDeclareFormApprovedDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    WfDeclareFormApprovedDto toDto(WfDeclareFormApproved po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    WfDeclareFormApproved toPo(WfDeclareFormApprovedParam param);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    WfDeclareFormApproved toMo(WfDeclareForm param);
//    WfDeclareForm toMo(WfDeclareFormApproved param);

    /**
     * 数据库原始数据更新
     *
     * @param wfDeclareFormApprovedParam
     * @param wfDeclareFormApproved
     */
    void updatePo(WfDeclareFormApprovedParam wfDeclareFormApprovedParam, @MappingTarget WfDeclareFormApproved wfDeclareFormApproved);

    default void patchPo(WfDeclareFormApprovedParam wfDeclareFormApprovedParam, WfDeclareFormApproved wfDeclareFormApproved) {
        // TODO 自行实现局部更新
    }
}
