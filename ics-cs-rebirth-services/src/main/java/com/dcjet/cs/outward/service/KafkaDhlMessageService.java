package com.dcjet.cs.outward.service;

import com.dcjet.cs.common.service.FastdFsService;
import com.dcjet.cs.dto.war.WarringPassConfigDto;
import com.dcjet.cs.dto.war.WarringPassExpandConfigDto;
import com.dcjet.cs.entry.dao.DecEEntryHeadMapper;
import com.dcjet.cs.entry.dao.DecIEntryHeadMapper;
import com.dcjet.cs.entry.model.DecEEntryHead;
import com.dcjet.cs.entry.model.DecIEntryHead;
import com.dcjet.cs.entry.service.DecEEntryHeadService;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.mat.model.Attached;
import com.dcjet.cs.mqsync.core.service.BaseMqSyncService;
import com.dcjet.cs.outward.model.Documents;
import com.dcjet.cs.outward.model.KafkaDhlMessage;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.dcjet.cs.war.service.WarringPassExpandConfigService;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.file.XdoFileHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * generated by Generate dcits
 * 请求api返回信息 Service;
 *
 * @author: dces
 * @date: 2019-04-24
 */
@Service
@Slf4j
public class KafkaDhlMessageService extends BaseMqSyncService<KafkaDhlMessage> {
    @Resource
    private WarringPassConfigService warringPassConfigService;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    DecEEntryHeadService decEEntryHeadService;
    @Resource
    DecEEntryHeadMapper decEEntryHeadMapper;
    @Resource
    private WarringPassExpandConfigService warringPassExpandConfigService;
    @Resource
    private FastdFsService fastdFsService;
    @Resource
    private AttachedMapper attachedMapper;

    /**
     * 文件服务器客户端
     */
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Override
    public void consume(String message) {
        try {
            KafkaDhlMessage kafkaDhlMessage = JsonObjectMapper.getInstance().fromJson(message, KafkaDhlMessage.class);
            if (kafkaDhlMessage == null) {
                log.info(String.format("Json序列化失败：%s", kafkaDhlMessage));
            } else {
                boolean waybillChannel = true;
                UserInfoToken userInfo = new UserInfoToken<>();
                userInfo.setCompany(kafkaDhlMessage.getTradeCode());

                boolean expressPlacementAttachment = false;
                List<WarringPassExpandConfigDto> warringPassExpandConfigDtos = new ArrayList<>();
                List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(userInfo);
                if (warringPassConfigDtos != null) {
                    if (warringPassConfigDtos.size() > 0) {
                        WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);


                        warringPassExpandConfigDtos = warringPassExpandConfigService.selectAll(warringPassConfigDto.getSid(),userInfo);
                        if (warringPassExpandConfigDtos.size()>0){
                            WarringPassExpandConfigDto warringPassExpandConfigDto = warringPassExpandConfigDtos.get(0);
                            if (StringUtils.isNotBlank(warringPassExpandConfigDto.getExpressPlacementAttachment())&&StringUtils.equals(warringPassExpandConfigDto.getExpressPlacementAttachment(), CommonVariable.EPA_STATUS_1)){
                                expressPlacementAttachment = true;
                            }
                            if (StringUtils.equals(warringPassExpandConfigDto.getWaybillChannel(),ConstantsStatus.STATUS_1)){
                                waybillChannel = true;
                            }else {
                                waybillChannel = false;
                                log.info(String.format("运维配置项未配置DHL下单功能，企业内部编号：%s", kafkaDhlMessage.getInternalNumber()));
                            }
                        }
                    }
                }
                if (waybillChannel) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

                    Example example = new Example(DecErpEHeadN.class);
                    Example.Criteria criteria = example.createCriteria();
                    criteria.andEqualTo("emsListNo", kafkaDhlMessage.getInternalNumber());
                    criteria.andEqualTo("tradeCode", kafkaDhlMessage.getTradeCode());

                    DecErpEHeadN decErpEHeadN = decErpEHeadNMapper.selectOneByExample(example);
                    if (decErpEHeadN != null) {
                        if(StringUtils.isNotBlank(kafkaDhlMessage.getWaybillNumber())) {
                            if (StringUtils.equals(decErpEHeadN.getExpressChannel(), ConstantsStatus.STATUS_1)) {
                                decErpEHeadN.setHawb(kafkaDhlMessage.getWaybillNumber());

                                Date hawbCreateTime = null;
                                // 提运单生成时间
                                if(StringUtils.isNotBlank(kafkaDhlMessage.getWaybillCreateDate())){
                                    hawbCreateTime = sdf.parse(kafkaDhlMessage.getWaybillCreateDate());
                                }
                                decErpEHeadN.setHawbCreateTime(hawbCreateTime);
                                decErpEHeadNMapper.updateByPrimaryKey(decErpEHeadN);

                                //返填草单
                                List<DecEEntryHead> decEEntryHeads = decEEntryHeadService.selectByErpHeadId(kafkaDhlMessage.getTradeCode(), decErpEHeadN.getSid());
                                if (decEEntryHeads != null) {
                                    for (DecEEntryHead item : decEEntryHeads) {
                                        item.setHawb(kafkaDhlMessage.getWaybillNumber());
                                        // 提运单生成时间
                                        item.setHawbCreateTime(hawbCreateTime);
                                        decEEntryHeadMapper.updateByPrimaryKey(item);
                                    }
                                }
                                log.info(" -----------DHL下单数据回填成功:" + kafkaDhlMessage.getInternalNumber() + "-------------");
                            }
                        } else {
                            log.info(" -----------DHL下单功能查询表头数据为空,企业内部编号:"+kafkaDhlMessage.getInternalNumber()+"，返回的提运单号为空，不返填-------------");
                        }
                    }else {
                        log.info(" -----------DHL下单功能查询表头数据为空,企业内部编号:"+kafkaDhlMessage.getInternalNumber()+"-------------");
                    }

                    if (expressPlacementAttachment) {
                        if (CollectionUtils.isNotEmpty(kafkaDhlMessage.getDocuments())) {
                            log.info(" -----------DHL下单功能附件插入开始,企业内部编号:"+kafkaDhlMessage.getInternalNumber()+"-------------");
                            for (Documents documents : kafkaDhlMessage.getDocuments()) {
                                Attached att = new Attached();
                                att.setBusinessSid(decErpEHeadN.getSid());
                                att.setOriginFileName(documents.getFileName());
                                att.setTradeCode(decErpEHeadN.getTradeCode());
                                List<Attached> attachedList = attachedMapper.getList(att);
                                if (CollectionUtils.isNotEmpty(attachedList)) {
                                    log.info(" -----------DHL下单功能查询附件不为空,企业内部编号:"+kafkaDhlMessage.getInternalNumber()+"-------------");
                                    return;
                                }else {
                                    log.info(" -----------DHL下单功能附件插入开始,企业内部编号:"+kafkaDhlMessage.getInternalNumber()+"-------------");
                                    Attached attached = new Attached();
                                    byte[] bytes = fastdFsService.getByteFromFastDFS(documents.getContent());
                                    String fdfsId = fileHandler.uploadFile(bytes);
                                    attached.setFdfsId(fdfsId);
                                    attached.setFileName(fdfsId);
                                    attached.setSid(UUID.randomUUID().toString());
                                    if (StringUtils.isNotBlank(documents.getTypeCode())) {
                                        if (StringUtils.equals(ConstantsStatus.STATUS_1, documents.getTypeCode())) {
                                            attached.setAcmpType("invoiceNo");
                                        } else {
                                            attached.setAcmpType("other");
                                        }
                                    }
                                    attached.setBusinessType("EM");
                                    attached.setFileSize(documents.getFileSize());
                                    attached.setOriginFileName(documents.getFileName());
                                    attached.setBusinessSid(decErpEHeadN.getSid());
                                    attached.setTradeCode(decErpEHeadN.getTradeCode());
                                    attached.setInsertTime(new Date());
                                    attached.setInsertUser("kafka");
                                    attached.setDataSource(ConstantsStatus.STATUS_0);
                                    attachedMapper.insert(attached);
                                }
                            }
                            log.info(" -----------DHL下单功能附件插入结束,企业内部编号:"+kafkaDhlMessage.getInternalNumber()+"-------------");
                        }
                    }

                }
            }
        } catch (Exception ex) {
            log.error("KAFKA接口获取外发申请下发状态更新失败", ex);
            throw new RuntimeException(ex);
        }
    }
}

