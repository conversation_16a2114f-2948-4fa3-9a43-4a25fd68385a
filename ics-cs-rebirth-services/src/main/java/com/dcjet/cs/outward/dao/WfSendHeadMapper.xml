<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.outward.dao.WfSendHeadMapper">
    <resultMap id="wfSendHeadResultMap" type="com.dcjet.cs.outward.model.WfSendHead">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="BILL_NO" property="billNo" jdbcType="VARCHAR" />
		<result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
		<result column="SHIP_DATE" property="shipDate" jdbcType="DATE" />
		<result column="EMS_COP_NO" property="emsCopNo" jdbcType="VARCHAR" />
		<result column="APPLY_NO" property="applyNo" jdbcType="VARCHAR" />
		<result column="DECLARE_DATE" property="declareDate" jdbcType="DATE" />
		<result column="CONTRACTOR_TRADE_CODE" property="contractorTradeCode" jdbcType="VARCHAR" />
		<result column="CONTRACTOR_CREDIT_CODE" property="contractorCreditCode" jdbcType="VARCHAR" />
		<result column="CONTRACTOR_TRADE_NAME" property="contractorTradeName" jdbcType="VARCHAR" />
		<result column="CONTRACTOR_MASTER_CUSTOMS" property="contractorMasterCustoms" jdbcType="VARCHAR" />
		<result column="ENTRUST_EMS_NO" property="entrustEmsNo" jdbcType="VARCHAR" />
		<result column="ENTRUST_MASTER_CUSTOMS" property="entrustMasterCustoms" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="ENTRUST_CREDIT_CODE" property="entrustCreditCode" jdbcType="VARCHAR" />
		<result column="ENTRUST_TRADE_NAME" property="entrustTradeName" jdbcType="VARCHAR" />
		<result column="SHIPPER" property="shipper" jdbcType="VARCHAR" />
		<result column="DECLARE_TRADE_CODE" property="declareTradeCode" jdbcType="VARCHAR" />
		<result column="DECLARE_TRADE_NAME" property="declareTradeName" jdbcType="VARCHAR" />
		<result column="DECLARE_CREDIT_CODE" property="declareCreditCode" jdbcType="VARCHAR" />
		<result column="REMARK" property="remark" jdbcType="VARCHAR" />
		<result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
		<result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR" />
        <result column="HEAD_ID" property="headId" jdbcType="VARCHAR" />
        <result column="CONTRACTOR_COMPANY_CODE" property="contractorCompanyCode" jdbcType="VARCHAR" />
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,BILL_NO
     ,CREATE_DATE
     ,SHIP_DATE
     ,EMS_COP_NO
     ,APPLY_NO
     ,DECLARE_DATE
     ,CONTRACTOR_TRADE_CODE
     ,CONTRACTOR_CREDIT_CODE
     ,CONTRACTOR_TRADE_NAME
     ,CONTRACTOR_MASTER_CUSTOMS
     ,ENTRUST_EMS_NO
     ,ENTRUST_MASTER_CUSTOMS
     ,TRADE_CODE
     ,ENTRUST_CREDIT_CODE
     ,ENTRUST_TRADE_NAME
     ,SHIPPER
     ,DECLARE_TRADE_CODE
     ,DECLARE_TRADE_NAME
     ,DECLARE_CREDIT_CODE
     ,REMARK
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,DATA_SOURCE
     ,HEAD_ID
     ,CONTRACTOR_COMPANY_CODE
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
    </sql>
    <sql id="condition">
    <if test="billNo != null and billNo != ''"> 
		and BILL_NO like concat(concat('%', #{billNo}),'%')
	</if>
    <if test="createDateFrom != null and createDateFrom != ''">
       	<![CDATA[ and CREATE_DATE >= to_date(#{createDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
    </if>
    <if test="createDateTo != null and createDateTo != ''">
        <![CDATA[ and CREATE_DATE <= to_date(#{createDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
    </if>
    <if test="shipDateFrom != null and shipDateFrom != ''">
       	<![CDATA[ and SHIP_DATE >= to_date(#{shipDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
    </if>
    <if test="shipDateTo != null and shipDateTo != ''">
        <![CDATA[ and SHIP_DATE <= to_date(#{shipDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
    </if>
    <if test="emsCopNo != null and emsCopNo != ''"> 
		and EMS_COP_NO like concat(concat('%', #{emsCopNo}),'%')
	</if>
    <if test="applyNo != null and applyNo != ''"> 
		and APPLY_NO = #{applyNo}
	</if>
    <if test="declareDateFrom != null and declareDateFrom != ''">
       	<![CDATA[ and DECLARE_DATE >= to_date(#{declareDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
    </if>
    <if test="declareDateTo != null and declareDateTo != ''">
        <![CDATA[ and DECLARE_DATE <= to_date(#{declareDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
    </if>
    <if test="contractorTradeCode != null and contractorTradeCode != ''"> 
		and CONTRACTOR_TRADE_CODE = #{contractorTradeCode}
	</if>
    <if test="contractorCreditCode != null and contractorCreditCode != ''"> 
		and CONTRACTOR_CREDIT_CODE = #{contractorCreditCode}
	</if>
    <if test="contractorTradeName != null and contractorTradeName != ''"> 
		and CONTRACTOR_TRADE_NAME like concat(concat('%', #{contractorTradeName}),'%')
	</if>
    <if test="contractorMasterCustoms != null and contractorMasterCustoms != ''"> 
		and CONTRACTOR_MASTER_CUSTOMS = #{contractorMasterCustoms}
	</if>
    <if test="entrustEmsNo != null and entrustEmsNo != ''"> 
		and ENTRUST_EMS_NO = #{entrustEmsNo}
	</if>
    <if test="entrustMasterCustoms != null and entrustMasterCustoms != ''"> 
		and ENTRUST_MASTER_CUSTOMS = #{entrustMasterCustoms}
	</if>
		and TRADE_CODE = #{tradeCode}
    <if test="entrustCreditCode != null and entrustCreditCode != ''">
		and ENTRUST_CREDIT_CODE = #{entrustCreditCode}
	</if>
    <if test="entrustTradeName != null and entrustTradeName != ''"> 
		and ENTRUST_TRADE_NAME = #{entrustTradeName}
	</if>
    <if test="shipper != null and shipper != ''"> 
		and SHIPPER = #{shipper}
	</if>
    <if test="declareTradeCode != null and declareTradeCode != ''"> 
		and DECLARE_TRADE_CODE = #{declareTradeCode}
	</if>
    <if test="declareTradeName != null and declareTradeName != ''"> 
		and DECLARE_TRADE_NAME = #{declareTradeName}
	</if>
    <if test="declareCreditCode != null and declareCreditCode != ''"> 
		and DECLARE_CREDIT_CODE = #{declareCreditCode}
	</if>
    <if test="remark != null and remark != ''"> 
		and REMARK = #{remark}
	</if>
    <if test="dataSource != null and dataSource != ''"> 
		and DATA_SOURCE = #{dataSource}
	</if>
    <if test="headId != null and headId != ''">
        and HEAD_ID = #{headId}
    </if>
    <if test="contractorCompanyCode != null and contractorCompanyCode != ''">
        and CONTRACTOR_COMPANY_CODE = #{contractorCompanyCode}
    </if>
    </sql>    
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="wfSendHeadResultMap" parameterType="com.dcjet.cs.outward.model.WfSendHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_WF_SEND_HEAD t
        <where>
            <include refid="condition"></include>
        </where>
        ORDER BY t.CREATE_DATE desc,t.BILL_NO, t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_WF_SEND_HEAD t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="checkBillNo" parameterType="java.util.Map" resultMap="wfSendHeadResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_WF_SEND_HEAD t
        <where>
            1=1
            <if test="billNo != null and billNo != ''">
                and BILL_NO = #{billNo}
            </if>
            <if test="tradeCode != null and tradeCode != ''">
                and TRADE_CODE = #{tradeCode}
            </if>
            <if test="sid != null and sid != '' and sid != 'undefined'">
                and SID != #{sid}
            </if>
            and ROWNUM = 1
        </where>
    </select>
    <select id="checkBillNo" parameterType="java.util.Map" resultMap="wfSendHeadResultMap" databaseId="postgresql">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_WF_SEND_HEAD t
        <where>
            1=1
            <if test="billNo != null and billNo != ''">
                and BILL_NO = #{billNo}
            </if>
            <if test="tradeCode != null and tradeCode != ''">
                and TRADE_CODE = #{tradeCode}
            </if>
            <if test="sid != null and sid != '' and sid != 'undefined'">
                and SID != #{sid}
            </if>
            limit 1
        </where>
    </select>
    <update id="updateByHeadId" parameterType="com.dcjet.cs.outward.model.WfSendHead">
        UPDATE T_WF_SEND_HEAD
        <set>
            <if test="emsCopNo != null and emsCopNo != ''">EMS_COP_NO = #{emsCopNo},</if>
            <if test="applyNo != null and applyNo != ''">APPLY_NO = #{applyNo},</if>
            <if test="declareDate != null">DECLARE_DATE = #{declareDate},</if>
            <if test="contractorTradeCode != null and contractorTradeCode != ''">CONTRACTOR_TRADE_CODE = #{contractorTradeCode},</if>
            <if test="contractorCreditCode != null and contractorCreditCode != ''">CONTRACTOR_CREDIT_CODE = #{contractorCreditCode},</if>
            <if test="contractorTradeName != null and contractorTradeName != ''">CONTRACTOR_TRADE_NAME = #{contractorTradeName},</if>
            <if test="contractorMasterCustoms != null and contractorMasterCustoms != ''">CONTRACTOR_MASTER_CUSTOMS = #{contractorMasterCustoms},</if>
            <if test="entrustEmsNo != null and entrustEmsNo != ''">ENTRUST_EMS_NO = #{entrustEmsNo},</if>
            <if test="tradeCode != null and tradeCode != ''">TRADE_CODE = #{tradeCode},</if>
            <if test="entrustCreditCode != null and entrustCreditCode != ''">ENTRUST_CREDIT_CODE = #{entrustCreditCode},</if>
            <if test="entrustTradeName != null and entrustTradeName != ''">ENTRUST_TRADE_NAME = #{entrustTradeName},</if>
            <if test="entrustMasterCustoms != null and entrustMasterCustoms != ''">ENTRUST_MASTER_CUSTOMS = #{entrustMasterCustoms},</if>
            <if test="declareTradeCode != null and declareTradeCode != ''">DECLARE_TRADE_CODE = #{declareTradeCode},</if>
            <if test="declareTradeName != null and declareTradeName != ''">DECLARE_TRADE_NAME = #{declareTradeName},</if>
            <if test="declareCreditCode != null and declareCreditCode != ''">DECLARE_CREDIT_CODE = #{declareCreditCode},</if>
            <if test="contractorCompanyCode != null and contractorCompanyCode != ''">CONTRACTOR_COMPANY_CODE = #{contractorCompanyCode},</if>
        </set>
        <where>
            HEAD_ID = #{headId}
        </where>
    </update>
</mapper>
