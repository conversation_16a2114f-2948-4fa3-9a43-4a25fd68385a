package com.dcjet.cs.outward.service;

import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.outward.*;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.outward.dao.GwstdPrretMessageMapper;
import com.dcjet.cs.outward.mapper.GwstdPrretMessageDtoMapper;
import com.dcjet.cs.outward.model.GwstdPrretMessage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2023-5-30
 */
@Service
public class GwstdPrretMessageService extends BaseService<GwstdPrretMessage> {
    @Resource
    private GwstdPrretMessageMapper gwstdPrretMessageMapper;

    @Resource
    private GwstdPrretMessageDtoMapper gwstdPrretMessageDtoMapper;

    @Override
    public Mapper<GwstdPrretMessage> getMapper() {
        return gwstdPrretMessageMapper;
    }

    /**
     * 获取分页信息
     *
     * @param gwstdPrretMessageParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwstdPrretMessageDto>> getListPaged(GwstdPrretMessageParam gwstdPrretMessageParam, PageParam pageParam) {
        // 启用分页查询
        GwstdPrretMessage gwstdPrretMessage = gwstdPrretMessageDtoMapper.toPo(gwstdPrretMessageParam);
        Page<GwstdPrretMessage> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdPrretMessageMapper.getList(gwstdPrretMessage));
        List<GwstdPrretMessageDto> gwstdPrretMessageDtos = page.getResult().stream().map(head -> {
            GwstdPrretMessageDto dto = gwstdPrretMessageDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdPrretMessageDto>> paged = ResultObject.createInstance(gwstdPrretMessageDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
}
