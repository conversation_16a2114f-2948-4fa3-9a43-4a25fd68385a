package com.dcjet.cs.outward.service;

import com.dcjet.cs.dto.outward.WfSendListDto;
import com.dcjet.cs.dto.outward.WfSendListParam;
import com.dcjet.cs.outward.dao.WfSendListMapper;
import com.dcjet.cs.outward.mapper.WfSendListDtoMapper;
import com.dcjet.cs.outward.model.WfSendList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-7-18
 */
@Service
public class WfSendListService extends BaseService<WfSendList> {
    @Resource
    private WfSendListMapper wfSendListMapper;
    @Resource
    private WfSendListDtoMapper wfSendListDtoMapper;
    @Override
    public Mapper<WfSendList> getMapper() {
        return wfSendListMapper;
    }
    @Resource
    private AuditLogUtil auditLogUtil;
    private final static String MODEL = xdoi18n.XdoI18nUtil.t("外发加工-外发加工发货单表体");
    /**
     * 功能描述: grid分页查询
     *
     * @param wfSendListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<WfSendListDto>> selectAllPaged(WfSendListParam wfSendListParam, PageParam pageParam) {
        // 启用分页查询
        WfSendList wfSendList = wfSendListDtoMapper.toPo(wfSendListParam);
        Page<WfSendList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> wfSendListMapper.getList(wfSendList));
        List<WfSendListDto> wfSendListDtos = page.getResult().stream().map(head -> {
            WfSendListDto dto = wfSendListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		 ResultObject<List<WfSendListDto>> paged = ResultObject.createInstance(wfSendListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @return
     */
    public List<WfSendListDto> selectAll(WfSendListParam exportParam) {
        WfSendList wfSendList = wfSendListDtoMapper.toPo(exportParam);
        List<WfSendListDto> wfSendListDtos = new ArrayList<>();
        List<WfSendList> wfSendLists = wfSendListMapper.getList(wfSendList);
        if (CollectionUtils.isNotEmpty(wfSendLists)) {
            wfSendListDtos = wfSendLists.stream().map(item -> {
                WfSendListDto dto = wfSendListDtoMapper.toDto(item);
                return dto;
            }).collect(Collectors.toList());
        }
        return wfSendListDtos;
    }
    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public WfSendListDto insert(WfSendListParam model, UserInfoToken userInfo) {
        String sid = UUID.randomUUID().toString();
        WfSendList wfSendList = wfSendListDtoMapper.toPo(model);
        wfSendList.setSid(sid);
        wfSendList.setInsertUser(userInfo.getUserNo());
        wfSendList.setInsertUserName(userInfo.getUserName());
        wfSendList.setInsertTime(new Date());
        wfSendList.setTradeCode(userInfo.getCompany());
        Integer maxSerialNo = wfSendListMapper.getMaxSerialNo(wfSendList.getHeadId());
        if(maxSerialNo == null || maxSerialNo == 0) {
            maxSerialNo = 1;
        }else {
            maxSerialNo++;
        }
        wfSendList.setSerialNo(maxSerialNo);
        int insertStatus = wfSendListMapper.insert(wfSendList);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(),MODEL,userInfo,wfSendList);
        return insertStatus > 0 ? wfSendListDtoMapper.toDto(wfSendList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param wfSendListParam
     * @param userInfo
     * @return
     */
    public WfSendListDto update(WfSendListParam wfSendListParam, UserInfoToken userInfo) {
        WfSendList wfSendList = wfSendListMapper.selectByPrimaryKey(wfSendListParam.getSid());
        wfSendListDtoMapper.updatePo(wfSendListParam, wfSendList);
        wfSendList.setUpdateUser(userInfo.getUserNo());
        wfSendList.setUpdateTime(new Date());
        // 更新数据
        int update = wfSendListMapper.updateByPrimaryKey(wfSendList);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(),MODEL,userInfo,wfSendList);
        return update > 0 ? wfSendListDtoMapper.toDto(wfSendList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids, UserInfoToken userInfo) {
        Example exampleList = new Example(WfSendList.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andIn("sid", sids);
        List<WfSendList> wfSendListList = wfSendListMapper.selectByExample(exampleList);
        wfSendListMapper.deleteBySids(sids);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.DELETE.getValue(),MODEL,userInfo,wfSendListList);
    }
	public int getListNumByHeadIds(List<String> sids) {
        return wfSendListMapper.getListNumByHeadIds(sids);
    }
}
