package com.dcjet.cs.dev.mapper;

import com.dcjet.cs.dev.model.DevEntryRecord;
import com.dcjet.cs.dto.dev.DevEntryRecordDto;
import com.dcjet.cs.dto.dev.DevEntryRecordParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-10-8
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DevEntryRecordDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DevEntryRecordDto toDto(DevEntryRecord po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DevEntryRecord toPo(DevEntryRecordParam param);
    /**
     * 数据库原始数据更新
     * @param devEntryRecordParam
     * @param devEntryRecord
     */
    void updatePo(DevEntryRecordParam devEntryRecordParam, @MappingTarget DevEntryRecord devEntryRecord);
    default void patchPo(DevEntryRecordParam devEntryRecordParam, DevEntryRecord devEntryRecord) {
        // TODO 自行实现局部更新
    }
}
