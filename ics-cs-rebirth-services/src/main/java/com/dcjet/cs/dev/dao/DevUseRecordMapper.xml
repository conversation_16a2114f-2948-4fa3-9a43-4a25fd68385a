<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dev.dao.DevUseRecordMapper">
    <resultMap id="devUseRecordResultMap" type="com.dcjet.cs.dev.model.DevUseRecord">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="CAPITAL_NO" property="capitalNo" jdbcType="VARCHAR"/>
        <result column="USE_ADDRESS" property="useAddress" jdbcType="VARCHAR"/>
        <result column="USE_DATE" property="useDate" jdbcType="TIMESTAMP"/>
        <result column="USE_STATUS" property="useStatus" jdbcType="VARCHAR"/>
        <result column="TRADE_STATUS" property="tradeStatus" jdbcType="VARCHAR"/>
        <result column="DEPOSITARY_OFFICER" property="depositaryOfficer" jdbcType="VARCHAR"/>
        <result column="EMS_LIST_NO" property="emsListNo" jdbcType="VARCHAR"/>
        <result column="ENTRY_NO" property="entryNo" jdbcType="VARCHAR"/>
        <result column="DECLARE_DATE" property="declareDate" jdbcType="TIMESTAMP"/>
        <result column="TRADE_MODE" property="tradeMode" jdbcType="VARCHAR"/>
        <result column="G_NAME" property="GName" jdbcType="VARCHAR"/>
        <result column="G_MODEL" property="GModel" jdbcType="VARCHAR"/>
        <result column="CODE_T_S" property="codeTS" jdbcType="VARCHAR"/>
        <result column="UNIT" property="unit" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="NUMERIC"/>
        <result column="DEC_TOTAL" property="decTotal" jdbcType="NUMERIC"/>
        <result column="CURR" property="curr" jdbcType="VARCHAR"/>
        <result column="PASS_DATE" property="passDate" jdbcType="TIMESTAMP"/>
        <result column="EMS_NO" property="emsNo" jdbcType="VARCHAR"/>
        <result column="ITEM_NO" property="itemNo" jdbcType="VARCHAR"/>
        <result column="TRADE_VALID_DATE" property="tradeValidDate" jdbcType="TIMESTAMP"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="G_MARK" property="GMark" jdbcType="VARCHAR"/>
        <result column="LIST_SID" property="listSid" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,CAPITAL_NO
     ,USE_ADDRESS
     ,USE_DATE
     ,USE_STATUS
     ,TRADE_STATUS
     ,DEPOSITARY_OFFICER
     ,EMS_LIST_NO
     ,ENTRY_NO
     ,DECLARE_DATE
     ,TRADE_MODE
     ,G_NAME
     ,G_MODEL
     ,CODE_T_S
     ,UNIT
     ,QTY
     ,DEC_TOTAL
     ,CURR
     ,PASS_DATE
     ,EMS_NO
     ,ITEM_NO
     ,TRADE_VALID_DATE
     ,TRADE_CODE
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,G_MARK
     ,LIST_SID
    </sql>
    <sql id="condition">
        <if test="capitalNo != null and capitalNo != ''">
            and CAPITAL_NO like concat(concat('%', #{capitalNo}),'%')
        </if>
        <if test="useAddress != null and useAddress != ''">
            and USE_ADDRESS like concat(concat('%', #{useAddress}),'%')
        </if>
        <if test="useDateFrom != null and useDateFrom != ''">
            <![CDATA[ and USE_DATE >= to_date(#{useDateFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="useDateTo != null and useDateTo != ''">
            <![CDATA[ and USE_DATE < to_date(#{useDateTo}, 'yyyy-MM-dd')+1 ]]>
        </if>
        <if test="useStatus != null and useStatus != ''">
            and USE_STATUS = #{useStatus}
        </if>
        <if test="tradeStatus != null and tradeStatus != ''">
            and TRADE_STATUS = #{tradeStatus}
        </if>
        <if test="depositaryOfficer != null and depositaryOfficer != ''">
            and DEPOSITARY_OFFICER like concat(concat('%', #{depositaryOfficer}),'%')
        </if>
        <if test="emsListNo != null and emsListNo != ''">
            and EMS_LIST_NO like concat(concat('%', #{emsListNo}),'%')
        </if>
        <if test="entryNo != null and entryNo != ''">
            and ENTRY_NO like concat(concat('%', #{entryNo}),'%')
        </if>
        <if test="declareDateFrom != null and declareDateFrom != ''">
            <![CDATA[ and DECLARE_DATE >= to_date(#{declareDateFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="declareDateTo != null and declareDateTo != ''">
            <![CDATA[ and DECLARE_DATE < to_date(#{declareDateTo}, 'yyyy-MM-dd')+1 ]]>
        </if>
        <if test="tradeMode != null and tradeMode != ''">
            and TRADE_MODE = #{tradeMode}
        </if>
        <if test="GName != null and GName != ''">
            and G_NAME like concat(concat('%', #{GName}),'%')
        </if>
        <if test="GModel != null and GModel != ''">
            and G_MODEL like concat(concat('%', #{GModel}),'%')
        </if>
        <if test="codeTS != null and codeTS != ''">
            and CODE_T_S like concat(concat('%', #{codeTS}),'%')
        </if>
        <if test="unit != null and unit != ''">
            and UNIT = #{unit}
        </if>
        <if test="qty != null and qty != ''">
            and QTY = #{qty}
        </if>
        <if test="decTotal != null and decTotal != ''">
            and DEC_TOTAL = #{decTotal}
        </if>
        <if test="curr != null and curr != ''">
            and CURR = #{curr}
        </if>
        <if test="passDateFrom != null and passDateFrom != ''">
            <![CDATA[ and PASS_DATE >= to_date(#{passDateFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="passDateTo != null and passDateTo != ''">
            <![CDATA[ and PASS_DATE < to_date(#{passDateTo}, 'yyyy-MM-dd')+1 ]]>
        </if>
        <if test="emsNo != null and emsNo != ''">
            and EMS_NO like concat(concat('%', #{emsNo}),'%')
        </if>
        <if test="itemNo != null and itemNo != ''">
            and ITEM_NO like concat(concat('%', #{itemNo}),'%')
        </if>
        <if test="tradeValidDateFrom != null and tradeValidDateFrom != ''">
            <![CDATA[ and TRADE_VALID_DATE >= to_date(#{tradeValidDateFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="tradeValidDateTo != null and tradeValidDateTo != ''">
            <![CDATA[ and TRADE_VALID_DATE < to_date(#{tradeValidDateTo}, 'yyyy-MM-dd')+1 ]]>
        </if>
        and TRADE_CODE = #{tradeCode}
        <if test="GMark != null and GMark != ''">
            and G_MARK = #{GMark}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="devUseRecordResultMap" parameterType="com.dcjet.cs.dev.model.DevUseRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_DEV_USE_RECORD t
        <where>
            <include refid="condition"></include>
        </where>
        order by DECLARE_DATE desc,EMS_LIST_NO,sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_DEV_USE_RECORD t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <!-- 根据emsListNo查询实体 -->
    <select id="selectByEmsListNo" parameterType="com.dcjet.cs.dev.model.DevEntryRecord"
            resultMap="devUseRecordResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_DEV_USE_RECORD where EMS_LIST_NO = #{emsListNo} and TRADE_CODE = #{tradeCode}
        <if test="entryNo != null and entryNo != ''">
            and ENTRY_NO =#{entryNo}
        </if>
    </select>
    <!-- 根据清单内部编号更新项目确认书编号和监管日期 -->
    <update id="updateByEmsListNo" parameterType="com.dcjet.cs.dev.model.DevUseRecord">
        update T_DEV_USE_RECORD
        <set>
            ITEM_NO = #{itemNo},
            TRADE_VALID_DATE = #{tradeValidDate}
        </set>
        <where>
            EMS_LIST_NO = #{emsListNo}
            AND TRADE_CODE = #{tradeCode}
        </where>
    </update>
    <!-- 根据清单内部编号更新实体 -->
    <update id="updateByEmsListNo2" parameterType="com.dcjet.cs.dev.model.DevUseRecord">
        update T_DEV_USE_RECORD
        <set>
            CAPITAL_NO = #{capitalNo},
            USE_ADDRESS = #{useAddress},
            TRADE_VALID_DATE = #{tradeValidDate},
            USE_DATE = #{useDate},
            USE_STATUS = #{useStatus},
            DEPOSITARY_OFFICER = #{depositaryOfficer},
            TRADE_STATUS = #{tradeStatus}
        </set>
        <where>
            EMS_LIST_NO = #{emsListNo}
            AND TRADE_CODE = #{tradeCode}
        </where>
    </update>
</mapper>
