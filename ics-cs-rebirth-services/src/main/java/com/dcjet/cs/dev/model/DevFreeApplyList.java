package com.dcjet.cs.dev.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-5-14
 */
@Setter
@Getter
@Table(name = "T_DEV_FREE_APPLY_LIST")
public class DevFreeApplyList implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 表头SID
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 流水号
     */
    @Column(name = "SERIAL_NO")
    private Long serialNo;
    /**
     * 企业料号
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;
    /**
     * 商品编码
     */
    @Column(name = "CODE_T_S")
    private String codeTS;
    /**
     * 商品名称
     */
    @Column(name = "G_NAME")
    private String GName;
    /**
     * 申报规格型号
     */
    @Column(name = "G_MODEL")
    private String GModel;
    /**
     * 申报数量
     */
    @Column(name = "QTY")
    private BigDecimal qty;
    /**
     * 计量单位
     */
    @Column(name = "UNIT")
    private String unit;
    /**
     * 法一数量
     */
    @Column(name = "QTY_1")
    private BigDecimal qty1;
    /**
     * 法一单位
     */
    @Column(name = "UNIT_1")
    private String unit1;
    /**
     * 法二单位
     */
    @Column(name = "UNIT_2")
    private String unit2;
    /**
     * 申报总价
     */
    @Column(name = "DEC_TOTAL")
    private BigDecimal decTotal;
    /**
     * 币制
     */
    @Column(name = "CURR")
    private String curr;
    /**
     * 原产国
     */
    @Column(name = "ORIGIN_COUNTRY")
    private String originCountry;
    /**
     * 关联单号
     */
    @Column(name = "LINKED_NO")
    private String linkedNo;
    /**
     * 关联单行号
     */
    @Column(name = "LINE_NO")
    private String lineNo;
    /**
     * 合同协议号
     */
    @Column(name = "CONTR_NO")
    private String contrNo;
    /**
     * 采购负责人
     */
    @Column(name = "PO_PERSON")
    private String poPerson;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 数据来源 1录入;2导入;3数据提取
     */
    @Column(name = "DATA_SOURCE")
    private String dataSource;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 变更人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 变更日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 制单人姓名
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;

    @Transient
    private String note1;
    @Transient
    private String fileName;
    @Transient
    private String originFileName;
    @Transient
    private String gNoAll;
    @Transient
    private String bondSid;

    @Column(name = "QTY2")
    private BigDecimal qty2;

    @Column(name = "use_to")
    private String useTo;
    @Column(name = "structure")
    private String structure;
    @Column(name = "cop_g_name")
    private String copGName;
    @Column(name = "files")
    private String files;
    /** 对应清单 */
    @Column(name = "correspond_bill")
    private String correspondBill;
}
