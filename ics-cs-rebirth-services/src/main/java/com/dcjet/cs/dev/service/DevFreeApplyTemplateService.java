package com.dcjet.cs.dev.service;

import com.dcjet.cs.dev.dao.DevFreeApplyTemplateMapper;
import com.dcjet.cs.dev.mapper.DevFreeApplyTemplateDtoMapper;
import com.dcjet.cs.dev.model.DevFreeApplyTemplate;
import com.dcjet.cs.dto.dev.DevFreeApplyTemplateDto;
import com.dcjet.cs.dto.dev.DevFreeApplyTemplateParam;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-5-15
 */
@Service
public class DevFreeApplyTemplateService extends BaseService<DevFreeApplyTemplate> {
    @Resource
    private DevFreeApplyTemplateMapper devFreeApplyTemplateMapper;
    @Resource
    private DevFreeApplyTemplateDtoMapper devFreeApplyTemplateDtoMapper;

    @Override
    public Mapper<DevFreeApplyTemplate> getMapper() {
        return devFreeApplyTemplateMapper;
    }

    /**
     * 获取分页信息
     *
     * @param devFreeApplyTemplateParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<DevFreeApplyTemplateDto>> getListPaged(DevFreeApplyTemplateParam devFreeApplyTemplateParam, PageParam pageParam) {
        // 启用分页查询
        DevFreeApplyTemplate devFreeApplyTemplate = devFreeApplyTemplateDtoMapper.toPo(devFreeApplyTemplateParam);
        Page<DevFreeApplyTemplate> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> devFreeApplyTemplateMapper.getList(devFreeApplyTemplate));
        List<DevFreeApplyTemplateDto> devFreeApplyTemplateDtos = page.getResult().stream().map(head -> {
            DevFreeApplyTemplateDto dto = devFreeApplyTemplateDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        return ResultObject.createInstance(devFreeApplyTemplateDtos, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 功能描述:新增
     *
     * @param devFreeApplyTemplateParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<DevFreeApplyTemplateDto> insert(DevFreeApplyTemplateParam devFreeApplyTemplateParam, UserInfoToken userInfo) {
        ResultObject<DevFreeApplyTemplateDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        DevFreeApplyTemplate devFreeApplyTemplate = devFreeApplyTemplateDtoMapper.toPo(devFreeApplyTemplateParam);

        // 判断模板名称是否存在
        Example example = new Example(DevFreeApplyTemplate.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tempName", devFreeApplyTemplate.getTempName());
        criteria.andEqualTo("tradeCode", devFreeApplyTemplate.getTradeCode());
        DevFreeApplyTemplate template = devFreeApplyTemplateMapper.selectOneByExample(example);
        if (null != template) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("模板名称已存在"));
        }


        // 新增数据
        devFreeApplyTemplate.setSid(UUID.randomUUID().toString());
        devFreeApplyTemplate.setInsertUser(userInfo.getUserNo());
        devFreeApplyTemplate.setInsertTime(new Date());
        int insertStatus = devFreeApplyTemplateMapper.insertSelective(devFreeApplyTemplate);
        if (insertStatus > 0) {
            resultObject.setData(devFreeApplyTemplateDtoMapper.toDto(devFreeApplyTemplate));
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
        devFreeApplyTemplateMapper.deleteBySids(sids);
    }

    /**
     * @param userInfo
     * @return com.xdo.domain.ResultObject
     * @description 查询所有制单员
     * <AUTHOR>
     * @date 2020/6/2 9:50
     */
    public ResultObject selectInsertUser(UserInfoToken userInfo) {
        return ResultObject.createInstance(true, "", devFreeApplyTemplateMapper.selectInsertUser(userInfo.getCompany()));
    }

}
