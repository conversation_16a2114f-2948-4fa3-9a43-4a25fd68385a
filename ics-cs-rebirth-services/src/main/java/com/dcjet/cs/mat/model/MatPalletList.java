package com.dcjet.cs.mat.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-4-12
 */
@Setter
@Getter
@Table(name = "T_MAT_PALLET_LIST")
public class MatPalletList implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 制单人姓名
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;



    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 修改人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;
    /**
     * 表头SID
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 客户CODE
     */
    @Column(name = "CLIENT_CODE")
    private String clientCode;
    /**
     * 客户名称
     */
    @Column(name = "CLIENT_NAME")
    private String clientName;
    /**
     * 联系人1
     */
    @Column(name = "LINKMAN_NAME1")
    private String linkmanName1;
    /**
     * 联系人2
     */
    @Column(name = "LINKMAN_NAME2")
    private String linkmanName2;
    /**
     * 联系人3
     */
    @Column(name = "LINKMAN_NAME3")
    private String linkmanName3;
    /**
     * 类型
     */
    @Column(name = "PACKAGE_TYPE")
    private String packageType;
}
