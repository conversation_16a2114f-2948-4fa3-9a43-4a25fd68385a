package com.dcjet.cs.mat.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-4-16
 */
@Setter
@Getter
@Table(name = "T_MAT_IMGEXG_ORG_SYNC_TMP")
public class MatImgexgOrgSyncTmp implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 备案号
     */
    @Column(name = "EMS_NO")
    private String emsNo;
    /**
     * 物料类型 I 料件  E 成品
     */
    @Column(name = "G_MARK")
    @JsonProperty("gMark")
    private String gMark;
    /**
     * 备案序号
     */
    @Column(name = "SERIAL_NO")
    private Integer serialNo;
    /**
     * 备案料号
     */
    @Column(name = "COP_G_NO")
    private String copGNo;
    /**
     * 商品编码
     */
    @Column(name = "CODE_T_S")
    private String codeTS;
    /**
     * 商品名称
     */
    @Column(name = "G_NAME")
    @JsonProperty("gName")
    private String gName;
    /**
     * 商品规格型号
     */
    @Column(name = "G_MODEL")
    @JsonProperty("gModel")
    private String gModel;
    /**
     * 申报计量单位
     */
    @Column(name = "UNIT")
    private String unit;
    /**
     * 法定计量单位
     */
    @Column(name = "UNIT_1")
    private String unit1;
    /**
     * 法定第二计量单位
     */
    @Column(name = "UNIT_2")
    private String unit2;
    /**
     * 备案数量
     */
    @Column(name = "QTY")
    private BigDecimal qty;
    /**
     * 申报单价
     */
    @Column(name = "DEC_PRICE")
    private BigDecimal decPrice;
    /**
     * 申报总价
     */
    @Column(name = "DEC_TOTAL")
    private BigDecimal decTotal;
    /**
     * 申报币制
     */
    @Column(name = "CURR")
    private String curr;
    /**
     * 产销国
     */
    @Column(name = "COUNTRY")
    private String country;
    /**
     * 归类标记 1-主料件 2-辅料件；
     */
    @Column(name = "CLASS_MARK")
    private String classMark;
    /**
     * 修改标记代码 0-未修改 1-修改 2-删除 3-增加 备案时新增；变更时修改、增加；删除暂时不用。海关审批通过时，自动将最新备案数据更新到未修改。
     */
    @Column(name = "MODIFY_MARK")
    private String modifyMark;
    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 备案有效期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "RECORD_DATE")
    private Date recordDate;
    /**
     * 备案有效期-开始
     */
    @Transient
    private String recordDateFrom;
    /**
     * 备案有效期-结束
     */
    @Transient
    private String recordDateTo;
    /**
     * 手/账册内部编号
     */
    @Column(name = "COP_EMS_NO")
    private String copEmsNo;
    /**
     * 制单人姓名
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;
    /**
     * 监管证件
     */
    @Column(name = "CREDENTIALS")
    private String credentials;
    /**
     * 减免方式
     */
    @Column(name = "DUTY_MODE")
    private String dutyMode;
    /**
     * 批次号
     */
    @Column(name = "BATCH_NO")
    private String batchNo;
    @Column(name = "BUSSINESS_ID")
    private String bussinessId;
    @Transient
    private Integer gNo;
}
