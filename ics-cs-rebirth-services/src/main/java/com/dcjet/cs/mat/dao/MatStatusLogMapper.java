package com.dcjet.cs.mat.dao;

import com.dcjet.cs.mat.model.MatStatusLog;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* MatStatusLog
* <AUTHOR>
* @date: 2020-5-20
*/
public interface MatStatusLogMapper extends Mapper<MatStatusLog> {
    /**
     * 查询获取数据
     * @param matStatusLog
     * @return
     */
    List<MatStatusLog> getList(MatStatusLog matStatusLog);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
