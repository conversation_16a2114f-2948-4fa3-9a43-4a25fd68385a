package com.dcjet.cs.mat.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-17
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_MAT_NON_BONDED")
public class MatNonBonded extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 物料类型 I 料件 E 成品 2 设备 3 其他
     */
    @Column(name = "G_MARK")
    @NotEmpty(message = "物料类型不能为空！")
    @ApiModelProperty(name = "物料类型",example ="1_G_MARK" )
    private String GMark;
    /**
     * 商品料号(企业料号)
     */
    @Column(name = "COP_G_NO")
    @NotEmpty(message = "企业料号不能为空！")
    private String copGNo;
    /**
     * 企业料号-多个(用于查询)
     */
    @Transient
    private List<String> copGNos;
    /**
     * 商品编码
     */
    @Column(name = "CODE_T_S")
    @NotEmpty(message = "商品编码不能为空！")
    @ApiModelProperty(name = "商品编码",value = "商品编码",example ="4_CODE_T_S" )
    private String codeTS;
    /**
     * 商品名称
     */
    @Column(name = "G_NAME")
    @NotEmpty(message = "商品名称不能为空！")
    @ApiModelProperty(name = "商品名称",value = "商品名称",example ="5_G_NAME" )
    private String GName;
    /**
     * 商品规格型号
     */
    @Column(name = "G_MODEL")
    @NotEmpty(message = "申报规格型号不能为空！")
    @ApiModelProperty(name = "申报规格型号",value = "申报规格型号",example ="7_G_MODEL" )
    private String GModel;
    /**
     * 申报计量单位
     */
    @Column(name = "UNIT")
    @NotEmpty(message = "申报计量单位不能为空！")
    @ApiModelProperty(name = "申报计量单位",value = "申报计量单位",example ="8_UNIT" )
    private String unit;
    /**
     * 法定计量单位
     */
    @Column(name = "UNIT_1")
    @NotEmpty(message = "法定计量单位不能为空！")
    private String unit1;
    /**
     * 法定第二计量单位
     */
    @Column(name = "UNIT_2")
    private String unit2;
    /**
     * 申报数量
     */
    @Column(name = "QTY")
    private BigDecimal qty;
    /**
     * 申报要素
     */
    @Column(name = "DECLARE_ELEMENTS")
    private String declareElements;
    /**
     * 申报单价
     */
    @Column(name = "DEC_PRICE")
    @ApiModelProperty(name = "申报单价",value = "申报单价",example ="B_DEC_PRICE" )
    private BigDecimal decPrice;
    /**
     * 币制
     */
    @Column(name = "CURR")
    @ApiModelProperty(name = "申报币制",value = "申报币制",example ="9_CURR" )
    private String curr;
    /**
     * 减免方式
     */
    @Column(name = "DUTY_MODE")
    private String dutyMode;
    /**
     * 产销国
     */
    @Column(name = "COUNTRY")
    @ApiModelProperty(name = "产销国",value = "产销国",example ="A_COUNTRY" )
    private String country;
    /**
     * 归类标记 1-主料件 2-辅料件；
     */
    @Column(name = "CLASS_MARK")
    private String classMark;
    /**
     * 法一单位比例
     */
    @Column(name = "FACTOR_1")
    @ApiModelProperty(name = "法一比例因子",value = "法一比例因子",example ="F_FACTOR_1" )
    private BigDecimal factor1;
    /**
     * 法二单位比例
     */
    @Column(name = "FACTOR_2")
    @ApiModelProperty(name = "法二比例因子",value = "法二比例因子",example ="G_FACTOR_2" )
    private BigDecimal factor2;
    /**
     * 重量比例因子
     */
    @Column(name = "FACTOR_WT")
    private BigDecimal factorWt;
    /**
     * 归类说明
     */
    @Column(name = "CLASS_REMARK")
    private String classRemark;
    /**
     * 中文名称
     */
    @Column(name = "COP_G_NAME")
    @ApiModelProperty(name = "中文名称",value = "中文名称",example ="6_COP_G_NAME" )
    private String copGName;
    /**
     * 中文规格型号
     */
    @Column(name = "COP_G_MODEL")
    @ApiModelProperty(name = "中文规格型号",value = "中文规格型号",example ="C_COP_G_MODEL" )
    private String copGModel;
    /**
     * 工厂
     */
    @Column(name = "FACTORY")
    private String factory;
    /**
     * 成本中心
     */
    @Column(name = "COST_CENTER")
    @ApiModelProperty(name = "成本中心",value = "成本中心",example ="M_COST_CENTER" )
    private String costCenter;
    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 单重
     */
    @Column(name = "NET_WT")
    @ApiModelProperty(name = "净重",value = "净重",example ="K_NET_WT" )
    private BigDecimal netWt;
    /**
     * 重量单位
     */
    @Column(name = "UNIT_WT")
    private String unitWt;
    /**
     * 数据状态 0 正常，1 过滤
     */
    @Column(name = "STATUS")
    private String status;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

    /**
     * 创建时间-开始
     */
    @Transient
    private String insertTimeFrom;
    /**
     * 创建时间-结束
     */
    @Transient
    private String insertTimeTo;

    /**
     * 更新时间-开始
     */
    @Transient
    private String updateTimeFrom;
    /**
     * 更新时间-结束
     */
    @Transient
    private String updateTimeTo;
    /**
     * 发送人
     */
    @Column(name = "SEND_USER")
    private String sendUser;
    /**
     * 发送人
     */
    @Column(name = "SEND_USER_NAME")
    private String sendUserName;
    /**
     * 发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "DECLARE_DATE")
    private Date declareDate;
    /**
     * 发送时间-开始
     */
    @Transient
    private String declareDateFrom;
    /**
     * 发送时间-结束
     */
    @Transient
    private String declareDateTo;
    /**
     * 内审员
     */
    @Column(name = "APPR_USER")
    private String apprUser;
    /**
     * 内审员
     */
    @Column(name = "APPR_USER_NAME")
    private String apprUserName;
    /**
     * 审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "APPR_DATE")
    private Date apprDate;
    /**
     * 审核时间-开始
     */
    @Transient
    private String apprDateFrom;
    /**
     * 审核时间-结束
     */
    @Transient
    private String apprDateTo;
    /**
     * 审核状态
     */
    @Column(name = "APPR_STATUS")
    private String apprStatus;
    /**
     * 备案有效期开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "RECORD_DATE_START")
    @ApiModelProperty(name = "备案有效期起",value = "备案有效期起",example ="2_RECORD_DATE" )
    private Date recordDateStart;
    /**
     * 备案有效期开始时间-开始
     */
    @Transient
    private String recordDateStartFrom;
    /**
     * 备案有效期开始时间-结束
     */
    @Transient
    private String recordDateStartTo;
    /**
     * 备案有效期结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "RECORD_DATE_END")
    @ApiModelProperty(name = "备案有效期止",value = "备案有效期止",example ="3_RECORD_DATE" )
    private Date recordDateEnd;
    /**
     * 备案有效期结束时间-开始
     */
    @Transient
    private String recordDateEndFrom;
    /**
     * 备案有效期结束时间-结束
     */
    @Transient
    private String recordDateEndTo;

    /**
     * ERP料号
     */
    @Column(name = "ERP_G_NO")
    private String erpGNo;
    /**
     * ERP单位
     */
    @Column(name = "UNIT_ERP")
    @ApiModelProperty(name = "ERP单位",value = "ERP单位",example ="H_UNIT_ERP" )
    private String unitErp;
    /**
     * 净重数量
     */
    @Column(name = "QTY_WT")
    @ApiModelProperty(name = "净重数量",value = "净重数量",example ="J_QTY_WT" )
    private BigDecimal qtyWt;

    /**
     * ERP比例因子
     */
    @Column(name = "FACTOR_ERP")
    @ApiModelProperty(name = "ERP比例因子",value = "ERP比例因子",example ="I_FACTOR_ERP" )
    private BigDecimal factorErp;

    /**
     * 英文名称
     */
    @Column(name = "COP_G_NAME_EN")
    @ApiModelProperty(name = "英文名称",value = "英文名称",example ="D_COP_G_NAME_EN" )
    private String copGNameEn;
    /**
     * 英文规格型号
     */
    @Column(name = "COP_G_MODEL_EN")
    @ApiModelProperty(name = "英文规格型号",value = "英文规格型号",example ="E_COP_G_NAME_EN" )
    private String copGModelEn;

    @Transient
    private String attach;
    @Transient
    private String attachName;

    @Transient
    private String apprStatusName;

    /**
     * 物料类型 I 料件  E 成品 2 设备 3 其他
     */
    @Transient
    private String gMarkName;
    @Transient
    private String userNo;


    /**
     * 原始料物料
     */
    @Column(name = "ORIGINAL_G_NO")
    @ApiModelProperty(name = "原始料物料",value = "原始料物料",example ="L_ORIGINAL_G_NO" )
    private String originalGNo;

    /**
     * 包装种类
     */
    @Column(name = "WRAP_TYPE")
    private String wrapType;
    /**
     * 从量比例因子
     */
    @Column(name = "FACTOR_QTY")
    private BigDecimal factorQty;

    /**
     * 进口普通税率(包含百分比符号及从量信息)
     */
    @Transient
    private String impOrdinaryRate;
    /**
     * 进口最惠国税率(包含百分比符号及从量信息)
     */
    @Transient
    private String impDiscountRate;
    /**
     * 进口暂定税率(包含百分比符号及从量信息)
     */
    @Transient
    private String impTempRate;
    /**
     * 增值税税率
     */
    @Transient
    private String addTaxRate;

    /**
     * 进口消费税从价定率
     */
    @Transient
    private String impConsumeRatePerc;
    /**
     * 进口消费税从量定额
     */
    @Transient
    private String impConsumeRatePrice;
    /**
     * 单位
     */
    @Transient
    private String impConsumeRateUnit;

    /**
     * 监管证件
     */
    @Column(name = "CREDENTIALS")
    private String credentials;

    /**
     * 物料信息表SID
     */
    @Transient
    private String erpSid;
    /**
     * CIQ编码
     */
    @Column(name = "CIQ_NO")
    @ApiModelProperty(name = "CIQ代码",value = "CIQ代码",example ="M_CIQ_NO" )
    private String ciqNo;
    /**
     * 锁定标志（多进程生成、解析时，防止并发取任务）
     */
    @Column(name = "LOCK_MARK")
    private String lockMark;
    /**
     * 客供单价
     */
    @Column(name = "CLIENT_PRICE")
    private BigDecimal clientPrice;
    /**
     * 检验检疫
     */
    @Column(name = "INSPMONITORCOND")
    private String inspmonitorcond;
    /**
     * 是否涉检
     */
    @Transient
    private String credentialStatus;

    @Column(name = "use_to")
    private String useTo;
    @Column(name = "structure")
    private String structure;
    /**
     * 对应清单
     */
    @Column(name = "CORRESPOND_BILL")
    private String correspondBill;
    /**
     * 物资清单名称1
     */
    @Column(name = "BILL_LIST_NAME1")
    private String billListName1;
    /**
     * 物资清单名称2
     */
    @Column(name = "BILL_LIST_NAME2")
    private String billListName2;
    /**
     * 物资清单附件号
     */
    @Column(name = "BILL_ATTACHMENT_NUMBER")
    private String billAttachmentNumber;
    /**
     * 物资清单序号
     */
    @Column(name = "BILL_SERIAL_NO")
    private BigDecimal billSerialNo;
    /**
     * 物资清单序号
     */
    @Column(name = "bill_policy_basis")
    private String billPolicyBasis;

    /**
     * 客户/供应商代码
     */
    @Column(name = "SUPPLIER_CODE")
    private String supplierCode;
    /**
     * 工厂代码
     */
    @Column(name = "FACTORY_CODE")
    private String factoryCode;
    /**
     * 品牌
     */
    @Column(name = "BRAND")
    private String brand;

    /**
     * 客户料号
     */
    @Column(name = "CUSTOMER_G_NO")
    private String customerGNo;

    /**
     * 是否是危化品
     */
    @Column(name = "IS_HAZCHEM")
    private String isHazchem;

    /**
     * 是否是危险货物
     */
    @Column(name = "IS_DG")
    private String isDg;
}
