package com.dcjet.cs.mat.mapper;

import com.dcjet.cs.dto.mat.MatImgexgDto;
import com.dcjet.cs.dto.mat.MatImgexgParam;
import com.dcjet.cs.dto.mat.MatNonBondedParam;
import com.dcjet.cs.mat.model.MatImgexg;
import com.dcjet.cs.mat.model.MatImgexgForApi;
import com.dcjet.cs.mat.model.MatImgexgOrg;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import java.math.BigDecimal;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-17
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = BigDecimal.class)
public interface MatImgexgDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    //@Mapping( target = "netWt", expression = "java(po.getNetWt() != null ? po.getNetWt().toPlainString() : null)")
    MatImgexgDto toDto(MatImgexg po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    MatImgexg toPo(MatImgexgParam param);

    MatImgexg toBPo(MatImgexgDto param);
    /**
     * 数据库原始数据更新
     * @param matImgexgParam
     * @param matImgexg
     */
    void updatePo(MatImgexgParam matImgexgParam, @MappingTarget MatImgexg matImgexg);
    default void patchPo(MatImgexgParam matImgexgParam, MatImgexg matImgexg) {
        // TODO 自行实现局部更新
    }

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    MatImgexgOrg toOrg(MatImgexg param);

    MatNonBondedParam toNonPo(MatImgexg po);

    MatImgexg toMatPo(MatNonBondedParam mat);

    MatImgexgForApi toMatApiDto(MatImgexg param);
}
