package com.dcjet.cs.mat.dao.imp;

import com.dcjet.cs.mat.model.imp.EmsConsumeTmp;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* EmsConsumeTmp
* <AUTHOR>
* @date: 2020-5-20
*/
public interface EmsConsumeTmpMapper extends Mapper<EmsConsumeTmp> {
    /**
     * 根据传入的数据类型获取相应数据
     *
     * @param param
     * @return 返回结果
     */
    List<EmsConsumeTmp> selectByFlag(Map<String, Object> param);
}
