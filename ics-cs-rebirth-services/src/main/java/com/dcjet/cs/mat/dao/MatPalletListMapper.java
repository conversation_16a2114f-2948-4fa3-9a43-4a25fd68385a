package com.dcjet.cs.mat.dao;

import com.dcjet.cs.mat.model.MatPalletList;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * MatPalletList
 *
 * <AUTHOR>
 * @date: 2021-4-12
 */
public interface MatPalletListMapper extends Mapper<MatPalletList> {
    /**
     * 查询获取数据
     *
     * @param matPalletList
     * @return
     */
    List<MatPalletList> getList(MatPalletList matPalletList);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
