package com.dcjet.cs.mat.service.imp;

import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.mat.dao.EmsConsumePreMapper;
import com.dcjet.cs.mat.dao.EmsHeadMapper;
import com.dcjet.cs.mat.dao.imp.EmsConsumeTmpMapper;
import com.dcjet.cs.mat.model.EmsConsumePre;
import com.dcjet.cs.mat.model.EmsHead;
import com.dcjet.cs.mat.model.MatImgexgOrg;
import com.dcjet.cs.mat.model.imp.EmsConsumeTmp;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.MatVariable;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.EnumCollection;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import com.xdo.multidb.DynamicDataSourcePool;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EmsConsumePreImportService extends BaseService<EmsConsumeTmp> implements ImportHandlerInterface {
    @Resource
    private BulkInsertConfig bulkInsertConfig;

    public IBulkInsert getBulkInsertInstance() throws Exception{
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }
    @Resource
    private EmsHeadMapper emsHeadMapper;

    @Resource
    private EmsConsumePreMapper emsConsumPreMapper;

    @Resource
    private EmsConsumeTmpMapper emsConsumTmpMapper;

    @Autowired
    private DynamicDataSourcePool dynamicDataSourcePool;

     /**0：正确数据 1：错误数据 2：警告数据 对应于EnumCollection.EntityListType的枚举的ordinal*/
    public final int CORRECT_FLAG = EnumCollection.EntityListType.CORRECT_LIST.ordinal();
    public final int WRONG_FLAG = EnumCollection.EntityListType.WRONG_LIST.ordinal();
    public final int WARN_FLAG = EnumCollection.EntityListType.WARN_LIST.ordinal();

    @Override
    public Mapper<EmsConsumeTmp> getMapper() {
        return emsConsumTmpMapper;
    }

    @Override
    public String getTaskCode() {
        String strTaskCode = "CONSUME";
        return strTaskCode;
    }

    @Override
    public List<ExcelImportDto> checkData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        List<ExcelImportDto> excelImportDtoList=new ArrayList<>();
        //根据配置文件的每个sheet的id获取特定sheet转化出来的实体列表，按配置顺序获取
        String strSheetId="CONSUME";
        if(!mapObjectList.containsKey(strSheetId)){
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在" + strSheetId);
            return null;
        }
        List<Object> objectList=mapObjectList.get(strSheetId);
        //获取任务基本参数
        //获取任务的导入类型(新增、修改、删除)，如只有新增可不判断，否则根据类型做不同的业务操作
        mapBasicParam.get("importType");
        //从自定义参数中获取需要的数据，此处是导入表体，需获取对应表头的id和当前操作者
        if(!mapBusinessParam.containsKey("headId") ){
            XdoImportLogger.log("部分自定义参数未成功获取");
            return null;
        }
        String headId = mapBusinessParam.get("headId").toString();
        String insertUser = mapBusinessParam.get("insertUser").toString();
        EmsHead emsHead = emsHeadMapper.selectByPrimaryKey(headId);
        String tradeCode = emsHead.getTradeCode();
        String emsNo = emsHead.getEmsNo();
        String copEmsNo = emsHead.getCopEmsNo();
        //遍历当前实体列表进行校验
        String guid = UUID.randomUUID().toString();
        int intObjectListSize = objectList.size();
        XdoImportLogger.log("共获取实体：" + intObjectListSize);
        List<EmsConsumeTmp> sheetEmlConsumeILists = Collections.synchronizedList(new ArrayList<>(intObjectListSize));
        List<EmsConsumeTmp> sheetEmlConsumeILists1 = Collections.synchronizedList(new ArrayList<>(intObjectListSize));
        objectList.forEach(pa -> {
            EmsConsumeTmp el = (EmsConsumeTmp) pa;
            //基础赋值
            el = initBasicData(el,headId,insertUser,tradeCode,emsNo,guid);
            //基础校验
            el = checkTmpData(el,emsHead);
            if(el.getTempFlag() == 0) {
                // 将校验正确的数据存入正确集合
                sheetEmlConsumeILists1.add(el);
            }else {
                // 将校验错误数据存入待入库临时表
                sheetEmlConsumeILists.add(el);
            }
        });
        List<EmsConsumeTmp> sheetEmlConsumeILists2 = sheetEmlConsumeILists1.parallelStream().filter(s -> s.getTempFlag() == 0).collect(Collectors.toList());
        //判断成品序号对应的申报状态必须一致
        //根据成品序号分组
        Map<String, List<EmsConsumeTmp>> distinctMap = sheetEmlConsumeILists2.parallelStream().collect(Collectors.groupingBy(o -> o.getExgGNo()+"|"+o.getExgVersion()));
        //根据成品序号拿到分组集合判断申报状态值
        for(Map.Entry<String, List<EmsConsumeTmp>> entry : distinctMap.entrySet()) {
            //校验同一商品序号对应的料件序号不能重复
            Map<String, List<EmsConsumeTmp>> distinctImgGNoList = entry.getValue().parallelStream().collect(Collectors.groupingBy(o -> o.getImgGNo()));
            for(Map.Entry<String, List<EmsConsumeTmp>> ss : distinctImgGNoList.entrySet()) {
                if(ss.getValue().size() > 1) {
                    ss.getValue().stream().forEach(s -> {
                        s.setTempFlag(WRONG_FLAG);
                        s.setTempRemark(s.getTempRemark() + xdoi18n.XdoI18nUtil.t("同一成品序号下料件序号不能重复|"));
                    });
                }
            }
            List<EmsConsumeTmp> first = entry.getValue().parallelStream().filter(x -> x.getUcnsDclStat().equals(MatVariable.UCNS_DCL_1) || "未申报".equals(x.getUcnsDclStat())).collect(Collectors.toList());
            List<EmsConsumeTmp> secord = entry.getValue().parallelStream().filter(x -> x.getUcnsDclStat().equals(MatVariable.UCNS_DCL_2) || "已申报".equals(x.getUcnsDclStat())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(first) && CollectionUtils.isNotEmpty(secord)){
                entry.getValue().parallelStream().forEach(s -> {
                    s.setTempFlag(WRONG_FLAG);
                    s.setTempRemark(s.getTempRemark() + xdoi18n.XdoI18nUtil.t("成品序号对应的申报状态必须一致|"));
                });
            }
        }
        sheetEmlConsumeILists2.parallelStream().forEachOrdered(el -> {
            // 手动切换库
            dynamicDataSourcePool.setDataSource(tradeCode);

            if(StringUtils.isBlank(el.getTempRemark())){
                el.setTempRemark("");
            }
            Integer exgGNo = Integer.valueOf(el.getExgGNo());
            Integer imgGNo = Integer.valueOf(el.getImgGNo());
            Example example = new Example(EmsConsumePre.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("exgGNo", exgGNo);
            criteria.andEqualTo("imgGNo", imgGNo);
            criteria.andEqualTo("exgVersion", el.getExgVersion());
            criteria.andEqualTo("headId", headId);
            EmsConsumePre consumeRsp = emsConsumPreMapper.selectOneByExample(example);
            if(consumeRsp == null) {
                //新增
                el.setExgGNo(el.getExgGNo());
                el.setImgGNo(el.getImgGNo());
                el.setSid(UUID.randomUUID().toString());
                el.setModifyMark(CommonEnum.modifyMarkEnum.MODIFY_MARK_3.getCode());
                el.setSendApiStatus(CommonEnum.BillEnum.BILLSTATUS_B.getCode());
                MatImgexgOrg orgE = emsConsumPreMapper.orgInfo(tradeCode,CommonEnum.GMarkEnum.EXG.getCode(),exgGNo,copEmsNo);
                MatImgexgOrg orgI = emsConsumPreMapper.orgInfo(tradeCode, CommonEnum.GMarkEnum.IMG.getCode(),imgGNo,copEmsNo);
                if(orgE == null) {
                    el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("成品序号") + exgGNo + xdoi18n.XdoI18nUtil.t("在物料中心不存在|"));
                }else {
                    el.setCopExgNo(orgE.getCopGNo());
                }
                if(orgI == null) {
                    el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("料件序号") + imgGNo + xdoi18n.XdoI18nUtil.t("在物料中心不存在|"));
                }else {
                    el.setCopImgNo(orgI.getCopGNo());
                }
                el.setDecDmInvisiable(StringUtils.isBlank(el.getDecDmInvisiable()) ? MatVariable.STATUS_0 : el.getDecDmInvisiable());
                el.setDecDmVisiable(StringUtils.isBlank(el.getDecDmVisiable()) ? MatVariable.STATUS_0 : el.getDecDmVisiable());
                el.setBondMtpckPrpr(StringUtils.isBlank(el.getBondMtpckPrpr()) ? "100" : el.getBondMtpckPrpr());
                el.setTempOper(MatVariable.STATUS_0);
            }else {
                //处理标记是 0 不变，申报状态是已申报的数据不允许修改
                if(!Arrays.asList(MatVariable.UPDATE_STATUS).contains(emsHead.getApprStatus()) || (CommonVariable.APPR_STATUS_JC.equals(emsHead.getApprStatus()) && MatVariable.UCNS_DCL_2.equals(consumeRsp.getUcnsDclStat()))) {
                    el.setTempRemark(xdoi18n.XdoI18nUtil.t("只有暂存、内审退回、内审通过、备案通过（表体申报状态仅为未申报的）、备案退回的数据，才可操作|"));
                }else {
                    //修改
                    el.setSid(consumeRsp.getSid());
                    el.setGNo(consumeRsp.getGNo().toString());
                    el.setCopImgNo(consumeRsp.getCopImgNo());
                    el.setCopExgNo(consumeRsp.getCopExgNo());
                    el.setInsertUser(consumeRsp.getInsertUser());
                    el.setInsertTime(consumeRsp.getInsertTime());
                    el.setUpdateUser(insertUser);
                    el.setUpdateTime(new Date());
                    el.setSendApiStatus(consumeRsp.getSendApiStatus());
                    el.setDecAllConsume(consumeRsp.getDecAllConsume() != null ? consumeRsp.getDecAllConsume().toPlainString() : null);
                    el.setDecDmInvisiable(StringUtils.isBlank(el.getDecDmInvisiable()) ? consumeRsp.getDecDmInvisiable().toPlainString() : el.getDecDmInvisiable());
                    el.setDecDmVisiable(StringUtils.isBlank(el.getDecDmVisiable()) ? consumeRsp.getDecDmVisiable().toPlainString() : el.getDecDmVisiable());
                    el.setBondMtpckPrpr(StringUtils.isBlank(el.getBondMtpckPrpr()) ? consumeRsp.getBondMtpckPrpr().toPlainString() : el.getBondMtpckPrpr());
                    el.setNote(StringUtils.isBlank(el.getNote()) ? consumeRsp.getNote() : el.getNote());
                    el.setModifyMark(CommonEnum.modifyMarkEnum.MODIFY_MARK_0.getCode().equals(consumeRsp.getModifyMark()) ? CommonEnum.modifyMarkEnum.MODIFY_MARK_1.getCode() : consumeRsp.getModifyMark());
                    el.setTempOper(MatVariable.STATUS_1);
                }
            }
            if(!StringUtils.isBlank(el.getTempRemark())) {
                String remark=el.getTempRemark().substring(0,el.getTempRemark().length()-1);
                el.setTempRemark(remark);
                el.setTempFlag(WRONG_FLAG);
            }
            if(StringUtils.isBlank(el.getSid())) {
                el.setSid(UUID.randomUUID().toString());
            }
            sheetEmlConsumeILists.add(el);
        });
        //根据tempIndex排序
        List<EmsConsumeTmp> checkedList = sheetEmlConsumeILists.parallelStream().sorted(Comparator.comparing(EmsConsumeTmp::getTempIndex)).collect(Collectors.toList());
        XdoImportLogger.log("共获取插入实体："+checkedList.size()+";执行快速入库操作");
        //初始化快速入库实例（在config文件夹下新建OracleBulkConfig用于读取配置文件中的数据库连接配置）
        //使用快速入库时，拼接的sql语句会根据当前实体进行，如果某个字段存在于实体定义中，但传入的值为空，那么这个字段在数据表存在的默认值将无效
        // 所以请显示赋值，或者，如果要使用数据表的默认值请将此字段从实体中去除
        try {
            emsConsumPreMapper.deleteByHeadId(headId);
        }catch(Exception ex) {
            XdoImportLogger.log("删除"+headId+"临时表数据时报错，报错原因：" + ex);
        }
        IBulkInsert iBulkInsert = getBulkInsertInstance();
        int intEffectCount = 0;
        try {
            intEffectCount = iBulkInsert.fastImport(checkedList);
        }catch(BulkInsertException ex) {
            intEffectCount = ex.getCommitRowCount();
            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
            XdoImportLogger.log(ex);
            return null;
        }catch(Exception ex) {
            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
            XdoImportLogger.log(ex);
            return null;
        }
        ExcelImportDto<EmsConsumeTmp> excelImportDto = new ExcelImportDto<>();
        excelImportDto.setTempOwnerId(guid);
        //获取正确、错误、警告数据列表(警告数据根据各自业务需求设置,可不加)
        Map<String, Object> param = new HashMap<>(2);
        param.put("tempOwner", guid);
        param.put("tempFlag",CORRECT_FLAG);
        List<EmsConsumeTmp> correctList = selectDataList(param);
        excelImportDto.setCorrectNumber(correctList.size());
        excelImportDto.setCorrectList(correctList);
        param.put("tempFlag",WRONG_FLAG);
        List<EmsConsumeTmp> wrongList = selectDataList(param);
        excelImportDto.setWrongNumber(wrongList.size());
        excelImportDto.setWrongList(wrongList);
        XdoImportLogger.log("correct："+excelImportDto.getCorrectList().size()
                +";wrong："+excelImportDto.getWrongList().size());
        excelImportDtoList.add(excelImportDto);
        return excelImportDtoList;
    }

    /**
     * 基础赋值
     * @param el
     * @param headId
     * @param insertUser
     * @param tradeCode
     * @param emsNo
     * @param guid
     * @return
     */
    private EmsConsumeTmp initBasicData(EmsConsumeTmp el,String headId,String insertUser,String tradeCode,String emsNo,String guid) {
        el.setSid(UUID.randomUUID().toString());
        el.setHeadId(headId);
        el.setInsertUser(insertUser);
        el.setInsertTime(new Date());
        el.setTradeCode(tradeCode);
        el.setEmsNo(emsNo);
        el.setTempOwner(guid);
        el.setEtpsExeMark(MatVariable.STATUS_1);
        el.setDataSource(MatVariable.DATS_SOURCE_1);
        return el;
    }

    /**
     * 基本校验
     * @param el
     * @param emsHead
     * @return
     */
    private EmsConsumeTmp checkTmpData(EmsConsumeTmp el,EmsHead emsHead) {
        /*校验成品序号是否为数值*/
        if (StringUtils.isNotBlank(el.getExgGNo())) {
            String regex = "^[0-9]*$";
            if(!el.getExgGNo().matches(regex)) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("成品序号格式不正确|"));
            }
        }
        /*校验料件序号是否为数值*/
        if (StringUtils.isNotBlank(el.getImgGNo())) {
            String regex = "^[0-9]*$";
            if(!el.getImgGNo().matches(regex)) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("料件序号格式不正确|"));
            }
        }
        /*校验净耗*/
        if (StringUtils.isNotBlank(el.getDecCm())) {
            String regex = "^(\\d{0,9})(\\.\\d{1,9})?$";
            if(!el.getDecCm().matches(regex)) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("净耗格式不正确|"));
            }else {
                if (new BigDecimal(el.getDecCm()).compareTo(BigDecimal.ZERO) <= 0) {
                    el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("净耗必须大于0|"));
                }
            }
        }
        /*校验无形损耗率*/
        if (StringUtils.isNotBlank(el.getDecDmInvisiable())) {
            String regex = "^(\\d{0,2})(\\.\\d{1,9})?$";
            if(!el.getDecDmInvisiable().matches(regex)) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("无形损耗率格式不正确|"));
            }else {
                if(new BigDecimal(el.getDecDmInvisiable()).compareTo(BigDecimal.ZERO) < 0 || new BigDecimal(el.getDecDmInvisiable()).compareTo(new BigDecimal(100)) >= 0) {
                    el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("无形损耗率必须大于等于0且小于100|"));
                }
            }
        }
        /*校验有形损耗率*/
        if (StringUtils.isNotBlank(el.getDecDmVisiable())) {
            String regex = "^(\\d{0,2})(\\.\\d{1,9})?$";
            if(!el.getDecDmVisiable().matches(regex)) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("有形损耗率格式不正确|"));
            }else {
                if(new BigDecimal(el.getDecDmVisiable()).compareTo(BigDecimal.ZERO) < 0 || new BigDecimal(el.getDecDmVisiable()).compareTo(new BigDecimal(100)) >= 0) {
                    el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("有形损耗率必须大于等于0且小于100|"));
                }else {
                    /*校验有形损耗率+无形损耗率*/
                    if (StringUtils.isNotBlank(el.getDecDmInvisiable())) {
                        String regex2 = "^(\\d{0,2})(\\.\\d{1,9})?$";
                        if(el.getDecDmInvisiable().matches(regex2)) {
                            if(new BigDecimal(el.getDecDmInvisiable()).compareTo(BigDecimal.ZERO) >= 0 && new BigDecimal(el.getDecDmInvisiable()).compareTo(new BigDecimal(100)) < 0) {
                                BigDecimal totalDecDm = BigDecimal.ZERO;
                                totalDecDm = totalDecDm.add(new BigDecimal(el.getDecDmInvisiable()));
                                totalDecDm = totalDecDm.add(new BigDecimal(el.getDecDmVisiable()));
                                if (totalDecDm.compareTo(BigDecimal.ZERO) < 0 || totalDecDm.compareTo(new BigDecimal(100)) >= 0) {
                                    el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("有形损耗率+无形损耗率必须大于等于0且小于100|"));
                                }
                            }
                        }
                    }
                }
            }
        }
        /*校验保税料件比例*/
        if (StringUtils.isNotBlank(el.getBondMtpckPrpr())) {
            String regex = "^(\\d{0,3})(\\.\\d{1,9})?$";
            if(!el.getBondMtpckPrpr().matches(regex)) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("保税料件比例格式不正确|"));
            }else {
                if(new BigDecimal(el.getBondMtpckPrpr()).compareTo(BigDecimal.ZERO) < 0 || new BigDecimal(el.getBondMtpckPrpr()).compareTo(new BigDecimal(100)) > 0) {
                    el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("保税料件比例必须大于等于0且小于等于100|"));
                }
            }
        }
        //判断如果表头类型为手册
        if((CommonEnum.EMS_TYPE_ENUM.EMS_TYPE_11.getCode().equals(emsHead.getBillFlag()) || CommonEnum.EMS_TYPE_ENUM.EMS_TYPE_12.getCode().equals(emsHead.getBillFlag()))) {
            //如果是手册企业 无论填不填值 都置为0
            el.setExgVersion(CommonVariable.EXG_VERSION_0);
        }
        //判断如果表头类型为账册
        if((CommonEnum.EMS_TYPE_ENUM.EMS_TYPE_21.getCode().equals(emsHead.getBillFlag()) || CommonEnum.EMS_TYPE_ENUM.EMS_TYPE_22.getCode().equals(emsHead.getBillFlag()))) {
            if(StringUtils.isBlank(el.getExgVersion())) {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("单耗版本号不能为空|"));
            }else {
                if(CommonVariable.EXG_VERSION_0.equals(el.getExgVersion())) {
                    el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("单耗版本号不能为0|"));
                }
                if(el.getExgVersion().length() > 8) {
                    el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("单耗版本号长度不能超过8|"));
                }
            }
        }
        if(StringUtils.isBlank(el.getUcnsDclStat())) {
            el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("申报状态不能为空|"));
        }else {
            if(MatVariable.UCNS_DCL_1.equals(el.getUcnsDclStat()) || MatVariable.UCNS_DCL_2.equals(el.getUcnsDclStat()) || "未申报".equals(el.getUcnsDclStat()) || "已申报".equals(el.getUcnsDclStat())) {
                switch(el.getUcnsDclStat()) {
                    case "未申报":
                        el.setUcnsDclStat(MatVariable.UCNS_DCL_1);
                        break;
                    case "已申报":
                        el.setUcnsDclStat(MatVariable.UCNS_DCL_2);
                        break;
                    default:
                        break;
                }
            }else {
                el.setTempRemark(el.getTempRemark() + xdoi18n.XdoI18nUtil.t("申报状态必须为 1或未申报、2或已申报|"));
            }
        }
        if(!StringUtils.isBlank(el.getTempRemark())) {
            String remark=el.getTempRemark().substring(0,el.getTempRemark().length()-1);
            el.setTempRemark(remark);
            el.setTempFlag(WRONG_FLAG);
        }
        return el;
    }

    @Override
    public ResultObject persistData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("导入正式表成功"));
        //获取任务基本参数
        mapBasicParam.get("importType");//获取任务的导入类型(新增、修改、删除)，如只有新增可不判断，否则根据类型做不同的业务操作
        //按配置顺序（同时也是业务校验时添加到excelImportDtoList的顺序）获取临时表批次Id
        if(tempOwnerIdList==null || tempOwnerIdList.size()<=0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("临时表处理批次Id为空，无法执行持久化数据方法"));
        }
        //按顺序获取临时表批次Id，单sheet时只需获取第一个即可
        String strTempOwnerId = tempOwnerIdList.get(0);
        String headId = mapBusinessParam.get("headId").toString();
        BigDecimal nextGNo = emsConsumPreMapper.getNextSerialNo(headId);
        emsConsumPreMapper.importTmpData(strTempOwnerId, headId, nextGNo);
        /* 更新表头状态为暂存 */
        EmsHead emsHead=emsHeadMapper.selectByPrimaryKey(headId);
        emsHead.setApprStatus(CommonVariable.APPR_STATUS_0);
        emsHead.setUpdateUser("importService");
        emsHead.setUpdateTime(new Date());
        emsHeadMapper.updateByPrimaryKey(emsHead);
        return resultObject;
    }

    /**
     * 描述：获取导入的各种类型数据
     *
     * @param param
     * @return
     * <AUTHOR>
     * @date 2019-03-16
     */
    public List<EmsConsumeTmp> selectDataList(Map<String, Object> param) {
        return emsConsumTmpMapper.selectByFlag(param);
    }
}
