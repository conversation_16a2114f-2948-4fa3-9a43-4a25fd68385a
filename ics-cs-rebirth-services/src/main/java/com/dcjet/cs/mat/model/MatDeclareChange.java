package com.dcjet.cs.mat.model;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-12-12
 */
@Setter
@Getter
@Table(name = "t_mat_declare_change")
public class MatDeclareChange implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * 变更日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "change_date")
    @JsonProperty("changeDate")
    private Date changeDate;
    /**
     * 变更内容
     */
    @Column(name = "change_result")
    @JsonProperty("changeResult")
    private String changeResult;
    /**
     * 新申报要素
     */
    @Column(name = "element")
    @JsonProperty("element")
    private String element;
    /**
     * 旧申报要素
     */
    @Column(name = "element_old")
    @JsonProperty("elementOld")
    private String elementOld;
    /**
     * 商品编码
     */
    @Column(name = "code_t_s")
    @JsonProperty("codeTS")
    private String codeTS;
    /**
     * 商品名称
     */
    @Column(name = "g_name")
    @JsonProperty("gName")
    private String gName;
    /**
     * 创建人
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 企业代码
     */
    @Column(name = "trade_code")
    private String tradeCode;

    @Transient
    private String dateTime;

    @Transient
    private String emsNo;
    @Transient
    private Integer serialNo;
    @Transient
    private String copGNo;
    @Transient
    private String GModel;
    @Transient
    private String GMark;
    @Transient
    private String facGNo;
}
