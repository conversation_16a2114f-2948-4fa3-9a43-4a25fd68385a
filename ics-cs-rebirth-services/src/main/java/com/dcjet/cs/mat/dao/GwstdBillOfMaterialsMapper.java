package com.dcjet.cs.mat.dao;

import com.dcjet.cs.mat.model.GwstdBillOfMaterials;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * GwstdBillOfMaterials
 *
 * <AUTHOR>
 * @date: 2022-7-13
 */
public interface GwstdBillOfMaterialsMapper extends Mapper<GwstdBillOfMaterials> {
    /**
     * 查询获取数据
     *
     * @param gwstdBillOfMaterials
     * @return
     */
    List<GwstdBillOfMaterials> getList(GwstdBillOfMaterials gwstdBillOfMaterials);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
