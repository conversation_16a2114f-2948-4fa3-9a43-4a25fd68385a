package com.dcjet.cs.mat.dao;

import com.dcjet.cs.mat.model.MatNonSuitRel;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* MatNonSuitRel
* <AUTHOR>
* @date: 2020-12-15
*/
public interface MatNonSuitRelMapper extends Mapper<MatNonSuitRel> {
    /**
     * 查询获取数据
     * @param matNonSuitRel
     * @return
     */
    List<MatNonSuitRel> getList(MatNonSuitRel matNonSuitRel);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
