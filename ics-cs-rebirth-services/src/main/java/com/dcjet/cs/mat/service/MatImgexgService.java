package com.dcjet.cs.mat.service;

import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.dao.BiCustomerParamsMapper;
import com.dcjet.cs.bi.model.BiCustomerParams;
import com.dcjet.cs.bi.model.BiTransform;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.aeo.AeoAuditInfoParam;
import com.dcjet.cs.dto.mat.*;
import com.dcjet.cs.dto.war.WarringPassConfigDto;
import com.dcjet.cs.dto.war.WarringPassExpandConfigDto;
import com.dcjet.cs.mat.dao.*;
import com.dcjet.cs.mat.mapper.MatImgexgDtoMapper;
import com.dcjet.cs.mat.model.*;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.MatVariable;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.dcjet.cs.war.service.WarringPassExpandConfigService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * 物料中心-企业物料 Service;
 *
 * <AUTHOR>
 * @date: 2019-4-17
 */
@Service
public class MatImgexgService extends BaseService<MatImgexg> {

    private static final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private MatImgexgMapper matImgexgMapper;
    @Resource
    private MatImgexgDtoMapper matImgexgDtoMapper;
    @Resource
    private CommonService commonService;

    @Override
    public Mapper<MatImgexg> getMapper() {
        return matImgexgMapper;
    }

    @Resource
    private AeoAuditInfoService aeoAuditInfoService;
    @Resource
    private MatImgexgLastMapper matImgexgLastMapper;
    @Resource
    private MatImgexgChangeMapper matImgexgChangeMapper;
    @Resource
    private WarringPassConfigService warringPassConfigService;
    @Resource
    private MatStatusLogMapper matStatusLogMapper;
    @Resource
    private BiCustomerParamsMapper biCustomerParamsMapper;
    @Resource
    private BiClientInformationMapper biClientInformationMapper;
    @Resource
    private AttachedMapper attachedMapper;
    @Resource
    protected BulkSqlOpt bulkSqlOpt;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource
    private AuditLogUtil auditLogUtil;
    @Resource
    private WarringPassExpandConfigService warringPassExpandConfigService;
    @Resource
    private MatImgexgOrgMapper matImgexgOrgMapper;
    @Resource
    private PCodeHolder pCodeHolder;

    //region 企业物料基本接口

    public String getCiqNameByCodeTSAndCiqCode(String codeTS,String ciqCode){
        String ciqName = null;
        Optional<List<Map<String, String>>> ciqCodeListOptional = pCodeHolder.getByCodes(PCodeType.COMPLEX_CIQCODE,Arrays.asList(codeTS));
        if(ciqCodeListOptional.isPresent()){
            List<Map<String, String>> list = ciqCodeListOptional.get();
            ciqName = list.stream().filter(it-> ciqCode.equals(it.get("CODE"))).map(it -> it.get("NAME")).collect(Collectors.joining());
        }
        return ciqName;
    }

    /**
     * 获取分页信息
     *
     * @param matImgexgParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<MatImgexgDto>> getListPaged(MatImgexgParam matImgexgParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        MatImgexg matImgexg = matImgexgDtoMapper.toPo(matImgexgParam);
        //判断是否有中文 没有中文认为是code，有中文认为是name??
        if (matImgexgParam.getUserName() != null) {
            if (CommonVariable.checkname(matImgexgParam.getUserName())) {
                matImgexg.setInsertUser(matImgexgParam.getUserName());
            } else {
                matImgexg.setInsertUserName(matImgexgParam.getUserName());
            }
        }
        matImgexg.setTradeCode(userInfo.getCompany());
        Page<MatImgexg> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> matImgexgMapper.getList(matImgexg));
        List<MatImgexgDto> matImgexgDtos = page.getResult().stream().map(head -> {
            MatImgexgDto dto = matImgexgDtoMapper.toDto(head);
            if(dto.getNetWt()!=null) {
                dto.setNetWt(head.getNetWt().toPlainString());
            }

            if(StringUtils.isNotBlank(dto.getCodeTS()) && StringUtils.isNotBlank(dto.getCiqNo())){
                dto.setCiqNo(dto.getCiqNo() + " " + getCiqNameByCodeTSAndCiqCode(dto.getCodeTS(),dto.getCiqNo()));
            }
            return dto;
        }).collect(Collectors.toList());
        return (ResultObject<List<MatImgexgDto>>) ResultObject.createInstance(matImgexgDtos, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 功能描述:新增
     *
     * @param matImgexgParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<MatImgexgDto> insert(MatImgexgParam matImgexgParam, UserInfoToken userInfo) {
        ResultObject<MatImgexgDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        MatImgexg matImgexg = matImgexgDtoMapper.toPo(matImgexgParam);
        /*
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();

        //去除企业料号/备案料号特殊字符
//        matImgexg.setFacGNo(commonService.parseCharacter(matImgexg.getFacGNo()));
//        matImgexg.setCopGNo(commonService.parseCharacter(matImgexg.getCopGNo()));

        /*
         * 判断保税中是否存在相同料号数据
         */
        boolean existBond = existBond(sid, userInfo.getCompany(), matImgexg.getGMark(), matImgexg.getFacGNo(), matImgexg.getCopEmsNo());
        if (existBond) {
            throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("该企业料号在保税已存在") + xdoi18n.XdoI18nUtil.t("无法新增"));
        }
        /*
         * 获取通关业务设置中同物料保税与非保税不得同时存在状态，不能同时存在是校验在非保税中是否存在
         */
        List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(userInfo);
        List<WarringPassExpandConfigDto> warringPassExpandConfigDtos = warringPassExpandConfigService.selectAll(warringPassConfigDtos.get(0).getSid(),userInfo);
        boolean productActivation = true;
        if (warringPassConfigDtos.size() > 0) {
            WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
            if (CommonVariable.CONFIG_1.equals(warringPassConfigDto.getUniqueness())) {
                boolean existNoBond = existNoBond(null, userInfo.getCompany(), null, matImgexg.getFacGNo());
                if (existNoBond) {
                    throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("该企业料号在非保税预归类中已存在") + xdoi18n.XdoI18nUtil.t("无法新增"));
                }
            }
            if (StringUtils.equals(warringPassConfigDto.getProductActivation(),CommonVariable.CONFIG_1)){
                productActivation = true;
            }else {
                productActivation = false;
            }
        }

        if (warringPassExpandConfigDtos.size()>0){
            WarringPassExpandConfigDto warringPassExpandConfigDto = warringPassExpandConfigDtos.get(0);
            if (warringPassExpandConfigDto.getDecPriceDigit()!=null){
                if (matImgexg.getDecPrice() != null) {
                    matImgexg.setDecPrice(matImgexg.getDecPrice().setScale(warringPassExpandConfigDto.getDecPriceDigit(), BigDecimal.ROUND_HALF_UP));
                }
            }
        }

        matImgexg.setSid(sid);
        matImgexg.setApprStatus(CommonVariable.APPR_STATUS_0);
        matImgexg.setStatus(MatVariable.STATUS_0);
        matImgexg.setInsertUser(userInfo.getUserNo());
        matImgexg.setInsertUserName(userInfo.getUserName());
        matImgexg.setInsertTime(new Date());
        matImgexg.setTradeCode(userInfo.getCompany());
        if (StringUtils.isBlank(matImgexg.getUnit2())) {
            matImgexg.setFactor2(null);
        }
        if (StringUtils.isBlank(matImgexg.getUnitErp())) {
            matImgexg.setFactorErp(null);
        }
        matImgexg.setKeyProductMark(keyproductMark(matImgexg,productActivation,userInfo));

        //根据HS编码带出检验检疫
        /*if (StringUtils.isNotBlank(matImgexg.getCodeTS())) {
            Map<String, String> mapTs = pCodeHolder.getTable(PCodeType.COMPLEX_CIQ, matImgexg.getCodeTS()).orElse(null);
            if (mapTs != null) {
                if (StringUtils.isNotBlank(mapTs.get("INSPMONITORCOND"))) {
                    matImgexg.setInspmonitorcond(mapTs.get("INSPMONITORCOND"));
                }
            }
        }*/

        AeoAuditInfoParam aeoAuditInfoParam = new AeoAuditInfoParam();
        if (CommonEnum.GMarkEnum.IMG.getCode().equals(matImgexg.getGMark())) {
            aeoAuditInfoParam.setApprType(MatVariable.APPR_TYPE_YI);
        } else {
            aeoAuditInfoParam.setApprType(MatVariable.APPR_TYPE_YE);
        }
        aeoAuditInfoParam.setBusinessSid(sid);
        aeoAuditInfoService.orderData(aeoAuditInfoParam, userInfo);

        // 新增数据
        int insertStatus = matImgexgMapper.insert(matImgexg);

        MatImgexgDto matImgexgDto = insertStatus > 0 ? matImgexgDtoMapper.toDto(matImgexg) : null;
        if (matImgexgDto != null) {
            if(matImgexgDto.getNetWt()!=null) {
                matImgexgDto.setNetWt(matImgexg.getNetWt().toPlainString());
            }
            resultObject.setData(matImgexgDto);
            auditLog(matImgexg, userInfo, CommonEnum.operationsEnum.INSERT.getValue());
        } else {
            throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    /**
     * 获取重点标识
     *
     * @param matImgexg
     * @param productActivation
     * @param userInfo
     * @return
     */
    private String keyproductMark(MatImgexg matImgexg, boolean productActivation, UserInfoToken userInfo) {
        String keyProductMark = null;
        if (productActivation) {
            List<MatImgexgOrg> matImgexgOrgs = matImgexgOrgMapper.selectKeyProductMarks(matImgexg.getCopGNo(), matImgexg.getEmsNo(), matImgexg.getGMark(), userInfo.getCompany());
            if (matImgexgOrgs.size()>0) {
                keyProductMark = matImgexgOrgs.get(0).getKeyProductMark();
            }
        }
        return keyProductMark;
    }

    private void auditLog(MatImgexg matImgexg, UserInfoToken userInfo, String operations) {
        if (StringUtils.isNotBlank(matImgexg.getGMark()) && "I".equals(matImgexg.getGMark())) {
            auditLogUtil.AuditLog(operations, "物料中心-企业料件信息", userInfo, matImgexg, operations);
        } else {
            auditLogUtil.AuditLog(operations, "物料中心-企业成品信息", userInfo, matImgexg, operations);
        }

    }

    /**
     * 功能描述:修改
     *
     * @param matImgexgParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<MatImgexgDto> update(MatImgexgParam matImgexgParam, UserInfoToken userInfo) {
        ResultObject<MatImgexgDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));

        MatImgexg matImgexg = matImgexgMapper.selectByPrimaryKey(matImgexgParam.getSid());
        if (matImgexg != null) {
            matImgexgDtoMapper.updatePo(matImgexgParam, matImgexg);

            //去除企业料号/备案料号特殊字符
//            matImgexg.setFacGNo(commonService.parseCharacter(matImgexg.getFacGNo()));
//            matImgexg.setCopGNo(commonService.parseCharacter(matImgexg.getCopGNo()));

            /*
             * 判断保税中是否存在相同料号数据
             */
            boolean existBond = existBond(matImgexg.getSid(), userInfo.getCompany(), matImgexg.getGMark(), matImgexg.getFacGNo(), matImgexg.getCopEmsNo());
            if (existBond) {
                throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("该企业料号在保税已存在") + xdoi18n.XdoI18nUtil.t("无法修改"));
            }
            /*
             * 获取通关业务设置中同物料保税与非保税不得同时存在状态，不能同时存在是校验在非保税中是否存在
             */
            boolean productActivation = true;
            List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(userInfo);
            List<WarringPassExpandConfigDto> warringPassExpandConfigDtos = warringPassExpandConfigService.selectAll(warringPassConfigDtos.get(0).getSid(),userInfo);
            if (warringPassConfigDtos.size() > 0) {
                WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
                if (CommonVariable.CONFIG_1.equals(warringPassConfigDto.getUniqueness())) {
                    boolean existNoBond = existNoBond(null, userInfo.getCompany(), null, matImgexg.getFacGNo());
                    if (existNoBond) {
                        throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("该企业料号在非保税预归类中已存在") + xdoi18n.XdoI18nUtil.t("无法修改"));
                    }
                }
                if (StringUtils.equals(warringPassConfigDto.getProductActivation(),CommonVariable.CONFIG_1)){
                    productActivation = true;
                }else {
                    productActivation = false;
                }
            }

            if (warringPassExpandConfigDtos.size()>0){
                WarringPassExpandConfigDto warringPassExpandConfigDto = warringPassExpandConfigDtos.get(0);
                if (warringPassExpandConfigDto.getDecPriceDigit()!=null){
                    if (matImgexg.getDecPrice() != null) {
                        matImgexg.setDecPrice(matImgexg.getDecPrice().setScale(warringPassExpandConfigDto.getDecPriceDigit(), BigDecimal.ROUND_HALF_UP));
                    }
                }
            }

            matImgexg.setApprStatus(CommonVariable.APPR_STATUS_0);

            if (StringUtils.isBlank(matImgexg.getUnit2())) {
                matImgexg.setFactor2(null);
            }
            if (StringUtils.isBlank(matImgexg.getUnitErp())) {
                matImgexg.setFactorErp(null);
            }

            matImgexg.setKeyProductMark(keyproductMark(matImgexg,productActivation,userInfo));

            //根据HS编码带出检验检疫
            /*if (StringUtils.isNotBlank(matImgexg.getCodeTS())) {
                Map<String, String> mapTs = pCodeHolder.getTable(PCodeType.COMPLEX_CIQ, matImgexg.getCodeTS()).orElse(null);
                if (mapTs != null) {
                    if (StringUtils.isNotBlank(mapTs.get("INSPMONITORCOND"))) {
                        matImgexg.setInspmonitorcond(mapTs.get("INSPMONITORCOND"));
                    }
                }
            }*/

            /*
            校验上次历史数据，增加修改记录
             */
            JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
            MatImgexgLast matImgexgLast = matImgexgLastMapper.selectByPrimaryKey(matImgexgParam.getSid());
            if (matImgexgLast != null) {
                /*
                 * 获取企业物料上次内审通过后的数据，认为是旧数据，比较当前变更后的数据
                 */
                MatImgexg matImgexgOld = jsonObjectMapper.fromJson(matImgexgLast.getLastData(), MatImgexg.class);

                if (matImgexg.getSid() != null) {
                    MatImgexgChange matImgexgChange = new MatImgexgChange();
                    matImgexgChange.setTradeCode(userInfo.getCompany());
                    matImgexgChange.setHeadId(matImgexg.getSid());
                    matImgexgChange.setValidMark(MatVariable.VALID_MARK_0);
                    matImgexgChangeMapper.deleteByHeadId(matImgexgChange);
                }
                /*
                 * 变更记录增加
                 */
                batchInsertChanges(matImgexgOld, matImgexg, userInfo);
            }
            auditUpdateInfo(matImgexg, userInfo);
            // 更新数据
            matImgexg.setUpdateTime(new Date());
            matImgexg.setUpdateUser(userInfo.getUserNo());
            matImgexg.setUpdateUserName(userInfo.getUserName());
            int update = matImgexgMapper.updateByPrimaryKey(matImgexg);
            MatImgexgDto matImgexgDto = update > 0 ? matImgexgDtoMapper.toDto(matImgexg) : null;
            if (matImgexgDto != null) {
                if(matImgexgDto.getNetWt()!=null) {
                    matImgexgDto.setNetWt(matImgexg.getNetWt().toPlainString());
                }
                resultObject.setData(matImgexgDto);
                auditLog(matImgexg, userInfo, CommonEnum.operationsEnum.UPDATE.getValue());
            } else {
                throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("修改失败"));
            }

            return resultObject;
        } else {
            return null;
        }
    }

    private void auditUpdateInfo(MatImgexg matImgexg, UserInfoToken userInfo) {
        /*
        增加判断2021-10-25，初次制单，不管修改人，保证内审人员
        增加判断2021-11-02，修改人和上次修改人或者制单人不一致就插入修改记录
         */
        if (StringUtils.isNotBlank(matImgexg.getUpdateUser()) && !matImgexg.getUpdateUser().equals(userInfo.getUserNo())) {
            matImgexg.setUpdateUser(userInfo.getUserNo());
            matImgexg.setUpdateUserName(userInfo.getUserName());
            matImgexg.setUpdateTime(new Date());

            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(matImgexg.getSid());
            aeoAuditInfo.setApprType("Y" + matImgexg.getGMark());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), CommonVariable.APPR_STATUS_3, userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        } else if (StringUtils.isBlank(matImgexg.getUpdateUser()) && StringUtils.isNotBlank(matImgexg.getInsertUser())&&!matImgexg.getInsertUser().equals(userInfo.getUserNo())) {
            matImgexg.setUpdateUser(userInfo.getUserNo());
            matImgexg.setUpdateUserName(userInfo.getUserName());
            matImgexg.setUpdateTime(new Date());

            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(matImgexg.getSid());
            aeoAuditInfo.setApprType("Y" + matImgexg.getGMark());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), CommonVariable.APPR_STATUS_3, userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    /**
     * 批量插入修改记录
     *
     * @param oldList
     * @param newList
     * @param userInfoToken
     */
    public void batchInsertChanges(MatImgexg oldList, MatImgexg newList, UserInfoToken userInfoToken) {
        FastDateFormat df = FastDateFormat.getInstance("yyyy-MM-dd");
        Date currDate = new Date();

        if (newList.getDecPrice()!=null) {
            newList.setDecPrice(new BigDecimal(newList.getDecPrice().stripTrailingZeros().toPlainString()));
        }

        Arrays.stream(MatImgexg.class.getDeclaredFields()).forEach(fl -> {
            /*
             * 循环获取实体类中数学name不为空的列，这些列是需要记录变更的列
             */
            if (fl.getDeclaredAnnotation(javax.persistence.Transient.class) != null
                    || fl.getDeclaredAnnotation(ApiModelProperty.class) == null || StringUtils.isBlank(fl.getDeclaredAnnotation(ApiModelProperty.class).name())) {
                return;
            }
            fl.setAccessible(true);
            try {
                if (fl.get(oldList) != null || fl.get(newList) != null) {
                    /*
                     * 如果前后数据一致，则认为没变更。
                     */
                    if (fl.get(oldList) != null && fl.get(newList) != null && StringUtils.compare(fl.get(oldList).toString(), fl.get(newList).toString()) == 0) {
                        return;
                    }
                    String sid = UUID.randomUUID().toString();
                    MatImgexgChange matImgexgChange = new MatImgexgChange();
                    matImgexgChange.setSid(sid);
                    matImgexgChange.setHeadId(newList.getSid());
                    matImgexgChange.setTradeCode(userInfoToken.getCompany());
                    matImgexgChange.setGMark(newList.getGMark());
                    matImgexgChange.setChangeCode(fl.getAnnotation(ApiModelProperty.class).example());
                    matImgexgChange.setChangeName(fl.getAnnotation(ApiModelProperty.class).name());
                    if (fl.get(oldList) != null) {
                        /*
                         * PCDOE参数转换，方便查询
                         */
                        String oldValue = fl.get(oldList).toString();
                        if (fl.getAnnotation(ApiModelProperty.class).example().contains("CURR")) {
                            oldValue = commonService.convertPCode(fl.get(oldList).toString(), PCodeType.CURR_OUTDATED);
                        } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("UNIT")) {
                            oldValue = commonService.convertPCode(fl.get(oldList).toString(), PCodeType.UNIT);
                        } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("COUNTRY")) {
                            oldValue = commonService.convertPCode(fl.get(oldList).toString(), PCodeType.COUNTRY_OUTDATED);
                        } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("DATE")) {
                            oldValue = df.format(fl.get(oldList));
                        }
                        matImgexgChange.setChangeBefore(oldValue);
                    }
                    if (fl.get(newList) != null) {
                        /*
                         * PCDOE参数转换，方便查询
                         */
                        String newValue = fl.get(newList).toString();
                        if (fl.getAnnotation(ApiModelProperty.class).example().contains("CURR")) {
                            newValue = commonService.convertPCode(fl.get(newList).toString(), PCodeType.CURR_OUTDATED);
                        } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("UNIT")) {
                            newValue = commonService.convertPCode(fl.get(newList).toString(), PCodeType.UNIT);
                        } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("COUNTRY")) {
                            newValue = commonService.convertPCode(fl.get(newList).toString(), PCodeType.COUNTRY_OUTDATED);
                        } else if (fl.getAnnotation(ApiModelProperty.class).example().contains("DATE")) {
                            newValue = df.format(fl.get(newList));
                        }
                        matImgexgChange.setChangeAfter(newValue);
                    }
                    matImgexgChange.setValidMark(MatVariable.VALID_MARK_0);
                    matImgexgChange.setInsertTime(currDate);
                    matImgexgChange.setInsertUser(userInfoToken.getUserNo());
                    matImgexgChange.setInsertUserName(userInfoToken.getUserName());
                    matImgexgChange.setEmsNo(newList.getEmsNo());
                    matImgexgChange.setCopGNo(newList.getCopGNo());
                    matImgexgChange.setFacGNo(newList.getFacGNo());
                    matImgexgChange.setGMark(newList.getGMark());
                    matImgexgChange.setSerialNo(newList.getSerialNo());
                    matImgexgChangeMapper.insert(matImgexgChange);
                }
            } catch (Exception ignored) {
            }
        });
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject delete(List<String> sids, UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
        List<MatImgexg> matImgexgList = matImgexgMapper.selectBySids(sids);
        //只有制單、内审核通过数据 並且 狀態為新增
        List<MatImgexg> canNotDatas = matImgexgList.stream()
                .filter(x -> !Arrays.asList(MatVariable.DELETE_STATUS_Y).contains(x.getApprStatus()))
                .collect(Collectors.toList());
        if (canNotDatas.size() > 0) {
            throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("只有“暂存”，“内审退回” 状态数据可以刪除!"));
        }
        /*
         * 判断物料是否在进出口预录入单重使用过，使用过则无法删除
         */
        for (MatImgexg matImgexg : matImgexgList) {
            boolean existICount = existICount(userInfo.getCompany(), matImgexg.getGMark(), matImgexg.getFacGNo(), matImgexg.getEmsNo(), CommonEnum.BondMarkEnum.BOND.getCode());
            boolean existECount = existECount(userInfo.getCompany(), matImgexg.getGMark(), matImgexg.getFacGNo(), matImgexg.getEmsNo(), CommonEnum.BondMarkEnum.BOND.getCode());
            if (existICount || existECount) {
                throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("删除数据中有已使用数据") + xdoi18n.XdoI18nUtil.t("无法删除"));
            }
            Map<String, Object> pa = new HashMap<>(4);
            pa.put("tradeCode", userInfo.getCompany());
            pa.put("GMark", matImgexg.getGMark());
            pa.put("facGNo", matImgexg.getFacGNo());
            pa.put("copEmsNo", matImgexg.getCopEmsNo());
            pa.put("bondMark", CommonEnum.BondMarkEnum.BOND.getCode());
            matImgexgMapper.updateClassify(pa);
        }
        // 删除附件
        Example example = new Example(Attached.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("businessSid", sids);
        List<Attached> list = attachedMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            for (Attached attached : list) {
                fileHandler.deleteFile(attached.getFileName());
            }
        }
        matImgexgMapper.deleteBySids(sids);
        for (MatImgexg matImgexg : matImgexgList) {
            auditLog(matImgexg, userInfo, CommonEnum.operationsEnum.DELETE.getValue());
        }
        return resultObject;

    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<MatImgexgDto> selectAll(MatImgexgParam exportParam, UserInfoToken userInfo) {
        String copGNo = exportParam.getCopGNo();
        if (StringUtils.isNotBlank(copGNo)) {
            if (copGNo.contains(",")) {
                List<String> copGNoList = new ArrayList<>();
                List<String> tmpList = new ArrayList<>(Arrays.asList(copGNo.split(",")));
                if (CollectionUtils.isNotEmpty(tmpList)) {
                    tmpList.stream().forEach(cgNo -> {
                        if (StringUtils.isNotBlank(cgNo)) {
                            copGNoList.add(cgNo.trim());
                        }
                    });
                }
                if (CollectionUtils.isNotEmpty(copGNoList)) {
                    exportParam.setCopGNos(copGNoList);
                    exportParam.setCopGNo("");
                }
            }
        }
        String facGNo = exportParam.getFacGNo();
        if (StringUtils.isNotBlank(facGNo)) {
            if (facGNo.contains(",")) {
                List<String> facGNoList = new ArrayList<>();
                List<String> tmpList = new ArrayList<>(Arrays.asList(facGNo.split(",")));
                if (CollectionUtils.isNotEmpty(tmpList)) {
                    tmpList.stream().forEach(cgNo -> {
                        if (StringUtils.isNotBlank(cgNo)) {
                            facGNoList.add(cgNo.trim());
                        }
                    });
                }
                if (CollectionUtils.isNotEmpty(facGNoList)) {
                    exportParam.setFacGNos(facGNoList);
                    exportParam.setFacGNo("");
                }
            }
        }
        MatImgexg matImgexg = matImgexgDtoMapper.toPo(exportParam);
        // 判断是否有中文 没有中文认为是code，有中文认为是name??
        if (exportParam.getUserName() != null) {
            if (CommonVariable.checkname(exportParam.getUserName())) {
                matImgexg.setInsertUser(exportParam.getUserName());
            } else {
                matImgexg.setInsertUserName(exportParam.getUserName());
            }
        }
        matImgexg.setTradeCode(userInfo.getCompany());
        List<MatImgexgDto> matImgExgDtos = new ArrayList<>();
        List<MatImgexg> matImgExgs = matImgexgMapper.getList(matImgexg);
        if (CollectionUtils.isNotEmpty(matImgExgs)) {
            matImgExgDtos = matImgExgs.stream().map(head -> {
                MatImgexgDto dto = matImgexgDtoMapper.toDto(head);
                if (dto.getNetWt() != null) {
                    dto.setNetWt(head.getNetWt().toPlainString());
                }
                return dto;
            }
            ).collect(Collectors.toList());
        }
        return matImgExgDtos;
    }
    //endregion

    /**
     * 功能描述: 获取所有备案通过可使用的备案号
     *
     * @return
     * @auther: 沈振宇
     * @date: 2018-10-26
     */
    public ResultObject selectEmsNo(UserInfoToken userInfo) {
        Map<String, Object> pa = new HashMap<>(1);
        pa.put("tradeCode", userInfo.getCompany());
        List<Map<String, Object>> matEmsNos = matImgexgMapper.selectEmsNo(pa);
        return ResultObject.createInstance(true, "", matEmsNos);
    }
    public ResultObject selectEmlEmsNo(UserInfoToken userInfo) {
        Map<String, Object> pa = new HashMap<>(1);
        pa.put("tradeCode", userInfo.getCompany());
        pa.put("emsType", "1");
        List<Map<String, Object>> matEmsNos = matImgexgMapper.selectEmsNo(pa);
        return ResultObject.createInstance(true, "", matEmsNos);
    }
    /**
     * 企业物料内审分页查询
     *
     * @param matImgexgParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<MatImgexgDto>> selectListForMatAudit(MatImgexgParam matImgexgParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        MatImgexg matImgexg = matImgexgDtoMapper.toPo(matImgexgParam);
        //判断是否有中文
        if (matImgexgParam.getUserName() != null) {
            if (CommonVariable.checkname(matImgexgParam.getUserName())) {
                matImgexg.setInsertUser(matImgexgParam.getUserName());
            } else {
                matImgexg.setInsertUserName(matImgexgParam.getUserName());
            }
        }
        matImgexg.setTradeCode(userInfo.getCompany());
        matImgexg.setUserNo(userInfo.getUserNo());
        Page<MatImgexg> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> matImgexgMapper.selectListForMatAudit(matImgexg));
        List<MatImgexgDto> matImgexgDtos = page.getResult().stream().map(head -> {
            MatImgexgDto dto = matImgexgDtoMapper.toDto(head);
            if(dto.getNetWt()!=null) {
                dto.setNetWt(head.getNetWt().toPlainString());
            }
            return dto;
        }).collect(Collectors.toList());
        return (ResultObject<List<MatImgexgDto>>) ResultObject.createInstance(matImgexgDtos, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 功能描述 内审-根据查询条件
     *
     * @param matImgexgParam 1
     * @param userInfo       2
     * @return java.util.List<com.dcjet.cs.dto.mat.MatImgexgDto>
     * <AUTHOR>
     * @date 2020/2/27
     * @version 1.0
     */
    public List<MatImgexgDto> selectAllForMatAudit(MatImgexgParam matImgexgParam, UserInfoToken userInfo) {
        // 启用分页查询
        MatImgexg matImgexg = matImgexgDtoMapper.toPo(matImgexgParam);
        matImgexg.setTradeCode(userInfo.getCompany());
        matImgexg.setUserNo(userInfo.getUserNo());
        List<MatImgexgDto> matImgexgDtos = new ArrayList<>();
        List<MatImgexg> matImgexgs = matImgexgMapper.selectListForMatAudit(matImgexg);
        if (CollectionUtils.isNotEmpty(matImgexgs)) {
            matImgexgDtos = matImgexgs.stream().map(head -> {
                MatImgexgDto dto = matImgexgDtoMapper.toDto(head);
                if(dto.getNetWt()!=null) {
                    dto.setNetWt(head.getNetWt().toPlainString());
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return matImgexgDtos;
    }

    /**
     * 功能描述 物料备案根据查询条件发送内审接口/批量内审通过
     *
     * @param matImgexgParam   1
     * @param matImgexgDtoList 2
     * @param apprStatus       3
     * @param userInfo         4
     * @return void
     * <AUTHOR>
     * @date 2021-07-09
     * @version 1.0
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAuditByList(MatImgexgParam matImgexgParam, List<MatImgexgDto> matImgexgDtoList, String apprStatus, UserInfoToken userInfo) {
        // 启用分页查询
        MatImgexg matImgexg = matImgexgDtoMapper.toPo(matImgexgParam);
        matImgexg.setTradeCode(userInfo.getCompany());
        matImgexg.setUserNo(userInfo.getUserNo());
        String lockMark = UUID.randomUUID().toString();
        matImgexg.setLockMark(lockMark);
        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        /*
         * apprStatus为2的时候为发送内审，否则是内审通过
         */
        if (StringUtils.isNotBlank(apprStatus) && CommonVariable.APPR_STATUS_2.equals(apprStatus)) {
            matImgexgMapper.updateByList(matImgexg);
            matImgexg.setSendUser(userInfo.getUserNo());
            matImgexg.setSendUserName(userInfo.getUserName());
            matImgexg.setDeclareDate(new Date());
        } else {
            matImgexgMapper.updateAuditByList(matImgexg);
            matImgexg.setApprUser(userInfo.getUserNo());
            matImgexg.setApprUserName(userInfo.getUserName());
            matImgexg.setApprDate(new Date());
        }

        matImgexg.setInsertUser(userInfo.getUserNo());
        matImgexg.setInsertUserName(userInfo.getUserName());
        matImgexg.setInsertTime(new Date());
        matImgexg.setApprStatus(apprStatus);
        if (StringUtils.isNotBlank(apprStatus) && CommonVariable.APPR_STATUS_8.equals(apprStatus)) {
            List<WarringPassConfigDto> configs = warringPassConfigService.selectAll(userInfo);
            if (CollectionUtils.isNotEmpty(configs)) {
                WarringPassConfigDto passConfig = configs.get(0);
                if (StringUtils.equals(passConfig.getReviewComments(), ConstantsStatus.STATUS_0)){
                    matImgexg.setNote(xdoi18n.XdoI18nUtil.t("已对单证涉及的价格、归类、原产地、数量、品名、规格等内容进行内审"));
                }
            }
        } else {
            matImgexg.setNote(matImgexgParam.getApprNote());
        }
        matImgexgMapper.auditByList(matImgexg);

        /*
         * 内审通过后，将现有物料数据存入物料历史数据，物料变更时需要比较做修改记录
         */
        if (StringUtils.isNotBlank(apprStatus) && CommonVariable.APPR_STATUS_8.equals(apprStatus)) {
            for (MatImgexgDto matImgexgDto : matImgexgDtoList) {

                matImgexgLastMapper.deleteByPrimaryKey(matImgexgDto.getSid());

                MatImgexg matImgexg2 = matImgexgDtoMapper.toBPo(matImgexgDto);
                MatImgexgLast matImgexgLast = new MatImgexgLast();
                matImgexgLast.setSid(matImgexgDto.getSid());
                matImgexgLast.setInsertTime(new Date());
                matImgexgLast.setInsertUser(userInfo.getUserNo());
                matImgexgLast.setInsertUserName(userInfo.getUserName());
                matImgexgLast.setTradeCode(userInfo.getCompany());
                matImgexgLast.setLastData(jsonObjectMapper.toJson(matImgexg2));

                try {
                    matImgexgLastMapper.insert(matImgexgLast);
                } catch (Exception e) {
                    matImgexgLastMapper.updateByPrimaryKey(matImgexgLast);
                }

                /*将最新的修改记录设置成有效*/
                Example example = new Example(MatImgexgChange.class);
                example.createCriteria().andEqualTo("headId", matImgexgDto.getSid());
                MatImgexgChange matImgexgChange = new MatImgexgChange();
                matImgexgChange.setValidMark(MatVariable.VALID_MARK_1);
                matImgexgChangeMapper.updateByExampleSelective(matImgexgChange, example);

            }
        }
    }

    /**
     * 功能描述: 设置物料状态 0-正常 1-过滤
     *
     * <AUTHOR> 朱正东
     * @version :   1.0
     * @date： 2018-11-20
     * @param: model 实体(List)
     * @param: loginUserNo 当前用户
     * @return:
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject satMatStatus(MatStatusParam matStatusParam, UserInfoToken userInfo) {

        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("设置物料状态成功"));
        String loginUserNo = userInfo.getUserNo();
        for (String sid : matStatusParam.getSidList()) {
            MatImgexg matImgexg = matImgexgMapper.selectByPrimaryKey(sid);
            if (MatVariable.STATUS_0.equals(matStatusParam.getStatus())) {
                matImgexg.setApprStatus(CommonVariable.APPR_STATUS_0);
                boolean existBond = existBond(matImgexg.getSid(), userInfo.getCompany(), matImgexg.getGMark(), matImgexg.getFacGNo(), matImgexg.getCopEmsNo());
                if (existBond) {
                    throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("该企业料号在非保税预归类中已存在") + xdoi18n.XdoI18nUtil.t("无法修改"));
                }
            }
            matImgexg.setStatus(matStatusParam.getStatus());
            matImgexg.setUpdateUser(loginUserNo);
            matImgexg.setUpdateUserName(userInfo.getUserName());
            matImgexg.setUpdateTime(new Date());

            //启用停用原因记录
            MatStatusLog log = new MatStatusLog();
            String uid = UUID.randomUUID().toString();
            log.setSid(uid);
            log.setTradeCode(userInfo.getCompany());
            log.setInsertUser(userInfo.getUserNo());
            log.setInsertUserName(userInfo.getUserName());
            log.setInsertTime(new Date());
            log.setNote(matStatusParam.getNote());
            log.setHeadId(matImgexg.getSid());
            log.setStatus(matStatusParam.getStatus());
            log.setType(matImgexg.getGMark());
            matStatusLogMapper.insert(log);

            matImgexgMapper.updateByPrimaryKeySelective(matImgexg);
        }
        return resultObject;
    }

    /**
     * 比例因子获取
     *
     * @param factorRateParam
     * @param userInfo
     * @return
     */
    public ResultObject getFactorRate(FactorRateParam factorRateParam, UserInfoToken userInfo) {
        Map<String, Object> pa = new HashMap<>(8);
        //pa.put("sid", factorRateParam.getSid());
        pa.put("bondMark", factorRateParam.getBondMark());
        pa.put("GMark", factorRateParam.getGmark());
        pa.put("emsNo", factorRateParam.getEmsNo());
        pa.put("facGNo", factorRateParam.getFacGNo());
        pa.put("origin", factorRateParam.getOrigin());
        pa.put("dest1", factorRateParam.getDest1());
        pa.put("dest2", factorRateParam.getDest2());
        pa.put("tradeCode", userInfo.getCompany());
        BigDecimal factorRate = matImgexgMapper.getFactorRate(pa);
        return ResultObject.createInstance(true, "", factorRate);
    }

    /**
     * 功能描述: 企业物料提取待归类数据
     *
     * @return
     * @auther: 沈振宇
     * @date: 2018-10-26
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<MatImgexgDto> pickUp(String gMark, UserInfoToken userInfo) {
        Map<String, Object> map = new HashMap<>(3);
        map.put("userNo", userInfo.getUserNo());
        map.put("tradeCode", userInfo.getCompany());
        map.put("gMark", gMark);
        matImgexgMapper.pickUpClassify(map);
        int takeCount = matImgexgMapper.pickUp(map);
        return ResultObject.createInstance(true, takeCount == 0 ? xdoi18n.XdoI18nUtil.t("没有可提取数据") : xdoi18n.XdoI18nUtil.t("成功提取数据") + takeCount + xdoi18n.XdoI18nUtil.t("条"));
    }

    /**
     * 获取企业料号列表
     */
    public ResultObject getFacGNoList(String tradeMode, UserInfoToken userInfo) {
        ResultObject obj = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        String tModel = CommonEnum.tModeEnum.getValue(tradeMode);
        List<String> result = matImgexgMapper.getFacGNoList(tModel, userInfo.getCompany());
        obj.setData(result);
        return obj;
    }

    //region 物料中心校验料号是否存在

    /**
     * 校验企业料号是否存在（进出口中）
     *
     * @param tradeCode
     * @param emsNo
     * @param gMark
     * @param facGNo
     * @return
     */
    public boolean existsFacGNo(String tradeCode, String emsNo, String gMark, String facGNo) {
        Integer cnt = matImgexgMapper.isExistsFacGNo(tradeCode, emsNo, gMark, facGNo);
        return cnt > 0;
    }

    /**
     * 功能描述 判断保税物料中是否存在该企业料号
     *
     * @param sid       唯一键
     * @param tradeCode 企业10位代码
     * @param GMark     物料标志
     * @param facGNo    企业料号
     * @param copEmsNo  手账册内部编号
     * @return boolean
     * <AUTHOR>
     * @date 2020/2/20
     * @version 1.0
     */
    public boolean existBond(String sid, String tradeCode, String GMark, String facGNo, String copEmsNo) {
        Map<String, Object> param = new HashMap<>(7);
        param.put("sid", sid);
        param.put("tradeCode", tradeCode);
        param.put("GMark", GMark);
        param.put("facGNo", facGNo);
        param.put("copEmsNo", copEmsNo);
        int sum = matImgexgMapper.getBond(param);
        return sum > 0;
    }

    /**
     * 功能描述 判断备案料号是否存在
     *
     * @param sid       唯一键
     * @param tradeCode 企业10位代码
     * @param GMark     物料标志
     * @param copGNo    备案料号
     * @param copEmsNo  手账册内部编号
     * @return boolean
     * <AUTHOR>
     * @date 2020/2/20
     * @version 1.0
     */
    public boolean existOrgBond(String sid, String tradeCode, String GMark, String copGNo, String copEmsNo) {
        Map<String, Object> param = new HashMap<>(7);
        param.put("sid", sid);
        param.put("tradeCode", tradeCode);
        param.put("GMark", GMark);
        param.put("copGNo", copGNo);
        param.put("copEmsNo", copEmsNo);
        int sum = matImgexgMapper.getOrgBond(param);
        return sum > 0;
    }

    /**
     * 功能描述 判断非保税物料中是否存在该企业料号
     *
     * @param sid       唯一键
     * @param tradeCode 企业10位编码
     * @param GMark     物料类型
     * @param copGNo    料号
     * @return boolean
     * <AUTHOR>
     * @date 2020/2/20
     * @version 1.0
     */
    public boolean existNoBond(String sid, String tradeCode, String GMark, String copGNo) {
        Map<String, Object> param = new HashMap<>(4);
        param.put("sid", sid);
        param.put("tradeCode", tradeCode);
        param.put("GMark", GMark);
        param.put("copGNo", copGNo);
        int sum = matImgexgMapper.getNoBond(param);
        return sum > 0;
    }

    /**
     * 功能描述
     *
     * @param tradeCode 1
     * @param GMark     2
     * @param facGNo    3
     * @param emsNo     4
     * @param bondMark  5
     * @return boolean
     * <AUTHOR>
     * @date 2020/2/20
     * @version 1.0
     */
    public boolean existICount(String tradeCode, String GMark, String facGNo, String emsNo, String bondMark) {
        Map<String, Object> param = new HashMap<>(5);
        param.put("tradeCode", tradeCode);
        param.put("GMark", GMark);
        param.put("facGNo", facGNo);
        param.put("emsNo", emsNo);
        param.put("bondMark", bondMark);
        int sum = matImgexgMapper.getICount(param);
        return sum > 0;
    }

    /**
     * 功能描述
     *
     * @param tradeCode 1
     * @param GMark     2
     * @param facGNo    3
     * @param emsNo     4
     * @param bondMark  5
     * @return boolean
     * <AUTHOR>
     * @date 2020/2/20
     * @version 1.0
     */
    public boolean existECount(String tradeCode, String GMark, String facGNo, String emsNo, String bondMark) {
        Map<String, Object> param = new HashMap<>(5);
        param.put("tradeCode", tradeCode);
        param.put("GMark", GMark);
        param.put("facGNo", facGNo);
        param.put("emsNo", emsNo);
        param.put("bondMark", bondMark);
        int sum = matImgexgMapper.getECount(param);
        return sum > 0;
    }
    //endregion

    /**
     * 功能描述 复制功能，暂时不用
     *
     * @param matImgexgParam 1
     * @param userInfo       2
     * @return com.xdo.domain.ResultObject
     * <AUTHOR>
     * @date 2020/2/21
     * @version 1.0
     */
    public ResultObject copy(MatImgexgParam matImgexgParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("复制成功"));

        MatImgexg matImgexg = matImgexgMapper.selectList(matImgexgParam.getSid(), userInfo.getCompany());
        matImgexg.setFacGNo(matImgexgParam.getFacGNo());

        if (CommonEnum.GMarkEnum.IMG.getCode().equals(matImgexg.getGMark()) || CommonEnum.GMarkEnum.EXG.getCode().equals(matImgexg.getGMark())) {
            /*
             * 规范固定字段
             */
            String sid = UUID.randomUUID().toString();

            boolean existBond = existBond(sid, userInfo.getCompany(), matImgexg.getGMark(), matImgexg.getFacGNo(), matImgexg.getCopEmsNo());
            if (existBond) {
                throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("该企业料号在保税已存在") + xdoi18n.XdoI18nUtil.t("无法复制"));
            }
            List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(userInfo);
            if (warringPassConfigDtos.size() > 0) {
                WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
                if (CommonVariable.CONFIG_1.equals(warringPassConfigDto.getUniqueness())) {
                    boolean existNoBond = existNoBond(null, userInfo.getCompany(), null, matImgexg.getFacGNo());
                    if (existNoBond) {
                        throw new ErrorException(CommonVariable.EXCEPTION, xdoi18n.XdoI18nUtil.t("该企业料号在非保税预归类中已存在") + xdoi18n.XdoI18nUtil.t("无法复制"));
                    }
                }
            }

            matImgexg.setSid(sid);
            matImgexg.setApprStatus(CommonVariable.APPR_STATUS_0);
            matImgexg.setStatus(MatVariable.STATUS_0);
            matImgexg.setInsertUser(userInfo.getUserNo());
            matImgexg.setInsertUserName(userInfo.getUserName());
            matImgexg.setInsertTime(new Date());
            matImgexg.setTradeCode(userInfo.getCompany());
            matImgexg.setUpdateUser(null);
            matImgexg.setUpdateUserName(null);
            matImgexg.setUpdateTime(null);
            matImgexg.setSendUser(null);
            matImgexg.setSendUserName(null);
            matImgexg.setDeclareDate(null);
            matImgexg.setApprUser(null);
            matImgexg.setApprUserName(null);
            matImgexg.setApprDate(null);

            // 新增数据
            int insertStatus = matImgexgMapper.insert(matImgexg);
            MatImgexgDto matImgexgDto = insertStatus > 0 ? matImgexgDtoMapper.toDto(matImgexg) : null;
            if (matImgexgDto != null) {
                resultObject.setData(matImgexgDto);
            } else {
                resultObject.setCode(400);
                resultObject.setSuccess(false);
                resultObject.setMessage(xdoi18n.XdoI18nUtil.t("新增失败"));
                return resultObject;
            }
        } else {
            resultObject.setCode(400);
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("只可以复制料件和成品的数据"));
            return resultObject;
        }
        return resultObject;
    }

    /**
     * 获取企业料件的ERP比例因子
     *
     * @param facGNos
     * @param userInfo
     * @return
     */
    public Map<String, List<ErpFactorDto>> getErpFactorByFacGNos(List<String> facGNos, String gMark, UserInfoToken userInfo) {
        List<ErpFactorDto> list = matImgexgMapper.getErpFactor(facGNos, gMark, userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.groupingBy(ErpFactorDto::getFacGNo));
        }
        return null;
    }

    /**
     * 提供给其它产品调用(成品料件大小料号及比例因子接口)
     */
    public ResultObject<List<MatImgexgForApi>> getMatImgExgByCopEmsNo(String copEmsNo, PageParam pageParam, String tradeCode) {
        ResultObject<List<MatImgexgForApi>> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        if (StringUtils.isBlank(copEmsNo)) {
            resultObject.setCode(400);
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("手账册内部编号copEmsNo不能为空"));
            return resultObject;
        }
        int total;
        try {
            Page<MatImgexg> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                    .doSelectPage(() -> matImgexgMapper.getMatImgExgByCopEmsNo(copEmsNo, tradeCode));
            List<MatImgexg> matImgexgList = page.getResult();
            // 接口对象
            List<MatImgexgForApi> matImgexgForApiList = new ArrayList<>(matImgexgList.size());
            if (CollectionUtils.isNotEmpty(matImgexgList)) {
                // 查询总数
                MatImgexg matImgexg = new MatImgexg();
                matImgexg.setCopEmsNo(copEmsNo);
                matImgexg.setTradeCode(tradeCode);
                total = matImgexgMapper.selectCount(matImgexg);
                // 初始化企业自定义参数库
                BiCustomerParams biCustomerParams = new BiCustomerParams();
                biCustomerParams.setParamsType(PCodeType.UNIT);
                biCustomerParams.setTradeCode(tradeCode);
                List<BiCustomerParams> biCustomerParamsList = biCustomerParamsMapper.getListByParams(biCustomerParams);
                // 转为Map
                Map<String, List<BiCustomerParams>> customerParamsMap = biCustomerParamsList.stream().collect(Collectors.groupingBy(BiCustomerParams::getParamsCode));
                Map<String, BiCustomerParams> oneMap = new HashMap<>(customerParamsMap.size());
                // 去重处理
                for (Map.Entry<String, List<BiCustomerParams>> entry : customerParamsMap.entrySet()) {
                    if (entry.getValue().size() > 1) {
                        for (BiCustomerParams s : entry.getValue()) {
                            if (s.getTradeCode() != null && CommonVariable.TRADE_CODE_9999999999.equals(s.getTradeCode())) {
                                oneMap.put(entry.getKey(), s);
                                break;
                            } else {
                                oneMap.put(entry.getKey(), s);
                            }
                        }
                    } else {
                        oneMap.put(entry.getKey(), entry.getValue().get(0));
                    }
                }
                // 初始化配置比例因子
                BiTransform biTransform = new BiTransform();
                biTransform.setTradeCode(tradeCode);
                biTransform.setType("UNIT");
                List<BiTransform> biTransformList = biClientInformationMapper.getTransformInfo(biTransform);
                Map<String, BiTransform> transformMap = biTransformList.stream().collect(Collectors.toMap(BiTransform::getOrigin, d -> d, (oldValue, newValue) -> newValue));
                // 转换ERP比例因子
                matImgexgList.forEach(s -> {
                    if (oneMap.size() > 0 && s.getUnitErp() != null) {
                        if (oneMap.get(s.getUnitErp()) != null) {
                            String customParamCode = oneMap.get(s.getUnitErp()).getCustomParamCode();
                            if (customParamCode == null) {
                                String pcodeValue = commonService.convertPCode(s.getUnitErp(), PCodeType.UNIT);
                                if (StringUtils.isNotBlank(pcodeValue)) {
                                    BiTransform transform = transformMap.get(pcodeValue);
                                    if (transform != null) {
                                        s.setFactorErp(new BigDecimal(transform.getRate()));
                                    }
                                }
                            } else {
                                BiTransform transform = transformMap.get(customParamCode);
                                if (transform != null) {
                                    s.setFactorErp(new BigDecimal(transform.getRate()));
                                }
                            }
                        }
                    }
                });
                matImgexgList.forEach(s -> matImgexgForApiList.add(matImgexgDtoMapper.toMatApiDto(s)));
                resultObject.setData(matImgexgForApiList);
            } else {
                resultObject.setCode(400);
                resultObject.setSuccess(false);
                resultObject.setMessage(xdoi18n.XdoI18nUtil.t("不存在料件/成品备案通过记录"));
                return resultObject;
            }
        } catch (Exception ex) {
            logger.error("调用企业料件/成品大小料号及比例因子接口出错：" + ex.getMessage());
            resultObject.setCode(500);
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("调用失败,原因：") + ex.getMessage());
            return resultObject;
        }
        resultObject.setTotal(total);
        resultObject.setPageIndex(pageParam.getPage());
        return resultObject;
    }

    /**
     * 提供给其它产品调用(成品料件大小料号及比例因子接口--直接取ERP比例因子)
     */
    public ResultObject<List<MatImgexgForApi>> getMatImgExgForWo(MatImgexgParamForApi param, PageParam pageParam) {
        ResultObject<List<MatImgexgForApi>> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        int total;
        try {
            Page<MatImgexg> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                    .doSelectPage(() -> matImgexgMapper.getMatImgExgForWo(param));
            List<MatImgexg> matImgexgList = page.getResult();
            List<MatImgexgForApi> matImgexgForApiList = new ArrayList<>(matImgexgList.size());
            matImgexgList.forEach(s -> matImgexgForApiList.add(matImgexgDtoMapper.toMatApiDto(s)));
            resultObject.setData(matImgexgForApiList);
            // 查询总数
            total = matImgexgMapper.selectMatImgExgForWoCount(param);
        } catch (ErrorException ex) {
            logger.error("调用企业料件/成品大小料号及比例因子接口出错：" + ex.getMessage());
            resultObject.setCode(500);
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("调用失败,原因：") + ex.getMessage());
            return resultObject;
        }
        resultObject.setTotal(total);
        resultObject.setPageIndex(pageParam.getPage());
        return resultObject;
    }

    /**
     * 整体更新监管条件和检验检疫条件
     *
     * @param userInfo
     * @return
     */
    public void updateCredential(UserInfoToken userInfo) {
//        List<ErpFactorDto> list = matImgexgMapper.getErpFactor(facGNos, gMark, userInfo.getCompany());
//        if(CollectionUtils.isNotEmpty(list)) {
//            return list.stream().collect(Collectors.groupingBy(ErpFactorDto::getFacGNo));
//        }
//        return null;
    }
}
