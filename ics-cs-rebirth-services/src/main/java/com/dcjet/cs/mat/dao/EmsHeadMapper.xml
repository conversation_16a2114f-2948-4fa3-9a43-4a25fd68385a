<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.mat.dao.EmsHeadMapper">
    <resultMap id="emsHeadResultMap" type="com.dcjet.cs.mat.model.EmsHead">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="COP_EMS_NO" property="copEmsNo" jdbcType="VARCHAR" />
		<result column="PRE_SEQ_NO" property="preSeqNo" jdbcType="VARCHAR" />
		<result column="EMS_NO" property="emsNo" jdbcType="VARCHAR" />
		<result column="VALID_DATE" property="validDate" jdbcType="DATE" />
		<result column="APPR_USER" property="apprUser" jdbcType="VARCHAR" />
		<result column="APPR_DATE" property="apprDate" jdbcType="TIMESTAMP" />
		<result column="APPR_STATUS" property="apprStatus" jdbcType="VARCHAR" />
		<result column="APPR_USER_NAME" property="apprUserName" jdbcType="VARCHAR" />
		<result column="SEND_API_DATE" property="sendApiDate" jdbcType="TIMESTAMP" />
		<result column="SEND_USER" property="sendUser" jdbcType="VARCHAR" />
		<result column="SEND_USER_NAME" property="sendUserName" jdbcType="VARCHAR" />
		<result column="SEND_API_TASK_ID" property="sendApiTaskId" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="IS_PASS" property="isPass" jdbcType="VARCHAR" />
		<result column="BILL_FLAG" property="billFlag" jdbcType="VARCHAR" />
        <result column="BILL_FLAG_NAME" property="billFlagName" jdbcType="VARCHAR" />
		<result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR" />
		<result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
		<result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR" />
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
        <result column="NOTE" property="note" jdbcType="VARCHAR" />
        <result column="SEND_APPR_DATE" property="sendApprDate" jdbcType="TIMESTAMP" />
        <result column="SEND_APPR_USER" property="sendApprUser" jdbcType="VARCHAR" />
        <result column="SEND_APPR_USER_NAME" property="sendApprUserName" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,COP_EMS_NO
     ,PRE_SEQ_NO
     ,EMS_NO
     ,VALID_DATE
     ,APPR_USER
     ,APPR_DATE
     ,APPR_STATUS
     ,APPR_USER_NAME
     ,SEND_API_DATE
     ,SEND_USER
     ,SEND_USER_NAME
     ,SEND_API_TASK_ID
     ,TRADE_CODE
     ,IS_PASS
     ,BILL_FLAG
     ,DATA_SOURCE
     ,INSERT_USER
     ,INSERT_USER_NAME
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_USER_NAME
     ,UPDATE_TIME
     ,NOTE
     ,SEND_APPR_DATE
     ,SEND_APPR_USER
     ,SEND_APPR_USER_NAME
    </sql>
    <sql id="condition">
    <if test="copEmsNo != null and copEmsNo != ''">
        and COP_EMS_NO like concat('%', concat(#{copEmsNo}, '%'))
	</if>
    <if test="preSeqNo != null and preSeqNo != ''"> 
		and PRE_SEQ_NO = #{preSeqNo}
	</if>
    <if test="emsNo != null and emsNo != ''"> 
		and EMS_NO like concat('%', concat(#{emsNo}, '%'))
	</if>
    <if test="apprUser != null and apprUser != ''">
		and APPR_USER = #{apprUser}
	</if>
    <if test="apprStatus != null and apprStatus != ''">
		and APPR_STATUS = #{apprStatus}
	</if>
	<!-- 内审页面查询时,控制只能查询[-1 内审退回 2 待审核 8 内审通过]的记录 -->
    <if test='type == "2"'>
        and APPR_STATUS IN ('-1','2','8')
        <if test='_databaseId == "postgresql" '>
            AND COALESCE(SEND_APPR_USER, INSERT_USER) != #{userNo,jdbcType=VARCHAR}
        </if>
        <if test='_databaseId != "postgresql" '>
            AND NVL(SEND_APPR_USER,INSERT_USER) != #{userNo,jdbcType=VARCHAR}
        </if>
    </if>
    <if test="apprUserName != null and apprUserName != ''"> 
		and APPR_USER_NAME = #{apprUserName}
	</if>
    <if test="sendUser != null and sendUser != ''">
		and SEND_USER = #{sendUser}
	</if>
    <if test="sendUserName != null and sendUserName != ''"> 
		and SEND_USER_NAME = #{sendUserName}
	</if>
    <if test="sendApiTaskId != null and sendApiTaskId != ''"> 
		and SEND_API_TASK_ID = #{sendApiTaskId}
	</if>
		and TRADE_CODE = #{tradeCode}
    <if test="isPass != null and isPass != ''">
		and IS_PASS = #{isPass}
	</if>
    <if test="billFlag != null and billFlag != ''"> 
		and BILL_FLAG = #{billFlag}
	</if>
    <if test="dataSource != null and dataSource != ''"> 
		and DATA_SOURCE = #{dataSource}
	</if>
        <if test='_databaseId == "postgresql" '>
            <if test="validDateFrom != null and validDateFrom != ''">
                <![CDATA[ and VALID_DATE >= to_timestamp(#{validDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="validDateTo != null and validDateTo != ''">
                <![CDATA[ and VALID_DATE < to_timestamp(#{validDateTo}, 'yyyy-MM-dd hh24:mi:ss')+ INTERVAL '1 day']]>
            </if>
            <if test="apprDateFrom != null and apprDateFrom != ''">
                <![CDATA[ and APPR_DATE >= to_timestamp(#{apprDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="apprDateTo != null and apprDateTo != ''">
                <![CDATA[ and APPR_DATE < to_timestamp(#{apprDateTo}, 'yyyy-MM-dd hh24:mi:ss')+ INTERVAL '1 day']]>
            </if>
            <if test="sendApiDateFrom != null and sendApiDateFrom != ''">
                <![CDATA[ and SEND_API_DATE >= to_timestamp(#{sendApiDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="sendApiDateTo != null and sendApiDateTo != ''">
                <![CDATA[ and SEND_API_DATE < to_timestamp(#{sendApiDateTo}, 'yyyy-MM-dd hh24:mi:ss')+ INTERVAL '1 day']]>
            </if>
            <if test="sendApprDateFrom != null and sendApprDateFrom != ''">
                <![CDATA[ and SEND_APPR_DATE >= to_timestamp(#{sendApprDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="sendApprDateTo != null and sendApprDateTo != ''">
                <![CDATA[ and SEND_APPR_DATE < to_timestamp(#{sendApprDateTo}, 'yyyy-MM-dd hh24:mi:ss')+ INTERVAL '1 day']]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="validDateFrom != null and validDateFrom != ''">
                <![CDATA[ and VALID_DATE >= to_date(#{validDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="validDateTo != null and validDateTo != ''">
                <![CDATA[ and VALID_DATE < to_date(#{validDateTo}, 'yyyy-MM-dd hh24:mi:ss')+1]]>
            </if>
            <if test="apprDateFrom != null and apprDateFrom != ''">
                <![CDATA[ and APPR_DATE >= to_date(#{apprDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="apprDateTo != null and apprDateTo != ''">
                <![CDATA[ and APPR_DATE < to_date(#{apprDateTo}, 'yyyy-MM-dd hh24:mi:ss')+1]]>
            </if>
            <if test="sendApiDateFrom != null and sendApiDateFrom != ''">
                <![CDATA[ and SEND_API_DATE >= to_date(#{sendApiDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="sendApiDateTo != null and sendApiDateTo != ''">
                <![CDATA[ and SEND_API_DATE < to_date(#{sendApiDateTo}, 'yyyy-MM-dd hh24:mi:ss')+1]]>
            </if>
            <if test="sendApprDateFrom != null and sendApprDateFrom != ''">
                <![CDATA[ and SEND_APPR_DATE >= to_date(#{sendApprDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="sendApprDateTo != null and sendApprDateTo != ''">
                <![CDATA[ and SEND_APPR_DATE < to_date(#{sendApprDateTo}, 'yyyy-MM-dd hh24:mi:ss')+1]]>
            </if>
        </if>
    </sql>
    <sql id="conditionForConsumeAudit">
        t.TRADE_CODE = #{tradeCode} AND a.APPR_TYPE = 'CONSUME'
        <if test="copEmsNo != null and copEmsNo != ''">
            and t.COP_EMS_NO like concat('%', concat(#{copEmsNo}, '%'))
        </if>
        <if test="emsNo != null and emsNo != ''">
            and t.EMS_NO like concat('%', concat(#{emsNo}, '%'))
        </if>
        <if test="apprStatus != null and apprStatus != ''">
            and a.STATUS = #{apprStatus}
        </if>
        <if test='_databaseId == "postgresql" '>
            <if test="apprDateFrom != null and apprDateFrom != ''">
                <![CDATA[ and a.APPR_DATE >= to_timestamp(#{apprDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="apprDateTo != null and apprDateTo != ''">
                <![CDATA[ and a.APPR_DATE < to_timestamp(#{apprDateTo}, 'yyyy-MM-dd hh24:mi:ss')+ INTERVAL '1 day']]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="apprDateFrom != null and apprDateFrom != ''">
                <![CDATA[ and a.APPR_DATE >= to_date(#{apprDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="apprDateTo != null and apprDateTo != ''">
                <![CDATA[ and a.APPR_DATE < to_date(#{apprDateTo}, 'yyyy-MM-dd hh24:mi:ss')+1]]>
            </if>
        </if>

        <if test='_databaseId == "postgresql" '>
            <if test="insertTimeFrom != null and insertTimeFrom != ''">
                <![CDATA[ and t.INSERT_TIME >= to_timestamp(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="insertTimeTo != null and insertTimeTo != ''">
                <![CDATA[ and t.INSERT_TIME < to_timestamp(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="insertTimeFrom != null and insertTimeFrom != ''">
                <![CDATA[ and t.INSERT_TIME >= to_date(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="insertTimeTo != null and insertTimeTo != ''">
                <![CDATA[ and t.INSERT_TIME < to_date(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
            </if>
        </if>

    </sql>

    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="emsHeadResultMap" parameterType="com.dcjet.cs.mat.model.EmsHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_GWSTD_EMS_HEAD t
        <where>
            <include refid="condition"></include>
        </where>
        order by APPR_STATUS desc, INSERT_TIME desc, t.sid
    </select>

    <select id="getListOnly" resultMap="emsHeadResultMap" parameterType="com.dcjet.cs.mat.model.EmsHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_GWSTD_EMS_HEAD t
        <where>
            TRADE_CODE = #{tradeCode}
            and EMS_NO =#{emsNo}
        </where>
    </select>

    <select id="selectBySids" resultType="com.dcjet.cs.mat.model.EmsHead" parameterType="java.util.List">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_GWSTD_EMS_HEAD
        where SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_GWSTD_EMS_HEAD t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="selectByCopEmsNo" resultType="int" parameterType="com.dcjet.cs.dto.mat.EmsHeadParam">
        select count(1) from T_GWSTD_EMS_HEAD where COP_EMS_NO=#{copEmsNo} and TRADE_CODE=#{tradeCode}
    </select>
    <select id="selectListForConsumeAudit" parameterType="com.dcjet.cs.mat.model.EmsHead" resultType="com.dcjet.cs.mat.model.EmsHead">
        select
          t.SID as sid,
          t.COP_EMS_NO as copEmsNo,
          t.EMS_NO as emsNo,
          t.BILL_FLAG as billFlag,
          case when t.BILL_FLAG = '11' or t.BILL_FLAG = '12' then '手册' else '账册' end as billFlagName,
          t.VALID_DATE as validDate,
          a.STATUS as apprStatus,
          a.APPR_DATE as apprDate,
          a.APPR_USER as apprUser,
          a.INSERT_USER_NAME as apprUserName,
          a.APPR_NOTE as apprNote,
          b.PARAMS_NAME as apprName
        from  T_GWSTD_EMS_HEAD t
        inner join T_AEO_AUDIT_INFO a on a.BILL_SID = t.sid
        left join T_BI_CUSTOMER_PARAMS b on t.APPR_STATUS = b.PARAMS_CODE and b.PARAMS_TYPE = 'AUDIT_TYPE'
        <where>
            <include refid="conditionForConsumeAudit"></include>
        </where>
        order by a.INSERT_TIME desc
    </select>

    <update id="emsMatPass" parameterType="java.lang.String">
        BEGIN
            <!-- 更新单损耗状态 表头备案通过 JC ，表体修改标志 为 0-->
            UPDATE t_gwstd_ems_head SET Appr_Status = 'JC', LAST_AUDIT_DATE = CURRENT_TIMESTAMP(3) WHERE SID = #{sid};
            UPDATE t_gwstd_ems_consume_pre SET MODIFY_MARK = '0' WHERE HEAD_ID = #{sid};
            <!-- 根据sid删除原有备案通过表数据-->
            DELETE FROM t_gwstd_ems_consume_cur WHERE HEAD_ID = #{sid} ;
            <!-- 插入新数据-->
            INSERT INTO t_gwstd_ems_consume_cur(SID, HEAD_ID, EMS_NO, G_NO, EXG_G_NO, COP_EXG_NO, IMG_G_NO, COP_IMG_NO, EXG_VERSION, DEC_ALL_CONSUME, DEC_CM, DEC_DM_VISIABLE,
                 DEC_DM_INVISIABLE, BOND_MTPCK_PRPR, UCNS_DCL_STAT, MODIFY_MARK, ETPS_EXE_MARK, NOTE, DATA_SOURCE, TRADE_CODE, SEND_API_STATUS, INSERT_USER, INSERT_TIME,
                 UPDATE_USER, UPDATE_TIME, INSERT_USER_NAME, UPDATE_USER_NAME

            )
            SELECT SID, HEAD_ID, EMS_NO, G_NO, EXG_G_NO, COP_EXG_NO, IMG_G_NO, COP_IMG_NO, EXG_VERSION, DEC_ALL_CONSUME, DEC_CM, DEC_DM_VISIABLE,
                 DEC_DM_INVISIABLE, BOND_MTPCK_PRPR, UCNS_DCL_STAT, MODIFY_MARK, ETPS_EXE_MARK, NOTE, DATA_SOURCE, TRADE_CODE, SEND_API_STATUS, INSERT_USER, INSERT_TIME,
                 UPDATE_USER, UPDATE_TIME, INSERT_USER_NAME, UPDATE_USER_NAME
            FROM t_gwstd_ems_consume_pre WHERE HEAD_ID = #{sid};
        END;
    </update>
    <update id="emsMatPass" parameterType="java.lang.String" databaseId="postgresql">
        <!-- 更新单损耗状态 表头备案通过 JC ，表体修改标志 为 0-->
        UPDATE t_gwstd_ems_head SET Appr_Status = 'JC', LAST_AUDIT_DATE = CURRENT_TIMESTAMP(3) WHERE SID = #{sid};
        UPDATE t_gwstd_ems_consume_pre SET MODIFY_MARK = '0' WHERE HEAD_ID = #{sid};
        <!-- 根据sid删除原有备案通过表数据-->
        DELETE FROM t_gwstd_ems_consume_cur WHERE HEAD_ID = #{sid} ;
        <!-- 插入新数据-->
        INSERT INTO t_gwstd_ems_consume_cur(SID, HEAD_ID, EMS_NO, G_NO, EXG_G_NO, COP_EXG_NO, IMG_G_NO, COP_IMG_NO, EXG_VERSION, DEC_ALL_CONSUME, DEC_CM, DEC_DM_VISIABLE,
             DEC_DM_INVISIABLE, BOND_MTPCK_PRPR, UCNS_DCL_STAT, MODIFY_MARK, ETPS_EXE_MARK, NOTE, DATA_SOURCE, TRADE_CODE, SEND_API_STATUS, INSERT_USER, INSERT_TIME,
             UPDATE_USER, UPDATE_TIME, INSERT_USER_NAME, UPDATE_USER_NAME

        )
        SELECT SID, HEAD_ID, EMS_NO, G_NO, EXG_G_NO, COP_EXG_NO, IMG_G_NO, COP_IMG_NO, EXG_VERSION, DEC_ALL_CONSUME, DEC_CM, DEC_DM_VISIABLE,
             DEC_DM_INVISIABLE, BOND_MTPCK_PRPR, UCNS_DCL_STAT, MODIFY_MARK, ETPS_EXE_MARK, NOTE, DATA_SOURCE, TRADE_CODE, SEND_API_STATUS, INSERT_USER, INSERT_TIME,
             UPDATE_USER, UPDATE_TIME, INSERT_USER_NAME, UPDATE_USER_NAME
        FROM t_gwstd_ems_consume_pre WHERE HEAD_ID = #{sid};
    </update>
    <!-- 根据账册号获取表头信息 -->
    <select id="selectHeadByEmsNo" parameterType="string" resultMap="emsHeadResultMap">
        select * from (
            SELECT
              <include refid="Base_Column_List" />
            FROM
              T_GWSTD_EMS_HEAD
            where trade_code = #{tradeCode} and ems_no = #{emsNo} order by insert_time desc
        ) where rownum = 1
    </select>
    <select id="selectHeadByEmsNo" parameterType="string" resultMap="emsHeadResultMap" databaseId="postgresql">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_GWSTD_EMS_HEAD
        where trade_code = #{tradeCode} and ems_no = #{emsNo} order by insert_time desc limit 1
    </select>
</mapper>
