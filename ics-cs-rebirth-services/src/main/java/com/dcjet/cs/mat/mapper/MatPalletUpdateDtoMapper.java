package com.dcjet.cs.mat.mapper;

import com.dcjet.cs.base.mapper.BasicConverter;
import com.dcjet.cs.dto.mat.MatPalletDto;
import com.dcjet.cs.dto.mat.MatPalletParam;
import com.dcjet.cs.dto.mat.MatPalletUpdateParam;
import com.dcjet.cs.mat.model.MatPallet;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-12-14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MatPalletUpdateDtoMapper extends BasicConverter<MatPallet, MatPalletDto, MatPalletParam, MatPalletUpdateParam> {
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    MatPallet toPo(MatPalletUpdateParam param);
}
