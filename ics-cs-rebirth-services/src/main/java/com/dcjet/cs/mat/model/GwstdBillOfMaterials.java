package com.dcjet.cs.mat.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-7-13
 */
@Setter
@Getter
@Table(name = "T_GWSTD_BILL_OF_MATERIALS")
public class GwstdBillOfMaterials implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 清单名称1
     */
    @Column(name = "LIST_NAME1")
    private String listName1;
    /**
     * 清单名称2
     */
    @Column(name = "LIST_NAME2")
    private String listName2;
    /**
     * 附件号
     */
    @Column(name = "ATTACHMENT_NUMBER")
    private String attachmentNumber;
    /**
     * 序号
     */
    @Column(name = "SERIAL_NO")
    private String serialNo;
    /**
     * 类别1
     */
    @Column(name = "CATEGORY1")
    private String category1;
    /**
     * 类别2
     */
    @Column(name = "CATEGORY2")
    private String category2;
    /**
     * 商品名称
     */
    @Column(name = "G_NAME")
    @JsonProperty("gName")
    private String gName;
    /**
     * 英文名称(仅参考)
     */
    @Column(name = "COP_G_NAME_EN")
    private String copGNameEn;
    /**
     * 税则号列(仅参考)
     */
    @Column(name = "TAX_CODE_COLUMN")
    private String taxCodeColumn;
    /**
     * 性能指标
     */
    @Column(name = "PERFORMANCE")
    private String performance;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 企业代码
     */
    @Column(name = "POLICY_BASIS")
    private String policyBasis;
}
