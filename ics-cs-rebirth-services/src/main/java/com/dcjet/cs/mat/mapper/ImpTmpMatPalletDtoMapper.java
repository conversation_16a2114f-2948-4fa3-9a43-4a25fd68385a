package com.dcjet.cs.mat.mapper;
import com.dcjet.cs.dto.mat.imp.ImpTmpMatPalletParam;
import com.dcjet.cs.mat.model.MatPallet;
import com.dcjet.cs.mat.model.imp.ImpTmpMatPallet;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-10-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ImpTmpMatPalletDtoMapper {
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    ImpTmpMatPallet toPo(ImpTmpMatPalletParam param);
    /**
     * 数据库原始数据更新
     * @param impTmpMatPalletParam
     * @param impTmpMatPallet
     */
    void updatePo(ImpTmpMatPalletParam impTmpMatPalletParam, @MappingTarget ImpTmpMatPallet impTmpMatPallet);
    default void patchPo(ImpTmpMatPalletParam impTmpMatPalletParam, ImpTmpMatPallet impTmpMatPallet) {
        // TODO 自行实现局部更新
    }

    /**
     * 数据库原始数据更新
     * @param po
     * @param model
     */
    void updatePo(ImpTmpMatPallet po, @MappingTarget MatPallet model);

    MatPallet tmpPo(ImpTmpMatPallet pallet);

}
