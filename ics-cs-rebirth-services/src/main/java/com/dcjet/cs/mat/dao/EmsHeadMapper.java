package com.dcjet.cs.mat.dao;
import com.dcjet.cs.dto.mat.EmsHeadParam;
import com.dcjet.cs.mat.model.EmsHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* EmsHead
* <AUTHOR>
* @date: 2020-5-15
*/
public interface EmsHeadMapper extends Mapper<EmsHead> {
    /**
     * 查询获取数据
     * @param emsHead
     * @return
     */
    List<EmsHead> getList(EmsHead emsHead);

    List<EmsHead> getListOnly(EmsHead emsHead);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    List<EmsHead> selectBySids(List<String> sids);

    int selectByCopEmsNo(EmsHeadParam emsHeadParam);

    List<EmsHead> selectListForConsumeAudit(EmsHead emsHead);

    void emsMatPass(@Param("sid") String sid);

    EmsHead selectHeadByEmsNo(@Param("tradeCode") String tradeCode,@Param("emsNo") String emsNo);
}
