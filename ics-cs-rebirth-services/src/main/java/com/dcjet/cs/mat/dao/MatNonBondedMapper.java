package com.dcjet.cs.mat.dao;
import com.dcjet.cs.dto.aeo.AeoMessageDto;
import com.dcjet.cs.dto.mat.MatIeInfoDto;
import com.dcjet.cs.dto.mat.MatIeInfoParam;
import com.dcjet.cs.mat.model.MatImgexgOrg;
import com.dcjet.cs.mat.model.MatNonBonded;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* MatNonBonded
* <AUTHOR>
* @date: 2019-4-17
*/
public interface MatNonBondedMapper extends Mapper<MatNonBonded> {
    /**
     * 查询获取数据
     * @param matNonBonded
     * @return
     */
    List<MatNonBonded> getList(MatNonBonded matNonBonded);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 功能描述: 根据CODE_TS带出税率数据
     *
     * <AUTHOR> 沈振宇
     * @return list数据
     */
    MatNonBonded selectCodeTS(@Param("codeTS") String codeTS);

    List<MatNonBonded> selectBySids(List<String> listSids);
    //List<MatNonBonded> selectCodeTS(Map<String, Object> map);
    @Select({"select t.* from T_MAT_NON_BONDED t where EXISTS (select 1 from T_IMP_TMP_MAT_NON_BONDED tmp " +
            "where tmp.TEMP_OWNER = #{tempOwner} " +
            "and tmp.TRADE_CODE = t.TRADE_CODE " +
            "and tmp.COP_G_NO = t.COP_G_NO ) "})
    @ResultMap({"matNonBondedResultMap"})
    List<MatNonBonded> getImportMatNonBondedList(@Param("tempOwner") String tempOwner);

    /**
     *
     * @param tradeCode
     * @param copGNo
     * @return
     */
    MatNonBonded selectAllData(@Param("tradeCode") String tradeCode, @Param("copGNo") String copGNo);

    /**
     * 根据sid查询非保税数据
     * @param listSids
     * @param tradeCode
     *
     * @return
     */
    List<MatNonBonded> listCheckNonCodeTS(@Param("listSids") List<String> listSids, @Param("tradeCode") String tradeCode);
    List<Map<String,String>> checkCodeTS(@Param("listFacGNo") List<String> listFacGNo, @Param("tradeCode") String tradeCode);

    List<Map<String,String>> getImgExgByNo(MatNonBonded matNonBonded);
    List<Map<String,String>> getImgExgOrgByNo(MatNonBonded matNonBonded);
    List<Map<String,String>> getNoBondByNo(MatNonBonded matNonBonded);
    /**
     * 提取
     * 非保税预归类
     * @param param
     * @return
     */
    List<Map<String, Object>> insertSelectNonBonded(Map<String, Object> param);
    /**
     * 获取物料信息接口待提取数据
     * @param tradeCode
     * @param sid
     * @return
     */
    List<MatNonBonded> getCopList(@Param("tradeCode") String tradeCode, @Param("sid") String sid);

    int updateUnit(MatNonBonded matNonBonded);

    void cleanFactor1(MatNonBonded matNonBonded);

    void cleanFactor2(MatNonBonded matNonBonded);

    List<MatNonBonded> selectErrorData(MatNonBonded matNonBonded);

    MatNonBonded selectList(@Param("sid")String sid, @Param("tradeCode") String tradeCode);
    int auditByList(MatNonBonded matNonBonded);
    int updateByList(MatNonBonded matNonBonded);

    /**
     * 功能描述：获取产销国
     * @param copGNo
     * @param GMark
     * @param tradeCode
     * @return
     */
    MatNonBonded getNonCompany(@Param("copGNo") String copGNo, @Param("GMark") String GMark, @Param("tradeCode") String tradeCode);

    /**
     * 根据企业料号查询所有数据
     * @param copGNo
     * @param tradeCode
     * @return
     */
    MatNonBonded getCopGNo(@Param("copGNo") String copGNo, @Param("tradeCode") String tradeCode);


    /**
     * 查询获取企业料号列表（进出口使用）
     * @param matNonBonded
     * @return
     */
    List<String> getFacGNoList(MatNonBonded matNonBonded);

    List<MatIeInfoDto> selectAllDatas(@Param("tradeCode") String tradeCode, @Param("copGNo") String copGNo);

    List<MatNonBonded> getNonCode(@Param("sids")List<String> sids);

    List<AeoMessageDto> getCodesList(@Param("codeTSs") List<String> codeTSs, @Param("sids") List<String> sids, @Param("tradeCode")  String tradeCode);
}
