package com.dcjet.cs.mat.dao;

import com.dcjet.cs.mat.model.MatDeclareChange;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;

/**
 * generated by Generate 神码
 * MatDeclareChange
 *
 * <AUTHOR>
 * @date: 2022-12-12
 */
public interface MatDeclareChangeMapper extends Mapper<MatDeclareChange> {
    /**
     * 查询获取数据
     *
     * @param matDeclareChange
     * @return
     */
    List<MatDeclareChange> getList(MatDeclareChange matDeclareChange);

    /**
     * 查询获取数据
     *
     * @param matDeclareChange
     * @return
     */
    List<MatDeclareChange> getNonList(MatDeclareChange matDeclareChange);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    void deleteDate(@Param("dateTime") String dateTime);

    Date getTime();
}
