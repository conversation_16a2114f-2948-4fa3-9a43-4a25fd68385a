package com.dcjet.cs.mat.mapper;

import com.dcjet.cs.dto.mat.MatImgexgChangeDto;
import com.dcjet.cs.dto.mat.MatImgexgChangeParam;
import com.dcjet.cs.mat.model.MatImgexgChange;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-5-30
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MatImgexgChangeDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    MatImgexgChangeDto toDto(MatImgexgChange po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    MatImgexgChange toPo(MatImgexgChangeParam param);

    /**
     * 数据库原始数据更新
     *
     * @param matImgexgChangeParam
     * @param matImgexgChange
     */
    void updatePo(MatImgexgChangeParam matImgexgChangeParam, @MappingTarget MatImgexgChange matImgexgChange);

    default void patchPo(MatImgexgChangeParam matImgexgChangeParam, MatImgexgChange matImgexgChange) {
        // TODO 自行实现局部更新
    }
}
