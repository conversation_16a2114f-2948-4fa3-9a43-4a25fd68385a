package com.dcjet.cs.mat.service;

import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.mat.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.mat.dao.MatDeclareChangeMapper;
import com.dcjet.cs.mat.mapper.MatDeclareChangeDtoMapper;
import com.dcjet.cs.mat.model.MatDeclareChange;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-12-12
 */
@Service
public class MatDeclareChangeService extends BaseService<MatDeclareChange> {
    @Resource
    private MatDeclareChangeMapper matDeclareChangeMapper;
    @Resource
    private MatDeclareChangeDtoMapper matDeclareChangeDtoMapper;

    @Override
    public Mapper<MatDeclareChange> getMapper() {
        return matDeclareChangeMapper;
    }

    /**
     * 获取分页信息
     *
     * @param matDeclareChangeParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<MatDeclareChangeDto>> getListPaged(MatDeclareChangeParam matDeclareChangeParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        MatDeclareChange matDeclareChange = matDeclareChangeDtoMapper.toPo(matDeclareChangeParam);
        matDeclareChange.setTradeCode(userInfo.getCompany());
        Page<MatDeclareChange> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> matDeclareChangeMapper.getList(matDeclareChange));
        List<MatDeclareChangeDto> matDeclareChangeDtos = page.getResult().stream().map(head -> {
            MatDeclareChangeDto dto = matDeclareChangeDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<MatDeclareChangeDto>> paged = ResultObject.createInstance(matDeclareChangeDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 获取分页信息
     *
     * @param matDeclareChangeParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<MatDeclareChangeDto>> getNonListPaged(MatDeclareChangeParam matDeclareChangeParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        MatDeclareChange matDeclareChange = matDeclareChangeDtoMapper.toPo(matDeclareChangeParam);
        matDeclareChange.setTradeCode(userInfo.getCompany());
        Page<MatDeclareChange> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> matDeclareChangeMapper.getNonList(matDeclareChange));
        List<MatDeclareChangeDto> matDeclareChangeDtos = page.getResult().stream().map(head -> {
            MatDeclareChangeDto dto = matDeclareChangeDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<MatDeclareChangeDto>> paged = ResultObject.createInstance(matDeclareChangeDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<MatDeclareChangeDto> selectAll(MatDeclareChangeParam exportParam, UserInfoToken userInfo) {
        MatDeclareChange matDeclareChange = matDeclareChangeDtoMapper.toPo(exportParam);
        matDeclareChange.setTradeCode(userInfo.getCompany());
        List<MatDeclareChangeDto> matDeclareChangeDtos = new ArrayList<>();
        List<MatDeclareChange> matDeclareChanges = matDeclareChangeMapper.getList(matDeclareChange);
        if (CollectionUtils.isNotEmpty(matDeclareChanges)) {
            matDeclareChangeDtos = matDeclareChanges.stream().map(head -> {
                MatDeclareChangeDto dto = matDeclareChangeDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return matDeclareChangeDtos;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<MatDeclareChangeDto> selectAllNon(MatDeclareChangeParam exportParam, UserInfoToken userInfo) {
        MatDeclareChange matDeclareChange = matDeclareChangeDtoMapper.toPo(exportParam);
        matDeclareChange.setTradeCode(userInfo.getCompany());
        List<MatDeclareChangeDto> matDeclareChangeDtos = new ArrayList<>();
        List<MatDeclareChange> matDeclareChanges = matDeclareChangeMapper.getNonList(matDeclareChange);
        if (CollectionUtils.isNotEmpty(matDeclareChanges)) {
            matDeclareChangeDtos = matDeclareChanges.stream().map(head -> {
                MatDeclareChangeDto dto = matDeclareChangeDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return matDeclareChangeDtos;
    }

    public Date getTime() {
        return matDeclareChangeMapper.getTime();
    }
}
