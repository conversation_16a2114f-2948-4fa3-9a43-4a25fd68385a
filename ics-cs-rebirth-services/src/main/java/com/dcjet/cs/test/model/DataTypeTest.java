package com.dcjet.cs.test.model;

import xdo.interceptor.decimal.RemoveTailingZero;

import java.math.BigDecimal;
import java.util.Date;
@RemoveTailingZero
public class DataTypeTest {
    // 4 byte <-> integer
    private int intTypeTest;
    // 2 byte <-> smallint
    private short shortTypeTest;
    // 8 byte <-> bigint
    private long longTypeTest;
    // 4 byte <-> numeric
    private float floatTypeTest;
    // 8 byte <-> numeric
    private double doubleTypeTest;
    // 2 byte <-> char(2)
    private char charTypeTest;
    // <->TEXT
    private String stringTypeTest;
    // <-> numeric
    private BigDecimal bigDecimalTypeTest;
    // <-> timestamp
    private Date dateTypeTest;

    public int getIntTypeTest() {
        return intTypeTest;
    }

    public void setIntTypeTest(int intTypeTest) {
        this.intTypeTest = intTypeTest;
    }

    public short getShortTypeTest() {
        return shortTypeTest;
    }

    public void setShortTypeTest(short shortTypeTest) {
        this.shortTypeTest = shortTypeTest;
    }

    public long getLongTypeTest() {
        return longTypeTest;
    }

    public void setLongTypeTest(long longTypeTest) {
        this.longTypeTest = longTypeTest;
    }

    public float getFloatTypeTest() {
        return floatTypeTest;
    }

    public void setFloatTypeTest(float floatTypeTest) {
        this.floatTypeTest = floatTypeTest;
    }

    public double getDoubleTypeTest() {
        return doubleTypeTest;
    }

    public void setDoubleTypeTest(double doubleTypeTest) {
        this.doubleTypeTest = doubleTypeTest;
    }

    public char getCharTypeTest() {
        return charTypeTest;
    }

    public void setCharTypeTest(char charTypeTest) {
        this.charTypeTest = charTypeTest;
    }

    public String getStringTypeTest() {
        return stringTypeTest;
    }

    public void setStringTypeTest(String stringTypeTest) {
        this.stringTypeTest = stringTypeTest;
    }

    public BigDecimal getBigDecimalTypeTest() {
        return bigDecimalTypeTest;
    }

    public void setBigDecimalTypeTest(BigDecimal bigDecimalTypeTest) {
        this.bigDecimalTypeTest = bigDecimalTypeTest;
    }

    public Date getDateTypeTest() {
        return dateTypeTest;
    }

    public void setDateTypeTest(Date dateTypeTest) {
        this.dateTypeTest = dateTypeTest;
    }
}
