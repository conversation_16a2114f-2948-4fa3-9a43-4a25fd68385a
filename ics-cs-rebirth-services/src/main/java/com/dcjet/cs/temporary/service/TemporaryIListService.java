
package com.dcjet.cs.temporary.service;

import com.dcjet.cs.dto.temporary.TemporaryIEListExtractionParam;
import com.dcjet.cs.dto.temporary.TemporaryIListDto;
import com.dcjet.cs.dto.temporary.TemporaryIListParam;
import com.dcjet.cs.dto.temporary.TemporaryIListSearchParam;
import com.dcjet.cs.temporary.dao.TemporaryIListMapper;
import com.dcjet.cs.temporary.mapper.TemporaryIListDtoMapper;
import com.dcjet.cs.temporary.model.TemporaryIList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/***
 * 出境修理表体 service default implement
 *
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@Service
public class TemporaryIListService {

	@Resource
    private TemporaryIListMapper temporaryIListMapper;

	@Resource
    private TemporaryIListDtoMapper temporaryIListDtoMapper;
    @Resource
    private AuditLogUtil auditLogUtil;
    private final static String MODEL = xdoi18n.XdoI18nUtil.t("暂时进出境-暂时进境记录表头");
	 /**
     * 获取分页信息
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    public ResultObject<List<TemporaryIListDto>> list(TemporaryIListSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        searchParam.setTradeCode(token.getCompany());
        Page<TemporaryIList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> temporaryIListMapper.getList(searchParam));
        return getListResultObject(page);
    }

    /***
     * 根据id获取详情
     *
     * @param sid
     * @param token
     * @return
     */
	public TemporaryIListDto get(@Nonnull String sid, UserInfoToken token) {
        TemporaryIList po = temporaryIListMapper.selectByPrimaryKey(sid);
        return temporaryIListDtoMapper.toDto(po);
    }


    /***
     * 拉去提取列表
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    public ResultObject<List<TemporaryIListDto>> unextraction(TemporaryIEListExtractionParam searchParam, PageParam pageParam, UserInfoToken token) {
        TemporaryIList queryParam = temporaryIListDtoMapper.fromEIListExtractionParam(searchParam);
        queryParam.setTradeCode(token.getCompany());
        Page<TemporaryIList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> temporaryIListMapper.take(queryParam));
        return getListResultObject(page);
    }

	 /**
     * 新增
     *
     * @param param
     * @param token
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TemporaryIListDto insert(TemporaryIListParam param, UserInfoToken token) {
        TemporaryIList po = temporaryIListDtoMapper.toPo(param);
		po.preInsert(token);
        int insertState = temporaryIListMapper.insert(po);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(),MODEL,token,po);
        return  insertState > 0 ? temporaryIListDtoMapper.toDto(po) : null;
    }


	/**
     * 修改
     *
     * @param param
     * @param token
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TemporaryIListDto update(@Nonnull String sid, TemporaryIListParam param, UserInfoToken token) {
        TemporaryIList po = temporaryIListMapper.selectByPrimaryKey(sid);
        if (po == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请选择有效的数据"));
        }

        temporaryIListDtoMapper.updatePo(param, po);
        po.preUpdate(token);
        int updateState = temporaryIListMapper.updateByPrimaryKey(po);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(),MODEL,token,po);
        return updateState > 0 ? temporaryIListDtoMapper.toDto(po) : null;
	}


	/**
     * 批量删除
     *
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> sids, UserInfoToken userInfo) {
        Example exampleList = new Example(TemporaryIList.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andIn("sid", sids);
        List<TemporaryIList> temporaryIListList = temporaryIListMapper.selectByExample(exampleList);
		temporaryIListMapper.deleteBySids(sids,  userInfo.getCompany());
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.DELETE.getValue(),MODEL,userInfo,temporaryIListList);
    }

    private ResultObject<List<TemporaryIListDto>> getListResultObject(Page<TemporaryIList> page) {
        List<TemporaryIListDto> dtoList = page.getResult().stream().map(head -> {
            TemporaryIListDto dto = temporaryIListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<TemporaryIListDto>> paged = ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

}
