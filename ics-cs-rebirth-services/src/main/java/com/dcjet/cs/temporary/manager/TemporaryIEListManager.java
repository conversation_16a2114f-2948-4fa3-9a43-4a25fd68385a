package com.dcjet.cs.temporary.manager;

import com.dcjet.cs.temporary.dao.TemporaryIEHeadMapper;
import com.dcjet.cs.temporary.dao.TemporaryIEListMapper;
import com.dcjet.cs.temporary.dao.TemporaryIHeadMapper;
import com.dcjet.cs.temporary.dao.TemporaryIListMapper;
import com.dcjet.cs.temporary.model.TemporaryIEHead;
import com.dcjet.cs.temporary.model.TemporaryIEList;
import com.dcjet.cs.temporary.model.TemporaryIHead;
import com.dcjet.cs.temporary.model.TemporaryIList;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import javax.annotation.Resource;
import java.util.List;

@Component
public class TemporaryIEListManager {

    @Resource
    private TemporaryIEHeadMapper temporaryIEHeadMapper;

    @Resource
    private TemporaryIEListMapper temporaryIEListMapper;

    @Resource
    private TemporaryIHeadMapper temporaryIHeadMapper;

    @Resource
    private TemporaryIListMapper temporaryIListMapper;

    public List<TemporaryIEList> findByHeadId(String headId, UserInfoToken user) {
        Sqls sqls = Sqls.custom().andEqualTo("tradeCode", user.getCompany())
                .andEqualTo("headId", headId);
        List<TemporaryIEList> list = temporaryIEListMapper.selectByExample(Example.builder(TemporaryIEList.class).andWhere(sqls).build());
        return list;
    }

    public void deleteByIdList(List<String> sids, UserInfoToken user) {
        sids.forEach(it -> {
            TemporaryIEList ieList = temporaryIEListMapper.selectByPrimaryKey(it);
            if (ieList != null) {
                TemporaryIEHead head = temporaryIEHeadMapper.selectByPrimaryKey(ieList.getHeadId());
                if (TemporaryIEHead.STATUS_DEC.equals(head.getStatus())) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前单据已生成报关，禁止此操作"));
                }

                temporaryIEListMapper.deleteByPrimaryKey(it);
                updateIHeadStatus(ieList.getTemporaryListId(), user);
            }
        });
    }


    public void deleteByHeadId(String headId, UserInfoToken user) {
        List<TemporaryIEList> list = findByHeadId(headId, user);
        if (list.size() == 0) {
            return;
        }
        temporaryIEListMapper.deleteByHeadId(headId, user.getCompany());
        list.stream().forEach(it -> {
            updateIHeadStatus(it.getTemporaryListId(), user);
        });
    }

    private void updateIHeadStatus(String temporaryListId, UserInfoToken user) {
        TemporaryIList eList = temporaryIListMapper.selectByPrimaryKey(temporaryListId);
        if (eList == null) {
            return;
        }
        List<TemporaryIList> eListList = temporaryIListMapper.existsTake(eList.getHeadId(), user.getCompany());
        if (eListList.size() > 0) {
            return;
        }
        TemporaryIHead eHead = temporaryIHeadMapper.selectByPrimaryKey(eList.getHeadId());
        eHead.setStatus(TemporaryIHead.STATUS_WAIT);
        eHead.preUpdate(user);
        temporaryIHeadMapper.updateByPrimaryKey(eHead);
    }


}
