
package com.dcjet.cs.temporary.service;

import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.temporary.*;
import com.dcjet.cs.entry.dao.DecEEntryHeadMapper;
import com.dcjet.cs.entry.model.DecEEntryHead;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpEListN;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.erp.service.DecErpEHeadNService;
import com.dcjet.cs.temporary.dao.TemporaryIEHeadMapper;
import com.dcjet.cs.temporary.dao.TemporaryIEListMapper;
import com.dcjet.cs.temporary.dao.TemporaryIHeadMapper;
import com.dcjet.cs.temporary.dao.TemporaryIListMapper;
import com.dcjet.cs.temporary.manager.TemporaryIEListManager;
import com.dcjet.cs.temporary.manager.TemporaryIHeadManager;
import com.dcjet.cs.temporary.mapper.TemporaryIEHeadDtoMapper;
import com.dcjet.cs.temporary.mapper.TemporaryIEListDtoMapper;
import com.dcjet.cs.temporary.model.TemporaryIEHead;
import com.dcjet.cs.temporary.model.TemporaryIEList;
import com.dcjet.cs.temporary.model.TemporaryIHead;
import com.dcjet.cs.temporary.model.TemporaryIList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;


/***
 * 修理复运进境表头 service default implement
 *
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@Service
public class TemporaryIEHeadService {

	@Resource
    private TemporaryIEHeadMapper temporaryIEHeadMapper;

	@Resource
    private TemporaryIEHeadDtoMapper temporaryIEHeadDtoMapper;

	@Resource
	private TemporaryIEListMapper temporaryIEListMapper;

	@Resource
	private TemporaryIEListManager temporaryIEListManager;

	@Resource
	private TemporaryIEListDtoMapper temporaryIEListDtoMapper;

	@Resource
	private TemporaryIHeadMapper temporaryIHeadMapper;

    @Resource
    private TemporaryIHeadManager temporaryIHeadManager;

	@Resource
	private TemporaryIListMapper temporaryIListMapper;

    @Resource
    private DecErpEHeadNService decErpEHeadNService;

    @Resource
    private DecEEntryHeadMapper decEEntryHeadMapper;

    @Resource
    private CommonService commonService;
    @Resource
    private AuditLogUtil auditLogUtil;
    private final static String MODEL = xdoi18n.XdoI18nUtil.t("暂时进出境-复运出境管理表头");

	 /**
     * 获取分页信息
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    public ResultObject<List<TemporaryIEHeadDto>> list(TemporaryIEHeadSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        TemporaryIEHead po = temporaryIEHeadDtoMapper.fromSearchParam(searchParam);
        po.setTradeCode(token.getCompany());
        Page<TemporaryIEHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> temporaryIEHeadMapper.getList(po));
        List<TemporaryIEHeadDto> dtoList = page.getResult().stream().map(head -> {
            updateEntryNo(head);
            TemporaryIEHeadDto dto = temporaryIEHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<TemporaryIEHeadDto>> paged = ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    private void updateEntryNo(TemporaryIEHead head) {
        if (!(TemporaryIEHead.STATUS_DEC.equals(head.getStatus()) && Strings.isNullOrEmpty(head.getEntryNo()))) {
            return;
        }

        Sqls sqls = Sqls.custom().andEqualTo("tradeCode", head.getTradeCode())
                .andLike("emsListNo", head.getEmsListNo() + "%")
                .andIsNotNull("entryNo");

        Example example = Example.builder(DecEEntryHead.class).andWhere(sqls).build();
        List<DecEEntryHead> list = decEEntryHeadMapper.selectByExample(example);

        StringJoiner sjDeclareDate = new StringJoiner(",");
        StringJoiner sjEntryNo = new StringJoiner(",");
        for (DecEEntryHead decEEntryHead : list) {
            if (!Strings.isNullOrEmpty(decEEntryHead.getDDate())) {
                sjDeclareDate.add(decEEntryHead.getDDate());
            }

            if (!Strings.isNullOrEmpty(decEEntryHead.getEntryNo())) {
                sjEntryNo.add(decEEntryHead.getEntryNo());
            }
        }

        head.setDeclareDate(sjDeclareDate.toString());
        head.setEntryNo(sjEntryNo.toString());
        temporaryIEHeadMapper.updateByPrimaryKey(head);
    }

    /***
     * 根据id获取详情
     *
     * @param sid
     * @param token
     * @return
     */
	public TemporaryIEHeadDto get(@Nonnull String sid, UserInfoToken token) {
        TemporaryIEHead po = temporaryIEHeadMapper.selectByPrimaryKey(sid);
        return temporaryIEHeadDtoMapper.toDto(po);
    }

    /***
     *
     * @param param
     * @param token
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateIEHead(TemporaryIHeadGenerateIParam param, UserInfoToken token) {

        TemporaryIHead eHead = temporaryIHeadMapper.selectByPrimaryKey(param.getSid());
        if (eHead == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请选择有效的数据"));
        }
        if (!eHead.canProgress()) {
           throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前状态不允许进行此操作"));
        }

        TemporaryIEHead eiHead = new TemporaryIEHead();
        eiHead.setEmsListNo(param.getEmsListNo());
        eiHead.setTradeCode(token.getCompany());
        int count = temporaryIEHeadMapper.getList(eiHead).size();
        if (count > 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已经存在相同的单据内部编号"));
        }

        eiHead.preInsert(token);

        Sqls sqls = Sqls.custom().andEqualTo("tradeCode", token.getCompany())
                .andEqualTo("headId", param.getSid());
        List<TemporaryIList> list = temporaryIListMapper.selectByExample(Example.builder(TemporaryIEList.class).andWhere(sqls).build());
        if (list.size() == 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("表体数据为空，无法完成复运"));
        }

        List<TemporaryIEList> temporaryEILists = list.stream().map(it -> {
            List<TemporaryIEList> eiLists = temporaryIEListMapper.findByTemporaryListId(it.getSid(), token.getCompany());
            if (eiLists.size() > 0) {
                throw new ArgumentException(String.format(xdoi18n.XdoI18nUtil.t("企业料号")+":[%s],%s",it.getFacGNo(),xdoi18n.XdoI18nUtil.t("数据已经被提取使用过，无法完成整单复运！")));
            }

            TemporaryIEList eiList = temporaryIEListDtoMapper.fromEList(it);
            eiList.setHeadId(eiHead.getSid());
            eiList.setEntryNo(eHead.getEntryNo());
            eiList.setTemporaryListId(it.getSid());
            eiList.preInsert(token);
            return eiList;
        }).collect(Collectors.toList());

        temporaryIHeadManager.updateEHeadStatusProgress(eHead, null, token);

        temporaryIEHeadMapper.insert(eiHead);
        temporaryIEListMapper.insertList(temporaryEILists);
    }

	 /**
     * 新增
     *
     * @param param
     * @param token
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TemporaryIEHeadDto insert(TemporaryIEHeadParam param, UserInfoToken token) {
        TemporaryIEHead po = temporaryIEHeadDtoMapper.toPo(param);
		po.preInsert(token);
        TemporaryIEHead queryParam = new TemporaryIEHead();
        queryParam.setEmsListNo(param.getEmsListNo());
        queryParam.setTradeCode(token.getCompany());
        po.setTradeCode(token.getCompany());

        int count = temporaryIEHeadMapper.getList(queryParam).size();
        if (count > 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已经存在相同的单据内部编号"));
        }

        int insertState = temporaryIEHeadMapper.insert(po);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(),MODEL,token,po);
        return  insertState > 0 ? temporaryIEHeadDtoMapper.toDto(po) : null;
    }


	/**
     * 修改
     *
     * @param param
     * @param token
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TemporaryIEHeadDto update(@Nonnull String sid, TemporaryIEHeadParam param, UserInfoToken token) {
        TemporaryIEHead po = temporaryIEHeadMapper.selectByPrimaryKey(sid);
        if (po == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请选择有效的数据"));
        }

        if (param.getCompleteDate() != null) {
            if (Strings.isNullOrEmpty(po.getEntryNo())) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("报关单号为空时不能修改复运完成日期"));
            }
        }

        TemporaryIEHead queryParam = new TemporaryIEHead();
        queryParam.setEmsListNo(param.getEmsListNo());
        queryParam.setTradeCode(token.getCompany());
        List<TemporaryIEHead> poList = temporaryIEHeadMapper.getList(queryParam);
        if (poList.size() > 1) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已经存在相同的单据内部编号"));
        }

        if (poList.stream().filter(it -> !it.getSid().equals(po.getSid())).count() > 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已经存在相同的单据内部编号"));
        }

        po.setNote(param.getNote());
        if (TemporaryIEHead.STATUS_TS.equals(po.getStatus())) {
            po.setEmsListNo(param.getEmsListNo());
        } else if (TemporaryIEHead.STATUS_DEC.equals(po.getStatus())) {
            if (param.getCompleteDate() != null) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(po.getInsertTime());
                cal.set(Calendar.HOUR_OF_DAY, 0);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                if (param.getCompleteDate().compareTo(cal.getTime()) == -1) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("复运完成日期不能小于制单日期"));
                }
                po.setCompleteDate(param.getCompleteDate());
            }
        }
        po.preUpdate(token);

        int updateState = temporaryIEHeadMapper.updateByPrimaryKey(po);
        if (TemporaryIEHead.STATUS_DEC.equals(po.getStatus())) {
            if (param.getCompleteDate() != null) {
                temporaryIHeadManager.updateCompleteDate(po.getSid(), token);
            }
        }
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(),MODEL,token,po);
        return updateState > 0 ? temporaryIEHeadDtoMapper.toDto(po) : null;
	}


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateStateByDecErpDelete(DecErpEHeadN decErpEHeadN, UserInfoToken token) {
        if (!CommonEnum.dataSourceEnum.SOURCE_12.getCode().equals(decErpEHeadN.getDataSource())) {
            return;
        }

        TemporaryIEHead queryParam = new TemporaryIEHead();
        queryParam.setEmsListNo(decErpEHeadN.getEmsListNo());
        queryParam.setTradeCode(token.getCompany());
        List<TemporaryIEHead> poList = temporaryIEHeadMapper.select(queryParam);
        if (poList.size() == 0) {
            return;
        }

        for (TemporaryIEHead eiHead : poList) {
           eiHead.preUpdate(token);
           eiHead.setStatus(TemporaryIEHead.STATUS_TS);
           temporaryIEHeadMapper.updateByPrimaryKey(eiHead);
        }
    }


	/**
     * 批量删除
     *
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> sids, UserInfoToken userInfo) {
        List<TemporaryIEHead> temporaryIEHeadList=new ArrayList<>();
        sids.forEach(it -> {
            TemporaryIEHead head = temporaryIEHeadMapper.selectByPrimaryKey(it);
            if (head != null && !head.canDelete()) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("只有暂存状态的数据可以被删除"));
            }
            temporaryIEHeadList.add(head);
            temporaryIEHeadMapper.deleteByPrimaryKey(it);
            temporaryIEListManager.deleteByHeadId(it, userInfo);
        });
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.DELETE.getValue(),MODEL,userInfo,temporaryIEHeadList);
    }

    /***
     * 点击报关生成，校验表体是否存在，弹框供用户选择模板；可不选，数据生成至非保进口模块（非保小提单）；当前数据状态变更：报关生成
     *
     * @param param
     * @param token
     */
    public void dec(TemporaryIEHeadDecParam param, UserInfoToken token) throws Exception {
        TemporaryIEHead head = temporaryIEHeadMapper.selectByPrimaryKey(param.getSid());
        if (head == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数据非法"));
        }

        if (!head.canDec()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前数据无法报关生成"));
        }

        DecErpEHeadN decErpEHeadN = generateHead(head, param, token);
        List<DecErpEListN> decErpList = generateList(head, param, token);
        ResultObject<DecErpIHeadN> resultObject = decErpEHeadNService.insertAll(decErpEHeadN, decErpList, true, token
                , false, decErpEHeadN.getGMark(),null,param.getTemplateId());
        if (!resultObject.isSuccess()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("生成提单失败"));
        }
        head.preDec(token);
        temporaryIEHeadMapper.updateByPrimaryKey(head);
    }

    private List<DecErpEListN> generateList(TemporaryIEHead head, TemporaryIEHeadDecParam param, UserInfoToken token) {
        List<TemporaryIEList> list = temporaryIEListManager.findByHeadId(head.getSid(), token);
        if (list.size() == 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前数据没有表体数据，无法完成报关生成"));
        }
        List<DecErpEListN> decNList = list.stream().map(it -> {
            DecErpEListN decErpList = new DecErpEListN();
            decErpList.setGMark(it.getGMark());
            decErpList.setFacGNo(it.getFacGNo());
            decErpList.setCodeTS(it.getCodeTS());
            decErpList.setGName(it.getGName());
            decErpList.setGModel(it.getGModel());
            decErpList.setUnit(it.getUnit());
            decErpList.setQty(it.getQty());
            decErpList.setDecPrice(it.getDecPrice());
            decErpList.setDecTotal(it.getDecTotal());
            decErpList.setCurr(it.getCurr());
            decErpList.setNote(it.getNote());
            decErpList.setOriginCountry(it.getOriginCountry());

            // 原提单表体数据
            TemporaryIList temporaryIList = temporaryIListMapper.selectByPrimaryKey(it.getTemporaryListId());
            //repair/return/temporary
            commonService.buildDecErpEListN(temporaryIList.getDecErpListId(), decErpList,"temporary");

            return decErpList;
        }).collect(Collectors.toList());

        return decNList;
    }

    private DecErpEHeadN generateHead(TemporaryIEHead head, TemporaryIEHeadDecParam param, UserInfoToken token) {
        DecErpEHeadN decErpEHeadN = new DecErpEHeadN();
        decErpEHeadN.setTradeMode("2600");
        decErpEHeadN.setEmsListNo(head.getEmsListNo());
        decErpEHeadN.setBondMark("1");
        decErpEHeadN.setDataSource(CommonEnum.dataSourceEnum.SOURCE_12.getCode());

        // 原提单表头数据
        List<String> sids = temporaryIListMapper.selectDecListId(head.getSid());
        commonService.buildDecErpEHeadN(decErpEHeadN, sids);
        return decErpEHeadN;
    }



}
