<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.temporary.dao.TemporaryIListMapper">
    <sql id="columns">
        t.SID,
		t.TRADE_CODE,
		t.HEAD_ID,
		t.G_MARK,
		t.FAC_G_NO,
		t.CODE_T_S,
		t.G_NAME,
		t.QTY,
        t.QTY - (SELECT coalesce(SUM(QTY), 0) FROM T_GWSTD_TEMPORARY_I_E_LIST WHERE TEMPORARY_LIST_ID = t.SID) AS remainQty,
		t.DEC_PRICE,
		t.DEC_TOTAL,
		t.G_MODEL,
		t.UNIT,
		t.CURR,
		t.INSERT_USER,
		t.INSERT_USER_NAME,
		t.INSERT_TIME,
		t.UPDATE_USER,
		t.UPDATE_USER_NAME,
		t.UPDATE_TIME,
        t.ORIGIN_COUNTRY,
        t.DESTINATION_COUNTRY
    </sql>
    <sql id="condition">
		<if test="tradeCode != null and tradeCode != ''">
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        </if>
		<if test="headId != null and headId != ''">
            and t.HEAD_ID = #{headId,jdbcType=VARCHAR}
        </if>
		<if test="gMark != null and gMark != ''">
            and t.G_MARK = #{gMark,jdbcType=VARCHAR}
        </if>
		<if test="facGNo != null and facGNo != ''">
            and t.FAC_G_NO like concat(concat('%', #{facGNo}),'%')
        </if>
		<if test="codeTS != null and codeTS != ''">
            and t.CODE_T_S like concat(concat('%', #{codeTS}),'%')
        </if>
		<if test="gName != null and gName != ''">
            and t.G_NAME like concat(concat('%', #{gName}),'%')
        </if>
		<if test="gModel != null and gModel != ''">
            and t.G_MODEL like concat(concat('%', #{gModel}),'%')
        </if>
		<if test="unit != null and unit != ''">
            and t.UNIT = #{unit,jdbcType=VARCHAR}
        </if>
		<if test="curr != null and curr != ''">
            and t.CURR = #{curr,jdbcType=VARCHAR}
        </if>
    </sql>
    <select id="getList" parameterType="com.dcjet.cs.dto.temporary.TemporaryIListSearchParam" resultType="com.dcjet.cs.temporary.model.TemporaryIList">
        SELECT 
        <include refid="columns"/>
        FROM T_GWSTD_TEMPORARY_I_LIST t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.INSERT_TIME
    </select>
    <select id="fetch" resultType="com.dcjet.cs.temporary.model.TemporaryIList">
        SELECT l.sid DEC_ERP_LIST_ID, l.FAC_G_NO, l.CODE_T_S, l.G_NAME, l.G_MODEL, l.UNIT, l.CURR, l.G_MARK, l.QTY, l.DEC_PRICE, l.DEC_TOTAL, l.ORIGIN_COUNTRY, l.DESTINATION_COUNTRY FROM t_dec_erp_i_list_n  l
        WHERE l.TRADE_CODE = #{tradeCode} AND
        EXISTS (SELECT r.LIST_SID FROM T_DEC_I_ENTRY_LIST e, T_DEC_ERP_I_RELATION r WHERE e.SID = r.ENTRY_LIST_SID AND e.HEAD_ID = #{headId} AND r.LIST_SID = l.SID)
    </select>
    <select id="take" parameterType="com.dcjet.cs.temporary.model.TemporaryIList" resultType="com.dcjet.cs.temporary.model.TemporaryIList">
        SELECT
        h.EMS_LIST_NO, h.ENTRY_NO, h.DECLARE_DATE, l.SID, l.HEAD_ID, l.FAC_G_NO, l.CODE_T_S, l.G_NAME, l.G_MODEL, l.UNIT, l.CURR, l.G_MARK, l.QTY, l.DEC_PRICE, l.DEC_TOTAL, l.ORIGIN_COUNTRY, l.DESTINATION_COUNTRY,
        l.QTY - (SELECT coalesce(SUM(QTY), 0) FROM T_GWSTD_TEMPORARY_I_E_LIST WHERE TEMPORARY_LIST_ID = l.SID) AS remainQty
        FROM T_GWSTD_TEMPORARY_I_LIST l LEFT JOIN T_GWSTD_TEMPORARY_I_HEAD h ON l.HEAD_ID = h.SID
        <where>
            l.TRADE_CODE = #{tradeCode} AND h.STATUS <![CDATA[ <> ]]> '2' AND (SELECT COALESCE(SUM(QTY), 0)  FROM T_GWSTD_TEMPORARY_I_E_LIST WHERE TEMPORARY_LIST_ID = l.SID) <![CDATA[ < l.QTY ]]>
            <if test="idList != null">
                and l.sid in
                <foreach collection="idList"  item="item" open="(" separator="," close=")"  >
                    #{item}
                </foreach>
            </if>
            <if test="headId != null and headId != ''">
                and l.HEAD_ID = #{headId,jdbcType=VARCHAR}
            </if>
            <if test="emsListNo != null and emsListNo != ''">
                and h.EMS_LIST_NO like concat(concat('%', #{emsListNo}),'%')
            </if>
            <if test="entryNo != null and entryNo != ''">
                and h.ENTRY_NO like concat(concat('%', #{entryNo}),'%')
            </if>
            <if test="facGNo != null and facGNo != ''">
                and l.FAC_G_NO like concat(concat('%', #{facGNo}),'%')
            </if>
            <if test="codeTS != null and codeTS != ''">
                and l.CODE_T_S like concat(concat('%', #{codeTS}),'%')
            </if>
            <if test="gName != null and gName != ''">
                and l.G_NAME like concat(concat('%', #{gName}),'%')
            </if>
        </where>
    </select>
    <select id="existsTake" resultType="com.dcjet.cs.temporary.model.TemporaryIList">
        SELECT l.* FROM T_GWSTD_TEMPORARY_I_LIST l WHERE l.HEAD_ID = #{headId} AND l.TRADE_CODE = #{tradeCode}
            AND EXISTS (SELECT * FROM T_GWSTD_TEMPORARY_I_E_LIST WHERE TEMPORARY_LIST_ID = l.SID)
    </select>

    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_GWSTD_TEMPORARY_I_LIST t where t.SID in
        <foreach collection="sids"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        and t.trade_code = #{tradeCode}
    </delete>

    <select id="selectDecListId" resultType="java.lang.String">
        select b.dec_erp_list_id from t_gwstd_temporary_i_e_list a
        left join t_gwstd_temporary_i_list b on b.sid = a.temporary_list_id
        where a.head_id = #{sid}
    </select>

</mapper>