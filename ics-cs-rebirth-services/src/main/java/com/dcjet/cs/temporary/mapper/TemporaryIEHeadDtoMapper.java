
package com.dcjet.cs.temporary.mapper;

import com.dcjet.cs.dto.temporary.TemporaryIEHeadDto;
import com.dcjet.cs.dto.temporary.TemporaryIEHeadParam;
import com.dcjet.cs.dto.temporary.TemporaryIEHeadSearchParam;
import com.dcjet.cs.temporary.model.TemporaryIEHead;
import org.mapstruct.*;

/***
 * 修理复运进境表头转换
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TemporaryIEHeadDtoMapper {

    /***
     * search param to entity
     * 
     * @param param
     * @return
     */
    @BeanMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    TemporaryIEHead fromSearchParam(TemporaryIEHeadSearchParam param);

    /***
     * dto to entity
     * 
     * @param param
     * @return
     */
    TemporaryIEHead toPo(TemporaryIEHeadParam param);

    /***
     * entity to dto
     * 
     * @param po
     * @return
     */
    TemporaryIEHeadDto toDto(TemporaryIEHead po);

    /***
     * param update entity 
     * 
     * @param param
     * @param po
     */
    @Mapping(target = "tradeCode", ignore = true)
    void updatePo(TemporaryIEHeadParam param, @MappingTarget TemporaryIEHead po);

} 