package com.dcjet.cs.temporary.manager;

import com.dcjet.cs.temporary.dao.TemporaryEHeadMapper;
import com.dcjet.cs.temporary.dao.TemporaryEIHeadMapper;
import com.dcjet.cs.temporary.dao.TemporaryEIListMapper;
import com.dcjet.cs.temporary.dao.TemporaryEListMapper;
import com.dcjet.cs.temporary.model.TemporaryEHead;
import com.dcjet.cs.temporary.model.TemporaryEIHead;
import com.dcjet.cs.temporary.model.TemporaryEIList;
import com.dcjet.cs.temporary.model.TemporaryEList;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import javax.annotation.Resource;
import java.util.List;

@Component
public class TemporaryEIListManager {
    @Resource
    private TemporaryEIHeadMapper temporaryEIHeadMapper;

    @Resource
    private TemporaryEIListMapper temporaryEIListMapper;

    @Resource
    private TemporaryEHeadMapper temporaryEHeadMapper;

    @Resource
    private TemporaryEListMapper temporaryEListMapper;

    public List<TemporaryEIList> findByHeadId(String headId, UserInfoToken user) {
        Sqls sqls = Sqls.custom().andEqualTo("tradeCode", user.getCompany())
                .andEqualTo("headId", headId);
        List<TemporaryEIList> list = temporaryEIListMapper.selectByExample(Example.builder(TemporaryEIList.class).andWhere(sqls).build());
        return list;
    }

    public void deleteByIdList(List<String> sids, UserInfoToken user) {
        sids.forEach(it -> {
            TemporaryEIList eiList = temporaryEIListMapper.selectByPrimaryKey(it);
            if (eiList != null) {
                TemporaryEIHead head = temporaryEIHeadMapper.selectByPrimaryKey(eiList.getHeadId());
                if (TemporaryEIHead.STATUS_DEC.equals(head.getStatus())) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前单据已生成报关，禁止此操作"));
                }
                temporaryEIListMapper.deleteByPrimaryKey(it);
                updateEHeadStatus(eiList.getTemporaryListId(), user);
            }
        });
    }


    public void deleteByHeadId(String headId, UserInfoToken user) {
        List<TemporaryEIList> list = findByHeadId(headId, user);
        if (list.size() == 0) {
            return;
        }
        temporaryEIListMapper.deleteByHeadId(headId, user.getCompany());
        list.stream().forEach(it -> {
            updateEHeadStatus(it.getTemporaryListId(), user);
        });
    }

    private void updateEHeadStatus(String temporaryListId, UserInfoToken user) {
        TemporaryEList eList = temporaryEListMapper.selectByPrimaryKey(temporaryListId);
        if (eList == null) {
            return;
        }
        List<TemporaryEList> eListList = temporaryEListMapper.existsTake(eList.getHeadId(), user.getCompany());
        if (eListList.size() > 0) {
            return;
        }
        TemporaryEHead eHead = temporaryEHeadMapper.selectByPrimaryKey(eList.getHeadId());
        eHead.setStatus(TemporaryEHead.STATUS_WAIT);
        eHead.preUpdate(user);
        temporaryEHeadMapper.updateByPrimaryKey(eHead);
    }


}
