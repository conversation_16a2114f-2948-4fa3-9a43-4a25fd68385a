package com.dcjet.cs.cost.mapper;

import com.dcjet.cs.cost.model.CostCompareIHead;
import com.dcjet.cs.dto.cost.CostCompareIHeadDto;
import com.dcjet.cs.dto.cost.CostCompareIHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-8-15
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CostCompareIHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CostCompareIHeadDto toDto(CostCompareIHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CostCompareIHead toPo(CostCompareIHeadParam param);
    /**
     * 数据库原始数据更新
     * @param costCompareIHeadParam
     * @param costCompareIHead
     */
    void updatePo(CostCompareIHeadParam costCompareIHeadParam, @MappingTarget CostCompareIHead costCompareIHead);
    default void patchPo(CostCompareIHeadParam costCompareIHeadParam, CostCompareIHead costCompareIHead) {
        // TODO 自行实现局部更新
    }
}
