package com.dcjet.cs.cost.dao;

import com.dcjet.cs.cost.model.ImpTmpQuoList;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * ImpTmpQuoList
 *
 * <AUTHOR>
 * @date: 2020-7-28
 */
public interface ImpTmpQuoListMapper extends Mapper<ImpTmpQuoList> {
    /**
     * 查询获取数据
     *
     * @param impTmpQuoList
     * @return
     */
    List<ImpTmpQuoList> getList(ImpTmpQuoList impTmpQuoList);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int deleteByTempOwner(ImpTmpQuoList impTmpQuoList);
}
