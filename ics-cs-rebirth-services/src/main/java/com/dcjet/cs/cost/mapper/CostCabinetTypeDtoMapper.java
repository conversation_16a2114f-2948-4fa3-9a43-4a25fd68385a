package com.dcjet.cs.cost.mapper;

import com.dcjet.cs.cost.model.CostCabinetType;
import com.dcjet.cs.dto.cost.CostCabinetTypeDto;
import com.dcjet.cs.dto.cost.CostCabinetTypeParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-5
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CostCabinetTypeDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CostCabinetTypeDto toDto(CostCabinetType po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CostCabinetType toPo(CostCabinetTypeParam param);

    /**
     * 数据库原始数据更新
     *
     * @param costCabinetTypeParam
     * @param costCabinetType
     */
    void updatePo(CostCabinetTypeParam costCabinetTypeParam, @MappingTarget CostCabinetType costCabinetType);

    default void patchPo(CostCabinetTypeParam costCabinetTypeParam, CostCabinetType costCabinetType) {
        // TODO 自行实现局部更新
    }
}
