package com.dcjet.cs.cost.service.impl;

import com.alibaba.excel.EasyExcel;
import com.dcjet.cs.base.service.impl.BasicServiceImpl;
import com.dcjet.cs.cost.dao.CostCourseMapper;
import com.dcjet.cs.cost.mapper.CostCourseDtoMapper;
import com.dcjet.cs.cost.model.CostCourse;
import com.dcjet.cs.cost.service.CostCourseService;
import com.dcjet.cs.dto.cost.CostCourseDto;
import com.dcjet.cs.dto.cost.CostCourseParam;
import com.dcjet.cs.dto.cost.CostCourseSearchParam;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class CostCourseServiceImpl
        extends BasicServiceImpl<CostCourseMapper, CostCourse, CostCourseDto, CostCourseParam, CostCourseSearchParam, CostCourseDtoMapper>
        implements CostCourseService {

    @Override
    protected void insertEntityHook(CostCourse entity, CostCourseParam param, UserInfoToken userInfo) {
        if (Strings.isNullOrEmpty(param.getCostCourseCode())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("费用科目代码不能为空"));
        }
        List<CostCourse> costCourses = mapper.findByCode(userInfo.getCompany(), param.getCostCourseCode());
        if (CollectionUtils.isNotEmpty(costCourses)) {
            if (CommonVariable.IE_MARK_I.equals(param.getIEMark()) || CommonVariable.IE_MARK_E.equals(param.getIEMark())) {
                if (costCourses.stream().anyMatch(e -> "N".equals(e.getIEMark()) || e.getIEMark().equals(entity.getIEMark()))) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已经存在相同的费用科目代码"));
                }
            } else {
                if (costCourses.size() > 0) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已经存在相同的费用科目代码"));
                }
            }
        }
    }

    @Override
    protected void updateEntityHook(CostCourse entity, CostCourseParam param, UserInfoToken userInfo) {
        if (Strings.isNullOrEmpty(param.getCostCourseCode())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("费用科目代码不能为空"));
        }
        List<CostCourse> costCourses = mapper.findByCode(userInfo.getCompany(), param.getCostCourseCode());
        costCourses = costCourses.stream().filter(e -> !e.getSid().equals(entity.getSid())).collect(Collectors.toList());
        if (CommonVariable.IE_MARK_I.equals(entity.getIEMark()) || CommonVariable.IE_MARK_E.equals(entity.getIEMark())) {
            if (costCourses.stream().anyMatch(e -> e.getIEMark().equals(entity.getIEMark()) || "N".equals(e.getIEMark()))) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已经存在相同的费用科目代码"));
            }
        } else {
            if (costCourses.size() > 0) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("已经存在相同的费用科目代码"));
            }
        }
        entity.setTradeCode(userInfo.getCompany());
    }

    @Override
    public void deleteBatchIds(List<String> idList, UserInfoToken userInfo) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请选择要删除的数据"));
        }
        CostCourse costCourse = new CostCourse();
        costCourse.setTradeCode(userInfo.getCompany());
        costCourse.setIdList(idList);
        List<CostCourse> list = mapper.getList(costCourse);
        Optional<CostCourse> optionalCostCourse = list.stream().filter(it -> ConstantsStatus.STATUS_2.equals(it.getStatus())).findFirst();

        if (optionalCostCourse.isPresent()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("删除的数据中包含已经使用的费用科目"));
        }
        mapper.deleteByIdList(idList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject importData(InputStream inputStream, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance();
        List<Map<Integer, String>> list = EasyExcel.read(inputStream).headRowNumber(0).sheet().doReadSync();
        if (list == null || list.size() == 0) {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("第一行表头无数据，请下载正确的模版。"));
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("第一行表头无数据，请下载正确的模版。"));
        }
        if (list.size() == 1) {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("导入内容为空！"));
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("导入内容为空！"));
        }
        Map<Integer, String> headTitle = list.get(0);
        list.remove(0);

        List<CostCourse> costCourses = new ArrayList<>();
        list.forEach(row -> {
            CostCourse head = new CostCourse();
            head.setIsFixed(ConstantsStatus.STATUS_0);
            row.entrySet().forEach(cell -> {
                int index = cell.getKey();
                String headName = headTitle.get(index);
                if (!Strings.isNullOrEmpty(headName)) {
                    updateHeadValue(head, headName, cell.getValue());
                }
            });
            costCourses.add(head);
        });

        //数据校验
        checkData(costCourses, userInfo);
        costCourses.forEach(head -> {
            List<CostCourse> oldCostCourse = mapper.findByCode(userInfo.getCompany(), head.getCostCourseCode());
            if (CollectionUtils.isNotEmpty(oldCostCourse)) {
                oldCostCourse = oldCostCourse.stream().filter(e -> e.getIEMark().equals(head.getIEMark())).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(oldCostCourse)) {
                CostCourse oldHead = oldCostCourse.get(0);
                if (headTitle.containsValue(xdoi18n.XdoI18nUtil.t("费用科目名称"))) {
                    oldHead.setCostCourseName(head.getCostCourseName());
                }
                if (headTitle.containsValue(xdoi18n.XdoI18nUtil.t("费用科目种类"))) {
                    oldHead.setCourseType(head.getCourseType());
                }
                if (headTitle.containsValue(xdoi18n.XdoI18nUtil.t("备注"))) {
                    oldHead.setNote(head.getNote());
                }
                oldHead.preUpdate(userInfo);
                mapper.updateByPrimaryKey(oldHead);
                head.setSid(oldHead.getSid());
            } else {
                head.preInsert(userInfo);
                head.setValidStatus(ConstantsStatus.STATUS_1);
                mapper.insert(head);
            }
        });
        resultObject.setData(costCourses);
        return resultObject;
    }

    private void updateHeadValue(CostCourse head, String headName, String value) {
        if(xdoi18n.XdoI18nUtil.t("进出口类型").equals(headName)) {
            if (!Arrays.asList(new String[]{"I", "E", "N"}).contains(value)) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("进出口类型只能为I,E或N"));
            }
            head.setIEMark(value);
            return;
        }
        if(xdoi18n.XdoI18nUtil.t("费用科目大类").equals(headName)) {
            if (!Arrays.asList(new String[]{"1", "2", "3", "4"}).contains(value)) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("费用科目种类只能为1,2,3,4"));
            }
            head.setCourseType(value);
            return;
        }
        if(xdoi18n.XdoI18nUtil.t("费用科目代码").equals(headName)) {
            if (Strings.isNullOrEmpty(value)) {
                return;
            }
            head.setCostCourseCode(value);
            return;
        }
        if(xdoi18n.XdoI18nUtil.t("费用科目名称").equals(headName)) {
            head.setCostCourseName(value);
            return;
        }
        if(xdoi18n.XdoI18nUtil.t("备注").equals(headName)) {
            head.setNote(value);
        }
    }

    /**
     * 数据校验
     * 空值
     * 长度校验
     * 当前批次重复性校验
     * 数据库重复校验
     *
     * @param costCourses
     */
    public void checkData(List<CostCourse> costCourses, UserInfoToken userInfo) {
        //校验空值，长度，及旧数据是否已存在
        costCourses.forEach(head -> {
            if (StringUtils.isEmpty(head.getIEMark())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("进出口类型不能为空！"));
            }
            if (StringUtils.isEmpty(head.getCostCourseCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("费用科目代码不能为空！"));
            }
            if (Strings.isNullOrEmpty(head.getCourseType())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("费用科目大类不能为空"));
            }
            if (Strings.isNullOrEmpty(head.getCostCourseName())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("费用科目名称不能为空"));
            }
            try {
                if (head.getCostCourseName().getBytes("GBK").length > 50) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("费用科目名称长度不能超过50字节"));
                }

                if (head.getCostCourseCode().getBytes("GBK").length > 50) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("费用科目代码长度不能超过50字节"));
                }
                if (StringUtils.isNotEmpty(head.getNote()) && head.getNote().getBytes("GBK").length > 255) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("备注不能超过255字节"));
                }

            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            List<CostCourse> oldCostCourse = mapper.findByCode(userInfo.getCompany(), head.getCostCourseCode());
            if (CollectionUtils.isNotEmpty(oldCostCourse)) {
                if (oldCostCourse.stream().anyMatch(e -> e.getIEMark().equals(head.getIEMark()) && e.getCostCourseCode().equals(head.getCostCourseCode()))) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("费用科目代码：") + head.getCostCourseCode() + xdoi18n.XdoI18nUtil.t("数据已存在，导入失败！"));
                }
                if (CommonVariable.IE_MARK_I.equals(head.getIEMark()) || CommonVariable.IE_MARK_E.equals(head.getIEMark())) {
                    if (oldCostCourse.stream().anyMatch(e -> "N".equals(e.getIEMark()) && e.getCostCourseCode().equals(head.getCostCourseCode()))) {
                        throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("费用科目代码：") + head.getCostCourseCode() + xdoi18n.XdoI18nUtil.t("数据已存在，导入失败！"));
                    }
                } else {
                    if (oldCostCourse.stream().anyMatch(e -> (CommonVariable.IE_MARK_I.equals(e.getIEMark())
                            || CommonVariable.IE_MARK_E.equals(e.getIEMark())) && e.getCostCourseCode().equals(head.getCostCourseCode()))) {
                        throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("费用科目代码：") + head.getCostCourseCode() + xdoi18n.XdoI18nUtil.t("数据已存在，导入失败！"));
                    }
                }
            }
        });
        //校验当前导入文件中是否存在重复数据
        Map<String, List<CostCourse>> map = costCourses.stream().collect(
                Collectors.groupingBy(e -> e.getIEMark() + "|" + e.getCostCourseCode()));
        Set<String> keys = map.keySet();
        for (String key : keys) {
            if (map.get(key).size() > 1) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("存在重复数据，导入失败！"));
            }
        }

        List<String> typeNs = costCourses.stream().filter(e -> "N".equals(e.getIEMark())).map(e -> e.getCostCourseCode()).collect(Collectors.toList());
        List<String> typeIEs = costCourses.stream().filter(e -> CommonVariable.IE_MARK_I.equals(e.getIEMark()) || CommonVariable.IE_MARK_E.equals(e.getIEMark())).map(e -> e.getCostCourseCode()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(typeNs)) {
            for (String code : typeNs) {
                if (CollectionUtils.isNotEmpty(typeIEs) && typeIEs.contains(code)) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("存在重复数据，导入失败！"));
                }
            }
        }
    }
}
