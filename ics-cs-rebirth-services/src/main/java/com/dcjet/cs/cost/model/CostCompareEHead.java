package com.dcjet.cs.cost.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-8-15
 */
@Setter
@Getter
@Table(name = "T_COST_COMPARE_E_HEAD")
public class CostCompareEHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 付汇单号
     */
	@Column(name = "PAY_NO")
	private  String payNo;
	/**
     * 收款单位类型（1 货代 2 报关行）
     */
	@Column(name = "REMITTEE_TYPE")
	private  String remitteeType;
	/**
     * 收款单位编码
     */
	@Column(name = "REMITTEE_CODE")
	private  String remitteeCode;
	/**
     * 收款单位名称
     */
	@Column(name = "REMITTEE_NAME")
	private  String remitteeName;
	/**
     * 实际总费用
     */
	@Column(name = "actual_cost_rmb_total")
	private  BigDecimal actualCostRmbTotal;
	/**
     * 预估总费用
     */
	@Column(name = "estimate_cost_rmb_total")
	private  BigDecimal estimateCostRmbTotal;
	/**
     * 总差异
     */
	@Column(name = "DIFFERENCE_TOTAL")
	private  BigDecimal differenceTotal;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 审核人
     */
	@Column(name = "AUDITOR")
	private  String auditor;
	/**
     * 审核状态(0 未审核、1 待审核、2 审核通过、3 审核退回)
     */
	@Column(name = "AUDIT_STATUS")
	private  String auditStatus;
	/**
     * 审核意见
     */
	@Column(name = "AUDIT_OPINION")
	private  String auditOpinion;
	/**
     * 比对状态(0 未比对、1 比对中、2 比对成功、3 比对失败)
     */
	@Column(name = "COMPARE_STATUS")
	private  String compareStatus;
	/**
     * 企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
     * 创建日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
     * 制单人姓名
     */
	@Column(name = "INSERT_USER_NAME")
	private  String insertUserName;
	/**
     * 变更人
     */
	@Column(name = "UPDATE_USER")
	private  String updateUser;

	/**
     * 变更日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;

	/**
	 * 变更人
	 */
	@Column(name = "LOCK_MARK")
	private  String lockMark;

	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}

	/**
     * 修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
	 * 发送人
	 */
	@Column(name = "SEND_USER")
	private  String sendUser;
	/**
	 * 发送人
	 */
	@Column(name = "SEND_USER_NAME")
	private  String sendUserName;
	/***
	 * 设置通用的新增字段
	 *
	 * @param token
	 */
	public void preInsert(UserInfoToken token) {
		this.setSid(UUID.randomUUID().toString());
		this.setInsertTime(new Date());
		if (token != null) {
			this.setTradeCode(token.getCompany());
			this.setInsertUser(token.getUserNo());
			this.setInsertUserName(token.getUserName());
		}

	}
	public void preInsert1(UserInfoToken token) {
		this.setInsertTime(new Date());
		if (token != null) {
			this.setTradeCode(token.getCompany());
			this.setInsertUser(token.getUserNo());
			this.setInsertUserName(token.getUserName());
		}

	}

	/***
	 * 设置通用的更新字段
	 *
	 * @param token
	 */
	public void preUpdate(UserInfoToken token) {
		this.setUpdateTime(new Date());
		if (token != null) {
			this.setUpdateUser(token.getUserNo());
			this.setUpdateUserName(token.getUserName());
		}

	}

	/**
	 * 提单sid
	 */
	@Transient
	private String erpHeadId;
	/**
	 * 差异结果
	 */
	@Transient
	private String differenceCompare;
	@Transient
	private String userNo;
}
