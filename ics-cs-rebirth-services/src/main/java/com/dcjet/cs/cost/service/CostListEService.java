
package com.dcjet.cs.cost.service;

import com.dcjet.cs.base.service.BasicService;
import com.dcjet.cs.cost.model.CostListE;
import com.dcjet.cs.dto.cost.CostListEDto;
import com.dcjet.cs.dto.cost.CostListEParam;
import com.dcjet.cs.dto.cost.CostListESearchParam;


/***
 * 出口费用表体 service 接口
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2020-02-27
 */
public interface CostListEService 
        extends BasicService<CostListE, CostListEDto, CostListEParam, CostListESearchParam> {
}