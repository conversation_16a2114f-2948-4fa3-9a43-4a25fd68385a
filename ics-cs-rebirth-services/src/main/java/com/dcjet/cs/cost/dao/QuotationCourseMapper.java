package com.dcjet.cs.cost.dao;

import com.dcjet.cs.cost.model.QuotationCourse;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * QuotationCourse
 *
 * <AUTHOR>
 * @date: 2020-7-8
 */
public interface QuotationCourseMapper extends Mapper<QuotationCourse> {
    /**
     * 查询获取数据
     *
     * @param quotationCourse
     * @return
     */
    List<QuotationCourse> getList(QuotationCourse quotationCourse);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 根据headId批量获取sid
     */
    List<String> getSidByHeadIds(List<String> headIds);

    /**
     * 根据headId删除
     */
    int deleteByHeadIds(List<String> headIds);
}
