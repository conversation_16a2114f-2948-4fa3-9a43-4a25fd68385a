package com.dcjet.cs.cost.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-8-16
 */
@Setter
@Getter
@Table(name = "T_COST_BASIC_INFO")
public class CostBasicInfo extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 预录入单表头sid
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 计费重量
     */
    @Column(name = "WEIGHT")
    private BigDecimal weight;
    /**
     * 体积
     */
    @Column(name = "VOLUME")
    private BigDecimal volume;
    /**
     * 仓储天数
     */
    @Column(name = "STORE_TIME")
    private Integer storeTime;
    /**
     * 维护状态（0 未预估 1 已预估 2 已修改）
     */
    @Column(name = "MAINTAIN_STATUS")
    private String maintainStatus;
    /**
     * 锁定状态（0 未锁定 1 已锁定）
     */
    @Column(name = "LOCK_STATUS")
    private String lockStatus;
    /**
     * 查验次数
     */
    @Column(name = "CHECK_AMOUNT")
    private Integer checkAmount;
    /**
     * 预估时间
     */
    @Column(name = "ESTIMATE_TIME")
    private Date estimateTime;
}
