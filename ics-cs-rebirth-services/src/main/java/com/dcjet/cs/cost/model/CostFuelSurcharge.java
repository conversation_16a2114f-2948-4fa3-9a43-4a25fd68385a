package com.dcjet.cs.cost.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-8-16
 */
@Setter
@Getter
@Table(name = "t_cost_fuel_surcharge")
public class CostFuelSurcharge extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @Column(name = "head_id")
    private String headId;
    /**
     * 年
     */
    @Column(name = "year")
    private Integer year;
    /**
     * 月
     */
    @Column(name = "month")
    private Integer month;
    /**
     * 周
     */
    @Column(name = "week")
    private Integer week;
    /**
     * 燃油率
     */
    @Column(name = "FUEL_SURCHARGE")
    private BigDecimal fuelSurcharge;
}
