package com.dcjet.cs.cost.service;

import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.cost.dao.ImpTmpQuoListMapper;
import com.dcjet.cs.cost.dao.QuotationHeadMapper;
import com.dcjet.cs.cost.dao.QuotationListMapper;
import com.dcjet.cs.cost.mapper.QuotationListDtoMapper;
import com.dcjet.cs.cost.model.ImpTmpQuoList;
import com.dcjet.cs.cost.model.QuotationHead;
import com.dcjet.cs.cost.model.QuotationList;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.base.service.BaseService;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.EnumCollection;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务系统校验数据、持久化数据的业务类（采用临时表校验，只要用到临时表就算临时表校验）
 * 临时表校验需要在校验方法checkData中返回TempOwnerId，并在持久化数据方法persistData中接收
 * 导入服务在发现业务采用了临时表校验（即checkData方法返回的结果包含TempOwnerId）时，
 * 在调用persistData方法时便不会把正确数据文件重新解析传递给业务方法，减少不必要的解析时间
 */
@Service
public class QuotationListImportService extends BaseService<ImpTmpQuoList> implements ImportHandlerInterface {
    @Resource
    private BulkInsertConfig bulkInsertConfig;

    public IBulkInsert getBulkInsertInstance() throws Exception {
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }

    @Resource
    private ImpTmpQuoListMapper mapper;
    @Resource
    private QuotationListMapper quotationListMapper;
    @Resource
    private QuotationListDtoMapper quotationListDtoMapper;
    @Resource
    private QuotationHeadMapper quotationHeadMapper;

    @Override
    public Mapper<ImpTmpQuoList> getMapper() {
        return mapper;
    }

    // 0：正确数据 1：错误数据 对应于EnumCollection.EntityListType的枚举的ordinal
    public final int CORRECT_FLAG = EnumCollection.EntityListType.CORRECT_LIST.ordinal();
    public final int WRONG_FLAG = EnumCollection.EntityListType.WRONG_LIST.ordinal();

    /**
     * 返回当前业务类对应的任务类型，导入服务执行器将根据此方法的返回值获取相应的业务类实例
     *
     * @return 当前业务类对应的任务类型
     */
    @Override
    public String getTaskCode() {
        String strTaskCode = "QUOTATION_LIST,QUOTATION_LIST_UPS";
        return strTaskCode;
    }

    /**
     * 业务校验方法（临时表校验）
     *
     * @param mapBasicParam    任务基础参数(当前任务的sid、taskCode、tradeCode、importType、insertUserOid、insertUserName)
     * @param mapBusinessParam 业务自定义参数(比如insertUser、插入表体时用到的表头id等都可以在这里传入,需在前端导入时确定需要用到的参数并传递)
     * @param mapObjectList    实体列表map，可根据xmlContent的sheet的id获取某类实体列表
     * @return 业务校验结果，head根据xmlContent的sheet的field获取，无需设置；当采用临时表校验方式时tempOwnerId必须设置，否则插入正确数据时无法进行操作
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ExcelImportDto> checkData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        List<ExcelImportDto> excelImportDtoList = new ArrayList<>();
        if (mapObjectList.size() <= 0) {
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }
        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        //从自定义参数中获取需要的数据
        if (!mapBasicParam.containsKey("insertUserOid") || !mapBasicParam.containsKey("insertUserName")
                || !mapBusinessParam.containsKey("headId")) {
            XdoImportLogger.log("部分自定义参数未成功获取");
            return null;
        }
        String insertUser = mapBasicParam.get("insertUserOid").toString();
        String insertUserName = mapBasicParam.get("insertUserName").toString();
        String headId = mapBusinessParam.get("headId").toString();
        //遍历当前实体列表进行校验
        String guid = UUID.randomUUID().toString();
        int intObjectListSize = objectList.size();
        XdoImportLogger.log("共获取实体：" + intObjectListSize);
        List<ImpTmpQuoList> impTmpQuoLists = Collections.synchronizedList(new ArrayList<>(intObjectListSize));
        QuotationHead quotationHead = quotationHeadMapper.selectByPrimaryKey(headId);
        objectList.forEach(pa -> {
            ImpTmpQuoList el = (ImpTmpQuoList) pa;
            el.setSid(UUID.randomUUID().toString());
            el.setTempOwner(guid);
            el.setTradeCode(tradeCode);
            el.setInsertUser(insertUser);
            el.setInsertUserName(insertUserName);
            el.setInsertTime(new Date());
            el.setUpdateUser(insertUser);
            el.setUpdateTime(new Date());
            el.setHeadId(headId);
            el.setCalType(CommonEnum.CAL_TYPE_ENUM.CAL_TYPE_999.getCode());
            el.setCurr(quotationHead.getCurr());
            impTmpQuoLists.add(el);
        });
        check(impTmpQuoLists, headId);
        XdoImportLogger.log("共获取插入实体：" + impTmpQuoLists.size() + ";执行快速入库操作");
        //校验错误数据
        ExcelImportDto<ImpTmpQuoList> excelImportDto = new ExcelImportDto<>();
        excelImportDto.setTempOwnerId(guid);
        List<ImpTmpQuoList> correctList = impTmpQuoLists.stream().filter(e -> 0 == e.getTempFlag()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(correctList)) {
            //初始化快速入库实例（在config文件夹下新建OracleBulkConfig用于读取配置文件中的数据库连接配置）
            IBulkInsert iBulkInsert = getBulkInsertInstance();
            int intEffectCount = 0;
            try {
                intEffectCount = iBulkInsert.fastImport(correctList);
            } catch (BulkInsertException ex) {
                intEffectCount = ex.getCommitRowCount();
                XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
                XdoImportLogger.log(ex);
                return null;
            } catch (Exception ex) {
                XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
                XdoImportLogger.log(ex);
                return null;
            }
        }
        List<ImpTmpQuoList> wrongList = impTmpQuoLists.stream().filter(e -> 1 == e.getTempFlag()).collect(Collectors.toList());
        excelImportDto.setCorrectList(correctList);
        excelImportDto.setWrongNumber(wrongList.size());
        if (wrongList.size() > 0 && correctList.size() > 0) {
            wrongList.addAll(correctList);
        }
        excelImportDto.setWrongList(wrongList);
        XdoImportLogger.log("correct：" + excelImportDto.getCorrectList().size()
                + ";wrong：" + excelImportDto.getWrongList().size());
        excelImportDtoList.add(excelImportDto);
        return excelImportDtoList;
    }

    /**
     * 业务持久化数据方法
     *
     * @param mapBasicParam    任务基础参数(当前任务的sid、taskCode、tradeCode、importType、insertUserOid、insertUserName)
     * @param mapBusinessParam 业务自定义参数(比如insertUser、插入表体时用到的表头id等都可以在这里传入,需在前端导入时确定需要用到的参数并传递)
     * @param mapObjectList    正确数据序列化的实体列表集合，根据导入配置文件(xmlContent)的每个sheet配置的id获取相应实体列表（采用内存校验时传递）
     * @param tempOwnerIdList  临时表处理批次Id，单sheet时取第一条（采用临时表校验时传递）
     * @return 业务持久化数据结果，导入服务根据返回值设置任务状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject persistData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        //按配置顺序（同时也是业务校验时添加到excelImportDtoList的顺序）获取临时表批次Id
        if (tempOwnerIdList == null || tempOwnerIdList.size() <= 0) {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("临时表处理批次Id为空，无法执行持久化数据方法"));
            return resultObject;
        }
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        //按顺序获取临时表批次Id，单sheet时只需获取第一个即可
        String strTempOwnerId = tempOwnerIdList.get(0);
        List<ImpTmpQuoList> tempList = selectDataList(strTempOwnerId, CORRECT_FLAG);
        Date currDate = new Date();
        if (CollectionUtils.isNotEmpty(tempList)) {
            QuotationList param = new QuotationList() {{
                setHeadId(tempList.get(0).getHeadId());
            }};
            List<QuotationList> quotationLists = quotationListMapper.getList(param);
            tempList.forEach(e -> {
                QuotationList quotationList = new QuotationList();
                quotationListDtoMapper.tempToBean(e,quotationList);
                quotationList.setSid(UUID.randomUUID().toString());
                quotationList.setInsertTime(currDate);

                List<QuotationList> upperList = quotationLists.stream().filter(head ->
                        head.getUpperWeight().compareTo(quotationList.getUpperWeight()) > 0).collect(Collectors.toList());
                List<QuotationList> lowList = quotationLists.stream().filter(head ->
                        head.getUpperWeight().compareTo(quotationList.getUpperWeight()) < 0).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(lowList)) {
                    QuotationList lowQuo = lowList.stream().max(Comparator.comparing(QuotationList::getUpperWeight)).get();
                    quotationList.setLowWeight(lowQuo.getUpperWeight());
                } else {
                    quotationList.setLowWeight(BigDecimal.ZERO);
                }
                if (CollectionUtils.isNotEmpty(upperList)) {
                    QuotationList upperQuo = upperList.stream().min(Comparator.comparing(QuotationList::getUpperWeight)).get();
                    upperQuo.setLowWeight(quotationList.getUpperWeight());
                    quotationListMapper.updateByPrimaryKey(upperQuo);
                }
                quotationListMapper.insert(quotationList);
                quotationLists.add(quotationList);
            });
            if (StringUtils.isNotEmpty(strTempOwnerId)) {
                ImpTmpQuoList impTmpQuoList = new ImpTmpQuoList();
                impTmpQuoList.setTradeCode(tradeCode);
                impTmpQuoList.setTempOwner(strTempOwnerId);
                mapper.deleteByTempOwner(impTmpQuoList);
            }
        }
        return resultObject;
    }

    /**
     * 描述：获取导入的各种类型数据
     *
     * @param tempOwner
     * @param tempFlag
     * @return
     * <AUTHOR>
     * @date 2019-03-16
     */
    private List<ImpTmpQuoList> selectDataList(String tempOwner, int tempFlag) {
        Example example = new Example(ImpTmpQuoList.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("tempOwner", tempOwner);
        criteria.andIn("tempFlag", Arrays.asList(tempFlag));
        return mapper.selectByExample(example);
    }

    /**
     * 校验数据
     */
    private void check(List<ImpTmpQuoList> list, String headId) {
        if (CollectionUtils.isNotEmpty(list)) {
            QuotationList quotationList = new QuotationList() {{
                setHeadId(headId);
            }};
            List<QuotationList> quotationLists = quotationListMapper.getList(quotationList);
            Map<String, List<QuotationList>> quoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(quotationLists)) {
                quoMap = quotationLists.stream().collect(Collectors.groupingBy(e -> e.getUpperWeight().stripTrailingZeros().toPlainString()));
            }
            Map<String, List<QuotationList>> finalQuoMap = quoMap;
            list.forEach(e -> {
                if (e.getTempFlag() == 0 && e.getUpperWeight() == null) {
                    e.setTempFlag(1);
                    e.setTempRemark(xdoi18n.XdoI18nUtil.t("重量上限不能为空！"));
                }
                if (e.getTempFlag() == 0 && StringUtils.isEmpty(e.getPriceType())) {
                    e.setTempFlag(1);
                    e.setTempRemark(xdoi18n.XdoI18nUtil.t("价格类型不能为空！"));
                }
                if (MapUtils.isNotEmpty(finalQuoMap) && finalQuoMap.containsKey(e.getUpperWeight().toPlainString())) {
                    e.setTempFlag(1);
                    e.setTempRemark(String.format("[%s]%s",e.getUpperWeight(),xdoi18n.XdoI18nUtil.t("重量上限已存在！")));
                }
            });
            Map<BigDecimal, List<ImpTmpQuoList>> maps = list.stream().filter(e -> e.getUpperWeight() != null).collect(Collectors.groupingBy(e -> e.getUpperWeight()));
            for (BigDecimal key : maps.keySet()) {
                if (maps.get(key).size() > 1) {
                    maps.get(key).forEach(e -> {
                        e.setTempFlag(1);
                        e.setTempRemark(String.format("[%s]%s",e.getUpperWeight(),xdoi18n.XdoI18nUtil.t("导入数据中重量上限重复")));
                    });
                }
            }

        }
    }
}
