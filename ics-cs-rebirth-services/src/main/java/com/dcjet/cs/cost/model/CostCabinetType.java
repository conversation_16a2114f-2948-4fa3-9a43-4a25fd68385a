package com.dcjet.cs.cost.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-5
 */
@Setter
@Getter
@Table(name = "T_COST_CABINET_TYPE")
public class CostCabinetType extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 车柜属性(1 国内段 2 国际段)
     */
    @Column(name = "CABINET_ATTR")
    private String cabinetAttr;
    /**
     * 车柜类型
     */
    @Column(name = "CABINET_TYPE")
    private String cabinetType;
    /**
     * 车柜名称
     */
    @Column(name = "CABINET_NAME")
    private String cabinetName;
    /**
     * 车柜数量
     */
    @Column(name = "CABINET_AMOUNT")
    private Integer cabinetAmount;
    /**
     * 提单表头sid
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 进出口标记
     */
    @Column(name = "IE_MARK")
    private String ieMark;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
}
