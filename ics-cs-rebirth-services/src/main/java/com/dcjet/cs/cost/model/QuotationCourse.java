package com.dcjet.cs.cost.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-7-8
 */
@Setter
@Getter
@Table(name = "T_COST_QUO_COURSE")
public class QuotationCourse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 科目种类
     */
    @Column(name = "COURSE_TYPE")
    private String courseType;
    /**
     * 科目代码
     */
    @Column(name = "COURSE_CODE")
    private String courseCode;
    /**
     * 计费种类（1 票数 2 计费重量 3 车柜型 4 天/重量计 5 天/体积计 6 体积）
     */
    @Column(name = "CHARGE_TYPE")
    private String chargeType;
    /**
     * 币制
     */
    @Column(name = "CURR")
    private String curr;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 对应报价单表头sid
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 最低收费标准
     */
    @Column(name = "MIN_CHARGE")
    private BigDecimal minCharge;
    /**
     * 最高收费标准
     */
    @Column(name = "MAX_CHARGE")
    private BigDecimal maxCharge;
    /**
     * 柜属性（0 普货 1 冷柜）
     */
    @Column(name = "CABINET_TYPE")
    private String cabinetType;
    /**
     * 免费仓储天数
     */
    @Column(name = "FREE_STORE")
    private Integer freeStore;

    @Transient
    private String flag;
}
