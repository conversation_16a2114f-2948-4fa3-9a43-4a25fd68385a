package com.dcjet.cs.cost.mapper;

import com.dcjet.cs.cost.model.CostCompareEHead;
import com.dcjet.cs.dto.cost.CostCompareEHeadDto;
import com.dcjet.cs.dto.cost.CostCompareEHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-8-15
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CostCompareEHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CostCompareEHeadDto toDto(CostCompareEHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CostCompareEHead toPo(CostCompareEHeadParam param);
    /**
     * 数据库原始数据更新
     * @param costCompareEHeadParam
     * @param costCompareEHead
     */
    void updatePo(CostCompareEHeadParam costCompareEHeadParam, @MappingTarget CostCompareEHead costCompareEHead);
    default void patchPo(CostCompareEHeadParam costCompareEHeadParam, CostCompareEHead costCompareEHead) {
        // TODO 自行实现局部更新
    }
}
