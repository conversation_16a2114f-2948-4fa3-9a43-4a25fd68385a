<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.cost.dao.ActualCostCompareMapper">
    <sql id="condition">
        <if test="ieMark != null and ieMark != ''">
            and t.i_e_mark = #{ieMark}
        </if>
        <if test="isAgreement != null and isAgreement != ''">
            and t.is_agreement = #{isAgreement}
        </if>
        <if test="emsListNo != null and emsListNo != ''">
            and t.ems_list_no like concat(concat('%',#{emsListNo}),'%')
        </if>
        <if test="transMode != null and transMode != ''">
            and t.TRANS_MODE = #{transMode}
        </if>
        <if test='_databaseId == "postgresql" '>
            <if test="insertTimeFrom != null and insertTimeFrom != ''">
                <![CDATA[ and t.INSERT_TIME >= to_timestamp(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="insertTimeTo != null and insertTimeTo != ''">
                <![CDATA[ and t.INSERT_TIME < to_timestamp(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="insertTimeFrom != null and insertTimeFrom != ''">
                <![CDATA[ and t.INSERT_TIME >= to_date(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="insertTimeTo != null and insertTimeTo != ''">
                <![CDATA[ and t.INSERT_TIME < to_date(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss')+1 ]]>
            </if>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultType="com.dcjet.cs.dto.cost.ActualCostCompareDto"
            parameterType="com.dcjet.cs.dto.cost.ActualCostCompareParam">
        SELECT * FROM
        (
            SELECT
                dec_head.ems_list_no,
                dec_head.trans_mode,
                dec_head.insert_time,
                'E' AS i_e_Mark,
                dec_head.fee_mark,
                dec_head.fee_curr,
                dec_head.fee_rate,
                dec_head.insur_curr,
                dec_head.insur_rate,
                dec_head.insur_mark,
                actual.fee_curr AS actual_fee_curr,
                actual.fee_mark AS actual_fee_mark,
                actual.fee_rate AS actual_fee_rate,
                actual.insur_curr AS actual_insur_curr,
                actual.insur_mark AS actual_insur_mark,
                actual.insur_rate AS actual_insur_rate,
                actual.is_agreement,
                actual.note
            FROM t_dec_erp_e_head_n dec_head
            JOIN t_cost_actual_info actual
                ON actual.head_id = dec_head.sid
            WHERE dec_head.trade_code = #{tradeCode}
            UNION ALL
            SELECT
                dec_head.ems_list_no,
                dec_head.trans_mode,
                dec_head.insert_time,
                'I' AS i_e_Mark,
                dec_head.fee_mark,
                dec_head.fee_curr,
                dec_head.fee_rate,
                dec_head.insur_curr,
                dec_head.insur_rate,
                dec_head.insur_mark,
                actual.fee_curr AS actual_fee_curr,
                actual.fee_mark AS actual_fee_mark,
                actual.fee_rate AS actual_fee_rate,
                actual.insur_curr AS actual_insur_curr,
                actual.insur_mark AS actual_insur_mark,
                actual.insur_rate AS actual_insur_rate,
                actual.is_agreement,
                actual.note
            FROM t_dec_erp_i_head_n dec_head
            JOIN t_cost_actual_info actual
                ON actual.head_id = dec_head.sid
            WHERE dec_head.trade_code = #{tradeCode}
        ) T
        <where>
            <include refid="condition"></include>
        </where>
        order by t.i_e_mark,insert_time desc
    </select>
</mapper>
