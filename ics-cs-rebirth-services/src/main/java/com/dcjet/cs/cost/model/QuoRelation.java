package com.dcjet.cs.cost.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-7-9
 */
@Setter
@Getter
@Table(name = "T_COST_QUO_RELATION")
public class QuoRelation implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 报价单表头sid
     */
    @Column(name = "QUO_SID")
    private String quoSid;
    /**
     * 报价单编号
     */
    @Column(name = "QUO_NO")
    private String quoNo;
    /**
     * 报价单名称
     */
    @Column(name = "QUO_NAME")
    private String quoName;
    /**
     * 报价单类型
     */
    @Column(name = "QUO_TYPE")
    private String quoType;
    /**
     * 报价单表头备注
     */
    @Column(name = "QUO_NOTE")
    private String quoNote;
    /**
     * 报价汇总
     */
    @Column(name = "DEC_TOTAL")
    private BigDecimal decTotal;
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 对应供应商报价sid
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
}
