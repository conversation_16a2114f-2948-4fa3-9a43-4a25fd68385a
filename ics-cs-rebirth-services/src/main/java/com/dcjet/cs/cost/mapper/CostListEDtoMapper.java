
package com.dcjet.cs.cost.mapper;

import com.dcjet.cs.base.mapper.BasicConverter;
import com.dcjet.cs.cost.model.CostListE;
import com.dcjet.cs.dto.cost.CostListEDto;
import com.dcjet.cs.dto.cost.CostListEParam;
import com.dcjet.cs.dto.cost.CostListESearchParam;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/***
 * 出口费用表体转换
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2020-02-27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CostListEDtoMapper extends BasicConverter<CostListE, CostListEDto, CostListEParam, CostListESearchParam> {
}