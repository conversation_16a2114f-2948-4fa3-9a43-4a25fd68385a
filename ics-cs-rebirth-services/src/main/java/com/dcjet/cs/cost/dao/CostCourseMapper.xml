<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.cost.dao.CostCourseMapper">
    <sql id="columns">
        t.SID,
		t.COST_COURSE_CODE,
		t.COST_COURSE_NAME,
		t.I_E_MARK,
		t.INSERT_TIME,
		t.INSERT_USER,
		t.INSERT_USER_NAME,
		t.NOTE,
		t.SERIAL_NO,
		t.TRADE_CODE,
		t.UPDATE_TIME,
		t.UPDATE_USER,
		t.UPDATE_USER_NAME,
		t.VALID_STATUS,
		t.COURSE_TYPE,
		t.IS_FIXED
    </sql>
    <sql id="condition">
            (t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR} or t.TRADE_CODE = '9999999999')
        <if test="sid != null and sid != ''">
            and t.SID = #{sid,jdbcType=VARCHAR}
        </if>
		<if test="costCourseCode != null and costCourseCode != ''">
            and t.COST_COURSE_CODE = #{costCourseCode,jdbcType=VARCHAR}
        </if>
		<if test="costCourseName != null and costCourseName != ''">
            and t.COST_COURSE_NAME = #{costCourseName,jdbcType=VARCHAR}
        </if>
		<if test="iEMark != null and iEMark != ''">
            and t.I_E_MARK = #{iEMark,jdbcType=VARCHAR}
        </if>
        <if test="ieMarkList != null">
            and t.I_E_MARK in
            <foreach collection="ieMarkList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
	    <if test="status != null and status != ''">
            and t.STATUS = #{status,jdbcType=VARCHAR}
        </if>
		<if test="note != null and note != ''">
            and t.NOTE = #{note,jdbcType=VARCHAR}
        </if>
		<if test="serialNo != null and serialNo != ''">
            and t.SERIAL_NO = #{serialNo,jdbcType=NUMERIC}
        </if>
		<if test="validStatus != null and validStatus != ''">
            and t.VALID_STATUS = #{validStatus,jdbcType=VARCHAR}
        </if>
        <if test="idList != null">
            and t.SID in
            <foreach collection="idList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="courseType != null and courseType != ''">
            and t.COURSE_TYPE = #{courseType,jdbcType=VARCHAR}
        </if>

        <if test="isFixed != null and isFixed != ''">
            and t.IS_FIXED = #{isFixed,jdbcType=VARCHAR}
        </if>
    </sql>
    <select id="getList" parameterType="com.dcjet.cs.cost.model.CostCourse" resultType="com.dcjet.cs.cost.model.CostCourse">
        SELECT * FROM (
            SELECT T.SID,
                   T.COST_COURSE_CODE,
                   T.COST_COURSE_NAME,
                   T.I_E_MARK,
                   T.INSERT_TIME,
                   T.INSERT_USER,
                   T.INSERT_USER_NAME,
                   T.NOTE,
                   T.SERIAL_NO,
                   T.TRADE_CODE,
                   T.UPDATE_TIME,
                   T.UPDATE_USER,
                   T.UPDATE_USER_NAME,
                   T.VALID_STATUS,
                   T.COURSE_TYPE,
                   T.IS_FIXED,
                   CASE WHEN isUsed.C IS NULL
                       OR isUsed.C = 0 THEN
                       '1' ELSE'2'
                   END AS STATUS
            FROM T_COST_COURSE T
            LEFT JOIN (
                SELECT u.COST_COURSE_CODE, u.I_E_MARK, COUNT ( 1 ) C
                FROM (
                    SELECT DISTINCT e.COST_COURSE_CODE, 'E' AS I_E_MARK
                    FROM T_COST_LIST_E e
                    WHERE e.TRADE_CODE = #{tradeCode}
                    UNION ALL
                    SELECT DISTINCT i.COST_COURSE_CODE, 'I' AS I_E_MARK
                    FROM T_COST_LIST_I i
                    WHERE i.TRADE_CODE = #{tradeCode}
                    UNION ALL SELECT q.course_code, h.ie_type
                    FROM t_cost_quo_course q
                    JOIN t_cost_quo_head h ON h.sid = q.head_id
                        AND q.trade_code = #{tradeCode}
                ) u
                GROUP BY u.COST_COURSE_CODE, u.I_E_MARK
            ) isUsed ON isUsed.COST_COURSE_CODE = T.COST_COURSE_CODE
            AND ( isUsed.I_E_MARK = T.I_E_MARK OR isUsed.I_E_MARK = 'N' )
        ) t
        <where>
            <include refid="condition"></include>
        </where>
        ORDER BY STATUS DESC, t.VALID_STATUS DESC, t.COST_COURSE_CODE
    </select>
    <select id="findByCode" resultType="com.dcjet.cs.cost.model.CostCourse">
        select
            <include refid="columns"></include>
        from T_COST_COURSE t
        WHERE t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
            AND t.COST_COURSE_CODE = #{code,jdbcType=VARCHAR}
    </select>

</mapper>