package com.dcjet.cs.cost.mapper;

import com.dcjet.cs.cost.model.CostBasicInfo;
import com.dcjet.cs.dto.cost.CostBasicInfoDto;
import com.dcjet.cs.dto.cost.CostBasicInfoParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-8-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CostBasicInfoDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CostBasicInfoDto toDto(CostBasicInfo po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CostBasicInfo toPo(CostBasicInfoParam param);

    /**
     * 数据库原始数据更新
     *
     * @param costBasicInfoParam
     * @param costBasicInfo
     */
    void updatePo(CostBasicInfoParam costBasicInfoParam, @MappingTarget CostBasicInfo costBasicInfo);

    default void patchPo(CostBasicInfoParam costBasicInfoParam, CostBasicInfo costBasicInfo) {
        // TODO 自行实现局部更新
    }
}
