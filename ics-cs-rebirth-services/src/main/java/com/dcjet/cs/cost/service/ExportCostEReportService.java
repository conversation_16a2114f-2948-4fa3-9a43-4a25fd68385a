package com.dcjet.cs.cost.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.cost.dao.CostHeadEMapper;
import com.dcjet.cs.cost.mapper.CostHeadEDtoMapper;
import com.dcjet.cs.cost.model.CostHeadE;
import com.dcjet.cs.dto.cost.CostHeadEDto;
import com.dcjet.cs.dto.cost.CostHeadESearchParam;
import com.dcjet.cs.dto.cost.CostHeadIDto;
import com.dcjet.cs.util.CommonEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 出口费用报表-异步导出
 * @author: WJ
 * @createDate: 2020/9/14 14:13
 */
@Component
public class ExportCostEReportService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private CostHeadEDtoMapper costHeadEDtoMapper;
    @Resource
    private CostHeadEMapper costHeadEMapper;
    @Resource
    private PCodeHolder pCodeHolder;

    private final String taskName = xdoi18n.XdoI18nUtil.t("出口费费用报表-异步导出(COST_E_REPORT)");

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("COST_E_REPORT");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        CostHeadESearchParam exportParam = convertParam(taskInfo);
        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        CostHeadE e = costHeadEDtoMapper.fromSearchParam(exportParam);
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(e));
        Integer count = costHeadEMapper.selectDataCount(e);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        CostHeadESearchParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        CostHeadE e = costHeadEDtoMapper.fromSearchParam(exportParam);
        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(e));
        Page<CostHeadE> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> costHeadEMapper.getReport(e));

        List<CostHeadEDto> costHeadEDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            costHeadEDtos = page.getResult().stream().map(head -> {
                CostHeadEDto dto = costHeadEDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        convertPrint(costHeadEDtos);

        List<Object> list = new ArrayList<>();
        list.addAll(costHeadEDtos);
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    private CostHeadESearchParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        CostHeadESearchParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, CostHeadESearchParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount){
        return 5000;
    }

    private void convertPrint(List<CostHeadEDto> costHeadEDtos) {
        if (CollectionUtils.isNotEmpty(costHeadEDtos)) {
            costHeadEDtos.forEach(e -> {
                if (StringUtils.isNotEmpty(e.getStatus())) {
                    e.setStatus(CommonEnum.COST_STATUS_ENUM.getValue(e.getStatus()));
                }
                if (StringUtils.isNotEmpty(e.getTrafMode())) {
                    e.setTrafMode(e.getTrafMode() + " " + pCodeHolder.getValue(PCodeType.TRANSF, e.getTrafMode()));
                }
                if (StringUtils.isNotEmpty(e.getWrapType())) {
                    e.setWrapType(e.getWrapType() + " " + pCodeHolder.getValue(PCodeType.WRAP, e.getWrapType()));
                }
                if (StringUtils.isNotEmpty(e.getIEPort())) {
                    e.setIEPort(e.getIEPort() + " " + pCodeHolder.getValue(PCodeType.CUSTOMS_REL, e.getIEPort()));
                }
                if (StringUtils.isNotEmpty(e.getTradeCountry())) {
                    e.setTradeCountry(e.getTradeCountry() + " " + pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, e.getTradeCountry()));
                }
                if (StringUtils.isNotEmpty(e.getMasterCustoms())) {
                    e.setMasterCustoms(e.getMasterCustoms() + " " + pCodeHolder.getValue(PCodeType.CUSTOMS_REL, e.getMasterCustoms()));
                }
                if (StringUtils.isNotEmpty(e.getCurr())) {
                    e.setCurr(e.getCurr() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, e.getCurr()));
                }
                if (StringUtils.isNotEmpty(e.getBodyCurr())) {
                    e.setBodyCurr(e.getBodyCurr() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, e.getBodyCurr()));
                }
                if (StringUtils.isNotEmpty(e.getForwardCode()) && StringUtils.isNotEmpty(e.getForwardName())) {
                    e.setForwardCode(e.getDeclareCode() + " " + e.getForwardName());
                }
                if (StringUtils.isNotEmpty(e.getDeclareCode()) && StringUtils.isNotEmpty(e.getDeclareName())) {
                    e.setDeclareCode(e.getDeclareCode() + " " + e.getDeclareName());
                }
                if (StringUtils.isNotEmpty(e.getRemitteeCode()) && StringUtils.isNotEmpty(e.getRemitteeName())) {
                    e.setRemitteeCode(e.getRemitteeCode() + " " + e.getRemitteeName());
                }
                if(StringUtils.isNotEmpty(e.getCostCourseName())){
                    e.setCostCourseCode(e.getCostCourseName());
                }
            });
        }
    }
}
