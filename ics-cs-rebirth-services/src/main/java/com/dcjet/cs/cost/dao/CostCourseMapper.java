package com.dcjet.cs.cost.dao;

import com.dcjet.cs.base.repository.BasicMapper;
import com.dcjet.cs.cost.model.CostCourse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***
 * 费用科目
 *
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2020-02-26
 */
public interface CostCourseMapper extends BasicMapper<CostCourse> {

    /***
     * 根据code获取数据
     *
     * @param tradeCode
     * @param code
     * @return
     */
    List<CostCourse> findByCode(@Param("tradeCode") String tradeCode, @Param("code") String code);
}