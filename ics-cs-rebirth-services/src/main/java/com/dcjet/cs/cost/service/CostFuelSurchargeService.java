package com.dcjet.cs.cost.service;

import com.dcjet.cs.cost.dao.CostFuelSurchargeMapper;
import com.dcjet.cs.cost.dao.QuotationHeadMapper;
import com.dcjet.cs.cost.mapper.CostFuelSurchargeDtoMapper;
import com.dcjet.cs.cost.model.CostFuelSurcharge;
import com.dcjet.cs.cost.model.QuotationHead;
import com.dcjet.cs.dto.cost.CostFuelSurchargeDto;
import com.dcjet.cs.dto.cost.CostFuelSurchargeParam;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-8-16
 */
@Service
public class CostFuelSurchargeService extends BaseService<CostFuelSurcharge> {
    @Resource
    private CostFuelSurchargeMapper costFuelSurchargeMapper;
    @Resource
    private CostFuelSurchargeDtoMapper costFuelSurchargeDtoMapper;
    @Resource
    private QuotationHeadMapper quotationHeadMapper;

    @Override
    public Mapper<CostFuelSurcharge> getMapper() {
        return costFuelSurchargeMapper;
    }

    /**
     * 获取分页信息
     *
     * @param costFuelSurchargeParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<CostFuelSurchargeDto>> getListPaged(CostFuelSurchargeParam costFuelSurchargeParam, PageParam pageParam) {
        // 启用分页查询
        CostFuelSurcharge costFuelSurcharge = costFuelSurchargeDtoMapper.toPo(costFuelSurchargeParam);
        Page<CostFuelSurcharge> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> costFuelSurchargeMapper.getList(costFuelSurcharge));
        List<CostFuelSurchargeDto> costFuelSurchargeDtos = page.getResult().stream().map(head -> {
            CostFuelSurchargeDto dto = costFuelSurchargeDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<CostFuelSurchargeDto>> paged = ResultObject.createInstance(costFuelSurchargeDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param costFuelSurchargeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CostFuelSurchargeDto insert(CostFuelSurchargeParam costFuelSurchargeParam, UserInfoToken userInfo) {
        CostFuelSurcharge costFuelSurcharge = costFuelSurchargeDtoMapper.toPo(costFuelSurchargeParam);
        checkData(costFuelSurchargeParam);
        /**
         * 规范固定字段
         */
        costFuelSurcharge.preInsert(userInfo);
        // 新增数据
        int insertStatus = costFuelSurchargeMapper.insert(costFuelSurcharge);
        return insertStatus > 0 ? costFuelSurchargeDtoMapper.toDto(costFuelSurcharge) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param costFuelSurchargeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CostFuelSurchargeDto update(CostFuelSurchargeParam costFuelSurchargeParam, UserInfoToken userInfo) {
        CostFuelSurcharge costFuelSurcharge = costFuelSurchargeMapper.selectByPrimaryKey(costFuelSurchargeParam.getSid());
        checkData(costFuelSurchargeParam);
        costFuelSurchargeDtoMapper.updatePo(costFuelSurchargeParam, costFuelSurcharge);
        costFuelSurcharge.preUpdate(userInfo);
        // 更新数据
        int update = costFuelSurchargeMapper.updateByPrimaryKey(costFuelSurcharge);
        return update > 0 ? costFuelSurchargeDtoMapper.toDto(costFuelSurcharge) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        costFuelSurchargeMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CostFuelSurchargeDto> selectAll(CostFuelSurchargeParam exportParam, UserInfoToken userInfo) {
        CostFuelSurcharge costFuelSurcharge = costFuelSurchargeDtoMapper.toPo(exportParam);
        // costFuelSurcharge.setTradeCode(userInfo.getCompany());
        List<CostFuelSurchargeDto> costFuelSurchargeDtos = new ArrayList<>();
        List<CostFuelSurcharge> costFuelSurcharges = costFuelSurchargeMapper.getList(costFuelSurcharge);
        if (CollectionUtils.isNotEmpty(costFuelSurcharges)) {
            costFuelSurchargeDtos = costFuelSurcharges.stream().map(head -> {
                CostFuelSurchargeDto dto = costFuelSurchargeDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return costFuelSurchargeDtos;
    }

    private void checkData(CostFuelSurchargeParam costFuelSurchargeParam) {
        if (costFuelSurchargeParam.getYear() != null && costFuelSurchargeParam.getYear() <= 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("年份必须是大于0的整数"));
        }
        if (costFuelSurchargeParam.getMonth() != null && (costFuelSurchargeParam.getMonth() < 1 || costFuelSurchargeParam.getMonth() > 12)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("月份的取值范围是1-12"));
        }
        if (costFuelSurchargeParam.getWeek() != null && (costFuelSurchargeParam.getWeek() < 1 || costFuelSurchargeParam.getWeek() > 5)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("周的取值范围是1-5"));
        }
        QuotationHead quotationHead = quotationHeadMapper.selectByPrimaryKey(costFuelSurchargeParam.getHeadId());
        CostFuelSurcharge costFuelSurcharge = new CostFuelSurcharge();
        costFuelSurcharge.setHeadId(quotationHead.getSid());
        costFuelSurcharge.setYear(costFuelSurchargeParam.getYear());
        costFuelSurcharge.setMonth(costFuelSurchargeParam.getMonth());
        if ("2".equals(quotationHead.getCountryType())) {
            costFuelSurcharge.setWeek(costFuelSurchargeParam.getWeek());
        }
        costFuelSurcharge.setSid(costFuelSurchargeParam.getSid());
        if (costFuelSurchargeMapper.getList(costFuelSurcharge).size() > 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("当前年月周数据已存在！"));
        }
    }
}
