package com.dcjet.cs.cost.service;

import com.dcjet.cs.aeo.dao.AeoAuditInfoMapper;
import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.cost.dao.CostCompareEHeadMapper;
import com.dcjet.cs.cost.mapper.CostCompareEHeadDtoMapper;
import com.dcjet.cs.cost.model.CostCompareEHead;
import com.dcjet.cs.dto.cost.CostCompareEHeadDto;
import com.dcjet.cs.dto.cost.CostCompareEHeadParam;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: WJ
 * @createDate: 2021/8/23 14:30
 */
@Service
public class CostAuditEService {

    @Resource
    private CostCompareEHeadMapper costCompareEHeadMapper;
    @Resource
    private CostCompareEHeadDtoMapper costCompareEHeadDtoMapper;
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    @Resource
    private CommonService commonService;

    public ResultObject<List<CostCompareEHeadDto>> getAuditPaged(CostCompareEHeadParam costCompareEHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        CostCompareEHead costCompareEHead = costCompareEHeadDtoMapper.toPo(costCompareEHeadParam);
        costCompareEHead.setTradeCode(userInfo.getCompany());
        costCompareEHead.setUserNo(userInfo.getUserNo());
        Page<CostCompareEHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> costCompareEHeadMapper.getAuditPaged(costCompareEHead));
        List<CostCompareEHeadDto> costCompareEHeadDtos = page.getResult().stream().map(head -> {
            CostCompareEHeadDto dto = costCompareEHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<CostCompareEHeadDto>> paged = ResultObject.createInstance(costCompareEHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 审核
     *
     * @param paramList
     * @param status
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<List<CostCompareEHeadDto>> audit(List<CostCompareEHeadParam> paramList, String status,
                                                         UserInfoToken userInfo) {
        if (CollectionUtils.isEmpty(paramList)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("请至少审核一票"));
        }

        Example example = new Example(CostCompareEHead.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sid", paramList.stream().map(CostCompareEHeadParam::getSid).collect(Collectors.toList()));
        List<CostCompareEHead> list = costCompareEHeadMapper.selectByExample(example);
        if (ConstantsStatus.STATUS_2.equals(status)) {
            if (list.stream().filter(x -> !ConstantsStatus.STATUS_1.equals(x.getAuditStatus())).count() > 0) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有待审核状态数据可以进行审核操作"));
            }
        } else if (ConstantsStatus.STATUS_3.equals(status)) {
            if (list.stream().filter(x -> !ConstantsStatus.STATUS_1.equals(x.getAuditStatus())
                    && !ConstantsStatus.STATUS_2.equals(x.getAuditStatus())).count() > 0) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有待审核和内审通过状态数据可以进行内审退回"));
            }
        }

//        if (list.stream().filter(x -> userInfo.getUserNo().equals(x.getSendUser())).count() > 0) {
//            throw new ErrorException(400, "发送审核人无法进行审核");
//        }

        list.stream().forEach(x -> {
            x.setAuditStatus(status);
            x.setAuditor(userInfo.getUserNo());
//            x.preUpdate(userInfo);
        });
        if (paramList.size() == 1) {
            list.get(0).setAuditOpinion(paramList.get(0).getAuditOpinion());
        }
        bulkSqlOpt.batchUpdate(list, CostCompareEHeadMapper.class);

        // 记录审核情况
        List<AeoAuditInfo> aeoAuditInfoList = list.stream().map(x -> commonService.getAeoAuditInfo(userInfo,
                x.getSid(), "COST_AUDIT", status, paramList.get(0).getAuditOpinion())).collect(Collectors.toList());
        bulkSqlOpt.batchInsert(aeoAuditInfoList, AeoAuditInfoMapper.class);

        return ResultObject.createInstance(true, "");
    }

}
