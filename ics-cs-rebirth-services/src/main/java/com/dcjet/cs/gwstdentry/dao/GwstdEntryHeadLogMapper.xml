<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.gwstdentry.dao.GwstdEntryHeadLogMapper">
    <resultMap id="gwstdEntryHeadLogResultMap" type="com.dcjet.cs.gwstdentry.model.GwstdEntryHeadLog">
		<result column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="ENTRY_ID" property="entryId" jdbcType="VARCHAR" />
		<result column="SEQ_NO" property="seqNo" jdbcType="VARCHAR" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR" />
		<result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
		<result column="RETURN_JSON" property="seqNo" jdbcType="VARCHAR" />
		<result column="INSERT_TIME" property="insertTime" jdbcType="DATE"/>
	</resultMap>

</mapper>
