package com.dcjet.cs.gwstdentry.service;

import com.dcjet.cs.dto.gwstdentry.GwstdEntryHeadDto;
import com.dcjet.cs.dto.gwstdentry.GwstdEntryHeadParam;
import com.dcjet.cs.dto.returnEntry.commonSub.ReturnSubEntryDto;
import com.dcjet.cs.dto.returnEntry.commonSub.ReturnSubEntryHead;
import com.dcjet.cs.dto.returnEntry.commonSub.ReturnSubEntryParam;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryHeadMapper;
import com.dcjet.cs.gwstdentry.mapper.GwstdEntryHeadDtoMapper;
import com.dcjet.cs.gwstdentry.model.GwstdEntryHead;
import com.dcjet.cs.gwstdentry.model.GwstdEntryMessage;
import com.dcjet.cs.imp.service.KafkaEntryService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-3-10
 */
@Service
@Slf4j
public class GwstdEntryHeadService extends BaseService<GwstdEntryHead> {
    @Resource
    private GwstdEntryHeadMapper gwstdEntryHeadMapper;
    @Resource
    private GwstdEntryHeadDtoMapper gwstdEntryHeadDtoMapper;

    @Override
    public Mapper<GwstdEntryHead> getMapper() {
        return gwstdEntryHeadMapper;
    }

    @Resource
    private KafkaEntryService kafkaEntryService;

    /**
     * 获取分页信息
     *
     * @param gwstdEntryHeadParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwstdEntryHeadDto>> getListPaged(GwstdEntryHeadParam gwstdEntryHeadParam, PageParam pageParam, UserInfoToken userInfoToken) {
        // 启用分页查询
        GwstdEntryHead gwstdEntryHead = gwstdEntryHeadDtoMapper.toPo(gwstdEntryHeadParam);
        gwstdEntryHead.setTradeCode(userInfoToken.getCompany());
        Page<GwstdEntryHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdEntryHeadMapper.getList(gwstdEntryHead));
        List<GwstdEntryHeadDto> gwstdEntryHeadDtos = page.getResult().stream().map(head -> {
            GwstdEntryHeadDto dto = gwstdEntryHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdEntryHeadDto>> paged = ResultObject.createInstance(gwstdEntryHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 根据统一编号或报关单号或预录入单内部编号查询订阅报关单数据 -- For public api interface
     *
     * @param param
     * @return
     */
    public ResultObject<List<ReturnSubEntryDto>> querySubEntry(ReturnSubEntryParam param, PageParam pageParam) {
        pageParam.setLimit(200);//固定每页200行
        Page<ReturnSubEntryHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdEntryHeadMapper.querySubEntryData(param));
        List<ReturnSubEntryDto> dtoList = page.getResult().stream().map(s -> {
            //组装订阅报关单数据结构
            ReturnSubEntryDto dto = new ReturnSubEntryDto();
            dto.setEntryHead(s);
            if (CollectionUtils.isNotEmpty(s.getEntryList())) {
                s.getEntryList().forEach(ss -> ss.setEmsListNo(s.getEmsListNo()));
                dto.setEntryList(s.getEntryList());
            }
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<ReturnSubEntryDto>> paged = ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public void saveToKafka(GwstdEntryMessage message) {
        saveToKafka(message, null);
    }

    public void saveToKafka(GwstdEntryMessage message, ConsumerRecord<String, String> record) {
        String tradeCode = null;
        if (null == message) {
            log.info(" -----------KAFKA接口开始更新报关单下发信息，无任何信息,退出 -------------");
            return;
        }
        if (null != message.getHead()) {
            tradeCode = message.getHead().getTradeCode();
        } else {
            if (CollectionUtils.isEmpty(message.getWorkflowList()) && null == message.getLicense()) {
                log.info(" -----------KAFKA接口开始更新报关单下发信息，无表头\\WORKFLOW\\LICENSE信息,退出-------------");
                return;
            }
            message.setExistHead(false);
            String seqNo = null;
            String entryNo = null;
            if (CollectionUtils.isNotEmpty(message.getWorkflowList())) {
                seqNo = message.getWorkflowList().get(0).getSeqNo();
                entryNo = message.getWorkflowList().get(0).getEntryNo();
            } else if (null != message.getLicense()) {
                seqNo = message.getLicense().getSeqNo();
            }
            GwstdEntryHead gwstdEntryHead = new GwstdEntryHead();
            if (StringUtils.isNotBlank(seqNo)) {
                gwstdEntryHead.setSeqNo(seqNo);
                List<GwstdEntryHead> list = gwstdEntryHeadMapper.select(gwstdEntryHead);
                if (CollectionUtils.isNotEmpty(list)) {
                    tradeCode = list.get(0).getTradeCode();
                    message.setHead(list.get(0));
                }
            }
            if (StringUtils.isBlank(tradeCode) && StringUtils.isNotBlank(entryNo)) {
                gwstdEntryHead.setSeqNo(null);
                gwstdEntryHead.setEntryNo(entryNo);
                List<GwstdEntryHead> listEntryNo = gwstdEntryHeadMapper.select(gwstdEntryHead);
                if (CollectionUtils.isNotEmpty(listEntryNo)) {
                    tradeCode = listEntryNo.get(0).getTradeCode();
                    message.setHead(listEntryNo.get(0));
                }
            }
        }
        if (StringUtils.isBlank(tradeCode)) {
            log.info(" -----------KAFKA接口开始更新报关单下发信息，无表头tradeCode信息,退出 -------------");
            return;
        }
        message.setTradeCode(tradeCode);
        if (record != null) {
            kafkaEntryService.saveKafkaData(message, record);
        } else {
            kafkaEntryService.saveNoKafkaData(message, "DB");
        }
        log.info(" -----------获取报关单kafka下发信息结束-------------");
    }

    /**
     * 索雷博报关单反向生成
     */
    public void reverseGeneration() {
        Map<String, String> param = new HashMap<>();
        param.put("P_TRADE_CODE", "3107945181");
        param.put("P_RET_CODE", "");
        param.put("P_RET_STR", "");
        Map<String, String> errMap = gwstdEntryHeadMapper.entryIReverseGeneration(param);
        Object errorCode = errMap.get("p_ret_code");
        if (errorCode != null && !errorCode.toString().isEmpty()) {
            log.error("索雷博进口报关单反向生成错误:" + errMap.get("p_ret_str"));
        }
        errMap = gwstdEntryHeadMapper.entryEReverseGeneration(param);
        errorCode = errMap.get("p_ret_code");
        if (errorCode != null && !errorCode.toString().isEmpty()) {
            log.error("索雷博出口报关单反向生成错误:" + errMap.get("p_ret_str"));
        }
    }

    /**
     * 宿州泰盛报关单反向生成
     */
    public void reverseGenerationForSztaison() {
        Map<String, String> param = new HashMap<>();
        param.put("P_TRADE_CODE", "341196027M");
        param.put("P_RET_CODE", "");
        param.put("P_RET_STR", "");
        Map<String, String> errMap = gwstdEntryHeadMapper.entryIReverseGenerationForSztaison(param);
        Object errorCode = errMap.get("p_ret_code");
        if (errorCode != null && !errorCode.toString().isEmpty()) {
            log.error("盛泰宿州进口报关单反向生成错误:" + errMap.get("p_ret_str"));
        }
        errMap = gwstdEntryHeadMapper.entryEReverseGenerationForSztaison(param);
        errorCode = errMap.get("p_ret_code");
        if (errorCode != null && !errorCode.toString().isEmpty()) {
            log.error("盛泰宿州出口报关单反向生成错误:" + errMap.get("p_ret_str"));
        }
    }

    /**
     * 上海艺康报关单反向生成
     */
    public void reverseGenerationForEcolab() {
        Map<String, String> param = new HashMap<>();
        param.put("P_TRADE_CODE", "3226941430");
        param.put("P_RET_CODE", "");
        param.put("P_RET_STR", "");
        Map<String, String> errMap = gwstdEntryHeadMapper.entryIReverseGenerationForEcolab(param);
        Object errorCode = errMap.get("p_ret_code");
        if (errorCode != null && !errorCode.toString().isEmpty()) {
            log.error("上海艺康进口报关单反向生成错误:" + errMap.get("p_ret_str"));
        }

        errMap = gwstdEntryHeadMapper.entryEReverseGenerationForEcolab(param);
        errorCode = errMap.get("p_ret_code");
        if (errorCode != null && !errorCode.toString().isEmpty()) {
            log.error("上海艺康出口报关单反向生成错误:" + errMap.get("p_ret_str"));
        }
    }
}
