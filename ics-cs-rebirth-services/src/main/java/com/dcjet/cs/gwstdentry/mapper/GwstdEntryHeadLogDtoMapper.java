package com.dcjet.cs.gwstdentry.mapper;

import com.dcjet.cs.dto.gwstdentry.GwstdEntryHeadLogDto;
import com.dcjet.cs.dto.gwstdentry.GwstdEntryHeadLogParam;
import com.dcjet.cs.gwstdentry.model.GwstdEntryHead;
import com.dcjet.cs.gwstdentry.model.GwstdEntryHeadLog;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-3-10
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwstdEntryHeadLogDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwstdEntryHeadLogDto toDto(GwstdEntryHeadLog po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwstdEntryHeadLog toPo(GwstdEntryHeadLogParam param);
    /**
     * 数据库原始数据更新
     * @param gwstdEntryHeadLogParam
     * @param gwstdEntryHeadLog
     */
    void updatePo(GwstdEntryHeadLogParam gwstdEntryHeadLogParam, @MappingTarget GwstdEntryHeadLog gwstdEntryHeadLog);
    default void patchPo(GwstdEntryHeadLogParam gwstdEntryHeadLogParam, GwstdEntryHeadLog gwstdEntryHeadLog) {
        // TODO 自行实现局部更新
    }
    GwstdEntryHeadLog toCo(GwstdEntryHead po);

}
