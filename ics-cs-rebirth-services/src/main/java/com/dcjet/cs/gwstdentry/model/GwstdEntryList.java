package com.dcjet.cs.gwstdentry.model;

import com.dcjet.cs.entCompare.model.Compare;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2020-3-10
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_GWSTD_ENTRY_LIST")
public class GwstdEntryList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * OID
     */
	@Column(name = "SID")
	private String sid;
	/**
     * 检验检疫代码
     */
	@Column(name = "CIQ_CODE")
	@JsonProperty("CIQ_CODE")
	private String ciqCode;
	/**
     * 境内目的地（商检）
     */
	@Column(name = "CIQDEST_CODE")
	@JsonProperty("CIQDEST_CODE")
	private String ciqdestCode;
	/**
     * 检验检疫名称
     */
	@Column(name = "CIQ_NAME")
	@JsonProperty("CIQ_NAME")
	private String ciqName;
	/**
     * 商品编号
     */
	@Column(name = "CODE_TS")
	@JsonProperty("CODE_T_S")
	@Compare(column = "codeTS",columnName = "商品编号")
	private String codeTS;
	/**
     * 合同商品项序号
     */
	@Column(name = "CONTR_ITEM")
	@JsonProperty("CONTR_ITEM")
	private Integer contrItem;
	/**
     * 成交币制
     */
	@Column(name = "CURR")
	@JsonProperty("CURR")
	@Compare(column = "curr",columnName = "成交币制")
	private String curr;
	/**
     * 非危险化学品(危险货物信息)
     */
	@Column(name = "DANGER_FLAG")
	@JsonProperty("DANGER_FLAG")
	private String dangerFlag;
	/**
     * 危险货物名称(危险货物信息)
     */
	@Column(name = "DANG_NAME")
	@JsonProperty("DANG_NAME")
	private String dangName;
	/**
     * 数据来源（0：固定 1：电子口岸 2：爬网 3：组合）
     */
	@Column(name = "DATA_SOURCE")
	@JsonProperty("DATA_SOURCE")
	private String dataSource;
	/**
     * 申报单价
     */
	@Column(name = "DECL_PRICE")
	@JsonProperty("DEC_PRICE")
	@Compare(column = "decPrice",columnName = "申报单价")
	private BigDecimal decPrice;
	/**
     * 申报总价
     */
	@Column(name = "DECL_TOTAL")
	@JsonProperty("DEC_TOTAL")
	@Compare(column = "decTotal",columnName = "申报总价")
	private BigDecimal decTotal;
	/**
     * 最终目的国
     */
	@Column(name = "DESTINATION_COUNTRY")
	@JsonProperty("DESTINATION_COUNTRY")
	@Compare(column = "destinationCountry",columnName = "最终目的国")
	private String destinationCountry;
	/**
     * 目的地（产地）代码-行政区域
     */
	@Column(name = "DISTRICT_CODE")
	@JsonProperty("DISTRICT_CODE")
	@Compare(column = "districtCode",columnName = "境内目的地")
	private String districtCode;
	/**
     * 货物属性代码(检验检疫)
     */
	@Column(name = "GOOD_SATTR")
	@JsonProperty("GOOD_SATTR")
	private String goodSattr;
	/**
     * 货物品牌(检验检疫)
     */
	@Column(name = "GOODS_BRAND")
	@JsonProperty("GOODS_BRAND")
	private String goodsBrand;
	/**
     * 货物型号(检验检疫)
     */
	@Column(name = "GOODS_MODEL")
	@JsonProperty("GOODS_MODEL")
	private String goodsModel;
	/**
     * 货物规格(检验检疫)
     */
	@Column(name = "GOODS_SPEC")
	@JsonProperty("GOODS_SPEC")
	private String goodsSpec;
	/**
     * 商品名称
     */
	@Column(name = "G_NAME")
	@JsonProperty("G_NAME")
	@Compare(column = "GName",columnName = "商品名称")
	private String gname;
	/**
     * 商品规格、型号
     */
	@Column(name = "G_MODEL")
	@JsonProperty("G_MODEL")
	@Compare(column = "GModel",columnName = "商品规格型号")
	private String gmodel;
	/**
     * 商品序号
     */
	@Column(name = "G_NO")
	@JsonProperty("G_NO")
	private Integer gno;
	/**
     * 申报数量
     */
	@Column(name = "G_QTY")
	@JsonProperty("QTY")
	@Compare(column = "qty",columnName = "申报数量")
	private BigDecimal qty;
	/**
     * 申报计量单位
     */
	@Column(name = "G_UNIT")
	@JsonProperty("UNIT")
	@Compare(column = "unit",columnName = "申报计量单位")
	private String unit;
	/**
     * 生产日期(检验检疫)
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonProperty("PRODUCE_DATE")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "PRODUCE_DATE")
	private Date produceDate;
	/**
     * 生产日期(检验检疫)-开始
     */
	@Transient
	private String produceDateFrom;
	/**
     * 生产日期(检验检疫)-结束
     */
	@Transient
    private String produceDateTo;
	/**
     * 产销国
     */
	@Column(name = "ORIGIN_COUNTRY")
	@JsonProperty("ORIGIN_COUNTRY")
	@Compare(column = "originCountry",columnName = "原产国")
	private String originCountry;
	/**
     * 危包规格(危险货物信息)
     */
	@Column(name = "PACK_MODEL")
	@JsonProperty("PACK_MODEL")
	private String packModel;
	/**
     * 危包类别
     */
	@Column(name = "PACK_TYPE")
	@JsonProperty("PACK_TYPE")
	private String packType;
	/**
     * 成品版本号
     */
	/*@Column(name = "EXG_VERSION")
	private String exgVersion;*/
	/**
     * 产品有效期(检验检疫)
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonProperty("PROD_VALIDDT")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "PROD_VALIDDT")
	private Date prodValiddt;
	/**
     * 产品有效期(检验检疫)-开始
     */
	@Transient
	private String prodValiddtFrom;
	/**
     * 产品有效期(检验检疫)-结束
     */
	@Transient
    private String prodValiddtTo;
	/**
     * 第一（法定）数量
     */
	@Column(name = "QTY_1")
	@JsonProperty("QTY_1")
	@Compare(column = "qty1",columnName = "第一（法定）数量")
	private BigDecimal qty1;
	/**
     * 第一(法定)计量单位
     */
	@Column(name = "UNIT_1")
	@JsonProperty("UNIT_1")
	@Compare(column = "unit1",columnName = "第一(法定)计量单位")
	private String unit1;
	/**
     * 第二数量
     */
	@Column(name = "QTY_2")
	@JsonProperty("QTY_2")
	@Compare(column = "qty2",columnName = "法定第二数量")
	private BigDecimal qty2;
	/**
     * 第二计量单位
     */
	@Column(name = "UNIT_2")
	@JsonProperty("UNIT_2")
	@Compare(column = "unit2",columnName = "第二计量单位")
	private String unit2;
	/**
     * 成分/原料/组分(检验检疫)
     */
	@Column(name = "STUFF")
	@JsonProperty("STUFF")
	private String stuff;
	/**
     * 危险货物编码
     */
	@Column(name = "DANG_CODE")
	@JsonProperty("DANG_CODE")
	private String dangCode;
	/**
     * 用途
     */
	@Column(name = "USE_TO")
	@JsonProperty("USE_TO")
	private String useType;
	/**
     * 征减免税方式
     */
	@Column(name = "DUTY_MODE")
	@JsonProperty("DUTY_MODE")
	@Compare(column = "dutyMode",columnName = "征减免税方式")
	private String dutyMode;
	/**
     * 报关单中心统一编号（预录入号）
     */
	@Column(name = "SEQ_NO")
	@JsonProperty("SEQ_NO")
	private String seqNo;
	/**
     * 海关编号
     */
	@Column(name = "ENTRY_ID")
	@JsonProperty("ENTRY_ID")
	private String entryNo;
	/**
     * 归类标志 0自动归类1人工归类
     */
	@Column(name = "CLASS_MARK")
	@JsonProperty("CLASS_MARK")
	private String classMark;
	/**
     * 目的地（产地）代码-行政区域
     */
	@Column(name = "DISTRICT_POST_CODE")
	@JsonProperty("DISTRICT_POST_CODE")
	private String districtPostCode;
	/**
     * 插入时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "MY_INSERT_TIME")
	private Date myInsertTime;
	/**
     * 插入时间-开始
     */
	@Transient
	private String myInsertTimeFrom;
	/**
     * 插入时间-结束
     */
	@Transient
    private String myInsertTimeTo;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "MY_UPDATE_TIME")
	private Date myUpdateTime;
	/**
     * 更新时间-开始
     */
	@Transient
	private String myUpdateTimeFrom;
	/**
     * 更新时间-结束
     */
	@Transient
    private String myUpdateTimeTo;
	/**
     * 工缴费(美元)
     */
	@Column(name = "WORK_USD")
	@JsonProperty("WORK_USD")
	private BigDecimal workUsd;
	/**
     * 加工料件/成品货号
     */
	@Column(name = "EXG_NO")
	@JsonProperty("EXG_NO")
	private String exgNo;
	/**
     * 成交总价
     */
	@Column(name = "TRADE_TOTAL")
	@JsonProperty("TRADE_TOTAL")
	private BigDecimal tradeTotal;
	/**
     * 生产单位注册号
     */
	@Column(name = "MNUFCTRRE_GNO")
	@JsonProperty("MNUFCTRRE_GNO")
	private String mnufctrreGno;
	/**
     * 原产地区代码
     */
	@Column(name = "ORIG_PLACE_CODE")
	@JsonProperty("ORIG_PLACE_CODE")
	private String origPlaceCode;
	/**
     * UN编码
     */
	@Column(name = "UN_CODE")
	@JsonProperty("UN_CODE")
	private String unCode;
	/**
	 * 关务系统入库时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private Date insertTime;
}
