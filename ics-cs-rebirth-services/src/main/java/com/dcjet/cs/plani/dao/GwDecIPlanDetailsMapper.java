package com.dcjet.cs.plani.dao;

import com.dcjet.cs.plani.model.GwDecIPlanDetails;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * GwDecIPlanDetails
 *
 * <AUTHOR>
 * @date: 1/30/2024
 */
public interface GwDecIPlanDetailsMapper extends Mapper<GwDecIPlanDetails> {
    /**
     * 查询获取数据
     *
     * @param gwDecIPlanDetails
     * @return
     */
    List<GwDecIPlanDetails> getList(GwDecIPlanDetails gwDecIPlanDetails);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * @param param
     * @return
     */
    List<String> getGNameByFacGNo(Map<String, Object> param);

    /**
     * 数据提取
     *
     * @param
     * @return
     */
    int extractInsert(@Param("tempOwner") String tempOwner, @Param("tradeCode") String tradeCode, @Param("insertUser") String insertUser, @Param("insertTime") Date insertTime);
}
