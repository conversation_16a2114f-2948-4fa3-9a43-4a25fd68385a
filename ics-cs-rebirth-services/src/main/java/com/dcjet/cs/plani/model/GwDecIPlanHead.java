package com.dcjet.cs.plani.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-1-20
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "t_gw_dec_i_plan_head")
public class GwDecIPlanHead extends BasicModel implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 清关表编号，自动生成（编码规则：I+YYYYMM+3位流水号）
	 */
	@Column(name = "clear_no")
	private String clearNo;
	/**
	 * 提运单号
	 */
	@Column(name = "bill_no")
	private String billNo;
	/**
	 * 发票号
	 */
	@Column(name = "invoice_no")
	private String invoiceNo;
	/**
	 * 运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递
	 */
	@Column(name = "traf_mode")
	private String trafMode;
	/**
	 * 运输方式名称
	 */
	@Column(name = "traf_name")
	private String trafName;
	/**
	 * 柜型 A：20GP，B：40GP，C：40HQ，D：45GP，E：散货，F：空运，G：快递
	 */
	@Column(name = "container_type")
	private String containerType;
	/**
	 * 柜量
	 */
	@Column(name = "container_qty")
	private Integer containerQty;
	/**
	 * 启运港
	 */
	@Column(name = "desp_port")
	private String despPort;
	/**
	 * 目的港
	 */
	@Column(name = "dest_port")
	private String destPort;
	/**
	 * etd日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Column(name = "etd")
	private Date etd;
	/**
	 * etd日期-开始
	 */
	@Transient
	private String etdFrom;
	/**
	 * etd日期-结束
	 */
	@Transient
	private String etdTo;
	/**
	 * eta日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Column(name = "eta")
	private Date eta;
	/**
	 * eta日期-开始
	 */
	@Transient
	private String etaFrom;
	/**
	 * eta日期-结束
	 */
	@Transient
	private String etaTo;
	/**
	 * 状态，0：暂存，1：已确认，2：已制单
	 */
	@Column(name = "status")
	private String status;

	/**
	 * 随附单据（Y/N）
	 */
	@Transient
	private String hasAttached;
	/**
	 * 最晚送货日期-开始
	 */
	@Transient
	private String latestDeliveryDateFrom;
	/**
	 * 最晚送货日期-结束
	 */
	@Transient
	private String latestDeliveryDateTo;
	/**
	 * 实际到港日
	 */
	@Transient
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date arrivalPortDate;
	/**
	 * 免箱期
	 */
	@Transient
	private Integer freeBoxDays;
	/**
	 * 滞箱日期
	 */
	@Transient
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date retainBoxDate;
	/**
	 * 最晚送货日期
	 */
	@Transient
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date latestDeliveryDate;
//	/**
//	 * 委派货代
//	 */
//	@Transient
//	private String forwardCode;
	/**
	 * 委派货代
	 */
	@Transient
	private String forwardName;
	/**
	 * 预计送货日期
	 */
	@Transient
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date expectDeliveryDate;
	/**
	 * 实际送货时间
	 */
	@Transient
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date actualDeliveryDate;

//	=====================================
	/**
	 * 进口批次号
	 */
	@Column(name = "IN_BATCH_NO")
	private String inBatchNo;

	/**
	 * 包装种类
	 */
	@Column(name = "WRAP_TYPE")
	private String wrapType;

	/**
	 * 供应商代码
	 */
	@Column(name = "SUPPLIER_CODE")
	private String supplierCode;

	/**
	 * 供应商名称
	 */
	@Column(name = "SUPPLIER_NAME")
	private String supplierName;

	/**
	 * 毛重KG
	 */
	@Digits(integer = 8, fraction = 5, message = "毛重KG必须为数字,整数位最大8位,小数最大5位!")
	@Column(name = "GROSS_WT")
	private BigDecimal grossWt;

	/**
	 * 件数
	 */
	@Digits(integer = 8, fraction = 0, message = "件数必须为数字,整数位最大8位,小数最大0位!")
	@Column(name = "PACK_NUM")
	private BigDecimal packNum;

	/**
	 * 体积m³
	 */
	@Digits(integer = 8, fraction = 2, message = "体积m³必须为数字,整数位最大8位,小数最大2位!")
	@Column(name = "VOLUME")
	private BigDecimal volume;

	/**
	 * 主提运单
	 */
	@Column(name = "MAWB")
	private String mawb;

	/**
	 * 分提运单
	 */
	@Column(name = "HAWB")
	private String hawb;

	/**
	 * 计费重量
	 */
	@Digits(integer = 8, fraction = 5, message = "计费重量必须为数字,整数位最大8位,小数最大5位!")
	@Column(name = "C_WEIGHT")
	private BigDecimal cweight;

	/**
	 * 国际货代
	 */
	@Column(name = "FORWARD_CODE")
	private String forwardCode;

	/**
	 * 航班号/航名航次
	 */
	@Column(name = "VOYAGE_NO")
	private String voyageNo;

	/**
	 * 船公司/航空公司
	 */
	@Column(name = "CARRIER")
	private String carrier;

	/**
	 * 计划交期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "PLAN_DELIVERY_TIME")
	private Date planDeliveryTime;

	/**
	 * 计划交期-开始
	 */
	@Transient
	private String planDeliveryTimeFrom;
	/**
	 * 计划交期-结束
	 */
	@Transient
	private String planDeliveryTimeTo;

	/**
	 * 物料属性(0 普货、1 危险品、2 冷藏品)
	 */
	@Column(name = "GOODS_CATEGORY")
	private String goodsCategory;

	/**
	 * 贸易条款
	 */
	@Column(name = "TRADE_TERMS")
	private String tradeTerms;

	/**
	 * 贸易条款地址
	 */
	@Column(name = "TRADE_AREA")
	private String tradeArea;

	/**
	 * 采购人员
	 */
	@Column(name = "PURCHASE_USER")
	private String purchaseUser;

	/**
	 * 备注
	 */
	@Column(name = "NOTE")
	private String note;
}
