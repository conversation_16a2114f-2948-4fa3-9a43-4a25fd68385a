package com.dcjet.cs.receipt.dao;

import com.dcjet.cs.receipt.model.ReceiptTemplateFile;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* ReceiptTemplateFile
* <AUTHOR>
* @date: 2019-6-3
*/
public interface ReceiptTemplateFileMapper extends Mapper<ReceiptTemplateFile> {
    /**
     * 查询获取数据
     * @param receiptTemplateFile
     * @return
     */
    List<ReceiptTemplateFile> getList(ReceiptTemplateFile receiptTemplateFile);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 根据表体sid获取附件
     * @param listSid
     * @return
     */
    List<ReceiptTemplateFile> selectBylistsid(String listSid);

    /**
     * 根据表头sid删除发票模板
     * @param headIds
     * @return
     */
    int deleteByHeadIds(List<String> headIds);

    /**
     * 根据表头id获取文件列表
     * @param headId
     * @return
     */
    List<ReceiptTemplateFile> selectByHeadId(String headId);

    /**
     * 根据表体id获取下一个文件顺序号
     * @param listNo
     * @return
     */
    int getMaxSerialNo(String listNo);

    /**
     * 表体附件数查询
     * @param listId
     * @return
     */
    int fileCount(String listId);


}
