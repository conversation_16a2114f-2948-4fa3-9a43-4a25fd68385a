package com.dcjet.cs.receipt.service;

import com.dcjet.cs.dto.receipt.ReceiptTemplateFileDto;
import com.dcjet.cs.dto.receipt.ReceiptTemplateFileParam;
import com.dcjet.cs.receipt.dao.ReceiptTemplateFileMapper;
import com.dcjet.cs.receipt.mapper.ReceiptTemplateFileDtoMapper;
import com.dcjet.cs.receipt.model.ReceiptTemplateFile;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-6-3
 */
@Service
public class ReceiptTemplateFileService extends BaseService<ReceiptTemplateFile> {
    @Resource
    private ReceiptTemplateFileMapper receiptTemplateFileMapper;
    @Resource
    private ReceiptTemplateFileDtoMapper receiptTemplateFileDtoMapper;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Override
    public Mapper<ReceiptTemplateFile> getMapper() {
        return receiptTemplateFileMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param receiptTemplateFileParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<ReceiptTemplateFileDto>> getListPaged(ReceiptTemplateFileParam receiptTemplateFileParam, PageParam pageParam) {
        // 启用分页查询
        ReceiptTemplateFile receiptTemplateFile = receiptTemplateFileDtoMapper.toPo(receiptTemplateFileParam);
        Page<ReceiptTemplateFile> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> receiptTemplateFileMapper.getList(receiptTemplateFile));
        List<ReceiptTemplateFileDto> receiptTemplateFileDtos = page.getResult().stream().map(head -> {
            ReceiptTemplateFileDto dto = receiptTemplateFileDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<ReceiptTemplateFileDto>> paged = ResultObject.createInstance(receiptTemplateFileDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param receiptTemplateFileParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ReceiptTemplateFileDto insert(ReceiptTemplateFileParam receiptTemplateFileParam, UserInfoToken userInfo) {
        ReceiptTemplateFile receiptTemplateFile = receiptTemplateFileDtoMapper.toPo(receiptTemplateFileParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        receiptTemplateFile.setSid(sid);
        receiptTemplateFile.setInsertUser(userInfo.getUserNo());
        receiptTemplateFile.setInsertTime(new Date());
        // 新增数据
        int insertStatus = receiptTemplateFileMapper.insert(receiptTemplateFile);
        return  insertStatus > 0 ? receiptTemplateFileDtoMapper.toDto(receiptTemplateFile) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param receiptTemplateFileParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ReceiptTemplateFileDto update(ReceiptTemplateFileParam receiptTemplateFileParam, UserInfoToken userInfo) {
        ReceiptTemplateFile receiptTemplateFile = receiptTemplateFileMapper.selectByPrimaryKey(receiptTemplateFileParam.getSid());
        receiptTemplateFileDtoMapper.updatePo(receiptTemplateFileParam, receiptTemplateFile);
        // 更新数据
        int update = receiptTemplateFileMapper.updateByPrimaryKey(receiptTemplateFile);
        return update > 0 ? receiptTemplateFileDtoMapper.toDto(receiptTemplateFile) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
		receiptTemplateFileMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<ReceiptTemplateFileDto> selectAll(ReceiptTemplateFileParam exportParam, UserInfoToken userInfo) {
        ReceiptTemplateFile receiptTemplateFile = receiptTemplateFileDtoMapper.toPo(exportParam);
        // receiptTemplateFile.setTradeCode(userInfo.getCompany());
        List<ReceiptTemplateFileDto> receiptTemplateFileDtos = new ArrayList<>();
        List<ReceiptTemplateFile> receiptTemplateFiles = receiptTemplateFileMapper.getList(receiptTemplateFile);
        if (CollectionUtils.isNotEmpty(receiptTemplateFiles)) {
            receiptTemplateFileDtos = receiptTemplateFiles.stream().map(head -> {
                ReceiptTemplateFileDto dto = receiptTemplateFileDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return receiptTemplateFileDtos;
    }

    /**
     * 根据表体sid获取附件
     * @param listSid
     * @return
     */
    public  List<ReceiptTemplateFileDto> selectByListId(String listSid){
       return toDto(receiptTemplateFileMapper.selectBylistsid(listSid));
    }

    private List<ReceiptTemplateFileDto> toDto(List<ReceiptTemplateFile> list) {
        List<ReceiptTemplateFileDto> listDto = new ArrayList<>();
        for(ReceiptTemplateFile item : list) {
            listDto.add(receiptTemplateFileDtoMapper.toDto(item));
        }
        return listDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public ReceiptTemplateFileDto insert(ReceiptTemplateFile receiptTemplateFile, UserInfoToken userInfo){
        int insertStatus = receiptTemplateFileMapper.insertSelective(receiptTemplateFile);
        return insertStatus>0? receiptTemplateFileDtoMapper.toDto(receiptTemplateFile):null;
    }

    /**
     * 删除
     * @param sid
     */
    public void deleteWithFile(String sid){
        ReceiptTemplateFile receiptTemplateFile = receiptTemplateFileMapper.selectByPrimaryKey(sid);
        String filePath = receiptTemplateFile.getFdfsId();
        try {
            fileHandler.deleteFile(filePath);
        }catch(Exception err){}
        receiptTemplateFileMapper.deleteByPrimaryKey(sid);
    }

    /**
     * 根据headId删除数据和文件服务器文件
     * @param headIds
     */
    public void deleteByHeadIds(List<String> headIds){
        for(String headId : headIds){
            List<ReceiptTemplateFile> fileList = receiptTemplateFileMapper.selectByHeadId(headId);
            for(ReceiptTemplateFile item: fileList){
                deleteWithFile(item.getSid());
            }
        }
    }

    public int getMaxSerialNo(String listId){
       return receiptTemplateFileMapper.getMaxSerialNo(listId);
    }

    public Boolean isExistsFile(String listId) {
        Integer result = receiptTemplateFileMapper.fileCount(listId);
        return result > 0;
    }


}
