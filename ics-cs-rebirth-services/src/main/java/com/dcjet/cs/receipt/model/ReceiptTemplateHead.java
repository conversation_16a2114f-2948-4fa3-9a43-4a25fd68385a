package com.dcjet.cs.receipt.model;
import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-5-21
 */
@Setter
@Getter
@Table(name = "T_RECEIPT_TEMPLATE_HEAD")
public class ReceiptTemplateHead extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * SID
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 单证属性
     */
	@Column(name = "DOCUMENT_TYPE")
	private  String documentType;
	/**
     * 适用客户代码
     */
	@Column(name = "CUSTOMER_CODE")
	private  String customerCode;
	/**
     * 模板编号
     */
	@Column(name = "TEMPLATE_CODE")
	private  String templateCode;
	/**
     * 运输方式
     */
	@Column(name = "TRAF_MODE")
	private  String trafMode;
	/**
     * 联络人
     */
	@Column(name = "LINKMAN_NAME")
	private  String linkmanName;
	/**
     * 目的地
     */
	@Column(name = "DESTINATION")
	private  String destination;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 状态
     */
	@Column(name = "STATUS")
	private  String status;
	/**
     * 模板类型
     */
	@Column(name = "TEMPLATE_TYPE")
	private  String templateType;

	/**
	 * 新增该模板的企业编码
	 */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
	 * 进出口标记
	 */
	@Column(name = "I_E_MARK")
	private  String IEMark;
	/**
	 * 运输方式名称
	 */
	@Column(name = "TRAF_MODE_NAME")
	private  String trafModeName;
	/**
	 * 币制显示
	 */
	@Column(name = "CURR_NAME")
	private  String currName;
	/**
	 * 目的港
	 */
	@Column(name = "DEST_PORT")
	private  String destPort;
	/**
	 * 适用客户
	 */
	@Transient
	private  String customerName;
	/**
	 * 基础数据类型
	 */
	@Transient
	private  String cliType;
}
