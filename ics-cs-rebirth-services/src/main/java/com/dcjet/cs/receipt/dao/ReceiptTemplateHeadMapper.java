package com.dcjet.cs.receipt.dao;

import com.dcjet.cs.receipt.model.ReceiptTemplateHead;
import com.xdo.domain.KeyValuePair;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* ReceiptTemplateHead
* <AUTHOR>
* @date: 2019-5-21
*/
public interface ReceiptTemplateHeadMapper extends Mapper<ReceiptTemplateHead> {
    /**
     * 查询获取数据
     * @param receiptTemplateHead
     * @return
     */
    List<ReceiptTemplateHead> getList(ReceiptTemplateHead receiptTemplateHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 是否存在
     * @param param
     * @return
     */
    Integer isExists(Map<String, Object> param);

    /**
     * 功能描述:获取模板列表（以keyvalue展示）
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/6/12
     * @param:  receiptTemplateHeadParam 出入参数
     * @return:
     */
    List<KeyValuePair> getTemplateList(ReceiptTemplateHead receiptTemplateHead);
}
