package com.dcjet.cs.cert.service.observer.listener;

import com.dcjet.cs.cert.dao.CertificateDeductMapper;
import com.dcjet.cs.cert.dao.CertificateListMapper;
import com.dcjet.cs.cert.dao.CertificateMapper;
import com.dcjet.cs.cert.model.CertificateDeduct;
import com.dcjet.cs.cert.service.CertificateCatalogListService;
import com.dcjet.cs.cert.service.TimesAndQtyCtrl.deductQty.DeductQtyTemplate;
import com.dcjet.cs.cert.service.TimesAndQtyCtrl.selectCert.ISelectCert;
import com.dcjet.cs.cert.service.config.CertConstans;
import com.dcjet.cs.cert.service.impl.InvolveCertEService;
import com.dcjet.cs.cert.service.impl.InvolveCertIService;
import com.dcjet.cs.cert.service.observer.event.CertDeductClacEvent;
import com.dcjet.cs.common.component.DistributedLockComponent;
import com.dcjet.cs.dto.cert.simpleInfo.CatalogListSimpleInfo;
import com.dcjet.cs.dto.cert.simpleInfo.CertListSimpleInfo;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpEListNMapper;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIListNMapper;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpEListN;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.erp.model.DecErpIListN;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.dcjet.cs.util.variable.MatVariable;
import com.xdo.common.exception.ErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class CertDeductCalcListener implements SmartApplicationListener {
    @Resource
    private InvolveCertIService involveCertIService;
    @Resource
    private InvolveCertEService involveCertEService;
    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private DecErpIListNMapper decErpIListNMapper;
    @Resource
    private DecErpEListNMapper decErpEListNMapper;
    @Resource
    private CertificateCatalogListService catalogListService;
    @Resource
    private CertificateMapper certificateMapper;
    @Resource
    private CertificateListMapper certificateListMapper;
    @Resource
    private CertificateDeductMapper deductMapper;
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    @Resource
    private DistributedLockComponent distributedLockComponent;

    private Map<String, ISelectCert> mapForHandler = new HashMap<>();

    private Map<String, DeductQtyTemplate> mapForDeductQty = new HashMap<>();

    CertDeductCalcListener(List<ISelectCert> handlers, List<DeductQtyTemplate> deductQtyHandlers) {
        for(ISelectCert handler : handlers) {
            mapForHandler.put(handler.ctrlType(), handler);
        }
        for(DeductQtyTemplate handler : deductQtyHandlers) {
            mapForDeductQty.put(handler.qtyCtrlFlag(), handler);
        }
    }

    @Override
    public boolean supportsEventType(Class<? extends ApplicationEvent> eventType) {
        return eventType == CertDeductClacEvent.class;
    }

    @Override
    public boolean supportsSourceType(Class<?> sourceType) {
        return true;
    }

    @Override
    public int getOrder() {
        return 0;
    }

    /**
     * 证书提取：
     *      1. 提取涉证目录；
     *      2. 自动匹配
     * @param event
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onApplicationEvent(ApplicationEvent event) {
        //
        String param = event.getSource().toString();
        String decErpHeadId = param.substring(0, param.indexOf("|"));
        String ieMark = param.substring(param.indexOf("|")+1, param.lastIndexOf("|"));
        String userNo = param.substring(param.lastIndexOf("|")+1);
        //进程ID，用作redis分布式锁（避免自己的锁被其他进程解锁）
//        String threadId = Long.toString(Thread.currentThread().getId());
        String lockId = decErpHeadId + CertConstans.REDIS_KEY_SUFFIX_EXTRACT;
//        boolean lock = redisDistributedLock.lock(lockId, threadId, 5);
//        if (!lock) {
//            throw new ErrorException(429, "证书提取中...请稍后");
//        }


        try {
            if (CommonVariable.IE_MARK_I.equals(ieMark)) {

                //1. 提取涉证目录
                DecErpIHeadN decErpIHeadN = decErpIHeadNMapper.selectByPrimaryKey(decErpHeadId);

                boolean lock = distributedLockComponent.tryLock(lockId, 60, decErpIHeadN.getTradeCode(), userNo);
                if (!lock) {
                    throw new ErrorException(429, xdoi18n.XdoI18nUtil.t("服务器正在计算中，请稍后！"));
                }

                String bondMark = DecVariable.DEC_TYPE_1.equals(decErpIHeadN.getDecType()) ? decErpIHeadN.getBondMark() : null;

                // 1.1 提取facGNos， hsCodes
                List<DecErpIListN> list = decErpIListNMapper.getList(new DecErpIListN() {{ setHeadId(decErpHeadId); }});
                Set<String> facGNos = list.stream().map(e -> e.getFacGNo()).collect(Collectors.toSet());
                Set<String> hsCodes = list.stream().map(e -> e.getCodeTS()).collect(Collectors.toSet());

                // 1.2 根据facGNos、hsCodes获取对应的涉证目录
                Map<String, Set<CatalogListSimpleInfo>> facGNoInvolveCatalogs =
                        involveCertIService.involveCertCatalogByFacGNos(bondMark, list, facGNos, decErpIHeadN.getTradeCode(), Function.identity());
                Map<String, Set<CatalogListSimpleInfo>> hsCodeInvolveCatalogs =
                        involveCertIService.involveCertCatalogByHsCodes(bondMark, list, hsCodes, decErpIHeadN.getTradeCode(), Function.identity());

                List<CertificateDeduct> deductList = new ArrayList<>(16);
                for (DecErpIListN item : list) {
                    Date currDate = new Date();

                    // fCatalogInfos：当前提单表体 facGNo 匹配的涉证目录集合
                    Set<CatalogListSimpleInfo> fCatalogInfos =
                            facGNoInvolveCatalogs.get(catalogListService.getKey(item, CertConstans.CTRL_COLUMN_FAC_G_NO));

                    if (CollectionUtils.isNotEmpty(fCatalogInfos)) {
                        for (CatalogListSimpleInfo info : fCatalogInfos) {
                            CertificateDeduct deduct = new CertificateDeduct();
                            deduct.setSid(UUID.randomUUID().toString());
                            deduct.setValidMark(MatVariable.VALID_MARK_1);
                            deduct.setDecErpHeadId(item.getHeadId());
                            deduct.setDecErpListId(item.getSid());
                            deduct.setCatalogId(info.getHeadId());
                            deduct.setCatalogListId(info.getSid());
                            deduct.setTradeCode(decErpIHeadN.getTradeCode());
                            deduct.setInsertUser(userNo);
                            deduct.setInsertTime(currDate);
                            deduct.setOriginCountry(item.getOriginCountry());
                            deduct.setQty(item.getQty());
                            deductList.add(deduct);
                        }
                    }

                    // hCatalogInfos：当前提单表体 hsCode 匹配的涉证目录集合
                    Set<CatalogListSimpleInfo> hCatalogInfos = hsCodeInvolveCatalogs.get(catalogListService.getKey(item, CertConstans.CTRL_COLUMN_CODE_T_S));
                    if (CollectionUtils.isNotEmpty(hCatalogInfos)) {
                        for (CatalogListSimpleInfo info : hCatalogInfos) {
                            CertificateDeduct deduct = new CertificateDeduct();
                            deduct.setSid(UUID.randomUUID().toString());
                            deduct.setValidMark(MatVariable.VALID_MARK_1);
                            deduct.setDecErpHeadId(item.getHeadId());
                            deduct.setDecErpListId(item.getSid());
                            deduct.setCatalogId(info.getHeadId());
                            deduct.setCatalogListId(info.getSid());
                            deduct.setTradeCode(decErpIHeadN.getTradeCode());
                            deduct.setInsertUser(userNo);
                            deduct.setInsertTime(currDate);
                            deduct.setOriginCountry(item.getOriginCountry());
                            deduct.setQty(item.getQty());
                            deductList.add(deduct);
                        }
                    }
                }

                if (CollectionUtils.isEmpty(deductList)) {
                    return;
                }
                // 3.3 批量新增deduct
                bulkSqlOpt.batchOperate(deductList, deduct -> {
                    deductMapper.insert(deduct);
                    return 0;
                });

                //2. 自动匹配
//                autoMatch(deductList,decErpIHeadN.getTradeCode(),userNo);
            }
            else if (CommonVariable.IE_MARK_E.equals(ieMark)) {

                //1. 提取涉证目录
                DecErpEHeadN decErpEHeadN = decErpEHeadNMapper.selectByPrimaryKey(decErpHeadId);

                boolean lock = distributedLockComponent.tryLock(lockId, 60, decErpEHeadN.getTradeCode(), userNo);
                if (!lock) {
                    throw new ErrorException(429, xdoi18n.XdoI18nUtil.t("服务器正在计算中，请稍后！"));
                }

                String bondMark = DecVariable.DEC_TYPE_1.equals(decErpEHeadN.getDecType()) ? decErpEHeadN.getBondMark() : null;

                // 1.1 提取facGNos， hsCodes
                List<DecErpEListN> list = decErpEListNMapper.getList(new DecErpEListN() {{ setHeadId(decErpHeadId); }});
                Set<String> facGNos = list.stream().map(e -> e.getFacGNo()).collect(Collectors.toSet());
                Set<String> hsCodes = list.stream().map(e -> e.getCodeTS()).collect(Collectors.toSet());

                // 1.2 根据facGNos、hsCodes获取对应的涉证目录
                Map<String, Set<CatalogListSimpleInfo>> facGNoInvolveCatalogs =
                        involveCertEService.involveCertCatalogByFacGNos(bondMark, list, facGNos, decErpEHeadN.getTradeCode(), Function.identity());
                Map<String, Set<CatalogListSimpleInfo>> hsCodeInvolveCatalogs =
                        involveCertEService.involveCertCatalogByHsCodes(bondMark, list, hsCodes, decErpEHeadN.getTradeCode(), Function.identity());

                List<CertificateDeduct> deductList = new ArrayList<>(16);
                for (DecErpEListN item : list) {
                    Date currDate = new Date();
                    Set<CatalogListSimpleInfo> fCatalogInfos = facGNoInvolveCatalogs.get(catalogListService.getKey(item, CertConstans.CTRL_COLUMN_FAC_G_NO));
                    if (CollectionUtils.isNotEmpty(fCatalogInfos)) {
                        for (CatalogListSimpleInfo info : fCatalogInfos) {
                            CertificateDeduct deduct = new CertificateDeduct();
                            deduct.setSid(UUID.randomUUID().toString());
                            deduct.setValidMark(MatVariable.VALID_MARK_1);
                            deduct.setDecErpHeadId(item.getHeadId());
                            deduct.setDecErpListId(item.getSid());
                            deduct.setCatalogId(info.getHeadId());
                            deduct.setCatalogListId(info.getSid());
                            deduct.setTradeCode(decErpEHeadN.getTradeCode());
                            deduct.setInsertUser(userNo);
                            deduct.setInsertTime(currDate);
                            deduct.setOriginCountry(item.getOriginCountry());
                            deduct.setQty(item.getQty());
                            deductList.add(deduct);
                        }
                    }

                    Set<CatalogListSimpleInfo> hCatalogInfos = hsCodeInvolveCatalogs.get(catalogListService.getKey(item, CertConstans.CTRL_COLUMN_CODE_T_S));
                    if (CollectionUtils.isNotEmpty(hCatalogInfos)) {
                        for (CatalogListSimpleInfo info : hCatalogInfos) {
                            CertificateDeduct deduct = new CertificateDeduct();
                            deduct.setSid(UUID.randomUUID().toString());
                            deduct.setValidMark(MatVariable.VALID_MARK_1);
                            deduct.setDecErpHeadId(item.getHeadId());
                            deduct.setDecErpListId(item.getSid());
                            deduct.setCatalogId(info.getHeadId());
                            deduct.setCatalogListId(info.getSid());
                            deduct.setTradeCode(decErpEHeadN.getTradeCode());
                            deduct.setInsertUser(userNo);
                            deduct.setInsertTime(currDate);
                            deduct.setOriginCountry(item.getOriginCountry());
                            deduct.setQty(item.getQty());
                            deductList.add(deduct);
                        }
                    }
                }
                if (CollectionUtils.isEmpty(deductList)) {
                    return;
                }
                // 3.3 批量新增deduct
                bulkSqlOpt.batchOperate(deductList, deduct -> {
                    deductMapper.insert(deduct);
                    return 0;
                });

                //2. 自动匹配
//                autoMatch(deductList,decErpEHeadN.getTradeCode(),userNo);
            }
            else {
                throw new RuntimeException(xdoi18n.XdoI18nUtil.t("ieMark参数错误"));
            }
        }
        finally {
            //证书提取不可重复锁
            distributedLockComponent.unlock(lockId);
        }
    }

    /**
     * 自动匹配
     * 1. 提取涉及的证书台账；
     * 2. 自动匹配证书台账；
     * 3. 完成证书的扣量
     * @param deductList
     * 
     */
    public void autoMatch(List<CertificateDeduct> deductList,String company,String userNo) {

        // 1. 提取涉及的证件台账
        //  1.1 加锁
        Set<String> catalogIds = deductList.stream().map(e -> e.getCatalogId()).collect(Collectors.toSet());
        for (String lockKey : catalogIds) {
//            Boolean lock = redisDistributedLock.lock(lockKey + CertConstans.REDIS_KEY_SUFFIX_DEDUCT, threadId, 120);
//            if (!lock) {
//                throw new ErrorException(429, "计算中...请稍后再试");
//            }
            boolean lock = distributedLockComponent.tryLock(lockKey + CertConstans.REDIS_KEY_SUFFIX_DEDUCT, 60, company, userNo);
            if (!lock) {
                throw new ErrorException(429, xdoi18n.XdoI18nUtil.t("服务器正在计算中，请稍后！"));
            }

        }

        try {

            Set<String> catalogListIds = deductList.stream().map(e -> e.getCatalogListId()).collect(Collectors.toSet());
            // 1.2 提取台账 (Map from catalogListId to list of certListSimpleInfo)
            List<CertListSimpleInfo> certDatas = certificateMapper.getCertIdsByCatalogId(new ArrayList<>(catalogListIds));
            LinkedHashMap<String, List<CertListSimpleInfo>> certDataMap = certDatas.stream().collect(Collectors.groupingBy(e -> e.getCatalogListId(), LinkedHashMap::new, Collectors.toList()));

            //2. 自动匹配证书台账（内存匹配）
            for (CertificateDeduct deduct : deductList) {
                if (certDataMap.containsKey(deduct.getCatalogListId())) {
                    List<CertListSimpleInfo> theCerts = certDataMap.get(deduct.getCatalogListId());
                    for (CertListSimpleInfo cert : theCerts) {
                        String timeAndQtyCtrl = CommonEnum.certCtrlType.getCtrlType(cert.getTimesCtrlFlag(), cert.getQtyCtrlFlag());
                        deduct.setCtrlType(timeAndQtyCtrl);
//                        if (mapForHandler.get(timeAndQtyCtrl).matchCert(deduct, cert)) {
//                            break;
//                        }
                        if(mapForDeductQty.get(cert.getQtyCtrlFlag()).deductQty(deduct, cert)) {
                            break;
                        }
                    }
                }
                if (StringUtils.isEmpty(deduct.getCertificateId())) {
                    deduct.setCtrlType(CertConstans.CTRL_TYPE_NONE);
                }
            }

            //3. 完成证书的扣量
            // 3.1 批量更新证书台账表头
            List<CertificateDeduct> certsForBatchUpdate = new ArrayList<>(16);
            deductList.stream().filter(e -> StringUtils.isNotEmpty(e.getCertificateId())).collect(Collectors.groupingBy(e -> e.getCertificateId())).forEach((k, v) -> {
                List<CertificateDeduct> filterDeducts = v.stream()
                        .filter(e -> Arrays.asList(CertConstans.CTRL_TYPE_ONLY_HEAD_QTY, CertConstans.CTRL_TYPE_TIMES_AND_HEAD_QTY).contains(e.getCtrlType()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterDeducts)) {
                    certsForBatchUpdate.add(new CertificateDeduct() {{
                        setCertificateId(k);
                        setQty(new BigDecimal(filterDeducts.stream().mapToDouble(e -> e.getQty().doubleValue()).sum()));
                    }});
                }
            });
            if (CollectionUtils.isNotEmpty(certsForBatchUpdate)) {
                certificateMapper.updateBatch(certsForBatchUpdate);
            }
            // 3.2 批量更新证书台账表体
            List<CertificateDeduct> certListForBatchUpdate = new ArrayList<>(16);
            deductList.stream().filter(e -> StringUtils.isNotEmpty(e.getCertificateListId())).collect(Collectors.groupingBy(e -> e.getCertificateListId())).forEach((k, v) -> {
                List<CertificateDeduct> filterDeducts = v.stream()
                        .filter(e -> Arrays.asList(CertConstans.CTRL_TYPE_ONLY_LIST_QTY, CertConstans.CTRL_TYPE_TIMES_AND_LIST_QTY).contains(e.getCtrlType()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterDeducts)) {
                    certListForBatchUpdate.add(new CertificateDeduct() {{
                        setCertificateListId(k);
                        setQty(new BigDecimal(filterDeducts.stream().mapToDouble(e -> e.getQty().doubleValue()).sum()));
                    }});
                }
            });
            if (CollectionUtils.isNotEmpty(certListForBatchUpdate)) {
                certificateListMapper.updateBatch(certListForBatchUpdate);
            }
            // 3.3 批量新增deduct
            bulkSqlOpt.batchOperate(deductList, deduct -> {
                deductMapper.insert(deduct);
                return 0;
            });
        } finally {
            //解锁
            for (String lockKey : catalogIds) {
                distributedLockComponent.unlock(lockKey + CertConstans.REDIS_KEY_SUFFIX_DEDUCT);
            }
        }
    }

}
