<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.cert.dao.CertificateDeductMapper">
    <resultMap id="resultForList" type="com.dcjet.cs.cert.model.CertificateDeduct">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_LIST_ID" property="certificateListId" jdbcType="VARCHAR"/>
        <result column="DEC_ERP_LIST_ID" property="decErpListId" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="NUMERIC"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="DATE"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_ID" property="certificateId" jdbcType="VARCHAR"/>
        <result column="DEC_ERP_HEAD_ID" property="decErpHeadId" jdbcType="VARCHAR"/>
        <result column="VALID_MARK" property="validMark" jdbcType="VARCHAR"/>
        <result column="CTRL_TYPE" property="ctrlType" jdbcType="VARCHAR"/>
        <result column="CATALOG_ID" property="catalogId" jdbcType="VARCHAR"/>
        <result column="CATALOG_LIST_ID" property="catalogListId" jdbcType="VARCHAR"/>
        <result column="BILL_HEAD_ID" property="billHeadId" jdbcType="VARCHAR"/>
        <association property="certificate" column="CERTIFICATE_ID" select="com.dcjet.cs.cert.dao.CertificateMapper.getCertById" fetchType="lazy"/>
        <association property="certificateList" column="CERTIFICATE_LIST_ID" select="com.dcjet.cs.cert.dao.CertificateListMapper.getCertListById" fetchType="lazy"/>
    </resultMap>

    <sql id="columns">
        t.SID,
		t.CERTIFICATE_LIST_ID,
		t.DEC_ERP_LIST_ID,
		t.INSERT_TIME,
		t.INSERT_USER,
		t.INSERT_USER_NAME,
		t.QTY,
		t.TRADE_CODE,
		t.UPDATE_TIME,
		t.UPDATE_USER,
		t.UPDATE_USER_NAME,
		t.CERTIFICATE_ID,
		t.DEC_ERP_HEAD_ID,
		t.VALID_MARK,
		t.CTRL_TYPE,
		t.CATALOG_ID,
		t.CATALOG_LIST_ID
    </sql>

    <sql id="condition">
        <if test="sid != null and sid != ''">
            and t.SID = #{sid,jdbcType=VARCHAR}
        </if>
		<if test="certificateListId != null and certificateListId != ''">
            and t.CERTIFICATE_LIST_ID = #{certificateListId,jdbcType=VARCHAR}
        </if>
		<if test="decErpListId != null and decErpListId != ''">
            and t.DEC_ERP_LIST_ID = #{decErpListId,jdbcType=VARCHAR}
        </if>
		<if test="insertTime != null and insertTime != ''">
            and t.INSERT_TIME = #{insertTime,jdbcType=DATE}
        </if>
		<if test="insertUser != null and insertUser != ''">
            and t.INSERT_USER = #{insertUser,jdbcType=VARCHAR}
        </if>
		<if test="insertUserName != null and insertUserName != ''">
            and t.INSERT_USER_NAME = #{insertUserName,jdbcType=VARCHAR}
        </if>
		<if test="qty != null and qty != ''">
            and t.QTY = #{qty,jdbcType=NUMERIC}
        </if>
		<if test="tradeCode != null and tradeCode != ''">
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        </if>
		<if test="updateTime != null and updateTime != ''">
            and t.UPDATE_TIME = #{updateTime,jdbcType=DATE}
        </if>
		<if test="updateUser != null and updateUser != ''">
            and t.UPDATE_USER = #{updateUser,jdbcType=VARCHAR}
        </if>
		<if test="updateUserName != null and updateUserName != ''">
            and t.UPDATE_USER_NAME = #{updateUserName,jdbcType=VARCHAR}
        </if>
        <if test="certificateId != null and certificateId != ''">
            and t.CERTIFICATE_ID = #{certificateId,jdbcType=VARCHAR}
        </if>
        <if test="decErpHeadId != null and decErpHeadId != ''">
            and t.DEC_ERP_HEAD_ID = #{decErpHeadId,jdbcType=VARCHAR}
        </if>
        <if test="validMark != null and validMark != ''">
            and t.VALID_MARK = #{validMark,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="getList" parameterType="com.dcjet.cs.cert.model.CertificateDeduct" resultMap="resultForList">
        SELECT 
        <include refid="columns"/>
        ,t.BILL_HEAD_ID
        FROM T_CERTIFICATE_DEDUCT t
        <where>
            <include refid="condition"></include>
        </where>
    </select>

    <select id="getDeductExtInfos" resultType="com.dcjet.cs.cert.model.CertificateDeduct">
        SELECT
        <include refid="columns"/>
        ,t.BILL_HEAD_ID
        ,c.CTRL_COLUMN
        ,cl.FAC_G_NO
        ,cl.CODE_T_S
        FROM T_CERTIFICATE_DEDUCT t
            left join T_CERTIFICATE_CATALOG c on c.SID = t.CATALOG_ID
            left join T_CERTIFICATE_CATALOG_LIST cl on cl.SID = t.CATALOG_LIST_ID
        WHERE t.DEC_ERP_HEAD_ID = #{decErpHeadId}
          AND t.VALID_MARK != '0'
    </select>

    <select id="getDeductsLeftJoinBillHead" resultType="com.dcjet.cs.cert.model.CertificateDeduct">
       SELECT
        <include refid="columns"/>
        ,COALESCE(er.BILL_SID, ir.BILL_SID) as BILL_HEAD_ID
        FROM T_CERTIFICATE_DEDUCT t
            left join T_DEC_ERP_E_RELATION er on er.LIST_SID = t.DEC_ERP_LIST_ID
            left join T_DEC_ERP_I_RELATION ir on ir.LIST_SID = t.DEC_ERP_LIST_ID
        WHERE t.DEC_ERP_HEAD_ID = #{decErpHeadId}
          AND t.CTRL_TYPE in ('1','4','5')
          AND t.VALID_MARK = '1'
    </select>

    <update id="updateBatch" parameterType="list">
        update t_certificate
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="declare_times = COALESCE(declare_times,0) + (case" suffix="end),">
                <foreach collection="list" item="i" index="index">
                    <if test="i.declareTimes != null">
                        when sid=#{i.sid} then #{i.declareTimes}
                    </if>
                </foreach>
            </trim>
        </trim>
        where sid in
        <foreach collection="list" index="index" item="i" separator="," open="(" close=")">
            #{i.sid}
        </foreach>
    </update>

    <select id="getInvolveCerts" resultType="com.dcjet.cs.cert.model.CertificateDeduct">
        select  t.SID,
		        t.CERTIFICATE_LIST_ID,
		        t.DEC_ERP_LIST_ID,
		         t.CTRL_TYPE,
		        t.TRADE_CODE,
		        t.CERTIFICATE_ID,

		        COALESCE(il.QTY, el.QTY) as QTY,
		        COALESCE(il.FAC_G_NO, el.FAC_G_NO) as FAC_G_NO,
		        COALESCE(il.G_NAME, el.G_NAME) as G_NAME,
		        COALESCE(il.CODE_T_S, el.CODE_T_S) as CODE_T_S,
		        cc.DOCUMENT_CODE,
		        cc.DOCUMENT_NAME,
		        c.DOCUMENT_NO,
		        t.catalog_id,
		        t.catalog_list_id
        from t_certificate_deduct t
            left join t_certificate c on c.sid = t.certificate_id
            left join t_certificate_catalog cc on cc.sid = t.catalog_id
            left join t_dec_erp_i_list_n il on il.sid = t.dec_erp_list_id
            left join t_dec_erp_e_list_n el on el.sid = t.dec_erp_list_id
        WHERE t.DEC_ERP_HEAD_ID = #{decErpHeadId}
          AND t.VALID_MARK != '0'
        order by t.VALID_MARK, c.DOCUMENT_NO
    </select>

    <select id="getQtyUseDetail" resultType="com.dcjet.cs.dto.cert.useDtl.CertQtyUseDetailCert">
        select
            m.ems_list_no, m.insert_time, m.fac_g_no, m.cop_g_no, m.g_mark, m.ems_no, m.g_name, m.code_t_s,
            sum(m.dec_total)/sum(m.qty) as dec_price, sum(m.dec_total) as dec_total, sum(m.qty) as qty,
            m.list_total_qty, m.list_remain_qty
        from
        (
            select
                COALESCE (ih.ems_list_no, eh.ems_list_no) as ems_list_no,
                COALESCE (ih.insert_time, eh.insert_time) as insert_time,
                COALESCE (il.fac_g_no, el.fac_g_no) as fac_g_no,
                COALESCE (il.cop_g_no, el.cop_g_no) as cop_g_no,
                COALESCE (il.g_mark, el.g_mark) as g_mark,
                COALESCE (il.ems_no, el.ems_no) as ems_no,
                COALESCE (il.g_name, el.g_name) as g_name,
                COALESCE (il.code_t_s, el.code_t_s) as code_t_s,
                COALESCE (il.dec_price, el.dec_price) as dec_price,
                COALESCE (il.dec_total, el.dec_total) as dec_total,
                COALESCE (il.curr, el.curr) as curr,
                t.qty,
                certList.total_qty as list_total_qty,
                (certList.total_qty - certList.qty) as list_remain_qty
            from t_certificate_deduct t
                left join t_dec_erp_i_head_n ih
                    join t_dec_erp_i_list_n il on il.head_id = ih.sid
                on ih.sid = t.dec_erp_head_id and il.sid = t.dec_erp_list_id
                left join t_dec_erp_e_head_n eh
                    join t_dec_erp_e_list_n el on el.head_id = eh.sid
                on eh.sid = t.dec_erp_head_id and el.sid = t.dec_erp_list_id
                join t_certificate cert on cert.sid = t.certificate_id
                join t_certificate_list certList on certList.sid = t.certificate_list_id
            where t.certificate_id = #{certificateId}
              and t.valid_mark != '0'
        ) m
        <where>
            <if test="emsListNo != null and emsListNo != ''">
                and m.ems_list_No like '%'|| #{emsListNo} || '%'
            </if>
            <if test="facGNo != null and facGNo != ''">
                and m.fac_g_no like '%'|| #{facGNo} || '%'
            </if>
            <if test="gname != null and gname != ''">
                and m.g_name like '%'|| #{gname} || '%'
            </if>
            <if test="codeTS != null and codeTS != ''">
                and m.code_t_s like '%'|| #{codeTS} || '%'
            </if>
        </where>
        group by m.ems_list_no, m.insert_time, m.fac_g_no,
            m.cop_g_no, m.g_mark, m.ems_no, m.g_name, m.code_t_s,
            m.list_total_qty, m.list_remain_qty
        order by m.insert_time desc, m.ems_list_no desc
    </select>

    <select id="getTimesUseDetail" resultType="com.dcjet.cs.dto.cert.useDtl.CertTimesUseDetail">
        select * from
        (select
            COALESCE (ib.ems_list_no, eb.ems_list_no) as ems_list_no_bill,
           COALESCE (ih.ems_list_no, eh.ems_list_no) as ems_list_no,
           COALESCE (ih.insert_time, eh.insert_time) as insert_time,
           COALESCE (ib.list_no, eb.list_no) as list_no,
           COALESCE (ib.entry_no, eb.entry_no) as entry_no,
           COALESCE (ib.entry_declare_date, eb.entry_declare_date) as entry_declare_date
        from t_certificate_deduct t
            left join t_dec_i_bill_head ib
                join t_dec_erp_i_head_n ih on ih.sid = ib.head_id
            on ib.sid = t.bill_head_id
            left join t_dec_e_bill_head eb
                join t_dec_erp_e_head_n eh on eh.sid = eb.head_id
            on eb.sid = t.bill_head_id
        where t.certificate_id = #{certificateId}
          and t.valid_mark = '2'
        ) m
        <where>
            <if test="emsListNo != null and emsListNo != ''">
                and m.ems_list_No like '%'|| #{emsListNo} || '%'
            </if>
            <if test="emsListNoBill != null and emsListNoBill != ''">
                and m.ems_list_No_bill like '%'|| #{emsListNoBill} || '%'
            </if>
            <if test="insertTimeFrom != null">
                <![CDATA[ and m.insert_time >= #{insertTimeFrom,jdbcType=DATE} ]]>
            </if>
            <if test="insertTimeTo != null">
                <![CDATA[ and m.insert_time < #{insertTimeTo,jdbcType=DATE} + 1 ]]>
            </if>
        </where>
        group by m.ems_list_no_bill, m.ems_list_no, m.insert_time, m.list_no, m.entry_no, m.entry_declare_date
        order by m.insert_time desc, m.ems_list_no_bill desc
    </select>
</mapper>