package com.dcjet.cs.cert.service.observer.event;

import org.springframework.context.ApplicationEvent;

public class CertDeductCleanEvent extends ApplicationEvent {
    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source the object on which the event initially occurred or with
     *               which the event is associated (never {@code null})
     */
    public CertDeductCleanEvent(Object source) {
        super(source);
    }
}
