package com.dcjet.cs.cert.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 涉证扣量
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2020-04-29
 */
@Setter @Getter
@Table(name = "T_CERTIFICATE_DEDUCT")
public class CertificateDeduct extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 证件台账表头ID
     */
    @Column(name = "CERTIFICATE_ID")
    private String certificateId;
    /**
     * 证件台账表体ID
     */
    @Column(name = "CERTIFICATE_LIST_ID")
    private String certificateListId;
    
    /**
     * 提单表体ID
     */
    @Column(name = "DEC_ERP_LIST_ID")
    private String decErpListId;

    /**
     * 提单表头ID
     */
    @Column(name = "DEC_ERP_HEAD_ID")
    private String decErpHeadId;

    @Column(name = "CATALOG_ID")
    private String catalogId;

    @Column(name = "CATALOG_LIST_ID")
    private String catalogListId;

    @Column(name = "BILL_HEAD_ID")
    private String billHeadId;
    
    /**
     * 本次扣量
     */
    @Column(name = "QTY")
    private BigDecimal qty;

    /**
     * 有效标记
     */
    @Column(name = "VALID_MARK")
    private String validMark;

    /**
     * 控制类型
     */
    @Column(name = "CTRL_TYPE")
    private String ctrlType;



    @Transient
    private Certificate certificate;

    @Transient
    private CertificateList certificateList;

    @Transient
    private String originCountry;


    @Transient
    private String facGNo;

    @Transient
    private String gName;

    @Transient
    private String codeTS;

    @Transient
    private String ctrlColumn;

    @Transient
    private String documentCode;

    @Transient
    private String documentName;

    @Transient
    private String documentNo;


    
}