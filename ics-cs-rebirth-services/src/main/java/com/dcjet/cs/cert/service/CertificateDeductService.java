package com.dcjet.cs.cert.service;

import com.dcjet.cs.base.service.BasicService;
import com.dcjet.cs.base.service.ImportService;
import com.dcjet.cs.cert.model.CertificateDeduct;
import com.dcjet.cs.dto.cert.*;
import com.dcjet.cs.dto.cert.useDtl.CertQtyUseDetailCert;
import com.dcjet.cs.dto.cert.useDtl.CertTimesUseDetail;
import com.dcjet.cs.dto.cert.useDtl.CertUseRecord;
import com.dcjet.cs.dto.cert.useDtl.CertUserDetailSearchParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;

import java.util.List;

public interface CertificateDeductService
        extends BasicService<CertificateDeduct, CertificateDeductDto, CertificateDeductParam, CertificateDeductSearchParam>, ImportService<CertificateListParam> {

    ResultObject<List<CertificateDeductDto>> getListPaged(String decErpHeadId, PageParam pageParam, UserInfoToken userInfo);

    Boolean deductTimes(String decErpHeadId, UserInfoToken token);

    Boolean cleanDeductTimes(String decErpHeadId, UserInfoToken token);

    ResultObject<List<CertUseRecord>> getCertificateUseDetails(CertificateSearchParam param, PageParam pageParam, UserInfoToken token);

    ResultObject<List<CertQtyUseDetailCert>> getQtyUseDetail(CertUserDetailSearchParam param, PageParam pageParam);

    ResultObject<List<CertTimesUseDetail>> getTimesUseDetail(CertUserDetailSearchParam param, PageParam pageParam);

    List<String> checkCertRemainTimes(String decErpHeadId, UserInfoToken token);

    List<CertificateListDto> getSpecifiedCertList(String cataLogListId, String certListId);

}
