
package com.dcjet.cs.cert.service;

import com.dcjet.cs.base.service.BasicService;
import com.dcjet.cs.base.service.ImportService;
import com.dcjet.cs.cert.model.CertificateCatalogList;
import com.dcjet.cs.dto.cert.CertificateCatalogListDto;
import com.dcjet.cs.dto.cert.CertificateCatalogListParam;
import com.dcjet.cs.dto.cert.CertificateCatalogListSearchParam;
import com.dcjet.cs.dto.cert.simpleInfo.CatalogListSimpleInfo;
import com.dcjet.cs.erp.model.DecErpEListN;
import com.dcjet.cs.erp.model.DecErpIListN;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;

import java.util.List;


/***
 * 物料涉证明细 service 接口
 * 
 * generated by <PERSON>rate 神码
 * <AUTHOR>
 * @date: 2020-04-29
 */
public interface CertificateCatalogListService 
        extends BasicService<CertificateCatalogList, CertificateCatalogListDto, CertificateCatalogListParam, CertificateCatalogListSearchParam>, ImportService<CertificateCatalogListParam> {

    List<CatalogListSimpleInfo> getSimpleInfoByFacGNo(List<String> facGNos, String ieMark, String bondMark, String tradeCode);

    List<CatalogListSimpleInfo> getSimpleInfoByHSCode(List<String> codeTS, String ieMark, String bondMark, String tradeCode);

    String getKey(CertificateCatalogListParam list, String ctrlColumn);

    String getKey(CatalogListSimpleInfo list, String ctrlColumn);

    String getKey(DecErpIListN list, String ctrlColumn);

    String getKey(DecErpEListN list, String ctrlColumn);

    List<CertificateCatalogList> getCatalogList(String catalogId, UserInfoToken token);

    ResultObject<List<CertificateCatalogList>> getListAll(CertificateCatalogListSearchParam parm, PageParam pageParam, UserInfoToken token);
}