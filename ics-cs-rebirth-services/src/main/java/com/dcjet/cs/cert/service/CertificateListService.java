
package com.dcjet.cs.cert.service;

import com.dcjet.cs.base.service.BasicService;
import com.dcjet.cs.base.service.ImportService;
import com.dcjet.cs.cert.model.CertificateList;
import com.dcjet.cs.dto.cert.CertificateListDto;
import com.dcjet.cs.dto.cert.CertificateListParam;
import com.dcjet.cs.dto.cert.CertificateListSearchParam;
import com.dcjet.cs.dto.cert.CertificateWarningSearchParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;

import java.util.List;


/***
 * 证件台账表体 service 接口
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2020-04-29
 */
public interface CertificateListService 
        extends BasicService<CertificateList, CertificateListDto, CertificateListParam, CertificateListSearchParam>, ImportService<CertificateListParam> {


    ResultObject<List<CertificateListDto>> getWarning(CertificateWarningSearchParam param, PageParam pageParam, UserInfoToken token);

    List<CertificateListDto> getSpecifiedCertList(String cataLogListId, String certListId);
}