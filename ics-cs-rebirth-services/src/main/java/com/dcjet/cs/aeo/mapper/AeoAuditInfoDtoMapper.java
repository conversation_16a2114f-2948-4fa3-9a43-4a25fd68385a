package com.dcjet.cs.aeo.mapper;

import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.model.AuditUser;
import com.dcjet.cs.dto.aeo.AeoAuditInfoDto;
import com.dcjet.cs.dto.aeo.AeoAuditInfoParam;
import com.dcjet.cs.dto.aeo.AuditUserDto;
import com.dcjet.cs.dto.aeo.AuditUserParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AeoAuditInfoDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    AeoAuditInfoDto toDto(AeoAuditInfo po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    AeoAuditInfo toPo(AeoAuditInfoParam param);
    /**
     * 数据库原始数据更新
     * @param aeoAuditInfoParam
     * @param aeoAuditInfo
     */
    void updatePo(AeoAuditInfoParam aeoAuditInfoParam, @MappingTarget AeoAuditInfo aeoAuditInfo);
    default void patchPo(AeoAuditInfoParam aeoAuditInfoParam, AeoAuditInfo aeoAuditInfo) {
        // TODO 自行实现局部更新
    }

    AuditUser toUserPo(AuditUserParam param);
    AuditUserDto toUserDto(AuditUser po);
}
