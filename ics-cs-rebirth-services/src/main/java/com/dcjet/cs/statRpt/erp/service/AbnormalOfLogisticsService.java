package com.dcjet.cs.statRpt.erp.service;

import com.dcjet.cs.dto.statRpt.erp.dto.AbnormalOfLogistics;
import com.dcjet.cs.dto.statRpt.erp.param.AbnormalOfLogisticsParam;
import com.dcjet.cs.statRpt.erp.dao.AbnormalOfLogisticsMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AbnormalOfLogisticsService {

    @Resource
    private AbnormalOfLogisticsMapper mapper;

    /**
     * 进口分页查询
     * @param param
     * @param pageParam
     * @param token
     * @return
     */
    public ResultObject getListIPaged(AbnormalOfLogisticsParam param, PageParam pageParam, UserInfoToken token) {
        param.setTradeCode(token.getCompany());
        Page<AbnormalOfLogistics> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getListI(param));
        List<AbnormalOfLogistics> abnormalOfLogistics = page.getResult().stream().collect(Collectors.toList());
        return ResultObject.createInstance(abnormalOfLogistics, (int) page.getTotal(), page.getPageNum());
    }


    /**
     * 出口分页查询
     * @param param
     * @param pageParam
     * @param token
     * @return
     */
    public ResultObject getListEPaged(AbnormalOfLogisticsParam param, PageParam pageParam, UserInfoToken token) {
        param.setTradeCode(token.getCompany());
        Page<AbnormalOfLogistics> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getListE(param));
        List<AbnormalOfLogistics> abnormalOfLogistics = page.getResult().stream().collect(Collectors.toList());
        return ResultObject.createInstance(abnormalOfLogistics, (int) page.getTotal(), page.getPageNum());
    }


    public List<AbnormalOfLogistics> getListI(AbnormalOfLogisticsParam param, UserInfoToken token) {
        param.setTradeCode(token.getCompany());
        return mapper.getListI(param);
    }

    public List<AbnormalOfLogistics> getListE(AbnormalOfLogisticsParam param, UserInfoToken token) {
        param.setTradeCode(token.getCompany());
        return mapper.getListE(param);
    }

}
