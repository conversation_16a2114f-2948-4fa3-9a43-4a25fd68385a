<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.statRpt.erp.dao.PercentOfAgentMapper">
    <resultMap id="AgentPercentResult" type="com.dcjet.cs.dto.statRpt.erp.dto.PercentOfAgent">
        <result column="agent_code" property="agentCode" jdbcType="VARCHAR"></result>
        <result column="agent_name" property="agentName" jdbcType="VARCHAR"></result>
        <result column="bgd_cnt" property="bgdCnt" jdbcType="NUMERIC"></result>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"></result>
        <result column="dec_total" property="decTotal" jdbcType="NUMERIC"></result>
    </resultMap>

    <select id="getList" resultMap="AgentPercentResult">
        with core as (
            select
                AGENT_CODE,
                AGENT_NAME,
                curr,
                COUNT ( DISTINCT sid ) bgd_cnt,
                SUM (DECL_TOTAL ) dec_total
            from
            (
                select
                    bgdh.AGENT_CODE,
                    bgdh.AGENT_NAME,
                    bgdl.curr,
                    bgdh.sid,
                    bgdl.DECL_TOTAL
                    from T_GWSTD_ENTRY_HEAD bgdh
                    join t_gwstd_entry_list bgdl on bgdl.SEQ_NO = bgdh.SEQ_NO
                    left join t_dec_e_entry_head entryh on bgdh.entry_id = entryh.entry_no
                    left join t_dec_erp_e_relation r on r.entry_sid = entryh.sid
                    left JOIN t_dec_erp_e_head_n erph ON r.head_id = erph.sid
                <where>
                    and bgdh.IS_DECLARE = '1'
                    AND bgdh.I_E_MARK = 'E'
                    <include refid="condition"></include>
                    <include refid="conditionInsertTime"></include>
                </where>

                union ALL

                select
                    bgdh.AGENT_CODE,
                    bgdh.AGENT_NAME,
                    bgdl.curr,
                    bgdh.sid,
                    bgdl.DECL_TOTAL
                    from T_GWSTD_ENTRY_HEAD bgdh
                    join t_gwstd_entry_list bgdl on bgdl.SEQ_NO = bgdh.SEQ_NO
                    left join t_dec_i_entry_head entryh on bgdh.entry_id = entryh.entry_no
                    left join t_dec_erp_i_relation r on r.entry_sid = entryh.sid
                    left JOIN t_dec_erp_i_head_n erph ON r.head_id = erph.sid
                <where>
                    and bgdh.IS_DECLARE = '1'
                    AND bgdh.I_E_MARK = 'I'
                    <include refid="condition"></include>
                    <include refid="conditionInsertTime"></include>
                </where>
            ) A
            GROUP BY
            AGENT_CODE,
            AGENT_NAME,
            curr
        )
        select
            t.AGENT_CODE,
            t.AGENT_NAME,
            sum(t.bgd_cnt) bgd_cnt,
            sum(t.dec_total*rate.CURR_PRICE_USD) dec_total
        from core t
            left join T_EXCHANGE_RATE rate on rate.CODE = t.CURR and rate.CURR_DATE = #{currDate}
        group by t.AGENT_CODE, t.AGENT_NAME
        order by t.AGENT_CODE
    </select>

    <sql id="condition">
        and bgdh.TRADE_CODE in
        <foreach collection="tradeCode.split(',')" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="iemark!=null and iemark!=''">
            and bgdh.I_E_MARK = #{iemark}
        </if>
        <if test="declareDateFrom != null and declareDateFrom != ''">
            <![CDATA[and bgdh.D_DATE >= to_date(#{declareDateFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="declareDateTo != null and declareDateTo != ''">
            <![CDATA[and bgdh.D_DATE < to_date(#{declareDateTo}, 'yyyy-MM-dd') + 1]]>
        </if>
        <if test="agentCode != null and agentCode != ''">
            and (bgdh.AGENT_CODE = #{agentCode} or bgdh.AGENT_NAME like concat('%', concat(#{agentCode}, '%')))
        </if>
    </sql>

    <sql id="conditionInsertTime">
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[and erph.INSERT_TIME >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[and erph.INSERT_TIME < to_date(#{insertTimeTo}, 'yyyy-MM-dd') + 1]]>
        </if>
    </sql>

    <select id="getGroupList" resultMap="AgentPercentResult">
        with core as (
        select bgdh.AGENT_CODE,
        bgdh.AGENT_NAME,
        bgdl.curr,
        count(distinct bgdh.sid) bgd_cnt, sum(bgdl.DECL_TOTAL) dec_total
        ,bgdh.trade_code
        from T_GWSTD_ENTRY_HEAD bgdh
        join t_gwstd_entry_list bgdl on bgdl.SEQ_NO = bgdh.SEQ_NO
        <where>
            and bgdh.IS_DECLARE = '1'
            <include refid="condition"></include>
        </where>
        group by bgdh.trade_code,bgdh.AGENT_CODE, bgdh.AGENT_NAME, bgdl.curr
        )
        select
        t.AGENT_CODE,
        t.AGENT_NAME,
        sum(t.bgd_cnt) bgd_cnt,
        sum(t.dec_total*rate.CURR_PRICE_USD) dec_total
        ,t.trade_code
        from core t
        left join T_EXCHANGE_RATE rate on rate.CODE = t.CURR and rate.CURR_DATE = #{currDate}
        group by t.trade_code,t.AGENT_CODE, t.AGENT_NAME
        order by t.trade_code,t.AGENT_CODE
    </select>

    <sql id="groupCondition">
        and h.TRADE_CODE is not null
        and h.TRADE_CODE in
        <foreach collection="tradeCode.split(',')" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[and h.insert_time >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[and h.insert_time < to_date(#{insertTimeTo}, 'yyyy-MM-dd') + 1]]>
        </if>
        <if test="tradeMode != null and tradeMode != ''">
            and h.TRADE_MODE = #{tradeMode}
        </if>
    </sql>

    <select id="getJkCnt" resultMap="AgentPercentResult">
        select h.TRADE_CODE , count(1) jkCnt from t_dec_erp_i_head_n h
        <where>
            <include refid="groupCondition"></include>
        </where>
        group by h.trade_code
        order by h.trade_code
    </select>

    <select id="getCkCnt" resultMap="AgentPercentResult">
        select h.TRADE_CODE , count(1) ckCnt from t_dec_erp_e_head_n h
        <where>
            <include refid="groupCondition"></include>
        </where>
        group by h.trade_code
        order by h.trade_code
    </select>

    <select id="getJkPrice" resultMap="AgentPercentResult">
        select h.TRADE_CODE , sum(DEC_TOTAL) jkPrice from t_dec_erp_i_list_n h
        <where>
            <include refid="groupCondition"></include>
        </where>
        group by h.trade_code
        order by h.trade_code
    </select>

    <select id="getCkPrice" resultMap="AgentPercentResult">
        select h.TRADE_CODE , sum(DEC_TOTAL) ckPrice from t_dec_erp_e_list_n h
        <where>
            <include refid="groupCondition"></include>
        </where>
        group by h.trade_code
        order by h.trade_code
    </select>
</mapper>
