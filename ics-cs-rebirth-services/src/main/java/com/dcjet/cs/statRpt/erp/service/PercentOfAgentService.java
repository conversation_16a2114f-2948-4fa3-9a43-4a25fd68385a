package com.dcjet.cs.statRpt.erp.service;

import com.dcjet.cs.company.model.GwCompany;
import com.dcjet.cs.dto.statRpt.erp.PercentOfTotalDto;
import com.dcjet.cs.dto.statRpt.erp.dto.PercentOfAgent;
import com.dcjet.cs.dto.statRpt.erp.param.PercentOfAgentParam;
import com.dcjet.cs.statRpt.erp.dao.CommonPercentMapper;
import com.dcjet.cs.statRpt.erp.dao.PercentOfAgentMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PercentOfAgentService {

    @Resource
    private CommonPercentMapper commonMapper;

    @Resource
    private PercentOfAgentMapper mapper;

    /**
     * 分页查询
     * @param param
     * @param pageParam
     * @param token
     * @return
     */
    public ResultObject getListPaged(PercentOfAgentParam param, PageParam pageParam, UserInfoToken token) {
        param.setTradeCode(token.getCompany());
        Page<PercentOfAgent> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(param));
        List<PercentOfTotalDto> dataAll = commonMapper.getEntryAllForZdDate(param);

        NumberFormat nf = NumberFormat.getPercentInstance();
        nf.setMinimumFractionDigits(2);
        List<PercentOfAgent> percentOfAgents = page.getResult().stream().map(el -> {
//            el.setPercentOfbgdCnt(nf.format((float)el.getBgdCnt()/dataAll.get(0).getBgdCntAll()));
//            el.setPercentOfdecTotal(nf.format(el.getDecTotal().divide(dataAll.get(0).getDecTotalAll(), 4, BigDecimal.ROUND_HALF_UP).doubleValue()));
            if(el.getBgdCnt() > 0) {
                el.setPercentOfbgdCnt(nf.format((float) el.getBgdCnt() / dataAll.get(0).getBgdCntAll()));
            }
            if(el.getDecTotal() != null) {
                el.setPercentOfdecTotal(nf.format(el.getDecTotal().divide(dataAll.get(0).getDecTotalAll(), 4, BigDecimal.ROUND_HALF_UP).doubleValue()));
            }
            return el;
        }).collect(Collectors.toList());
        return ResultObject.createInstance(percentOfAgents, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * getList
     * @param param
     * @param token
     * @return
     */
    public List<PercentOfAgent> getList(PercentOfAgentParam param, UserInfoToken token) {
        param.setTradeCode(token.getCompany());
        List<PercentOfAgent> result = mapper.getList(param);

        List<PercentOfTotalDto> dataAll = commonMapper.getEntryAllForZdDate(param);
        NumberFormat nf = NumberFormat.getPercentInstance();
        nf.setMinimumFractionDigits(2);
        result.stream().forEach(el -> {
//            el.setPercentOfbgdCnt(nf.format((float)el.getBgdCnt()/dataAll.get(0).getBgdCntAll()));
//            el.setPercentOfdecTotal(nf.format(el.getDecTotal().divide(dataAll.get(0).getDecTotalAll(), 4, BigDecimal.ROUND_HALF_UP).doubleValue()));
            if(el.getBgdCnt() > 0) {
                el.setPercentOfbgdCnt(nf.format((float) el.getBgdCnt() / dataAll.get(0).getBgdCntAll()));
            }
            if(el.getDecTotal() != null) {
                el.setPercentOfdecTotal(nf.format(el.getDecTotal().divide(dataAll.get(0).getDecTotalAll(), 4, BigDecimal.ROUND_HALF_UP).doubleValue()));
            }
        });
        return result;
    }

    /**
     *
     * @param param
     * @return
     */
    public ResultObject getGroupListPaged(List<GwCompany> companyList, PercentOfAgentParam param) {

        List<PercentOfAgent> rtnList = new ArrayList<>();

        // 进口票数
        Map<String, PercentOfAgent> jkCntMap =  mapper.getJkCnt(param);

        // 出口票数
        Map<String, PercentOfAgent> ckCntMap =  mapper.getCkCnt(param);

        // 进口金额
        Map<String, PercentOfAgent> jkPriceMap =  mapper.getJkPrice(param);

        // 出口金额
        Map<String, PercentOfAgent> ckPriceMap =  mapper.getCkPrice(param);


        if(CollectionUtils.isNotEmpty(companyList)){

            for(GwCompany company:companyList){
                PercentOfAgent pa = new PercentOfAgent();
                pa.setTradeCode(company.getCorpCode() + "  " + company.getCorpName());

                if(jkCntMap.containsKey(company.getCorpCode())){
                    pa.setJkCnt(jkCntMap.get(company.getCorpCode()).getJkCnt());
                }
                if(ckCntMap.containsKey(company.getCorpCode())) {
                    pa.setCkCnt(ckCntMap.get(company.getCorpCode()).getCkCnt());
                }
                if(jkPriceMap.containsKey(company.getCorpCode())) {
                    pa.setJkPrice(jkPriceMap.get(company.getCorpCode()).getJkPrice());
                }
                if(ckPriceMap.containsKey(company.getCorpCode())) {
                    pa.setCkPrice(ckPriceMap.get(company.getCorpCode()).getCkPrice());
                }
                if(StringUtils.isNotBlank(pa.getJkCnt()) || StringUtils.isNotBlank(pa.getCkCnt()) || pa.getJkPrice() != null || pa.getCkPrice() != null ){
                    rtnList.add(pa);
                }
            }
        }

        return ResultObject.createInstance(rtnList, rtnList.size());
    }

    /**
     * getList
     * @param param
     * @param token
     * @return
     */
    public List<PercentOfAgent> getGroupList(List<GwCompany> companyList,PercentOfAgentParam param, UserInfoToken token) {
        List<PercentOfAgent> rtnList = new ArrayList<>();

        // 进口票数
        Map<String, PercentOfAgent> jkCntMap =  mapper.getJkCnt(param);

        // 出口票数
        Map<String, PercentOfAgent> ckCntMap =  mapper.getCkCnt(param);

        // 进口金额
        Map<String, PercentOfAgent> jkPriceMap =  mapper.getJkPrice(param);

        // 出口金额
        Map<String, PercentOfAgent> ckPriceMap =  mapper.getCkPrice(param);


        if(CollectionUtils.isNotEmpty(companyList)){
            for(GwCompany company:companyList){
                PercentOfAgent pa = new PercentOfAgent();
                pa.setTradeCode(company.getCorpCode() + "  " + company.getCorpName());

                if(jkCntMap.containsKey(company.getCorpCode())){
                    pa.setJkCnt(jkCntMap.get(company.getCorpCode()).getJkCnt());
                }
                if(ckCntMap.containsKey(company.getCorpCode())) {
                    pa.setCkCnt(ckCntMap.get(company.getCorpCode()).getCkCnt());
                }
                if(jkPriceMap.containsKey(company.getCorpCode())) {
                    pa.setJkPrice(jkPriceMap.get(company.getCorpCode()).getJkPrice());
                }
                if(ckPriceMap.containsKey(company.getCorpCode())) {
                    pa.setCkPrice(ckPriceMap.get(company.getCorpCode()).getCkPrice());
                }
                if(StringUtils.isNotBlank(pa.getJkCnt()) || StringUtils.isNotBlank(pa.getCkCnt()) || pa.getJkPrice() != null || pa.getCkPrice() != null ){
                    rtnList.add(pa);
                }
            }
        }
        return rtnList;
    }

}
