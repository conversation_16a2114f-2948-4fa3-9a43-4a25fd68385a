<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.statRpt.erp.dao.PromiseItemStatMapper">
    <resultMap id="PromiseItemResult" type="com.dcjet.cs.dto.statRpt.erp.dto.PromiseItemStat">
        <result column="ems_list_no" property="emsListNo" jdbcType="VARCHAR"></result>
        <result column="insert_time" property="insertTime" jdbcType="VARCHAR"></result>
        <result column="entry_id" property="entryId" jdbcType="VARCHAR"></result>
        <result column="d_date" property="ddate" jdbcType="VARCHAR"></result>
        <result column="ems_no" property="emsNo" jdbcType="VARCHAR"></result>
        <result column="list_no" property="listNo" jdbcType="VARCHAR"></result>
        <result column="trade_mode" property="tradeMode" jdbcType="VARCHAR"></result>
        <result column="traf_mode" property="trafMode" jdbcType="VARCHAR"></result>
        <result column="i_e_port" property="ieport" jdbcType="VARCHAR"></result>
        <result column="overseas_trade_code" property="overseasTradeCode" jdbcType="VARCHAR"></result>
        <result column="bill_no" property="billNo" jdbcType="VARCHAR"></result>
        <result column="seq_no" property="seqNo" jdbcType="VARCHAR"></result>
    </resultMap>

    <resultMap id="PromiseItemDetailResult" type="com.dcjet.cs.dto.statRpt.erp.dto.PromiseItemStat">

        <result column="seq_no" property="seqNo" jdbcType="VARCHAR"></result>
        <result column="dec_total" property="decTotal" jdbcType="NUMERIC"></result>
        <result column="curr" property="curr" jdbcType="VARCHAR"></result>
        <result column="g_name" property="gname" jdbcType="VARCHAR"></result>
    </resultMap>

    <select id="getListI" resultMap="PromiseItemResult">
        select
               itd.EMS_LIST_NO,
               itd.INSERT_TIME,
               dyh.ENTRY_ID,
               dyh.D_DATE,
               dyh.MANUAL_NO ems_no,
               iqd.LIST_NO,
               dyh.TRADE_MODE,
               dyh.TRAF_MODE,
               dyh.I_E_PORT,
               dyh.OVERSEAS_TRADE_CODE || '|' || dyh.OVERSEAS_TRADE_NAME as OVERSEAS_TRADE_CODE,
               dyh.BILL_NO,
               itd.BOND_MARK,
               dyh.CONFIRM_SPECIAL,
               dyh.CONFIRM_PRICE,
               dyh.CONFIRM_ROYALTIES,
               dyh.CUS_REMARK_AUTO,
               dyh.SEQ_NO
        from t_gwstd_entry_head dyh
            left join T_DEC_I_ENTRY_HEAD ibgd on ibgd.SEQ_NO = dyh.SEQ_NO
            left join T_DEC_I_BILL_HEAD iqd on iqd.sid = ibgd.BILL_HEAD_ID
            left join T_DEC_ERP_I_HEAD_N itd on itd.SID = ibgd.ERP_HEAD_ID
        where dyh.I_E_MARK = 'I' and dyh.IS_DECLARE = '1'
            <include refid="condition"></include>
        ORDER by iqd.INSERT_TIME desc nulls last
    </select>

    <select id="getListE" resultMap="PromiseItemResult">
        select
               itd.EMS_LIST_NO,
               itd.INSERT_TIME,
               dyh.ENTRY_ID,
               dyh.D_DATE,
               dyh.MANUAL_NO ems_no,
               iqd.LIST_NO,
               dyh.TRADE_MODE,
               dyh.TRAF_MODE,
               dyh.I_E_PORT,
               dyh.OVERSEAS_TRADE_CODE || '|' || dyh.OVERSEAS_TRADE_NAME as OVERSEAS_TRADE_CODE,
               dyh.BILL_NO,
               itd.BOND_MARK,
               dyh.CONFIRM_SPECIAL,
               dyh.CONFIRM_PRICE,
               dyh.CONFIRM_ROYALTIES,
               dyh.CUS_REMARK_AUTO,
               dyh.SEQ_NO
        from t_gwstd_entry_head dyh
            left join T_DEC_E_ENTRY_HEAD ibgd on ibgd.SEQ_NO = dyh.SEQ_NO
            left join T_DEC_E_BILL_HEAD iqd on iqd.sid = ibgd.BILL_HEAD_ID
            left join T_DEC_ERP_E_HEAD_N itd on itd.SID = ibgd.ERP_HEAD_ID
        where dyh.I_E_MARK = 'E' and dyh.IS_DECLARE = '1'
            <include refid="condition"></include>
        ORDER by iqd.INSERT_TIME desc nulls last
    </select>

    <select id="getDetail" resultMap="PromiseItemDetailResult">
        select
            dyl.SEQ_NO,
            sum(dyl.DECL_TOTAL) dec_total,
            max(dyl.CURR) curr,
            max(dyl.G_NAME) g_name
        from T_GWSTD_ENTRY_LIST dyl
            join T_GWSTD_ENTRY_HEAD dyh on dyh.SEQ_NO = dyl.SEQ_NO
        where dyh.TRADE_CODE = #{tradeCode} and dyh.IS_DECLARE = '1'
            and dyl.SEQ_NO in
            <foreach collection="seqNos" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        group by dyl.SEQ_NO
    </select>


    <sql id="condition">
        and dyh.TRADE_CODE = #{tradeCode}
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[and itd.INSERT_TIME >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[and itd.INSERT_TIME < to_date(#{insertTimeTo}, 'yyyy-MM-dd') + 1]]>
        </if>
        and (dyh.CONFIRM_SPECIAL = 'Y' or dyh.CONFIRM_PRICE = 'Y' or dyh.CONFIRM_ROYALTIES = 'Y' or dyh.CUS_REMARK_AUTO = 'Y')
        <if test='confirmSpecial != null and confirmSpecial != ""'>
            <if test='confirmSpecial == "0"'>
                and dyh.CONFIRM_SPECIAL = 'Y'
            </if>
            <if test='confirmSpecial == "1"'>
                and dyh.CONFIRM_SPECIAL = 'N'
            </if>
            <if test='confirmSpecial == "2"'>
                and (dyh.CONFIRM_SPECIAL is null or dyh.CONFIRM_SPECIAL = '')
            </if>
        </if>
        <if test='confirmPrice != null and confirmPrice != ""'>
            <if test='confirmPrice == "0"'>
                and dyh.CONFIRM_PRICE = 'Y'
            </if>
            <if test='confirmPrice == "1"'>
                and dyh.CONFIRM_PRICE = 'N'
            </if>
            <if test='confirmPrice == "2"'>
                and (dyh.CONFIRM_PRICE is null or dyh.CONFIRM_PRICE = '')
            </if>
        </if>
        <if test='confirmRoyalties != null and confirmRoyalties != ""'>
            <if test='confirmRoyalties == "0"'>
                and dyh.CONFIRM_ROYALTIES = 'Y'
            </if>
            <if test='confirmRoyalties == "1"'>
                and dyh.CONFIRM_ROYALTIES = 'N'
            </if>
            <if test='confirmRoyalties == "2"'>
                and (dyh.CONFIRM_ROYALTIES is null or dyh.CONFIRM_ROYALTIES = '')
            </if>
        </if>
        <if test='cusRemarkAuto != null and cusRemarkAuto != ""'>
            <if test='cusRemarkAuto == "0"'>
                and dyh.CUS_REMARK_AUTO = '0'
            </if>
            <if test='cusRemarkAuto == "1"'>
                and dyh.CUS_REMARK_AUTO = '1'
            </if>
            <if test='cusRemarkAuto == "2"'>
                and (dyh.CUS_REMARK_AUTO is null or dyh.CUS_REMARK_AUTO = '')
            </if>
        </if>
        <if test='bondMark != null and bondMark != "" and bondMark != "N"'>
            <if test='bondMark == "0"'>
                and dyh.MANUAL_NO is not null and dyh.MANUAL_NO != ''
            </if>
            <if test='bondMark == "1"'>
                and dyh.MANUAL_NO is null or dyh.MANUAL_NO = ''
            </if>
        </if>
    </sql>

</mapper>
