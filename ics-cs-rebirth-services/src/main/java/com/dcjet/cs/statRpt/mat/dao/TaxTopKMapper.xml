<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.statRpt.mat.dao.TaxTopKMapper">
    <resultMap id="hsCodeNumberResult" type="com.dcjet.cs.dto.statRpt.mat.dto.TaxTopK">
        <result column="g_name" property="gname" jdbcType="VARCHAR"></result>
        <result column="CODE_TS" property="codeTS" jdbcType="VARCHAR"></result>
        <result column="tax_all" property="taxAll" jdbcType="NUMERIC"></result>
        <result column="tariff" property="tariff" jdbcType="NUMERIC"></result>
        <result column="the_vat" property="theVAT" jdbcType="NUMERIC"></result>
        <result column="curr" property="curr" jdbcType="VARCHAR"></result>
    </resultMap>

    <sql id="columns">
        t.g_name
        ,t.code_ts
        ,t.tax_all
        ,t.tariff
        ,t.the_vat
        ,'RMB|人民币' as curr
    </sql>

    <select id="getList" resultMap="hsCodeNumberResult">
        select
        <include refid="columns"></include>
        from (
        <include refid="querySql"></include>
        ) t
        <if test='_databaseId == "postgresql" '>
            limit 10
        </if>
        <if test='_databaseId != "postgresql" '>
            <![CDATA[where rownum <= 10]]>
        </if>
    </select>

    <sql id="querySql">
        select
            dl.G_NAME, dl.CODE_TS, sum(dl.DUTY_VALUE) TAX_ALL,
            sum(case when dh.TAX_TYPE = 'A' then dl.DUTY_VALUE else 0 end) tariff,
            sum(case when dh.TAX_TYPE = 'L' then dl.DUTY_VALUE else 0 end) the_vat
        from T_GWSTD_DUTYFORM_LIST dl
            join T_GWSTD_DUTYFORM_HEAD dh on dh.TAX_VOU_NO = dl.TAX_VOU_NO
        <where>
            <include refid="condition"></include>
        </where>
        group by dl.G_NAME, dl.CODE_TS
        ORDER BY SUM(dl.DUTY_VALUE) desc nulls last
    </sql>

    <sql id="condition">
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[and dh.MY_INSERT_TIME >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[and dh.MY_INSERT_TIME < to_date(#{insertTimeTo}, 'yyyy-MM-dd') + 1]]>
        </if>
        <if test='tradeMode != null and tradeMode != ""'>
            and dh.TRADE_MODE in
            <foreach collection="tradeMode.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>
