package com.dcjet.cs.statRpt.mat.service;

import com.dcjet.cs.company.model.GwCompany;
import com.dcjet.cs.dto.statRpt.mat.dto.DecTotalTopK;
import com.dcjet.cs.dto.statRpt.mat.param.TopKParam;
import com.dcjet.cs.statRpt.mat.dao.DecTotalTopKMapper;
import com.xdo.common.token.UserInfoToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DecTotalTopKService {

    @Resource
    private DecTotalTopKMapper mapper;


    /**
     * 进口总货值topK
     * @param param
     * @param token
     * @return
     */
    public List<DecTotalTopK> getListI(TopKParam param, UserInfoToken token) {
        param.setTradeCode(token.getCompany());
        List<DecTotalTopK> result = mapper.getListI(param);
        if(!CollectionUtils.isEmpty(result)) {
            for(int i=0; i< result.size(); i++) {
                result.get(i).setSeqNo(i+1);
            }
        }
        return result;
    }


    /**
     * 出口总货值topK
     * @param param
     * @param token
     * @return
     */
    public List<DecTotalTopK> getListE(TopKParam param, UserInfoToken token) {
        param.setTradeCode(token.getCompany());
        List<DecTotalTopK> result = mapper.getListE(param);
        if(!CollectionUtils.isEmpty(result)) {
            for(int i=0; i< result.size(); i++) {
                result.get(i).setSeqNo(i+1);
            }
        }
        return result;
    }

    /**
     * 进口总货值topK
     * @param param
     * @param token
     * @return
     */
    public List<DecTotalTopK> getGroupListI(List<GwCompany> companyList,TopKParam param, UserInfoToken token) {
        List<DecTotalTopK> result = mapper.getGroupListI(param);
        if(!CollectionUtils.isEmpty(result)) {
            for(int i=0; i< result.size(); i++) {
                result.get(i).setSeqNo(i+1);

                if(StringUtils.isNotBlank(result.get(i).getTradeCode())){
                    int finalI = i;
                    result.get(i).setTradeCode(result.get(i).getTradeCode() + "  "  + companyList.stream().filter(e->result.get(finalI).getTradeCode().equals(e.getCorpCode())).map(e->e.getCorpName()).collect(Collectors.toList()).get(0));
                }
            }
        }
        return result;
    }


    /**
     * 出口总货值topK
     * @param param
     * @param token
     * @return
     */
    public List<DecTotalTopK> getGroupListE(List<GwCompany> companyList,TopKParam param, UserInfoToken token) {
        List<DecTotalTopK> result = null;

        if(StringUtils.isNotBlank(param.getFlag()) && "0".equals(param.getFlag())){
            result = mapper.getListE(param);
        }
        else {
            result = mapper.getGroupListE(param);
        }

        if(!CollectionUtils.isEmpty(result)) {
            List<DecTotalTopK> finalResult = result;

            for(int i=0; i< result.size(); i++) {
                result.get(i).setSeqNo(i+1);

                if(StringUtils.isNotBlank(result.get(i).getTradeCode())){
                    int finalI = i;

                    result.get(i).setTradeCode(result.get(i).getTradeCode() + "  "  + companyList.stream().filter(e-> finalResult.get(finalI).getTradeCode().equals(e.getCorpCode())).map(e->e.getCorpName()).collect(Collectors.toList()).get(0));
                }
            }
        }
        return result;
    }
}
