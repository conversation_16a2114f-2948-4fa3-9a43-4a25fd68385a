<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.statRpt.erp.dao.AbnormalOfLogisticsMapper">
    <resultMap id="logisticsAbnormalResult" type="com.dcjet.cs.dto.statRpt.erp.dto.AbnormalOfLogistics">
        <result column="abnormal_type" property="abnormalType" jdbcType="VARCHAR"></result>
        <result column="ems_list_no" property="emsListNo" jdbcType="VARCHAR"></result>
        <result column="insert_time" property="insertTime" jdbcType="VARCHAR"></result>
        <result column="entry_no" property="entryNo" jdbcType="VARCHAR"></result>
        <result column="d_date" property="ddate" jdbcType="VARCHAR"></result>
        <result column="ems_no" property="emsNo" jdbcType="VARCHAR"></result>
        <result column="list_no" property="listNo" jdbcType="VARCHAR"></result>
        <result column="trade_mode" property="tradeMode" jdbcType="VARCHAR"></result>
        <result column="traf_mode" property="trafMode" jdbcType="VARCHAR"></result>
        <result column="i_e_port" property="ieport" jdbcType="VARCHAR"></result>
        <result column="overseas_shipper" property="overseasShipper" jdbcType="VARCHAR"></result>
        <result column="g_name" property="gname" jdbcType="VARCHAR"></result>
    </resultMap>

    <select id="getListI" resultMap="logisticsAbnormalResult">
        select '1' as abnormal_type, qdh.EMS_LIST_NO,
               tdh.INSERT_TIME, bgdh.ENTRY_NO, bgdh.D_DATE,
               qdh.EMS_NO, qdh.LIST_NO, qdh.TRADE_MODE, qdh.TRAF_MODE, qdh.I_E_PORT,
               qdh.OVERSEAS_SHIPPER||'|'||qdh.OVERSEAS_SHIPPER_NAME as OVERSEAS_SHIPPER, qdl.G_NAME
        from T_DEC_I_LOGISTICS_TRACK l
            join T_DEC_ERP_I_HEAD_N tdh on tdh.SID = l.SID
            join T_DEC_I_BILL_HEAD qdh on qdh.HEAD_ID = tdh.SID
            left join T_DEC_I_BILL_LIST qdl on qdl.HEAD_ID = qdh.SID and qdl.SERIAL_NO = 1
            left join T_DEC_I_ENTRY_HEAD bgdh on bgdh.BILL_HEAD_ID = qdh.SID
        where tdh.TRADE_CODE = #{tradeCode}
            <include refid="condition"></include>
            <include refid="conditionForDamage"></include>
        union all
        select '2' as abnormal_type, qdh.EMS_LIST_NO,
               tdh.INSERT_TIME, bgdh.ENTRY_NO, bgdh.D_DATE,
               qdh.EMS_NO, qdh.LIST_NO, qdh.TRADE_MODE, qdh.TRAF_MODE, qdh.I_E_PORT,
               qdh.OVERSEAS_SHIPPER||'|'||qdh.OVERSEAS_SHIPPER_NAME as OVERSEAS_SHIPPER, qdl.G_NAME
        from T_DEC_I_CUSTOMS_TRACK c
            join t_dec_i_bill_head qdh on qdh.sid = c.HEAD_ID
            left join T_DEC_I_BILL_LIST qdl on qdl.HEAD_ID = qdh.SID and qdl.SERIAL_NO = 1
            left join T_DEC_I_ENTRY_HEAD bgdh on bgdh.BILL_HEAD_ID = qdh.SID
            join t_dec_erp_i_head_n tdh on tdh.SID = qdh.HEAD_ID
        where tdh.TRADE_CODE = #{tradeCode}
            <include refid="condition"></include>
            <include refid="conditionForCheck"></include>
        UNION ALL
        select '3' as abnormal_type, qdh.EMS_LIST_NO,
               tdh.INSERT_TIME, bgdh.ENTRY_NO, bgdh.D_DATE,
               qdh.EMS_NO, qdh.LIST_NO, qdh.TRADE_MODE, qdh.TRAF_MODE, qdh.I_E_PORT,
               qdh.OVERSEAS_SHIPPER||'|'||qdh.OVERSEAS_SHIPPER_NAME as OVERSEAS_SHIPPER, qdl.G_NAME
        from T_DEC_I_CUSTOMS_TRACK c
            join t_dec_i_bill_head qdh on qdh.sid = c.HEAD_ID
            left join T_DEC_I_BILL_LIST qdl on qdl.HEAD_ID = qdh.SID and qdl.SERIAL_NO = 1
            left join T_DEC_I_ENTRY_HEAD bgdh on bgdh.BILL_HEAD_ID = qdh.SID
            join t_dec_erp_i_head_n tdh on tdh.SID = qdh.HEAD_ID
        where tdh.TRADE_CODE = #{tradeCode}
            <include refid="condition"></include>
            <include refid="conditionForCustomsCheck"></include>
    </select>

    <select id="getListE" resultMap="logisticsAbnormalResult">
        select '2' as abnormal_type, qdh.EMS_LIST_NO,
               tdh.INSERT_TIME, bgdh.ENTRY_NO, bgdh.D_DATE,
               qdh.EMS_NO, qdh.LIST_NO, qdh.TRADE_MODE, qdh.TRAF_MODE, qdh.I_E_PORT,
               qdh.OVERSEAS_SHIPPER||'|'||qdh.OVERSEAS_SHIPPER_NAME as OVERSEAS_SHIPPER, qdl.G_NAME
        from T_DEC_E_CUSTOMS_TRACK c
            join t_dec_e_bill_head qdh on qdh.sid = c.HEAD_ID
            left join T_DEC_E_BILL_LIST qdl on qdl.HEAD_ID = qdh.SID and qdl.SERIAL_NO = 1
            left join T_DEC_E_ENTRY_HEAD bgdh on bgdh.BILL_HEAD_ID = qdh.SID
            join t_dec_erp_e_head_n tdh on tdh.SID = qdh.HEAD_ID
        where tdh.TRADE_CODE = #{tradeCode}
            <include refid="condition"></include>
            <include refid="conditionForCheck"></include>
        union all
        select '3' as abnormal_type, qdh.EMS_LIST_NO,
               tdh.INSERT_TIME, bgdh.ENTRY_NO, bgdh.D_DATE,
               qdh.EMS_NO, qdh.LIST_NO, qdh.TRADE_MODE, qdh.TRAF_MODE, qdh.I_E_PORT,
               qdh.OVERSEAS_SHIPPER||'|'||qdh.OVERSEAS_SHIPPER_NAME as OVERSEAS_SHIPPER, qdl.G_NAME
        from T_DEC_E_CUSTOMS_TRACK c
            join t_dec_e_bill_head qdh on qdh.sid = c.HEAD_ID
            left join T_DEC_E_BILL_LIST qdl on qdl.HEAD_ID = qdh.SID and qdl.SERIAL_NO = 1
            left join T_DEC_E_ENTRY_HEAD bgdh on bgdh.BILL_HEAD_ID = qdh.SID
            join t_dec_erp_e_head_n tdh on tdh.SID = qdh.HEAD_ID
        where tdh.TRADE_CODE = #{tradeCode}
            <include refid="condition"></include>
            <include refid="conditionForCustomsCheck"></include>
    </select>


    <sql id="condition">
        and tdh.APPR_STATUS = '8'
        <if test="emsListNo != null and emsListNo != ''">
            and qdh.EMS_LIST_NO like concat('%', concat(#{emsListNo}, '%'))
        </if>
        <if test="entryNo != null and entryNo != ''">
            and bgdh.ENTRY_NO like concat('%', concat(#{entryNo}, '%'))
        </if>
        <if test="listNo != null and listNo != ''">
            and qdh.LIST_NO like concat('%', concat(#{listNo}, '%'))
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[and tdh.INSERT_TIME >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[and tdh.INSERT_TIME < to_date(#{insertTimeTo}, 'yyyy-MM-dd') + 1]]>
        </if>
    </sql>

    <sql id="conditionForDamage">
        <if test='damageMark == null or damageMark == ""'>
            and l.DAMAGE_MARK = '1'
        </if>
        <if test="damageMark != null and damageMark != ''">
            <if test='damageMark == "1"'>
                and l.DAMAGE_MARK = '1'
            </if>
            <if test='damageMark == "0"'>
                and 1=2
            </if>
        </if>
    </sql>

    <sql id="conditionForCustomsCheck">
        <if test='customsCheckMark == null or customsCheckMark == ""'>
            and c.CUSTOMS_CHECK_REASON is not null
        </if>
        <if test="customsCheckMark != null and customsCheckMark != ''">
            <if test='customsCheckMark == "1"'>
                and c.CUSTOMS_CHECK_REASON is not null
            </if>
            <if test='customsCheckMark == "0"'>
                and 1=2
            </if>
        </if>
    </sql>

    <sql id="conditionForCheck">
        <if test='checkMark == null or checkMark == ""'>
            and c.check_reason is not null
        </if>
        <if test="checkMark != null and checkMark != ''">
            <if test='checkMark == "1"'>
                and c.check_reason is not null
            </if>
            <if test='checkMark == "0"'>
                and 1=2
            </if>
        </if>
    </sql>

</mapper>
