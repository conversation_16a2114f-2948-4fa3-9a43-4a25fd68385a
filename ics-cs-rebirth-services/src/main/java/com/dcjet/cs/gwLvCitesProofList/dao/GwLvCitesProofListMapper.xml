<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.gwLvCitesProofList.dao.GwLvCitesProofListMapper">
    <resultMap id="gwLvCitesProofListResultMap" type="com.dcjet.cs.gwLvCitesProofList.model.GwLvCitesProofList">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="brand_code" property="brandCode" jdbcType="VARCHAR" />
		<result column="batch_file_no" property="batchFileNo" jdbcType="VARCHAR" />
		<result column="out_side_no" property="outSideNo" jdbcType="VARCHAR" />
		<result column="hp_code" property="hpCode" jdbcType="VARCHAR" />
		<result column="is_quota" property="isQuota" jdbcType="VARCHAR" />
		<result column="cop_g_no" property="copGNo" jdbcType="VARCHAR" />
		<result column="g_name" property="gName" jdbcType="VARCHAR" />
		<result column="code_t_s" property="codeTS" jdbcType="VARCHAR" />
		<result column="declare_price" property="declarePrice" jdbcType="NUMERIC" />
		<result column="declare_num" property="declareNum" jdbcType="NUMERIC" />
		<result column="declare_total" property="declareTotal" jdbcType="NUMERIC" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="risk_quantity" property="riskQuantity" jdbcType="NUMERIC" />
		<result column="out_side_proof_valid" property="outSideProofValid" jdbcType="TIMESTAMP" />
		<result column="transport_no" property="transportNo" jdbcType="VARCHAR" />
		<result column="species_name" property="speciesName" jdbcType="VARCHAR" />
		<result column="scientific_name" property="scientificName" jdbcType="VARCHAR" />
		<result column="customs_type" property="customsType" jdbcType="VARCHAR" />
		<result column="risk_suit_quantity" property="riskSuitQuantity" jdbcType="NUMERIC" />
		<result column="risk_quantity_sum" property="riskQuantitySum" jdbcType="NUMERIC" />
		<result column="model_num" property="modelNum" jdbcType="NUMERIC" />
		<result column="model_unit" property="modelUnit" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="batch_file_serial" property="batchFileSerial" jdbcType="VARCHAR" />
        <result column="erp_factor" property="erpFactor" jdbcType="NUMERIC" />
        <result column="customsTypeName" property="customsTypeName" jdbcType="VARCHAR" />
        <result column="tr_mark" property="trMark" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
        t.sid
        ,t.insert_time
,t.insert_user
,t.update_time
,t.update_user
,t.trade_code
,t.brand_code
,t.batch_file_no
,t.out_side_no
,t.hp_code
,t.is_quota
,t.cop_g_no
,t.g_name
,t.code_t_s
,t.declare_price
,t.declare_num
,t.declare_total
,t.curr
,t.risk_quantity
,t.out_side_proof_valid
,t.transport_no
,t.species_name
,t.scientific_name
,t.customs_type
,t.risk_suit_quantity
,t.risk_quantity_sum
,t.model_num
,t.model_unit
,t.head_id
,t.batch_file_serial
,t.erp_factor
,p.name customsTypeName
,t.tr_mark
    </sql>
	<sql id="condition">
	<if test="headId != null and headId != ''">
		  t.head_id = #{headId}
	</if>
    <if test="brandCode != null and brandCode != ''">
		and t.brand_code = #{brandCode}
	</if>
    <if test="batchFileNo != null and batchFileNo != ''">
	  and t.batch_file_no like '%'|| #{batchFileNo} || '%'
	</if>
    <if test="outSideNo != null and outSideNo != ''">
	  and t.out_side_no like '%'|| #{outSideNo} || '%'
	</if>
    <if test="isQuota != null and isQuota != ''">
		and t.is_quota = #{isQuota}
	</if>
    <if test="copGNo != null and copGNo != ''">
	  and t.cop_g_no like '%'|| #{copGNo} || '%'
	</if>
    <if test="batchFileSerial != null and batchFileSerial != ''">
	  and t.batch_file_serial like '%'|| #{batchFileSerial} || '%'
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwLvCitesProofListResultMap" parameterType="com.dcjet.cs.gwLvCitesProofList.model.GwLvCitesProofList">
        select <include refid="Base_Column_List" />
        from t_gw_lv_cites_proof_list t
        LEFT JOIN t_gw_lv_customer_params P ON P.code = t.customs_type AND P.TYPE='CUSTOM_TYPE'
        <where>
            <!-- 用户Grid查询 and 条件-->
			<include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        update t_gw_lv_cites_batch_file_list l set status='1',proof_use_date=null where exists(
            select 1 from t_gw_lv_cites_proof_list t where l.out_side_no=t.out_side_no and l.transport_no=t.transport_no
            and t.SID in
            <foreach collection="list"  item="item" open="(" separator="," close=")"  >
                #{item}
            </foreach>
        );
        delete from t_gw_lv_cites_proof_list t
		where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
              #{item}
        </foreach>;
    </delete>
    <delete id="deleteByHeadIds" parameterType="java.util.List">

    </delete>
    <select id="getListNumByHeadIds" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from t_gw_lv_cites_proof_list t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>

    <insert id="insertBatchFileExtract" parameterType="java.util.List">
        insert into t_gw_lv_cites_proof_list(
            insert_time,
            insert_user,
            update_time,
            update_user,
            trade_code,
            brand_code,
            batch_file_no,
            out_side_no,
            hp_code,
            is_quota,
            cop_g_no,
            g_name,
            code_t_s,
            declare_price,
            declare_num,
            declare_total,
            curr,
            risk_quantity,
            out_side_proof_valid,
            transport_no,
            species_name,
            scientific_name,
            customs_type,
            risk_suit_quantity,
            risk_quantity_sum,
            model_num,
            model_unit,
            head_id,
            batch_file_serial,
            erp_factor,
            tr_mark)
        select
            CURRENT_TIMESTAMP insert_time,
            'sys' insert_user,
            CURRENT_TIMESTAMP update_time,
            'sys' update_user,
            t.trade_code,
            t.brand_code,
            l.batch_file_no,
            t.out_side_no,
            t.hp_code,
            l.is_quota,
            t.cop_g_no,
            t.g_name,
            t.code_t_s,
            t.declare_price,
            t.declare_num,
            t.declare_total,
            t.curr,
            t.risk_quantity,
            l.out_side_proof_valid,
            l.transport_no,
            t.species_name,
            t.scientific_name,
            t.customs_type,
            t.risk_suit_quantity,
            t.risk_quantity_sum,
            t.model_num,
            t.model_unit,
            #{headId} head_id,
            m.batch_file_serial,
            t.erp_factor,
            m.tr_mark
        from t_gw_lv_cites_batch_file_sku_list t
        inner join t_gw_lv_cites_batch_file_main m on m.sid=t.head_id
        inner join t_gw_lv_cites_batch_file_list l on l.head_id = t.head_id and l.out_side_no = t.out_side_no
        where t.out_side_no in <foreach collection="outSideNos"  item="item" open="(" separator="," close=")"  >
                #{item}
            </foreach>
          and l.transport_no in <foreach collection="transportNo"  item="item" open="(" separator="," close=")"  >
                #{item}
            </foreach>;

        update t_gw_lv_cites_batch_file_list set status='2',proof_use_date=CURRENT_TIMESTAMP
        where out_side_no in <foreach collection="outSideNos"  item="item" open="(" separator="," close=")"  >
                #{item}
            </foreach>
        and transport_no in<foreach collection="transportNo"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
    </insert>
</mapper>
