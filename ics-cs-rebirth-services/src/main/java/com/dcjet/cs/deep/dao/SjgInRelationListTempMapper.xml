<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.deep.dao.SjgInRelationListTempMapper">
    <resultMap id="sjgInRelationListTempResultMap" type="com.dcjet.cs.deep.model.SjgInRelationListTemp">
		<result column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="HEAD_ID" property="headId" jdbcType="VARCHAR" />
		<result column="FAC_G_NO_OUT" property="facGNoOut" jdbcType="VARCHAR" />
		<result column="COP_G_NO_OUT" property="copGNoOut" jdbcType="VARCHAR" />
		<result column="G_NO_OUT" property="gNoOut" jdbcType="VARCHAR" />
		<result column="FAC_G_NO_IN" property="facGNoIn" jdbcType="VARCHAR" />
		<result column="COP_G_NO_IN" property="copGNoIn" jdbcType="VARCHAR" />
		<result column="G_NO_IN" property="gNoIn" jdbcType="VARCHAR" />
		<result column="INSERT_TIME" property="insertTime" jdbcType="DATE" />
		<result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="DATE" />
		<result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
		<result column="TRADE_CODE_IN" property="tradeCodeIn" jdbcType="VARCHAR" />
		<result column="TEMP_OWNER" property="tempOwner" jdbcType="VARCHAR" />
		<result column="TEMP_FLAG" property="tempFlag" jdbcType="NUMERIC" />
		<result column="TEMP_REMARK" property="tempRemark" jdbcType="VARCHAR" />
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,HEAD_ID
     ,FAC_G_NO_OUT
     ,COP_G_NO_OUT
     ,G_NO_OUT
     ,FAC_G_NO_IN
     ,COP_G_NO_IN
     ,G_NO_IN
     ,INSERT_TIME
     ,INSERT_USER
     ,UPDATE_TIME
     ,UPDATE_USER
     ,TRADE_CODE_IN
     ,TEMP_OWNER
     ,TEMP_FLAG
     ,TEMP_REMARK
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
    </sql>
    <sql id="condition">
    <if test="headId != null and headId != ''"> 
		and HEAD_ID = #{headId}
	</if>
    <if test="facGNoOut != null and facGNoOut != ''"> 
		and FAC_G_NO_OUT = #{facGNoOut}
	</if>
    <if test="copGNoOut != null and copGNoOut != ''"> 
		and COP_G_NO_OUT = #{copGNoOut}
	</if>
    <if test="gNoOut != null and gNoOut != ''"> 
		and G_NO_OUT = #{gNoOut}
	</if>
    <if test="facGNoIn != null and facGNoIn != ''"> 
		and FAC_G_NO_IN = #{facGNoIn}
	</if>
    <if test="copGNoIn != null and copGNoIn != ''"> 
		and COP_G_NO_IN = #{copGNoIn}
	</if>
    <if test="gNoIn != null and gNoIn != ''"> 
		and G_NO_IN = #{gNoIn}
	</if>
    <if test="tradeCodeIn != null and tradeCodeIn != ''"> 
		and TRADE_CODE_IN = #{tradeCodeIn}
	</if>
    <if test="tempOwner != null and tempOwner != ''"> 
		and TEMP_OWNER = #{tempOwner}
	</if>
    <if test="tempFlag != null and tempFlag != ''"> 
		and TEMP_FLAG = #{tempFlag}
	</if>
    <if test="tempRemark != null and tempRemark != ''"> 
		and TEMP_REMARK = #{tempRemark}
	</if>
    </sql>    
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="sjgInRelationListTempResultMap" parameterType="com.dcjet.cs.deep.model.SjgInRelationListTemp">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_SJG_IN_RELATION_LIST_TEMP t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_SJG_IN_RELATION_LIST_TEMP t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="selectByFlag" resultMap="sjgInRelationListTempResultMap" parameterType="map">
        select t.* from T_SJG_IN_RELATION_LIST_TEMP t
        <where>
            and t.TEMP_OWNER = #{TEMP_OWNER,jdbcType=VARCHAR}
            AND t.TEMP_FLAG IN
            <foreach item="item" index="index" collection="TEMP_FLAG" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
--         order by t.TEMP_INDEX ASC
    </select>
</mapper>
