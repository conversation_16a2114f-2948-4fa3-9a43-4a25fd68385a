package com.dcjet.cs.deep.service;

import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.deep.dao.SjgOutCheckListMapper;
import com.dcjet.cs.deep.dao.SjgOutCheckListTempMapper;
import com.dcjet.cs.deep.mapper.SjgOutCheckListTempDtoMapper;
import com.dcjet.cs.deep.model.SjgOutCheckList;
import com.dcjet.cs.deep.model.SjgOutCheckListTemp;
import com.dcjet.cs.mat.dao.MatImgexgMapper;
import com.dcjet.cs.mat.model.MatImgexg;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.xdo.common.exception.ErrorException;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.EnumCollection;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class SjgOutCheckListImportService implements ImportHandlerInterface {
    @Resource
    private SjgOutCheckListMapper sjgOutCheckListMapper;
    @Resource
    private SjgOutCheckListTempMapper sjgOutCheckListTempMapper;
    @Resource
    private SjgOutCheckListTempDtoMapper sjgOutCheckListTempDtoMapper;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private BiClientInformationMapper biClientInformationMapper;
    @Resource
    private MatImgexgMapper matImgexgMapper;
    @Resource
    private BulkSqlOpt bulkSqlOpt;

    // 0：正确数据 1：错误数据 2：警告数据 对应于EnumCollection.EntityListType的枚举的ordinal
    public final int CORRECT_FLAG = EnumCollection.EntityListType.CORRECT_LIST.ordinal();
    public final int WRONG_FLAG = EnumCollection.EntityListType.WRONG_LIST.ordinal();

    @Override
    public String getTaskCode() {
        String strTaskCode = "SJG_OUT_CHECK_LIST";
        return strTaskCode;
    }

    @Override
    public List<ExcelImportDto> checkData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        List<ExcelImportDto> excelImportDtoList = new ArrayList<>();
        if(mapObjectList.size() <= 0){
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }
        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        //校验必填参数
        if(!mapBusinessParam.containsKey("insertUser")) {
            XdoImportLogger.log("部分自定义参数未成功获取");
            return null;
        }
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        String insertUser = mapBusinessParam.get("insertUser").toString();
        String guid = UUID.randomUUID().toString();
        int intObjectListSize = objectList.size();
        XdoImportLogger.log("共获取实体：" + intObjectListSize);
        List<SjgOutCheckListTemp> sjgOutCheckListTemps = Collections.synchronizedList(new ArrayList<>(intObjectListSize));
        Date currDate = new Date();
        objectList.forEach(pa -> {
            SjgOutCheckListTemp el = (SjgOutCheckListTemp)pa;
            el.setSid(UUID.randomUUID().toString());
            el.setTradeCode(tradeCode);
            el.setInsertUser(insertUser);
            el.setInsertTime(currDate);
            el.setTempOwner(guid);
            sjgOutCheckListTemps.add(el);
        });
        checkCurrTmp(sjgOutCheckListTemps, tradeCode);
        XdoImportLogger.log("共获取插入实体：" + sjgOutCheckListTemps.size() + ";执行快速入库操作");
        int status = bulkSqlOpt.batchInsert(sjgOutCheckListTemps, SjgOutCheckListTempMapper.class);
        if(status != 0) {
            XdoImportLogger.log("执行数据批量入库时出错：status" + status);
            return null;
        }
        ExcelImportDto<SjgOutCheckListTemp> excelImportDto = new ExcelImportDto<>();
        excelImportDto.setTempOwnerId(guid);
        //获取正确、错误、警告数据列表(警告数据根据各自业务需求设置,可不加)
        List<SjgOutCheckListTemp> correctList = selectDataList(guid, CORRECT_FLAG);
        excelImportDto.setCorrectNumber(correctList.size());
        excelImportDto.setCorrectList(correctList);
        List<SjgOutCheckListTemp> wrongList = selectDataList(guid, WRONG_FLAG);
        excelImportDto.setWrongNumber(wrongList.size());
        if(wrongList.size() > 0 && correctList.size() > 0){
            wrongList.addAll(correctList);
        }
        excelImportDto.setWrongList(wrongList);
        XdoImportLogger.log("correct："+ excelImportDto.getCorrectList().size()
                +";wrong："+excelImportDto.getWrongList().size());
        excelImportDtoList.add(excelImportDto);
        return excelImportDtoList;
    }

    private void checkCurrTmp(List<SjgOutCheckListTemp> SjgOutCheckListTemps, String tradeCode) {
        StringBuffer buffer;
        String errMsg = "";
        List<BiClientInformation> customers = null;
        List<MatImgexg> matImgexgs;
        Map<String, Object> paramMap;
        for(SjgOutCheckListTemp temp : SjgOutCheckListTemps) {
            buffer = new StringBuffer("");
            paramMap = new HashMap<>();
            paramMap.put("billNo", temp.getBillNo());
            paramMap.put("lineNo", temp.getLineNo());
            if(existsWithMap(paramMap)) {
                buffer.append(xdoi18n.XdoI18nUtil.t("单证编号和序号已存在！|"));
            }
            paramMap.put("tradeCode", tradeCode);
            paramMap.put("facGNo", temp.getFacGNo());
            paramMap.put("gMark", CommonEnum.GMarkEnum.EXG.getCode());
            matImgexgs = matImgexgMapper.selectByParamMap(paramMap);
            if(matImgexgs.size() == 0) {
                buffer.append(xdoi18n.XdoI18nUtil.t("企业料号不存在！|"));
            }
//            //申报数量
//            if(temp.getQty().multiply(temp.getDecPrice()).compareTo(temp.getDecTotal()) != 0) {
//                buffer.append("申报数量和报关单价之积不等于总价！|");
//            }
            //币制
            if (StringUtils.isBlank(pCodeHolder.getValue(PCodeType.CURR_OUTDATED, temp.getCurr()))) {
                buffer.append(xdoi18n.XdoI18nUtil.t("币制不存在|"));
            }
            //移动类型
            if(!StringUtils.equals(ConstantsStatus.STATUS_0, temp.getIeType()) && !StringUtils.equals(ConstantsStatus.STATUS_1, temp.getIeType())) {
                buffer.append(xdoi18n.XdoI18nUtil.t("移动类型只可以为0（成品入库）或者1（成品出库）！|"));
            }
            //库位别
            //发票号
            //交易日期
            //PO号
            //供应商代码
            if(StringUtils.isNotBlank(temp.getCustomerCode())) {
                customers =  biClientInformationMapper.findByCodeAndType(tradeCode, temp.getCustomerCode(),
                        Arrays.asList(new String[]{BiClientInformation.CUSTOMER_TYPE_CLI}));
                if(customers == null || customers.size() != 1) {
                    buffer.append(xdoi18n.XdoI18nUtil.t("客户代码不存在或者存在多个！|"));
                }
            }
            errMsg = buffer.toString();
            if(StringUtils.isNotBlank(errMsg)) {
                temp.setTempRemark(errMsg.substring(0, errMsg.length() - 1));
                temp.setTempFlag(WRONG_FLAG);
            } else {
                temp.setOutWay(CommonEnum.InOutWayEnum.WAY_6.getCode());
                temp.setBusinessId(temp.getSid());
                temp.setConfirmStatus(ConstantsStatus.STATUS_0);
                temp.setBondMark(CommonEnum.BondMarkEnum.BOND.getCode());
                temp.setGMark(CommonEnum.GMarkEnum.EXG.getCode());
                temp.setDataSource(ConstantsStatus.STATUS_2);
                if(matImgexgs.size() == 1) {
                    temp.setUnit(matImgexgs.get(0).getUnit());
                }
                if(customers != null) {
                    temp.setCustomerName(customers.get(0).getCompanyName());
                    temp.setCustomerLinkMan(customers.get(0).getLinkmanName());
                }
                temp.setTempRemark(errMsg);
                temp.setTempFlag(CORRECT_FLAG);
            }
        }
    }

    private boolean existsWithMap(Map<String, Object> paramMap) {
       return sjgOutCheckListMapper.selectCountByParamMap(paramMap) > 0;
    }

    @Override
    public ResultObject persistData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        //按配置顺序（同时也是业务校验时添加到excelImportDtoList的顺序）获取临时表批次Id
        if(tempOwnerIdList == null || tempOwnerIdList.size() <= 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("临时表处理批次Id为空，无法执行持久化数据方法"));
        }
        String strTempOwnerId = tempOwnerIdList.get(0);
        List<SjgOutCheckListTemp> tempList = selectDataList(strTempOwnerId, CORRECT_FLAG);
        List<SjgOutCheckList> list = new ArrayList<>(tempList.size());
        if(CollectionUtils.isNotEmpty(tempList)) {
            tempList.forEach(el -> {
                SjgOutCheckList model = new SjgOutCheckList();
                sjgOutCheckListTempDtoMapper.updatePo(el, model);
                list.add(model);
            });
            int status = bulkSqlOpt.batchInsert(list, SjgOutCheckListMapper.class);
            if (status != 0) {
                XdoImportLogger.log("执行数据批量入库时,已插入出错 status:" + status);
                return null;
            } else {
//                SjgOutCheckListTempMapper.deleteByOwnerId(strTempOwnerId);
                XdoImportLogger.log("执行数据批量入库成功，批次id：" + strTempOwnerId + ", 插入数据条数：" + list.size());
            }
        }
        return resultObject;
    }

    /**
     * 获取导入的各种类型数据
     * @param tempOwner
     * @param tempFlag
     * @return
     */
    private List<SjgOutCheckListTemp> selectDataList(String tempOwner, Integer tempFlag) {
        Map<String, Object> param = new HashMap<>(2);
        param.put("TEMP_OWNER", tempOwner);
        List flags = new ArrayList();
        flags.add(tempFlag);
        param.put("TEMP_FLAG", flags);
        return sjgOutCheckListTempMapper.selectByFlag(param);
    }
}
