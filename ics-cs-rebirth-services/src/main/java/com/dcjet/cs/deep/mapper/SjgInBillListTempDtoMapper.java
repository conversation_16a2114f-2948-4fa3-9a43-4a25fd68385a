package com.dcjet.cs.deep.mapper;
import com.dcjet.cs.deep.model.SjgInBillList;
import com.dcjet.cs.deep.model.SjgInBillListTemp;
import com.dcjet.cs.dto.deep.SjgInBillListTempParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-11-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SjgInBillListTempDtoMapper {
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    SjgInBillListTemp toPo(SjgInBillListTempParam param);
    /**
     * 数据库原始数据更新
     * @param sjgInBillListTempParam
     * @param sjgInBillListTemp
     */
    void updatePo(SjgInBillListTempParam sjgInBillListTempParam, @MappingTarget SjgInBillListTemp sjgInBillListTemp);
    default void patchPo(SjgInBillListTempParam sjgInBillListTempParam, SjgInBillListTemp sjgInBillListTemp) {
        // TODO 自行实现局部更新
    }

    void updatePo(SjgInBillListTemp sjgInBillListTemp, @MappingTarget SjgInBillList sjgInBillList);
}
