<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.deep.dao.ProductListMapper">
    <resultMap id="productListResultMap" type="com.dcjet.cs.deep.model.ProductList">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="BILL_NO" property="billNo" jdbcType="VARCHAR"/>
        <result column="LINE_NO" property="lineNo" jdbcType="NUMERIC"/>
        <result column="WAREHOUSE_NO" property="warehouseNo" jdbcType="VARCHAR"/>
        <result column="IE_TYPE" property="ieType" jdbcType="VARCHAR"/>
        <result column="LINKED_NO" property="linkedNo" jdbcType="VARCHAR"/>
        <result column="SO_NO" property="soNo" jdbcType="VARCHAR"/>
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="DELIVERY_DATE" property="deliveryDate" jdbcType="TIMESTAMP"/>
        <result column="FAC_G_NO" property="facGNo" jdbcType="VARCHAR"/>
        <result column="CUSTOMER_G_NO" property="customerGNo" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="NUMERIC"/>
        <result column="UNIT" property="unit" jdbcType="VARCHAR"/>
        <result column="DEC_PRICE" property="decPrice" jdbcType="NUMERIC"/>
        <result column="DEC_TOTAL" property="decTotal" jdbcType="NUMERIC"/>
        <result column="CURR" property="curr" jdbcType="VARCHAR"/>
        <result column="BOND_MARK" property="bondMark" jdbcType="VARCHAR"/>
        <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR"/>
        <result column="BUY_ADDRESS" property="buyAddress" jdbcType="VARCHAR"/>
        <result column="OUT_WAY" property="outWay" jdbcType="VARCHAR"/>
        <result column="BOM_VERSION" property="bomVersion" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="TEMP_OWNER" property="tempOwner" jdbcType="VARCHAR"/>
        <result column="TEMP_FLAG" property="tempFlag" jdbcType="NUMERIC"/>
        <result column="TEMP_REMARK" property="tempRemark" jdbcType="VARCHAR"/>
        <result column="MODIFY_MARK" property="modifyMark" jdbcType="NUMERIC"/>
        <result column="LAST_MODIFY_DATE" property="lastModifyDate" jdbcType="TIMESTAMP"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE"/>
        <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="SERIAL_NO" property="serialNo" jdbcType="NUMERIC"/>
        <result column="G_MARK" property="gMark" jdbcType="VARCHAR"/>
        <result column="WAREHOUSE" property="warehouse" jdbcType="VARCHAR"/>
        <result column="FACTORY" property="factory" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,BILL_NO
     ,LINE_NO
     ,WAREHOUSE_NO
     ,IE_TYPE
     ,LINKED_NO
     ,SO_NO
     ,INVOICE_NO
     ,DELIVERY_DATE
     ,FAC_G_NO
     ,CUSTOMER_G_NO
     ,QTY
     ,UNIT
     ,DEC_PRICE
     ,DEC_TOTAL
     ,CURR
     ,BOND_MARK
     ,CUSTOMER_CODE
     ,BUY_ADDRESS
     ,OUT_WAY
     ,BOM_VERSION
     ,NOTE
     ,TRADE_CODE
     ,TEMP_OWNER
     ,TEMP_FLAG
     ,TEMP_REMARK
     ,MODIFY_MARK
     ,LAST_MODIFY_DATE
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,DATA_SOURCE
     ,STATUS
     ,SERIAL_NO
     ,G_MARK
     ,WAREHOUSE
     ,FACTORY
    </sql>
    <sql id="condition">
        and TRADE_CODE = #{tradeCode}
        <if test="billNo != null and billNo != ''">
            and BILL_NO = #{billNo}
        </if>
        <if test="decPrice == 1">
            and (DEC_PRICE = 0 OR DEC_PRICE IS NULL)
        </if>
        <if test="warehouseNo != null and warehouseNo != ''">
            and WAREHOUSE_NO = #{warehouseNo}
        </if>
        <if test="linkedNo != null and linkedNo != ''">
            and LINKED_NO = #{linkedNo}
        </if>
        <if test="soNo != null and soNo != ''">
            and SO_NO = #{soNo}
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
            and INVOICE_NO = #{invoiceNo}
        </if>
        <if test="deliveryDateFrom != null and deliveryDateFrom != ''">
            <![CDATA[ and DELIVERY_DATE >= to_date(#{deliveryDateFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="deliveryDateTo != null and deliveryDateTo != ''">
            <![CDATA[ and DELIVERY_DATE < to_date(#{deliveryDateTo}, 'yyyy-MM-dd')+1 ]]>
        </if>
        <if test="facGNo != null and facGNo != ''">
            and FAC_G_NO = #{facGNo}
        </if>
        <if test="customerGNo != null and customerGNo != ''">
            and CUSTOMER_G_NO = #{customerGNo}
        </if>
        <if test="bondMark != null and bondMark != ''">
            and BOND_MARK = #{bondMark}
        </if>
        <if test="customerCode != null and customerCode != ''">
            and CUSTOMER_CODE = #{customerCode}
        </if>
        <if test="outWay != null and outWay != ''">
            and OUT_WAY = #{outWay}
        </if>
        <if test="tempOwner != null and tempOwner != ''">
            and TEMP_OWNER = #{tempOwner}
        </if>
        <if test="lastModifyDateFrom != null and lastModifyDateFrom != ''">
            <![CDATA[ and LAST_MODIFY_DATE >= to_date(#{lastModifyDateFrom}, 'yyyy-MM-dd')]]>
        </if>
        <if test="lastModifyDateTo != null and lastModifyDateTo != ''">
            <![CDATA[ and LAST_MODIFY_DATE < to_date(#{lastModifyDateTo}, 'yyyy-MM-dd')+1 ]]>
        </if>
        <if test="gMark != null and gMark != ''">
            and G_MARK = #{gMark}
        </if>
        <if test="status != null and status != ''">
            and STATUS = #{status}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="productListResultMap" parameterType="com.dcjet.cs.deep.model.ProductList">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_ERP_EXG_IE_LIST t
        <where>
            <include refid="condition"></include>
        </where>
        order by T.LAST_MODIFY_DATE desc,sid
    </select>

    <!-- 列表查询 and 条件 begin-->
    <select id="getListTotal" resultType="string" parameterType="com.dcjet.cs.deep.model.ProductList">
        select '总数量:'||to_char(f_xdo_nvl(sum( t.QTY), 0.0 ),'fm999999999999999990.09999')||' 总价:'||to_char(f_xdo_nvl(sum( t.DEC_TOTAL), 0.0
        ),'fm999999999999999990.09999') as total
        FROM
        T_ERP_EXG_IE_LIST t
        <where>
            <include refid="condition"></include>
        </where>
    </select>

    <!--只查询未提取的数据-->
    <select id="getListPickUp" resultMap="productListResultMap" parameterType="com.dcjet.cs.deep.model.ProductList">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_erp_exg_ie_list T
        <where>
            <include refid="condition"></include>
            and t.STATUS='0'
        </where>

        order by T.LAST_MODIFY_DATE desc
    </select>


    <delete id="deleteBySids">
        delete from T_ERP_EXG_IE_LIST t
        where
        t.trade_code = #{tradeCode}
        and t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateErp" parameterType="com.dcjet.cs.deep.model.MatreialErp">
        UPDATE t_erp_exg_ie_list T SET T.LOCK_MARK=#{uuid}
        <where>
            <include refid="condition"></include>
        </where>
    </update>

    <!-- 从erp成品表中查出lockmark已锁定和未提取的数据   -->
    <insert id="intoSjg" parameterType="string">
        INSERT INTO T_SJG_EXG_ERP_DETAIL_OUT
          (SID,
           BILL_NO,
           LINE_NO,
           WAREHOUSE_NO,
           IE_TYPE,
           LINKED_NO,
           SO_NO,
           INVOICE_NO,
           DELIVERY_DATE,
           FAC_G_NO,
           QTY,
           UNIT,
           DEC_PRICE,
           DEC_TOTAL,
           CURR,
           BOND_MARK,
           CUSTOMER_CODE,
           OUT_WAY,
           NOTE,
           TRADE_CODE,
           TEMP_OWNER,
           LAST_MODIFY_DATE,
           DATA_SOURCE,
           SERIAL_NO,
           G_MARK,
           ERP_SID,
           CONFIRM_STATUS,
           INSERT_USER,
           INSERT_TIME,
           BOND_MARK_TRANS,
           G_MARK_TRANS,
           BUY_ADDRESS,
           CUSTOMER_G_NO,
           BOM_VERSION)
          SELECT SYS_GUID(),
                 H.BILL_NO,
                 H.LINE_NO,
                 H.WAREHOUSE_NO,
                 H.IE_TYPE,
                 H.LINKED_NO,
                 H.SO_NO,
                 H.INVOICE_NO,
                 H.DELIVERY_DATE,
                 H.FAC_G_NO,
                 H.QTY,
                 H.UNIT,
                 H.DEC_PRICE,
                 H.DEC_TOTAL,
                 H.CURR,
                 s1.CUSTOM_PARAM_CODE as BOND_MARK,
                 H.CUSTOMER_CODE,
                 H.OUT_WAY,
                 H.NOTE,
                 H.TRADE_CODE,
                 H.TEMP_OWNER,
                 H.LAST_MODIFY_DATE,
                 H.DATA_SOURCE,
                 H.SERIAL_NO,
                 s2.CUSTOM_PARAM_CODE as G_MARK,
                 H.SID,
                 '0' CONFIRM_STATUS,
                 #{userName},
                CURRENT_TIMESTAMP(3),
                case H.BOND_MARK when '0' then '0' when '1' then '1' else S1.CUSTOM_PARAM_CODE end BOND_MARK_TRANS,
                case H.G_MARK when 'I' then 'I' when 'E' then 'E' when '2' then '2' when '3' then '3' when '4' then '4' else S2.CUSTOM_PARAM_CODE end G_MARK_TRANS
                 H.BUY_ADDRESS,
                 H.CUSTOMER_G_NO,
                 H.BOM_VERSION
            FROM t_erp_exg_ie_list H
            left join (select *
                         from T_BI_CUSTOMER_PARAMS P1
                        WHERE P1.TRADE_CODE =#{tradeCode}
                          AND P1.params_type = 'BONDMARK') s1
              on H.trade_code = s1.trade_code
             and H.BOND_MARK = s1.PARAMS_CODE
            left join (select *
                         from T_BI_CUSTOMER_PARAMS P1
                        WHERE P1.TRADE_CODE =#{tradeCode}
                          AND P1.params_type = 'GMARK') s2
              on H.trade_code = s2.trade_code
             and H.G_MARK = s2.PARAMS_CODE
           WHERE H.LOCK_MARK = #{uuid}
             and H.STATUS = #{status}
    </insert>

    <!--  把erp成品表中的数据状态改为已提取  -->
    <update id="updateErpStatus" parameterType="string">
        UPDATE t_erp_exg_ie_list T SET STATUS=#{status}
        WHERE T.LOCK_MARK=#{uuid}
    </update>
</mapper>
