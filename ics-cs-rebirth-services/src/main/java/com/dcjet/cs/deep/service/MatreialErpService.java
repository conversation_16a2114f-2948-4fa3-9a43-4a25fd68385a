package com.dcjet.cs.deep.service;

import com.dcjet.cs.deep.dao.MatreialErpMapper;
import com.dcjet.cs.deep.mapper.MatreialErpDtoMapper;
import com.dcjet.cs.deep.model.MatreialErp;
import com.dcjet.cs.dto.deep.ListSumInfo;
import com.dcjet.cs.dto.erp.MatreialErpDto;
import com.dcjet.cs.dto.erp.MatreialErpParam;
import com.dcjet.cs.util.ConstantsStatus;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-8-26
 */
@Service
public class MatreialErpService extends BaseService<MatreialErp> {
    @Resource
    private MatreialErpMapper matreialErpMapper;
    @Resource
    private MatreialErpDtoMapper matreialErpDtoMapper;
    @Override
    public Mapper<MatreialErp> getMapper() {
        return matreialErpMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param matreialErpParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<MatreialErpDto>> getListPaged(MatreialErpParam matreialErpParam, PageParam pageParam,UserInfoToken userInfo) {
        if(ConstantsStatus.STATUS_0.equals(matreialErpParam.getPickUp())) {
            // 如果是0说明是深加工进来的  只查询未提取的数据
            MatreialErp matreialErp = matreialErpDtoMapper.toPo(matreialErpParam);
            matreialErp.setTradeCode(userInfo.getCompany());
            Page<MatreialErp> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                    .doSelectPage(() -> matreialErpMapper.getListPickUp(matreialErp));
            List<MatreialErpDto> matreialErpDtos = page.getResult().stream().map(head -> {
                MatreialErpDto dto = matreialErpDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
            ResultObject<List<MatreialErpDto>> paged = ResultObject.createInstance(matreialErpDtos, (int) page.getTotal(), page.getPageNum());
            return paged;
        }else {
            // 启用分页查询
            MatreialErp matreialErp = matreialErpDtoMapper.toPo(matreialErpParam);
            matreialErp.setTradeCode(userInfo.getCompany());
            Page<MatreialErp> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                    .doSelectPage(() -> matreialErpMapper.getList(matreialErp));
            List<MatreialErpDto> matreialErpDtos = page.getResult().stream().map(head -> {
                MatreialErpDto dto = matreialErpDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
            ResultObject<List<MatreialErpDto>> paged = ResultObject.createInstance(matreialErpDtos, (int) page.getTotal(), page.getPageNum());
            return paged;
        }
    }

    /**
     * 从ERP料件出入库信息中提取未被提取的数据到待确认表中
     *
     * @param matreialErpParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer pickUp(MatreialErpParam matreialErpParam, PageParam pageParam,UserInfoToken userInfo) {
        int takeCount=0;
        MatreialErp matreialErp = matreialErpDtoMapper.toPo(matreialErpParam);
        matreialErp.setTradeCode(userInfo.getCompany());
        //先去erp表中查询符合条件的数据并更新
        String uuid = UUID.randomUUID().toString();
        matreialErp.setUuid(uuid);
        //先给要提取的数据打上标签
        int updateRe=matreialErpMapper.updateErp(matreialErp);
        if(updateRe>0) {
            //说明更新成功，继续走插入的操作
            String bond="BONDMARK";
            String mark="GMARK";
            takeCount=matreialErpMapper.intoSjg(uuid,ConstantsStatus.STATUS_0,bond,mark,userInfo.getCompany(),userInfo.getUserNo());
            XdoImportLogger.log("成功提取数据"+takeCount+"条");
            if(takeCount>0) {
                //跟新erp表中的已提取的数据状态
                matreialErpMapper.updateErpStatus(uuid,ConstantsStatus.STATUS_1);
            }
        }
        return takeCount;
    }

    /**
     * 功能描述:新增
     *
     * @param matreialErpParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MatreialErpDto insert(MatreialErpParam matreialErpParam, UserInfoToken userInfo) {
        MatreialErp matreialErp = matreialErpDtoMapper.toPo(matreialErpParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        matreialErp.setSid(sid);
        matreialErp.setInsertUser(userInfo.getUserNo());
        matreialErp.setInsertTime(new Date());
        // 新增数据
        int insertStatus = matreialErpMapper.insert(matreialErp);
        return  insertStatus > 0 ? matreialErpDtoMapper.toDto(matreialErp) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param matreialErpParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MatreialErpDto update(MatreialErpParam matreialErpParam, UserInfoToken userInfo) {
        MatreialErp matreialErp = matreialErpMapper.selectByPrimaryKey(matreialErpParam.getSid());
        matreialErpDtoMapper.updatePo(matreialErpParam, matreialErp);
        matreialErp.setUpdateUser(userInfo.getUserNo());
        matreialErp.setUpdateUserName(userInfo.getUserName());
        matreialErp.setUpdateTime(new Date());
        // 更新数据
        int update = matreialErpMapper.updateByPrimaryKey(matreialErp);
        return update > 0 ? matreialErpDtoMapper.toDto(matreialErp) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids,String tradeCode) {
		matreialErpMapper.deleteBySids(sids,tradeCode);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<MatreialErpDto> selectAll(MatreialErpParam exportParam, UserInfoToken userInfo) {
        MatreialErp matreialErp = matreialErpDtoMapper.toPo(exportParam);
         matreialErp.setTradeCode(userInfo.getCompany());
        List<MatreialErpDto> matreialErpDtos = new ArrayList<>();
        List<MatreialErp> matreialErps = matreialErpMapper.getList(matreialErp);
        if (CollectionUtils.isNotEmpty(matreialErps)) {
            matreialErpDtos = matreialErps.stream().map(head -> {
                MatreialErpDto dto = matreialErpDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return matreialErpDtos;
    }

    /**
     * 获取汇总统计
     *
     * <AUTHOR>
     * @param matreialErpParam
     * @return
     */
    public ResultObject getListTotal(MatreialErpParam matreialErpParam, UserInfoToken userInfo) {
        // 启用分页查询
        MatreialErp matreialErp = matreialErpDtoMapper.toPo(matreialErpParam);
        if(ConstantsStatus.STATUS_0.equals(matreialErpParam.getPickUp())) {
            // 如果是0说明是深加工进来的  只查询未提取的数据
            matreialErp.setStatus(ConstantsStatus.STATUS_0);
        }
        matreialErp.setTradeCode(userInfo.getCompany());
        String totals=matreialErpMapper.getListTotal(matreialErp);
        String lang = "";
        RequestAttributes attribs = RequestContextHolder.getRequestAttributes();
        if (attribs instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) attribs).getRequest();
            lang = request.getParameter("lang");
        }
        if (StringUtils.isNotBlank(lang)) {
            if(totals != null) {
                totals = totals.replaceAll("总数量:",xdoi18n.XdoI18nUtil.t("总数量:")).
                        replaceAll("总价:",xdoi18n.XdoI18nUtil.t("总价:"));
            }
        }
        ResultObject paged = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"),totals);
        return paged;
    }

    /**
     * 汇总信息
     * @param matreialErpParam
     * @param userInfo
     * @return
     */
    public ResultObject getListTotalSjg(MatreialErpParam matreialErpParam, UserInfoToken userInfo) {
        // 启用分页查询
        MatreialErp matreialErp = matreialErpDtoMapper.toPo(matreialErpParam);
        if(ConstantsStatus.STATUS_0.equals(matreialErpParam.getPickUp())){
            // 如果是0说明是深加工进来的  只查询未提取的数据
            matreialErp.setStatus(ConstantsStatus.STATUS_0);
        }
        matreialErp.setTradeCode(userInfo.getCompany());
        ListSumInfo totals=matreialErpMapper.getListTotalSjg(matreialErp);
        ResultObject paged = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("查询成功"), totals);
        return paged;
    }
}
