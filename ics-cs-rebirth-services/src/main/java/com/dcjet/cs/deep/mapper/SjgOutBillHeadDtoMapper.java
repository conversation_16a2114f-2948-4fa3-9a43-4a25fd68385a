package com.dcjet.cs.deep.mapper;
import com.dcjet.cs.deep.model.SjgOutBillHead;
import com.dcjet.cs.dto.deep.SjgOutBillHeadDto;
import com.dcjet.cs.dto.deep.SjgOutBillHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-11-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SjgOutBillHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    SjgOutBillHeadDto toDto(SjgOutBillHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    SjgOutBillHead toPo(SjgOutBillHeadParam param);
    /**
     * 数据库原始数据更新
     * @param sjgOutBillHeadParam
     * @param sjgOutBillHead
     */
    void updatePo(SjgOutBillHeadParam sjgOutBillHeadParam, @MappingTarget SjgOutBillHead sjgOutBillHead);
    default void patchPo(SjgOutBillHeadParam sjgOutBillHeadParam, SjgOutBillHead sjgOutBillHead) {
        // TODO 自行实现局部更新
    }
}
