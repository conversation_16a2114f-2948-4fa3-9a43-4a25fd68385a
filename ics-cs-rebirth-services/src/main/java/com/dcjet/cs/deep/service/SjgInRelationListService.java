package com.dcjet.cs.deep.service;

import com.dcjet.cs.deep.dao.SjgInRelationListMapper;
import com.dcjet.cs.deep.dao.SjgInRelationMapper;
import com.dcjet.cs.deep.mapper.SjgInRelationListDtoMapper;
import com.dcjet.cs.deep.model.SjgInRelation;
import com.dcjet.cs.deep.model.SjgInRelationList;
import com.dcjet.cs.deep.model.resultModel.SimpleInRelation;
import com.dcjet.cs.dto.deep.SjgInRelationListDto;
import com.dcjet.cs.dto.deep.SjgInRelationListParam;
import com.dcjet.cs.mat.service.MatImgexgService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.domain.SortParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-10-28
 */
@Service
public class SjgInRelationListService extends BaseService<SjgInRelationList> {
    @Resource
    private SjgInRelationMapper sjgInRelationMapper;
    @Resource
    private SjgInRelationListMapper sjgInRelationListMapper;
    @Resource
    private SjgInRelationListDtoMapper sjgInRelationListDtoMapper;
    @Override
    public Mapper<SjgInRelationList> getMapper() {
        return sjgInRelationListMapper;
    }
    @Resource
    private MatImgexgService matImgexgService;
    @Resource
    private AuditLogUtil auditLogUtil;
    private final static String MODEL = xdoi18n.XdoI18nUtil.t("进口结转-转出方备案信息表体");
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param sjgInRelationListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<SjgInRelationListDto>> getListPaged(SjgInRelationListParam sjgInRelationListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        SjgInRelationList sjgInRelationList = sjgInRelationListDtoMapper.toPo(sjgInRelationListParam);
        sjgInRelationList.setTradeCodeIn(userInfo.getCompany());
        if(StringUtils.isEmpty(pageParam.getSortOrderContent())) {
            List<SortParam> sortList = Arrays.asList(
                    new SortParam(){{setName("FAC_G_NO_IN"); setOrder(Order.ASC);}},
                    new SortParam(){{setName("G_NO_OUT"); setOrder(Order.ASC);}});
            pageParam.setSort(sortList);
        }
        Page<SjgInRelationList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> sjgInRelationListMapper.getList(sjgInRelationList));
        List<SjgInRelationListDto> sjgInRelationListDtos = page.getResult().stream().map(head -> {
            SjgInRelationListDto dto = sjgInRelationListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<SjgInRelationListDto>> paged = ResultObject.createInstance(sjgInRelationListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param sjgInRelationListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<SjgInRelationListDto> insert(SjgInRelationListParam sjgInRelationListParam, UserInfoToken userInfo) {
        SjgInRelationList sjgInRelationList = sjgInRelationListDtoMapper.toPo(sjgInRelationListParam);
        //校验
        ResultObject ro = checkInsUpt(sjgInRelationList, userInfo, null);
        if(!ro.isSuccess()) {
            return ro;
        }
        String sid = UUID.randomUUID().toString();
        sjgInRelationList.setSid(sid);
        sjgInRelationList.setTradeCodeIn(userInfo.getCompany());
        sjgInRelationList.setInsertUser(userInfo.getUserNo());
        sjgInRelationList.setInsertUserName(userInfo.getUserName());
        sjgInRelationList.setInsertTime(new Date());
        // 新增数据
        int insertStatus = sjgInRelationListMapper.insert(sjgInRelationList);
        if(insertStatus > 0) {
            ro.setData(sjgInRelationListDtoMapper.toDto(sjgInRelationList));
            auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(),MODEL,userInfo,sjgInRelationList);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return ro;
    }
    /**
     * 功能描述:修改
     *
     * @param sjgInRelationListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<SjgInRelationListDto> update(SjgInRelationListParam sjgInRelationListParam, UserInfoToken userInfo) {
        SjgInRelationList sjgInRelationList = sjgInRelationListMapper.selectByPrimaryKey(sjgInRelationListParam.getSid());
        sjgInRelationListDtoMapper.updatePo(sjgInRelationListParam, sjgInRelationList);
        //校验
        ResultObject ro = checkInsUpt(sjgInRelationList, userInfo, sjgInRelationList.getSid());
        if(!ro.isSuccess()) {
            return ro;
        }
        sjgInRelationList.setUpdateUser(userInfo.getUserNo());
        sjgInRelationList.setUpdateUserName(userInfo.getUserName());
        sjgInRelationList.setUpdateTime(new Date());
        // 更新数据
        int update = sjgInRelationListMapper.updateByPrimaryKey(sjgInRelationList);
        if(update > 0) {
            ro.setData(sjgInRelationListDtoMapper.toDto(sjgInRelationList));
            auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(),MODEL,userInfo,sjgInRelationList);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return ro;
    }

    /**
     * 校验功能
     * @return
     */
    private ResultObject checkInsUpt(SjgInRelationList sjgInRelationList, UserInfoToken userInfo, String sid) {
        ResultObject ro = ResultObject.createInstance(true, sid==null?xdoi18n.XdoI18nUtil.t("新增成功"):xdoi18n.XdoI18nUtil.t("修改成功"));
        //校验企业料号是否存在
        SjgInRelation headM = sjgInRelationMapper.selectByPrimaryKey(sjgInRelationList.getHeadId());
        if(headM == null) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("表头信息不存在"));
        }
        Boolean bl = matImgexgService.existsFacGNo(userInfo.getCompany(), headM.getEmsNoIn(), CommonEnum.GMarkEnum.IMG.getCode(), sjgInRelationList.getFacGNoIn());
        if(!bl) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("企业料号不存在"));
        }
        //校验重复性
        Set<String> sets = getKeyFromHeadId(sjgInRelationList.getHeadId(), sid);
        if(sets.contains(sjgInRelationList.getFacGNoIn() + "||" + sjgInRelationList.getGNoOut())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("已存在转入企业料号，转出备案序号"));
        }
        return ro;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        Example exampleList = new Example(SjgInRelationList.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andIn("sid", sids);
        List<SjgInRelationList> sjgInRelationListList = sjgInRelationListMapper.selectByExample(exampleList);
		sjgInRelationListMapper.deleteBySids(sids);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.DELETE.getValue(),MODEL,userInfo,sjgInRelationListList);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<SjgInRelationListDto> selectAll(SjgInRelationListParam exportParam, UserInfoToken userInfo) {
        SjgInRelationList sjgInRelationList = sjgInRelationListDtoMapper.toPo(exportParam);
        sjgInRelationList.setTradeCodeIn(userInfo.getCompany());
        List<SjgInRelationListDto> sjgInRelationListDtos = new ArrayList<>();
        List<SjgInRelationList> sjgInRelationLists = sjgInRelationListMapper.getList(sjgInRelationList);
        if (CollectionUtils.isNotEmpty(sjgInRelationLists)) {
            sjgInRelationListDtos = sjgInRelationLists.stream().map(head -> {
                SjgInRelationListDto dto = sjgInRelationListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return sjgInRelationListDtos;
    }

    public Set<String> getKeyFromHeadId(String headId, String sid) {
        Example example = new Example(SjgInRelationList.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("headId",headId);
        if(sid != null) {
            criteria.andNotEqualTo("sid", sid);
        }
        List<SjgInRelationList> list = sjgInRelationListMapper.selectByExample(example);
        return list.stream().map(el -> el.getFacGNoIn() + "||" + el.getGNoOut()).collect(Collectors.toSet());
    }

    /**
     * 匹配备案序号：获取匹配关系列表
     * @param headId
     * @param facGNos
     * @return
     */
    public Map<String, List<SimpleInRelation> > getMatchRelation(String headId, List<String> facGNos) {
        int size = facGNos.size();
        List<SimpleInRelation> result = new ArrayList<>(size);
        int idx = 0;
        while(idx < size) {
            List<SimpleInRelation> tmp = sjgInRelationListMapper.getMatchRelation(headId,
                    facGNos.subList(idx, (idx+500)>size?size:(idx+500)));
            if(CollectionUtils.isNotEmpty(tmp)) {
                result.addAll(tmp);
            }
            idx += 500;
        }
        return result.stream().collect(Collectors.groupingBy(el -> el.getFacGNoIn()));
    }

    /**
     * 根据企业料号 获取备案关系列表
     * @param facGNoIns
     * @return
     */
    public List<SimpleInRelation> getRelations(String headId, List<String> facGNoIns) {
        return sjgInRelationListMapper.getMatchRelation(headId, facGNoIns);
    }
}
