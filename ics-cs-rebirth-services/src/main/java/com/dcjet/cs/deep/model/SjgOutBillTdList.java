package com.dcjet.cs.deep.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-11-20
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_SJG_OUT_BILL_TD_LIST")
public class SjgOutBillTdList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 转出企业料号
     */
	@Column(name = "FAC_G_NO_OUT")
	private  String facGNoOut;
	/**
     * 转出备案料号
     */
	@Column(name = "COP_G_NO_OUT")
	private  String copGNoOut;
	/**
     * 转出备案序号
     */
	@Column(name = "G_NO_OUT")
//	@JsonProperty("gNoOut")
	private  Integer gNoOut;
	/**
     * 数量
     */
	@Column(name = "DEC_QTY")
	private  BigDecimal decQty;
	/**
     * 单价
     */
	@Column(name = "DEC_PRICE")
	private  BigDecimal decPrice;
	/**
     * 总价
     */
	@Column(name = "DEC_TOTAL")
	private  BigDecimal decTotal;
	/**
     * 转入备案序号
     */
	@Column(name = "G_NO_IN")
//	@JsonProperty("gNoIn")
	private  Integer gNoIn;
	/**
     * 转入备案料号
     */
	@Column(name = "COP_G_NO_IN")
	private  String copGNoIn;
	/**
     * 申报计量单位
     */
	@Column(name = "UNIT")
	private  String unit;
	/**
     * 币制
     */
	@Column(name = "CURR")
	private  String curr;
	/**
     * 报关单序号
     */
	@Column(name = "ENTRY_G_NO")
	private  Integer entryGNo;
	/**
     * 深加工转出料件表头的SID
     */
	@Column(name = "HEAD_ID")
	private  String headId;
	/**
     * 创建人
     */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
     * 创建日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
     * 变更人
     */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
     * 变更日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 制单人姓名
     */
	@Column(name = "INSERT_USER_NAME")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 商品名称
     */
	@Column(name = "G_NAME")
//	@JsonProperty("gName")
	private  String gName;
	/**
     * 清单序号
     */
	@Column(name = "BILL_G_NO")
	private  Integer billGNo;
	/**
     * SO料号
     */
	@Column(name = "SO_NO")
	private  String soNo;
	/**
     * 客户编码
     */
	@Column(name = "SUPPLIER_CODE")
	private  String supplierCode;
	/**
     * 客户名称
     */
	@Column(name = "SUPPLIER_NAME")
	private  String supplierName;
	/**
     * 成本中心
     */
	@Column(name = "COST_CENTER")
	private  String costCenter;
	/**
     * 单耗版本号
     */
	@Column(name = "BOM_VERSION")
	private  String bomVersion;
	/**
	 * 拆分状态 0未拆分 1已拆分
	 */
	@Column(name = "SPLIT_STATUS")
	private  String splitStatus;
}
