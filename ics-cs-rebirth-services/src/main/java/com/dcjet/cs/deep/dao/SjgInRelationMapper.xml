<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.deep.dao.SjgInRelationMapper">
    <resultMap id="sjgRelationInResultMap" type="com.dcjet.cs.deep.model.SjgInRelation">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE_OUT" property="tradeCodeOut" jdbcType="VARCHAR"/>
        <result column="TRADE_NAME_OUT" property="tradeNameOut" jdbcType="VARCHAR"/>
        <result column="EMS_NO_OUT" property="emsNoOut" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE_IN" property="tradeCodeIn" jdbcType="VARCHAR"/>
        <result column="TRADE_NAME_IN" property="tradeNameIn" jdbcType="VARCHAR"/>
        <result column="EMS_NO_IN" property="emsNoIn" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="DATE"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="VALID_DATE_OUT" property="validDateOut" jdbcType="DATE"/>
        <result column="CUSTOMER_CODE_OUT" property="customerCodeOut" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,TRADE_CODE_OUT
     ,TRADE_NAME_OUT
     ,EMS_NO_OUT
     ,TRADE_CODE_IN
     ,TRADE_NAME_IN
     ,EMS_NO_IN
     ,STATUS
     ,INSERT_TIME
     ,INSERT_USER
     ,UPDATE_TIME
     ,UPDATE_USER
     ,VALID_DATE_OUT
     ,CUSTOMER_CODE_OUT
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
    </sql>
    <sql id="condition">
        and TRADE_CODE_IN = #{tradeCodeIn}
        <if test="tradeCodeOut != null and tradeCodeOut != ''">
            and TRADE_CODE_OUT = #{tradeCodeOut}
        </if>
        <if test="tradeNameOut != null and tradeNameOut != ''">
            and TRADE_NAME_OUT like '%'|| #{tradeNameOut} || '%'
        </if>
        <if test="emsNoOut != null and emsNoOut != ''">
            and EMS_NO_OUT = #{emsNoOut}
        </if>

        <if test="tradeNameIn != null and tradeNameIn != ''">
            and TRADE_NAME_IN like '%'|| #{tradeNameIn} || '%'
        </if>
        <if test="emsNoIn != null and emsNoIn != ''">
            and EMS_NO_IN = #{emsNoIn}
        </if>
        <if test="status != null and status != ''">
            and STATUS = #{status}
        </if>

        <if test='_databaseId == "postgresql" '>
            <if test="validDateOutFrom != null and validDateOutFrom != ''">
                <![CDATA[ and VALID_DATE_OUT >= to_timestamp(#{validDateOutFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="validDateOutTo != null and validDateOutTo != ''">
                <![CDATA[ and VALID_DATE_OUT <= to_timestamp(#{validDateOutTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="validDateOutFrom != null and validDateOutFrom != ''">
                <![CDATA[ and VALID_DATE_OUT >= TO_DATE(#{validDateOutFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="validDateOutTo != null and validDateOutTo != ''">
                <![CDATA[ and VALID_DATE_OUT < TO_DATE(#{validDateOutTo}, 'yyyy-MM-dd hh24:mi:ss')+1 ]]>
            </if>
        </if>

        <if test="customerCodeOut != null and customerCodeOut != ''">
            and CUSTOMER_CODE_OUT = #{customerCodeOut}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="sjgRelationInResultMap" parameterType="com.dcjet.cs.deep.model.SjgInRelation">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_SJG_IN_RELATION t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.sid
    </select>
    <select id="getByEmsNoOutAndIn" resultMap="sjgRelationInResultMap"  parameterType="com.dcjet.cs.deep.model.SjgInRelation">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_SJG_IN_RELATION t
        <where>
            <if test="tradeCodeIn != null and tradeCodeIn != ''">
                and T.TRADE_CODE_IN = #{tradeCodeIn}
            </if>
            <if test="emsNoOut != null and emsNoOut != ''">
                and EMS_NO_OUT = #{emsNoOut}
            </if>
            <if test="emsNoIn != null and emsNoIn != ''">
                and EMS_NO_IN = #{emsNoIn}
            </if>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_SJG_IN_RELATION t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getSelectList"  resultType="int" parameterType="map">
        SELECT COUNT(*) FROM T_SJG_IN_RELATION_LIST where HEAD_ID=#{sid} and TRADE_CODE_IN=#{tradeCodeIn}
    </select>


    <resultMap id="sjgRelationInHeadListResultMap" type="com.dcjet.cs.deep.model.SjgInRelationHeadList">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE_OUT" property="tradeCodeOut" jdbcType="VARCHAR"/>
        <result column="TRADE_NAME_OUT" property="tradeNameOut" jdbcType="VARCHAR"/>
        <result column="EMS_NO_OUT" property="emsNoOut" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE_IN" property="tradeCodeIn" jdbcType="VARCHAR"/>
        <result column="TRADE_NAME_IN" property="tradeNameIn" jdbcType="VARCHAR"/>
        <result column="EMS_NO_IN" property="emsNoIn" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="DATE"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="VALID_DATE_OUT" property="validDateOut" jdbcType="DATE"/>
        <result column="CUSTOMER_CODE_OUT" property="customerCodeOut" jdbcType="VARCHAR"/>

        <result column="HEAD_ID" property="headId" jdbcType="VARCHAR" />
        <result column="FAC_G_NO_OUT" property="facGNoOut" jdbcType="VARCHAR" />
        <result column="COP_G_NO_OUT" property="copGNoOut" jdbcType="VARCHAR" />
        <result column="G_NO_OUT" property="gNoOut" jdbcType="NUMERIC" />
        <result column="FAC_G_NO_IN" property="facGNoIn" jdbcType="VARCHAR" />
        <result column="COP_G_NO_IN" property="copGNoIn" jdbcType="VARCHAR" />
        <result column="G_NO_IN" property="gNoIn" jdbcType="NUMERIC" />
    </resultMap>

    <sql id="conditionHeadList">
        <if test="tradeCodeOut != null and tradeCodeOut != ''">
            and T.TRADE_CODE_OUT = #{tradeCodeOut}
        </if>
        <if test="tradeNameOut != null and tradeNameOut != ''">
            and T.TRADE_NAME_OUT like '%'|| #{tradeNameOut} || '%'
        </if>
        <if test="emsNoOut != null and emsNoOut != ''">
            and T.EMS_NO_OUT = #{emsNoOut}
        </if>
        <if test="tradeCodeIn != null and tradeCodeIn != ''">
            and T.TRADE_CODE_IN = #{tradeCodeIn}
        </if>
        <if test="tradeNameIn != null and tradeNameIn != ''">
            and T.TRADE_NAME_IN like '%'|| #{tradeNameIn} || '%'
        </if>
        <if test="emsNoIn != null and emsNoIn != ''">
            and T.EMS_NO_IN = #{emsNoIn}
        </if>
        <if test="status != null and status != ''">
            and T.STATUS = #{status}
        </if>
        <if test='_databaseId == "postgresql" '>
            <if test="validDateOutFrom != null and validDateOutFrom != ''">
                <![CDATA[ and T.VALID_DATE_OUT >= to_timestamp(#{validDateOutFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="validDateOutTo != null and validDateOutTo != ''">
                <![CDATA[ and T.VALID_DATE_OUT <= to_timestamp(#{validDateOutTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="validDateOutFrom != null and validDateOutFrom != ''">
                <![CDATA[ and T.VALID_DATE_OUT >= TO_DATE(#{validDateOutFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="validDateOutTo != null and validDateOutTo != ''">
                <![CDATA[ and T.VALID_DATE_OUT <= TO_DATE(#{validDateOutTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
            </if>
        </if>

        <if test="customerCodeOut != null and customerCodeOut != ''">
            and T.CUSTOMER_CODE_OUT = #{customerCodeOut}
        </if>

        <if test="headId != null and headId != ''">
            and OUT.HEAD_ID = #{headId}
        </if>
        <if test="facGNoOut != null and facGNoOut != ''">
            and OUT.FAC_G_NO_OUT = #{facGNoOut}
        </if>
        <if test="copGNoOut != null and copGNoOut != ''">
            and OUT.COP_G_NO_OUT like '%'|| #{copGNoOut} || '%'
        </if>
        <if test="gNoOut != null and gNoOut != ''">
            and OUT.G_NO_OUT = #{gNoOut}
        </if>
        <if test="facGNoIn != null and facGNoIn != ''">
            and OUT.FAC_G_NO_IN like '%'|| #{facGNoIn} || '%'
        </if>
        <if test="copGNoIn != null and copGNoIn != ''">
            and OUT.COP_G_NO_IN like '%'|| #{copGNoIn} || '%'
        </if>
        <if test="gNoIn != null and gNoIn != ''">
            and OUT.G_NO_IN = #{gNoIn}
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and t.INSERT_TIME >= to_date(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')-1 ]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and t.INSERT_TIME <= to_date(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss')+1 ]]>
        </if>
    </sql>

    <!-- 列表查询 and 条件 begin-->
    <select id="getHeadList" resultMap="sjgRelationInHeadListResultMap" parameterType="com.dcjet.cs.deep.model.SjgInRelationHeadList">
        SELECT
        T.SID,
        T.TRADE_CODE_OUT,
        T.TRADE_NAME_OUT,
        T.EMS_NO_OUT,
        T.TRADE_CODE_IN,
        T.TRADE_NAME_IN,
        T.EMS_NO_IN,
        T.STATUS,
        T.INSERT_TIME,
        T.INSERT_USER,
        T.UPDATE_TIME,
        T.UPDATE_USER,
        T.VALID_DATE_OUT,
        T.CUSTOMER_CODE_OUT,
        OUT.HEAD_ID,
        OUT.FAC_G_NO_OUT,
        OUT.COP_G_NO_OUT,
        OUT.G_NO_OUT,
        OUT.FAC_G_NO_IN,
        OUT.COP_G_NO_IN,
        OUT.G_NO_IN
        FROM
        T_SJG_IN_RELATION T LEFT JOIN T_SJG_IN_RELATION_LIST OUT ON T.SID = OUT.HEAD_ID
        <where>
            <include refid="conditionHeadList"></include>
        </where>
    </select>
    <select id="getSjgSupplier" resultType="java.util.Map" parameterType="java.lang.String">
        select distinct TRADE_CODE_OUT as "TRADE_CODE_OUT", TRADE_NAME_OUT as "TRADE_NAME_OUT"
        from T_SJG_IN_RELATION where TRADE_CODE_IN = #{tradeCodeIn}
    </select>
    <select id="getRelationList" resultType="java.util.Map" parameterType="java.lang.String">
        select SID as "SID", TRADE_CODE_OUT as "TRADE_CODE_OUT", TRADE_NAME_OUT as "TRADE_NAME_OUT", EMS_NO_OUT as "EMS_NO_OUT", EMS_NO_IN as "EMS_NO_IN"
        from T_SJG_IN_RELATION
        where TRADE_CODE_IN = #{tradeCodeIn}
    </select>


</mapper>
