package com.dcjet.cs.deep.model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-11-20
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_SJG_OUT_CHECK_LIST")
public class SjgOutCheckList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 唯一键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 单证编号
     */
	@Column(name = "BILL_NO")
	private  String billNo;
	/**
     * 序号
     */
	@Column(name = "LINE_NO")
	private  Integer lineNo;
	/**
     * 移动类型
     */
	@Column(name = "IE_TYPE")
	private  String ieType;
	/**
     * 关联编号
     */
	@Column(name = "LINKED_NO")
	private  String linkedNo;
	/**
     * SO号
     */
	@Column(name = "SO_NO")
	private  String soNo;
	/**
     * 发票号
     */
	@Column(name = "INVOICE_NO")
	private  String invoiceNo;
	/**
     * 交易日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "DELIVERY_DATE")
	private  Date deliveryDate;
	/**
     * 交易日期-开始
     */
	@Transient
	private String deliveryDateFrom;
	/**
     * 交易日期-结束
     */
	@Transient
    private String deliveryDateTo;
	/**
     * 企业料号
     */
	@Column(name = "FAC_G_NO")
	private  String facGNo;
	/**
     * 申报数量
     */
	@Column(name = "QTY")
	private  BigDecimal qty;
	/**
     * 申报单位
     */
	@Column(name = "UNIT")
	private  String unit;
	/**
     * 单价
     */
	@Column(name = "DEC_PRICE")
	private  BigDecimal decPrice;
	/**
     * 总价
     */
	@Column(name = "DEC_TOTAL")
	private  BigDecimal decTotal;
	/**
     * 转换后的币制
     */
	@Column(name = "CURR")
	private  String curr;
	/**
     * 转换后的保完税标记
     */
	@Column(name = "BOND_MARK")
	private  String bondMark;
	/**
     * 客户代码
     */
	@Column(name = "CUSTOMER_CODE")
	private  String customerCode;
	/**
	 * 客户联系人
	 */
	@Column(name = "CUSTOMER_LINK_MAN")
	private String customerLinkMan;
	/**
     * 买方地址
     */
	@Column(name = "BUY_ADDRESS")
	private  String buyAddress;
	/**
     * 转换后的出口方式
     */
	@Column(name = "OUT_WAY")
	private  String outWay;
	/**
     * BOM版本号
     */
	@Column(name = "BOM_VERSION")
	private  String bomVersion;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 转换后的物料类型（I 料件  E 成品）
     */
	@Column(name = "G_MARK")
//	@JsonProperty("gMark")
	private  String gMark;
	/**
     * 锁定标志（预留，暂不使用）
     */
	@Column(name = "LOCK_MARK")
	private  String lockMark;
	/**
     * 工厂代码
     */
	@Column(name = "FACTORY")
	private  String factory;
	/**
     * 对应ERP出入库表的SID
     */
	@Column(name = "BUSINESS_ID")
	private  String businessId;
	/**
     * 对账确认状态(0.未确认 1.已确认未提取 2.进口报关已提取)
     */
	@Column(name = "CONFIRM_STATUS")
	private  String confirmStatus;
	/**
     * 对账确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CONFIRM_TIME")
	private  Date confirmTime;
	/**
     * 对账确认时间-开始
     */
	@Transient
	private String confirmTimeFrom;
	/**
     * 对账确认时间-结束
     */
	@Transient
    private String confirmTimeTo;
	/**
     * 对账单编号
     */
	@Column(name = "CHECK_NO")
	private  String checkNo;
	/**
     * 库位别
     */
	@Column(name = "WAREHOUSE_NO")
	private  String warehouseNo;
	/**
     * 客户名称
     */
	@Column(name = "CUSTOMER_NAME")
	private  String customerName;

	/***
	 * 拆分序号
	 */
	@Column(name = "SPLIT_SERIAL_NO")
	private Integer splitSerialNo;

	/***
	 * 拆分ID
	 */
	@Column(name = "SPLIT_ID")
	private String splitId;

	/**
	 * 交易数量
	 */
	@Column(name = "QTY_ERP")
	private  BigDecimal qtyErp;
	/**
	 * 交易单位
	 */
	@Column(name = "UNIT_ERP")
	private  String unitErp;
	/**
	 * 转换标记
	 */
	@Column(name = "CONVERT_MARK")
	private String convertMark;

	/**
	 * 数据来源  1：界面新增  2：界面导入  3：接口提取
	 */
	@Column(name = "DATA_SOURCE")
	private String dataSource;
}
