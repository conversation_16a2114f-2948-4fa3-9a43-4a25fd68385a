package com.dcjet.cs.deep.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-10-29
 */
@Setter
@Getter
@Table(name = "T_SJG_OUT_RELATION")
public class SjgOutRelation implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * SID
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 转入方海关十位代码
     */
	@Column(name = "TRADE_CODE_IN")
	private  String tradeCodeIn;
	/**
     * 转入方名称
     */
	@Column(name = "TRADE_NAME_IN")
	private  String tradeNameIn;
	/**
     * 转入方手（账）册号
     */
	@Column(name = "EMS_NO_IN")
	private  String emsNoIn;
	/**
     * 转出方海关十位代码
     */
	@Column(name = "TRADE_CODE_OUT")
	private  String tradeCodeOut;
	/**
     * 转出方名称
     */
	@Column(name = "TRADE_NAME_OUT")
	private  String tradeNameOut;
	/**
     * 转出方手（账）册号
     */
	@Column(name = "EMS_NO_OUT")
	private  String emsNoOut;
	/**
     * 执行情况(0.停用 1.启用)
     */
	@Column(name = "STATUS")
	private  String status;
	/**
     * 转入方手（账）册号有效日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "VALID_DATE_IN")
	private  Date validDateIn;
	/**
     * 转入方手（账）册号有效日期-开始
     */
	@Transient
	private String validDateInFrom;
	/**
     * 转入方手（账）册号有效日期-结束
     */
	@Transient
    private String validDateInTo;
	/**
     * 转入方代码(客户代码)
     */
	@Column(name = "CUSTOMER_CODE_IN")
	private  String customerCodeIn;


	/**
	 * 创建人
	 */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
	 * 创建人
	 */
	@Column(name = "INSERT_USER_NAME")
	private  String insertUserName;
	/**
	 * 创建时间
	 */
	@Column(name = "INSERT_TIME")
	private Date insertTime;

	/**
	 * 更新人
	 */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
	 * 更新人
	 */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
	 * 更新时间
	 */
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
}
