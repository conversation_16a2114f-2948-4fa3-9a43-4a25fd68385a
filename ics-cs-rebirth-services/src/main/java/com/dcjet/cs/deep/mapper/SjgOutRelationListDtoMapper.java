package com.dcjet.cs.deep.mapper;

import com.dcjet.cs.deep.model.SjgOutRelationList;
import com.dcjet.cs.dto.deep.SjgOutRelationListDto;
import com.dcjet.cs.dto.deep.SjgOutRelationListParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-10-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SjgOutRelationListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    SjgOutRelationListDto toDto(SjgOutRelationList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    SjgOutRelationList toPo(SjgOutRelationListParam param);
    /**
     * 数据库原始数据更新
     * @param sjgOutRelationListParam
     * @param sjgOutRelationList
     */
    void updatePo(SjgOutRelationListParam sjgOutRelationListParam, @MappingTarget SjgOutRelationList sjgOutRelationList);
    default void patchPo(SjgOutRelationListParam sjgOutRelationListParam, SjgOutRelationList sjgOutRelationList) {
        // TODO 自行实现局部更新
    }
}
