package com.dcjet.cs.deep.dao;

import com.dcjet.cs.deep.model.SjgInRelationListTemp;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* SjgInRelationListTemp
* <AUTHOR>
* @date: 2019-10-29
*/
public interface SjgInRelationListTempMapper extends Mapper<SjgInRelationListTemp> {
    /**
     * 查询获取数据
     * @param sjgInRelationListTemp
     * @return
     */
    List<SjgInRelationListTemp> getList(SjgInRelationListTemp sjgInRelationListTemp);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 根据传入的数据类型获取相应数据
     *
     * @param param
     * @return 返回结果
     */
    List<SjgInRelationListTemp> selectByFlag(Map<String, Object> param);
}
