<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.deep.dao.SjgInBillListTempMapper">
    <resultMap id="sjgInBillListTempResultMap" type="com.dcjet.cs.deep.model.SjgInBillListTemp">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="FAC_G_NO_IN" property="facGNoIn" jdbcType="VARCHAR" />
		<result column="COP_G_NO_IN" property="copGNoIn" jdbcType="VARCHAR" />
		<result column="G_NO_IN" property="gNoIn" jdbcType="NUMERIC" />
		<result column="DEC_QTY" property="decQty" jdbcType="NUMERIC" />
		<result column="DEC_PRICE" property="decPrice" jdbcType="NUMERIC" />
		<result column="DEC_TOTAL" property="decTotal" jdbcType="NUMERIC" />
		<result column="G_NO_OUT" property="gNoOut" jdbcType="NUMERIC" />
		<result column="COP_G_NO_OUT" property="copGNoOut" jdbcType="VARCHAR" />
		<result column="UNIT" property="unit" jdbcType="VARCHAR" />
		<result column="CURR" property="curr" jdbcType="VARCHAR" />
		<result column="ENTRY_G_NO" property="entryGNo" jdbcType="NUMERIC" />
		<result column="MATCH_RESULT" property="matchResult" jdbcType="NUMERIC" />
		<result column="HEAD_ID" property="headId" jdbcType="VARCHAR" />
		<result column="SERIAL_NO" property="serialNo" jdbcType="NUMERIC" />
		<result column="ARRIVAL_DATE" property="arrivalDate" jdbcType="DATE" />
		<result column="PO_NO" property="poNo" jdbcType="VARCHAR" />
		<result column="SUPPLIER_CODE" property="supplierCode" jdbcType="VARCHAR" />
		<result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR" />
		<result column="LIST_NO_IO" property="listNoIo" jdbcType="VARCHAR" />
		<result column="LINE_NO" property="lineNo" jdbcType="NUMERIC" />
		<result column="COST_CENTER" property="costCenter" jdbcType="VARCHAR" />
		<result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR" />
		<result column="BUSINESS_ID" property="businessId" jdbcType="VARCHAR" />
		<result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
		<result column="INSERT_TIME" property="insertTime" jdbcType="DATE" />
		<result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="DATE" />
		<result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="UNIT_ERP" property="unitErp" jdbcType="VARCHAR" />
		<result column="TEMP_OWNER" property="tempOwner" jdbcType="VARCHAR" />
		<result column="TEMP_FLAG" property="tempFlag" jdbcType="NUMERIC" />
		<result column="TEMP_REMARK" property="tempRemark" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,FAC_G_NO_IN
     ,COP_G_NO_IN
     ,G_NO_IN
     ,DEC_QTY
     ,DEC_PRICE
     ,DEC_TOTAL
     ,G_NO_OUT
     ,COP_G_NO_OUT
     ,UNIT
     ,CURR
     ,ENTRY_G_NO
     ,MATCH_RESULT
     ,HEAD_ID
     ,SERIAL_NO
     ,ARRIVAL_DATE
     ,PO_NO
     ,SUPPLIER_CODE
     ,SUPPLIER_NAME
     ,LIST_NO_IO
     ,LINE_NO
     ,COST_CENTER
     ,DATA_SOURCE
     ,BUSINESS_ID
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
     ,UNIT_ERP
     ,TEMP_OWNER
     ,TEMP_FLAG
     ,TEMP_REMARK
    </sql>
    <sql id="condition">
    <if test="facGNoIn != null and facGNoIn != ''"> 
		and FAC_G_NO_IN = #{facGNoIn}
	</if>
    <if test="copGNoIn != null and copGNoIn != ''"> 
		and COP_G_NO_IN = #{copGNoIn}
	</if>
    <if test="gNoIn != null and gNoIn != ''"> 
		and G_NO_IN = #{gNoIn}
	</if>
    <if test="decQty != null and decQty != ''"> 
		and DEC_QTY = #{decQty}
	</if>
    <if test="decPrice != null and decPrice != ''"> 
		and DEC_PRICE = #{decPrice}
	</if>
    <if test="decTotal != null and decTotal != ''"> 
		and DEC_TOTAL = #{decTotal}
	</if>
    <if test="gNoOut != null and gNoOut != ''"> 
		and G_NO_OUT = #{gNoOut}
	</if>
    <if test="copGNoOut != null and copGNoOut != ''"> 
		and COP_G_NO_OUT = #{copGNoOut}
	</if>
    <if test="unit != null and unit != ''"> 
		and UNIT = #{unit}
	</if>
    <if test="curr != null and curr != ''"> 
		and CURR = #{curr}
	</if>
    <if test="entryGNo != null and entryGNo != ''"> 
		and ENTRY_G_NO = #{entryGNo}
	</if>
    <if test="matchResult != null and matchResult != ''"> 
		and MATCH_RESULT = #{matchResult}
	</if>
    <if test="headId != null and headId != ''"> 
		and HEAD_ID = #{headId}
	</if>
    <if test="serialNo != null and serialNo != ''"> 
		and SERIAL_NO = #{serialNo}
	</if>

	<if test='_databaseId == "postgresql" '>
		<if test="arrivalDateFrom != null and arrivalDateFrom != ''">
			<![CDATA[ and ARRIVAL_DATE >= to_timestamp(#{arrivalDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
		</if>
		<if test="arrivalDateTo != null and arrivalDateTo != ''">
			<![CDATA[ and ARRIVAL_DATE <= to_timestamp(#{arrivalDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
		</if>
	</if>
	<if test='_databaseId != "postgresql" '>
		<if test="arrivalDateFrom != null and arrivalDateFrom != ''">
			<![CDATA[ and ARRIVAL_DATE >= TO_DATE(#{arrivalDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
		</if>
		<if test="arrivalDateTo != null and arrivalDateTo != ''">
			<![CDATA[ and ARRIVAL_DATE < TO_DATE(#{arrivalDateTo}, 'yyyy-MM-dd hh24:mi:ss')+1]]>
		</if>
	</if>

    <if test="poNo != null and poNo != ''"> 
		and PO_NO = #{poNo}
	</if>
    <if test="supplierCode != null and supplierCode != ''"> 
		and SUPPLIER_CODE = #{supplierCode}
	</if>
    <if test="supplierName != null and supplierName != ''"> 
		and SUPPLIER_NAME = #{supplierName}
	</if>
    <if test="listNoIo != null and listNoIo != ''"> 
		and LIST_NO_IO = #{listNoIo}
	</if>
    <if test="lineNo != null and lineNo != ''"> 
		and LINE_NO = #{lineNo}
	</if>
    <if test="costCenter != null and costCenter != ''"> 
		and COST_CENTER = #{costCenter}
	</if>
    <if test="dataSource != null and dataSource != ''"> 
		and DATA_SOURCE = #{dataSource}
	</if>
    <if test="businessId != null and businessId != ''"> 
		and BUSINESS_ID = #{businessId}
	</if>
    <if test="insertUserName != null and insertUserName != ''"> 
		and INSERT_USER_NAME = #{insertUserName}
	</if>
    <if test="updateUserName != null and updateUserName != ''"> 
		and UPDATE_USER_NAME = #{updateUserName}
	</if>
    <if test="unitErp != null and unitErp != ''"> 
		and UNIT_ERP = #{unitErp}
	</if>
    <if test="tempOwner != null and tempOwner != ''"> 
		and TEMP_OWNER = #{tempOwner}
	</if>
    <if test="tempFlag != null and tempFlag != ''"> 
		and TEMP_FLAG = #{tempFlag}
	</if>
    <if test="tempRemark != null and tempRemark != ''"> 
		and TEMP_REMARK = #{tempRemark}
	</if>
    </sql>    
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="sjgInBillListTempResultMap" parameterType="com.dcjet.cs.deep.model.SjgInBillListTemp">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_SJG_IN_BILL_LIST_TEMP t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_SJG_IN_BILL_LIST_TEMP t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
	<select id="selectByFlag" resultMap="sjgInBillListTempResultMap" parameterType="map">
		select t.* from T_SJG_IN_BILL_LIST_TEMP t
		<where>
			and t.TEMP_OWNER = #{TEMP_OWNER,jdbcType=VARCHAR}
			AND t.TEMP_FLAG IN
			<foreach item="item" index="index" collection="TEMP_FLAG" open="(" separator="," close=")">
				#{item}
			</foreach>
		</where>
	</select>
</mapper>
