<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.translate.dao.GwstdTranslateConfigMapper">
    <resultMap id="gwstdTranslateConfigResultMap" type="com.dcjet.cs.translate.model.GwstdTranslateConfig">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="BILL_TYPE" property="billType" jdbcType="VARCHAR"/>
        <result column="BILL_NAME" property="billName" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="IS_ENABLED" property="isEnabled" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="DATE"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE"/>
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,BILL_TYPE
     ,BILL_NAME
     ,TRADE_CODE
     ,IS_ENABLED
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
    </sql>
    <sql id="condition">
        <if test="billType != null and billType != ''">
            and BILL_TYPE = #{billType}
        </if>
        <if test="billName != null and billName != ''">
            and BILL_NAME = #{billName}
        </if>
        and TRADE_CODE = #{tradeCode}
        <if test="isEnabled != null and isEnabled != ''">
            and IS_ENABLED = #{isEnabled}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwstdTranslateConfigResultMap"
            parameterType="com.dcjet.cs.translate.model.GwstdTranslateConfig">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_GWSTD_TRANSLATE_CONFIG t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_GWSTD_TRANSLATE_CONFIG t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <update id="updateList">
        update T_GWSTD_TRANSLATE_CONFIG set IS_ENABLED='0' where trade_code=#{tradeCode}
    </update>
</mapper>
