package com.dcjet.cs.repair.manager;

import com.dcjet.cs.repair.dao.RepairIEHeadMapper;
import com.dcjet.cs.repair.dao.RepairIEListMapper;
import com.dcjet.cs.repair.dao.RepairIHeadMapper;
import com.dcjet.cs.repair.dao.RepairIListMapper;
import com.dcjet.cs.repair.model.RepairIEHead;
import com.dcjet.cs.repair.model.RepairIEList;
import com.dcjet.cs.repair.model.RepairIHead;
import com.dcjet.cs.repair.model.RepairIList;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import javax.annotation.Resource;
import java.util.List;

@Component
public class RepairIEListManager {

    @Resource
    private RepairIEHeadMapper repairIEHeadMapper;

    @Resource
    private RepairIEListMapper repairIEListMapper;

    @Resource
    private RepairIHeadMapper repairIHeadMapper;

    @Resource
    private RepairIListMapper repairIListMapper;

    public List<RepairIEList> findByHeadId(String headId, UserInfoToken user) {
        Sqls sqls = Sqls.custom().andEqualTo("tradeCode", user.getCompany())
                .andEqualTo("headId", headId);
        List<RepairIEList> list = repairIEListMapper.selectByExample(Example.builder(RepairIEList.class).andWhere(sqls).build());
        return list;
    }

    public void deleteByIdList(List<String> sids, UserInfoToken user) {
        sids.forEach(it -> {
            RepairIEList ieList = repairIEListMapper.selectByPrimaryKey(it);
            if (ieList != null) {
                RepairIEHead head = repairIEHeadMapper.selectByPrimaryKey(ieList.getHeadId());
                if (RepairIEHead.STATUS_DEC.equals(head.getStatus())) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前单据已生成报关，禁止此操作"));
                }
                repairIEListMapper.deleteByPrimaryKey(it);
                updateIHeadStatus(ieList.getRepairListId(), user);
            }
        });
    }


    public void deleteByHeadId(String headId, UserInfoToken user) {
        List<RepairIEList> list = findByHeadId(headId, user);
        if (list.size() == 0) {
            return;
        }
        repairIEListMapper.deleteByHeadId(headId, user.getCompany());
        list.stream().forEach(it -> {
            updateIHeadStatus(it.getRepairListId(), user);
        });
    }

    private void updateIHeadStatus(String repairListId, UserInfoToken user) {
        RepairIList eList = repairIListMapper.selectByPrimaryKey(repairListId);
        if (eList == null) {
            return;
        }
        List<RepairIList> eListList = repairIListMapper.existsTake(eList.getHeadId(), user.getCompany());
        if (eListList.size() > 0) {
            return;
        }
        RepairIHead eHead = repairIHeadMapper.selectByPrimaryKey(eList.getHeadId());
        eHead.setStatus(RepairIHead.STATUS_WAIT);
        eHead.preUpdate(user);
        repairIHeadMapper.updateByPrimaryKey(eHead);
    }


}
