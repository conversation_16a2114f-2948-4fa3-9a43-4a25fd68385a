
package com.dcjet.cs.repair.service;

import com.dcjet.cs.dto.repair.*;
import com.dcjet.cs.repair.dao.RepairIEHeadMapper;
import com.dcjet.cs.repair.dao.RepairIEListMapper;
import com.dcjet.cs.repair.dao.RepairIListMapper;
import com.dcjet.cs.repair.manager.RepairIEListManager;
import com.dcjet.cs.repair.manager.RepairIHeadManager;
import com.dcjet.cs.repair.mapper.RepairIEListDtoMapper;
import com.dcjet.cs.repair.mapper.RepairIListDtoMapper;
import com.dcjet.cs.repair.model.RepairIEHead;
import com.dcjet.cs.repair.model.RepairIEList;
import com.dcjet.cs.repair.model.RepairIList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/***
 * 修理复运进境表体 service default implement
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@Service
public class RepairIEListService {

	@Resource
    private RepairIEListMapper repairIEListMapper;

	@Resource
    private RepairIEListDtoMapper repairIEListDtoMapper;

	@Resource
	private RepairIEHeadMapper repairIEHeadMapper;

	@Resource
	private RepairIEListManager repairIEListManager;

	@Resource
	private RepairIListMapper repairIListMapper;

	@Resource
	private RepairIListDtoMapper repairIListDtoMapper;

	@Resource
	private RepairIHeadManager repairIHeadManager;

	 /**
     * 获取分页信息
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    public ResultObject<List<RepairIEListDto>> list(RepairIEListSearchParam searchParam, PageParam pageParam, UserInfoToken token) {
        RepairIEList po = repairIEListDtoMapper.fromSearchParam(searchParam);
        po.setTradeCode(token.getCompany());
        Page<RepairIEList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> repairIEListMapper.getList(po));
        List<RepairIEListDto> dtoList = page.getResult().stream().map(head -> {
            RepairIEListDto dto = repairIEListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<RepairIEListDto>> paged = ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

	/***
     * 根据id获取详情
     *
     * @param sid
     * @param token
     * @return
     */
	public RepairIEListDto get(@Nonnull String sid, UserInfoToken token) {
        RepairIEList po = repairIEListMapper.selectByPrimaryKey(sid);
        return repairIEListDtoMapper.toDto(po);
    }

	 /**
     * 新增
     *
     * @param param
     * @param token
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RepairIEListDto insert(RepairIEListParam param, UserInfoToken token) {
        RepairIEList po = repairIEListDtoMapper.toPo(param);
		po.preInsert(token);
        int insertState = repairIEListMapper.insert(po);
        return  insertState > 0 ? repairIEListDtoMapper.toDto(po) : null;
    }


	/**
     * 修改
     *
     * @param param
     * @param token
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RepairIEListDto update(@Nonnull String sid, RepairIEListParam param, UserInfoToken token) {
        RepairIEList po = repairIEListMapper.selectByPrimaryKey(sid);
        if (po == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请选择有效的数据"));
        }

        repairIEListDtoMapper.updatePo(param, po);
        po.calculate();
        po.preUpdate(token);
        int updateState = repairIEListMapper.updateByPrimaryKey(po);
        return updateState > 0 ? repairIEListDtoMapper.toDto(po) : null;
	}

	/**
     * 批量删除
     *
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> sids, UserInfoToken userInfo) {
        repairIEListManager.deleteByIdList(sids,  userInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void extraction(RepairIEListExtractionParam param, UserInfoToken token) {
        RepairIEHead head = repairIEHeadMapper.selectByPrimaryKey(param.getHeadId());
        if (head == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数据非法"));
        }

        if (!head.canExtraction()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前单据已生成报关，禁止此操作"));
        }

        Map<String, RepairEIListExtractionDataParam> map = null;

        RepairIList queryParam = repairIListDtoMapper.fromEIListExtractionParam(param);
        queryParam.setTradeCode(token.getCompany());

        if (param.getData() != null && !param.getData().isEmpty()) {
            map = param.getData().stream().collect(Collectors.toMap(it -> it.getSid(), it -> it, (a, b) -> a));
            List<String> idList = new ArrayList<>(map.keySet());
            queryParam.setIdList(idList);
        }

        List<RepairIList> list = repairIListMapper.take(queryParam);
        if (list.size() == 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("未找到可以被提取的数据"));
        }
        final Map<String, RepairEIListExtractionDataParam> finalMap = map;
        List<RepairIEList> eiListList = list.stream().map(it -> {
            RepairIEList ieList = repairIEListDtoMapper.fromEList(it);
            if (finalMap != null && finalMap.containsKey(it.getSid())) {
                RepairEIListExtractionDataParam dataParam = finalMap.get(it.getSid());
                if (dataParam == null || dataParam.getExtractQty() == null) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("数量不能为空"));
                }
                if (dataParam.getExtractQty().compareTo(it.getRemainQty()) == 1) {
                    throw new ArgumentException(String.format(xdoi18n.XdoI18nUtil.t("企业料号")+":[%s],%s",it.getFacGNo(),xdoi18n.XdoI18nUtil.t("本次复运数量不能大于剩余数量")));
                }
                ieList.setQty(dataParam.getExtractQty());
                ieList.setRepairPrice(dataParam.getRepairPrice());
                ieList.calculate();
            } else {
                ieList.setQty(it.getRemainQty());
            }
            ieList.setHeadId(head.getSid());
            ieList.setRepairListId(it.getSid());
            ieList.preInsert(token);

            repairIHeadManager.updateEHeadStatusProgress(null, it.getHeadId(), token);
            return ieList;
        }).collect(Collectors.toList());
        repairIEListMapper.insertList(eiListList);

    }


}