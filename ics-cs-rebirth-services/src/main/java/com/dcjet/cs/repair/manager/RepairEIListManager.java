package com.dcjet.cs.repair.manager;

import com.dcjet.cs.repair.dao.RepairEHeadMapper;
import com.dcjet.cs.repair.dao.RepairEIHeadMapper;
import com.dcjet.cs.repair.dao.RepairEIListMapper;
import com.dcjet.cs.repair.dao.RepairEListMapper;
import com.dcjet.cs.repair.model.RepairEHead;
import com.dcjet.cs.repair.model.RepairEIHead;
import com.dcjet.cs.repair.model.RepairEIList;
import com.dcjet.cs.repair.model.RepairEList;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import javax.annotation.Resource;
import java.util.List;

@Component
public class RepairEIListManager {

    @Resource
    private RepairEIHeadMapper repairEIHeadMapper;

    @Resource
    private RepairEIListMapper repairEIListMapper;

    @Resource
    private RepairEHeadMapper repairEHeadMapper;

    @Resource
    private RepairEListMapper repairEListMapper;

    public List<RepairEIList> findByHeadId(String headId, UserInfoToken user) {
        Sqls sqls = Sqls.custom().andEqualTo("tradeCode", user.getCompany())
                .andEqualTo("headId", headId);
        List<RepairEIList> list = repairEIListMapper.selectByExample(Example.builder(RepairEIList.class).andWhere(sqls).build());
        return list;
    }

    public void deleteByIdList(List<String> sids, UserInfoToken user) {
        sids.forEach(it -> {
            RepairEIList eiList = repairEIListMapper.selectByPrimaryKey(it);
            if (eiList != null) {
                RepairEIHead head = repairEIHeadMapper.selectByPrimaryKey(eiList.getHeadId());
                if (RepairEIHead.STATUS_DEC.equals(head.getStatus())) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前单据已生成报关，禁止此操作"));
                }
                repairEIListMapper.deleteByPrimaryKey(it);
                updateEHeadStatus(eiList.getRepairListId(), user);
            }
        });
    }


    public void deleteByHeadId(String headId, UserInfoToken user) {
        List<RepairEIList> list = findByHeadId(headId, user);
        if (list.size() == 0) {
            return;
        }
        repairEIListMapper.deleteByHeadId(headId, user.getCompany());
        list.stream().forEach(it -> {
            updateEHeadStatus(it.getRepairListId(), user);
        });
    }

    private void updateEHeadStatus(String repairListId, UserInfoToken user) {
        RepairEList eList = repairEListMapper.selectByPrimaryKey(repairListId);
        if (eList == null) {
            return;
        }
        List<RepairEList> eListList = repairEListMapper.existsTake(eList.getHeadId(), user.getCompany());
        if (eListList.size() > 0) {
            return;
        }
        RepairEHead eHead = repairEHeadMapper.selectByPrimaryKey(eList.getHeadId());
        eHead.setStatus(RepairEHead.STATUS_WAIT);
        eHead.preUpdate(user);
        repairEHeadMapper.updateByPrimaryKey(eHead);
    }


}
