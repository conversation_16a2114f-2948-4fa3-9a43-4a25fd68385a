<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.repair.dao.RepairEIHeadMapper">
    <sql id="columns">
        t.SID,
		t.TRADE_CODE,
		t.STATUS,
		t.EMS_LIST_NO,
		t.ENTRY_NO,
		t.DECLARE_DATE,
		t.COMPLETE_DATE,
		t.NOTE,
		t.INSERT_USER,
		t.INSERT_USER_NAME,
		t.INSERT_TIME,
		t.UPDATE_USER,
		t.UPDATE_USER_NAME,
		t.UPDATE_TIME 
    </sql>
    <sql id="condition">
		<if test="tradeCode != null and tradeCode != ''">
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        </if>
		<if test="status != null and status != ''">
            and t.STATUS = #{status,jdbcType=VARCHAR}
        </if>
        <if test="emsListNo != null and emsListNo != ''">
            and t.EMS_LIST_NO like concat(concat('%', #{emsListNo}),'%')
        </if>
        <if test="entryNo != null and entryNo != ''">
            and t.ENTRY_NO like concat(concat('%', #{entryNo}),'%')
        </if>
        <if test="declareDateFrom != null">
            and <![CDATA[ t.DECLARE_DATE >= #{declareDateFrom} ]]>
        </if>
        <if test="declareDateTo != null">
            and <![CDATA[ t.DECLARE_DATE < to_char(to_date(#{declareDateTo}, 'yyyy-MM-dd hh24:mi:ss') + 1, 'yyyy-MM-dd') ]]>
        </if>
        <if test="completeDateFrom != null">
            and <![CDATA[ t.COMPLETE_DATE >= to_date(#{completeDateFrom}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="completeDateTo != null">
            and <![CDATA[ t.COMPLETE_DATE < to_date(#{completeDateTo}, 'yyyy-MM-dd hh24:mi:ss') + 1 ]]>
        </if>
		 
    </sql>  
    <select id="getList" parameterType="com.dcjet.cs.repair.model.RepairEIHead" resultType="com.dcjet.cs.repair.model.RepairEIHead">
        SELECT 
        <include refid="columns"/>
        FROM T_GWSTD_REPAIR_E_I_HEAD t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.INSERT_TIME
    </select>
    <select id="findByEHead" resultType="com.dcjet.cs.repair.model.RepairEIHead">
        SELECT * FROM T_GWSTD_REPAIR_E_I_HEAD h WHERE SID IN (
            SELECT l.HEAD_ID FROM T_GWSTD_REPAIR_E_I_LIST l
            WHERE REPAIR_LIST_ID IN (SELECT SID FROM T_GWSTD_REPAIR_E_LIST WHERE HEAD_ID = #{eHeadId}) GROUP BY l.HEAD_ID )
        AND h.STATUS = '1'
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_GWSTD_REPAIR_E_I_HEAD t where t.SID in
        <foreach collection="sids"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        and t.trade_code = #{tradeCode}
    </delete>
</mapper>