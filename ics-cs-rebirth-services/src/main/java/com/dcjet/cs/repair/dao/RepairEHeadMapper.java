package com.dcjet.cs.repair.dao;

import com.dcjet.cs.base.repository.InsertBatchMapper;
import com.dcjet.cs.dto.repair.RepairEHeadSearchParam;
import com.dcjet.cs.dto.repair.RepairWarningDto;
import com.dcjet.cs.dto.repair.RepairWarningSearchParam;
import com.dcjet.cs.repair.model.RepairEHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/***
 * 出境修理表头
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
public interface RepairEHeadMapper extends Mapper<RepairEHead>, InsertBatchMapper<RepairEHead> {

    /**
     * 查询获取数据
     * @param param
     * @return
     */
    List<RepairEHead> getList(RepairEHeadSearchParam param);


    /***
     * 获取新的没有拉去过的数据
     *
     * @param tradeCode
     * @return
     */
    List<RepairEHead> fetch(@Param("tradeCode") String tradeCode);

    /***
     * 根据复运获取表头
     *
     * @param eiHeadId
     * @return
     */
    List<RepairEHead> findByEIHeadId(@Param("eiHeadId") String eiHeadId);


    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("sids") List<String> sids, @Param("tradeCode") String tradeCode);

    /***
     * 获取预警列表
     *
     * @return
     */
    List<RepairWarningDto> findWarning(RepairWarningSearchParam param);

} 