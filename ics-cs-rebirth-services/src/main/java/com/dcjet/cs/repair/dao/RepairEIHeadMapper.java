package com.dcjet.cs.repair.dao;

import com.dcjet.cs.repair.model.RepairEIHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/***
 * 修理复运进境表头
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
public interface RepairEIHeadMapper extends Mapper<RepairEIHead> {

    /**
     * 查询获取数据
     * @param param
     * @return
     */
    List<RepairEIHead> getList(RepairEIHead param);

    /***
     *
     *
     * @param eHeadId
     * @return
     */
    List<RepairEIHead> findByEHead(@Param("eHeadId") String eHeadId);


    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("sids") List<String> sids, @Param("tradeCode") String tradeCode);

} 