package com.dcjet.cs.repair.dao;

import com.dcjet.cs.repair.model.RepairIEHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/***
 * 修理复运进境表头
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
public interface RepairIEHeadMapper extends Mapper<RepairIEHead> {

    /**
     * 查询获取数据
     * @param param
     * @return
     */
    List<RepairIEHead> getList(RepairIEHead param);

    /***
     *
     *
     * @param iHeadId
     * @return
     */
    List<RepairIEHead> findByIHead(@Param("iHeadId") String iHeadId);


    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("sids") List<String> sids, @Param("tradeCode") String tradeCode);

} 