package com.dcjet.cs.repair.event;

import com.dcjet.cs.base.event.CRUDEnum;
import com.dcjet.cs.erp.event.DecErpEHeadEvent;
import com.dcjet.cs.erp.event.DecErpIHeadEvent;
import com.dcjet.cs.repair.service.RepairEIHeadService;
import com.dcjet.cs.repair.service.RepairIEHeadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

@Component
public class RepairEventListener {
    private static final Logger log = LoggerFactory.getLogger(RepairEventListener.class);

    @Resource
    private RepairEIHeadService repairEIHeadService;

    @Resource
    private RepairIEHeadService repairIEHeadService;

    @TransactionalEventListener(value = DecErpIHeadEvent.class)
    public void onDecErpIHeadEvent(DecErpIHeadEvent event) {
        if (log.isDebugEnabled()) {
            log.debug("监听到DecErpIHeadEvent事件 {}", event);
        }
        if (event.getCrud() == CRUDEnum.DELETE) {
            event.validation();

            repairEIHeadService.updateStateByDecErpDelete(event.getDecErpIHeadN(), event.getToken());
        }
    }


    @TransactionalEventListener(value = DecErpEHeadEvent.class)
    public void onDecErpEHeadEvent(DecErpEHeadEvent event) {
        if (log.isDebugEnabled()) {
            log.debug("监听到DecErpEHeadEvent事件 {}", event);
        }
        if (event.getCrud() == CRUDEnum.DELETE) {
            event.validation();

            repairIEHeadService.updateStateByDecErpDelete(event.getDecErpEHeadN(), event.getToken());
        }
    }






}
