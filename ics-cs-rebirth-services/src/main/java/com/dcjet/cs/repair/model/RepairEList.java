package com.dcjet.cs.repair.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 出境修理表体
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@Setter @Getter
@Table(name = "T_GWSTD_REPAIR_E_LIST")
public class RepairEList extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    
    /**
     * 表头主键
     */
    @Column(name = "HEAD_ID")
    private String headId;

    /**
     * 提单表体SID
     */
    @Column(name = "DEC_ERP_LIST_ID")
    private String decErpListId;
    
    /**
     * 物料类型
     */
    @Column(name = "G_MARK")
    private String gMark;
    
    /**
     * 企业料号
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;
    
    /**
     * 商品编码
     */
    @Column(name = "CODE_T_S")
    private String codeTS;
    
    /**
     * 商品名称
     */
    @Column(name = "G_NAME")
    private String gName;
    
    /**
     * 申报数量
     */
    @Column(name = "QTY")
    private BigDecimal qty;
    
    /**
     * 申报单价
     */
    @Column(name = "DEC_PRICE")
    private BigDecimal decPrice;
    
    /**
     * 申报总价
     */
    @Column(name = "DEC_TOTAL")
    private BigDecimal decTotal;
    
    /**
     * 规格型号
     */
    @Column(name = "G_MODEL")
    private String gModel;
    
    /**
     * 计量单位
     */
    @Column(name = "UNIT")
    private String unit;
    
    /**
     * 币制
     */
    @Column(name = "CURR")
    private String curr;

    @Transient
    private String emsListNo;

    @Transient
    private String entryNo;

    @Transient
    private String declareDate;

    /***
     * 剩余数量
     */
    @Transient
    private BigDecimal remainQty;

    @Transient
    private List<String> idList;

}