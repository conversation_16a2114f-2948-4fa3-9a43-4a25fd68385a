package com.dcjet.cs.config;

import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceResolvable;
import org.springframework.context.NoSuchMessageException;
import xdoi18n.XdoI18nUtil;

import java.util.Locale;

public class TranslateMessageSource implements MessageSource {
    @Override
    public String getMessage(String code, Object[] args, String defaultMessage, Locale locale) {
        return XdoI18nUtil.t(code);
    }

    @Override
    public String getMessage(String code, Object[] args, Locale locale) throws NoSuchMessageException {
        return XdoI18nUtil.t(code);
    }

    @Override
    public String getMessage(MessageSourceResolvable resolvable, Locale locale) throws NoSuchMessageException {
        return XdoI18nUtil.t(resolvable.getDefaultMessage());
    }
}
