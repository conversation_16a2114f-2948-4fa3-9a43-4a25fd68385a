package com.dcjet.cs.report.dao;

import com.dcjet.cs.report.model.VDecEBill;
import com.dcjet.cs.report.model.VDecSumModel;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate dcits
* 
* @author: 沈振宇
* @date: 2019-03-11
*/
public interface VDecEBillMapper extends Mapper<VDecEBill> {

    /**
     * 查询获取数据
     * @param vDecEBill
     * @return
     */
    List<VDecEBill> getList(VDecEBill vDecEBill);

    /**
     * 查询获取数据
     * @param vDecEBill
     * @return
     */
    List<VDecEBill> getHeadList(VDecEBill vDecEBill);
    /**
     * 功能描述:汇总净重、申报数量、申报总价
     *
     * @param vDecEBill
     * @return
     */
    VDecSumModel getListSum(VDecEBill vDecEBill);

    /**
     * 汇总表头总净重、总毛重
     * @param vDecEBill
     * @return
     */
    VDecSumModel getHeadSum(VDecEBill vDecEBill);

    Integer selectDataCount(VDecEBill vDecEBill);

    Integer selectDataCountAll(VDecEBill vDecEBill);

    int deleteReportBillEHead(String tradeCode);

    int insertReportBillEHead(String tradeCode);

    int deleteReportBillEHeadList(String tradeCode);

    int insertReportBillEHeadList(String tradeCode);
}
