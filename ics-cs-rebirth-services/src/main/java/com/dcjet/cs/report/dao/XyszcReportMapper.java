package com.dcjet.cs.report.dao;

import com.dcjet.cs.report.model.XyszcReport;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* XyszcReport
* <AUTHOR>
* @date: 2020-3-13
*/
public interface XyszcReportMapper extends Mapper<XyszcReport> {
    /**
     * 查询获取数据
     * @param xyszcReport
     * @return
     */
    List<XyszcReport> getList(XyszcReport xyszcReport);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 根据企业编码删除数据
     */
    int deleteByTradeCode(@Param("tradeCode") String tradeCode);
}
