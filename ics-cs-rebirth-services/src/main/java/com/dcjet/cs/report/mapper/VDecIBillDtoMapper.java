package com.dcjet.cs.report.mapper;

import com.dcjet.cs.dto.report.VDecIBillDto;
import com.dcjet.cs.dto.report.VDecIBillHeadDto;
import com.dcjet.cs.dto.report.VDecIBillHeadParam;
import com.dcjet.cs.dto.report.VDecIBillParam;
import com.dcjet.cs.report.model.VDecIBill;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate dcits
 *
 * @author: 沈振宇
 * @date: 2019-03-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VDecIBillDtoMapper {

    /**
     * 转换DTO到数据库对象
     * @param po
     * @return
     */
    VDecIBillDto toDto(VDecIBill po);

    /**
     * 转换Param到数据库对象
     * @param param
     * @return
     */
    VDecIBill toPo(VDecIBillParam param);

    /**
     * 杞崲鏁版嵁搴撳璞″埌DTO
     * @param po
     * @return
     */
    VDecIBillHeadDto toHeadDto(VDecIBill po);

    /**
     * 杞崲鏁版嵁搴撳璞″埌DTO
     * @param po
     * @return
     */
    VDecIBill toHeadPo(VDecIBillHeadParam po);

}
