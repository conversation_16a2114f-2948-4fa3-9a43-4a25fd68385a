package com.dcjet.cs.report.service;

import com.dcjet.cs.dto.report.VWfReceiveHeadListDto;
import com.dcjet.cs.dto.report.VWfReceiveHeadListParam;
import com.dcjet.cs.outward.dao.WfReceiveListMapper;
import com.dcjet.cs.outward.model.WfReceiveHead;
import com.dcjet.cs.report.dao.VWfReceiveHeadListMapper;
import com.dcjet.cs.report.mapper.VWfReceiveHeadListDtoMapper;
import com.dcjet.cs.report.model.VWfReceiveHeadList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-7-18
 */
@Service
public class VWfReceiveHeadListService extends BaseService<VWfReceiveHeadList> {
    @Resource
    private VWfReceiveHeadListMapper vWfReceiveHeadListMapper;
    @Resource
    private VWfReceiveHeadListDtoMapper vWfReceiveHeadListDtoMapper;

    @Override
    public Mapper<VWfReceiveHeadList> getMapper() {
        return vWfReceiveHeadListMapper;
    }

    /**
     * 获取分页信息
     *
     * @param vWfReceiveHeadListParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<VWfReceiveHeadListDto>> getHeadListPaged(VWfReceiveHeadListParam vWfReceiveHeadListParam, PageParam pageParam) {
        // 启用分页查询
        VWfReceiveHeadList vWfReceiveHeadList = vWfReceiveHeadListDtoMapper.toPo(vWfReceiveHeadListParam);
        Page<VWfReceiveHeadList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vWfReceiveHeadListMapper.getHeadList(vWfReceiveHeadList));
        List<VWfReceiveHeadListDto> vWfReceiveHeadListDtos = page.getResult().stream().map(head -> {
            VWfReceiveHeadListDto dto = vWfReceiveHeadListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<VWfReceiveHeadListDto>> paged = ResultObject.createInstance(vWfReceiveHeadListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<VWfReceiveHeadListDto> selectHeadListAll(VWfReceiveHeadListParam exportParam, UserInfoToken userInfo) {
        VWfReceiveHeadList vWfReceiveHeadList = vWfReceiveHeadListDtoMapper.toPo(exportParam);
        vWfReceiveHeadList.setTradeCode(userInfo.getCompany());
        List<VWfReceiveHeadListDto> vWfReceiveHeadListDtos = new ArrayList<>();
        List<VWfReceiveHeadList> vWfReceiveHeadLists = vWfReceiveHeadListMapper.getHeadList(vWfReceiveHeadList);
        if (CollectionUtils.isNotEmpty(vWfReceiveHeadLists)) {
            vWfReceiveHeadListDtos = vWfReceiveHeadLists.stream().map(head -> {
                VWfReceiveHeadListDto dto = vWfReceiveHeadListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return vWfReceiveHeadListDtos;
    }

}
