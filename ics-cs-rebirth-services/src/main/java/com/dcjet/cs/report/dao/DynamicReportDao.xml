<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.report.dao.DynamicReportDao">

    <select id="listVO" resultType="com.dcjet.cs.report.vo.DynamicReportListVO">
        select SID,REPORT_NAME,REMARK
        from T_DYNAMIC_REPORT
        where TRADE_CODE = #{tradeCode}
        <if test="reportName != null and reportName != ''">
            and REPORT_NAME like concat(concat('%',#{reportName}),'%')
        </if>
        order by ORDER_NO
    </select>

    <select id="getSql" resultType="java.lang.String">
        select SQL from T_DYNAMIC_REPORT where SID = #{reportId}
    </select>

    <select id="listAll" resultType="com.dcjet.cs.report.model.DynamicReport">
        select SID,SQL
        from T_DYNAMIC_REPORT
    </select>
</mapper>