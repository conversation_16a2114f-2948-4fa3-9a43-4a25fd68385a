package com.dcjet.cs.report.model;

import com.dcjet.cs.base.model.BasicModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-03-14
 */
@Setter
@Getter
@RemoveTailingZero
public class VDecIEntry extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 关检关联号
     */
    @Column(name = "COP_NO")
    private  String copNo;
    /**
     * 申报单位代码
     */
    @Column(name = "DECLARE_CODE")
    private  String declareCode;
    /**
     * 申报企业名称
     */
    @Column(name = "DECLARE_NAME")
    private  String declareName;
    /**
     * 批准文号
     */
    @Column(name = "APPR_NO")
    private  String apprNo;
    /**
     * 清单内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private  String emsListNo;
    /**
     * 合同号
     */
    @Column(name = "CONTR_NO")
    private  String contrNo;
    /**
     * 录入单位编号
     */
    @Column(name = "INPUT_CODE")
    private  String inputCode;
    /**
     * 录入单位名称
     */
    @Column(name = "INPUT_NAME")
    private  String inputName;
    /**
     * 主管海关（申报地海关）
     */
    @Column(name = "MASTER_CUSTOMS")
    private  String masterCustoms;
    /**
     * 征免性质
     */
    @Column(name = "CUT_MODE")
    private  String cutMode;
    /**
     * 数据来源
     */
    @Column(name = "DATA_SOURCE")
    private  String dataSource;
    /**
     * 报关/转关关系标志
     */
    @Column(name = "DECL_TRNREL")
    private  String declTrnrel;
    /**
     * 经停港/指运港
     */
    @Column(name = "DEST_PORT")
    private  String destPort;
    /**
     * 报关标志
     */
    @Column(name = "DCLCUS_MARK")
    private  String dclcusMark;
    /**
     * 报关单号
     */
    @Column(name = "ENTRY_NO")
    private  String entryNo;
    /**
     * 报关单类型
     */
    @Column(name = "ENTRY_TYPE")
    private  String entryType;
    /**
     * 运费币制
     */
    @Column(name = "FEE_CURR")
    private  String feeCurr;
    /**
     * 运费标记
     */
    @Column(name = "FEE_MARK")
    private  String feeMark;
    /**
     * 运费／率
     */
    @Column(name = "FEE_RATE")
    private  BigDecimal feeRate;
    /**
     * 毛重
     */
    @Column(name = "GROSS_WT")
    private  BigDecimal grossWt;
    /**
     * 进出口日期
     */
    @Column(name = "I_E_DATE")
    private  Date IEDate;
    /**
     * 进出口日期-开始
     */
    @Transient
    private String IEDateFrom;
    /**
     * 进出口日期-结束
     */
    @Transient
    private String IEDateTo;
    /**
     * 进出口标志
     */
    @Column(name = "I_E_MARK")
    private  String IEMark;
    /**
     * 进出口岸
     */
    @Column(name = "I_E_PORT")
    private  String IEPort;
    /**
     * 录入员名称
     */
    @Column(name = "INPUTER_NAME")
    private  String inputerName;
    /**
     * 保险费币制
     */
    @Column(name = "INSUR_CURR")
    private  String insurCurr;
    /**
     * 保险费标记
     */
    @Column(name = "INSUR_MARK")
    private  String insurMark;
    /**
     * 保险费／率
     */
    @Column(name = "INSUR_RATE")
    private BigDecimal insurRate;
    /**
     * 许可证编号
     */
    @Column(name = "LICENSE_NO")
    private  String licenseNo;
    /**
     * 账册编号
     */
    @Column(name = "EMS_NO")
    private  String emsNo;
    /**
     * 净重
     */
    @Column(name = "NET_WT")
    private  BigDecimal netWt;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private  String note;
    /**
     * 杂费币制
     */
    @Column(name = "OTHER_CURR")
    private  String otherCurr;
    /**
     * 杂费标志
     */
    @Column(name = "OTHER_MARK")
    private  String otherMark;
    /**
     * 杂费率
     */
    @Column(name = "OTHER_RATE")
    private  BigDecimal otherRate;
    /**
     * 消费使用/生产销售 单位代码
     */
    @Column(name = "OWNER_CODE")
    private  String ownerCode;
    /**
     * 消费使用/生产销售 单位名称
     */
    @Column(name = "OWNER_NAME")
    private  String ownerName;
    /**
     * 件数
     */
    @Column(name = "PACK_NUM")
    private  Integer packNum;
    /**
     * 申报人标识
     */
    @Column(name = "PARTENER_ID")
    private  String partenerId;
    /**
     * 打印日期
     */
    @Column(name = "PRINT_DATE")
    private  Date printDate;
    /**
     * 打印日期-开始
     */
    @Transient
    private String printDateFrom;
    /**
     * 打印日期-结束
     */
    @Transient
    private String printDateTo;
    /**
     * 预录入编号
     */
    @Column(name = "PRE_ENTRY_NO")
    private  String preEntryNo;
    /**
     * 风险参数
     */
    @Column(name = "RISK")
    private  String risk;
    /**
     * 报关单统一编号
     */
    @Column(name = "SEQ_NO")
    private  String seqNo;
    /**
     * 通关申请单号
     */
    @Column(name = "TGD_NO")
    private  String tgdNo;
    /**
     * 启运国/运抵国
     */
    @Column(name = "TRADE_COUNTRY")
    private  String tradeCountry;
    /**
     * 监管方式
     */
    @Column(name = "TRADE_MODE")
    private  String tradeMode;
    /**
     * 境内收发货人编号
     */
    @Column(name = "TRADE_CODE")
    private  String tradeCode;
    /**
     * 运输方式代码
     */
    @Column(name = "TRAF_MODE")
    private  String trafMode;
    /**
     * 运输工具代码及名称
     */
    @Column(name = "TRAF_NAME")
    private  String trafName;
    /**
     * 境内收发货人名称
     */
    @Column(name = "TRADE_NAME")
    private  String tradeName;
    /**
     * 成交方式
     */
    @Column(name = "TRANS_MODE")
    private  String transMode;
    /**
     * 单据类型
     */
    @Column(name = "LIST_TYPE")
    private  String listType;
    /**
     * 录入员 IC 卡号
     */
    @Column(name = "TYPIST_NO")
    private  String typistNo;
    /**
     * 包装种类
     */
    @Column(name = "WRAP_TYPE")
    private  String wrapType;
    /**
     * 包装种类
     */
    @Column(name = "WRAP_TYPE2")
    private  String wrapType2;
    /**
     * 担保验放标志
     */
    @Column(name = "CHK_SURETY")
    private  String chkSurety;
    /**
     * 备案清单类型
     */
    @Column(name = "BILL_TYPE")
    private  String billType;
    /**
     * 录入单位社会信用代码
     */
    @Column(name = "INPUT_CREDIT_CODE")
    private  String inputCreditCode;
    /**
     * 收发货人社会信用代码
     */
    @Column(name = "TRADE_CREDIT_CODE")
    private  String tradeCreditCode;
    /**
     * 申报单位社会信用代码
     */
    @Column(name = "DECLARE_CREDIT_CODE")
    private  String declareCreditCode;
    /**
     * 消费使用/生产销售单位单位社会信用代码
     */
    @Column(name = "OWNER_CREDIT_CODE")
    private  String ownerCreditCode;
    /**
     * 价格说明
     */
    @Column(name = "PROMISE_ITEMS")
    private  String promiseItems;
    /**
     * 贸易国别
     */
    @Column(name = "TRADE_NATION")
    private  String tradeNation;
    /**
     * 查验分流
     */
    @Column(name = "CHECK_FLOW")
    private  String checkFlow;
    /**
     * 税收征管标记
     */
    @Column(name = "TAX_AAMIN_MARK")
    private  String taxAaminMark;
    /**
     * 标记唛码
     */
    @Column(name = "MARK_NO")
    private  String markNo;
    /**
     * 启运港代码
     */
    @Column(name = "DESP_PORT")
    private  String despPort;
    /**
     * 入境口岸代码
     */
    @Column(name = "ENTRY_PORT")
    private  String entryPort;
    /**
     * 货物存放地点
     */
    @Column(name = "WAREHOUSE")
    private  String warehouse;
    /**
     * B/L 号
     */
    @Column(name = "BL_NO")
    private  String blNo;
    /**
     * 口岸检验检疫机关
     */
    @Column(name = "INSPORG_CODE")
    private  String insporgCode;
    /**
     * 特种业务标识
     */
    @Column(name = "SPECDECL_FLAG")
    private  String specdeclFlag;
    /**
     * 目的地检验检疫机关
     */
    @Column(name = "PURPORG_CODE")
    private  String purporgCode;
    /**
     * 启运日期
     */
    @Column(name = "DESP_DATE")
    private  String despDate;
    /**
     * 卸毕日期
     */
    @Column(name = "CMPLDSCHRG_DT")
    private  String cmpldschrgDt;
    /**
     * 关联理由
     */
    @Column(name = "CORRELATION_REASONFLAG")
    private  String correlationReasonflag;
    /**
     * 领证机关
     */
    @Column(name = "VSAORG_CODE")
    private  String vsaorgCode;
    /**
     * 原集装箱标识
     */
    @Column(name = "ORIGBOX_FLAG")
    private  String origboxFlag;
    /**
     * 申报人员姓名
     */
    @Column(name = "DECLARER_NAME")
    private  String declarerName;
    /**
     * 有无其他包装
     */
    @Column(name = "NOOTHER_PACK")
    private  String nootherPack;
    /**
     * 检验检疫受理机关
     */
    @Column(name = "ORG_CODE")
    private  String orgCode;
    /**
     * 境外收发货人代码
     */
    @Column(name = "OVERSEAS_SHIPPER")
    private  String overseasShipper;
    /**
     * 境外发货人名称
     */
    @Column(name = "OVERSEAS_SHIPPER_NAME")
    private  String overseasShipperName;
    /**
     * 境外发货人名称 （外文）
     */
    @Column(name = "OVERSEAS_SHIPPER_ENAME")
    private  String overseasShipperEname;
    /**
     * 境外收发货人地址
     */
    @Column(name = "OVERSEAS_SHIPPER_ADDR")
    private  String overseasShipperAddr;
    /**
     * 境外收货人编码
     */
    @Column(name = "OVERSEASCONSIGNEE_CODE")
    private  String overseasconsigneeCode;
    /**
     * 境外收货人名称 (外文）
     */
    @Column(name = "OVERSEASCONSIGNEE_ENAME")
    private  String overseasconsigneeEname;
    /**
     * 境内收发货人名称 （外文）
     */
    @Column(name = "TRADE_ENAME")
    private  String tradeEname;
    /**
     * 关联号码
     */
    @Column(name = "CORRELATION_NO")
    private  String correlationNo;
    /**
     * EDI 申报备注
     */
    @Column(name = "EDI_REMARK")
    private  String ediRemark;
    /**
     * EDI 申报备注2
     */
    @Column(name = "EDI_REMARK2")
    private  String ediRemark2;
    /**
     * 境内收发货人检验 检疫编码
     */
    @Column(name = "TRADE_CIQ_CODE")
    private  String tradeCiqCode;
    /**
     * 消费使用/生产销 售单位检验检疫编 码
     */
    @Column(name = "OWNER_CIQ_CODE")
    private  String ownerCiqCode;
    /**
     * 申报单位检验检疫编码
     */
    @Column(name = "DECL_CIQ_CODE")
    private  String declCiqCode;
    /**
     * 申报日期
     */
    @Column(name = "D_DATE")
    private  String DDate;
    @Transient
    private String DDateFrom;

    @Transient
    private String DDateTo;
    /**
     * 航次号
     */
    @Column(name = "VOYAGE_NO")
    private  String voyageNo;
    /**
     * 报关单状态编码
     */
    @Column(name = "STATUS")
    private  String status;

    /**
     * 创建人
     */
    @Transient
    private  String entryInsertUser;

    /**
     * 提运单号
     */
    @Column(name = "HAWB")
    private  String hawb;
    /**
     * 提单表头id
     */
    @Column(name = "ERP_HEAD_ID")
    private  String erpHeadId;
    /**
     * 保税标识 0-保税 1-非保税
     */
    @Column(name = "BOND_MARK")
    private  String bondMark;
    /**
     * 核销周期
     */
    @Column(name = "DCR_CYCLE")
    private  String dcrCycle;
    /**
     * 立交桥编号
     */
    @Column(name = "LJQ_NO")
    private  String ljqNo;
    /**
     * -1 未发送 0 发送失败  1发送成功 2发送中
     */
    @Column(name = "SEND_API_STATUS")
    private  String sendApiStatus;
    /**
     * 发送api任务id
     */
    @Column(name = "SEND_API_TASK_ID")
    private  String sendApiTaskId;
    /**
     * 发送api错误标志 报关金二审核结果（一般贸易使用） 0 正常 1 错误
     */
    @Column(name = "SEND_API_ERROR_MARK")
    private  String sendApiErrorMark;
    /**
     * 清单表头id
     */
    @Column(name = "BILL_HEAD_ID")
    private  String billHeadId;

    /**
     * 创建日期-开始
     */
    @Transient
    private String insertTimeFrom;
    /**
     * 创建日期-结束
     */
    @Transient
    private String insertTimeTo;
    /**
     * 归类标志
     */
    @Column(name = "CLASS_MARK")
    private  String classMark;
    /**
     * 商品编码
     */
    @Column(name = "CODE_T_S")
    private  String codeTS;
    /**
     * 备案序号
     */
    @Column(name = "G_NO")
    private  BigDecimal GNo;
    /**
     * 企业申报单价
     */
    @Column(name = "DEC_PRICE")
    private  BigDecimal decPrice;
    /**
     * 企业申报总价
     */
    @Column(name = "DEC_TOTAL")
    private  BigDecimal decTotal;
    /**
     * 征免方式
     */
    @Column(name = "DUTY_MODE")
    private  String dutyMode;
    /**
     * 货号
     */
    @Column(name = "COP_G_NO")
    private  String copGNo;
    /**
     * 版本号
     */
    @Column(name = "EXG_VERSION")
    private  String exgVersion;
    /**
     * 申报计量单位与 法定单位比例因 子
     */
    @Column(name = "FACTOR_1")
    private  BigDecimal factor1;
    /**
     * 法定单位
     */
    @Column(name = "UNIT_1")
    private  String unit1;
    /**
     * 法定数量
     */
    @Column(name = "QTY_1")
    private  BigDecimal qty1;
    /**
     * 计量单位
     */
    @Column(name = "UNIT")
    private  String unit;
    /**
     * 商品规格型号
     */
    @Column(name = "G_MODEL")
    private  String GModel;
    /**
     * 商品名称
     */
    @Column(name = "G_NAME")
    private  String GName;
    /**
     * 商品序号
     */
    @Column(name = "SERIAL_NO")
    private  Integer serialNo;
    /**
     * 申报数量
     */
    @Column(name = "QTY")
    private  BigDecimal qty;
    /**
     * 原产国
     */
    @Column(name = "ORIGIN_COUNTRY")
    private  String originCountry;
    /**
     * 法定第二单位
     */
    @Column(name = "UNIT_2")
    private  String unit2;
    /**
     * 法定第二数量
     */
    @Column(name = "QTY_2")
    private  BigDecimal qty2;
    /**
     * 币制
     */
    @Column(name = "CURR")
    private  String curr;
    /**
     * 用途/生产厂家
     */
    @Column(name = "USE_TO")
    private  String useTo;
    /**
     * 工缴费
     */
    @Column(name = "WORK_USD")
    private  BigDecimal workUsd;
    /**
     * 最终目的国
     */
    @Column(name = "DESTINATION_COUNTRY")
    private  String destinationCountry;
    /**
     * 检验检疫编码
     */
    @Column(name = "CIQ_CODE")
    private  String ciqCode;
    /**
     * 商品英文名称
     */
    @Column(name = "DECL_GOODS_ENAME")
    private  String declGoodsEname;
    /**
     * 原产地区代码
     */
    @Column(name = "ORIGPLACE_CODE")
    private  String origplaceCode;
    /**
     * 用途代码
     */
    @Column(name = "PUR_POSE")
    private  String purPose;
    /**
     * 产品有效期
     */
    @Column(name = "PROD_VALIDDT")
    private  String prodValiddt;
    /**
     * 产品保质期
     */
    @Column(name = "PROD_QGP")
    private  String prodQgp;
    /**
     * 货物属性代码
     */
    @Column(name = "GOODS_ATTR")
    private  String goodsAttr;
    /**
     * 成份/原料/组份
     */
    @Column(name = "STUFF")
    private  String stuff;
    /**
     * UN编码
     */
    @Column(name = "UN_CODE")
    private  String unCode;
    /**
     * 危险货物名称
     */
    @Column(name = "DANG_NAME")
    private  String dangName;
    /**
     * 危包类别
     */
    @Column(name = "DANG_PACK_TYPE")
    private  String dangPackType;
    /**
     * 危包规格
     */
    @Column(name = "DANG_PACK_SPEC")
    private  String dangPackSpec;
    /**
     * 境外生产企业名称
     */
    @Column(name = "ENGMANENT_CNM")
    private  String engmanentCnm;
    /**
     * 非危险化学品
     */
    @Column(name = "NODANG_FLAG")
    private  String nodangFlag;
    /**
     * 目的地代码
     */
    @Column(name = "DEST_CODE")
    private  String destCode;
    /**
     * 检验检疫货物规格
     */
    @Column(name = "GOODS_SPEC")
    private  String goodsSpec;
    /**
     * 货物型号
     */
    @Column(name = "GOODS_MODEL")
    private  String goodsModel;
    /**
     * 货物品牌
     */
    @Column(name = "GOODS_BRAND")
    private  String goodsBrand;
    /**
     * 生产日期
     */
    @Column(name = "PRODUCE_DATE")
    private  String produceDate;
    /**
     * 生产批号
     */
    @Column(name = "PRODBATCH_NO")
    private  String prodbatchNo;
    /**
     * 境内目的地/境内货源地
     */
    @Column(name = "DISTRICT_CODE")
    private  String districtCode;
    /**
     * 检验检疫名称
     */
    @Column(name = "CIQ_NAME")
    private  String ciqName;
    /**
     * 生产单位注册号
     */
    @Column(name = "MNUFCTR_REGNO")
    private  String mnufctrRegno;
    /**
     * 生产单位名称
     */
    @Column(name = "MNUFCR_REGNAME")
    private  String mnufcrRegname;

    /**
     * 报关单表头id
     */
    @Column(name = "HEAD_ID")
    private  String headId;
    /**
     * 境内目的地/境内货源地 （行政）
     */
    @Column(name = "DISTRICT_POST_CODE")
    private  String districtPostCode;
    /**
     * 状态
     */
    @Column(name = "APPR_STATUS")
    private String apprStatus;
    @Transient
    private String apprStatusName;

    /**
     * 委托日期
     */
    @Column(name = "ENTRUST_DATE")
    private  Date entrustDate;
    /**
     * 委托日期-开始
     */
    @Transient
    private String entrustDateFrom;
    /**
     * 委托日期-结束
     */
    @Transient
    private String entrustDateTo;
    /**
     * 委托人
     */
    @Column(name = "ENTRUST_PERSON")
    private  String entrustPerson;
    /**
     * 受托人
     */
    @Column(name = "BE_ENTRUST_PERSON")
    private  String beEntrustPerson;

    /**
     * 商检查验日期
     */
    @Column(name = "CHECK_DATE")
    private  Date checkDate;
    /**
     * 商检查验日期-开始
     */
    @Transient
    private String checkDateFrom;
    /**
     * 商检查验日期-结束
     */
    @Transient
    private String checkDateTo;
    /**
     * 商检查验原因
     */
    @Column(name = "CHECK_REASON")
    private  String checkReason;

    /**
     * 商检查验完成日期-开始
     */
    @Transient
    private String completeCheckDateFrom;
    /**
     * 商检查验完成日期-结束
     */
    @Transient
    private String completeCheckDateTo;
    /**
     * 商检查验结果
     */
    @Column(name = "CHECK_RESULT")
    private  String checkResult;

    /**
     * 完税日期
     */
    @Column(name = "DUTY_DATE")
    private  Date dutyDate;
    /**
     * 完税日期-开始
     */
    @Transient
    private String dutyDateFrom;
    /**
     * 完税日期-结束
     */
    @Transient
    private String dutyDateTo;
    /**
     * 缴税方式
     */
    @Column(name = "DUTY_TYPE")
    private  String dutyType;
    /**
     * 完税总价
     */
    @Column(name = "DUTY_TOTAL")
    private  BigDecimal dutyTotal;
    /**
     * 关税
     */
    @Column(name = "DUTY_PRICE")
    private  BigDecimal dutyPrice;
    /**
     * 增值税
     */
    @Column(name = "TAX_PRICE")
    private  BigDecimal taxPrice;

    /**
     * 放行日期
     */
    @Column(name = "PASS_DATE")
    private  Date passDate;
    /**
     * 放行日期-开始
     */
    @Transient
    private String passDateFrom;
    /**
     * 放行日期-结束
     */
    @Transient
    private String passDateTo;

    /**
     * 海关查验日期
     */
    @Column(name = "CUSTOMS_CHECK_DATE")
    private  Date customsCheckDate;
    /**
     * 海关查验日期-开始
     */
    @Transient
    private String customsCheckDateFrom;
    /**
     * 海关查验日期-结束
     */
    @Transient
    private String customsCheckDateTo;
    /**
     * 海关查验原因
     */
    @Column(name = "CUSTOMS_CHECK_REASON")
    private  String customsCheckReason;
    /**
     * 海关查验结果
     */
    @Column(name = "CUSTOMS_CHECK_RESULT")
    private  String customsCheckResult;

    /**
     * 财务单据号
     */
    @Column(name = "FINANCE_NO")
    private  String financeNo;
    /**
     * 入/出库单号
     */
    @Column(name = "IN_OUT_NO")
    private  String inOutNo;

    /**
     * 到货通知日期
     */
    @Column(name = "NOTICE_DATE")
    private  Date noticeDate;
    /**
     * 到货通知日期-开始
     */
    @Transient
    private String noticeDateFrom;
    /**
     * 到货通知日期-结束
     */
    @Transient
    private String noticeDateTo;
    /**
     * 破损标记0-正常 1-破损
     */
    @Column(name = "DAMAGE_MARK")
    private  String damageMark;
    /**
     * 报关费用
     */
    @Column(name = "LOGISTICS_FEE")
    private  BigDecimal logisticsFee;
    /**
     * 物流费用
     */
    @Column(name = "CUSTOMS_FEE")
    private  BigDecimal customsFee;
    /**
     * 送货单号
     */
    @Column(name = "DELIVERY_NO")
    private  String deliveryNo;
    /**
     * 车牌号
     */
    @Column(name = "PLATE_NUM")
    private  String plateNum;
    /**
     * 送货人
     */
    @Column(name = "DELIVERY_PERSON")
    private  String deliveryPerson;
    /**
     * 送货日期
     */
    @Column(name = "DELIVERY_DATE")
    private  Date deliveryDate;
    /**
     * 送货日期-开始
     */
    @Transient
    private String deliveryDateFrom;
    /**
     * 送货日期-结束
     */
    @Transient
    private String deliveryDateTo;
    /**
     * 关联核注清单编号
     */
    @Column(name = "REL_LIST_NO")
    private  String relListNo;
    /**
     * 关联账册（手册）编号
     */
    @Column(name = "REL_EMS_NO")
    private  String relEmsNo;
    /**
     * 核注清单编号
     */
    @Column(name = "LIST_NO")
    private  String listNo;
    /**
     * 清单申报日期
     */
    @Column(name = "BILL_DECLARE_DATE")
    private  Date billDeclareDate;
    /**
     * 清单申报日期-开始
     */
    @Transient
    private String billDeclareDateFrom;
    /**
     * 清单申报日期-结束
     */
    @Transient
    private String billDeclareDateTo;
    /**
     * 清单状态
     */
    @Column(name = "BILL_STATUS")
    private String billStatus;
    /**
     * 提单内部编号
     */
    @Column(name = "ERP_EMS_LIST_NO")
    private String erpEmsListNo;
    /**
     * 提单内部编号
     */
    @Column(name = "ERP_INSERT_TIME")
    private Date erpInsertTime;
    /**
     * 提单制单日期-开始
     */
    @Transient
    private String erpInsertTimeFrom;
    /**
     * 提单制单日期-结束
     */
    @Transient
    private String erpInsertTimeTo;

    /**
     * 物料类型
     */
    @Column(name = "G_MARK")
    private String GMark;
    /**
     * 总数量
     */
    @Column(name = "QTY_ALL")
    private String qtyAll;
    /**
     * 总金额
     */
    @Column(name = "TOTAL_ALL")
    private String totalAll;

    /**
     * 报关单状态
     */
    @Column(name = "ENTRY_STATUS")
    private String entryStatus;
    /**
     * 报关单状态
     */
    @Column(name = "ENTRY_STATUS_NAME")
    private String entryStatusName;

    /**
     * 关税缓征利息
     */
    @Column(name = "DUTY_INTEREST")
    private String dutyInterest;
    /**
     * 增值税缓征利息
     */
    @Column(name = "TAX_INTEREST")
    private String taxInterest;

    /**
     * 贸易条款
     */
    @Transient
    private  String tradeTerms;

    /**
     * 计划进口日期(邀请日期)
     */
    @Transient
    @ApiModelProperty("计划进口日期")
    private Date inviteDate;

    /**
     * 计费重量
     */
    @Transient
    @ApiModelProperty("计费重量")
    private BigDecimal cweight;
    /**
     * 发票号码(预录入单表头)
     */
    @Transient
    @ApiModelProperty("发票号码")
    private  String invoiceNo;
    /**
     * 承运人(物流追踪)
     */
    @Transient
    @ApiModelProperty("承运人")
    private  String carrier;
}
