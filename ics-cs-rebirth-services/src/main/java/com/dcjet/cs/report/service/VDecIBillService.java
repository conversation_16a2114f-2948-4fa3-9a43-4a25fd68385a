package com.dcjet.cs.report.service;


import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.report.VDecIBillDto;
import com.dcjet.cs.dto.report.VDecIBillHeadDto;
import com.dcjet.cs.dto.report.VDecIBillHeadParam;
import com.dcjet.cs.dto.report.VDecIBillParam;
import com.dcjet.cs.erp.dao.DecIBillHeadMapper;
import com.dcjet.cs.erp.mapper.DecIBillHeadDtoMapper;
import com.dcjet.cs.report.dao.VDecIBillMapper;
import com.dcjet.cs.report.mapper.VDecIBillDtoMapper;
import com.dcjet.cs.report.model.VDecIBill;
import com.dcjet.cs.report.model.VDecSumModel;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * Service;
 *
 * @author: 沈振宇
 * @date: 2019-03-11
 */
@Service
public class VDecIBillService extends BaseService<VDecIBill> {

    @Resource
    private VDecIBillMapper viDecIBillMapper;

    @Override
    public Mapper<VDecIBill> getMapper() {
        return viDecIBillMapper;
    }
    @Resource
    private VDecIBillDtoMapper vDecIBillDtoMapper;

    @Resource
    private DecIBillHeadMapper decIBillHeadMapper;
    @Resource
    private DecIBillHeadDtoMapper decIBillHeadDtoMapper;
    @Resource
    private PCodeHolder pCodeHolder;

    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param vDecIBillParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<VDecIBillDto>> getListPaged(VDecIBillParam vDecIBillParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        VDecIBill vDecIBill = vDecIBillDtoMapper.toPo(vDecIBillParam);
        vDecIBill.setTradeCode(userInfo.getCompany());
        Page<VDecIBill> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> viDecIBillMapper.getList(vDecIBill));
        List<VDecIBillDto> vDecIBillDtos = page.getResult().stream().map(head -> {
            VDecIBillDto dto = vDecIBillDtoMapper.toDto(head);
            dto=changeWrap(dto);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<VDecIBillDto>> paged = ResultObject.createInstance(vDecIBillDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    private  VDecIBillDto changeWrap(VDecIBillDto dto)
    {
        String allWrap=(StringUtils.isBlank(dto.getWrapType())?"":dto.getWrapType()+',')+dto.getWrapType2();
        if(StringUtils.isNotBlank(allWrap))
        {
            String newWarpList="";
            String[] warpList=allWrap.split(",");
            for (String warp :warpList) {
                String warpName =  pCodeHolder.getValue(PCodeType.WRAP,warp);
                if (StringUtils.isNotBlank(warpName)) {
                    newWarpList=newWarpList+warpName+"/";
                }
            }
            if(newWarpList.length()>0) {
                dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
            }
        }
        return dto;
    }
    private  VDecIBillHeadDto changeHeadWrap(VDecIBillHeadDto dto)
    {
        String allWrap=(StringUtils.isBlank(dto.getWrapType())?"":dto.getWrapType()+',')+dto.getWrapType2();
        if(StringUtils.isNotBlank(allWrap))
        {
            String newWarpList="";
            String[] warpList=allWrap.split(",");
            for (String warp :warpList) {
                String warpName =  pCodeHolder.getValue(PCodeType.WRAP,warp);
                if (StringUtils.isNotBlank(warpName)) {
                    newWarpList=newWarpList+warpName+"/";
                }
            }
            if(newWarpList.length()>0) {
                dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
            }
        }
        return dto;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param vDecIBillParam
     * @param userInfo
     * @return
     */
    public List<VDecIBillDto> selectAll(VDecIBillParam vDecIBillParam, UserInfoToken userInfo) {
        VDecIBill vDecIBill = vDecIBillDtoMapper.toPo(vDecIBillParam);
        vDecIBill.setTradeCode(userInfo.getCompany());
        List<VDecIBillDto> vDecIBillDtos = new ArrayList<>();
        List<VDecIBill> vDecEEntries = viDecIBillMapper.getList(vDecIBill);
        if (CollectionUtils.isNotEmpty(vDecEEntries)) {
            vDecIBillDtos = vDecEEntries.stream().map(head -> {
                VDecIBillDto dto = vDecIBillDtoMapper.toDto(head);
                dto=changeWrap(dto);
                return dto;
            }).collect(Collectors.toList());
        }
        return vDecIBillDtos;
    }

    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param vDecIBillHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<VDecIBillHeadDto>> getListHeadPaged(VDecIBillHeadParam vDecIBillHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        VDecIBill vDecIBill = vDecIBillDtoMapper.toHeadPo(vDecIBillHeadParam);
        vDecIBill.setTradeCode(userInfo.getCompany());
        Page<VDecIBill> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> viDecIBillMapper.getHeadList(vDecIBill));
        List<VDecIBillHeadDto> vDecIBillHeadDtos = page.getResult().stream().map(head -> {
            VDecIBillHeadDto dto = vDecIBillDtoMapper.toHeadDto(head);
            dto=changeHeadWrap(dto);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<VDecIBillHeadDto>> paged = ResultObject.createInstance(vDecIBillHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<VDecIBillHeadDto> selectHeadAll(VDecIBillHeadParam exportParam, UserInfoToken userInfo) {
        VDecIBill vDecIBill = vDecIBillDtoMapper.toHeadPo(exportParam);
        vDecIBill.setTradeCode(userInfo.getCompany());
        List<VDecIBillHeadDto> vDecIBillHeadDtos = new ArrayList<>();
        List<VDecIBill> vDecIBills = viDecIBillMapper.getHeadList(vDecIBill);
        if (CollectionUtils.isNotEmpty(vDecIBills)) {
            vDecIBillHeadDtos = vDecIBills.stream().map(head -> {
                VDecIBillHeadDto dto = vDecIBillDtoMapper.toHeadDto(head);
                dto=changeHeadWrap(dto);
                return dto;
            }).collect(Collectors.toList());
        }
        return vDecIBillHeadDtos;
    }

    /**
     * 功能描述:汇总净重、申报数量、申报总价
     *
     * @param
     * @param userInfo
     * @return
     */
    public ResultObject getListSum(VDecIBillParam vDecIBillParam,UserInfoToken userInfo){
        VDecIBill vDecIBill = vDecIBillDtoMapper.toPo(vDecIBillParam);
        vDecIBill.setTradeCode(userInfo.getCompany());
        VDecSumModel vDecSumModel = viDecIBillMapper.getListSum(vDecIBill);

        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"), vDecSumModel);
    }

    /**
     * 功能描述:汇总表头
     *
     * @param
     * @param userInfo
     * @return
     */
    public ResultObject getHeadSum(VDecIBillHeadParam vDecIBillHeadParam,UserInfoToken userInfo){
        VDecIBill vDecIBill = vDecIBillDtoMapper.toHeadPo(vDecIBillHeadParam);
        vDecIBill.setTradeCode(userInfo.getCompany());
        VDecSumModel vDecSumModel= viDecIBillMapper.getHeadSum(vDecIBill);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"), vDecSumModel);
    }

    /**
     * 报表中心-进口清单表头数据更新
     */
    public void reportBillIHead(String tradeCode){
        viDecIBillMapper.deleteReportBillIHead(tradeCode);
        viDecIBillMapper.insertReportBillIHead(tradeCode);
    }

    /**
     * 报表中心-进口清单表头表体数据更新
     */
    public void reportBillIHeadList(String tradeCode){
        viDecIBillMapper.deleteReportBillIHeadList(tradeCode);
        viDecIBillMapper.insertReportBillIHeadList(tradeCode);
    }

}
