<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.report.dao.XyszcReportMapper">
    <resultMap id="xyszcReportResultMap" type="com.dcjet.cs.report.model.XyszcReport">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="FAC_G_NO" property="facGNo" jdbcType="VARCHAR" />
		<result column="ORDER_TOTAL" property="orderTotal" jdbcType="NUMERIC" />
		<result column="QUANTITY_ARRIVED" property="quantityArrived" jdbcType="NUMERIC" />
		<result column="QUANTITY_NO_ARRIVED" property="quantityNoArrived" jdbcType="NUMERIC" />
		<result column="ERP_UNIT" property="erpUnit" jdbcType="VARCHAR" />
		<result column="UNIT" property="unit" jdbcType="VARCHAR" />
		<result column="ERP_SCALE_FACTOR" property="erpScaleFactor" jdbcType="NUMERIC" />
		<result column="NO_ARRIVED_CONVERT" property="noArrivedConvert" jdbcType="NUMERIC" />
		<result column="COP_G_NO" property="copGNo" jdbcType="VARCHAR" />
		<result column="COP_G_NAME" property="copGName" jdbcType="VARCHAR" />
		<result column="COP_LINE_NO" property="copLineNo" jdbcType="NUMERIC" />
		<result column="COP_AMOUNT" property="copAmount" jdbcType="NUMERIC" />
		<result column="EXPORT_VOLUME" property="exportVolume" jdbcType="NUMERIC" />
		<result column="HAND_ALLOWANCE" property="handAllowance" jdbcType="NUMERIC" />
		<result column="HAND_ALLOWANCE_DIFF" property="handAllowanceDiff" jdbcType="NUMERIC" />
		<result column="DIFF_PRECENT" property="diffPrecent" jdbcType="NUMERIC" />
		<result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
		<result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,FAC_G_NO
     ,ORDER_TOTAL
     ,QUANTITY_ARRIVED
     ,QUANTITY_NO_ARRIVED
     ,ERP_UNIT
     ,UNIT
     ,ERP_SCALE_FACTOR
     ,NO_ARRIVED_CONVERT
     ,COP_G_NO
     ,COP_G_NAME
     ,COP_LINE_NO
     ,COP_AMOUNT
     ,EXPORT_VOLUME
     ,HAND_ALLOWANCE
     ,HAND_ALLOWANCE_DIFF
     ,DIFF_PRECENT
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,DATA_SOURCE
     ,TRADE_CODE
    </sql>
    <sql id="condition">
    <if test="facGNo != null and facGNo != ''"> 
		and FAC_G_NO like '%'|| #{facGNo} || '%'
	</if>
    <if test="orderTotal != null and orderTotal != ''"> 
		and ORDER_TOTAL = #{orderTotal}
	</if>
    <if test="quantityArrived != null and quantityArrived != ''"> 
		and QUANTITY_ARRIVED = #{quantityArrived}
	</if>
    <if test="quantityNoArrived != null and quantityNoArrived != ''"> 
		and QUANTITY_NO_ARRIVED = #{quantityNoArrived}
	</if>
    <if test="erpUnit != null and erpUnit != ''"> 
		and ERP_UNIT = #{erpUnit}
	</if>
    <if test="unit != null and unit != ''"> 
		and UNIT = #{unit}
	</if>
    <if test="erpScaleFactor != null and erpScaleFactor != ''"> 
		and ERP_SCALE_FACTOR = #{erpScaleFactor}
	</if>
    <if test="noArrivedConvert != null and noArrivedConvert != ''"> 
		and NO_ARRIVED_CONVERT = #{noArrivedConvert}
	</if>
    <if test="copGNo != null and copGNo != ''"> 
		and COP_G_NO like '%'|| #{copGNo} || '%'
	</if>
    <if test="copGName != null and copGName != ''"> 
		and COP_G_NAME like '%'|| #{copGName} || '%'
	</if>
    <if test="copLineNo != null and copLineNo != ''"> 
		and COP_LINE_NO = #{copLineNo}
	</if>
    <if test="copAmount != null and copAmount != ''"> 
		and COP_AMOUNT = #{copAmount}
	</if>
    <if test="exportVolume != null and exportVolume != ''"> 
		and EXPORT_VOLUME = #{exportVolume}
	</if>
    <if test="handAllowance != null and handAllowance != ''"> 
		and HAND_ALLOWANCE = #{handAllowance}
	</if>
    <if test="handAllowanceDiff != null and handAllowanceDiff != ''">
        <if test='_databaseId == "postgresql" '>
            and HAND_ALLOWANCE_DIFF::text like '%'|| #{handAllowanceDiff} || '%'
        </if>
        <if test='_databaseId != "postgresql" '>
            and HAND_ALLOWANCE_DIFF like '%'|| #{handAllowanceDiff} || '%'
        </if>
	</if>
    <if test="diffPrecent != null and diffPrecent != ''"> 
		and DIFF_PRECENT &lt; #{diffPrecent}
	</if>
        and TRADE_CODE = #{tradeCode}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="xyszcReportResultMap" parameterType="com.dcjet.cs.report.model.XyszcReport">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_XYSZC_REPORT t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.COP_LINE_NO, t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_XYSZC_REPORT t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByTradeCode">
        delete from T_XYSZC_REPORT t where TRADE_CODE = #{tradeCode}
    </delete>
</mapper>
