package com.dcjet.cs.report.mapper;

import com.dcjet.cs.dto.report.VDecEBillDto;
import com.dcjet.cs.dto.report.VDecEBillHeadDto;
import com.dcjet.cs.dto.report.VDecEBillHeadParam;
import com.dcjet.cs.dto.report.VDecEBillParam;
import com.dcjet.cs.report.model.VDecEBill;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
* generated by Generate dcits
* 
* @author: 沈振宇
* @date: 2019-03-11
*/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VDecEBillDtoMapper {

   /**
    * 转换DTO到数据库对象
    * @param po
    * @return
    */
   VDecEBillDto toDto(VDecEBill po);

   /**
    * 转换Param到数据库对象
    * @param param
    * @return
    */
   VDecEBill toPo(VDecEBillParam param);

   /**
    * 杞崲鏁版嵁搴撳璞″埌DTO
    * @param po
    * @return
    */
   VDecEBillHeadDto toHeadDto(VDecEBill po);

   /**
    * 杞崲鏁版嵁搴撳璞″埌DTO
    * @param po
    * @return
    */
   VDecEBill toHeadPo(VDecEBillHeadParam po);

}
