package com.dcjet.cs.report.vo;

import com.dcjet.cs.report.model.DynamicReport;
import com.dcjet.cs.report.model.DynamicReportParam;
import com.dcjet.cs.report.model.DynamicReportRtnValue;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DynamicReportInit {
    private DynamicReport main;
    private List<DynamicReportParam> paramList;
    private List<DynamicReportRtnValue> rtnValueList;
    String aa = "{\n" +
            "  \"main\": {\n" +
            "    \"reportName\": \"erp进口明细\",\n" +
            "    \"remark\": \"查询erp进口表体，测试\",\n" +
            "    \"sql\": \"select SID as sid,LINKED_NO as linkedNo,LINE_NO as lineNo , " +
            "BOND_MARK as bondMark,CURR as curr\\nfrom T_ERP_DEC_I_LIST \\n\\n\\nwhere TRADE_CODE = #{tradeCode}  <if test=\\\"dateFrom != null and dateFrom != ''\\\">\\n    <![CDATA[ and INSERT_TIME >= to_date(#{dateFrom},'yyyy-MM-dd')]]>\\n</if>\"\n" +
            "  " +
            "},\n" +
            "  \"paramList\": [\n" +
            "    {\n" +
            "      \"param\": \"dateFrom\",\n" +
            "      \"paramName\": \"创建时间-起\",\n" +
            "      \"inputType\": \"date\",\n" +
            "      \"convertType\": \"yyyy-MM-dd\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"param\": \"bondMark\",\n" +
            "      \"paramName\": \"保税/非保税标记\",\n" +
            "      \"inputType\": \"select\",\n" +
            "      \"convertType\": \"bondMark\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"param\": \"curr\",\n" +
            "      \"paramName\": \"币制\",\n" +
            "      \"inputType\": \"select\",\n" +
            "      \"convertType\": \"CURR_OUTDATED\"\n" +
            "    }\n" +
            "  ],\n" +
            "  \"rtnValueList\": [\n" +
            "    {\n" +
            "      \"field\": \"sid\",\n" +
            "      \"fieldName\": \"主键\",\n" +
            "      \"fieldType\": \"normal\",\n" +
            "      \"convertType\": null\n" +
            "    " +
            "},\n" +
            "    {\n" +
            "      \"field\": \"linkedNo\",\n" +
            "      \"fieldName\": \"单据号\",\n" +
            "      \"fieldType\": \"normal\",\n" +
            "      \"convertType\": null\n" +
            "    },\n" +
            "    {\n" +
            "      \"field\": \"lineNo\",\n" +
            "      \"fieldName\": \"序号\",\n" +
            "      \"fieldType\": \"normal\",\n" +
            "      \"convertType\": null\n" +
            "    },\n" +
            "    {\n" +
            "      \"field\": \"bondMark\",\n" +
            "      \"fieldName\": \"\",\n" +
            "      \"fieldType\": \"enum\",\n" +
            "      \"convertType\": \"bondMark\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"field\": \"curr\",\n" +
            "      \"fieldName\": \"币制\",\n" +
            "      \"fieldType\": \"pcode\",\n" +
            "      \"convertType\": \"CURR_OUTDATED\"\n" +
            "    }\n" +
            "  ]\n" +
            "}";
}
