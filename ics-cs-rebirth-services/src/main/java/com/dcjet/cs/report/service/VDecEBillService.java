package com.dcjet.cs.report.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.report.VDecEBillDto;
import com.dcjet.cs.dto.report.VDecEBillHeadDto;
import com.dcjet.cs.dto.report.VDecEBillHeadParam;
import com.dcjet.cs.dto.report.VDecEBillParam;
import com.dcjet.cs.report.dao.VDecEBillMapper;
import com.dcjet.cs.report.mapper.VDecEBillDtoMapper;
import com.dcjet.cs.report.model.VDecEBill;
import com.dcjet.cs.report.model.VDecSumModel;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * Service;
 *
 * @author: 沈振宇
 * @date: 2019-03-11
 */
@Service
public class VDecEBillService extends BaseService<VDecEBill> {

    @Resource
    private VDecEBillMapper vDecEBillMapper;

    @Override
    public Mapper<VDecEBill> getMapper() {
        return vDecEBillMapper;
    }

    @Resource
    private VDecEBillDtoMapper vDecEBillDtoMapper;

    @Resource
    private PCodeHolder pCodeHolder;

    /**
     * 获取分页信息
     *
     * @param vDecEBillParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<VDecEBillDto>> getListPaged(VDecEBillParam vDecEBillParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        VDecEBill vDecEBill = vDecEBillDtoMapper.toPo(vDecEBillParam);
        vDecEBill.setTradeCode(userInfo.getCompany());
        Page<VDecEBill> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vDecEBillMapper.getList(vDecEBill));
        List<VDecEBillDto> vDecEBillDtos = page.getResult().stream().map(head -> {
            VDecEBillDto dto = vDecEBillDtoMapper.toDto(head);
            dto = changeWrap(dto);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<VDecEBillDto>> paged = ResultObject.createInstance(vDecEBillDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    private VDecEBillDto changeWrap(VDecEBillDto dto) {
        String allWrap = (StringUtils.isBlank(dto.getWrapType()) ? "" : dto.getWrapType() + ',') + dto.getWrapType2();
        if (StringUtils.isNotBlank(allWrap)) {
            String newWarpList = "";
            String[] warpList = allWrap.split(",");
            for (String warp : warpList) {
                String warpName = pCodeHolder.getValue(PCodeType.WRAP, warp);
                if (StringUtils.isNotBlank(warpName)) {
                    newWarpList = newWarpList + warpName + "/";
                }
            }
            if (newWarpList.length() > 0) {
                dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
            }
        }
        return dto;
    }

    private VDecEBillHeadDto changeHeadWrap(VDecEBillHeadDto dto) {
        String allWrap = (StringUtils.isBlank(dto.getWrapType()) ? "" : dto.getWrapType() + ',') + dto.getWrapType2();
        if (StringUtils.isNotBlank(allWrap)) {
            String newWarpList = "";
            String[] warpList = allWrap.split(",");
            for (String warp : warpList) {
                String warpName = pCodeHolder.getValue(PCodeType.WRAP, warp);
                if (StringUtils.isNotBlank(warpName)) {
                    newWarpList = newWarpList + warpName + "/";
                }
            }
            if (newWarpList.length() > 0) {
                dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
            }
        }
        return dto;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param vDecEBillParam
     * @param userInfo
     * @return
     */
    public List<VDecEBillDto> selectAll(VDecEBillParam vDecEBillParam, UserInfoToken userInfo) {
        VDecEBill vDecEBill = vDecEBillDtoMapper.toPo(vDecEBillParam);
        vDecEBill.setTradeCode(userInfo.getCompany());
        List<VDecEBillDto> vDecEBillDtos = new ArrayList<>();
        List<VDecEBill> vDecEEntries = vDecEBillMapper.getList(vDecEBill);
        if (CollectionUtils.isNotEmpty(vDecEEntries)) {
            vDecEBillDtos = vDecEEntries.stream().map(head -> {
                VDecEBillDto dto = vDecEBillDtoMapper.toDto(head);
                dto = changeWrap(dto);
                return dto;
            }).collect(Collectors.toList());
        }
        return vDecEBillDtos;
    }

    /**
     * 获取分页信息
     *
     * @param vDecEBillHeadParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<VDecEBillHeadDto>> getListHeadPaged(VDecEBillHeadParam vDecEBillHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        VDecEBill vDecEBill = vDecEBillDtoMapper.toHeadPo(vDecEBillHeadParam);
        vDecEBill.setTradeCode(userInfo.getCompany());
        Page<VDecEBill> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> vDecEBillMapper.getHeadList(vDecEBill));
        List<VDecEBillHeadDto> vDecEBillHeadDtos = page.getResult().stream().map(head -> {
            VDecEBillHeadDto dto = vDecEBillDtoMapper.toHeadDto(head);
            dto = changeHeadWrap(dto);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<VDecEBillHeadDto>> paged = ResultObject.createInstance(vDecEBillHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<VDecEBillHeadDto> selectHeadAll(VDecEBillHeadParam exportParam, UserInfoToken userInfo) {
        VDecEBill vDecEBill = vDecEBillDtoMapper.toHeadPo(exportParam);
        vDecEBill.setTradeCode(userInfo.getCompany());
        List<VDecEBillHeadDto> vDecEBillHeadDtos = new ArrayList<>();
        List<VDecEBill> vDecEBills = vDecEBillMapper.getHeadList(vDecEBill);
        if (CollectionUtils.isNotEmpty(vDecEBills)) {
            vDecEBillHeadDtos = vDecEBills.stream().map(head -> {
                VDecEBillHeadDto dto = vDecEBillDtoMapper.toHeadDto(head);
                dto = changeHeadWrap(dto);
                return dto;
            }).collect(Collectors.toList());
        }
        return vDecEBillHeadDtos;
    }

    /**
     * 功能描述:汇总净重、申报数量、申报总价
     *
     * @param
     * @param userInfo
     * @return
     */
    public ResultObject getListSum(VDecEBillParam vDecEBillParam, UserInfoToken userInfo) {
        VDecEBill vDecEBill = vDecEBillDtoMapper.toPo(vDecEBillParam);
        vDecEBill.setTradeCode(userInfo.getCompany());
        VDecSumModel vDecSumModel = vDecEBillMapper.getListSum(vDecEBill);
        VDecEBillDto vDecEBillDto = vDecEBillDtoMapper.toDto(vDecEBill);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"), vDecSumModel);
    }

    /**
     * 功能描述:汇总表头
     *
     * @param
     * @param userInfo
     * @return
     */
    public ResultObject getHeadSum(VDecEBillHeadParam vDecEBillHeadParam, UserInfoToken userInfo) {
        VDecEBill vDecEBill = vDecEBillDtoMapper.toHeadPo(vDecEBillHeadParam);
        vDecEBill.setTradeCode(userInfo.getCompany());
        VDecSumModel vDecSumModel = vDecEBillMapper.getHeadSum(vDecEBill);
        VDecEBillHeadDto vDecEBillHeadDto = vDecEBillDtoMapper.toHeadDto(vDecEBill);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"), vDecSumModel);
    }

    /**
     * 报表中心-出口清单表头数据更新
     */
    public void reportBillEHead(String tradeCode){
        vDecEBillMapper.deleteReportBillEHead(tradeCode);
        vDecEBillMapper.insertReportBillEHead(tradeCode);
    }

    /**
     * 报表中心-出口清单表头数据更新
     */
    public void reportBillEHeadList(String tradeCode){
        vDecEBillMapper.deleteReportBillEHeadList(tradeCode);
        vDecEBillMapper.insertReportBillEHeadList(tradeCode);
    }
}
