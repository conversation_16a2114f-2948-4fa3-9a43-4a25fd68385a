package com.dcjet.cs.report.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2020-3-13
 */
@Setter
@Getter
@Table(name = "T_XYSZC_REPORT")
public class XyszcReport implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 唯一主键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 企业成品料号
     */
	@Column(name = "FAC_G_NO")
	private  String facGNo;
	/**
     * 订单总量
     */
	@Column(name = "ORDER_TOTAL")
	private  BigDecimal orderTotal;
	/**
     * 已到货量
     */
	@Column(name = "QUANTITY_ARRIVED")
	private  BigDecimal quantityArrived;
	/**
     * 未到货量
     */
	@Column(name = "QUANTITY_NO_ARRIVED")
	private  BigDecimal quantityNoArrived;
	/**
     * erp计量单位
     */
	@Column(name = "ERP_UNIT")
	private  String erpUnit;
	/**
     * 申报计量单位
     */
	@Column(name = "UNIT")
	private  String unit;
	/**
     * erp比例因子
     */
	@Column(name = "ERP_SCALE_FACTOR")
	private  BigDecimal erpScaleFactor;
	/**
     * 转换后未到货量
     */
	@Column(name = "NO_ARRIVED_CONVERT")
	private  BigDecimal noArrivedConvert;
	/**
     * 备案成品料号
     */
	@Column(name = "COP_G_NO")
	private  String copGNo;
	/**
     * 备案成品名称
     */
	@Column(name = "COP_G_NAME")
	private  String copGName;
	/**
     * 备案成品序号
     */
	@Column(name = "COP_LINE_NO")
	private  BigDecimal copLineNo;
	/**
     * 备案数量
     */
	@Column(name = "COP_AMOUNT")
	private  BigDecimal copAmount;
	/**
     * 已出口量
     */
	@Column(name = "EXPORT_VOLUME")
	private  BigDecimal exportVolume;
	/**
     * 手册余量
     */
	@Column(name = "HAND_ALLOWANCE")
	private  BigDecimal handAllowance;
	/**
     * 手册余量与未到货差异
     */
	@Column(name = "HAND_ALLOWANCE_DIFF")
	private  BigDecimal handAllowanceDiff;
	/**
     * 差异百分比
     */
	@Column(name = "DIFF_PRECENT")
	private  BigDecimal diffPrecent;
	/**
     * 创建人
     */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;

	/**
	 * 数据来源
	 */
	@Column(name = "DATA_SOURCE")
	private String dataSource;
	/**
	 * 企业编码
	 */
	@Column(name = "TRADE_CODE")
	private String tradeCode;
}
