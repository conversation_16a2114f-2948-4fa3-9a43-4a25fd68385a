<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.report.dao.XyszcReportTmpMapper">
    <resultMap id="xyszcReprotTempResultMap" type="com.dcjet.cs.report.model.XyszcReportTmp">
        <id column="SID" property="sid" jdbcType="VARCHAR" />
        <result column="FAC_G_NO" property="facGNo" jdbcType="VARCHAR" />
        <result column="ORDER_TOTAL" property="orderTotal" jdbcType="NUMERIC" />
        <result column="QUANTITY_ARRIVED" property="quantityArrived" jdbcType="NUMERIC" />
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
        <result column="INSERT_TIME" property="insertTime" jdbcType="DATE" />
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE" />
        <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="TEMP_OWNER" property="tempOwner" jdbcType="VARCHAR"/>
        <result column="TEMP_FLAG" property="tempFlag" jdbcType="VARCHAR"/>
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,FAC_G_NO
     ,ORDER_TOTAL
     ,QUANTITY_ARRIVED
     ,INSERT_TIME
     ,INSERT_USER
     ,UPDATE_TIME
     ,UPDATE_USER
     ,DATA_SOURCE
     ,TRADE_CODE
     ,TEMP_FLAG
     ,TEMP_OWNER
    </sql>
    <sql id="condition">
    <if test="facGNo != null and facGNo != ''">
		and FAC_G_NO = #{facGNo}
	</if>
		and TRADE_CODE = #{tradeCode}
    <if test="tempOwner != null and tempOwner != ''">
		and TEMP_OWNER = #{tempOwner}
	</if>
    <if test="tempFlag != null and tempFlag != ''"> 
		and TEMP_FLAG = #{tempFlag}
	</if>
    </sql>    
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="xyszcReprotTempResultMap" parameterType="com.dcjet.cs.report.model.XyszcReportTmp">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_TMP_XYSZC_REPORT t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_TMP_XYSZC_REPORT t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByTradeCode">
        delete from T_TMP_XYSZC_REPORT t where trade_code = #{tradeCode}
    </delete>

    <select id="selectByFlag" resultMap="xyszcReprotTempResultMap" parameterType="map">
        select t.* from T_TMP_XYSZC_REPORT t
        <where>
            and t.TEMP_OWNER = #{TEMP_OWNER,jdbcType=VARCHAR}
            AND t.TEMP_FLAG IN
            <foreach item="item" index="index" collection="TEMP_FLAG" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        order by t.TEMP_INDEX ASC
    </select>
</mapper>
