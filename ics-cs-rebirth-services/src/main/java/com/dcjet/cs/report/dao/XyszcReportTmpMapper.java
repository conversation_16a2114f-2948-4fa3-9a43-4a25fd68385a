package com.dcjet.cs.report.dao;

import com.dcjet.cs.report.model.XyszcReportTmp;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* SjgInRelationListTemp
* <AUTHOR>
* @date: 2019-10-29
*/
public interface XyszcReportTmpMapper extends Mapper<XyszcReportTmp> {
    /**
     * 查询获取数据
     * @param xyszcReportTmp
     * @return
     */
    List<XyszcReportTmp> getList(XyszcReportTmp xyszcReportTmp);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 根据tradeCode批量删除临时表数据
     */
    int deleteByTradeCode(@Param("tradeCode") String tradeCode);
    /**
     * 根据传入的数据类型获取相应数据
     *
     * @param param
     * @return 返回结果
     */
    List<XyszcReportTmp> selectByFlag(Map<String, Object> param);
}
