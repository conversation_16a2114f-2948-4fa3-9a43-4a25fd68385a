package com.dcjet.cs.report.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.report.VDecIBillDto;
import com.dcjet.cs.dto.report.VDecIBillParam;
import com.dcjet.cs.report.dao.VDecIBillMapper;
import com.dcjet.cs.report.mapper.VDecIBillDtoMapper;
import com.dcjet.cs.report.model.VDecIBill;
import com.dcjet.cs.util.CommonEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 进口清单-表头表体异步导出
 * @author: WJ
 * @createDate: 2020/9/14 10:37
 */
@Component
public class ExportIBillService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private VDecIBillMapper viDecIBillMapper;

    @Resource
    private VDecIBillDtoMapper vDecIBillDtoMapper;

    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private CommonService commonService;

    private String taskName;

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("I_BILL_HEAD_LIST_0");
        list.add("I_BILL_HEAD_LIST_1");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        VDecIBillParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        VDecIBill vDecIBill = vDecIBillDtoMapper.toPo(exportParam);
        if (CommonEnum.BondMarkEnum.BOND.getCode().equals(vDecIBill.getBondMark())) {
            taskName = xdoi18n.XdoI18nUtil.t("进口保税清单-表头表体异步导出(I_BILL_HEAD_LIST_0)");
        }
        if (CommonEnum.BondMarkEnum.NOBOND.getCode().equals(vDecIBill.getBondMark())) {
            taskName = xdoi18n.XdoI18nUtil.t("进口非保税清单-表头表体异步导出(I_BILL_HEAD_LIST_1)");
        }
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(vDecIBill));
        Integer count = viDecIBillMapper.selectDataCountAll(vDecIBill);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        VDecIBillParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        VDecIBill vDecIBill = vDecIBillDtoMapper.toPo(exportParam);
        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(vDecIBill));
        Page<VDecIBill> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> viDecIBillMapper.getList(vDecIBill));

        List<VDecIBillDto> vDecIBillDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            vDecIBillDtos = page.getResult().stream().map(head -> {
                VDecIBillDto dto = vDecIBillDtoMapper.toDto(head);
                dto=changeWrap(dto);
                return dto;
            }).collect(Collectors.toList());
        }

        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(vDecIBillDtos));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    /**
     * 设置异步导出的分页条数
     */
    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    public List<VDecIBillDto> convertForPrint(List<VDecIBillDto> list) {
        for (VDecIBillDto item : list) {
            if (StringUtils.isNotBlank(item.getContainerType())) {
                item.setContainerType(item.getContainerType() + "|" + CommonEnum.ContainerEnum.getValue(item.getContainerType()));
            }
            if (StringUtils.isNotBlank(item.getGMark())) {
                item.setGMark(CommonEnum.GMarkEnum.getValue(item.getGMark()));
            }
            //-1 未发送 0 发送失败  1发送成功 2发送中 J1 通过 J3 退单 JDL 删单
            if (StringUtils.isNotBlank(item.getBillStatus())) {
                item.setBillStatus(item.getBillStatus() + "|" + CommonEnum.BillEnum.getValue(item.getBillStatus()));
            }
            if (StringUtils.isNotBlank(item.getInsurMark())) {
                item.setInsurMark(CommonEnum.FeeMarkEnum.getValue(item.getInsurMark()));
            }
            if (StringUtils.isNotBlank(item.getFeeMark())) {
                item.setFeeMark(CommonEnum.FeeMarkEnum.getValue(item.getFeeMark()));
            }
            if (StringUtils.isNotBlank(item.getOtherMark())) {
                item.setOtherMark(CommonEnum.FeeMarkEnum.getValue(item.getOtherMark()));
            }
            item.setCutMode(commonService.convertPCode(item.getCutMode(), PCodeType.LEVYTYPE));
            item.setCurrAll(commonService.convertPCode(item.getCurrAll(), PCodeType.CURR_OUTDATED));
            item.setOtherCurr(commonService.convertPCode(item.getOtherCurr(), PCodeType.CURR_OUTDATED));
            item.setFeeCurr(commonService.convertPCode(item.getFeeCurr(), PCodeType.CURR_OUTDATED));
            item.setInsurCurr(commonService.convertPCode(item.getInsurCurr(), PCodeType.CURR_OUTDATED));
            item.setTrafMode(commonService.convertPCode(item.getTrafMode(), PCodeType.TRANSF));
            item.setTradeMode(commonService.convertPCode(item.getTradeMode(), PCodeType.TRADE));
            item.setTransMode(commonService.convertPCode(item.getTransMode(), PCodeType.TRANSAC));
            item.setMasterCustoms(commonService.convertPCode(item.getMasterCustoms(), PCodeType.CUSTOMS_REL));
            item.setTradeCountry(commonService.convertPCode(item.getTradeCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setIEPort(commonService.convertPCode(item.getIEPort(), PCodeType.CUSTOMS_REL));
            item.setWrapType(commonService.convertPCode(item.getWrapType(), PCodeType.WRAP));
            if(StringUtils.isNotBlank(item.getWrapType2()))
            {
                String[] wrapList=item.getWrapType2().split(",");
                StringBuilder newWrap= new StringBuilder();
                for(String wrap :wrapList)
                {
                    newWrap.append(commonService.convertPCode(wrap, PCodeType.WRAP)).append("/");
                }
                item.setWrapType2(newWrap.toString());
            }
            item.setOriginCountry(commonService.convertPCode(item.getOriginCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setCurr(commonService.convertPCode(item.getCurr(), PCodeType.CURR_OUTDATED));
            item.setDutyMode(commonService.convertPCode(item.getDutyMode(), PCodeType.LEVYMODE));
            item.setUnit(commonService.convertPCode(item.getUnit(), PCodeType.UNIT));
            item.setUnit1(commonService.convertPCode(item.getUnit1(), PCodeType.UNIT));
            item.setUnit2(commonService.convertPCode(item.getUnit2(), PCodeType.UNIT));
            item.setDestinationCountry(commonService.convertPCode(item.getDestinationCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setEntryPort(commonService.convertPCode(item.getEntryPort(), PCodeType.CIQ_ENTY_PORT));
        }
        return list;
    }

    private VDecIBillDto changeWrap(VDecIBillDto dto)
    {
        String allWrap=(StringUtils.isBlank(dto.getWrapType())?"":dto.getWrapType()+',')+dto.getWrapType2();
        if(StringUtils.isNotBlank(allWrap))
        {
            String newWarpList="";
            String[] warpList=allWrap.split(",");
            for (String warp :warpList) {
                String warpName =  pCodeHolder.getValue(PCodeType.WRAP,warp);
                if (StringUtils.isNotBlank(warpName)) {
                    newWarpList=newWarpList+warpName+"/";
                }
            }
            if(newWarpList.length()>0) {
                dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
            }
        }
        return dto;
    }

    private VDecIBillParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        VDecIBillParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, VDecIBillParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
