<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.mqsync.dao.SyncKeyLogMapper">

    <delete id="deleteHistoryDate">
        delete from t_gwstd_mq_sync_key_log
        <where>
            <if test='_databaseId == "postgresql" '>
                <![CDATA[ insert_time < current_date::timestamp + '-3 month' ]]>
            </if>
            <if test='_databaseId != "postgresql" '>
                <![CDATA[ insert_time < ADD_MONTHS(trunc(sysdate), -3) ]]>
            </if>
        </where>
    </delete>
</mapper>