<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.mqsync.dao.SyncLogMapper">

    <select id="list" resultType="com.dcjet.cs.mqsync.model.SyncLog">
        select a.*
        from T_GWSTD_MQ_SYNC_LOG a

        <where>
            <if test="serviceName != null and serviceName != ''">
                and a.SERVICE_NAME = #{serviceName}
            </if>
            <if test="methodName != null and methodName != ''">
                and a.METHOD_NAME = #{methodName}
            </if>
            <if test="status != null and status != ''">
                and a.STATUS = #{status}
            </if>
            <if test="maxFailedCount != null ">
                <![CDATA[ and a.FAILED_COUNT <= #{maxFailedCount} ]]>
            </if>
            <if test="amount != null ">
                <if test='_databaseId == "postgresql" '>
                    limit #{amount}
                </if>
                <if test='_databaseId != "postgresql" '>
                    <![CDATA[ and ROWNUM <= #{amount} ]]>
                </if>
            </if>
        </where>
    </select>

    <delete id="deleteHistoryDate">
        delete from t_gwstd_mq_sync_log
        <where>
            <if test='_databaseId == "postgresql" '>
                <![CDATA[ insert_time < current_date::timestamp + '-6 month' ]]>
            </if>
            <if test='_databaseId != "postgresql" '>
                <![CDATA[ insert_time < ADD_MONTHS(trunc(sysdate), -6) ]]>
            </if>
        </where>
    </delete>
</mapper>