package com.dcjet.cs.mqsync.core.context;

import com.dcjet.cs.mqsync.core.metadata.MqConsumerMetadata;
import com.dcjet.cs.mqsync.core.metadata.MqSimpleMetadata;

public class MqMetadataHolder {
    private static transient ThreadLocal<MqSimpleMetadata> kafkaMetadata = new ThreadLocal<>();

    public static void setMetadata(MqConsumerMetadata metadata) {
        kafkaMetadata.set(
                new MqSimpleMetadata(
                        metadata.getGroupId(), metadata.getKafkaTopic(),
                        metadata.getOffset()));
    }

    public static void setMetadata(MqSimpleMetadata metadata) {
        kafkaMetadata.set(metadata);
    }

    public static MqSimpleMetadata getMetadata() {
        return kafkaMetadata.get();
    }

    public static void clear() {
        kafkaMetadata.remove();
    }
}
