package com.dcjet.cs.gwLvCitesRiskSpecies.dao;
import com.dcjet.cs.gwLvCitesRiskSpecies.model.GwLvCitesRiskSpecies;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* GwLvCitesRiskSpecies
* <AUTHOR>
* @date: 2022-4-13
*/
public interface GwLvCitesRiskSpeciesMapper extends Mapper<GwLvCitesRiskSpecies> {
    /**
     * 查询获取数据
     * @param gwLvCitesRiskSpecies
     * @return
     */
    List<GwLvCitesRiskSpecies> getList(GwLvCitesRiskSpecies gwLvCitesRiskSpecies);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
