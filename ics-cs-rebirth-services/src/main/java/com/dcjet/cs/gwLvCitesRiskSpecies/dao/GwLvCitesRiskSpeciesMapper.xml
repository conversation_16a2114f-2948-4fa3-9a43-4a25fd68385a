<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.gwLvCitesRiskSpecies.dao.GwLvCitesRiskSpeciesMapper">
    <resultMap id="gwLvCitesRiskSpeciesResultMap" type="com.dcjet.cs.gwLvCitesRiskSpecies.model.GwLvCitesRiskSpecies">
		<result column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="is_open" property="isOpen" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="species_name" property="speciesName" jdbcType="VARCHAR" />
		<result column="scientific_name" property="scientificName" jdbcType="VARCHAR" />
		<result column="address" property="address" jdbcType="VARCHAR" />
        <result column="species_type" property="speciesType" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,insert_time
     ,insert_user
     ,update_time
     ,update_user
     ,is_open
     ,trade_code
     ,species_name
     ,scientific_name
     ,address
,species_type
    </sql>
    <sql id="condition">
        and trade_code = #{tradeCode}
    <if test="speciesName != null and speciesName != ''">
        <if test="isSearchLikeMatch != null and isSearchLikeMatch">
            and species_name like '%'|| #{speciesName} || '%'
        </if>
        <if test="isSearchLikeMatch == null or !isSearchLikeMatch">
            and species_name = #{speciesName}
        </if>
	</if>
    <if test="speciesType != null and speciesType!=''">
        and species_type = #{speciesType}
    </if>
    <if test="scientificName != null and scientificName != ''">
        <if test="isSearchLikeMatch != null and isSearchLikeMatch">
            and scientific_name like '%'|| #{scientificName} || '%'
        </if>
        <if test="isSearchLikeMatch == null or !isSearchLikeMatch">
            and scientific_name = #{scientificName}
        </if>
	</if>
        <if test="isOpen != null and isOpen != ''">
            and is_open =#{isOpen}
        </if>
        <if test="sid != null and sid != ''">
            and sid !=#{sid}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwLvCitesRiskSpeciesResultMap" parameterType="com.dcjet.cs.gwLvCitesRiskSpecies.model.GwLvCitesRiskSpecies">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_gw_lv_cites_risk_species t
        <where>
            <include refid="condition"></include>
        </where>
        order by update_time desc
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_gw_lv_cites_risk_species t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
