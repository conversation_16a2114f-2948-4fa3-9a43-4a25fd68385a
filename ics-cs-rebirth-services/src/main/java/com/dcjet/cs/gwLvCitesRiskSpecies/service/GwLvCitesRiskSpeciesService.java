package com.dcjet.cs.gwLvCitesRiskSpecies.service;
import com.dcjet.cs.gwLvCitesProofMain.service.EnumLvDictionary;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.gwLvCitesRiskSpecies.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.gwLvCitesRiskSpecies.dao.GwLvCitesRiskSpeciesMapper;
import com.dcjet.cs.gwLvCitesRiskSpecies.mapper.GwLvCitesRiskSpeciesDtoMapper;
import com.dcjet.cs.gwLvCitesRiskSpecies.model.GwLvCitesRiskSpecies;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-4-13
 */
@Service
public class GwLvCitesRiskSpeciesService extends BaseService<GwLvCitesRiskSpecies> {
    @Resource
    private GwLvCitesRiskSpeciesMapper gwLvCitesRiskSpeciesMapper;
    @Resource
    private GwLvCitesRiskSpeciesDtoMapper gwLvCitesRiskSpeciesDtoMapper;
    @Override
    public Mapper<GwLvCitesRiskSpecies> getMapper() {
        return gwLvCitesRiskSpeciesMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwLvCitesRiskSpeciesParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwLvCitesRiskSpeciesDto>> getListPaged(GwLvCitesRiskSpeciesParam gwLvCitesRiskSpeciesParam, PageParam pageParam) {
        // 启用分页查询
        GwLvCitesRiskSpecies gwLvCitesRiskSpecies = gwLvCitesRiskSpeciesDtoMapper.toPo(gwLvCitesRiskSpeciesParam);
        gwLvCitesRiskSpecies.setIsSearchLikeMatch(true);

        Page<GwLvCitesRiskSpecies> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwLvCitesRiskSpeciesMapper.getList(gwLvCitesRiskSpecies));
        List<GwLvCitesRiskSpeciesDto> gwLvCitesRiskSpeciesDtos = page.getResult().stream().map(head -> {
            GwLvCitesRiskSpeciesDto dto = gwLvCitesRiskSpeciesDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwLvCitesRiskSpeciesDto>> paged = ResultObject.createInstance(gwLvCitesRiskSpeciesDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwLvCitesRiskSpeciesParam
     * @param userInfo
     * @return
     */
    public GwLvCitesRiskSpeciesDto insert(GwLvCitesRiskSpeciesParam gwLvCitesRiskSpeciesParam, UserInfoToken userInfo) {
        GwLvCitesRiskSpecies gwLvCitesRiskSpecies = gwLvCitesRiskSpeciesDtoMapper.toPo(gwLvCitesRiskSpeciesParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwLvCitesRiskSpecies.setSid(sid);
        gwLvCitesRiskSpecies.setInsertUser(userInfo.getUserNo());
        gwLvCitesRiskSpecies.setInsertTime(new Date());
        gwLvCitesRiskSpecies.setUpdateUser(userInfo.getUserNo());
        gwLvCitesRiskSpecies.setUpdateTime(new Date());
        gwLvCitesRiskSpecies.setIsOpen("open");


        // 新增数据
        int insertStatus = gwLvCitesRiskSpeciesMapper.insert(gwLvCitesRiskSpecies);
        return  insertStatus > 0 ? gwLvCitesRiskSpeciesDtoMapper.toDto(gwLvCitesRiskSpecies) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwLvCitesRiskSpeciesParam
     * @param userInfo
     * @return
     */
    public GwLvCitesRiskSpeciesDto update(GwLvCitesRiskSpeciesParam gwLvCitesRiskSpeciesParam, UserInfoToken userInfo) {
        GwLvCitesRiskSpecies gwLvCitesRiskSpecies = gwLvCitesRiskSpeciesMapper.selectByPrimaryKey(gwLvCitesRiskSpeciesParam);
        gwLvCitesRiskSpeciesDtoMapper.updatePo(gwLvCitesRiskSpeciesParam, gwLvCitesRiskSpecies);
        gwLvCitesRiskSpecies.setUpdateUser(userInfo.getUserNo());
        gwLvCitesRiskSpecies.setUpdateTime(new Date());

        // 更新数据
        int update = gwLvCitesRiskSpeciesMapper.updateByPrimaryKey(gwLvCitesRiskSpecies);
        return update > 0 ? gwLvCitesRiskSpeciesDtoMapper.toDto(gwLvCitesRiskSpecies) : null;
    }

    public boolean existsSpecies(String tradeCode,String speciesName,String scientificName,String sid){
        GwLvCitesRiskSpecies para = new GwLvCitesRiskSpecies();
        para.setTradeCode(tradeCode);
        para.setSpeciesName(speciesName);
        para.setScientificName(scientificName);
        para.setSid(sid);

        //获取待归类数据
        List<GwLvCitesRiskSpecies> r = gwLvCitesRiskSpeciesMapper.getList(para);
        if(r==null || r.size() <= 0) return false;

        return true;

    }

    /**
     * 启用停用数据
     * @param par
     * @param userInfo
     * @return
     */
    public Pair<Boolean,String> StopAndOpen(List<String> par,String isOpen, UserInfoToken userInfo) {
        if(par == null|| par.size() <= 0) {
            return Pair.of(false,"传入的查询条件为空，操作失败！");
        }

        par.stream().forEach(speciesName -> {
            GwLvCitesRiskSpeciesParam param = new GwLvCitesRiskSpeciesParam();
            param.setTradeCode(userInfo.getCompany());
            param.setSpeciesName(speciesName);

            GwLvCitesRiskSpecies species = gwLvCitesRiskSpeciesMapper.selectByPrimaryKey(param);
            if(species != null) {
                species.setUpdateUser(userInfo.getUserNo());
                species.setUpdateTime(new Date());
                species.setIsOpen(isOpen);
                gwLvCitesRiskSpeciesMapper.updateByPrimaryKey(species);
            }
        });

        return Pair.of(true,isOpen + "操作成功！");
    }

    public List<Pair<String,String>> checkData(GwLvCitesRiskSpeciesParam par, EnumLvDictionary.OperateType operateType) {

        List<Pair<String,String>> result = new ArrayList();

        if(existsSpecies(par.getTradeCode(),par.getSpeciesName(),null,par.getSid())) {

            result.add(Pair.of("errorMess", "[物种中文名]已存在。"));
        }
        if(existsSpecies(par.getTradeCode(),null,par.getScientificName(),par.getSid())) {

            result.add(Pair.of("errorMess", "[拉丁学名]已存在。"));
        }

        return result;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		gwLvCitesRiskSpeciesMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwLvCitesRiskSpeciesDto> selectAll(GwLvCitesRiskSpeciesParam exportParam, UserInfoToken userInfo) {
        GwLvCitesRiskSpecies gwLvCitesRiskSpecies = gwLvCitesRiskSpeciesDtoMapper.toPo(exportParam);
        if(gwLvCitesRiskSpecies.getIsSearchLikeMatch() == null)
            gwLvCitesRiskSpecies.setIsSearchLikeMatch(true);

        List<GwLvCitesRiskSpeciesDto> gwLvCitesRiskSpeciesDtos = new ArrayList<>();
        List<GwLvCitesRiskSpecies> gwLvCitesRiskSpeciess = gwLvCitesRiskSpeciesMapper.getList(gwLvCitesRiskSpecies);
        if (CollectionUtils.isNotEmpty(gwLvCitesRiskSpeciess)) {
            gwLvCitesRiskSpeciesDtos = gwLvCitesRiskSpeciess.stream().map(head -> {
                GwLvCitesRiskSpeciesDto dto = gwLvCitesRiskSpeciesDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwLvCitesRiskSpeciesDtos;
    }


}
