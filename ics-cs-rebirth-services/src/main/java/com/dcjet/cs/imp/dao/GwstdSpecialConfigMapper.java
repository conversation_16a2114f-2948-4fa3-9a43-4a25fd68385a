package com.dcjet.cs.imp.dao;

import com.dcjet.cs.imp.model.GwstdSpecialConfig;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwstdSpecialConfig
* <AUTHOR>
* @date: 2019-7-3
*/
public interface GwstdSpecialConfigMapper extends Mapper<GwstdSpecialConfig> {
    /**
     * 查询获取数据
     * @param gwstdSpecialConfig
     * @return
     */
    List<GwstdSpecialConfig> getList(GwstdSpecialConfig gwstdSpecialConfig);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * @param tradeCode
     * @return 返回访问立交桥的类型
     */
    String selectByTradCode(String tradeCode);

    /**
     * @param tradeCode
     * @return 返回发送目的地(0.立交桥 1.金二)
     */
    String selectSendDestByTradeCode(String tradeCode);

    /**
     *
     * @param tradeCode
     * @return
     */
    int deleteByTradeCode(String tradeCode);

}
