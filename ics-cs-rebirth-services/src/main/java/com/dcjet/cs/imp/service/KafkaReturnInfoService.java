package com.dcjet.cs.imp.service;

import com.dcjet.cs.erp.dao.DecEBillHeadMapper;
import com.dcjet.cs.erp.dao.DecIBillHeadMapper;
import com.dcjet.cs.erp.model.DecEBillHead;
import com.dcjet.cs.erp.model.DecIBillHead;
import com.dcjet.cs.imp.dao.KafkaReturnInfoMapper;
import com.dcjet.cs.imp.model.KafkaBillModel;
import com.dcjet.cs.imp.model.KafkaReceiveMessage;
import com.dcjet.cs.mqsync.core.service.BaseMqSyncService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.google.common.base.Strings;
import com.xdo.common.json.JsonObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * generated by Generate dcits
 * 请求api返回信息 Service;
 *
 * @author: 鏈辫緣
 * @date: 2019-04-24
 */
@Service
@Slf4j
public class KafkaReturnInfoService extends BaseMqSyncService<KafkaReceiveMessage> {
    @Resource
    private KafkaReturnInfoMapper kafkaReturnInfoMapper;
    @Resource
    public DecIBillHeadMapper decIBillHeadMapper;
    @Resource
    public DecEBillHeadMapper decEBillHeadMapper;

    /**
     * 功能描述:插入报关立交桥post返回数据
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/4/24
     * @param: tradeCode 企业编码
     * @param: billSid 单据id
     * @param: billType 单据类型
     * @param: returnJson api返回json数据
     * @param: strApiType api类型
     * @param: blnIsSucc 调用是否成功
     * @param: ljqNo 报关立交桥暂存编号
     * @return:
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void consume(String data) {
        log.info(" -----------KAFKA接口获取清单回执信息开始 -------------");
        KafkaReceiveMessage message = null;
        try {
            message = JsonObjectMapper.getInstance().fromJson(data, KafkaReceiveMessage.class);
            KafkaBillModel billEntryModel = message.getData();
            log.info(String.format(" -----------KAFKA接口开始更新清单回执信息，内部编号：【%s】 -------------", billEntryModel.getEmsListNo()));
            // 0-报文已接收->J0  1-通过（已核扣）->J1 2-转人工-J2
            if (ConstantsStatus.STATUS_0.equals(billEntryModel.getStatus())) {
                billEntryModel.setStatus("J0");
            }
            if (ConstantsStatus.STATUS_1.equals(billEntryModel.getStatus())) {
                billEntryModel.setStatus("J1");
            }
            if (ConstantsStatus.STATUS_2.equals(billEntryModel.getStatus())) {
                billEntryModel.setStatus("J2");
            }
            if (ConstantsStatus.STATUS_3.equals(billEntryModel.getStatus())) {
                billEntryModel.setStatus("J3");
            }
            billEntryModel.setBondMark(CommonEnum.BondMarkEnum.BOND.getCode());
            /*
            kafka 认为是保税的数据
             */
            kafkaReturnInfoMapper.updatePassedListNo(billEntryModel);
            /*
            清单终审通过状态，B/C手册加项增量
             */
            if ("J1".equals(billEntryModel.getStatus())) {
                Example exampleIBillHead = new Example(DecIBillHead.class);
                Example.Criteria criteria = exampleIBillHead.createCriteria();
                criteria.andEqualTo("tradeCode", billEntryModel.getTradeCode());
                criteria.andEqualTo("emsListNo", billEntryModel.getEmsListNo());
                List<DecIBillHead> decIBillHeadList = decIBillHeadMapper.selectByExample(exampleIBillHead);
                for (DecIBillHead decIBillHead : decIBillHeadList) {
                    log.info(String.format(" -----------KAFKA接口开始更新清单回执信息，BC手册回余量开始【%s】 -------------", billEntryModel.getEmsListNo()));
                    if (!Strings.isNullOrEmpty(decIBillHead.getEmsNo()) && (CommonVariable.EMS_B.equals(decIBillHead.getEmsNo().substring(0
                            , 1)) || CommonVariable.EMS_C.equals(decIBillHead.getEmsNo().substring(0, 1)))) {
                        if (Constants.TRADE_MODE_4400.equals(decIBillHead.getTradeMode()) || Constants.TRADE_MODE_4600.equals(decIBillHead.getTradeMode())) {
                            Map<String, Object> pa = new HashMap<String, Object>(4);
                            pa.put("P_BILL_HEAD_SID", decIBillHead.getSid());
                            pa.put("P_STATUS", billEntryModel.getStatus());
                            pa.put("P_ERR_MSG", "");
                            kafkaReturnInfoMapper.qtyCalcIBill(pa);
                        }
                    }
                    log.info(String.format(" -----------KAFKA接口开始更新清单回执信息，BC手册回余量结束【%s】 -------------", billEntryModel.getEmsListNo()));
                }
                Example exampleEBillHead = new Example(DecEBillHead.class);
                Example.Criteria criteriaE = exampleEBillHead.createCriteria();
                criteriaE.andEqualTo("tradeCode", billEntryModel.getTradeCode());
                criteriaE.andEqualTo("emsListNo", billEntryModel.getEmsListNo());
                List<DecEBillHead> decEBillHeadList = decEBillHeadMapper.selectByExample(exampleEBillHead);

                for (DecEBillHead decEBillHead : decEBillHeadList) {
                    if (!Strings.isNullOrEmpty(decEBillHead.getEmsNo()) && (CommonVariable.EMS_B.equals(decEBillHead.getEmsNo().substring(0, 1)) || CommonVariable.EMS_C.equals(decEBillHead.getEmsNo().substring(0, 1)))) {
                        if (Constants.TRADE_MODE_0700.equals(decEBillHead.getTradeMode()) || Constants.TRADE_MODE_0300.equals(decEBillHead.getTradeMode())) {
                            Map<String, Object> pa = new HashMap<String, Object>(4);
                            pa.put("P_BILL_HEAD_SID", decEBillHead.getSid());
                            pa.put("P_STATUS", billEntryModel.getStatus());
                            pa.put("P_ERR_MSG", "");
                            kafkaReturnInfoMapper.qtyCalcEBill(pa);
                        }
                    }
                }
            }
            /*
             删单状态下需要撤回手册的余量，只能根据清单回余量 ||"J3".equals(billEntryModel.getStatus())
             */
            if ("DL".equals(billEntryModel.getStatus())) {
                Example exampleIBillHead = new Example(DecIBillHead.class);
                Example.Criteria criteria = exampleIBillHead.createCriteria();
                criteria.andEqualTo("tradeCode", billEntryModel.getTradeCode());
                criteria.andEqualTo("emsListNo", billEntryModel.getEmsListNo());
                List<DecIBillHead> decIBillHeadList = decIBillHeadMapper.selectByExample(exampleIBillHead);
                for (DecIBillHead decIBillHead : decIBillHeadList) {
                    log.info(String.format(" -----------KAFKA接口开始更新清单回执信息，BC手册回余量开始【%s】 -------------", billEntryModel.getEmsListNo()));
                    if (!Strings.isNullOrEmpty(decIBillHead.getEmsNo()) && (CommonVariable.EMS_B.equals(decIBillHead.getEmsNo().substring(0
                            , 1)) || CommonVariable.EMS_C.equals(decIBillHead.getEmsNo().substring(0, 1)))) {
                        Map<String, Object> pa = new HashMap<String, Object>(4);
                        pa.put("P_BILL_HEAD_SID", decIBillHead.getSid());
                        pa.put("P_STATUS", billEntryModel.getStatus());
                        pa.put("P_ERR_MSG", "");
                        kafkaReturnInfoMapper.qtyCalcIBill(pa);
                    }
                    log.info(String.format(" -----------KAFKA接口开始更新清单回执信息，BC手册回余量结束【%s】 -------------", billEntryModel.getEmsListNo()));
                }
                Example exampleEBillHead = new Example(DecEBillHead.class);
                Example.Criteria criteriaE = exampleEBillHead.createCriteria();
                criteriaE.andEqualTo("tradeCode", billEntryModel.getTradeCode());
                criteriaE.andEqualTo("emsListNo", billEntryModel.getEmsListNo());
                List<DecEBillHead> decEBillHeadList = decEBillHeadMapper.selectByExample(exampleEBillHead);
                for (DecEBillHead decEBillHead : decEBillHeadList) {
                    if (!Strings.isNullOrEmpty(decEBillHead.getEmsNo()) && (CommonVariable.EMS_B.equals(decEBillHead.getEmsNo().substring(0, 1)) || CommonVariable.EMS_C.equals(decEBillHead.getEmsNo().substring(0, 1)))) {
                        Map<String, Object> pa = new HashMap<String, Object>(4);
                        pa.put("P_BILL_HEAD_SID", decEBillHead.getSid());
                        pa.put("P_STATUS", billEntryModel.getStatus());
                        pa.put("P_ERR_MSG", "");
                        kafkaReturnInfoMapper.qtyCalcEBill(pa);
                    }
                }
            }
            log.info(String.format(" -----------KAFKA接口结束更新清单回执信息 -------------", billEntryModel.getEmsListNo()));
        } catch (Exception ex) {
            assert message != null;
            log.error(String.format("调用api返回json入库失败,单据号：【%s】", JsonObjectMapper.getInstance().toJson(message.getData())), ex);
            throw new RuntimeException(ex);
        }
    }

}
