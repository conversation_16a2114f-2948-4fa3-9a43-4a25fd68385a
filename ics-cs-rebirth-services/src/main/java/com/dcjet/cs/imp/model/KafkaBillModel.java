package com.dcjet.cs.imp.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Getter;
import lombok.Setter;

/**
 * @Auther: Administrator
 * @Date: 2019/4/19 14:30
 * @Description:清单、报关单数据模型
 */
@Getter
@Setter
@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
public class KafkaBillModel extends  BaseModel {

    /**
     * 清单流水号
     */
    @JsonAlias("BILL_LIST_NO")
    private String billListNo;
    /**
     * 清单申报日期
     */
    @JsonAlias("DECLARE_DATE")
    private String declareDate;
    /**
     * 企业清单内部编号
     */
    @JsonAlias("EMS_LIST_NO")
    private String emsListNo;
    /**
     * 备案号
     */
    @JsonAlias("EMS_NO")
    private String emsNo;
    /**
     * 报关单申报日期
     */
    @JsonAlias("ENTRY_DECLARE_DATE")
    private String entryDeclareDate;
    /**
     * 报关单号
     */
    @JsonAlias("ENTRY_NO")
    private String entryNo;
    /**
     * 报关单状态
     */
    @JsonAlias("ENTRY_STATUS")
    private String entryStatus;
    /**
     * 清单编号
     */
    @JsonAlias("LIST_NO")
    private String listNo;
    /**
     * 清单预录入编号
     */
    @JsonAlias("PRE_SEQ_NO")
    private String preSeqNo;
    /**
     * 报关单统一编号
     */
    @JsonAlias("SEQ_NO")
    private String seqNo;
    /**
     * 状态
     */
    @JsonAlias("STATUS")
    private String status;
    /**
     * 企业编码
     */
    @JsonAlias("TRADE_CODE")
    private String tradeCode;
    private String bondMark;
}
