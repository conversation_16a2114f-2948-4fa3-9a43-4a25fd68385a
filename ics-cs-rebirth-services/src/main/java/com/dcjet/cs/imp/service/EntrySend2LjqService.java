package com.dcjet.cs.imp.service;

import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.entry.dao.DecEEntryHeadMapper;
import com.dcjet.cs.entry.dao.DecEEntryListMapper;
import com.dcjet.cs.entry.dao.DecIEntryHeadMapper;
import com.dcjet.cs.entry.dao.DecIEntryListMapper;
import com.dcjet.cs.entry.mapper.DecEEntryHeadDtoMapper;
import com.dcjet.cs.entry.mapper.DecEEntryListDtoMapper;
import com.dcjet.cs.entry.mapper.DecIEntryHeadDtoMapper;
import com.dcjet.cs.entry.mapper.DecIEntryListDtoMapper;
import com.dcjet.cs.entry.model.DecEEntryHead;
import com.dcjet.cs.entry.model.DecEEntryList;
import com.dcjet.cs.entry.model.DecIEntryHead;
import com.dcjet.cs.entry.model.DecIEntryList;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.imp.model.*;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.RequestUtil;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.google.common.base.Strings;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.internal.util.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: WJ
 * @createDate: 2021/4/6 11:10
 */
@Service
public class EntrySend2LjqService {

    private static final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private DecIEntryHeadMapper decIEntryHeadMapper;
    @Resource
    private DecIEntryHeadDtoMapper decIEntryHeadDtoMapper;
    @Resource
    private DecIEntryListMapper decIEntryListMapper;
    @Resource
    private DecIEntryListDtoMapper decIEntryListDtoMapper;
    @Resource
    private DecEEntryHeadMapper decEEntryHeadMapper;
    @Resource
    private DecEEntryHeadDtoMapper decEEntryHeadDtoMapper;
    @Resource
    private DecEEntryListMapper decEEntryListMapper;
    @Resource
    private DecEEntryListDtoMapper decEEntryListDtoMapper;
    @Resource
    private GwstdSpecialConfigService gwstdSpecialConfigService;
    @Resource
    private CommonService commonService;
    @Resource
    private PostApiReturnInfoService postApiReturnInfoService;


    /** 访问立交桥成功状态 */
    private final int successStatus = 0;

    /** 访问报关立交桥的错误状态，报关单数据错误 */
    private final int errStatusData = 1;
    /** 访问报关立交桥的错误状态，发送立交桥暂存接口失败 */
    private final int errStatusSave = 2;
     /** 访问报关立交桥的错误状态，调用立交桥发送到单一窗口接口失败 */
    private final int errStatusSend = 3;

    /**
     * 向立交桥申报
     * @param headIds
     * @param ieMark
     * @param userInfoToken
     * @return
     */
    public String decl2Ljq(List<String> headIds, String ieMark, UserInfoToken userInfoToken, String sendDest) {
        StringBuilder backMsg = new StringBuilder();
        for (String headId : headIds) {
            String backMessage = send2Ljq(headId, ieMark, userInfoToken, sendDest);
            if (StringUtils.isNotBlank(backMessage)) {
                backMsg.append(backMessage).append("|");
            }
        }
        return backMsg.toString();
    }

    private String send2Ljq(String headId, String ieMark, UserInfoToken userInfoToken, String sendDest) {
        // 记录调用报关立交桥接口成功失败结果，数组分别代表两个接口全部成功的数量，导入成功上载失败的数量，导入失败的数量
        int[] returnStatus = {0,0,0};
        // 需要发送数据
        List<String> needSendList = null;
        // 判断访问立交桥的接口类型，是否访问上载接口
        String genertalEnt = gwstdSpecialConfigService.selectByTradCode(userInfoToken.getCompany());
        String msg = "";
        String emsListNo = "";
        // 进口
        if (ieMark.equals(CommonVariable.IE_MARK_I)) {
            // 获取有效状态下的非保税数据
            DecIEntryHead decIEntryHead=new DecIEntryHead();
            decIEntryHead.setTradeCode(userInfoToken.getCompany());
            decIEntryHead.setSid(headId);

            List<DecIEntryHead> decIEntryHeads = decIEntryHeadMapper.getList(decIEntryHead);
            if (CollectionUtils.isEmpty(decIEntryHeads)) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未找到需要发送的报关单数据！"));
            }

            if (StringUtils.isNotBlank(decIEntryHeads.get(0).getEntryNo())&&StringUtils.isNotBlank(decIEntryHeads.get(0).getSeqNo())){
                return xdoi18n.XdoI18nUtil.t("报关单号已存在，不可发送");
            }

            emsListNo = decIEntryHeads.get(0).getEmsListNo();

            DecErpIHeadN decErpIHeadN = decErpIHeadNMapper.selectByPrimaryKey(decIEntryHeads.get(0).getErpHeadId());
            // 提单中有报关单的总净重，主管海关为空
            long errDataCount = decIEntryHeads.stream().filter(x ->
                    (x.getBondMark().equals(ConstantsStatus.STATUS_1) && StringHelper.isNullOrEmptyString(x.getMasterCustoms()))
                    || x.getNetWt() == null).count();
            // 需要发送的数据
            needSendList = decIEntryHeads.stream()
                    .filter(x -> Arrays.asList(DecVariable.SEND_STATUS_VALID).contains(x.getSendApiStatus()) && x.getBondMark().equals(ConstantsStatus.STATUS_1))
                    .map(DecIEntryHead::getSid).collect(Collectors.toList());

            // 数据校验
            msg = checkEntryData(decErpIHeadN.getApprStatus(), decIEntryHeads.get(0).getBondMark(), errDataCount,
                    genertalEnt, needSendList.size());
        } else {
            // 获取有效状态下的非保税数据
            DecEEntryHead decEEntryHead=new DecEEntryHead();
            decEEntryHead.setTradeCode(userInfoToken.getCompany());
            decEEntryHead.setSid(headId);

            List<DecEEntryHead> decEEntryHeads = decEEntryHeadMapper.getList(decEEntryHead);
            if (CollectionUtils.isEmpty(decEEntryHeads)) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未找到需要发送的报关单数据！"));
            }

            if (StringUtils.isNotBlank(decEEntryHeads.get(0).getEntryNo())&&StringUtils.isNotBlank(decEEntryHeads.get(0).getSeqNo())){
                return xdoi18n.XdoI18nUtil.t("报关单号已存在，不可发送");
            }

            emsListNo = decEEntryHeads.get(0).getEmsListNo();

            DecErpEHeadN decErpEHeadN = decErpEHeadNMapper.selectByPrimaryKey(decEEntryHeads.get(0).getErpHeadId());
            // 总净重或主管海关为空
            long errDataCount = decEEntryHeads.stream()
                    .filter(x -> (x.getBondMark().equals(ConstantsStatus.STATUS_1) && StringHelper.isNullOrEmptyString(x.getMasterCustoms()))
                            || x.getNetWt() == null).count();
            // 需要发送的数据
            needSendList = decEEntryHeads.stream()
                    .filter(x -> Arrays.asList(DecVariable.SEND_STATUS_VALID).contains(x.getSendApiStatus()) && x.getBondMark().equals(ConstantsStatus.STATUS_1))
                    .map(DecEEntryHead::getSid).collect(Collectors.toList());

            // 数据校验
            msg = checkEntryData(decErpEHeadN.getApprStatus(), decEEntryHeads.get(0).getBondMark(), errDataCount, genertalEnt, needSendList.size());
        }
        if (StringUtils.isNotBlank(msg)) {
            return xdoi18n.XdoI18nUtil.t("内部编号：[") + emsListNo + "]" + msg;
        }
        // 访问立交桥接口类型，true 导入和上载接口，false 导入接口
        if (genertalEnt.equals(ConstantsStatus.STATUS_1)) {
            sendList(needSendList, ieMark, userInfoToken, true, returnStatus, sendDest);
            return xdoi18n.XdoI18nUtil.t("内部编号：[") + emsListNo + "]," + returnStatus[0] + xdoi18n.XdoI18nUtil.t(" 单发送成功上载成功,\n ") + returnStatus[1]
                    + xdoi18n.XdoI18nUtil.t(" 单发送成功上载失败，\n ") + returnStatus[2] + " " + xdoi18n.XdoI18nUtil.t(" 单发送失败");
        } else {
            sendList(needSendList, ieMark, userInfoToken, false, returnStatus, sendDest);
            return xdoi18n.XdoI18nUtil.t("内部编号：[") + emsListNo + "]," + returnStatus[0] + xdoi18n.XdoI18nUtil.t(" 单发送成功\n ") + returnStatus[2] + xdoi18n.XdoI18nUtil.t(" 单发送失败");
        }
    }

    /**
     *  调用报关立交桥接口发送数据
     * @param needSendList
     * @param ieMark
     * @param userInfoToken
     * @param isSend
     * @param returnStatus
     */
    private void sendList(List<String> needSendList, String ieMark, UserInfoToken userInfoToken, boolean isSend, int[] returnStatus, String sendDest) {
        String taskId = UUID.randomUUID().toString();
        for (String sendId : needSendList) {
            int status = sendLjqEntry(sendId, ieMark, taskId, userInfoToken, isSend, sendDest);
            switch (status) {
                case 0:
                    returnStatus[0]++;
                    break;
                case 3:
                    returnStatus[1]++;
                    break;
                case 2:
                    returnStatus[2]++;
                    break;
                default:
                    logger.info("调用报关立交桥接口发送数据返回状态为：{}", status);
                    break;
            }
        }
    }

    /**
     * 数据、状态检查
     * @param erpApprStatus
     * @param bondMark
     * @param size
     * @param genertalEnt
     * @param sendSize
     */
    private String checkEntryData(String erpApprStatus, String bondMark, long size, String genertalEnt, int sendSize) {
        // 是否可操作 内审通过才能操作
        if (!CommonVariable.APPR_STATUS_8.equals(erpApprStatus)) {
            return xdoi18n.XdoI18nUtil.t("当前单据状态不可发送立交桥报关单！");
        }
        // 总净重或主管海关为空
        if (size != 0) {
            if (StringUtils.equals(bondMark, CommonEnum.BondMarkEnum.BOND.getCode())) {
                return xdoi18n.XdoI18nUtil.t("报关单数据中存在总净重为空的数据！");
            } else {
                return xdoi18n.XdoI18nUtil.t("报关单数据中存在总净重或申报地海关(主管海关)为空的数据！");
            }
        }

        if (StringUtils.isBlank(genertalEnt)) {
            return xdoi18n.XdoI18nUtil.t("报关单发送方式未配置，请联系系统管理员!");
        }

        // 不存在需要发送的数据
        if (sendSize == 0) {
            return xdoi18n.XdoI18nUtil.t("未找到需要发送的报关单数据！");
        }
        return null;
    }

    /**
     * 功能描述:调用报关立交桥接口发送数据
     *
     * @param headId
     * @param ieMark
     * @param userInfo
     * @param isSend 企业是否要调用立交桥上载接口
     */
    public int sendLjqEntry(String headId, String ieMark, String taskId, UserInfoToken userInfo, boolean isSend, String sendDest) {
        logger.info(String.format("【***************推送立交桥接口信息开始(任务id:%s)*****************】", taskId));
        Map<String, Object> map = new HashMap<>(3);
        int ljqStatus;
        if(ieMark.equals(CommonVariable.IE_MARK_I)){
            ljqStatus = send2LjqI(headId, taskId, isSend, map, sendDest);
        }else{
            ljqStatus = send2LjqE(headId, taskId, isSend, map, sendDest);
        }
        // 错误信息
        String errMessage = (String) map.get("errMessage");
        // 存在错误信息则记录错误信息
        if (StringUtils.isNotBlank(errMessage)) {
            logger.info("错误信息：{}", errMessage);
            postApiReturnInfoService.selfInsertLjq(taskId,userInfo.getUserNo(),userInfo.getCompany(),headId
                    ,ieMark.equals(CommonVariable.IE_MARK_I)?postApiReturnInfoService.I_ENTRY:postApiReturnInfoService.E_ENTRY
                    ,errMessage, CommonEnum.ApiType.BGLJQ_API.getValue(),false, (String) map.get("ljqNo"));
//            commonService.recodeRequestLog(userInfo, "LjqEntry", ljqStatus, CommonEnum.ApiType.BGLJQ_API.getValue(),
//                    errMessage);
        }
        logger.info("【***************推送报关单信息结束*****************】");
        return ljqStatus;
    }

    private int send2LjqE(String headId, String taskId, boolean isSend, Map<String, Object> m, String sendDest) {
        DecEEntryHead decEEntryHead = decEEntryHeadMapper.selectByPrimaryKey(headId);
        // 立交桥暂存后返回的编号
        String ljqNo = decEEntryHead.getLjqNo();
        logger.info("立交桥暂存后返回的编号:{}", ljqNo);
        m.put("ljqNo", ljqNo);
        int ljqStatus = successStatus;
        if (StringUtils.isBlank(ljqNo)) {
            // 进口报关单数据
            EntryHead entryHead = decEEntryHeadDtoMapper.toEnt(decEEntryHead);
            buildEntryHead(CommonVariable.IE_MARK_I, entryHead);

            DecEEntryList decEEntryList=new DecEEntryList();
            decEEntryList.setHeadId(decEEntryHead.getSid());
            decEEntryList.setTradeCode(decEEntryHead.getTradeCode());
            List<DecEEntryList> decEEntryLists = decEEntryListMapper.selectListByParam(decEEntryList);

            if (CollectionUtils.isEmpty(decEEntryLists)) {
                //如果表体为空，直接返回
                ljqStatus = errStatusData;
                return ljqStatus;
            }
            List<EntryList> entryLists = new ArrayList<>();
            for (DecEEntryList decEEntryListItem : decEEntryLists) {
                // 报关立交桥中出口表体中 原产国和目的国字段是反的
                String country = decEEntryListItem.getOriginCountry();
                decEEntryListItem.setOriginCountry(decEEntryListItem.getDestinationCountry());
                decEEntryListItem.setDestinationCountry(country);

                entryLists.add(decEEntryListDtoMapper.toEnt(decEEntryListItem));
            }

            EntryEntity entryEntity = new EntryEntity();
            // 报关单表头
            entryEntity.setEntryHead(entryHead);
            // 报关单表体
            entryEntity.setEntryList(entryLists);
            entryEntity.setEntryCertificate(null);
            entryEntity.setEntryContainer(null);
            entryEntity.setEntryDocument(null);
            entryEntity.setEntryTranEntity(null);
            entryEntity.setEntryTranListEntity(null);

            BgljqRequest bgljqRequest = new BgljqRequest();
            bgljqRequest.setServiceName("EntryImport");
            bgljqRequest.setEntry(entryEntity);

            try {
                GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.LJQ_ENTRY_URL);
                if (null == gwstdHttpConfig || Strings.isNullOrEmpty(gwstdHttpConfig.getBaseUrl()) || Strings.isNullOrEmpty(gwstdHttpConfig.getServiceUrl())) {
                    m.put("errMessage", xdoi18n.XdoI18nUtil.t("未配置报关立交桥api地址！"));
                    ljqStatus = errStatusSend;
                    return ljqStatus;
                }
                if (Strings.isNullOrEmpty(gwstdHttpConfig.getToken())) {
                    m.put("errMessage", xdoi18n.XdoI18nUtil.t("未配置报关立交桥请求token！"));
                    ljqStatus = errStatusSend;
                    return ljqStatus;
                }
                bgljqRequest.setToKen(gwstdHttpConfig.getToken());
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                String postData= jsonObjectMapper.toJson(bgljqRequest);
                logger.info(String.format("报关立交桥导入接口请求数据，%s", postData));
                String url = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();
                logger.info("报关立交桥api地址:{}", url);
                logger.info(String.format("****Begin本次发送的清单内部编号为：%s****", decEEntryHead.getEmsListNo()));
                String str = RequestUtil.sendPost(url, null, postData);
                logger.info(String.format("报关立交桥导入接口返回数据，%s", str));
                BgliqResult bgliqResult = jsonObjectMapper.fromJson(str, BgliqResult.class);

                // 请求失败
                if (bgliqResult.getResultCode() != 1) {
                    logger.info(String.format("报关立交桥导入立交桥接口调用失败，%s", bgliqResult.getResultMessage()));
                    m.put("errMessage", xdoi18n.XdoI18nUtil.t("导入立交桥接口失败：") + bgliqResult.getResultMessage());
                    ljqStatus = errStatusSave;
                } else {
                    // 更新报关单 发送成功,调用立交桥发送单一窗口接口
                    ljqNo = new String(bgliqResult.getResultBytes());
                    logger.info(String.format("报关立交桥导入成功，返回ljqNo 为%s", ljqNo));
                    // 判断企业是否需要调用上载接口
                    if (isSend) {
                        ljqStatus = sendLjqSingleEntryBill(ljqNo, m, headId, taskId, CommonVariable.IE_MARK_E, sendDest);
                    } else {
                        decEEntryHeadMapper.updateHgNo(headId, ConstantsStatus.STATUS_1, taskId, ljqNo,null,
                                ConstantsStatus.STATUS_1, sendDest);
                        ljqStatus = successStatus;
                    }
                }
            } catch (Exception ex) {
                logger.error("本次发送异常：", ex);
                m.put("errMessage", xdoi18n.XdoI18nUtil.t("导入立交桥接口失败：") + ex.getMessage());
                ljqStatus = errStatusSave;
            }
        } else {
            // 上次导入成功但是调用立交桥上载接口失败，再次调用上载接口
            ljqStatus = sendLjqSingleEntryBill(ljqNo, m, headId, taskId, CommonVariable.IE_MARK_E, sendDest);
        }
        return ljqStatus;
    }

    private int send2LjqI(String headId, String taskId, boolean isSend, Map<String, Object> m, String sendDest) {
        DecIEntryHead decIEntryHead = decIEntryHeadMapper.selectByPrimaryKey(headId);
        String ljqNo = decIEntryHead.getLjqNo();
        m.put("ljqNo", ljqNo);
        int ljqStatus = successStatus;
        if (StringUtils.isBlank(ljqNo)) {
            // 出口报关单表头数据
            EntryHead entryHead = decIEntryHeadDtoMapper.toEnt(decIEntryHead);
            buildEntryHead(CommonVariable.IE_MARK_I, entryHead);

            // 出口报关单表体数据
            DecIEntryList decIEntryList=new DecIEntryList();
            decIEntryList.setHeadId(decIEntryHead.getSid());
            decIEntryList.setTradeCode(decIEntryHead.getTradeCode());
            List<DecIEntryList> decIEntryLists = decIEntryListMapper.selectListByParam(decIEntryList);
            if (CollectionUtils.isEmpty(decIEntryLists)) {
                // 如果表体为空，直接返回
                ljqStatus = errStatusData;
                return ljqStatus;
            }
            List<EntryList> entryLists = new ArrayList<>();
            for (DecIEntryList decIEntryListItem : decIEntryLists) {
                entryLists.add(decIEntryListDtoMapper.toEnt(decIEntryListItem));
            }

            EntryEntity entryEntity = new EntryEntity();
            // 报关单表头
            entryEntity.setEntryHead(entryHead);
            // 报关单表体
            entryEntity.setEntryList(entryLists);
            entryEntity.setEntryCertificate(null);
            entryEntity.setEntryContainer(null);
            entryEntity.setEntryDocument(null);
            entryEntity.setEntryTranEntity(null);
            entryEntity.setEntryTranListEntity(null);

            BgljqRequest bgljqRequest = new BgljqRequest();
            bgljqRequest.setServiceName("EntryImport");
            bgljqRequest.setEntry(entryEntity);

            try {
                GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.LJQ_ENTRY_URL);
                if (null == gwstdHttpConfig || Strings.isNullOrEmpty(gwstdHttpConfig.getBaseUrl()) || Strings.isNullOrEmpty(gwstdHttpConfig.getServiceUrl())) {
                    m.put("errMessage", xdoi18n.XdoI18nUtil.t("未配置报关立交桥api地址！"));
                    ljqStatus = errStatusSend;
                    return ljqStatus;
                }
                if (Strings.isNullOrEmpty(gwstdHttpConfig.getToken())) {
                    m.put("errMessage", xdoi18n.XdoI18nUtil.t("未配置报关立交桥请求token！"));
                    ljqStatus = errStatusSend;
                    return ljqStatus;
                }
                bgljqRequest.setToKen(gwstdHttpConfig.getToken());
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                String postData= jsonObjectMapper.toJson(bgljqRequest);
                logger.info(String.format("报关立交桥导入接口请求数据，%s", postData));
                String url = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();
                logger.info("报关立交桥api地址:{}", url);
                logger.info(String.format("****Begin本次发送的清单内部编号为：%s****", decIEntryHead.getEmsListNo()));
                String str = RequestUtil.sendPost(url, null, postData);
                logger.info(String.format("报关立交桥导入接口返回数据，%s", str));
                BgliqResult bgliqResult = jsonObjectMapper.fromJson(str, BgliqResult.class);

                //请求失败
                if (bgliqResult.getResultCode() != 1) {
                    logger.info(String.format("报关立交桥导入立交桥接口调用失败，%s", bgliqResult.getResultMessage()));
                    m.put("errMessage", xdoi18n.XdoI18nUtil.t("导入立交桥接口失败：") + bgliqResult.getResultMessage());
                    ljqStatus = errStatusSave;
                } else {
                    // 更新报关单 发送成功,调用立交桥发送单一窗口接口
                    ljqNo = new String(bgliqResult.getResultBytes());
                    logger.info(String.format("报关立交桥导入成功，返回ljqNo 为%s", ljqNo));
                    // 判断企业是否需要调用上载接口
                    if (isSend) {
                        ljqStatus = sendLjqSingleEntryBill(ljqNo, m, headId, taskId, CommonVariable.IE_MARK_I, sendDest);
                    } else {
                        decIEntryHeadMapper.updateHgNo(headId, ConstantsStatus.STATUS_1, taskId, ljqNo,null, ConstantsStatus.STATUS_1, sendDest);
                        ljqStatus = successStatus;
                    }
                }
            } catch (Exception ex) {
                logger.error("本次发送异常：", ex);
                m.put("errMessage", xdoi18n.XdoI18nUtil.t("导入立交桥接口失败：") + ex.getMessage());
                ljqStatus = errStatusSave;
            }
        } else {
            // 上次导入成功但是调用立交桥上载接口失败，再次调用上载接口
            ljqStatus = sendLjqSingleEntryBill(ljqNo, m, headId, taskId, CommonVariable.IE_MARK_I, sendDest);
        }

        return ljqStatus;
    }

    private void buildEntryHead(String ieMark, EntryHead entryHead) {
        // 报关立交桥出口字段显示境外收货人编码和名称字段
        entryHead.setOverseasconsigneecode("");
        entryHead.setOverseasconsignorcode("");
        entryHead.setOverseasconsignorename(entryHead.getOverseasconsignorcname());
        entryHead.setDomesticconsigneeename(entryHead.getOverseasconsignorcname());
        entryHead.setOverseasconsigneeename(entryHead.getOverseasconsignorcname());
        // 报关类型默认设置005
        entryHead.setDeclarationType("005");
        entryHead.setIEMark(ieMark);
        // 数据所属企业（报关行：对应申报单位代码；企业：对应经营单位代码）
        entryHead.setDataOwnCode(entryHead.getTradeCode());
        // 数据库字段和要求的立交桥字段不一致,将不一致的改为一致
        entryHead.setEntryType(StringUtils.isNotBlank(entryHead.getEntryType()) ? "00" + entryHead.getEntryType() : "001");
        entryHead.setEntryTransitType(StringUtils.isNotBlank(entryHead.getEntryTransitType()) ? "00" + entryHead.getEntryTransitType() : "001");
        entryHead.setCheckSurety(StringUtils.isNotBlank(entryHead.getCheckSurety()) ? "00" + entryHead.getCheckSurety() : "002");
        entryHead.setBillType(StringUtils.isNotBlank(entryHead.getBillType()) ? "00" + entryHead.getBillType() : "001");
        //价格说明 根据数据库中1,2,3,4 选择前三位是否选择
        String promiseItems = "";
        if (StringUtils.isNotBlank(entryHead.getPromiseItmes())) {
            if (entryHead.getPromiseItmes().contains(DecVariable.PROMISE_ITEM_1)) {
                promiseItems += ConstantsStatus.STATUS_1;
            } else {
                promiseItems += ConstantsStatus.STATUS_0;
            }
            if (entryHead.getPromiseItmes().contains(DecVariable.PROMISE_ITEM_2)) {
                promiseItems += ConstantsStatus.STATUS_1;
            } else {
                promiseItems += ConstantsStatus.STATUS_0;
            }
            if (entryHead.getPromiseItmes().contains(DecVariable.PROMISE_ITEM_3)) {
                promiseItems += ConstantsStatus.STATUS_1;
            } else {
                promiseItems += ConstantsStatus.STATUS_0;
            }
        } else {
            promiseItems = "000";
        }
        entryHead.setPromiseItmes(promiseItems);
    }

    /**
     * 功能描述:发送进出口清单备案信息到立交桥后，再次调用立交桥接口，发送数据到海关
     * @param taskId
     * @param m
     * @param headId
     * @param ljqNo 立交桥暂存后返回的编号
     */
    public int sendLjqSingleEntryBill(String ljqNo, Map<String, Object> m, String headId, String taskId, String ieMark, String sendDest) {
        int ljqStatus = successStatus;
        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        BgljqUploadRequest bgljqUplodeRequest = new BgljqUploadRequest();
        bgljqUplodeRequest.setServiceName("EntryUpload");
        bgljqUplodeRequest.setEntryListNo(ljqNo);
        GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.LJQ_ENTRY_URL);
        if (null == gwstdHttpConfig || Strings.isNullOrEmpty(gwstdHttpConfig.getBaseUrl()) || Strings.isNullOrEmpty(gwstdHttpConfig.getServiceUrl())) {
            m.put("errMessage", xdoi18n.XdoI18nUtil.t("未配置报关立交桥api地址！"));
            return errStatusSend;
        }
        if (Strings.isNullOrEmpty(gwstdHttpConfig.getToken())) {
            m.put("errMessage", xdoi18n.XdoI18nUtil.t("未配置报关立交桥请求token！"));
            return errStatusSend;
        }
        bgljqUplodeRequest.setToKen(gwstdHttpConfig.getToken());

        String postData= jsonObjectMapper.toJson(bgljqUplodeRequest);
        logger.info(String.format("报关立交桥上载接口请求数据，%s", postData));
        String url = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();
        logger.info("报关立交桥api地址:{}", url);
        try {
            String str = RequestUtil.sendPost(url, null, postData);
            logger.info(String.format("报关立交桥上载接口返回数据，%s", str));
            BgliqResult bgliqResult = jsonObjectMapper.fromJson(str, BgliqResult.class);
            //请求失败
            if (bgliqResult.getResultCode() != 1) {
                logger.info(String.format("上载立交桥接口失败，%s", bgliqResult.getResultMessage()));
                m.put("errMessage", xdoi18n.XdoI18nUtil.t("上载立交桥接口失败：") + bgliqResult.getResultMessage());
                ljqStatus = errStatusSend;
            } else {
                //报关立交桥上载成功
                logger.info("立交桥上载成功");
                if (ieMark.equals(CommonVariable.IE_MARK_I)) {
                    decIEntryHeadMapper.updateHgNo(headId, ConstantsStatus.STATUS_1, taskId, ljqNo,null, ConstantsStatus.STATUS_1, sendDest);
                } else {
                    decEEntryHeadMapper.updateHgNo(headId, ConstantsStatus.STATUS_1, taskId, ljqNo,null, ConstantsStatus.STATUS_1, sendDest);
                }
            }
        } catch (Exception ex) {
            logger.error("本次发送异常：", ex);
            m.put("errMessage", xdoi18n.XdoI18nUtil.t("上载立交桥接口失败：") + ex.getMessage());
            ljqStatus = errStatusSend;
        }
        return ljqStatus;
    }

    /**
     * 大提单发送立交桥报关
     * @param headId
     * @param ieMark
     * @param userInfoToken
     * @param sendDest
     * @return
     */
    public String decl2LjqByErp(String headId, String ieMark, UserInfoToken userInfoToken, String sendDest) {
// 记录调用报关立交桥接口成功失败结果，数组分别代表两个接口全部成功的数量，导入成功上载失败的数量，导入失败的数量
        int[] returnStatus = {0,0,0};

        // 需要发送数据
        List<String> needSendList = null;
        // 判断访问立交桥的接口类型，是否访问上载接口
        String genertalEnt = gwstdSpecialConfigService.selectByTradCode(userInfoToken.getCompany());
        String msg = "";
        String emsListNo = "";
        // 进口
        if (ieMark.equals(CommonVariable.IE_MARK_I)) {
            DecErpIHeadN decErpIHeadN = decErpIHeadNMapper.selectByPrimaryKey(headId);

            DecIEntryHead decIEntryHead=new DecIEntryHead();
            decIEntryHead.setTradeCode(userInfoToken.getCompany());
            decIEntryHead.setErpHeadId(headId);

            // 获取有效状态下的非保税数据
            List<DecIEntryHead> decIEntryHeads = decIEntryHeadMapper.getList(decIEntryHead);
            if (CollectionUtils.isEmpty(decIEntryHeads)) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未找到需要发送的报关单数据！"));
            }
            // 数据校验
            for (DecIEntryHead x : decIEntryHeads) {
                // 提单中有报关单的总净重，主管海关为空
                long errDataCount = 0L;
                if ((x.getBondMark().equals(ConstantsStatus.STATUS_1) && StringHelper.isNullOrEmptyString(x.getMasterCustoms()))
                        || x.getNetWt() == null) {
                    errDataCount = 1L;
                }
                int sendSize = 0;
                if (Arrays.asList(DecVariable.SEND_STATUS_VALID).contains(x.getSendApiStatus()) && x.getBondMark().equals(ConstantsStatus.STATUS_1)) {
                    sendSize = 1;
                }
                String errMsg = checkEntryData(decErpIHeadN.getApprStatus(), x.getBondMark(), errDataCount, genertalEnt, sendSize);
                if (StringUtils.isBlank(errMsg)) {
                    needSendList.add(x.getSid());
                } else {
                    msg = msg + xdoi18n.XdoI18nUtil.t("内部编号[") + x.getEmsListNo() + "]," + errMsg + "|";
                }
            }
        } else {
            DecErpEHeadN decErpEHeadN = decErpEHeadNMapper.selectByPrimaryKey(headId);
            DecEEntryHead decEEntryHead=new DecEEntryHead();
            decEEntryHead.setTradeCode(userInfoToken.getCompany());
            decEEntryHead.setErpHeadId(headId);
            // 获取有效状态下的非保税数据
            List<DecEEntryHead> decEEntryHeads = decEEntryHeadMapper.getList(decEEntryHead);
            if (CollectionUtils.isEmpty(decEEntryHeads)) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未找到需要发送的报关单数据！"));
            }
            // 数据校验
            for (DecEEntryHead x : decEEntryHeads) {
                // 提单中有报关单的总净重，主管海关为空
                long errDataCount = 0L;
                if ((x.getBondMark().equals(ConstantsStatus.STATUS_1) && StringHelper.isNullOrEmptyString(x.getMasterCustoms()))
                        || x.getNetWt() == null) {
                    errDataCount = 1L;
                }
                int sendSize = 0;
                if (Arrays.asList(DecVariable.SEND_STATUS_VALID).contains(x.getSendApiStatus()) && x.getBondMark().equals(ConstantsStatus.STATUS_1)) {
                    sendSize = 1;
                }
                String errMsg = checkEntryData(decErpEHeadN.getApprStatus(), x.getBondMark(), errDataCount, genertalEnt, sendSize);
                if (StringUtils.isBlank(errMsg)) {
                    needSendList.add(x.getSid());
                } else {
                    msg = msg + xdoi18n.XdoI18nUtil.t("内部编号[") + x.getEmsListNo() + "]," + errMsg + "|";
                }
            }
        }
        if (StringUtils.isNotBlank(msg)) {
            return xdoi18n.XdoI18nUtil.t("内部编号：[") + emsListNo + "]" + msg;
        }
        // 访问立交桥接口类型，true 导入和上载接口，false 导入接口
        if (genertalEnt.equals(ConstantsStatus.STATUS_1)) {
            sendList(needSendList, ieMark, userInfoToken, true, returnStatus, sendDest);
            return returnStatus[0] + xdoi18n.XdoI18nUtil.t(" 单发送成功上载成功,\n ") + returnStatus[1]
                    + xdoi18n.XdoI18nUtil.t(" 单发送成功上载失败，\n ") + returnStatus[2] + " " + xdoi18n.XdoI18nUtil.t(" 单发送失败");
        } else {
            sendList(needSendList, ieMark, userInfoToken, false, returnStatus, sendDest);
            return returnStatus[0] + xdoi18n.XdoI18nUtil.t(" 单发送成功\n ") + returnStatus[2] + xdoi18n.XdoI18nUtil.t(" 单发送失败");
        }
    }
}
