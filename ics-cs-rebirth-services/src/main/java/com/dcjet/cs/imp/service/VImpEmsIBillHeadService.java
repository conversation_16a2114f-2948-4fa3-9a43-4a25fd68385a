package com.dcjet.cs.imp.service;

import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.imp.*;
import com.dcjet.cs.dto.war.WarringPassConfigDto;
import com.dcjet.cs.erp.dao.DecIBillHeadMapper;
import com.dcjet.cs.imp.dao.VImpEmsIBillHeadMapper;
import com.dcjet.cs.imp.dao.VImpEmsIBillListMapper;
import com.dcjet.cs.imp.mapper.VImpEmsIBillHeadDtoMapper;
import com.dcjet.cs.imp.mapper.VImpEmsIBillListDtoMapper;
import com.dcjet.cs.imp.model.BillCancelBody;
import com.dcjet.cs.imp.model.VImpEmsIBillHead;
import com.dcjet.cs.metaData.config.WcfFunction;
import com.dcjet.cs.metaData.main.CallMetaDataUtil;
import com.dcjet.cs.metaData.wcf.ServiceStub;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.DateUtil;
import com.dcjet.cs.util.pojo.BillApiReturn;
import com.dcjet.cs.util.pojo.BillHeadApiReturn;
import com.dcjet.cs.util.pojo.BillListApiReturn;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * Service;
 *
 * @author: 鏈辨涓�
 * @date: 2019-04-18
 */
@Service
public class VImpEmsIBillHeadService extends BaseService<VImpEmsIBillHead> {
    private static final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Resource
    private VImpEmsIBillHeadMapper mapperHead;
    @Resource
    private VImpEmsIBillListMapper mapperBody;
    @Resource
    private PostApiReturnInfoService postApiReturnInfoService;
    @Resource
    private VImpEmsIBillHeadDtoMapper dtoHeadMapper;
    @Resource
    private VImpEmsIBillListDtoMapper dtoBodyMapper;
    @Resource
    private BaseCallApi baseCallApi;
    /**
     * 元数据请求工具类
     */
    @Resource
    private CallMetaDataUtil callMetaDataUtil;
    //备案数据出口清单Mapper
    @Resource
    private DecIBillHeadMapper decIBillHeadMapper;
    @Override
    public Mapper<VImpEmsIBillHead> getMapper() {
        return mapperHead;
    }

    @Resource
    private CommonService commonService;
    @Resource
    private WarringPassConfigService warringPassConfigService;

    /**
     * 功能描述:发送进口清单需信息
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date： 2018-11-20
     * @param: listESids 需要发送的清单sid
     * @param: userInfo 用户信息
     * @return:
     */
    public String sendEmsIBill(List<String> listESids, UserInfoToken userInfo) {

        List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(userInfo);

        String result = "";
        //覆盖 先删后插入
        String importType = CommonEnum.ImportType.Cover.getValue();
        //api类型
        String apiType = CommonEnum.ApiType.INVT_API.getValue();
        logger.info("【***************推送金二进口清单信息开始(任务id:%s)*****************】");
        logger.info("获取清单信息");
        List<VImpEmsIBillHeadDto> billHeads = dtoHeadMapper.toDtoList(mapperHead.getEmsIBillHeadBySids(listESids));
        logger.info("根据表头依次请求api");
        for (VImpEmsIBillHeadDto head : billHeads) {
            //当报关单类型为X（进口两步申报报关单）或者Y（进口两步申报备案清单）时，该“报关单号”栏位必填；否则，该栏位只能为空
            if(StringUtils.isNotEmpty(head.getEntryType())&&!"X".equals(head.getEntryType().toUpperCase()))
            {
                head.setEntryNo(null);
            }
            /**
             * 当清单类型为以下几种时，填写该栏位，否则填写空
             * 1：集报清单
             * 5：保税展示交易
             * 4：简单加工
             * 9：一纳成品内销
             */
            if(!CollectionUtils.containsAny(Arrays.asList("1", "5", "4", "9"),head.getListType())) {
                head.setRotateApplyNo(null);
            }
            String taskId = java.util.UUID.randomUUID().toString();
            logger.info(String.format("****Begin本次发送的手（账）号为：%s  清单内部编号为：%s", head.getEmsNo(), head.getEmsListNo()));
//            DecIBillHead decIBillHead=decIBillHeadMapper.selectByPrimaryKey(head.getSid());
            //if(decIBillHead.getSendApiStatus())
            JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
            List<InvtHead<VImpEmsIBillHeadDto, VImpEmsIBillListDto>> invtIHeadList = new ArrayList();
            List<VImpEmsIBillListDto> list = mapperBody.getEmsIBillListByHeadSid(head.getSid())
                    .parallelStream()
                    .map(x->{
                        //如果是边角料，不清除边角料信息
                        if(!"0844、0845、0864、0865".contains(head.getTradeMode()))
                        {
                            x.setCodeTS(null);
                            x.setGName(null);
                            x.setUnit(null);
                        }
                        if (warringPassConfigDtos != null) {
                            if (warringPassConfigDtos.size() > 0) {
                                WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
                                if (!StringUtils.equals(warringPassConfigDto.getProductActivation(),ConstantsStatus.STATUS_1)){
                                    x.setUseType(null);
                                }
                            }
                        }
                        return  dtoBodyMapper.toDto(x); })
                    .collect(Collectors.toList());
            //拼装表头表体数据
            invtIHeadList.add(new InvtHead<VImpEmsIBillHeadDto, VImpEmsIBillListDto>() {{
                setInvthead(head);
                setInvtlists(list);
            }});
            boolean blnIsError = false;
            String strErrorMsg = "";
            try {
                GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.SEND_EMS_BILL_LIST);
                if (null == gwstdHttpConfig || Strings.isNullOrEmpty(gwstdHttpConfig.getBaseUrl()) || Strings.isNullOrEmpty(gwstdHttpConfig.getServiceUrl())) {
                    return xdoi18n.XdoI18nUtil.t("未配置发送清单备案地址！");
                }
                String url = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();
                logger.info("发送清单备案地址:{}", url);
                String postData = jsonObjectMapper.toJson(invtIHeadList);
                logger.info("发送清单备案数据:{}", postData);
                ResultModel<InvtHead<VImpEmsIBillHeadDto, VImpEmsIBillListDto>> resultModel= jsonObjectMapper.fromJson(
                        baseCallApi.callEmsApi(null, head.getEmsNo(), importType, taskId, invtIHeadList,
                                userInfo.getAccessToken(), url)
                        , new TypeReference<ResultModel<InvtHead<VImpEmsIBillHeadDto, VImpEmsIBillListDto>>>() {
                        });

                logger.info("更新本次发送时间");
                //更新发送时间
                decIBillHeadMapper.updateSendApiTimeBySid(head.getSid());

                if (!resultModel.isSuccess()) {
                    logger.info(String.format("调用金二接口返回错误,异常信息%s", resultModel.getMessage()));
                    blnIsError = true;
                    strErrorMsg = head.getEmsListNo()+ resultModel.getMessage();
                    result += xdoi18n.XdoI18nUtil.t("发送失败,调用金二接口返回错误,清单内部编号：") + strErrorMsg;
                } else {
                    if (resultModel.getData().getErrorDataCount() > 0) {
                        //插入异常数据
                        postApiReturnInfoService.selfInsertBill(taskId, userInfo.getUserNo(), userInfo.getCompany(), head.getSid()
                                , postApiReturnInfoService.I_BILL
                                , jsonObjectMapper.toJson(resultModel.getData().getErrorData().get(0)), apiType, false);
                        result += xdoi18n.XdoI18nUtil.t("发送失败,调用金二接口返回错误,清单内部编号：") + head.getEmsListNo();
                    } else {
                        logger.info("本api校验正常，更新本次发送单据状态");
                        /*
                        接口返回成功,发送后更新各种发送类型状态,发送状态为1 发送成功，明细状态为1 锁定
                         */
                        decIBillHeadMapper.updateSendApiStatusBySid(head.getSid(), ConstantsStatus.STATUS_1, taskId,ConstantsStatus.STATUS_1);
                    }
                }
            } catch (Exception ex) {
                logger.error("本次发送异常,调用金二接口异常：{}", ex);
                blnIsError = true;
                strErrorMsg = xdoi18n.XdoI18nUtil.t("本次发送异常,调用金二接口异常：") + head.getEmsListNo()+ ex.getMessage();
                result += strErrorMsg;
            } finally {
                if (blnIsError) {

                    logger.error("api请求异常数据入库");
                    head.setTempRemark(strErrorMsg);
                    postApiReturnInfoService.selfInsertBill(taskId, userInfo.getUserNo(), userInfo.getCompany(), head.getSid()
                            , postApiReturnInfoService.I_BILL
                            , jsonObjectMapper.toJson(invtIHeadList.get(0)), apiType, false);
                }
                logger.info("****End本次发送的完成");
            }
        }
        logger.info("【***************推送进口清单信息结束*****************】");
        return result;
    }

    /**
     * 功能描述:发送进口清单信息（2010）
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date： 2018-11-20
     * @param: listESids 需要发送的清单sid
     * @param: userInfo 用户信息
     * @return:
     */
    public String sendEmsIBillH2000(List<String> listESids, UserInfoToken userInfo) {

        List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(userInfo);

        String result = "";
        //覆盖 先删后插入
        String importType = CommonEnum.ImportType.Cover.getValue();
        //api类型
        String apiType = CommonEnum.ApiType.INVT_API.getValue();
        logger.info("【***************推送H2000进口清单信息开始(任务id:%s)*****************】");
        logger.info("获取清单信息");
        List<VImpEmsIBillHeadH2000Dto> billHeads = dtoHeadMapper.toH2000DtoList(mapperHead.getEmsIBillHeadBySids(listESids));
        logger.info("根据表头依次请求api");
        for (VImpEmsIBillHeadH2000Dto head : billHeads) {
            String taskId = java.util.UUID.randomUUID().toString();
            logger.info(String.format("****Begin本次发送的手（账）号为：%s  清单内部编号为：%s", head.getEmsNo(), head.getEmsListNo()));
            JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
            List<InvtHead<VImpEmsIBillHeadH2000Dto, VImpEmsIBillListH2000Dto>> invtIHeadList = new ArrayList();
            List<VImpEmsIBillListH2000Dto> list = mapperBody.getEmsIBillListByHeadSid(head.getSid())
                                                  .parallelStream()
                                                   .map(x->{
                                                       if (warringPassConfigDtos != null) {
                                                           if (warringPassConfigDtos.size() > 0) {
                                                               WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
                                                               if (!StringUtils.equals(warringPassConfigDto.getProductActivation(),ConstantsStatus.STATUS_1)){
                                                                   x.setUseType(null);
                                                               }
                                                           }
                                                       }
                                                       return  dtoBodyMapper.toH2000Dto(x); })
                                                   .collect(Collectors.toList());
            //拼装表头表体数据
            invtIHeadList.add(new InvtHead<VImpEmsIBillHeadH2000Dto, VImpEmsIBillListH2000Dto>() {{
                setInvthead(head);
                setInvtlists(list);
            }});
            boolean blnIsError = false;
            String strErrorMsg = "";
            try {
                ServiceStub.OperateInfo operateInfo = new ServiceStub.OperateInfo();
                operateInfo.setTradeCodeKey(String.format("%s_IN", userInfo.getCompany()));
                operateInfo.setEmsNo(head.getEmsNo());
                operateInfo.setImportType(4);
                operateInfo.setUserId(userInfo.getCompany());
                operateInfo.setPwd(userInfo.getCompany());
                operateInfo.setInfoData(DateUtil.createDataHandler(baseCallApi.convertToPostData(null, head.getEmsNo(), importType, taskId, invtIHeadList)));
                ResultModel<InvtHead<VImpEmsIBillHeadH2000Dto, VImpEmsIBillListH2000Dto>> resultModel = callMetaDataUtil.CallWcf(
                        WcfFunction.ImpEmsBillUnion, operateInfo, new TypeReference<ResultModel<InvtHead<VImpEmsIBillHeadH2000Dto, VImpEmsIBillListH2000Dto>>>() {
                        });

                logger.info("更新本次发送时间");
                //更新发送时间
                decIBillHeadMapper.updateSendApiTimeBySid(head.getSid());

                if (!resultModel.isSuccess()) {
                    logger.info(String.format("本api请求异常,异常信息%s", resultModel.getMessage()));
                    blnIsError = true;
                    strErrorMsg = resultModel.getMessage();
                    result += strErrorMsg;
                } else {
                    if (resultModel.getData().getErrorDataCount() > 0) {
                        //插入异常数据
                        postApiReturnInfoService.selfInsertBill(taskId, userInfo.getUserNo(), userInfo.getCompany(), head.getSid()
                                , postApiReturnInfoService.I_BILL
                                , jsonObjectMapper.toJson(resultModel.getData().getErrorData().get(0)), apiType, false);
                        result  += xdoi18n.XdoI18nUtil.t("发送失败,存在异常数据,清单内部编号：") + head.getEmsListNo();
                    } else {
                        logger.info("本api校验正常，更新本次发送单据状态");
                        /*
                        接口返回成功,发送后更新各种发送类型状态,发送状态为1 发送成功，明细状态为1 锁定
                         */
                        decIBillHeadMapper.updateSendApiStatusBySid(head.getSid(), ConstantsStatus.STATUS_1, taskId,ConstantsStatus.STATUS_1);
                    }
                }
            } catch (Exception ex) {
                logger.error("本次发送异常：", ex);
                blnIsError = true;
                strErrorMsg = xdoi18n.XdoI18nUtil.t("本次发送异常：") +head.getEmsListNo() + ex.getMessage();
                result += strErrorMsg;
            } finally {
                if (blnIsError) {

                    logger.error("api请求异常数据入库");
                    head.setTempRemark(strErrorMsg);
                    postApiReturnInfoService.selfInsertBill(taskId, userInfo.getUserNo(), userInfo.getCompany(), head.getSid()
                            , postApiReturnInfoService.I_BILL
                            , jsonObjectMapper.toJson(invtIHeadList.get(0)), apiType, false);
                }
                logger.info("****End本次发送的完成");
            }
        }
        logger.info("【***************推送进口清单信息结束*****************】");
        return result;
    }

    /**
     * 功能描述:发送进口清单撤回信息
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date： 2018-11-20
     * @param: listESids 需要发送的清单sid
     * @param: userInfo 用户信息
     * @return:
     */
    public String sendEmsIBillCancel(List<String> listESids, UserInfoToken userInfo) {
        String errorMessage="";
        //覆盖 先删后插入
        String importType = CommonEnum.ImportType.Cover.getValue();
        //api类型
        String apiType = CommonEnum.ApiType.INVT_API.getValue();
        logger.info("【***************推送金二进口清单(删单)信息开始(任务id:%s)*****************】");
        logger.info("获取清单信息");
        List<VImpEmsIBillHeadDto> billHeads = dtoHeadMapper.toDtoList(mapperHead.getEmsIBillHeadBySids(listESids));
        logger.info("根据表头依次请求api");
        for (VImpEmsIBillHeadDto head : billHeads) {
            String taskId = java.util.UUID.randomUUID().toString();
            logger.info(String.format("****Begin本次(删单)的手（账）号为：%s  清单内部编号为：%s", head.getEmsNo(), head.getEmsListNo()));
//            DecIBillHead decIBillHead=decIBillHeadMapper.selectByPrimaryKey(head.getSid());
            //if(decIBillHead.getSendApiStatus())
            JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
            BillCancelBody billCancelBody=new BillCancelBody();
            billCancelBody.setEmsListNo(head.getEmsListNo());
            List<BillCancelBody> billCancelBodies=new ArrayList<>();
            billCancelBodies.add(billCancelBody);
            boolean blnIsError = false;
            String strErrorMsg = "";
            try {
                GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.CANCEL_EMS_BILL_LIST);
                if (null == gwstdHttpConfig || Strings.isNullOrEmpty(gwstdHttpConfig.getBaseUrl()) || Strings.isNullOrEmpty(gwstdHttpConfig.getServiceUrl())) {
                    return xdoi18n.XdoI18nUtil.t("未配置撤回清单备案地址！");
                }
                String url = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();
                logger.info("撤回清单备案地址:{}", url);
                ResultObject<BillCancelBody> resultModel= jsonObjectMapper.fromJson(
                        baseCallApi.callEmsApi(null, head.getEmsNo(), importType, taskId, billCancelBodies,
                                userInfo.getAccessToken(), url)
                        , new TypeReference<ResultObject<BillCancelBody>>() {
                        });

                logger.info("更新本次发送时间");
                //更新发送时间
                decIBillHeadMapper.updateSendApiTimeBySid(head.getSid());

                if (!resultModel.isSuccess()) {
                    if(ConstantsStatus.RETURN_STATUS_3.equals(resultModel.getData().getCode())||ConstantsStatus.RETURN_STATUS_4.equals(resultModel.getData().getCode()))
                        {
                            logger.info("本api校验正常，核注清单无数据时或者撤销失败状态，更新本次发送单据状态");
                        /*
                        删单接口返回提单无单据,更新各种发送类型状态,发送状态为3 清单撤回，明细状态为1 可修改
                         */
                        decIBillHeadMapper.updateSendApiStatusBySid(head.getSid(), CommonEnum.BillEnum.BILLSTATUS_3.getCode(), taskId,ConstantsStatus.STATUS_0);
                        /*
                        删单接口返回提单无单据,提单状态为 C 清单撤回
                         */
                        decIBillHeadMapper.updateErpStatus(head.getSid(),head.getHeadId(), ConstantsStatus.STATUS_0, CommonVariable.APPR_STATUS_C);
                    }
                    else {
                        errorMessage = resultModel.getMessage();
                        logger.info(String.format("本api请求异常,异常信息%s", resultModel.getMessage()));
                        blnIsError = true;
                        strErrorMsg = resultModel.getMessage();
                    }
                } else {
                        logger.info("本api校验正常，更新本次发送单据状态");
                        /*
                        删单接口返回提单无单据,更新各种发送类型状态,发送状态为3 清单撤回，明细状态为1 可修改
                         */
                        decIBillHeadMapper.updateSendApiStatusBySid(head.getSid(), CommonEnum.BillEnum.BILLSTATUS_3.getCode(), taskId,ConstantsStatus.STATUS_0);
                        /*
                        删单接口返回提单无单据,提单状态为 C 清单撤回
                         */
                        decIBillHeadMapper.updateErpStatus(head.getSid(),head.getHeadId(), ConstantsStatus.STATUS_0,CommonVariable.APPR_STATUS_C);

                }
            } catch (Exception ex) {
                errorMessage=xdoi18n.XdoI18nUtil.t("清单撤回失败！");
                logger.error("本次发送异常：", ex);
                blnIsError = true;
                strErrorMsg = xdoi18n.XdoI18nUtil.t("本次发送异常：") + ex.getMessage();
            } finally {
                if (blnIsError) {
                    logger.error("api请求异常数据入库");
                    //组装错误数据
                    BillHeadApiReturn billHeadApiReturn = new BillHeadApiReturn();
                    List<BillListApiReturn> billListApiReturn = new ArrayList<>();
                    BillApiReturn billApiReturn = new BillApiReturn();
                    billHeadApiReturn.setTempRemark(strErrorMsg);
                    billApiReturn.setInvthead(billHeadApiReturn);
                    billApiReturn.setInvtlists(billListApiReturn);
                    postApiReturnInfoService.selfInsertBill(taskId, userInfo.getUserNo(), userInfo.getCompany(), head.getSid()
                            , postApiReturnInfoService.I_BILL_CANCEL
                            , jsonObjectMapper.toJson(billApiReturn), apiType, false);
                }
                logger.info("****End本次撤回发送的完成");
            }
        }
        logger.info("【***************推送进口清单撤回信息结束*****************】");
        return errorMessage;
    }

}
