package com.dcjet.cs.imp.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-04-18
 */
@Setter
@Getter
@RemoveTailingZero
public class VImpEmsIBillHead implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 报关单申报单位编码
     */
    @Column(name = "AGENT_CODE")
    private String agentCode;
    /**
     * 清单单申报单位编码
     */
    @Column(name = "DECLARE_CODE")
    private String declareCode;
    /**
     * 进出口标记
     */
    @Column(name = "I_E_MARK")
    private String IEMark;
    /**
     * 运输方式
     */
    @Column(name = "TRAF_MODE")
    private String trafMode;
    /**
     * 监管方式
     */
    @Column(name = "TRADE_MODE")
    private String tradeMode;
    /**
     * 进境关别/出境关别
     */
    @Column(name = "I_E_PORT")
    private String IEPort;
    /**
     * 启运国(地区)/运抵国(地区)启运国(地区)/运抵国(地区)
     */
    @Column(name = "TRADE_COUNTRY")
    private String tradeCountry;
    /**
     * 料件成品标志{I-料件,E-成品}
     */
    @Column(name = "G_MARK")
    private String GMark;
    /**
     * 企业内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;
    /**
     * 清单类型
     */
    @Column(name = "LIST_TYPE")
    private String listType;
    /**
     * 报关标志{1-报关，2-非报关}
     */
    @Column(name = "DCLCUS_MARK")
    private String dclcusMark;
    /**
     * 报关类型{1.关联报关2.对应报关}
     */
    @Column(name = "DCLCUS_TYPE")
    private String dclcusType;
    /**
     * 报关单类型
     */
    @Column(name = "ENTRY_TYPE")
    private String entryType;
    /**
     * 归并类型{0：自动归并 1：人工归并}
     */
    @Column(name = "MERGE_TYPE")
    private String mergeType;
    /**
     * 关联手(账)册编号
     */
    @Column(name = "REL_EMS_NO")
    private String relEmsNo;
    /**
     * 申请表编号
     */
    @Column(name = "ROTATE_APPLY_NO")
    private String rotateApplyNo;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 关联核注清单编号
     */
    @Column(name = "REL_LIST_NO")
    private String relListNo;
    /**
     * 关联报关单编号
     */
    @Column(name = "REL_ENTRY_NO")
    private String relEntryNo;
    /**
     * 关联报关单收发货人编码
     */
    @Column(name = "REL_ENTRY_RECEIVE_CODE")
    private String relEntryReceiveCode;
    /**
     * 关联报关单消费使用单位编码
     */
    @Column(name = "REL_ENTRY_TRADE_CODE")
    private String relEntryTradeCode;
    /**
     * 关联报关单申报单位编码
     */
    @Column(name = "REL_ENTRY_DECLARE_CODE")
    private String relEntryDeclareCode;
    /**
     * 境外发货人/境外收货人
     */
    @Column(name = "OVERSEAS_SHIPPER_NAME")
    private String overseasShipperName;
    /**
     * 运输工具名称及航次号
     */
    @Column(name = "TRAF_NAME")
    private String trafName;
    /**
     * 航次号
     */
    @ApiModelProperty("航次号")
    private String voyageNo;
    /**
     * 提运单号
     */
    @Column(name = "BILL_NO")
    private String billNo;
    /**
     * 货物存放点
     */
    @Column(name = "WAREHOUSE")
    private String warehouse;
    /**
     * 征免性质
     */
    @Column(name = "CUT_MODE")
    private String cutMode;
    /**
     * 许可证号
     */
    @Column(name = "LICENSE_NO")
    private String licenseNo;
    /**
     * 启运港
     */
    @Column(name = "DESP_PORT")
    private String despPort;
    /**
     * 贸易国（地区）
     */
    @Column(name = "TRADE_NATION")
    private String tradeNation;
    /**
     * 经停港指运港
     */
    @Column(name = "DEST_PORT")
    private String destPort;
    /**
     * 入境口岸\离境口岸
     */
    @Column(name = "ENTRY_PORT")
    private String entryPort;
    /**
     * 合同协议号
     */
    @Column(name = "CONTR_NO")
    private String contrNo;
    /**
     * 包装种类 多个,分割
     */
    @Column(name = "WRAP_TYPE")
    private String wrapType;
    /**
     * 件数
     */
    @Column(name = "PACK_NUM")
    private BigDecimal packNum;
    /**
     * 净重(公斤)
     */
    @Column(name = "NET_WT")
    private BigDecimal netWt;
    /**
     * 毛重(公斤)
     */
    @Column(name = "GROSS_WT")
    private BigDecimal grossWt;
    /**
     * 成交方式
     */
    @Column(name = "TRANS_MODE")
    private String transMode;
//    /**
//     * 运费
//     */
//    @Column(name = "FEE_RATE_NOTE")
//    private java.math.BigDecimal feeRateNote;
//    /**
//     * 保费
//     */
//    @Column(name = "INSUR_RATE_NOTE")
//    private java.math.BigDecimal insurRateNote;
//    /**
//     * 杂费
//     */
//    @Column(name = "OTHER_RATE_NOTE")
//    private java.math.BigDecimal otherRateNote;
    /**
     * 特殊关系确认
     */
    @Column(name = "CONFIRM_SPECIAL")
    private String confirmSpecial;
    /**
     * 价格影响确认
     */
    @Column(name = "CONFIRM_PRICE")
    private String confirmPrice;
    /**
     * 价格影响确认
     */
    @Column(name = "CONFIRM_ROYALTIES")
    private String confirmRoyalties;
    /**
     * 自报自缴
     */
    @Column(name = "DUTY_SELF")
    private String dutySelf;
    /**
     * null
     */
    @Column(name = "EMS_NO")
    private String emsNo;
    /**
     * 主管海关
     */
    @Column(name = "MASTER_CUSTOMS")
    private String masterCustoms;
    /**
     * 申报地海关
     */
    @Column(name = "DECL_CUSTOMS")
    private String declCustoms;
    /**
     * 经营单位编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 报关单号
     */
    @Column(name = "ENTRY_NO")
    private String entryNo;
    /**
     * 提单SID
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 杂费币制
     */
    @ApiModelProperty("杂费币制")
    private  String otherCurr;
    /**
     * 杂费类型
     */
    @ApiModelProperty("杂费类型")
    private  String otherMark;
    /**
     * 杂费
     */
    @ApiModelProperty("杂费")
    private BigDecimal otherRate;
    /**
     * 运费币制
     */
    @ApiModelProperty("运费币制")
    private  String feeCurr;
    /**
     * 运费类型
     */
    @ApiModelProperty("运费类型")
    private  String feeMark;
    /**
     * 运费
     */
    @ApiModelProperty("运费")
    private  BigDecimal feeRate;
    /**
     * 保费币制
     */
    @ApiModelProperty("保费币制")
    private  String insurCurr;
    /**
     * 保费类型
     */
    @ApiModelProperty("保费类型")
    private  String insurMark;
    /**
     * 保费
     */
    @ApiModelProperty("保费")
    private  BigDecimal insurRate;
    /**
     * 境外发货人Aeo/境外收货人Aeo
     */
    @ApiModelProperty("境外发货人Aeo/境外收货人Aeo")
    private String overseasShipperAeo;
    /**
     * 公式价格确认
     */
    @Column(name = "CONFIRM_FORMULA_PRICE")
    private String confirmFormulaPrice;
    /**
     * 暂定价格确认
     */
    @Column(name = "CONFIRM_TEMP_PRICE")
    private String confirmTempPrice;
}
