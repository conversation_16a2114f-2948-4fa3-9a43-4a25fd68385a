package com.dcjet.cs.imp.service;

import com.dcjet.cs.dec.dao.DecECustomsTrackMapper;
import com.dcjet.cs.dec.dao.DecICustomsTrackMapper;
import com.dcjet.cs.dec.model.DecICustomsTrack;
import com.dcjet.cs.dec.service.DecEntryReturnService;
import com.dcjet.cs.dto.dec.DecEntryReturnParam;
import com.dcjet.cs.dto.notice.NoticeTaskEventParam;
import com.dcjet.cs.dto.war.WarringPassConfigDto;
import com.dcjet.cs.entry.dao.DecEEntryHeadMapper;
import com.dcjet.cs.entry.dao.DecIEntryHeadMapper;
import com.dcjet.cs.entry.model.DecEEntryHead;
import com.dcjet.cs.entry.model.DecIEntryHead;
import com.dcjet.cs.erp.dao.DecEBillHeadMapper;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.dao.DecIBillHeadMapper;
import com.dcjet.cs.erp.model.DecEBillHead;
import com.dcjet.cs.erp.model.DecIBillHead;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryAttachMapper;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryHeadMapper;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryLicenseMapper;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryWorkflowMapper;
import com.dcjet.cs.gwstdentry.model.*;
import com.dcjet.cs.gwstdentry.service.GwstdEntryListService;
import com.dcjet.cs.mqsync.core.service.BaseMqSyncService;
import com.dcjet.cs.notice.service.event.NoticeTaskEvent;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * 请求api返回信息 Service;
 *
 * @date: 2022-03-28
 */
@Service
@Slf4j
public class KafkaEntryService extends BaseMqSyncService<GwstdEntryMessage> {
    @Resource
    private GwstdEntryListService gwstdEntryListService;
    @Resource
    private GwstdEntryHeadMapper gwstdEntryHeadMapper;
    @Resource
    private GwstdEntryWorkflowMapper gwstdEntryWorkflowMapper;
    @Resource
    private GwstdEntryLicenseMapper gwstdEntryLicenseMapper;
    @Resource
    private GwstdEntryAttachMapper gwstdEntryAttachMapper;
    @Resource
    private DecECustomsTrackMapper decECustomsTrackMapper;
    @Resource
    private DecICustomsTrackMapper decICustomsTrackMapper;
    @Resource
    private DecEEntryHeadMapper decEEntryHeadMapper;
    @Resource
    private DecIEntryHeadMapper decIEntryHeadMapper;
    @Resource
    private DecEBillHeadMapper decEBillHeadMapper;
    @Resource
    private DecIBillHeadMapper decIBillHeadMapper;
    @Resource
    private WarringPassConfigService warringPassConfigService;
    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private DecEntryReturnService decEntryReturnService;
    @Resource
    private ApplicationContext applicationContext;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void consume(String message) {
        GwstdEntryMessage gwstdEntryMessage = JsonObjectMapper.getInstance().fromJson(message, GwstdEntryMessage.class);
        try {
            if (gwstdEntryMessage == null) {
                log.info(String.format("Json序列化失败：%s", message));
                return;
            }
            Date date = new Date();
            GwstdEntryHead gwstdEntryHead = gwstdEntryMessage.getHead();
            if (gwstdEntryMessage.isExistHead()) {
                //根据海关编号\预录入代码删除表头数据
                gwstdEntryHeadMapper.deleteClear(gwstdEntryHead.getSeqNo());
                //判断是否申报到H2K, 0.未申报至H2K 1.已经申报至H2K
                if (StringUtils.isNotBlank(gwstdEntryHead.getSeqNo()) && StringUtils.isNotBlank(gwstdEntryHead.getEntryNo())) {
                    gwstdEntryHead.setIsDeclare(ConstantsStatus.STATUS_1);
                } else {
                    gwstdEntryHead.setIsDeclare(ConstantsStatus.STATUS_0);
                }
                String sid = UUID.randomUUID().toString();
                GwstdEntryHead param = new GwstdEntryHead();
                param.setSeqNo(gwstdEntryHead.getSeqNo());
                List<GwstdEntryHead> existsEntryHeadList = gwstdEntryHeadMapper.select(param);
                //如果数据库中已存在entryHead数据,sid取存在的sid.
                if (CollectionUtils.isNotEmpty(existsEntryHeadList)) {
                    gwstdEntryHead.setSid(existsEntryHeadList.get(0).getSid());

                    // 【艺康】定制
                    if ("3226941430".equals(gwstdEntryHead.getTradeCode())) {
                        gwstdEntryHead.setAttDownloadFlag(existsEntryHeadList.get(0).getAttDownloadFlag());
                    }
                } else {
                    gwstdEntryHead.setSid(sid);
                    // 【艺康】定制
                    if ("3226941430".equals(gwstdEntryHead.getTradeCode())) {
                        // 首次新增，0.附件待下载
                        gwstdEntryHead.setAttDownloadFlag(CommonEnum.attDownloadFlagEnum.WAIT.getCode());
                    }
                }
                gwstdEntryHead.setMyInsertTime(new Date());
                gwstdEntryHead.setInsertTime(new Date());
                gwstdEntryHead.setEntryCompareFlag(ConstantsStatus.STATUS_0);
                gwstdEntryHeadMapper.insert(gwstdEntryHead);
            }
            if (CollectionUtils.isNotEmpty(gwstdEntryMessage.getList())) {
                //根据预录入单号删除表体数据
                gwstdEntryListService.listDelete(gwstdEntryHead.getSeqNo());
                for (GwstdEntryList gwstdEntryList : gwstdEntryMessage.getList()) {
                    gwstdEntryList.setSid(UUID.randomUUID().toString());
                    gwstdEntryList.setMyInsertTime(date);
                    gwstdEntryList.setInsertTime(date);
                    gwstdEntryListService.insert(gwstdEntryList);
                }
            }
            if (!CollectionUtils.isEmpty(gwstdEntryMessage.getWorkflowList())) {
                gwstdEntryWorkflowMapper.workflowDelete(gwstdEntryHead.getSeqNo());
                for (GwstdEntryWorkflow gwstdEntryWorkflow : gwstdEntryMessage.getWorkflowList()) {
                    gwstdEntryWorkflow.setSid(UUID.randomUUID().toString());
                    gwstdEntryWorkflow.setInsertTime(date);
                    gwstdEntryWorkflow.setTradeCode(gwstdEntryHead.getTradeCode());
                    gwstdEntryWorkflowMapper.insertSelective(gwstdEntryWorkflow);
                }
            }
            if (gwstdEntryMessage.getLicense() != null) {
                GwstdEntryLicense gwstdEntryLicense = gwstdEntryMessage.getLicense();
                gwstdEntryLicenseMapper.licenseDelete(gwstdEntryHead.getSeqNo());
                gwstdEntryLicense.setSid(UUID.randomUUID().toString());
                gwstdEntryLicense.setInsertTime(date);
                if (StringUtils.isEmpty(gwstdEntryHead.getTradeCode())) {
                    gwstdEntryLicense.setTradeCode(gwstdEntryHead.getRelEmsNo());
                }
                gwstdEntryLicenseMapper.insertSelective(gwstdEntryLicense);
            }

            // 报关单状态
            String status = gwstdEntryWorkflowMapper.selectStatus(gwstdEntryHead.getSeqNo(), gwstdEntryHead.getTradeCode());
            if (StringUtils.equals(gwstdEntryHead.getIsDeclare(), ConstantsStatus.STATUS_1)) {
                //强制更新报关单申报日期
                updateDDateForce(gwstdEntryHead);
                //更新报关单回填日期
                updateApprDate(gwstdEntryHead);
                //更新报关单状态
                if (gwstdEntryHead.getSeqNo() != null) {
                    updateStatus(gwstdEntryHead, status);
                    //预警查验邮件通知
                    warningCheckEmail(gwstdEntryHead);
                }
            }
            //进出口报关追踪系统自动回填报关单号码、申报日期
            updateSubscriptionEntry(gwstdEntryHead);
            //更新进口国际物流信息
            updateDecIInterLogistics(gwstdEntryHead);

            // 【艺康】定制
            if ("3226941430".equals(gwstdEntryHead.getTradeCode())) {
                if (CollectionUtils.isNotEmpty(gwstdEntryMessage.getAttachList())) {
                    List<GwstdEntryAttach> insertAttachList = new ArrayList<>();

                    GwstdEntryAttach gwstdEntryAttachParam = new GwstdEntryAttach();
                    gwstdEntryAttachParam.setSeqNo(gwstdEntryHead.getSeqNo());
                    // 旧附件
                    List<GwstdEntryAttach> oldAttachList = gwstdEntryAttachMapper.getList(gwstdEntryAttachParam);

                    // 不存在旧附件，全量新增
                    if (CollectionUtils.isEmpty(oldAttachList)) {
                        insertAttachList = gwstdEntryMessage.getAttachList();
                    }
                    // 存在旧附件，增量插入到订阅报关单附件表中
                    else {
                        // 增量的附件
                        insertAttachList = gwstdEntryMessage.getAttachList().stream()
                                .filter(newAttach -> oldAttachList.stream().noneMatch(oldAttach ->
                                        newAttach.getOrgFileName().equals(oldAttach.getOrgFileName())
                                                && newAttach.getAttType().equals(oldAttach.getAttType())
                                                && newAttach.getAttId().equals(oldAttach.getAttId())))
                                .collect(Collectors.toList());
                    }

                    if (CollectionUtils.isNotEmpty(insertAttachList)) {
                        for (GwstdEntryAttach gwstdEntryAttach : insertAttachList) {
                            gwstdEntryAttach.setSid(UUID.randomUUID().toString());
                            gwstdEntryAttach.setInsertTime(date);
                            gwstdEntryAttach.setTradeCode(gwstdEntryHead.getTradeCode());

                            gwstdEntryAttach.setAttDownloadFlag(StringUtils.isNotBlank(gwstdEntryAttach.getAttId()) ? CommonEnum.attDownloadFlagEnum.WAIT.getCode() : CommonEnum.attDownloadFlagEnum.NONE.getCode());
                            gwstdEntryAttachMapper.insertSelective(gwstdEntryAttach);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error(String.format("KAFKA接口获取报关单下发信息新增失败,统一编号：【%s】", gwstdEntryMessage.getHead().getSeqNo()), ex);
            throw new RuntimeException(ex);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateDDateForce(GwstdEntryHead gwstdEntryHead) {
        List<DecIEntryHead> entryHeadList = decIEntryHeadMapper.getseqNoList(gwstdEntryHead.getSeqNo(), gwstdEntryHead.getIEMark(), gwstdEntryHead.getTradeCode());
        if (CollectionUtils.isNotEmpty(entryHeadList)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            DecIEntryHead head = entryHeadList.get(0);
            if (gwstdEntryHead.getEntryDeclareDate() != null) {
                head.setIEMark(gwstdEntryHead.getIEMark());
                head.setEntryDeclareDate(gwstdEntryHead.getEntryDeclareDate());
                head.setDDate(sdf.format(gwstdEntryHead.getEntryDeclareDate()));
                decIEntryHeadMapper.updateDDateByEntryId(head);
            }
        }
    }

    /**
     * 更新进口国际物流信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateDecIInterLogistics(GwstdEntryHead gwstdEntryHead) {
        List<DecIEntryHead> seqNoList = decIEntryHeadMapper.getseqNoList(gwstdEntryHead.getSeqNo(), gwstdEntryHead.getIEMark(), gwstdEntryHead.getTradeCode());
        if (seqNoList.size() > 0) {
            String erpHeadId = seqNoList.get(0).getErpHeadId();
            updateDecEntryReturn(erpHeadId, gwstdEntryHead.getTradeCode());
        } else {
            List<DecIEntryHead> entryList = decIEntryHeadMapper.getEntryList(gwstdEntryHead.getEntryNo(), gwstdEntryHead.getIEMark(), gwstdEntryHead.getTradeCode());
            if (entryList.size() > 0) {
                String erpHeadId = entryList.get(0).getErpHeadId();
                updateDecEntryReturn(erpHeadId, gwstdEntryHead.getTradeCode());
            }
        }
    }

    public void updateDecEntryReturn(String erpHeadId, String tradeCode) {
        UserInfoToken userInfo = new UserInfoToken();
        userInfo.setCompany(tradeCode);
        userInfo.setUserNo("Kafka");
        userInfo.setUserName("Kafka Entry Consume");
        //根据企业编码查询配置项是否开启国际物流同步
        boolean isSync = false;
        List<WarringPassConfigDto> configs = warringPassConfigService.selectAll(userInfo);
        if (CollectionUtils.isNotEmpty(configs)) {
            WarringPassConfigDto passConfig = configs.get(0);
            if (CommonVariable.CONFIG_1.equals(passConfig.getSyncInterLogistics())) {
                isSync = true;
            }
        }
        if (isSync) {
            DecICustomsTrack entryTrack = decICustomsTrackMapper.selectEntryNosByErpHeadId(erpHeadId);
            if (entryTrack != null) {
                //更新报关单数据回传信息表
                DecEntryReturnParam decEntryReturnParam = new DecEntryReturnParam();
                decEntryReturnParam.setHeadId(erpHeadId);
                decEntryReturnParam.setEntryNo(entryTrack.getEntryNo());
                decEntryReturnParam.setPassDate(entryTrack.getPassDate());
                decEntryReturnParam.setEntryDeclareDate(entryTrack.getEntryDeclareDate());
                decEntryReturnService.updateByHeadId(decEntryReturnParam, userInfo);
                log.info(" -----------成功更新国际物流&报关信息回传表-------------");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateApprDate(GwstdEntryHead gwstdEntryHead) {
        //GwstdEntryWorkflow gwstdEntryWorkflow = gwstdEntryWorkflowMapper.getdDate(gwstdEntryHead.getSeqNo(), gwstdEntryHead.getEntryNo(), gwstdEntryHead.getTradeCode());
        //根据seqNo和报关单号统计报关单数据
        List<DecIEntryHead> seqNoList = decIEntryHeadMapper.getseqNoList(gwstdEntryHead.getSeqNo(), gwstdEntryHead.getIEMark(), gwstdEntryHead.getTradeCode());
        List<DecIEntryHead> entryList = decIEntryHeadMapper.getEntryList(gwstdEntryHead.getEntryNo(), gwstdEntryHead.getIEMark(), gwstdEntryHead.getTradeCode());
        log.info(" -----------KAFKA接口开始更新报关单下发信息，开始更新进口报关追踪放行日期-------------");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //更新报关追踪回填日期
        GwstdEntryWorkflow gwstdEntryWorkflow = gwstdEntryWorkflowMapper.getSeqNoDate(gwstdEntryHead.getSeqNo(), gwstdEntryHead.getTradeCode());
        String entryDeclareDateStr = null;
        if (gwstdEntryHead.getEntryDeclareDate() != null) {
            entryDeclareDateStr = sdf.format(gwstdEntryHead.getEntryDeclareDate());
        }
        if (seqNoList.size() > 0) {
            String seqNoEmsListNo = seqNoList.get(0).getEmsListNo();
            String erpHeadId = seqNoList.get(0).getErpHeadId();
            if (gwstdEntryWorkflow != null) {
                if (StringUtils.equals(gwstdEntryHead.getIEMark(), CommonVariable.IE_MARK_I)) {
                    //更新进口报关追踪放行日期
                    decICustomsTrackMapper.updateApprDate(seqNoEmsListNo, gwstdEntryWorkflow.getApprDate(), gwstdEntryHead.getEntryDeclareDate(), gwstdEntryHead.getTradeCode(), entryDeclareDateStr);
                    updateDecEntryReturn(erpHeadId, gwstdEntryHead.getTradeCode());
                } else {
                    //更新出口报关追踪放行日期
                    decECustomsTrackMapper.updateApprDate(seqNoEmsListNo, gwstdEntryWorkflow.getApprDate(), gwstdEntryHead.getEntryDeclareDate(), gwstdEntryHead.getTradeCode(), entryDeclareDateStr);
                }
                log.info(" -----------KAFKA接口开始更新报关单下发信息，更新进口报关追踪放行日期完成-------------");
            } else {
                log.info(" -----------KAFKA接口开始更新报关单下发信息，gwstdEntryWorkflow根据seqNo查询未获取到“批准日期”,批准日期为空,执行下一条根据entryNo查询数据-------------");
            }
            //更新进口日期
            if (gwstdEntryHead.getIEDate() != null) {
                if (StringUtils.equals(gwstdEntryHead.getIEMark(), CommonVariable.IE_MARK_I)) {
                    decIEntryHeadMapper.updateIEDate(seqNoEmsListNo, erpHeadId, gwstdEntryHead.getIEDate(), gwstdEntryHead.getTradeCode());
                    log.info(" -----------KAFKA接口开始更新报关单下发信息，更新进口报关追踪进口日期完成-------------");
                } else {
                    decEEntryHeadMapper.updateEEDate(seqNoEmsListNo, erpHeadId, gwstdEntryHead.getIEDate(), gwstdEntryHead.getTradeCode());
                    log.info(" -----------KAFKA接口开始更新报关单下发信息，更新出口报关追踪出口日期完成-------------");
                }
            } else {
                log.info(" -----------KAFKA接口开始更新报关单下发信息，gwstdEntryHead进口日期为空-------------");
            }
        } else {
            log.info(" -----------KAFKA接口开始更新报关单下发信息，seqNoList根据seqNo查询数据为空,执行下一条根据entryNo查询数据-------------");
            //更新报关追踪回填日期
            GwstdEntryWorkflow workflow = gwstdEntryWorkflowMapper.getEntryNoDate(gwstdEntryHead.getEntryNo(), gwstdEntryHead.getTradeCode());
            if (entryList.size() > 0) {
                String entryNoEmsListNo = entryList.get(0).getEmsListNo();
                String erpHeadId = entryList.get(0).getErpHeadId();
                if (workflow != null) {
                    if (StringUtils.equals(gwstdEntryHead.getIEMark(), CommonVariable.IE_MARK_I)) {
                        //更新进口报关追踪放行日期
                        decICustomsTrackMapper.updateApprDate(entryNoEmsListNo, workflow.getApprDate(), gwstdEntryHead.getEntryDeclareDate(), gwstdEntryHead.getTradeCode(), entryDeclareDateStr);
                        updateDecEntryReturn(erpHeadId, gwstdEntryHead.getTradeCode());
                    } else {
                        //更新出口报关追踪放行日期
                        decECustomsTrackMapper.updateApprDate(entryNoEmsListNo, workflow.getApprDate(), gwstdEntryHead.getEntryDeclareDate(), gwstdEntryHead.getTradeCode(), entryDeclareDateStr);
                    }
                    log.info(" -----------KAFKA接口开始更新报关单下发信息，更新进口报关追踪放行日期完成-------------");
                } else {
                    log.info(" -----------KAFKA接口开始更新报关单下发信息，gwstdEntryWorkflow根据entryNo查询未获取到“批准日期”,批准日期为空,执行下一条根据entryNo查询数据-------------");
                }
                //更新进口日期
                if (gwstdEntryHead.getIEDate() != null) {
                    if (StringUtils.equals(gwstdEntryHead.getIEMark(), CommonVariable.IE_MARK_I)) {
                        decIEntryHeadMapper.updateIEDate(entryNoEmsListNo, erpHeadId, gwstdEntryHead.getIEDate(), gwstdEntryHead.getTradeCode());
                        log.info(" -----------KAFKA接口开始更新报关单下发信息，更新进口报关追踪进口日期完成-------------");
                    } else {
                        decEEntryHeadMapper.updateEEDate(entryNoEmsListNo, erpHeadId, gwstdEntryHead.getIEDate(), gwstdEntryHead.getTradeCode());
                        log.info(" -----------KAFKA接口开始更新报关单下发信息，更新出口报关追踪出口日期完成-------------");
                    }
                } else {
                    log.info(" -----------KAFKA接口开始更新报关单下发信息，gwstdEntryHead进口日期为空-------------");
                }
            } else {
                log.info(" -----------KAFKA接口开始更新报关单下发信息，entryList根据entryNo查询数据为空-------------");
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(GwstdEntryHead gwstdEntryHead, String status) {
        if (StringUtils.isEmpty(status)) {
            log.info(" -----------KAFKA接口开始更新报关单下发信息，WORKFLOW表中获取状态entry_status为空-------------");
            return;
        }
        //更新报关单状态
        if (StringUtils.isEmpty(gwstdEntryHead.getEmsNo())) {
            if (gwstdEntryHead.getIEMark().equals(CommonVariable.IE_MARK_I)) {
                //根据报关单统一编号查询所有进口报关单数据
                DecIEntryHead decIEntryHead = new DecIEntryHead();
                decIEntryHead.setSeqNo(gwstdEntryHead.getSeqNo());
                decIEntryHead.setTradeCode(gwstdEntryHead.getTradeCode());
                List<DecIEntryHead> decIEntryHeads = decIEntryHeadMapper.getList(decIEntryHead);
                if (decIEntryHeads.size() != 0) {
                    for (DecIEntryHead intryHead : decIEntryHeads) {
                        //增加更新报关单号逻辑
                        String entryNo = StringUtils.isNoneBlank(intryHead.getEntryNo()) ? intryHead.getEntryNo() : gwstdEntryHead.getEntryNo();
                        //更新进口报关单状态
                        decIEntryHeadMapper.updateStatus(status, intryHead.getSid(), gwstdEntryHead.getIEMark(), intryHead.getTradeCode(), entryNo, gwstdEntryHead.getEntryDeclareDate());
                    }
                } else {
                    log.info(" -----------KAFKA接口开始更新报关单下发信息，查询进口报关单数据为空-------------" + decIEntryHeads);
                }
            } else {
                //根据报关单统一编号查询所有出口报关单数据
                DecEEntryHead decEEntryHead = new DecEEntryHead();
                decEEntryHead.setSeqNo(gwstdEntryHead.getSeqNo());
                decEEntryHead.setTradeCode(gwstdEntryHead.getTradeCode());
                List<DecEEntryHead> decEEntryHeads = decEEntryHeadMapper.getList(decEEntryHead);
                if (decEEntryHeads.size() != 0) {
                    for (DecEEntryHead entryHead : decEEntryHeads) {
                        //增加更新报关单号逻辑
                        String entryNo = StringUtils.isNoneBlank(entryHead.getEntryNo()) ? entryHead.getEntryNo() : gwstdEntryHead.getEntryNo();
                        //更新出口报关单状态
                        decIEntryHeadMapper.updateStatus(status, entryHead.getSid(), gwstdEntryHead.getIEMark(), entryHead.getTradeCode(), entryNo, gwstdEntryHead.getEntryDeclareDate());
                    }
                } else {
                    log.info(" -----------KAFKA接口开始更新报关单下发信息，查询出口报关单数据为空-------------" + decEEntryHeads);
                }
            }
        } else {
            //查询清单是否发送成功更新报关单状态
            String initials = gwstdEntryHead.getEmsNo().substring(0, 1);
            if (!"H".equals(initials)) {
                //根据报关单统一编号查询所有进口清单数据
                if (gwstdEntryHead.getIEMark().equals(CommonVariable.IE_MARK_I)) {
                    DecIBillHead decIBillHead = new DecIBillHead();
                    decIBillHead.setSeqNo(gwstdEntryHead.getSeqNo());
                    decIBillHead.setTradeCode(gwstdEntryHead.getTradeCode());
                    List<DecIBillHead> decIBillHeads = decIBillHeadMapper.getList(decIBillHead);
                    if (decIBillHeads.size() != 0) {
                        for (DecIBillHead iBillHead : decIBillHeads) {
                            //增加更新报关单号逻辑
                            String entryNo = StringUtils.isNoneBlank(iBillHead.getEntryNo()) ? iBillHead.getEntryNo() : gwstdEntryHead.getEntryNo();
                            //校验数据是否发送失败
                            if (iBillHead.getSendApiStatus().equals(ConstantsStatus.STATUS_0)) {
                                //更新进口报关单状态
                                decIEntryHeadMapper.updateStatus(status, iBillHead.getEntrySid(), gwstdEntryHead.getIEMark(), iBillHead.getTradeCode(), entryNo, gwstdEntryHead.getEntryDeclareDate());
                            }
                        }
                    } else {
                        log.info(" -----------KAFKA接口开始更新报关单下发信息，查询进口清单数据为空-------------" + decIBillHeads);
                    }
                } else {
                    //根据报关单统一编号查询所有出口清单数据
                    DecEBillHead decEBillHead = new DecEBillHead();
                    decEBillHead.setSeqNo(gwstdEntryHead.getSeqNo());
                    decEBillHead.setTradeCode(gwstdEntryHead.getTradeCode());
                    List<DecEBillHead> decEBillHeads = decEBillHeadMapper.getList(decEBillHead);
                    if (decEBillHeads.size() != 0) {
                        for (DecEBillHead eBillHead : decEBillHeads) {
                            //增加更新报关单号逻辑
                            String entryNo = StringUtils.isNoneBlank(eBillHead.getEntryNo()) ? eBillHead.getEntryNo() : gwstdEntryHead.getEntryNo();
                            //校验数据是否发送失败
                            if (eBillHead.getSendApiStatus().equals(ConstantsStatus.STATUS_0)) {
                                //更新出口报关单状态
                                decIEntryHeadMapper.updateStatus(status, eBillHead.getEntrySid(), gwstdEntryHead.getIEMark(), eBillHead.getTradeCode(), entryNo, gwstdEntryHead.getEntryDeclareDate());
                            }
                        }
                    } else {
                        log.info(" -----------KAFKA接口开始更新报关单下发信息，查询出口清单数据为空-------------" + decEBillHeads);
                    }
                }
            }
        }
    }

    /**
     * 进出口报关追踪系统自动回填报关单号码、申报日期
     *
     * @param gwstdEntryHead
     */
    private void updateSubscriptionEntry(GwstdEntryHead gwstdEntryHead) {
        UserInfoToken userInfoToken = new UserInfoToken();
        userInfoToken.setCompany(gwstdEntryHead.getTradeCode());

        //根据企业编码查询配置项
        List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(userInfoToken);
        if (warringPassConfigDtos.size() > 0) {
            WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
            //订阅报关单回填  0:不回填 1：回填
            if (StringUtils.equals(warringPassConfigDto.getSubscriptionEntry(), ConstantsStatus.STATUS_1)) {

                //根据合同协议号查询
                List<DecIBillHead> decIBillHeads = decIBillHeadMapper.selectContrNO(gwstdEntryHead.getContrNo(), gwstdEntryHead.getTradeCode());
                List<DecEBillHead> decEBillHeads = decEBillHeadMapper.selectContrNO(gwstdEntryHead.getContrNo(), gwstdEntryHead.getTradeCode());
//                List<DecECustomsTrack> decECustomsTracks = decECustomsTrackMapper.selectContrNO(gwstdEntryHead.getContrNo(), gwstdEntryHead.getTradeCode());
                //进口
                if (decIBillHeads.size() > 0) {
                    for (DecIBillHead decIBillHead : decIBillHeads) {
                        if (decIBillHead.getTrackDeclareDate() == null) {
                            decIBillHead.setEntryDeclareDate(gwstdEntryHead.getEntryDeclareDate());
                        }
                        if (decIBillHead.getTrackEntryNo() == null) {
                            decIBillHead.setEntryNo(gwstdEntryHead.getEntryNo());
                        }
                        decIBillHeadMapper.updateByPrimaryKeySelective(decIBillHead);
                        decICustomsTrackMapper.updateEntryNo(gwstdEntryHead.getTradeCode(), decIBillHead.getEntryNo(), decIBillHead.getEntryDeclareDate(), gwstdEntryHead.getContrNo(), decIBillHead.getSid(), decIBillHead.getEmsListNo());
                    }
                }
                //出口

                if (decEBillHeads.size() > 0) {
                    for (DecEBillHead decEBillHead : decEBillHeads) {
                        if (decEBillHead.getTrackDeclareDate() == null) {
                            decEBillHead.setEntryDeclareDate(gwstdEntryHead.getEntryDeclareDate());
                        }
                        if (decEBillHead.getTrackEntryNo() == null) {
                            decEBillHead.setEntryNo(gwstdEntryHead.getEntryNo());
                        }
                        decEBillHeadMapper.updateByPrimaryKeySelective(decEBillHead);
                        decECustomsTrackMapper.updateEntryNo(gwstdEntryHead.getTradeCode(), decEBillHead.getEntryNo(), decEBillHead.getEntryDeclareDate(), gwstdEntryHead.getContrNo(), decEBillHead.getSid(), decEBillHead.getEmsListNo());
                    }
                }
                /*if (decECustomsTracks.size() > 0) {
                    for (DecECustomsTrack decECustomsTrack : decECustomsTracks) {
                        if (decECustomsTrack.getDeclareDate() == null) {
                            decECustomsTrack.setDeclareDate(gwstdEntryHead.getEntryDeclareDate());
                        }
                        if (StringUtils.isBlank(decECustomsTrack.getEntryNo())) {
                            decECustomsTrack.setEntryNo(gwstdEntryHead.getEntryNo());
                        }
                        decECustomsTrackMapper.updateByPrimaryKeySelective(decECustomsTrack);
                        decECustomsTrackMapper.updateBillEntryNo(gwstdEntryHead.getTradeCode(),gwstdEntryHead.getEntryNo(),gwstdEntryHead.getEntryDeclareDate(),decECustomsTrack.getHeadId(),decECustomsTrack.getEmsListNo());
                    }
                }*/
            }
        }
    }


    /**
     * 预警查验邮件通知
     *
     * @param gwstdEntryHead
     */
    private void warningCheckEmail(GwstdEntryHead gwstdEntryHead) {
        String ieMark = gwstdEntryHead.getIEMark();
        if (StringUtils.equals(ieMark, "I")) {
            applicationContext.publishEvent(new NoticeTaskEvent(new NoticeTaskEventParam("ENTRY_I_INSPECTION", gwstdEntryHead.getTradeCode(), gwstdEntryHead.getEntryNo())));
        } else if (StringUtils.equals(ieMark, "E")) {
            applicationContext.publishEvent(new NoticeTaskEvent(new NoticeTaskEventParam("ENTRY_E_INSPECTION", gwstdEntryHead.getTradeCode(), gwstdEntryHead.getEntryNo())));
        }
    }

}

