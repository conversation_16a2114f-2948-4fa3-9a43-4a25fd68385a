package com.dcjet.cs.imp.dao;

import com.dcjet.cs.imp.model.VImpEmsCusExg;
import com.dcjet.cs.mat.model.MatImgexgOrg;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-04-21
 */
public interface VImpEmsCusExgMapper extends Mapper<VImpEmsCusExg> {

    /**
     * 根据参数查询
     *
     * @param param 传入参数
     * @return list数据
     * <AUTHOR>
     */
     List<VImpEmsCusExg> getEmsCusExgsBySids(List<MatImgexgOrg> sids);

}
