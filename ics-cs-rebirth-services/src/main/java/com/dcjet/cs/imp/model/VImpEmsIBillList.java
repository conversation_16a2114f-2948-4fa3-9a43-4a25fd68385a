package com.dcjet.cs.imp.model;

import lombok.Getter;
import lombok.Setter;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import java.io.Serializable;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-04-18
 */
@Setter
@Getter
@RemoveTailingZero
public class VImpEmsIBillList implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 表头id
     */
    @Id
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 料号
     */
    @Column(name = "COP_G_NO")
    private String copGNo;
    /**
     * 备案序号{对应底账序号}
     */
    @Column(name = "G_NO")
    private java.math.BigDecimal GNo;
    /**
     * 规格型号
     */
    @Column(name = "G_MODEL")
    private String GModel;
    /**
     * 币制
     */
    @Column(name = "CURR")
    private String curr;
    /**
     * 原产国(地区)
     */
    @Column(name = "ORIGIN_COUNTRY")
    private String originCountry;
    /**
     * 最终目的国（地区）
     */
    @Column(name = "DESTINATION_COUNTRY")
    private String destinationCountry;
    /**
     * 申报数量
     */
    @Column(name = "QTY")
    private java.math.BigDecimal qty;
    /**
     * 申报单价
     */
    @Column(name = "DEC_PRICE")
    private java.math.BigDecimal decPrice;
    /**
     * 申报总价
     */
    @Column(name = "DEC_TOTAL")
    private java.math.BigDecimal decTotal;
    /**
     * 第一比例因子
     */
    @Column(name = "FACTOR_1")
    private java.math.BigDecimal factor1;
    /**
     * 法定数量
     */
    @Column(name = "QTY_1")
    private java.math.BigDecimal qty1;
    /**
     * 第二比例因子
     */
    @Column(name = "FACTOR_2")
    private java.math.BigDecimal factor2;
    /**
     * 法定第二数量
     */
    @Column(name = "QTY_2")
    private java.math.BigDecimal qty2;
    /**
     * 重量比例因子
     */
    @Column(name = "FACTOR_WT")
    private java.math.BigDecimal factorWt;
    /**
     * 净重
     */
    @Column(name = "NET_WT")
    private java.math.BigDecimal netWt;
    /**
     * 征免方式
     */
    @Column(name = "DUTY_MODE")
    private String dutyMode;
    /**
     * 毛重
     */
    @Column(name = "GROSS_WT")
    private java.math.BigDecimal grossWt;
    /**
     * 单耗版本号
     */
    @Column(name = "EXG_VERSION")
    private String exgVersion;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 报关单商品序号
     */
    @Column(name = "ENTRY_G_NO")
    private java.math.BigDecimal entryGNo;
    /**
     * 手(账)册企业内部编号
     */
    @Column(name = "COP_EMS_NO")
    private String copEmsNo;

    /**
     * 境内目的地/境内货源地
     */
    @Column(name = "DISTRICT_CODE")
    private String districtCode;
    /**
     * 境内目的地/境内货源地(行政级别)
     */
    @Column(name = "DISTRICT_POST_CODE")
    private String districtPostCode;
    /**
     * 企业料号
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;
    /**
     * 用途
     */
    @Column(name = "USE_TYPE")
    private String useType;
    /**
     * 边角料商品编码
     */
    @Column(name = "CODE_T_S")
    private String  codeTS;
    /**
     * 边角料申报单位
     */
    @Column(name = "UNIT")
    private String  unit;
    /**
     * 边角料商品名称
     */
    @Column(name = "G_NAME")
    private String  GName;
}
