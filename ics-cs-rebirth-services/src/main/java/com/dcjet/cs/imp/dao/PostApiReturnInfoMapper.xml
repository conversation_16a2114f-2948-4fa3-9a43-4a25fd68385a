<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.imp.dao.PostApiReturnInfoMapper">
    <resultMap id="postApiReturnInfoResultMap" type="com.dcjet.cs.imp.model.PostApiReturnInfo">
        <result column="SID" jdbcType="VARCHAR" property="sid"/>
        <result column="BILL_SID" jdbcType="VARCHAR" property="billSid"/>
        <result column="RETURN_JSON" jdbcType="VARCHAR" property="returnJson"/>
        <result column="INSERT_USER" jdbcType="VARCHAR" property="insertUser"/>
        <result column="INSERT_TIME" jdbcType="TIMESTAMP" property="insertTime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="TRADE_CODE" jdbcType="VARCHAR" property="tradeCode"/>
        <result column="BILL_TYPE" jdbcType="VARCHAR" property="billType"/>
        <result column="TASK_ID" jdbcType="VARCHAR" property="taskId"/>
    </resultMap>
    <delete id="deleteByBillSid" parameterType="java.lang.String">
     DELETE FROM T_POST_API_RETURN_INFO T WHERE T.BILL_SID=#{billSid}
   </delete>

    <select id="selectByBillSid" resultMap="postApiReturnInfoResultMap">
    SELECT * FROM (
    SELECT * FROM T_POST_API_RETURN_INFO T  WHERE T.BILL_SID=#{billSid} AND T.TASK_ID = #{sendApiTaskId}
    ORDER BY T.INSERT_TIME DESC
     ) t
    <if test="_databaseId == 'postgresql'">
      limit 1
    </if>
    <if test="_databaseId != 'postgresql'">
        WHERE ROWNUM=1
    </if>
   </select>

    <delete id="deleteHistoryDate">
        delete from T_POST_API_RETURN_INFO
        <where>
            <if test='_databaseId == "postgresql" '>
                <![CDATA[ insert_time < current_date::timestamp + '-3 month' ]]>
            </if>
            <if test='_databaseId != "postgresql" '>
                <![CDATA[ insert_time < ADD_MONTHS(trunc(sysdate), -3) ]]>
            </if>
        </where>
    </delete>
</mapper>
