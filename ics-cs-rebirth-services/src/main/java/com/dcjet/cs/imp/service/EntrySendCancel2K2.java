package com.dcjet.cs.imp.service;

import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.dto.erp.DecBillHeadCommonDto;
import com.dcjet.cs.dto.ztyth.ZtythEmsHeadDto;
import com.dcjet.cs.dxt.service.BillCancelEApiService;
import com.dcjet.cs.dxt.service.BillCancelIApiService;
import com.dcjet.cs.erp.dao.DecEBillHeadMapper;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.dao.DecIBillHeadMapper;
import com.dcjet.cs.erp.mapper.DecEBillHeadDtoMapper;
import com.dcjet.cs.erp.mapper.DecIBillHeadDtoMapper;
import com.dcjet.cs.erp.model.DecEBillHead;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.erp.model.DecIBillHead;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.ztyth.service.ZtythEmsImgExgService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: WJ
 * @createDate: 2021/4/14 15:45
 */
@Service
public class EntrySendCancel2K2 {

    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private DecEBillHeadMapper decEBillHeadMapper;
    @Resource
    private DecIBillHeadMapper decIBillHeadMapper;
    @Resource
    private VImpEmsEBillHeadService impEmsEBillHeadService;
    @Resource
    private VImpEmsIBillHeadService impEmsIBillHeadService;
    /**
     * 进口清单mapper
     */
    @Resource
    private DecIBillHeadDtoMapper decIBillHeadDtoMapper;
    /**
     * 出口清单mapper
     */
    @Resource
    private DecEBillHeadDtoMapper decEBillHeadDtoMapper;
    @Resource
    private ZtythEmsImgExgService ztythEmsImgExgService;
    @Resource
    private BillCancelIApiService billCancelIApiService;
    @Resource
    private BillCancelEApiService billCancelEApiService;
    @Resource
    private AeoAuditInfoService aeoAuditInfoService;

    /**
     * 清单撤回功能
     * @param headId
     * @param ieMark
     * @param userInfoToken
     * @return
     */
    public String sendCancelEntry(String headId, String ieMark, UserInfoToken userInfoToken) {
        String errorMessage="";
        // 需要发送的清单列表
        DecBillHeadCommonDto listBillHeadCommonDtos;
        DecIBillHead decIBillHead = null;
        DecEBillHead decEBillHead = null;
        String erpApprStatus;
        // 进口
        // 获取有效状态的，且保税的数据
        if (ieMark.equals(CommonVariable.IE_MARK_I)) {
            decIBillHead = decIBillHeadMapper.selectByPrimaryKey(headId);
            listBillHeadCommonDtos = decIBillHeadDtoMapper.toCommonLDto(decIBillHead);
            DecErpIHeadN decErpIHeadN = decErpIHeadNMapper.selectByPrimaryKey(decIBillHead.getHeadId());
            erpApprStatus = decErpIHeadN.getApprStatus();

        } else {
            decEBillHead = decEBillHeadMapper.selectByPrimaryKey(headId);
            listBillHeadCommonDtos = decEBillHeadDtoMapper.toCommonLDto(decEBillHead);
            erpApprStatus = decErpEHeadNMapper.selectByPrimaryKey(decEBillHead.getHeadId()).getApprStatus();
        }
        checkBillHeadCommon(listBillHeadCommonDtos, erpApprStatus);

        // 发送点迅通删除暂存清单 added by caoyong 2020-02-19 start
        String emsNo = listBillHeadCommonDtos.getEmsNo();
        String billFlag = null;
        if (StringUtils.isNotBlank(emsNo)) {
            List<ZtythEmsHeadDto> ztythEmsHeadDto = ztythEmsImgExgService.getEmsNoListSelf(userInfoToken.getCompany(), userInfoToken).
                    parallelStream().filter(x -> x.getEmsNo().equals(emsNo)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ztythEmsHeadDto)) {
                billFlag = ztythEmsHeadDto.get(0).getBillFlag();
            }
        }

        //需要发送的清单sid列表
        List<String> listNeedSendBillSids = new ArrayList<>();
        listNeedSendBillSids.add(listBillHeadCommonDtos.getSid());

        if (ieMark.equals(CommonVariable.IE_MARK_I)) {
            if (StringUtils.equals("31", billFlag)) {
                // 发送点讯通
                errorMessage = billCancelIApiService.cancel(decIBillHead, userInfoToken);
            }else {
                // 发送金二
                errorMessage = impEmsIBillHeadService.sendEmsIBillCancel(listNeedSendBillSids, userInfoToken);
            }
            DecIBillHead billHead = decIBillHeadMapper.selectByPrimaryKey(decIBillHead.getSid());
            if (billHead != null) {
                if (StringUtils.equals(billHead.getSendApiStatus(),ConstantsStatus.STATUS_3)) {
                    AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
                    aeoAuditInfo.setApprType(CommonVariable.IE_MARK_I);
                    aeoAuditInfo.setBusinessSid(billHead.getHeadId());
                    Date date = new Date();
                    aeoAuditInfoService.auditInsert(aeoAuditInfo,date,CommonVariable.APPR_STATUS_C,userInfoToken.getUserNo(),userInfoToken.getUserName(), userInfoToken.getCompany());
                }
            }
        } else {
            if (StringUtils.equals("31", billFlag)) {
                // 发送点讯通
                errorMessage = billCancelEApiService.cancel(decEBillHead, userInfoToken);
            }else {
                // 发送金二
                errorMessage = impEmsEBillHeadService.sendEmsEBillCancel(listNeedSendBillSids, userInfoToken);
            }
            DecEBillHead bilEHead = decEBillHeadMapper.selectByPrimaryKey(decEBillHead.getSid());
            if (bilEHead != null) {
                if (StringUtils.equals(bilEHead.getSendApiStatus(),ConstantsStatus.STATUS_3)) {
                    AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
                    aeoAuditInfo.setApprType(CommonVariable.IE_MARK_E);
                    aeoAuditInfo.setBusinessSid(bilEHead.getHeadId());
                    Date date = new Date();
                    aeoAuditInfoService.auditInsert(aeoAuditInfo,date,CommonVariable.APPR_STATUS_C,userInfoToken.getUserNo(),userInfoToken.getUserName(), userInfoToken.getCompany());
                }
            }
        }
        return errorMessage;
    }

    private void checkBillHeadCommon(DecBillHeadCommonDto dto, String erpApprStatus) {
        // 可撤回状态 “发送失败”，“A0 暂存”“D1申报失败”“DL 删单”“J3 退单”“Z 入库失败”
        String[] arrValidStatus = {"0", "1", "A0", "D1", "Z", "J3"};
        // 是否可操作 内审通过才能操作
        if (!CommonVariable.APPR_STATUS_8.equals(erpApprStatus) && !CommonVariable.APPR_STATUS_C.equals(erpApprStatus)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("提单当前状态不可操作！"));
        }
        if (!Arrays.asList(arrValidStatus).contains(dto.getSendApiStatus())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("清单状态不允许撤回！"));
        }
        if (CommonEnum.BondMarkEnum.NOBOND.getCode().endsWith(dto.getBondMark())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("该清单数据中有非保税数据！"));
        }
        // 数据标记 1：金二 2：H2000
        if (!ConstantsStatus.STATUS_1.equals(dto.getDataMark())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("非金二清单不允许撤回！"));
        }
        if (StringUtils.isBlank(dto.getEmsListNo())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("清单内部编号不可为空！"));
        }
    }
}
