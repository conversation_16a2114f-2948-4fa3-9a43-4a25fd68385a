<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.gwLvCitesBatchFileSkuList.dao.GwLvCitesBatchFileSkuListMapper">
    <resultMap id="citesProofSkuListResultMap" type="com.dcjet.cs.gwLvCitesBatchFileSkuList.model.GwLvCitesBatchFileSkuList">
		<result column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="out_side_no" property="outSideNo" jdbcType="VARCHAR" />
		<result column="hp_code" property="hpCode" jdbcType="VARCHAR" />
		<result column="cop_g_no" property="copGNo" jdbcType="VARCHAR" />
		<result column="brand_code" property="brandCode" jdbcType="VARCHAR" />
		<result column="declare_price" property="declarePrice" jdbcType="NUMERIC" />
		<result column="declare_num" property="declareNum" jdbcType="NUMERIC" />
		<result column="declare_total" property="declareTotal" jdbcType="NUMERIC" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="risk_quantity" property="riskQuantity" jdbcType="NUMERIC" />
		<result column="risk_suit_quantity" property="riskSuitQuantity" jdbcType="NUMERIC" />
		<result column="risk_quantity_sum" property="riskQuantitySum" jdbcType="NUMERIC" />
		<result column="model_num" property="modelNum" jdbcType="NUMERIC" />
		<result column="model_unit" property="modelUnit" jdbcType="VARCHAR" />
		<result column="g_name" property="gName" jdbcType="VARCHAR" />
		<result column="code_t_s" property="codeTS" jdbcType="VARCHAR" />
		<result column="species_name" property="speciesName" jdbcType="VARCHAR" />
		<result column="scientific_name" property="scientificName" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="customs_type" property="customsType" jdbcType="VARCHAR" />
		<result column="customsTypeName" property="customsTypeName" jdbcType="VARCHAR" />
		<result column="is_quota" property="isQuota" jdbcType="VARCHAR" />
		<result column="hp_maturity_date" property="hpMaturityDate" jdbcType="TIMESTAMP" />
		<result column="erp_factor" property="erpFactor" jdbcType="NUMERIC" />
		<result column="order_by" property="orderBy" jdbcType="NUMERIC" />
		<result column="cites_qty" property="citesQty" jdbcType="NUMERIC" />
		<result column="batch_file_no" property="batchFileNo" jdbcType="VARCHAR" />
		<result column="batch_file_serial" property="batchFileSerial" jdbcType="VARCHAR" />
	</resultMap>
	<resultMap id="skuListResultMap" type="com.dcjet.cs.gwLvCitesBatchFileSkuList.model.GwLvCitesBatchFileSkuList">
		<result column="erp_cop_g_no" property="erpCopGNo" jdbcType="VARCHAR" />
		<result column="hp_code" property="hpCode" jdbcType="VARCHAR" />
		<result column="hp_name" property="hpName" jdbcType="VARCHAR" />
		<result column="cop_g_no" property="copGNo" jdbcType="VARCHAR" />
		<result column="declare_num" property="declareNum" jdbcType="NUMERIC" />
		<result column="out_side_no" property="outSideNo" jdbcType="VARCHAR" />
		<result column="out_side_proof_valid" property="outSideProofValid" jdbcType="TIMESTAMP" />
		<result column="batch_file_punish_date" property="batchFilePunishDate" jdbcType="TIMESTAMP" />
	</resultMap>
	<sql id="condition">
	<if test="tradeCode != null and tradeCode != ''">
		and t.trade_code = #{tradeCode}
	</if>
	and t.head_id = #{headId}
    <if test="isQuota != null and isQuota != ''">
		and t.is_quota = #{isQuota}
	</if>
    <if test="speciesName != null and speciesName != ''">
	  and t.species_name like '%'|| #{speciesName} || '%'
	</if>
    <if test="outSideNo != null and outSideNo != ''">
	  and t.out_side_no like '%'|| #{outSideNo} || '%'
	</if>
    <if test="copGNo != null and copGNo != ''">
	  and t.cop_g_no like '%'|| #{copGNo} || '%'
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="citesProofSkuListResultMap" parameterType="com.dcjet.cs.gwLvCitesBatchFileSkuList.model.GwLvCitesBatchFileSkuList">
        select
		p.name customsTypeName,
        t.*
        from t_gw_lv_cites_batch_file_sku_list t
		LEFT JOIN t_gw_lv_customer_params P ON P.code = t.customs_type AND P.TYPE='CUSTOM_TYPE'
        <where>
            <!-- 用户Grid查询 and 条件-->
			<include refid="condition"></include>
        </where>
order by t.cop_g_no,t.update_time desc
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete t_gw_lv_cites_batch_file_sku_list from  t
		where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
              #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete t_gw_lv_cites_batch_file_sku_list from  t
		where
		t.head_id
		in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getListNumByHeadIds" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from t_gw_lv_cites_batch_file_sku_list t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>

	<select id="getSkuList" resultMap="skuListResultMap" parameterType="java.util.List">
		select
		c.erp_cop_g_no ,
		sku.cop_g_no ,
		hp.hp_name,
		sku.hp_code
		from t_gw_lv_cites_batch_file_sku_list sku
		inner join t_gw_lv_sku_bom_list bomL on bomL.cop_g_no=sku.cop_g_no and bomL.brand_code=sku.brand_code
		inner join t_gw_lv_sku_bom b on b.is_open='open' and boml.version_no=b.version_no and b.erp_cop_g_no = boml.erp_cop_g_no
		left join t_gw_lv_sku_main_cur c on sku.trade_code =c.trade_code and sku.cop_g_no = c.cop_g_no and c.is_open='open'
		left join t_gw_lv_cites_hp hp on hp.trade_code =sku.trade_code and hp.hp_code = sku.hp_code
		where sku.head_id in
		<foreach collection="headIds"  item="item" open="(" separator="," close=")"  >
			#{item}
		</foreach>
		<if test="isCitesProof">
			and exists(select 1 from t_gw_lv_cites_proof_list l where l.brand_code=sku.brand_code and l.out_side_no=sku.out_side_no and l.cop_g_no=sku.cop_g_no)
		</if>
order by sku.cop_g_no
	</select>


	<select id="getQuoTaList" resultMap="skuListResultMap" parameterType="java.util.List">
		select
		c.erp_cop_g_no ,
		hp.hp_name,
		sum(sku.declare_num) declare_num,
		sku.out_side_no,
		fl.out_side_proof_valid,
		fl.batch_file_punish_date
		from t_gw_lv_cites_batch_file_sku_list sku
		left join t_gw_lv_cites_batch_file_list fl on fl.head_id = sku.head_id and fl.out_side_no = sku.out_side_no
		left join t_gw_lv_sku_main_cur c on sku.trade_code =c.trade_code and sku.cop_g_no = c.cop_g_no and c.is_open='open'
		left join t_gw_lv_cites_hp hp on hp.trade_code =sku.trade_code and hp.hp_code = sku.hp_code
		where sku.head_id in
		<foreach collection="headIds"  item="item" open="(" separator="," close=")"  >
			#{item}
		</foreach>
		group by c.erp_cop_g_no ,hp.hp_name,
		sku.out_side_no,
		fl.out_side_proof_valid,
		fl.batch_file_punish_date
		order by sku.out_side_no,sum(sku.order_by)
	</select>

	<select id="getCitesDetailList" resultMap="citesProofSkuListResultMap" parameterType="java.util.List">
		select
		ROW_NUMBER() OVER() rownum,
sku.out_side_no,
			sku.species_name,
			sku.scientific_name,
			sku.code_t_s,
			sku.declare_num,
			sku.risk_quantity_sum,
			sku.declare_price,
			sku.curr,
			sku.declare_total,
			sku.cop_g_no,
			sku.g_name,
			(select sum(o.quantity) from t_gw_lv_cites_outcites o where o.out_side_no = sku.out_side_no and o.cop_g_no=sku.cop_g_no and o.brand_code=sku.brand_code) cites_qty,
			p.name customs_type,
			sku.model_num,
			sku.model_unit,
		       hp.hp_maturity_date,
		sku.hp_code,
		       list.batch_file_no,
		       m.batch_file_serial
		from t_gw_lv_cites_batch_file_sku_list sku
		    inner join t_gw_lv_cites_batch_file_list list on list.out_side_no=sku.out_side_no and list.head_id=sku.head_id
		    inner join t_gw_lv_cites_batch_file_main m on m.sid=sku.head_id
		    left join t_gw_lv_cites_hp hp on hp.brand_code=sku.brand_code and hp.hp_code=sku.hp_code
				LEFT JOIN t_gw_lv_customer_params P ON P.code = sku.customs_type AND P.TYPE='CUSTOM_TYPE'
		where
		sku.head_id in <foreach collection="headIds"  item="item" open="(" separator="," close=")"  >
			#{item}
		</foreach>
		<if test="isCitesProof">
			and exists(select 1 from t_gw_lv_cites_proof_list l where l.brand_code=sku.brand_code and l.out_side_no=sku.out_side_no and l.cop_g_no=sku.cop_g_no)
		</if>
		order by sku.out_side_no,sku.order_by
	</select>
</mapper>
