package com.dcjet.cs.baseBrandVCList.dao;

import com.dcjet.cs.baseBrandVCList.model.GwBaseBrandVcList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* GwBaseBrandVcList
* <AUTHOR>
* @date: 2022-3-18
*/
public interface GwBaseBrandVcListMapper extends Mapper<GwBaseBrandVcList> {
    /**
     * 查询获取数据
     * @param gwBaseBrandVcList
     * @return
     */
    List<GwBaseBrandVcList> getList(GwBaseBrandVcList gwBaseBrandVcList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int deleteByBrandSids(@Param("sids") List<String> sids);

    GwBaseBrandVcList getByBrandCode(@Param("brandCode") String brandCode,@Param("vendorCode") String vendorCode, @Param("tradeCode")String tradeCode);

    List<Map<String,String>> getComboxVCcode(@Param("brandCode") String brandCode, @Param("tradeCode")String tradeCode);
}
