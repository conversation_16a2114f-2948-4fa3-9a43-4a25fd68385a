package com.dcjet.cs.insure;

import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.insure.*;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpEListNMapper;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIListNMapper;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.RequestUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class InsureService {
    @Resource
    private CommonService commonService;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;
    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private DecErpEListNMapper decErpEListNMapper;
    @Resource
    private DecErpIListNMapper decErpIListNMapper;

    public ResultObject<List<InsureRateDto>> getCompanyRateList(PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<InsureRateDto>> resultObject = ResultObject.createInstance(true);

        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.COMPANY_RATE_URL);
        if (gwstdHttpConfig == null) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未配置获取企业级保司费率接口地址"));
        }
        String url = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();

        ResultObject<List<InsureRateDto>> rtnResultObject = null;
        try {
            String result = RequestUtil.sendGet(url, userInfo.getAccessToken());

            log.info("请求获取企业级保司费率接口，返回结果：" + result);

            rtnResultObject = jsonObjectMapper.fromJson(result, new TypeReference<ResultObject<List<InsureRateDto>>>() {
            });

            if (!rtnResultObject.isSuccess()) {
                resultObject.setSuccess(false);
                resultObject.setMessage(rtnResultObject.getMessage());
            } else {
                resultObject.setData(rtnResultObject.getData());
            }
        } catch (Exception e) {
            log.error("请求获取企业级保司费率接口失败", e);
            throw new ErrorException(400, "请求获取企业级保司费率接口失败");
        }

//        ResultObject<List<InsureDto>> rtn = ResultObject.createInstance(true);
//        rtn.setData(paginate(resultObject.getData(), pageParam));

        return resultObject;
    }

    /*public List<InsureDto> paginate(List<InsureDto> data, PageParam pageParam) {
        // 计算分页的起始索引和结束索引
        int startIndex = (pageParam.getPage() - 1) * pageParam.getLimit();
        int endIndex = pageParam.getPage() * pageParam.getLimit();

        if (startIndex >= data.size()) {
            return Collections.emptyList();
        }

        if (endIndex >= data.size()) {
            endIndex = data.size();
        }

        // 返回分页后的子列表
        return data.subList(startIndex, endIndex);
    }*/

    /**
     * 投保
     *
     * @param ieMark
     * @param sid
     * @param userInfo
     * @return
     */
    public ResultObject<String> insure(String ieMark, String sid, UserInfoToken userInfo) {
        ResultObject<String> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("投保成功"));

        PreHead preHead = null;
        List<PreListItem> preList = new ArrayList<>();

        if (CommonEnum.I_E_MARK_ENUM.I_MARK.getCode().equals(ieMark)) {
            preHead = decErpIHeadNMapper.getInsurePreHeadBySid(sid, userInfo.getCompany());
            preList = decErpIListNMapper.getInsurePreListByHeadId(sid, userInfo.getCompany());
        } else if (CommonEnum.I_E_MARK_ENUM.E_MARK.getCode().equals(ieMark)) {
            preHead = decErpEHeadNMapper.getInsurePreHeadBySid(sid, userInfo.getCompany());
            preList = decErpEListNMapper.getInsurePreListByHeadId(sid, userInfo.getCompany());
        }

        // 已投保过
        if (StringUtils.isNotBlank(preHead.getInsureId())) {
            resultObject.setData(preHead.getInsureId());
            return resultObject;
        }

        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.INSURE_GOODS_INFO_URL);
        if (gwstdHttpConfig == null) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未配置投保接口地址"));
        }
        String url = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();

        InsureRequestParam param = new InsureRequestParam();
        param.setPreHead(preHead);
        param.setPreList(preList);

        String postData = jsonObjectMapper.toJson(param);
        log.info("请求投保接口，请求参数：" + postData);

        try {
            String result = RequestUtil.sendPost(url, userInfo.getAccessToken(), postData);
            log.info("请求投保接口，返回结果：" + result);

            ResultObject<InsureResponse> insureResponseResultObject = jsonObjectMapper.fromJson(result, new TypeReference<ResultObject<InsureResponse>>() {
            });

            if (!insureResponseResultObject.isSuccess()) {
                resultObject.setSuccess(false);
                resultObject.setMessage(insureResponseResultObject.getMessage());
            } else {
                if (CommonEnum.I_E_MARK_ENUM.I_MARK.getCode().equals(ieMark)) {
                    decErpIHeadNMapper.updateInsureInfoBySid(sid, insureResponseResultObject.getData().getBatchId());
                } else if (CommonEnum.I_E_MARK_ENUM.E_MARK.getCode().equals(ieMark)) {
                    decErpEHeadNMapper.updateInsureInfoBySid(sid, insureResponseResultObject.getData().getBatchId());
                }
                resultObject.setData(insureResponseResultObject.getData().getBatchId());
            }
        } catch (Exception e) {
            log.error("请求投保口失败", e);
            throw new ErrorException(400, "请求投保接口失败");
        }
        return resultObject;
    }

    /**
     * 获取投保弹出页面地址
     *
     * @param token
     * @return
     */
    public ResultObject<String> getInsurePageUrl(UserInfoToken token) {
        ResultObject<String> resultObject = ResultObject.createInstance(true);

        GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.INSURE_PAGE_URL);
        if (gwstdHttpConfig == null) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未配置投保页面地址"));
        }
        resultObject.setData(gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl());
        return resultObject;
    }
}
