package com.dcjet.cs.clear;

import com.dcjet.cs.erp.dao.BillSyncHeadTmpMapper;
import com.dcjet.cs.erp.dao.BillSyncListTmpMapper;
import com.dcjet.cs.erp.dao.ImpTmpDecErpEListMapper;
import com.dcjet.cs.erp.dao.ImpTmpDecErpIListMapper;
import com.dcjet.cs.gwstd.dao.GwKafkaReceiveMapper;
import com.dcjet.cs.imp.dao.PostApiReturnInfoMapper;
import com.dcjet.cs.mqsync.dao.SyncKeyLogMapper;
import com.dcjet.cs.mqsync.dao.SyncLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: WJ
 * @createDate: 2021/3/17 17:39
 */
@Service
@Slf4j
public class ClearHistoryDataService {

    @Resource
    private PostApiReturnInfoMapper postApiReturnInfoMapper;
    @Resource
    private SyncKeyLogMapper syncKeyLogMapper;
    @Resource
    private ImpTmpDecErpEListMapper impTmpDecErpEListMapper;
    @Resource
    private ImpTmpDecErpIListMapper impTmpDecErpiListMapper;
    @Resource
    private SyncLogMapper syncLogMapper;
    @Resource
    private BillSyncHeadTmpMapper billSyncHeadTmpMapper;
    @Resource
    private BillSyncListTmpMapper billSyncListTmpMapper;
    @Resource
    private GwKafkaReceiveMapper kafkaReceiveMapper;

    /**
     * 每月1号执行关务数据清理
     */
    public void clearData() {
        // 只保留最近四个月的数据
        postApiReturnInfoMapper.deleteHistoryDate();
        // 只保留最近四个月的数据
        syncKeyLogMapper.deleteHistoryDate();
        // 只保留最近三个月的数据
        impTmpDecErpEListMapper.deleteHistoryDate();
        // 只保留最近三个月的数据
        impTmpDecErpiListMapper.deleteHistoryDate();
        // 只保留最近七个月的数据
        syncLogMapper.deleteHistoryDate();
        // 只保留近一周的数据
        billSyncListTmpMapper.deleteHistoryDate();
        billSyncHeadTmpMapper.deleteHistoryDate();
        // 只保留最近15内的数据
        kafkaReceiveMapper.deleteHistoryDate();
    }
}
