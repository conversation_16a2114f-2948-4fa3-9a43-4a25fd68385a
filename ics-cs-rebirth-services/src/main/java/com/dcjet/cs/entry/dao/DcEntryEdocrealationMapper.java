package com.dcjet.cs.entry.dao;

import com.dcjet.cs.entry.model.DcEntryEdocrealation;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * DcEntryEdocrealation
 *
 * <AUTHOR>
 * @date: 2019-10-23
 */
public interface DcEntryEdocrealationMapper extends Mapper<DcEntryEdocrealation> {
    /**
     * 查询获取数据
     *
     * @param dcEntryEdocrealation
     * @return
     */
    List<DcEntryEdocrealation> getList(DcEntryEdocrealation dcEntryEdocrealation);

    List<DcEntryEdocrealation> getListByEntryListNo(@Param("entryListNo") String entryListNo);

    List<DcEntryEdocrealation> getSendList(DcEntryEdocrealation dcEntryEdocrealation);

    List<DcEntryEdocrealation> getFile(@Param("headId") String headId, @Param("edocCodeList") List<String> edocCodeList, @Param("type") String type, @Param("tradeCode") String tradeCode);

    void syncErpDeclareModule(@Param("headId") String headId, @Param("erpHeadId") String erpHeadId);

    /**
     * 批量删除
     *
     * @param sids
     * @return 0:成功 1:失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 按entryListNo批量删除
     *
     * @param entryListNos
     * @return
     */
    int deleteByEntryListNos(List<String> entryListNos);

    /**
     * 按emsListNo删除
     *
     * @param emsListNo
     * @return
     */
    int deleteByEmsListNo(@Param("emsListNo") String emsListNo);

    /**
     * 获取待发送的随附单据数据
     *
     * @param map
     * @return
     */
    List<DcEntryEdocrealation> getEntryEdocrealations(Map<String, Object> map);

    /**
     * 根据参数查询
     *
     * @param map
     * @return
     */
    List<Map<String, Object>> selectEdocRealation(Map<String, Object> map);

    /**
     * 更新随附单证发送状态
     *
     * @param map
     */
    void updateEdocStatus(Map<String, Object> map);

    /**
     * 查询获取随附单据表体sid数据
     *
     * @param map
     * @return
     */
    List<String> getExistEntryEdocrealationSid(Map<String, Object> map);

    /**
     * 根据sid获取单条随附单据数据
     *
     * @param map
     * @return
     */
    DcEntryEdocrealation getEntryEdocrealation(Map<String, Object> map);
}
