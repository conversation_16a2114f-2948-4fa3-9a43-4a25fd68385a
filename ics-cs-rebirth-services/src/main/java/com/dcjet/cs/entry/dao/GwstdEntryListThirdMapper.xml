<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.entry.dao.GwstdEntryListThirdMapper">
    <resultMap id="gwstdEntryListThirdResultMap" type="com.dcjet.cs.entry.model.GwstdEntryListThird">
        <id column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
        <result column="seq_no" property="seqNo" jdbcType="VARCHAR"/>
        <result column="ciq_code" property="ciqCode" jdbcType="VARCHAR"/>
        <result column="ciqdest_code" property="ciqdestCode" jdbcType="VARCHAR"/>
        <result column="ciq_name" property="ciqName" jdbcType="VARCHAR"/>
        <result column="code_ts" property="codeTs" jdbcType="VARCHAR"/>
        <result column="contr_item" property="contrItem" jdbcType="NUMERIC"/>
        <result column="curr" property="curr" jdbcType="VARCHAR"/>
        <result column="danger_flag" property="dangerFlag" jdbcType="VARCHAR"/>
        <result column="dang_name" property="dangName" jdbcType="VARCHAR"/>
        <result column="data_source" property="dataSource" jdbcType="VARCHAR"/>
        <result column="decl_price" property="declPrice" jdbcType="NUMERIC"/>
        <result column="decl_total" property="declTotal" jdbcType="NUMERIC"/>
        <result column="destination_country" property="destinationCountry" jdbcType="VARCHAR"/>
        <result column="district_code" property="districtCode" jdbcType="VARCHAR"/>
        <result column="goods_attr" property="goodsAttr" jdbcType="VARCHAR"/>
        <result column="goods_brand" property="goodsBrand" jdbcType="VARCHAR"/>
        <result column="goods_model" property="goodsModel" jdbcType="VARCHAR"/>
        <result column="goods_spec" property="goodsSpec" jdbcType="VARCHAR"/>
        <result column="g_name" property="gname" jdbcType="VARCHAR"/>
        <result column="g_model" property="gmodel" jdbcType="VARCHAR"/>
        <result column="g_no" property="gno" jdbcType="NUMERIC"/>
        <result column="g_qty" property="gqty" jdbcType="NUMERIC"/>
        <result column="g_unit" property="gunit" jdbcType="VARCHAR"/>
        <result column="produce_date" property="produceDate" jdbcType="TIMESTAMP"/>
        <result column="origin_country" property="originCountry" jdbcType="VARCHAR"/>
        <result column="pack_model" property="packModel" jdbcType="VARCHAR"/>
        <result column="pack_type" property="packType" jdbcType="VARCHAR"/>
        <result column="prod_validdt" property="prodValiddt" jdbcType="TIMESTAMP"/>
        <result column="qty_1" property="qty1" jdbcType="NUMERIC"/>
        <result column="unit_1" property="unit1" jdbcType="VARCHAR"/>
        <result column="qty_2" property="qty2" jdbcType="NUMERIC"/>
        <result column="unit_2" property="unit2" jdbcType="VARCHAR"/>
        <result column="stuff" property="stuff" jdbcType="VARCHAR"/>
        <result column="dang_code" property="dangCode" jdbcType="VARCHAR"/>
        <result column="use_to" property="useTo" jdbcType="VARCHAR"/>
        <result column="duty_mode" property="dutyMode" jdbcType="VARCHAR"/>
        <result column="entry_id" property="entryId" jdbcType="VARCHAR"/>
        <result column="class_mark" property="classMark" jdbcType="VARCHAR"/>
        <result column="district_post_code" property="districtPostCode" jdbcType="VARCHAR"/>
        <result column="work_usd" property="workUsd" jdbcType="NUMERIC"/>
        <result column="exg_no" property="exgNo" jdbcType="VARCHAR"/>
        <result column="trade_total" property="tradeTotal" jdbcType="NUMERIC"/>
        <result column="mnufctrre_gno" property="mnufctrreGno" jdbcType="VARCHAR"/>
        <result column="orig_place_code" property="origPlaceCode" jdbcType="VARCHAR"/>
        <result column="un_code" property="unCode" jdbcType="VARCHAR"/>
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="origin_country_name" property="originCountryName" jdbcType="VARCHAR"/>
        <result column="destination_country_name" property="destinationCountryName" jdbcType="VARCHAR"/>
        <result column="duty_mode_name" property="dutyModeName" jdbcType="VARCHAR"/>
        <result column="district_code_name" property="districtCodeName" jdbcType="VARCHAR"/>
        <result column="unit_1_convert" property="unit1Convert" jdbcType="VARCHAR"/>
        <result column="unit_2_convert" property="unit2Convert" jdbcType="VARCHAR"/>
        <result column="g_unit_convert" property="gunitConvert" jdbcType="VARCHAR"/>
        <result column="curr_convert" property="currConvert" jdbcType="VARCHAR"/>
        <result column="origin_country_convert" property="originCountryConvert" jdbcType="VARCHAR"/>
        <result column="destination_country_convert" property="destinationCountryConvert" jdbcType="VARCHAR"/>
        <result column="district_post_code_name" property="districtPostCodeName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     sid
     ,trade_code
     ,head_id
     ,seq_no
     ,ciq_code
     ,ciqdest_code
     ,ciq_name
     ,code_ts
     ,contr_item
     ,curr
     ,danger_flag
     ,dang_name
     ,data_source
     ,decl_price
     ,decl_total
     ,destination_country
     ,district_code
     ,goods_attr
     ,goods_brand
     ,goods_model
     ,goods_spec
     ,g_name
     ,g_model
     ,g_no
     ,g_qty
     ,g_unit
     ,produce_date
     ,origin_country
     ,pack_model
     ,pack_type
     ,prod_validdt
     ,qty_1
     ,unit_1
     ,qty_2
     ,unit_2
     ,stuff
     ,dang_code
     ,use_to
     ,duty_mode
     ,entry_id
     ,class_mark
     ,district_post_code
     ,work_usd
     ,exg_no
     ,trade_total
     ,mnufctrre_gno
     ,orig_place_code
     ,un_code
     ,insert_user
     ,insert_user_name
     ,insert_time
     ,update_user
     ,update_user_name
     ,update_time
     ,origin_country_name
     ,destination_country_name
     ,duty_mode_name
     ,district_code_name
     ,unit_1_convert
     ,unit_2_convert
     ,g_unit_convert
     ,curr_convert
     ,origin_country_convert
     ,destination_country_convert
     ,district_post_code_name
    </sql>
    <sql id="condition">
        <if test="headId != null and headId != ''">
            head_id = #{headId}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin -->
    <select id="getList" resultMap="gwstdEntryListThirdResultMap"
            parameterType="com.dcjet.cs.entry.model.GwstdEntryListThird">
        select
        <include refid="Base_Column_List"/>
        from t_gwstd_entry_list_third t
        <where>
            <!-- 用户Grid查询 and 条件-->
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_gwstd_entry_list_third t
        where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete from t_gwstd_entry_list_third t
        where
        t.head_id
        in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="getListNumByHeadIds" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from t_gwstd_entry_list_third t where t.head_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
