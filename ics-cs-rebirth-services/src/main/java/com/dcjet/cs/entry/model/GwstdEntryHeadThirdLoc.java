package com.dcjet.cs.entry.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2023-3-20
 */
@Setter
@Getter
@Table(name = "t_gwstd_entry_head_third_loc")
public class GwstdEntryHeadThirdLoc implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * 企业编码
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 申报单位社会信用代码
     */
    @Column(name = "agent_code_scc_loc")
    private String agentCodeSccLoc;
    /**
     * 申报单位名称
     */
    @Column(name = "agent_name_loc")
    private String agentNameLoc;
    /**
     * 提运单号码
     */
    @Column(name = "bill_no_loc")
    private String billNoLoc;
    /**
     * 境内收发货人社会信用代码
     */
    @Column(name = "trade_code_scc_loc")
    private String tradeCodeSccLoc;
    /**
     * 境内收发货人名称
     */
    @Column(name = "trade_name_loc")
    private String tradeNameLoc;
    /**
     * 境外收发货人编码
     */
    @Column(name = "overseas_trade_code_loc")
    private String overseasTradeCodeLoc;
    /**
     * 合同协议号
     */
    @Column(name = "contr_no_loc")
    private String contrNoLoc;
    /**
     * 征免性质编码
     */
    @Column(name = "cut_mode_loc")
    private String cutModeLoc;
//    /**
//     * 启运港(进口)/离境口岸(出口)
//     */
//    @Column(name = "desp_port_code_loc")
//    private String despPortCodeLoc;
    /**
     * 指运港/经停港(名称）
     */
    @Column(name = "distinate_port_loc")
    private String distinatePortLoc;
    /**
     * 申报日期
     */
    @Column(name = "d_date_loc")
    @JsonProperty("dDateLoc")
    private String dDateLoc;
    /**
     * 海关编号
     */
    @Column(name = "entry_id_loc")
    private String entryIdLoc;
    /**
     * 运费
     */
    @Column(name = "freight_loc")
    private String freightLoc;
    /**
     * 毛重
     */
    @Column(name = "gross_wt_loc")
    private String grossWtLoc;
    /**
     * 保费
     */
    @Column(name = "insurance_loc")
    private String insuranceLoc;
    /**
     * 进出口岸代码（进境关别（出境关别））
     */
    @Column(name = "i_e_port_loc")
    @JsonProperty("iEPortLoc")
    private String iEPortLoc;
    /**
     * 进出口日期
     */
    @Column(name = "i_e_date_loc")
    @JsonProperty("iEDateLoc")
    private String iEDateLoc;
    /**
     * 手册号码（备案号）
     */
    @Column(name = "manual_no_loc")
    private String manualNoLoc;
    /**
     * 申报地海关
     */
    @Column(name = "master_customs_loc")
    private String masterCustomsLoc;
    /**
     * 净重
     */
    @Column(name = "net_wt_loc")
    private String netWtLoc;
    /**
     * 备注
     */
    @Column(name = "note_loc")
    private String noteLoc;
    /**
     * 杂费
     */
    @Column(name = "other_loc")
    private String otherLoc;
    /**
     * 货主单位/消费使用单位（18位社会信用编码）--进口 /生产销售单位（18位社会信用编码）--出口
     */
    @Column(name = "owner_scc_loc")
    private String ownerSccLoc;
    /**
     * 货主单位名称（消费使用单位）
     */
    @Column(name = "owner_name_loc")
    private String ownerNameLoc;
    /**
     * 件数
     */
    @Column(name = "pack_no_loc")
    private String packNoLoc;
    /**
     * 监管方式
     */
    @Column(name = "trade_mode_loc")
    private String tradeModeLoc;
    /**
     * 启运国/运抵国
     */
    @Column(name = "trade_country_loc")
    private String tradeCountryLoc;
    /**
     * 运输工具名称
     */
    @Column(name = "traf_name_loc")
    private String trafNameLoc;
    /**
     * 成交方式
     */
    @Column(name = "trans_mode_loc")
    private String transModeLoc;
    /**
     * 运输工具航次(班)号
     */
    @Column(name = "voyage_no_loc")
    private String voyageNoLoc;
    /**
     * 包装种类
     */
    @Column(name = "wrap_type_loc")
    private String wrapTypeLoc;
    /**
     * 许可证编号
     */
    @Column(name = "license_no_loc")
    private String licenseNoLoc;
    /**
     * 运输方式代码
     */
    @Column(name = "traf_mode_loc")
    private String trafModeLoc;
    /**
     * 贸易国别（地区）代码
     */
    @Column(name = "trade_area_code_loc")
    private String tradeAreaCodeLoc;
    /**
     * 特殊关系确认
     */
    @Column(name = "confirm_special_loc")
    private String confirmSpecialLoc;
    /**
     * 价格影响确认
     */
    @Column(name = "confirm_price_loc")
    private String confirmPriceLoc;
    /**
     * 支付特许权使用费确认
     */
    @Column(name = "confirm_royalties_loc")
    private String confirmRoyaltiesLoc;
    /**
     * 入境口岸(进口专用)
     */
    @Column(name = "entry_port_code_loc")
    private String entryPortCodeLoc;
    /**
     * 创建人
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 创建人名称
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 进出境关别名称
     */
    @Column(name = "i_e_port_name_loc")
    @JsonProperty("iEPortNameLoc")
    private String iEPortNameLoc;
    /**
     * 征免性质名称
     */
    @Column(name = "cut_mode_name_loc")
    private String cutModeNameLoc;
    /**
     * 监管方式名称
     */
    @Column(name = "trade_mode_name_loc")
    private String tradeModeNameLoc;
    /**
     * 贸易国名称
     */
    @Column(name = "trade_area_name_loc")
    private String tradeAreaNameLoc;
    /**
     * 启运国/运抵国名称
     */
    @Column(name = "trade_country_name_loc")
    private String tradeCountryNameLoc;
    /**
     * 指运港/经停港名称
     */
    @Column(name = "distinate_port_name_loc")
    private String distinatePortNameLoc;
    /**
     * 进出境口岸
     */
    @Column(name = "entry_port_name_loc")
    private String entryPortNameLoc;
    /**
     * 包装种类名称
     */
    @Column(name = "wrap_type_name_loc")
    private String wrapTypeNameLoc;
    /**
     * 成交方式名称
     */
    @Column(name = "trans_mode_name_loc")
    private String transModeNameLoc;
    /**
     * 公式定价确认
     */
    @Column(name = "confirm_formula_price_loc")
    private String confirmFormulaPriceLoc;
    /**
     * 暂定价格确认
     */
    @Column(name = "confirm_temp_price_loc")
    private String confirmTempPriceLoc;
    /**
     * 自报自缴
     */
    @Column(name = "duty_self_loc")
    private String dutySelfLoc;
    /**
     * 随附单证及编号
     */
    @Column(name = "acmp_no_loc")
    private String acmpNoLoc;
}
