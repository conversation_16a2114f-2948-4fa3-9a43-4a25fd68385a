package com.dcjet.cs.entry.mapper;


import com.dcjet.cs.dto.entry.DecIEntryHeadDto;
import com.dcjet.cs.dto.entry.DecIEntryHeadParam;
import com.dcjet.cs.dto.imp.NormalEntryHeadParam;
import com.dcjet.cs.dto.imp.NormalEntryRetInfoReturn;
import com.dcjet.cs.entry.model.DecEntryHead;
import com.dcjet.cs.entry.model.DecIEntryHead;
import com.dcjet.cs.imp.model.EntryHead;
import com.dcjet.cs.imp.model.EntryReplyDetail;
import org.mapstruct.*;

/**
* generated by Generate dcits
* 进口报关单表头
* @author: 鏈辨涓�
* @date: 2019-03-13
*/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecIEntryHeadDtoMapper {

   /**
   * 杞崲鏁版嵁搴撳璞″埌DTO
   * @param po
   * @return
   */
   DecIEntryHeadDto toDto(DecIEntryHead po);

   /**
   * 杞崲Param鍒版暟鎹簱瀵硅薄
   * @param param
   * @return
   */
   DecIEntryHead toPo(DecIEntryHeadParam param);

   /**
    * 杞崲Param鍒版暟鎹簱瀵硅薄
    * @param param
    * @return
    */
   @Mappings({
     @Mapping(source = "hawb", target = "BillNo"),
     @Mapping(source = "insertUser", target = "Inputer"),
     @Mapping(source = "IEPort", target = "IEPort"),
     @Mapping(source = "emsNo", target = "ManualNo"),
     @Mapping(source = "trafMode", target = "TrafMode"),
     @Mapping(source = "declareCode", target = "AgentCode"),
     @Mapping(source = "destPort", target = "DistinatePort"),
     @Mapping(source = "packNum", target = "PackNo"),
     @Mapping(source = "masterCustoms", target = "MasterCustom"),
     @Mapping(source = "declareName", target = "AgentName"),
     @Mapping(source = "emsListNo", target = "CompSeqNo"),
     @Mapping(source = "declTrnrel", target = "EntryTransitType"),
     @Mapping(source = "promiseItems", target = "PromiseItmes"),
     @Mapping(source = "tradeNation", target = "TradeAreaCode"),
     @Mapping(source = "chkSurety", target = "CheckSurety"),
     @Mapping(source = "despPort", target = "Despportcode"),
     @Mapping(source = "entryPort", target = "Entyportcode"),
     @Mapping(source = "warehouse", target = "Goodsplace"),
     @Mapping(source = "declarerName", target = "Declarename"),
     @Mapping(source = "overseasShipper", target = "Overseasconsignorcode"),
     @Mapping(source = "overseasShipperName", target = "Overseasconsignorcname"),
     @Mapping(source = "overseasShipperEname", target = "Overseasconsignorename"),
     @Mapping(source = "overseasShipperAddr", target = "Overseasconsignoraddr"),
     @Mapping(source = "tradeCiqCode", target = "TradeCodeCiq"),
     @Mapping(source = "ownerCiqCode", target = "OwnerCodeCiq"),
     @Mapping(source = "declCiqCode", target = "AgentCodeCiq")
   })
   EntryHead toEnt(DecIEntryHead param);

   DecIEntryHead toPo(EntryReplyDetail entryReplyDetail);

    NormalEntryHeadParam toEnt2(DecIEntryHead decIEntryHead);

    DecIEntryHead toPo2(NormalEntryRetInfoReturn retInfoReturn);

   void toEntryHead(DecIEntryHead entity, @MappingTarget DecEntryHead head);
   NormalEntryHeadParam toNormalEntry(DecEntryHead entity);
}
