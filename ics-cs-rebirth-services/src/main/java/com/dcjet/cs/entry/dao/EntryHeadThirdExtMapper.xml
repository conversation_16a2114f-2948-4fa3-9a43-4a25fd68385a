<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.entry.dao.EntryHeadThirdExtMapper">
    <resultMap id="resultMap" type="com.dcjet.cs.entry.model.EntryHeadThirdExt">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="APPROVAL_STATUS" property="approvalStatus" jdbcType="VARCHAR"/>
        <result column="APPROVAL_NOTE" property="approvalNote" jdbcType="VARCHAR"/>
        <result column="SEND_STATUS" property="sendStatus" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="DATE"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE"/>
    </resultMap>

    <sql id="columns">
        t.SID,
		t.TRADE_CODE,
		t.APPROVAL_STATUS,
		t.APPROVAL_NOTE,
		t.SEND_STATUS,
		t.INSERT_USER,
		t.INSERT_USER_NAME,
		t.INSERT_TIME,
		t.UPDATE_USER,
		t.UPDATE_USER_NAME,
		t.UPDATE_TIME 
    </sql>
    <sql id="condition">
        <if test="tradeCode != null and tradeCode != ''">
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        </if>
        <if test="approvalStatus != null and approvalStatus != ''">
            and t.APPROVAL_STATUS = #{approvalStatus,jdbcType=VARCHAR}
        </if>
		<if test="approvalNote != null and approvalNote != ''">
            and t.APPROVAL_NOTE = #{approvalNote,jdbcType=VARCHAR}
        </if>
		<if test="sendStatus != null and sendStatus != ''">
            and t.SEND_STATUS = #{sendStatus,jdbcType=VARCHAR}
        </if>
		 
    </sql>  
    <select id="getList" parameterType="com.dcjet.cs.entry.model.EntryHeadThirdExt" resultType="com.dcjet.cs.entry.model.EntryHeadThirdExt">
        SELECT 
        <include refid="columns"/>
        FROM T_GWSTD_ENTRY_HEAD_THIRD_EXT t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
</mapper>