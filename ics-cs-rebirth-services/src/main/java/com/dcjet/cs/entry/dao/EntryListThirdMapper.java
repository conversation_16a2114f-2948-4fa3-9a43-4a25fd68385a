package com.dcjet.cs.entry.dao;

import com.dcjet.cs.entry.model.EntryListThird;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 报关单表体(第三方系统向单一窗口申报前的草单数据)
 * <p>
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-03-23
 */
public interface EntryListThirdMapper extends Mapper<EntryListThird> {

    /**
     * 查询获取数据
     *
     * @param param
     * @return
     */
    List<EntryListThird> getList(EntryListThird param);

    /**
     * 批量删除
     *
     * @param headId
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteByHeadId(@Param("headId") String headId);

} 