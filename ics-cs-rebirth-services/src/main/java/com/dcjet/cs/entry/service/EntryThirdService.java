package com.dcjet.cs.entry.service;

import com.dcjet.cs.aeo.dao.AeoAuditInfoMapper;
import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.common.component.AgentAuthManager;
import com.dcjet.cs.common.dao.AgentAuthMapper;
import com.dcjet.cs.common.dao.GwstdHttpConfigMapper;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.model.RequestLog;
import com.dcjet.cs.common.model.RequestLogEvent;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.entry.*;
import com.dcjet.cs.dto.notice.NoticeTaskEventParam;
import com.dcjet.cs.dto.war.WarringPassConfigDto;
import com.dcjet.cs.dto.war.WarringPassExpandConfigDto;
import com.dcjet.cs.entry.dao.*;
import com.dcjet.cs.entry.mapper.EntryThirdDtoMapper;
import com.dcjet.cs.entry.model.*;
import com.dcjet.cs.notice.service.event.NoticeTaskEvent;
import com.dcjet.cs.ocrInvoice.dao.GwOcrLogMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrLog;
import com.dcjet.cs.util.*;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.dcjet.cs.war.service.WarringPassExpandConfigService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class EntryThirdService {

    private final Logger logger = LoggerFactory.getLogger(EntryThirdService.class);

    @Value("${dc.export.temp:}")
    private String tempPath;
    @Resource
    private EntryHeadThirdMapper entryHeadThirdMapper;
    @Resource
    private EntryHeadThirdExtMapper entryHeadThirdExtMapper;
    @Resource
    private EntryListThirdMapper entryListThirdMapper;
    @Resource
    private EntryContainerThirdMapper entryContainerThirdMapper;
    @Resource
    private EntryThirdDtoMapper entryThirdDtoMapper;
    @Resource
    private DecIEntryHeadMapper decIEntryHeadMapper;
    @Resource
    private DecEEntryHeadMapper decEEntryHeadMapper;
    @Resource
    private AgentAuthManager agentAuthManager;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Resource
    private EntryThirdSendApproval entryThirdSendApproval;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    @Resource
    private AgentAuthMapper agentAuthMapper;
    @Resource
    private GwOcrLogMapper gwOcrLogMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private GwstdHttpConfigMapper httpConfigMapper;
    @Resource
    private ExcelService excelService;
    @Resource
    private ValidatorUtil validatorUtil;
    @Resource
    private AeoAuditInfoMapper aeoAuditInfoMapper;
    @Resource
    private WarringPassConfigService warringPassConfigService;
    @Resource
    private WarringPassExpandConfigService warringPassExpandConfigService;

    public ResultObject post(EntryHeadThirdParam param, HttpHeaders headers) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        recordLog("v1/public/agent/entry",
                null,
                RequestLog.BIZ_AGENT,
                JsonObjectMapper.getInstance().toJson(param),
                JsonObjectMapper.getInstance().toJson(agentAuthManager.getAuthInfo(headers)));

        agentAuthManager.getAndValidation(headers, param.getTradeCode());
        String errMsg = validatorUtil.validation(param);
        if (!Strings.isNullOrEmpty(errMsg)) {
            resultObject.setSuccess(false);
            resultObject.setCode(400);
            resultObject.setMessage(errMsg);
            return resultObject;
        }

        if (CommonVariable.IE_MARK_I.equals(param.getIEFlag())) {
            DecIEntryHead headParam = new DecIEntryHead();
            headParam.setTradeCode(param.getTradeCode());
            headParam.setEmsListNo(param.getEmsListNo());
            List<DecIEntryHead> headList = decIEntryHeadMapper.getList(headParam);
            if (headList.size() == 0) {
                resultObject.setSuccess(false);
                resultObject.setCode(400);
                resultObject.setMessage(xdoi18n.XdoI18nUtil.t("企业内部编号或企业编码错误"));
                return resultObject;
            }
        } else {
            DecEEntryHead headParam = new DecEEntryHead();
            headParam.setTradeCode(param.getTradeCode());
            headParam.setEmsListNo(param.getEmsListNo());
            List<DecEEntryHead> headList = decEEntryHeadMapper.getList(headParam);
            if (headList.size() == 0) {
                resultObject.setSuccess(false);
                resultObject.setCode(400);
                resultObject.setMessage(xdoi18n.XdoI18nUtil.t("企业内部编号或企业编码错误"));
                return resultObject;
            }
        }
        EntryHeadThird headThird = entryHeadThirdMapper.selectByEmsListNo(param.getEmsListNo(), param.getTradeCode());
        if (headThird == null) {
            headThird = new EntryHeadThird();
        } else {
            EntryHeadThirdExt headThirdExt = entryHeadThirdExtMapper.selectByPrimaryKey(headThird.getSid());
            if (headThirdExt != null && headThirdExt.isApprovalSuccess()) {
                resultObject.setSuccess(false);
                resultObject.setCode(400);
                resultObject.setMessage(xdoi18n.XdoI18nUtil.t("当前数据已经审批通过，无法接受数据"));
                return resultObject;
            }

            if (headThirdExt != null && EntryHeadThirdExt.APPROVAL_STATUS_BACK.equals(headThirdExt.getApprovalStatus())) {
                headThirdExt.setApprovalStatus(EntryHeadThirdExt.APPROVAL_STATUS_WAITING);
                entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
            }

        }

        entryThirdDtoMapper.updatePo(param, headThird);
        if (Strings.isNullOrEmpty(headThird.getSid())) {
            headThird.preInsert(null);
            entryHeadThirdMapper.insert(headThird);
        } else {
            headThird.preUpdate(null);
            entryHeadThirdMapper.updateByPrimaryKey(headThird);
            entryListThirdMapper.deleteByHeadId(headThird.getSid());
            entryContainerThirdMapper.deleteByHeadId(headThird.getSid());
        }

        try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
            EntryListThirdMapper listMapper = sqlSession.getMapper(EntryListThirdMapper.class);
            for (EntryListThirdParam listThirdParam : param.getList()) {
                errMsg = validatorUtil.validation(listThirdParam);
                if (!Strings.isNullOrEmpty(errMsg)) {
                    resultObject.setSuccess(false);
                    resultObject.setCode(400);
                    resultObject.setMessage(String.format("[%s]%s",errMsg,xdoi18n.XdoI18nUtil.t("表体")));
                    return resultObject;
                }
                EntryListThird listThird = entryThirdDtoMapper.toListPo(listThirdParam);
                listThird.setHeadId(headThird.getSid());
                listThird.preInsert(null);
                listThird.setTradeCode(param.getTradeCode());
                listMapper.insert(listThird);
            }
            sqlSession.flushStatements();

            EntryContainerThirdMapper containerMapper = sqlSession.getMapper(EntryContainerThirdMapper.class);
            for (EntryContainerThirdParam containerThirdParam : param.getContainers()) {
                errMsg = validatorUtil.validation(containerThirdParam);
                if (!Strings.isNullOrEmpty(errMsg)) {
                    resultObject.setSuccess(false);
                    resultObject.setCode(400);
                    resultObject.setMessage(String.format("[%s]%s",errMsg,xdoi18n.XdoI18nUtil.t("集装箱")));
                    return resultObject;
                }
                EntryContainerThird containerThird = entryThirdDtoMapper.toContainerPo(containerThirdParam);
                containerThird.setHeadId(headThird.getSid());
                containerThird.preInsert(null);
                containerThird.setTradeCode(param.getTradeCode());
                containerMapper.insert(containerThird);
            }
            sqlSession.flushStatements();

        }

        try {
            applicationEventPublisher.publishEvent(new NoticeTaskEvent(
                    new NoticeTaskEventParam(
                            CommonVariable.IE_MARK_I.equals(param.getIEFlag()) ? ConstantsNoticeItem.ENTRY_I_RECEIVED : ConstantsNoticeItem.ENTRY_E_RECEIVED,
                            param.getTradeCode(),
                            headThird.getEmsListNo())));
        } catch (Exception ex) {
            logger.error("发送通知出错", ex);
        }
        return resultObject;
    }

    public void approval(String ieMark, DecEntryApprovalParam param, UserInfoToken token) {
        String emsListNo = getEmsListNoById(ieMark, param.getSid(), token);

        ResultObject result = ResultObject.createInstance();
        boolean approvedByreview = false;
        List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(token);
        if (CollectionUtils.isNotEmpty(warringPassConfigDtos)) {
            WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
            List<WarringPassExpandConfigDto> warringPassExpandConfigs = warringPassExpandConfigService.selectAll(warringPassConfigDto.getSid(), token);
            if (warringPassExpandConfigs != null) {
                if (warringPassExpandConfigs.size() > 0) {
                    WarringPassExpandConfigDto warringPassExpandConfigDto = warringPassExpandConfigs.get(0);
                    if (StringUtils.isNotBlank(warringPassExpandConfigDto.getUploadChecklist()) && StringUtils.equals(warringPassExpandConfigDto.getUploadChecklist(), ConstantsStatus.STATUS_0)) {
                        approvedByreview = true;
                    }
                }
            }
        }
        EntryHeadThird thirdHead = new EntryHeadThird();
        if (approvedByreview){
            thirdHead = entryHeadThirdMapper.selectByEmsListNo(emsListNo, token.getCompany());
            if (thirdHead == null) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("未接受到回传的数据，无法进行审批"));
            }
        }else {
            thirdHead = entryHeadThirdMapper.selectByPrimaryKey(param.getSid());
            if (thirdHead==null) {
                thirdHead.setEmsListNo(emsListNo);
                thirdHead.setSid(param.getSid());
                thirdHead.setDataSource("3");
                thirdHead.setIEMark(ieMark);
                thirdHead.setTradeCode(token.getCompany());
                entryHeadThirdMapper.insert(thirdHead);
            }
        }

        EntryHeadThirdExt headThirdExt = entryHeadThirdExtMapper.selectByPrimaryKey(thirdHead.getSid());
        final boolean existExt = headThirdExt == null;
        if (headThirdExt == null) {
            headThirdExt = new EntryHeadThirdExt();
            headThirdExt.preInsert(token);
        }
        if (StringUtils.isNotBlank(thirdHead.getSid())){
            headThirdExt.setSid(thirdHead.getSid());
        }else {
            headThirdExt.setSid(UUID.randomUUID().toString());
        }
        headThirdExt.setApprovalStatus(param.getApprovalStatus());
        headThirdExt.setApprovalNote(param.getApprovalNote());
        if (existExt) {
            entryHeadThirdExtMapper.insert(headThirdExt);
        } else {
            headThirdExt.preUpdate(token);
            entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
        }

        if (approvedByreview) {
            if (agentAuthMapper.getByAgent(thirdHead.getAgentCode()) != null) {
                EntryThirdApprovalParam approvalParam = new EntryThirdApprovalParam();
                approvalParam.setApprove(EntryHeadThirdExt.APPROVAL_STATUS_PASS.equals(param.getApprovalStatus()) ? "1" : "0");
                approvalParam.setTradeCode(token.getCompany());
                approvalParam.setEmsListNo(thirdHead.getEmsListNo());
                approvalParam.setNote(param.getApprovalNote());

                ResultObject resultObject = entryThirdSendApproval.approval(approvalParam, thirdHead.getAgentCode(), token);

                headThirdExt.setSendStatus(resultObject.isSuccess() ? "1" : "-1");
                headThirdExt.preUpdate(token);
                entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
            }
        }else {
            headThirdExt.setApprovalStatus(param.getApprovalStatus());
            headThirdExt.preUpdate(token);
            entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
        }


        if (StringUtils.equals(headThirdExt.getApprovalStatus(), ConstantsStatus.STATUS_1)) {
            auditInfo(param.getSid(), ConstantsStatus.STATUS_1, param.getApprovalNote(), null, token);
        } else if (!StringUtils.equals(headThirdExt.getApprovalStatus(), ConstantsStatus.STATUS_1)) {
            auditInfo(param.getSid(), "-1", param.getApprovalNote(), null, token);
        }

        String bizCode;
        if (CommonVariable.IE_MARK_I.equals(thirdHead.getIEMark())) {
            bizCode = EntryHeadThirdExt.APPROVAL_STATUS_PASS.equals(param.getApprovalStatus()) ? ConstantsNoticeItem.ENTRY_I_PASS : ConstantsNoticeItem.ENTRY_I_NO_PASS;
        } else {
            bizCode = EntryHeadThirdExt.APPROVAL_STATUS_PASS.equals(param.getApprovalStatus()) ? ConstantsNoticeItem.ENTRY_E_PASS : ConstantsNoticeItem.ENTRY_E_NO_PASS;
        }
        try {
            applicationEventPublisher.publishEvent(new NoticeTaskEvent(new NoticeTaskEventParam(bizCode, thirdHead.getTradeCode(), thirdHead.getEmsListNo())));
        } catch (Exception ex) {
            logger.error("发送通知出错", ex);
        }
    }

    public void approvalApprovedByreview(String ieMark, DecEntryApprovalParam param, Date checkTheTime,String checklist, UserInfoToken token) {
        String emsListNo = getEmsListNoById(ieMark, param.getSid(), token);

        EntryHeadThird thirdHead = new EntryHeadThird();

        EntryHeadThird headThirda = entryHeadThirdMapper.selectByPrimaryKey(param.getSid());
        if (headThirda==null) {
            EntryHeadThird headThird = new EntryHeadThird();
                    headThird.setEmsListNo(emsListNo);
            headThird.setSid(param.getSid());
            headThird.setDataSource("3");
            headThird.setIEMark(ieMark);
            headThird.setInsertUser(token.getUserNo());
            headThird.setInsertUser(token.getUserName());
            headThird.setInsertTime(new Date());
            headThird.setTradeCode(token.getCompany());
            entryHeadThirdMapper.insert(headThird);

            EntryHeadThirdExt headThirdExt = entryHeadThirdExtMapper.selectByPrimaryKey(headThird.getSid());
            final boolean existExt = headThirdExt == null;
            if (headThirdExt == null) {
                headThirdExt = new EntryHeadThirdExt();
                headThirdExt.preInsert(token);
            }
            if (StringUtils.isNotBlank(thirdHead.getSid())){
                headThirdExt.setSid(thirdHead.getSid());
            }else {
                headThirdExt.setSid(param.getSid());
            }
            if (StringUtils.equals(checklist, ConstantsStatus.STATUS_2)) {
                headThirdExt.setApprovalStatus(param.getApprovalStatus());
                headThirdExt.setApprovalNote(param.getApprovalNote());
            } else if (StringUtils.equals(checklist, ConstantsStatus.STATUS_1)) {
                headThirdExt.setApprovalStatus(ConstantsStatus.STATUS_0);
                headThirdExt.setApprovalNote("待审核");
            }
            if (existExt) {
                entryHeadThirdExtMapper.insert(headThirdExt);
            } else {
                headThirdExt.preUpdate(token);
                entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
            }

            if (StringUtils.equals(checklist, ConstantsStatus.STATUS_1) || StringUtils.equals(checklist, ConstantsStatus.STATUS_2)) {
                if (agentAuthMapper.getByAgent(thirdHead.getAgentCode()) != null) {
                    EntryThirdApprovalParam approvalParam = new EntryThirdApprovalParam();
                    approvalParam.setApprove(EntryHeadThirdExt.APPROVAL_STATUS_PASS.equals(param.getApprovalStatus()) ? "1" : "0");
                    approvalParam.setTradeCode(token.getCompany());
                    approvalParam.setEmsListNo(thirdHead.getEmsListNo());
                    approvalParam.setNote(param.getApprovalNote());
                    ResultObject resultObject = entryThirdSendApproval.approval(approvalParam, thirdHead.getAgentCode(), token);

                    headThirdExt.setSendStatus(resultObject.isSuccess() ? "1" : "-1");
                    headThirdExt.preUpdate(token);
                    entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
                }
            } else {
                headThirdExt.setApprovalStatus(param.getApprovalStatus());
                headThirdExt.preUpdate(token);
                entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
            }
            if (StringUtils.equals(checklist, ConstantsStatus.STATUS_2)) {
                auditInfo(param.getSid(), headThirdExt.getApprovalStatus(), param.getApprovalNote(), checkTheTime, token);
            } else {
                auditInfo(param.getSid(), ConstantsStatus.STATUS_0, "待审核", checkTheTime, token);
            }
        }else {

            EntryHeadThirdExt headThirdExt = entryHeadThirdExtMapper.selectByPrimaryKey(headThirda.getSid());
            final boolean existExt = headThirdExt == null;
            if (headThirdExt == null) {
                headThirdExt = new EntryHeadThirdExt();
                headThirdExt.preInsert(token);
            }
            if (StringUtils.isNotBlank(thirdHead.getSid())){
                headThirdExt.setSid(thirdHead.getSid());
            }else {
                headThirdExt.setSid(UUID.randomUUID().toString());
            }
            if (StringUtils.equals(checklist, ConstantsStatus.STATUS_2)) {
                headThirdExt.setApprovalStatus(param.getApprovalStatus());
                headThirdExt.setApprovalNote(param.getApprovalNote());
            } else if (StringUtils.equals(checklist, ConstantsStatus.STATUS_1)) {
                headThirdExt.setApprovalStatus(ConstantsStatus.STATUS_0);
                headThirdExt.setApprovalNote("待审核");
            }
            if (existExt) {
                entryHeadThirdExtMapper.insert(headThirdExt);
            } else {
                headThirdExt.preUpdate(token);
                entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
            }
            if (StringUtils.equals(checklist, ConstantsStatus.STATUS_2)) {
                auditInfo(param.getSid(), headThirdExt.getApprovalStatus(), param.getApprovalNote(), checkTheTime, token);
            }else {
                auditInfo(param.getSid(), ConstantsStatus.STATUS_0, "待审核", checkTheTime, token);
            }
        }
    }

    public void onlyUploads(String ieMark, DecEntryApprovalParam param, Date checkTheTime,boolean approvedByreview, UserInfoToken token) {
        String emsListNo = getEmsListNoById(ieMark, param.getSid(), token);

        EntryHeadThird thirdHead = new EntryHeadThird();

        EntryHeadThird headThirda = entryHeadThirdMapper.selectByPrimaryKey(param.getSid());
        if (headThirda==null) {
            EntryHeadThird headThird = new EntryHeadThird();
                    headThird.setEmsListNo(emsListNo);
            headThird.setSid(param.getSid());
            headThird.setDataSource("3");
            headThird.setIEMark(ieMark);
            headThird.setInsertUser(token.getUserNo());
            headThird.setInsertUser(token.getUserName());
            headThird.setInsertTime(new Date());
            headThird.setTradeCode(token.getCompany());
            entryHeadThirdMapper.insert(headThird);

            EntryHeadThirdExt headThirdExt = entryHeadThirdExtMapper.selectByPrimaryKey(headThird.getSid());
            final boolean existExt = headThirdExt == null;
            if (headThirdExt == null) {
                headThirdExt = new EntryHeadThirdExt();
                headThirdExt.preInsert(token);
            }
            if (StringUtils.isNotBlank(thirdHead.getSid())){
                headThirdExt.setSid(thirdHead.getSid());
            }else {
                headThirdExt.setSid(param.getSid());
            }
//            headThirdExt.setApprovalStatus(param.getApprovalStatus());
            headThirdExt.setApprovalNote(null);
            if (existExt) {
                entryHeadThirdExtMapper.insert(headThirdExt);
            } else {
                headThirdExt.preUpdate(token);
                entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
            }


        if (!approvedByreview) {
            if (agentAuthMapper.getByAgent(thirdHead.getAgentCode()) != null) {
                EntryThirdApprovalParam approvalParam = new EntryThirdApprovalParam();
                approvalParam.setApprove(EntryHeadThirdExt.APPROVAL_STATUS_PASS.equals(param.getApprovalStatus()) ? "1" : "0");
                approvalParam.setTradeCode(token.getCompany());
                approvalParam.setEmsListNo(thirdHead.getEmsListNo());
                approvalParam.setNote(param.getApprovalNote());
                ResultObject resultObject = entryThirdSendApproval.approval(approvalParam, thirdHead.getAgentCode(), token);

                headThirdExt.setSendStatus(resultObject.isSuccess() ? "1" : "-1");
                headThirdExt.preUpdate(token);
                entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
            }
        }else {
//            headThirdExt.setApprovalStatus(param.getApprovalStatus());
            headThirdExt.preUpdate(token);
            entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
        }

        auditInfo(param.getSid(),headThirdExt.getApprovalStatus(), null, checkTheTime,token);

        }else {

            EntryHeadThirdExt headThirdExt = entryHeadThirdExtMapper.selectByPrimaryKey(headThirda.getSid());
            final boolean existExt = headThirdExt == null;
            if (headThirdExt == null) {
                headThirdExt = new EntryHeadThirdExt();
                headThirdExt.preInsert(token);
            }
            if (StringUtils.isNotBlank(thirdHead.getSid())){
                headThirdExt.setSid(thirdHead.getSid());
            }else {
                headThirdExt.setSid(UUID.randomUUID().toString());
            }
//            headThirdExt.setApprovalStatus(param.getApprovalStatus());
            headThirdExt.setApprovalNote(null);
            if (existExt) {
                entryHeadThirdExtMapper.insert(headThirdExt);
            } else {
                headThirdExt.preUpdate(token);
                entryHeadThirdExtMapper.updateByPrimaryKey(headThirdExt);
            }

            auditInfo(param.getSid(),headThirdExt.getApprovalStatus(), param.getApprovalNote(), checkTheTime,token);
        }
    }

    public void auditInfo(String entrySid , String approvalStatus, String approvalNote, Date time, UserInfoToken token) {
        Date date = new Date();
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        String sid = UUID.randomUUID().toString();
        aeoAuditInfo.setSid(sid);
        aeoAuditInfo.setBusinessSid(entrySid);
        aeoAuditInfo.setApprUser(token.getUserNo());
        if (time!=null) {
            aeoAuditInfo.setApprDate(time);
        }else {
            aeoAuditInfo.setApprDate(date);
        }
        if (StringUtils.equals(approvalStatus, ConstantsStatus.STATUS_1)) {
            aeoAuditInfo.setStatus("8");
        } else if (StringUtils.equals(approvalStatus, ConstantsStatus.STATUS_0)) {
            aeoAuditInfo.setStatus("-2");
        }else {
            aeoAuditInfo.setStatus("-1");
        }
        aeoAuditInfo.setInsertUser(token.getUserNo());
        aeoAuditInfo.setInsertUserName(token.getUserName());
        aeoAuditInfo.setInsertTime(date);
        aeoAuditInfo.setTradeCode(token.getCompany());
        aeoAuditInfo.setApprNote(approvalNote);

        aeoAuditInfoMapper.insert(aeoAuditInfo);
    }

    private String getEmsListNoById(String ieMark, String sid, UserInfoToken token) {
        if (CommonVariable.IE_MARK_I.equals(ieMark)) {
            DecIEntryHead head = decIEntryHeadMapper.selectByPrimaryKey(sid);
            if (head == null) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("未找到报关单草单数据"));
            }
            return head.getEmsListNo();
        } else {
            DecEEntryHead head = decEEntryHeadMapper.selectByPrimaryKey(sid);
            if (head == null) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("未找到报关单草单数据"));
            }
            return head.getEmsListNo();
        }
    }


    public ResultObject pushMessage(EntryThirdMessageParam param, HttpHeaders headers) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        logger.info("将报关行数据写入记录表中，企业编码：" + param.getTradeCode() + ",企业内部编号：" + param.getSeqNo() + ",统一编号：" + param.getSeqNo());
        recordLog("v1/public/agent/entry/message",
                null,
                RequestLog.BIZ_AGENT_MSG,
                JsonObjectMapper.getInstance().toJson(param),
                JsonObjectMapper.getInstance().toJson(agentAuthManager.getAuthInfo(headers)));
        logger.info("授权判断");
        agentAuthManager.getAndValidation(headers, param.getTradeCode());
        String errMsg = validatorUtil.validation(param);
        if (!Strings.isNullOrEmpty(errMsg)) {
            resultObject.setSuccess(false);
            resultObject.setCode(400);
            resultObject.setMessage(errMsg);
            return resultObject;
        }
        logger.info("获取之前发送第三方的报关单草单信息");
        EntryHeadThird headThird = entryHeadThirdMapper.selectByEmsListNo(param.getEmsListNo(), param.getTradeCode());
        if (headThird == null) {
            resultObject.setSuccess(false);
            resultObject.setCode(400);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("企业内部编号或企业编码错误"));
            return resultObject;
        }
        logger.info("更新之前发送第三方的报关单草单统一编号及报关单号");
        headThird.setSeqNo(param.getSeqNo());
        headThird.setEntryId(param.getEntryId());
        entryHeadThirdMapper.updateByPrimaryKey(headThird);
        try {
            updateEntry(headThird, param);
        } catch (Exception ex) {
            logger.error("更新关务报关单号出错！", ex);
        }
        return resultObject;
    }

    public void updateEntry(EntryHeadThird headThird, EntryThirdMessageParam param) {
        if (CommonVariable.IE_MARK_I.equals(headThird.getIEMark())) {
            DecIEntryHead headParam = new DecIEntryHead();
            headParam.setTradeCode(param.getTradeCode());
            headParam.setEmsListNo(param.getEmsListNo());
            logger.info("获取进口报关单单信息");
            List<DecIEntryHead> headList = decIEntryHeadMapper.getList(headParam);
            if (headList.size() > 0) {
                DecIEntryHead decIEntryHead = headList.get(0);
                decIEntryHead.setSeqNo(param.getSeqNo());
                decIEntryHead.setEntryNo(param.getEntryId());
                logger.info("更新进口报关单的统一编号及报关单号");
                decIEntryHeadMapper.updateLjqReply(decIEntryHead);
            }
        } else {
            DecEEntryHead headParam = new DecEEntryHead();
            headParam.setTradeCode(param.getTradeCode());
            headParam.setEmsListNo(param.getEmsListNo());
            logger.info("获取出口报关单单信息");
            List<DecEEntryHead> headList = decEEntryHeadMapper.getList(headParam);
            if (headList.size() > 0) {
                DecEEntryHead decEEntryHead = headList.get(0);
                decEEntryHead.setSeqNo(param.getSeqNo());
                decEEntryHead.setEntryNo(param.getEntryId());
                logger.info("更新出口报关单的统一编号及报关单号");
                decEEntryHeadMapper.updateLjqReply(decEEntryHead);
            }
        }
    }

    private void recordLog(String url, String tradeCode, String bizType, String requestBody, String headers) {
        RequestLog dto = new RequestLog();
        dto.setProtocol("http");
        dto.setMethod("post");
        dto.setTradeCode(tradeCode);
        dto.setUrl(url);
        dto.setBizType(bizType);
        dto.setStatus(200);
        dto.setTime(0);
        dto.setRequestBody(requestBody);
        dto.setNote(headers);
        logger.info(JsonObjectMapper.getInstance().toJson(dto));
        applicationEventPublisher.publishEvent(new RequestLogEvent(RequestLog.BIZ_AGENT, dto));
    }

    /**
     * 导入报关单草单
     *
     * @param file
     * @param emsListNo
     * @param iemark
     * @param userInfo
     * @return
     */
    public ResultObject importEntryThird(MultipartFile file, String emsListNo, String iemark, UserInfoToken userInfo) {
        EntryHeadThird thirdHead;
        List<EntryListThird> thirdList;

        try {
            // 读取整个excel
            XSSFWorkbook sheets = new XSSFWorkbook(file.getInputStream());
            // 读取第一个sheet
            XSSFSheet firstSheet = sheets.getSheetAt(0);
            thirdHead = buildHead(firstSheet);
            thirdList = buildBody(firstSheet);

        } catch (IOException e) {
            logger.error("读取excel数据发生异常", e);
            return ResultObject.createInstance(false, xdoi18n.XdoI18nUtil.t("导入失败"), e);
        }
        // 校验
        String oldSid = checkData(emsListNo, iemark, thirdHead, thirdList, userInfo);
        if (StringUtils.isNotBlank(oldSid)) {
            entryHeadThirdMapper.deleteByPrimaryKey(oldSid);
            entryHeadThirdExtMapper.deleteByPrimaryKey(oldSid);
            entryListThirdMapper.deleteByHeadId(oldSid);
            entryContainerThirdMapper.deleteByHeadId(oldSid);
        }

        String sid = UUID.randomUUID().toString();
        // 表头
        thirdHead.setOwnerCode(userInfo.getCompany());
        thirdHead.preInsert(userInfo);
        thirdHead.setSid(sid);
        thirdHead.setEmsListNo(emsListNo);
        thirdHead.setDataSource(ConstantsStatus.STATUS_1);
        thirdHead.setPromiseItems(promiseItems(thirdHead));
        // 表体
        thirdList.forEach(x -> {
            x.preInsert(userInfo);
            x.setHeadId(sid);
        });
        // 审核
        EntryHeadThirdExt headThirdExt = new EntryHeadThirdExt();
        headThirdExt.preInsert(userInfo);
        headThirdExt.setSid(sid);
        headThirdExt.setApprovalStatus(ConstantsStatus.STATUS_0);

        entryHeadThirdMapper.insert(thirdHead);
        entryHeadThirdExtMapper.insert(headThirdExt);
        bulkSqlOpt.batchInsert(thirdList, EntryListThirdMapper.class);
        // to do 更新上传情况
        if (ConstantsStatus.TYPE_I.equals(iemark)) {
            decIEntryHeadMapper.updateByEmsListNo(emsListNo, userInfo.getCompany());
        }
        if (ConstantsStatus.TYPE_E.equals(iemark)) {
            decEEntryHeadMapper.updateByEmsListNo(emsListNo, userInfo.getCompany());
        }

        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("导入成功"));
    }

    private String promiseItems(EntryHeadThird thirdHead) {
        String promise = null;
        if (StringUtils.isNotBlank(thirdHead.getConfirmSpecial())) {
            promise = thirdHead.getConfirmSpecial();
        } else {
            promise = "9";
        }
        if (StringUtils.isNotBlank(thirdHead.getConfirmPrice())) {
            promise = promise + thirdHead.getConfirmPrice();
        } else {
            promise = promise +"9";
        }
        if (StringUtils.isNotBlank(thirdHead.getConfirmRoyalties())) {
            promise = promise + thirdHead.getConfirmRoyalties();
        } else {
            promise = promise +"9";
        }
        if (StringUtils.isNotBlank(thirdHead.getConfirmFormulaPrice())) {
            promise = promise +thirdHead.getConfirmFormulaPrice();
        } else {
            promise = promise +"9";
        }
        if (StringUtils.isNotBlank(thirdHead.getConfirmTempPrice())) {
            promise = promise + thirdHead.getConfirmTempPrice();
        } else {
            promise = promise +"9";
        }
        return promise;
    }

    private String checkData(String emsListNo, String iemark, EntryHeadThird head, List<EntryListThird> list, UserInfoToken userInfo) {
        if (StringUtils.isBlank(emsListNo)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("参数企业内部编号为空"));
        }
        if (StringUtils.isBlank(iemark)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("参数进出口标志为空"));
        }
        if (null == head) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("表头未识别到数据"));
        }
        if (StringUtils.isNotBlank(head.getIEMark()) && !head.getIEMark().equals(iemark)) {
            throw new ErrorException(400, ConstantsStatus.TYPE_I.equals(iemark) ? xdoi18n.XdoI18nUtil.t("进口模块不可导入出口数据") : xdoi18n.XdoI18nUtil.t("出口模块不可导入进口数据"));
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("表体未识别到数据"));
        }
        EntryHeadThird oldHead = entryHeadThirdMapper.selectByEmsListNo(emsListNo, userInfo.getCompany());
        if (null != oldHead) {
            EntryHeadThirdExt headThirdExt = entryHeadThirdExtMapper.selectByPrimaryKey(oldHead.getSid());
            if (null != headThirdExt && headThirdExt.isApprovalSuccess()) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("当前数据已经审批通过，无法导入新数据"));
            }
            return oldHead.getSid();
        }

        return null;
    }

    private List<EntryListThird> buildBody(XSSFSheet firstSheet) {
        List<EntryListThird> list = new ArrayList<>();
        int index = 22;
        while (null != firstSheet.getRow(index) && firstSheet.getRow(index).cellIterator().hasNext()) {
            EntryListThird body = new EntryListThird();
            // 项号/商品序号
            body.setGNo(readCellValueLong(index, 0, firstSheet));
            // 备案序号
            body.setContrItem(readCellValueLong(index, 1, firstSheet));
            // 商品编号
            body.setCodeTs(readCellValueStr(index, 2, firstSheet));
            // 检验检疫编码
            body.setCiqCode(readCellValueStr(index, 3, firstSheet));
            // 商品名称
            body.setGName(readCellValueStr(index, 4, firstSheet));
            // 规格型号
            body.setGModel(readCellValueStr(index, 5, firstSheet));
            // 成交数量
            body.setGQty(readCellValueDecimal(index, 6, firstSheet));
            // 成交计量单位
            body.setGUnit(readCellValueStr(index, 7, firstSheet));
            // 单价
            body.setDeclPrice(readCellValueDecimal(index, 8, firstSheet));
            // 总价
            body.setDeclTotal(readCellValueDecimal(index, 9, firstSheet));
            // 币制
            body.setCurr(readCellValueStr(index, 10, firstSheet));
            // 法定第一数量
            body.setQty1(readCellValueDecimal(index, 11, firstSheet));
            // 法定第一计量单位
            body.setUnit1(readCellValueStr(index, 12, firstSheet));
            // 加工成品单耗版本号
//            body.setGNo(readCellValueLong(index, 13, firstSheet));
            // 货号
            body.setExgNo(readCellValueStr(index, 14, firstSheet));
            // 最终目的国（地区）
            body.setDestinationCountry(readCellValueStr(index, 15, firstSheet));
            // 法定第二数量
            body.setQty2(readCellValueDecimal(index, 16, firstSheet));
            // 法定第二计量单位
            body.setUnit2(readCellValueStr(index, 17, firstSheet));
            // 优惠贸易协定项下原产地
//            body.setOrigPlaceCode(readCellValueLong(index, 18, firstSheet));
            // 原产国（地区）
            body.setOriginCountry(readCellValueStr(index, 19, firstSheet));
            // 原产地区代码
            body.setOrigPlaceCode(readCellValueStr(index, 20, firstSheet));
            // 境内目的地/境内货源地
            body.setDistrictCode(readCellValueStr(index, 21, firstSheet));
            // 目的地代码/产地代码
            body.setDistrictPostCode(readCellValueStr(index, 22, firstSheet));
            // 征免方式
            body.setDutyMode(readCellValueStr(index, 23, firstSheet));
            // 货物属性代码
            body.setGoodsAttr(readCellValueStr(index, 24, firstSheet));
            // 英文名称
//            body.setGNo(readCellValueLong(index, 25, firstSheet));
            // 用途代码
            body.setUseTo(readCellValueStr(index, 26, firstSheet));
            // 非危险货物
            body.setDangerFlag(readCellValueStr(index, 27, firstSheet));
            // UN编号
            body.setUnCode(readCellValueStr(index, 28, firstSheet));
            // 危险类别(危包类别)
            body.setPackType(readCellValueStr(index, 29, firstSheet));
            // 包装类别
//            body.setGNo(readCellValueLong(index, 30, firstSheet));
            // 包装UN标记
//            body.setGNo(readCellValueLong(index, 31, firstSheet));
            // 成分/原料/组分
            body.setStuff(readCellValueStr(index, 32, firstSheet));
            // 产品有效期
            body.setProdValiddt(readCellValueDate(index, 33, firstSheet));
            // 产品保质期(天)
//            body.setGNo(readCellValueLong(index, 34, firstSheet));
            // 境外生产企业
//            body.setGNo(readCellValueLong(index, 35, firstSheet));
            // 货物规格
            body.setGoodsSpec(readCellValueStr(index, 36, firstSheet));
            // 货物型号
            body.setGoodsModel(readCellValueStr(index, 37, firstSheet));
            // 货物品牌
            body.setGoodsBrand(readCellValueStr(index, 38, firstSheet));
            // 生产日期
            body.setProduceDate(readCellValueDate(index, 39, firstSheet));
            // 生产批次
//            body.setGNo(readCellValueLong(index, 40, firstSheet));
            // 生产单位代码
            body.setMnufctrreGno(readCellValueStr(index, 41, firstSheet));
            list.add(body);
            index++;
        }
        return list;
    }

    private EntryHeadThird buildHead(XSSFSheet firstSheet) {
        EntryHeadThird head = new EntryHeadThird();

        // 进出口标识
        head.setIEMark(readCellValueStr(1, 1, firstSheet));
        // 申报地海关
        head.setMasterCustoms(readCellValueStr(1, 3, firstSheet));
        // 进/出境关别
        head.setIEPort(readCellValueStr(1, 5, firstSheet));
        // 备案号
        head.setManualNo(readCellValueStr(1, 7, firstSheet));
        // 合同协议号
        head.setContrNo(readCellValueStr(1, 9, firstSheet));

        // 进/出口日期
        head.setIedate(readCellValueDate(2, 1, firstSheet));
        // 境内收发货人代码(境内收发货人社会信用代码)
        head.setTradeCodeScc(readCellValueStr(2, 3, firstSheet));
        // 境内收发货人名称
        head.setTradeName(readCellValueStr(2, 5, firstSheet));
        // 境内收发货人名称（外文）
//        head.setAgentCodeScc(readCellValueStr(2, 7, firstSheet));
        // 境内收发货人检验检疫编码
        head.setTradeCiqCode(readCellValueStr(2, 9, firstSheet));

        // 境外收发货人代码
        head.setOverseasTradeCode(readCellValueStr(3, 1, firstSheet));
        // 境外收发货人名称（外文）
        head.setOverseasTradeName(readCellValueStr(3, 3, firstSheet));
        // 境外发货人地址
//        head.setAgentCodeScc(readCellValueStr(3, 5, firstSheet));
        // 境外收发货人名称（中文）
//        head.setAgentCodeScc(readCellValueStr(3, 7, firstSheet));

        // 消费使用单位代码
        head.setOwnerScc(readCellValueStr(4, 1, firstSheet));
        // 消费使用单位名称
        head.setOwnerName(readCellValueStr(4, 3, firstSheet));
        // 消费使用单位检验检疫代码
        head.setOwnerciqCode(readCellValueStr(4, 5, firstSheet));
        // 申报单位代码
        head.setAgentCodeScc(readCellValueStr(4, 7, firstSheet));
        // 申报单位名称
        head.setAgentName(readCellValueStr(4, 9, firstSheet));
        // 申报单位检验检疫代码
        head.setAgentRegCode(readCellValueStr(4, 11, firstSheet));

        // 运输方式
        head.setTrafMode(readCellValueStr(5, 1, firstSheet));
        // 运输工具名称
        head.setTrafName(readCellValueStr(5, 3, firstSheet));
        // 航次号
        head.setVoyageNo(readCellValueStr(5, 5, firstSheet));
        // 申报日期
        head.setDDate(readCellValueDate(5, 7, firstSheet));

        // 提运单号
        head.setBillNo(readCellValueStr(6, 1, firstSheet));
        // 监管方式
        head.setTradeMode(readCellValueStr(6, 3, firstSheet));
        // 征免性质
        head.setCutMode(readCellValueStr(6, 5, firstSheet));

        // 许可证号
        head.setLicenseNo(readCellValueStr(7, 1, firstSheet));
        // 启运国/运抵国（地区）
        head.setTradeCountry(readCellValueStr(7, 3, firstSheet));
        // 经停港/指运港
        head.setDistinatePort(readCellValueStr(7, 5, firstSheet));
        // 成交方式
        head.setTransMode(readCellValueStr(7, 7, firstSheet));

        // 运费标识
        head.setFeeMark(readCellValueStr(8, 1, firstSheet));
        // 运费数值
        head.setFeeRate(readCellValueDecimal(8, 3, firstSheet));
        // 运费币制代码
        head.setFeeCurr(readCellValueStr(8, 5, firstSheet));

        // 保费标识
        head.setInsurMark(readCellValueStr(9, 1, firstSheet));
        // 保费数值
        head.setInsurRate(readCellValueDecimal(9, 3, firstSheet));
        // 保费币制代码
        head.setInsurCurr(readCellValueStr(9, 5, firstSheet));

        // 杂费标识
        head.setOtherMark(readCellValueStr(10, 1, firstSheet));
        // 杂费数值
        head.setOtherRate(readCellValueDecimal(10, 3, firstSheet));
        // 杂费币制代码
        head.setOtherCurr(readCellValueStr(10, 5, firstSheet));

        // 件数
        head.setPackNo(readCellValueLong(11, 1, firstSheet));
        // 包装种类
        head.setWrapType(readCellValueStr(11, 3, firstSheet));
        // 毛重（KG）
        head.setGrossWt(readCellValueDecimal(11, 5, firstSheet));
        // 净重（KG）
        head.setNetWt(readCellValueDecimal(11, 7, firstSheet));

        // 贸易国别（地区）
        head.setTradeAreaCode(readCellValueStr(12, 1, firstSheet));
        // 货物存放地点
        head.setGoodsplace(readCellValueStr(12, 3, firstSheet));
        // 启运港
        head.setDespPortCode(readCellValueStr(12, 5, firstSheet));
        // 启运日期
//        head.setAgentCodeScc(readCellValueStr(12, 7, firstSheet));

        // 报关单类型
        head.setEntryType(readCellValueStr(13, 1, firstSheet));
        // 标记唛码
        head.setMarkNo(readCellValueStr(13, 3, firstSheet));
        // 备注
        head.setNote(readCellValueStr(13, 5, firstSheet));
        // 清单类型
        head.setBillType(readCellValueStr(13, 7, firstSheet));

        // 检验检疫受理机关
        head.setOrgCode(readCellValueStr(14, 1, firstSheet));
        // 领证机关
        head.setVsaorgCode(readCellValueStr(14, 3, firstSheet));
        // 口岸检验检疫机关
        head.setInsporgCode(readCellValueStr(14, 5, firstSheet));
        // 入境/离境口岸
        head.setEntryPortCode(readCellValueStr(14, 7, firstSheet));
        // B/L号
        head.setCiqBillNo(readCellValueStr(14, 9, firstSheet));

        // 目的地检验检疫机关
        head.setPurporgCode(readCellValueStr(15, 1, firstSheet));
        // 关联号码
        head.setCorrelationDeclno(readCellValueStr(15, 3, firstSheet));
        // 关联理由
        head.setCorrelationReasonFlag(readCellValueStr(15, 5, firstSheet));
        // 原箱运输
//        head.setAgentCodeScc(readCellValueStr(15, 7, firstSheet));
        // 卸毕日期
//        head.setAgentCodeScc(readCellValueStr(15, 9, firstSheet));

        // 特殊业务标识-国际赛事
//        head.setAgentCodeScc(readCellValueStr(16, 1, firstSheet));
        // 特殊业务标识-特殊进出军工物资
//        head.setAgentCodeScc(readCellValueStr(16, 3, firstSheet));
        // 特殊业务标识-国际援助物资
//        head.setAgentCodeScc(readCellValueStr(16, 5, firstSheet));
        // 特殊业务标识-国际会议
//        head.setAgentCodeScc(readCellValueStr(16, 7, firstSheet));
        // 特殊通关模式-直通放行
//        head.setAgentCodeScc(readCellValueStr(16, 9, firstSheet));

        // 特殊通关模式-外交礼遇
//        head.setAgentCodeScc(readCellValueStr(17, 1, firstSheet));
        // 特殊通关模式-转关
//        head.setAgentCodeScc(readCellValueStr(17, 3, firstSheet));
        // 特殊关系确认
        head.setConfirmSpecial(readCellValueStr(17, 5, firstSheet));
        // 价格影响确认
        head.setConfirmPrice(readCellValueStr(17, 7, firstSheet));
        // 与货物有关的特许权使用费支付确认
        head.setConfirmRoyalties(readCellValueStr(17, 9, firstSheet));
        // 公式定价确认
        head.setConfirmFormulaPrice(readCellValueStr(17, 11, firstSheet));
        // 暂定价格确认
        head.setConfirmTempPrice(readCellValueStr(17, 13, firstSheet));

        // 关联报关单
        head.setAgentCodeScc(readCellValueStr(18, 1, firstSheet));
        // 关联备案
        head.setRelManualNo(readCellValueStr(18, 3, firstSheet));
        // 保税/监管场地
        head.setBondedNo(readCellValueStr(18, 5, firstSheet));
        // 场地代码(码头/货场代码（为物流监控备用）)
        head.setCustomsField(readCellValueStr(18, 7, firstSheet));


        return head;
    }

    private String readCellValueStr(int row, int col, XSSFSheet sheet) {
        String str = getCellValue(row, col, sheet);
        return StringUtils.isNotBlank(str) ? str : null;
    }

    private BigDecimal readCellValueDecimal(int row, int col, XSSFSheet sheet) {
        String str = getCellValue(row, col, sheet);
        return StringUtils.isNotBlank(str) ? new BigDecimal(str) : null;
    }

    private Long readCellValueLong(int row, int col, XSSFSheet sheet) {
        String str = getCellValue(row, col, sheet);
        return StringUtils.isNotBlank(str) ? Long.valueOf(str) : null;
    }

    private Date readCellValueDate(int row, int col, XSSFSheet sheet) {
        String str = getCellValue(row, col, sheet);
        return DateUtil.tryDateParse(str);
    }

    private String getCellValue(int row, int col, XSSFSheet sheet) {
        XSSFCell cell = sheet.getRow(row).getCell(col);
        if (null == cell) {
            return null;
        }
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue();
    }

    @SneakyThrows
    public ResultObject<String> uploadEntryFile(MultipartFile[] file, String emsListNo, String ieMark, UserInfoToken userInfo) {
        if (StringUtils.isBlank(emsListNo)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("单据内部编号不能为空"));
        }
        if (emsListNo.getBytes(CommonVariable.GBK).length > 32) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("单据内部编号长度不能超过32位字节长度!"));
        }
        if (file.length>1){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只允许上传一个文件!"));
        }
        EntryHeadThird third = new EntryHeadThird();
        third.setEmsListNo(emsListNo);
        third.setDataSource(ConstantsStatus.STATUS_2);
        if (entryHeadThirdMapper.selectCount(third) > 0) {
            throw new ErrorException(400, String.format("[%s]%s",emsListNo,xdoi18n.XdoI18nUtil.t("内部编号已识别")));
        }
        // 获取url
        WarringPassConfigDto warringPassConfigDto = warringPassConfigService.selectAll(userInfo).get(0);
        String ocrEntryNo = warringPassConfigDto.getOcrEntryNo();
        if (StringUtils.isBlank(ocrEntryNo)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未配置OCR识别报关单模板分组号"));
        }
        String taskId = UUID.randomUUID().toString();
        Map<String, String> headers = new HashMap<String, String>(2){{
            put("Authorization", userInfo.getAccessToken());
            put("corpCode", userInfo.getCompany());
        }};
        GwstdHttpConfig httpConfig = httpConfigMapper.selectByType("OCR_UPLOAD");
        if (httpConfig == null) {
            throw new ErrorException(400, String.format("[%s]%s","httpConfig: OCR_UPLOAD",xdoi18n.XdoI18nUtil.t("未配置")));
        }
        String uploadUrl = httpConfig.getBaseUrl() + httpConfig.getServiceUrl() + "?sid=" + taskId
                + "&task_code=CUSTOMS FORM&template_type=" + ocrEntryNo + "&task_id=" + taskId;

        String theTempPath = tempPath + "/" + taskId + "/";
        //  判断文件夹是否存在
        File dir = new File(theTempPath);
        if (!dir.exists()) {
            dir.mkdir();
        }

        // 文件压缩处理
        File zipFile;
        for (MultipartFile mFile : file) {
            if (!mFile.getOriginalFilename().toLowerCase().endsWith("pdf")) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只支持pdf文件上传"));
            }
            File tempFile = new File(theTempPath + FileUtil.getValidFileName(mFile.getOriginalFilename()));
            mFile.transferTo(tempFile);
        }
        // 压缩文件
        FileUtil.fileToZip(theTempPath, theTempPath, FileUtil.getValidFileName(file[0].getOriginalFilename()));
        zipFile = new File(theTempPath + FileUtil.getValidFileName(file[0].getOriginalFilename()) + ".zip");

        // ocr上传文件
//        String uploadUrl = url + "?sid=" + taskId
//                + "&task_code=CUSTOMS FORM&template_type=4&task_id=" + taskId;
        String response = OkHttpUtils.httpPostFile(uploadUrl, zipFile, headers);
        FileUtil.deleteFileTree(theTempPath);
        ResultObject resultObject = JsonObjectMapper.getInstance().fromJson(response, ResultObject.class);

        GwOcrLog log = new GwOcrLog().initData(userInfo);
        log.setTaskId(taskId);
        // 配合演示  临时修改
        log.setBussinessType("CUSTOMS FORM");
        log.setEmsListNo(emsListNo);
        log.setExtendFiled1(ieMark);
        log.setStatus(resultObject.isSuccess() ? "0" : "1");
        log.setErrorMessage(resultObject.isSuccess() ? null : resultObject.getMessage());
        log.setUploadRes(response);
        gwOcrLogMapper.insert(log);

        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("上传成功"), taskId);
    }

    public ResultObject getListPaged(EntryHeadThirdParam param, PageParam pageParam, UserInfoToken userInfo) {
        param.setTradeCode(userInfo.getCompany());
        Page<EntryHeadThird> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> entryHeadThirdMapper.getListPage(param));
        return ResultObject.createInstance(page.getResult(), (int) page.getTotal(), page.getPageNum());
    }

    public ResultObject delete(List<String> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("参数为空"));
        }
        // 判断状态是否可以删除

        // 删除
        entryHeadThirdMapper.deleteAllBySids(sids);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("删除成功"));
    }

    public ResponseEntity selectAll(BasicExportParam<EntryHeadThirdParam> param, UserInfoToken userInfo) throws Exception {
        EntryHeadThirdParam thirdParam = param.getExportColumns();
        thirdParam.setTradeCode(userInfo.getCompany());
        List<EntryHeadThird> list = entryHeadThirdMapper.getListPage(thirdParam);
        list.forEach(head -> {
            if (ConstantsStatus.STATUS_1.equals(head.getOcrStatus())) {
                head.setOcrStatus("1 已识别");
            }
            if (ConstantsStatus.STATUS_2.equals(head.getOcrStatus())) {
                head.setOcrStatus("2 已使用");
            }
        });

        List<KeyValuePair<String, String>> header = new ArrayList<>();
        // 运保杂导出设置
        for (KeyValuePair<String, String> kv : param.getHeader()) {
            if (StringUtils.isNotBlank(kv.getKey())) {
                header.add(kv);
            } else {
                if ("运费".equals(kv.getValue())) {
                    header.add(new KeyValuePair<>("feeMark","运费类型"));
                    header.add(new KeyValuePair<>("feeRate","运费费率"));
                    header.add(new KeyValuePair<>("feeCurr","运费币制"));
                }
                if ("保费".equals(kv.getValue())) {
                    header.add(new KeyValuePair<>("insurMark","保费类型"));
                    header.add(new KeyValuePair<>("insurRate","保费费率"));
                    header.add(new KeyValuePair<>("insurCurr","保费币制"));
                }
                if ("杂费".equals(kv.getValue())) {
                    header.add(new KeyValuePair<>("otherMark","杂费类型"));
                    header.add(new KeyValuePair<>("otherRate","杂费费率"));
                    header.add(new KeyValuePair<>("otherCurr","杂费币制"));
                }
            }
        }

        return excelService.getExcelHeaders(URLEncoder.encode(param.getName(), CommonVariable.UTF8), header, list);
    }

}
