package com.dcjet.cs.entry.service;
import com.dcjet.cs.util.variable.DecVariable;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.entry.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.entry.dao.GwstdEntryHeadThirdMapper;
import com.dcjet.cs.entry.mapper.GwstdEntryHeadThirdDtoMapper;
import com.dcjet.cs.entry.model.GwstdEntryHeadThird;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2023-3-17
 */
@Service
public class GwstdEntryHeadThirdService extends BaseService<GwstdEntryHeadThird> {
    @Resource
    private GwstdEntryHeadThirdMapper gwstdEntryHeadThirdMapper;
    @Resource
    private GwstdEntryHeadThirdDtoMapper gwstdEntryHeadThirdDtoMapper;
    @Override
    public Mapper<GwstdEntryHeadThird> getMapper() {
        return gwstdEntryHeadThirdMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwstdEntryHeadThirdParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwstdEntryHeadThirdDto>> getListPaged(GwstdEntryHeadThirdParam gwstdEntryHeadThirdParam, PageParam pageParam) {
        // 启用分页查询
        GwstdEntryHeadThird gwstdEntryHeadThird = gwstdEntryHeadThirdDtoMapper.toPo(gwstdEntryHeadThirdParam);
        Page<GwstdEntryHeadThird> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdEntryHeadThirdMapper.getList(gwstdEntryHeadThird));
        List<GwstdEntryHeadThirdDto> gwstdEntryHeadThirdDtos = page.getResult().stream().map(head -> {
            GwstdEntryHeadThirdDto dto = gwstdEntryHeadThirdDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwstdEntryHeadThirdDto>> paged = ResultObject.createInstance(gwstdEntryHeadThirdDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwstdEntryHeadThirdParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdEntryHeadThirdDto insert(GwstdEntryHeadThirdParam gwstdEntryHeadThirdParam, UserInfoToken userInfo) {
        GwstdEntryHeadThird gwstdEntryHeadThird = gwstdEntryHeadThirdDtoMapper.toPo(gwstdEntryHeadThirdParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwstdEntryHeadThird.setSid(sid);
        gwstdEntryHeadThird.setInsertUser(userInfo.getUserNo());
        gwstdEntryHeadThird.setInsertTime(new Date());
        // 新增数据
        int insertStatus = gwstdEntryHeadThirdMapper.insert(gwstdEntryHeadThird);
        return  insertStatus > 0 ? gwstdEntryHeadThirdDtoMapper.toDto(gwstdEntryHeadThird) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param gwstdEntryHeadThirdParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdEntryHeadThirdDto update(GwstdEntryHeadThirdParam gwstdEntryHeadThirdParam, UserInfoToken userInfo) {
        GwstdEntryHeadThird gwstdEntryHeadThird = gwstdEntryHeadThirdMapper.selectByPrimaryKey(gwstdEntryHeadThirdParam.getSid());
        gwstdEntryHeadThirdDtoMapper.updatePo(gwstdEntryHeadThirdParam, gwstdEntryHeadThird);
        gwstdEntryHeadThird.setUpdateUser(userInfo.getUserNo());
        gwstdEntryHeadThird.setUpdateTime(new Date());
        gwstdEntryHeadThird.setPromiseItems(DecVariable.fixPromiseItems(gwstdEntryHeadThird.getPromiseItems()));
        // 更新数据
        int update = gwstdEntryHeadThirdMapper.updateByPrimaryKey(gwstdEntryHeadThird);
        return update > 0 ? gwstdEntryHeadThirdDtoMapper.toDto(gwstdEntryHeadThird) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		gwstdEntryHeadThirdMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwstdEntryHeadThirdDto> selectAll(GwstdEntryHeadThirdParam exportParam, UserInfoToken userInfo) {
        GwstdEntryHeadThird gwstdEntryHeadThird = gwstdEntryHeadThirdDtoMapper.toPo(exportParam);
        // gwstdEntryHeadThird.setTradeCode(userInfo.getCompany());
        List<GwstdEntryHeadThirdDto> gwstdEntryHeadThirdDtos = new ArrayList<>();
        List<GwstdEntryHeadThird> gwstdEntryHeadThirds = gwstdEntryHeadThirdMapper.getList(gwstdEntryHeadThird);
        if (CollectionUtils.isNotEmpty(gwstdEntryHeadThirds)) {
            gwstdEntryHeadThirdDtos = gwstdEntryHeadThirds.stream().map(head -> {
                GwstdEntryHeadThirdDto dto = gwstdEntryHeadThirdDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwstdEntryHeadThirdDtos;
    }
}
