package com.dcjet.cs.entry.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.FastdFsService;
import com.dcjet.cs.dto.entry.*;
import com.dcjet.cs.dto.mat.AttachedDto;
import com.dcjet.cs.dto.tax.dutyform.GwstdDutyformFileDto;
import com.dcjet.cs.dto.war.WarringPassConfigDto;
import com.dcjet.cs.dto.war.WarringPassExpandConfigDto;
import com.dcjet.cs.entry.dao.*;
import com.dcjet.cs.entry.mapper.DecIEntryHeadDtoMapper;
import com.dcjet.cs.entry.mapper.DecIEntryListDtoMapper;
import com.dcjet.cs.entry.mapper.EntryThirdDtoMapper;
import com.dcjet.cs.entry.model.*;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.mat.model.Attached;
import com.dcjet.cs.tax.model.dutyform.GwstdDutyformFile;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.FileUtil;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.dcjet.cs.war.service.WarringPassExpandConfigService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.common.util.DateUtils;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class DecIEntryDiffService {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /***
     * 第三方数据缺失
     */
    private final String DIFF_NOT_OTHER = "-1";
    /***
     * 结果一致
     */
    private final String DIFF_EQUAL = "0";
    /***
     * 结果不同
     */
    private final String DIFF_UNEQUAL = "1";


    @Resource
    private DecIEntryHeadMapper decIEntryHeadMapper;

    @Resource
    private DecIEntryHeadDtoMapper decIEntryHeadDtoMapper;

    @Resource
    private DecIEntryListMapper decIEntryListMapper;

    @Resource
    private DecIEntryListDtoMapper decIEntryListDtoMapper;

    @Resource
    private EntryHeadThirdMapper entryHeadThirdMapper;

    @Resource
    private EntryHeadThirdExtMapper entryHeadThirdExtMapper;

    @Resource
    private EntryListThirdMapper entryListThirdMapper;

    @Resource
    private EntryThirdDtoMapper entryHeadThirdDtoMapper;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private AttachedMapper attachedMapper;
    @Resource
    private WarringPassConfigService warringPassConfigService;
    @Resource
    private WarringPassExpandConfigService warringPassExpandConfigService;
    @Resource
    private EntryThirdService entryThirdService;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource
    private FastdFsService fastdFsService;

    public ResultObject<List<DecIEntryDiffDto>> diffList(DecIEntryHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        DecIEntryHead headParam = decIEntryHeadDtoMapper.toPo(param);
        headParam.setTradeCode(userInfo.getCompany());
        Page<DecIEntryHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decIEntryHeadMapper.getListDiff(headParam));
        List<DecIEntryDiffDto> dtoList = page.getResult().stream().map(head -> {
            DecIEntryDiffDto dto = diff(head, userInfo);
            dto.setResult(diffThirdData(dto));
            return dto;
        }).collect(Collectors.toList());

        ResultObject<List<DecIEntryDiffDto>> paged = ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public DecIEntryDiffDto diff(String sid,String dataSource, UserInfoToken token) {
        DecIEntryHead head = decIEntryHeadMapper.selectByPrimaryKey(sid);
        head.setDataSource(dataSource);
        if (head == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("未找到对应的记录"));
        }
        return diff(head, token);
    }


    public DecIEntryDiffDto diff(DecIEntryHead head, UserInfoToken token) {
        DecIEntryHeadDto headDto = decIEntryHeadDtoMapper.toDto(head);

//        if (StringUtils.isNotBlank(head.getPromiseItems()) && head.getPromiseItems().contains(DecVariable.PROMISE_ITEM_4)) {
//            headDto.setCusRemarkAuto("1");
//        } else {
//            headDto.setCusRemarkAuto("0");
//        }
        //运输工具及航次号组装
        if(StringUtils.isNotBlank(headDto.getTrafName())) {
            headDto.setVoyageNo(headDto.getTrafName()+(StringUtils.isBlank(headDto.getVoyageNo())?"":"/"+headDto.getVoyageNo()));
        }
        else {
//            headDto.setVoyageNo((StringUtils.isBlank(headDto.getVoyageNo())?"":"/"+headDto.getVoyageNo()));
            if (StringUtils.isBlank(headDto.getVoyageNo())) {
                headDto.setVoyageNo(headDto.getVoyageNo());
            }
        }
//        headDto.setVoyageNo((StringUtils.isBlank(headDto.getTrafName()) ? "" : headDto.getTrafName()) + "/" + headDto.getVoyageNo());
        headDto.setPromiseItems(DecVariable.fixPromiseItems(headDto.getPromiseItems()));
        entryHeadThirdDtoMapper.updateIDecHead(headDto);
        DecIEntryDiffDto dto = new DecIEntryDiffDto();
        dto.setHead(headDto);

        DecIEntryList listQueryParam = new DecIEntryList();
        listQueryParam.setHeadId(head.getSid());
        listQueryParam.setTradeCode(token.getCompany());
        List<DecIEntryList> list = decIEntryListMapper.selectListByParam(listQueryParam);
        List<DecIEntryListDto> dtoList = list.stream().map(it -> {
            DecIEntryListDto listDto = decIEntryListDtoMapper.toDto(it);
            entryHeadThirdDtoMapper.updateIDecList(listDto);
            return listDto;
        }).collect(Collectors.toList());
        dto.setList(dtoList);

        EntryHeadThird thirdHead = entryHeadThirdMapper.selectByEmsListNos(head.getEmsListNo(),head.getIEMark(), head.getDataSource(), token.getCompany());
        if (thirdHead == null) {
            return dto;
        }
        DecIEntryHeadDto otherHead = entryHeadThirdDtoMapper.toDecIHeadDto(thirdHead);
        if (null != thirdHead.getDDate()) {
            otherHead.setDDate(DateUtils.dateToString(thirdHead.getDDate(), "yyyy-MM-dd"));
        }
        otherHead.setCusRemarkAuto("0");
        if (!Strings.isNullOrEmpty(thirdHead.getType())) {
            if (thirdHead.getType().length() < 2) {
                otherHead.setCusRemarkAuto(thirdHead.getType().contains("Z") ? "1" : "0");
            }
            if (thirdHead.getType().length() > 1) {
                otherHead.setCusRemarkAuto(thirdHead.getType().substring(0, 2).contains("Z") ? "1" : "0");
            }
            if (thirdHead.getType().length() > 3) {
                String value = thirdHead.getType().substring(3, 4);
                otherHead.setCardMark("1".equals(value) ? "1" : "0");
            }

            if (thirdHead.getType().length() > 4) {
                String value = thirdHead.getType().substring(4, 5);
                otherHead.setCheckMark("1".equals(value) ? "1" : "0");
            }

            if (thirdHead.getType().length() > 5) {
                String value = thirdHead.getType().substring(5, 6);
                otherHead.setTaxMark("1".equals(value) ? "1" : "0");
            }
        }

//        otherHead.setPromiseItems(DecVariable.fixPromiseItems(otherHead.getPromiseItems()));
        dto.setOtherHead(otherHead);
        EntryHeadThirdExt headThirdExt = entryHeadThirdExtMapper.selectByPrimaryKey(thirdHead.getSid());
        if (headThirdExt != null) {
            headDto.setApprovalStatus(headThirdExt.getApprovalStatus());
            headDto.setApprovalNote(headThirdExt.getApprovalNote());
            headDto.setSendStatus(headThirdExt.getSendStatus());
        } else {
            headDto.setApprovalStatus("0");
            headDto.setSendStatus("0");
        }

        EntryListThird thirdListQueryParam = new EntryListThird();
        thirdListQueryParam.setTradeCode(token.getCompany());
        thirdListQueryParam.setHeadId(thirdHead.getSid());
        List<EntryListThird> thirdList = entryListThirdMapper.getList(thirdListQueryParam);
        List<DecIEntryListDto> thirdDtoList = thirdList.stream().map(it -> {
            DecIEntryListDto thirdListDto = entryHeadThirdDtoMapper.toDecIListDto(it);
            convertDto(thirdListDto);
            return thirdListDto;
        }).collect(Collectors.toList());
        dto.setOtherList(thirdDtoList);

        return dto;
    }

    private void convertDto(DecIEntryListDto listDto) {
        listDto.setUnitName(pCodeHolder.getValue(PCodeType.UNIT, listDto.getUnit()));
        listDto.setUnit1Name(pCodeHolder.getValue(PCodeType.UNIT, listDto.getUnit1()));
        listDto.setUnit2Name(pCodeHolder.getValue(PCodeType.UNIT, listDto.getUnit2()));

        if (StringUtils.isNumeric(listDto.getCurr())) {
            listDto.setCurr(pCodeHolder.getCodeValue(PCodeType.CURR, pCodeHolder.getValue(PCodeType.CURR_OUTDATED, listDto.getCurr())));
        }
        listDto.setCurrName(pCodeHolder.getValue(PCodeType.CURR, listDto.getCurr()));
        if (StringUtils.isNumeric(listDto.getOriginCountry())) {
            listDto.setOriginCountry(pCodeHolder.getCodeValue(PCodeType.COUNTRY, pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, listDto.getOriginCountry())));
        }
        listDto.setOriginCountryName(pCodeHolder.getValue(PCodeType.COUNTRY, listDto.getOriginCountry()));
        if (StringUtils.isNumeric(listDto.getDestinationCountry())) {
            listDto.setDestinationCountry(pCodeHolder.getCodeValue(PCodeType.COUNTRY, pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, listDto.getDestinationCountry())));
        }
        listDto.setDestinationCountryName(pCodeHolder.getValue(PCodeType.COUNTRY, listDto.getDestinationCountry()));
//        listDto.setDistrictName(pCodeHolder.getValue(PCodeType.AREA, listDto.getDistrictCode()));
//        listDto.setDestName(pCodeHolder.getValue(PCodeType.PORT_LIN, listDto.getDestCode()));
//        listDto.setDutyModeName(pCodeHolder.getValue(PCodeType.LEVYMODE, listDto.getDutyMode()));
    }

    /***
     *
     *
     * @param head
     * @return -1 没有第三方数据  0 数据一致 1 数据不一致
     */
    private String diffThirdData(DecIEntryDiffDto head) {
        String result = diffHead(head.getHead(), head.getOtherHead());
        if (result != DIFF_EQUAL) {
            return result;
        }

        if (head.getList() == null || head.getOtherList() == null) {
            return DIFF_NOT_OTHER;
        }

        Map<BigDecimal, DecIEntryListDto> listMap = head.getOtherList().stream().collect(Collectors.toMap(it -> it.getSerialNo(), it -> it, (a, b) -> a));
        for (DecIEntryListDto listDto : head.getList()) {
            result = diffList(listDto, listMap.get(listDto.getSerialNo()));
            if (result != DIFF_EQUAL) {
                return result;
            }
        }

        return DIFF_EQUAL;
    }

    private String diffHead(DecIEntryHeadDto head, DecIEntryHeadDto otherHead) {
        if (otherHead == null) {
            return DIFF_NOT_OTHER;
        }

        if (equals(head.getDeclareCode(), otherHead.getDeclareCode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getDeclareName(), otherHead.getDeclareName())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getContrNo(), otherHead.getContrNo())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getMasterCustoms(), otherHead.getMasterCustoms())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getDutyMode(), otherHead.getDutyMode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getDestPort(), otherHead.getDestPort())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getFeeCurr(), otherHead.getFeeCurr())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getFeeMark(), otherHead.getFeeMark())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getFeeRate(), otherHead.getFeeRate())) {
            return DIFF_UNEQUAL;
        }

        if(equals(head.getGrossWt(), otherHead.getGrossWt())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getIedate(), otherHead.getIedate())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getIEMark(), otherHead.getIEMark())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getIEPort(), otherHead.getIEPort())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getInsurCurr(), otherHead.getInsurCurr())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getInsurMark(), otherHead.getInsurMark())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getInsurRate(), otherHead.getInsurRate())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getEmsNo(), otherHead.getEmsNo())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getNetWt(), otherHead.getNetWt())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getNote(), otherHead.getNote())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getOtherCurr(), otherHead.getOtherCurr())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getOtherMark(), otherHead.getOtherMark())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getOtherRate(), otherHead.getOtherRate())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getOwnerCode(), otherHead.getOwnerCode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getOwnerName(), otherHead.getOwnerName())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getPackNum(), otherHead.getPackNum())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getTradeCountry(), otherHead.getTradeCountry())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getTradeMode(), otherHead.getTradeMode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getTradeCode(), otherHead.getTradeCode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getTrafMode(), otherHead.getTrafMode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getTrafName(), otherHead.getTrafName())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getTradeName(), otherHead.getTradeName())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getTransMode(), otherHead.getTransMode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getWrapType(), otherHead.getWrapType())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getPromiseItems(), otherHead.getPromiseItems())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getTradeNation(), otherHead.getTradeNation())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getMarkNo(), otherHead.getMarkNo())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getDespPort(), otherHead.getDespPort())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getEntryPort(), otherHead.getEntryPort())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getWarehouse(), otherHead.getWarehouse())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getHawb(), otherHead.getHawb())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getCutMode(), otherHead.getCutMode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getTradeCode(), otherHead.getTradeCode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getCusRemarkAuto(), otherHead.getCusRemarkAuto())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getCardMark(), otherHead.getCardMark())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getCheckMark(), otherHead.getCheckMark())) {
            return DIFF_UNEQUAL;
        }

        if (equals(head.getTaxMark(), otherHead.getTaxMark())) {
            return DIFF_UNEQUAL;
        }


        return  DIFF_EQUAL;
    }

    private String diffList(DecIEntryListDto list, DecIEntryListDto otherList) {
        if (otherList == null) {
            return DIFF_NOT_OTHER;
        }

        if (equals(list.getCodeTS(), otherList.getCodeTS())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getGNo(), otherList.getGNo())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getDecPrice(), otherList.getDecPrice())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getDecTotal(), otherList.getDecTotal())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getDutyMode(), otherList.getDutyMode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getUnit1(), otherList.getUnit1())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getQty1(), otherList.getQty1())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getUnit(), otherList.getUnit())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getGModel(), otherList.getGModel())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getGName(), otherList.getGName())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getQty(), otherList.getQty())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getOriginCountry(), otherList.getOriginCountry())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getUnit2(), otherList.getUnit2())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getQty2(), otherList.getQty2())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getCurr(), otherList.getCurr())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getDestinationCountry(), otherList.getDestinationCountry())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getDestCode(), otherList.getDestCode())) {
            return DIFF_UNEQUAL;
        }

        if (equals(list.getDistrictCode(), otherList.getDistrictCode())) {
            return DIFF_UNEQUAL;
        }

        return  DIFF_EQUAL;
    }

    /***
     * 如果第一个对象为空，认为比对一致
     *
     * @param first
     * @param second
     * @return
     */
    private Boolean equals(Object first, Object second) {
        if (first == null) {
            return true;
        }
        return Objects.equals(first, second);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject<AttachedDto> uploadChecklists(MultipartFile[] files, String businessSid, String checkTheTime, UserInfoToken token) throws Exception {
        ResultObject result = ResultObject.createInstance();

        String taskId = UUID.randomUUID().toString();

        //
        String uploadChecklist = "0";
        String approvedByreview = "0";
        //仅上传
        String onlyUpload = "0";
        List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(token);
        if (CollectionUtils.isNotEmpty(warringPassConfigDtos)) {
            WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);

            List<WarringPassExpandConfigDto> warringPassExpandConfigs = warringPassExpandConfigService.selectAll(warringPassConfigDto.getSid(), token);
            if (warringPassExpandConfigs != null) {
                if (warringPassExpandConfigs.size() > 0) {
                    WarringPassExpandConfigDto warringPassExpandConfigDto = warringPassExpandConfigs.get(0);
                    if (StringUtils.isNotBlank(warringPassExpandConfigDto.getUploadChecklist()) && StringUtils.equals(warringPassExpandConfigDto.getUploadChecklist(), ConstantsStatus.STATUS_0)) {
                        uploadChecklist = warringPassExpandConfigDto.getUploadChecklist();
                    }
                    if (StringUtils.isNotBlank(warringPassExpandConfigDto.getUploadChecklist()) && StringUtils.equals(warringPassExpandConfigDto.getUploadChecklist(), ConstantsStatus.STATUS_2)) {
                        approvedByreview = warringPassExpandConfigDto.getUploadChecklist();
                    }
                    if (StringUtils.isNotBlank(warringPassExpandConfigDto.getUploadChecklist()) && StringUtils.equals(warringPassExpandConfigDto.getUploadChecklist(), ConstantsStatus.STATUS_1)) {
                        onlyUpload = warringPassExpandConfigDto.getUploadChecklist();
                    }
                }
            }
        }

        logger.info("核对单上传开始: taskId：" + taskId+"企业编码："+token.getCompany());
        if (StringUtils.equals(onlyUpload, ConstantsStatus.STATUS_1) || StringUtils.equals(approvedByreview, ConstantsStatus.STATUS_2)) {
            Attached attachedList = attachedMapper.getlstAll(businessSid,token.getCompany());
            if (attachedList!=null){
                attachedMapper.deleteHeadId(businessSid, token.getCompany());
                fileHandler.deleteFile(attachedList.getFileName());
            }


            Attached attached = new Attached();
            String uuid = UUID.randomUUID().toString();
            attached.setSid(uuid);
            if (StringUtils.isBlank(businessSid)) {
                logger.info("业务单证主键能为空" + taskId);
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("业务单证主键不能为空"));
            }

            if (files != null && files.length > 0) {
                for (MultipartFile file : files) {
                    //保存文件
                    if (!file.isEmpty()) {
                        String originName = FileUtil.getValidFileName(file.getOriginalFilename());
                        String extName = originName.substring(originName.lastIndexOf("."));
                        if (StringUtils.isNotBlank(extName) && ".exe,.com,.pif,.bat,.scr".contains(extName.toLowerCase())) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("文件扩展名不支持"));
                        }
                        if (originName != null && originName.length() > 200) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("文件名长度不能超过200位"));
                        }
                        if (file.getSize() > 10 * 1024 * 1024) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("附件大小超过10M"));
                        }
                        String fileName = "";
                        int repeatCount = 0;
                        while (repeatCount < 3) {
                            try {
                                FileUtil.isValidFileType(file.getOriginalFilename());
                                fileName = fileHandler.uploadFile(file.getInputStream());
                                break;
                            } catch (Exception e) {
                                logger.error(e.getMessage());
                                fileName = "";
                                repeatCount++;
                            }
                        }
                        if (!StringUtils.isBlank(fileName)) {
                            attached.setFileName(fileName);
                        } else {
                            logger.info("文件上传失败:" + taskId+"企业编码："+token.getCompany());
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("文件上传失败"));
                        }
                        attached.setOriginFileName(originName);
                        attached.setBusinessSid(businessSid);
                        attached.setBusinessType("IM");
                        attached.setFileName(fileName);
                        attached.setAcmpType(null);
                        attached.setInsertUser(token.getUserNo());
                        attached.setInsertTime(new Date());
                        attached.setTradeCode(token.getCompany());
                        attached.setDataSource(ConstantsStatus.STATUS_0);
                        attachedMapper.insert(attached);
                    }
                }
            }else {
                logger.info("附件上传为空" + taskId);
            }

                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = formatter.parse(checkTheTime);
                DecEntryApprovalParam param = new DecEntryApprovalParam();
                param.setSid(businessSid);
                if (StringUtils.equals(onlyUpload,ConstantsStatus.STATUS_1)) {
                    logger.info("开始仅上传" + taskId);
                    entryThirdService.approvalApprovedByreview("I", param, date, onlyUpload, token);
                    logger.info("结束仅上传" + taskId);
                }else if (StringUtils.equals(approvedByreview,ConstantsStatus.STATUS_2)) {
                    logger.info("开始上传加内审" + taskId);
                    param.setApprovalStatus(ConstantsStatus.STATUS_1);
                    param.setApprovalNote("审批通过");
                    entryThirdService.approvalApprovedByreview("I", param, date, approvedByreview, token);
                    logger.info("结束上传加内审" + taskId);
                }


        }
//        if (onlyUpload){
//            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            Date date = formatter.parse(checkTheTime);
//            DecEntryApprovalParam param = new DecEntryApprovalParam();
//            param.setSid(businessSid);
//            entryThirdService.onlyUploads("I", param, date,onlyUpload, token);
//        }
//        if (approvedByreview) {
//            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            Date date = formatter.parse(checkTheTime);
//
//            DecEntryApprovalParam param = new DecEntryApprovalParam();
//            param.setSid(businessSid);
//            param.setApprovalStatus(ConstantsStatus.STATUS_1);
//            param.setApprovalNote("审批通过");
//            entryThirdService.approvalApprovedByreview("I", param, date, approvedByreview, token);
//        }

        return null;
    }


    public ResponseEntity downloadAppendix(String sid, UserInfoToken userInfo) throws Exception {
        Attached attached = attachedMapper.getlstAll(sid, userInfo.getCompany());
        String path = "";
        String fileName = "";
        path = attached.getFileName();
        fileName = attached.getOriginFileName();
        //获取附件
        byte[] bytes = fastdFsService.getByteFromFastDFS(path);
        HttpHeaders h = new HttpHeaders();
        fileName = URLEncoder.encode(fileName, CommonVariable.UTF8);
        h.setContentDispositionFormData("attachment", fileName);
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<>(bytes, h, HttpStatus.OK);

    }

    public ResponseEntity downloadAppendixIpreview(String sid, UserInfoToken userInfo) throws Exception {
        Attached file = attachedMapper.getlstAll(sid, userInfo.getCompany());

        return fastdFsService.getFileFromFastDFSByIOS(file.getFileName(), file.getOriginFileName());
    }

    public ResultObject getlist(String sid, UserInfoToken userInfo) {
        ResultObject result = null;
        Attached file = attachedMapper.getlstAll(sid, userInfo.getCompany());
        file.setFileName(file.getFileName() + ".pdf");
        result = ResultObject.createInstance(file, 0);
        return result;
    }
}
