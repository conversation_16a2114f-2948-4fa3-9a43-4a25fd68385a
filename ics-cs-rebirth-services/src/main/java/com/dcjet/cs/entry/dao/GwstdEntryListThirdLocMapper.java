package com.dcjet.cs.entry.dao;

import com.dcjet.cs.entry.model.EntryListThirdLoc;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * GwstdEntryListThirdLoc
 *
 * <AUTHOR>
 * @date: 2022-8-3
 */
public interface GwstdEntryListThirdLocMapper extends Mapper<EntryListThirdLoc> {
    /**
     * 查询获取数据
     *
     * @param gwstdEntryListThirdLoc
     * @return
     */
    List<EntryListThirdLoc> getList(EntryListThirdLoc gwstdEntryListThirdLoc);

    /**
     * 根据表头headId查询是否存在表体数据
     *
     * @param sids
     * @return
     */
    int getListNumByHeadIds(List<String> sids);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
