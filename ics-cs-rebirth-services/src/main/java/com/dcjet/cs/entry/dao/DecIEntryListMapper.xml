<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.entry.dao.DecIEntryListMapper">
    <resultMap id="tDecIEntryListResultMap" type="com.dcjet.cs.entry.model.DecIEntryList">
        <result column="SID" jdbcType="VARCHAR" property="sid"/>
        <result column="SEQ_NO" jdbcType="VARCHAR" property="seqNo"/>
        <result column="CLASS_MARK" jdbcType="VARCHAR" property="classMark"/>
        <result column="CODE_T_S" jdbcType="VARCHAR" property="codeTS"/>
        <result column="G_NO" jdbcType="DECIMAL" property="GNo"/>
        <result column="DEC_PRICE" jdbcType="DECIMAL" property="decPrice"/>
        <result column="DEC_TOTAL" jdbcType="DECIMAL" property="decTotal"/>
        <result column="DUTY_MODE" jdbcType="VARCHAR" property="dutyMode"/>
        <result column="COP_G_NO" jdbcType="VARCHAR" property="copGNo"/>
        <result column="EXG_VERSION" jdbcType="VARCHAR" property="exgVersion"/>
        <result column="FACTOR_1" jdbcType="DECIMAL" property="factor1"/>
        <result column="UNIT_1" jdbcType="VARCHAR" property="unit1"/>
        <result column="QTY_1" jdbcType="DECIMAL" property="qty1"/>
        <result column="UNIT" jdbcType="VARCHAR" property="unit"/>
        <result column="G_MODEL" jdbcType="VARCHAR" property="GModel"/>
        <result column="G_NAME" jdbcType="VARCHAR" property="GName"/>
        <result column="SERIAL_NO" jdbcType="DECIMAL" property="serialNo"/>
        <result column="QTY" jdbcType="DECIMAL" property="qty"/>
        <result column="ORIGIN_COUNTRY" jdbcType="VARCHAR" property="originCountry"/>
        <result column="UNIT_2" jdbcType="VARCHAR" property="unit2"/>
        <result column="QTY_2" jdbcType="DECIMAL" property="qty2"/>
        <result column="CURR" jdbcType="VARCHAR" property="curr"/>
        <result column="USE_TO" jdbcType="VARCHAR" property="useTo"/>
        <result column="WORK_USD" jdbcType="DECIMAL" property="workUsd"/>
        <result column="DESTINATION_COUNTRY" jdbcType="VARCHAR" property="destinationCountry"/>
        <result column="CIQ_CODE" jdbcType="VARCHAR" property="ciqCode"/>
        <result column="DECL_GOODS_ENAME" jdbcType="VARCHAR" property="declGoodsEname"/>
        <result column="ORIGPLACE_CODE" jdbcType="VARCHAR" property="origplaceCode"/>
        <result column="PUR_POSE" jdbcType="VARCHAR" property="purPose"/>
        <result column="PROD_VALIDDT" jdbcType="VARCHAR" property="prodValiddt"/>
        <result column="PROD_QGP" jdbcType="VARCHAR" property="prodQgp"/>
        <result column="GOODS_ATTR" jdbcType="VARCHAR" property="goodsAttr"/>
        <result column="STUFF" jdbcType="VARCHAR" property="stuff"/>
        <result column="UN_CODE" jdbcType="VARCHAR" property="unCode"/>
        <result column="DANG_NAME" jdbcType="VARCHAR" property="dangName"/>
        <result column="DANG_PACK_TYPE" jdbcType="VARCHAR" property="dangPackType"/>
        <result column="DANG_PACK_SPEC" jdbcType="VARCHAR" property="dangPackSpec"/>
        <result column="ENGMANENT_CNM" jdbcType="VARCHAR" property="engmanentCnm"/>
        <result column="NODANG_FLAG" jdbcType="VARCHAR" property="nodangFlag"/>
        <result column="DEST_CODE" jdbcType="VARCHAR" property="destCode"/>
        <result column="GOODS_SPEC" jdbcType="VARCHAR" property="goodsSpec"/>
        <result column="GOODS_MODEL" jdbcType="VARCHAR" property="goodsModel"/>
        <result column="GOODS_BRAND" jdbcType="VARCHAR" property="goodsBrand"/>
        <result column="PRODUCE_DATE" jdbcType="VARCHAR" property="produceDate"/>
        <result column="PRODBATCH_NO" jdbcType="VARCHAR" property="prodbatchNo"/>
        <result column="DISTRICT_CODE" jdbcType="VARCHAR" property="districtCode"/>
        <result column="CIQ_NAME" jdbcType="VARCHAR" property="ciqName"/>
        <result column="MNUFCTR_REGNO" jdbcType="VARCHAR" property="mnufctrRegno"/>
        <result column="MNUFCR_REGNAME" jdbcType="VARCHAR" property="mnufcrRegname"/>
        <result column="INSERT_USER" jdbcType="VARCHAR" property="insertUser"/>
        <result column="INSERT_TIME" jdbcType="TIMESTAMP" property="insertTime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="HEAD_ID" jdbcType="VARCHAR" property="headId"/>
        <result column="G_NAME_MODEL" jdbcType="VARCHAR" property="gNameModel"/>
        <result column="DEC_ALL" jdbcType="VARCHAR" property="decAll"/>
        <result column="QTY_UNIT" jdbcType="VARCHAR" property="qtyUnit"/>
        <result column="G_NO_ALL" jdbcType="VARCHAR" property="gNoAll"/>
        <result column="DISTRICT_POST_CODE" jdbcType="VARCHAR" property="districtPostCode"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" jdbcType="VARCHAR" property="tradeCode"/>
        <result column="EMS_NO" jdbcType="VARCHAR" property="emsNo"/>
    </resultMap>
    <sql id="Base_Column_List">
        H.EMS_NO,
        t.SID,
        t.SEQ_NO,
        t.CLASS_MARK,
        t.CODE_T_S,
        t.G_NO,
        t.DEC_PRICE,
        t.DEC_TOTAL,
        t.DUTY_MODE,
        t.COP_G_NO,
        t.EXG_VERSION,
        t.FACTOR_1,
        t.UNIT_1,
        t.QTY_1,
        t.UNIT,
        t.G_MODEL,
        t.G_NAME,
        t.SERIAL_NO,
        t.QTY,
        t.ORIGIN_COUNTRY,
        t.UNIT_2,
        t.QTY_2,
        t.CURR,
        t.USE_TO,
        t.WORK_USD,
        t.DESTINATION_COUNTRY,
        t.CIQ_CODE,
        t.DECL_GOODS_ENAME,
        t.ORIGPLACE_CODE,
        t.PUR_POSE,
        t.PROD_VALIDDT,
        t.PROD_QGP,
        t.GOODS_ATTR,
        t.STUFF,
        t.UN_CODE,
        t.DANG_NAME,
        t.DANG_PACK_TYPE,
        t.DANG_PACK_SPEC,
        t.ENGMANENT_CNM,
        t.NODANG_FLAG,
        t.DEST_CODE,
        t.GOODS_SPEC,
        t.GOODS_MODEL,
        t.GOODS_BRAND,
        t.PRODUCE_DATE,
        t.PRODBATCH_NO,
        t.DISTRICT_CODE,
        t.DISTRICT_POST_CODE,
        t.CIQ_NAME,
        t.MNUFCTR_REGNO,
        t.MNUFCR_REGNAME,
        t.INSERT_USER,
        t.INSERT_TIME,
        t.UPDATE_USER,
        t.UPDATE_TIME,
        t.HEAD_ID,
        t.G_NAME as G_NAME_MODEL,
        '' AS DEC_ALL,
        '' as  QTY_UNIT,
        '' as  G_NO_ALL
        ,t.INSERT_USER_NAME
     ,t.UPDATE_USER_NAME
     , t.TRADE_CODE
     , ( CASE  WHEN T.stuff is NULL THEN '' ELSE T.stuff || ';' END ) ||
	( CASE  WHEN T.prod_validdt is NULL THEN '' ELSE T.prod_validdt || ';' END ) ||
	( CASE  WHEN T.prod_Qgp is NULL THEN '' ELSE T.prod_Qgp || ';' END ) ||
	( CASE  WHEN T.engmanent_cnm is NULL THEN '' ELSE T.engmanent_cnm || ';' END )||
	( CASE  WHEN T.goods_Spec is NULL THEN '' ELSE T.goods_Spec || ';' END ) ||
	( CASE  WHEN T.goods_Model is NULL THEN '' ELSE T.goods_Model || ';' END ) ||
	( CASE  WHEN T.goods_Brand is NULL THEN '' ELSE T.goods_Brand || ';' END ) ||
	( CASE  WHEN T.produce_date is NULL THEN '' ELSE T.produce_date || ';' END ) ||
	( CASE  WHEN T.prodbatch_no is NULL THEN '' ELSE T.prodbatch_no || ';' END ) AS goodsAttrAll
    </sql>

    <sql id="Base_Column_List_Attr">
        t.SID,
        t.CODE_T_S,
        t.G_MODEL,
        t.G_NAME,
        t.SERIAL_NO,
        t.ORIGIN_COUNTRY,
        t.GOODS_SPEC,
        t.GOODS_ATTR,
        t.PUR_POSE,
        t.HEAD_ID,
        t.INSERT_USER,
        t.INSERT_TIME,
        t.UPDATE_USER,
        t.UPDATE_TIME,
       STUFF,
       PROD_VALIDDT,
       PROD_QGP,
       ENGMANENT_CNM,
       GOODS_SPEC,
       GOODS_MODEL,
       GOODS_BRAND,
       PRODUCE_DATE,
       PRODBATCH_NO
     , ( CASE  WHEN T.stuff is NULL THEN '' ELSE T.stuff || ';' END ) ||
	( CASE  WHEN T.prod_validdt is NULL THEN '' ELSE T.prod_validdt || ';' END ) ||
	( CASE  WHEN T.prod_Qgp is NULL THEN '' ELSE T.prod_Qgp || ';' END ) ||
	( CASE  WHEN T.engmanent_cnm is NULL THEN '' ELSE T.engmanent_cnm || ';' END )||
	( CASE  WHEN T.goods_Spec is NULL THEN '' ELSE T.goods_Spec || ';' END ) ||
	( CASE  WHEN T.goods_Model is NULL THEN '' ELSE T.goods_Model || ';' END ) ||
	( CASE  WHEN T.goods_Brand is NULL THEN '' ELSE T.goods_Brand || ';' END ) ||
	( CASE  WHEN T.produce_date is NULL THEN '' ELSE T.produce_date || ';' END ) ||
	( CASE  WHEN T.prodbatch_no is NULL THEN '' ELSE T.prodbatch_no || ';' END ) AS goodsAttrAll
    </sql>

    <select id="selectListByParam" parameterType="map" resultMap="tDecIEntryListResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_DEC_I_ENTRY_LIST t left join T_DEC_I_ENTRY_HEAD H on H.SID=t.HEAD_ID
        <where>
            <!-- 用户Grid查询 and 条件-->
            <if test="emsListNo != null and emsListNo!=''">
                and H.EMS_LIST_NO =#{emsListNo,jdbcType=VARCHAR}
            </if>
            <if test="headId != null and headId!=''">
                and t.HEAD_ID =#{headId,jdbcType=VARCHAR}
            </if>
                and H.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        </where>
        order by SERIAL_NO,t.sid
    </select>
    <select id="getListByBillHeadId" parameterType="string" resultMap="tDecIEntryListResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_DEC_I_ENTRY_LIST t where t.BILL_HEAD_ID = #{billHeadId}
        order by SERIAL_NO,t.sid
    </select>


    <!-- 用户Grid查询 用于导出  JJL ADD 2020-03-22 -->
    <select id="selectListAttr" parameterType="map" resultMap="tDecIEntryListResultMap">
        select
        <include refid="Base_Column_List_Attr"/>
        from T_DEC_I_ENTRY_LIST t inner join T_DEC_I_ENTRY_HEAD H on H.SID=t.HEAD_ID
        <where>
            <!-- 用户Grid查询 and 条件-->
            <if test="headId != null and headId!=''">
                and T.HEAD_ID =#{headId,jdbcType=VARCHAR}
            </if>
                and H.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        </where>
        order by SERIAL_NO,t.sid
    </select>

    <select id="selectList" resultType="com.dcjet.cs.entry.model.DecIEntryList">
        SELECT * FROM T_DEC_I_ENTRY_LIST WHERE serial_no=#{serialNo} AND CODE_T_S=#{codeTS} AND g_name = #{GName} AND head_id = #{headId} AND TRADE_CODE = #{tradeCode}
    </select>
</mapper>
