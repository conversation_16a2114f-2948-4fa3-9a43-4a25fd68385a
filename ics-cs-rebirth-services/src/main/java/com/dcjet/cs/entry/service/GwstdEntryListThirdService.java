package com.dcjet.cs.entry.service;

import com.dcjet.cs.dto.entry.GwstdEntryListThirdDto;
import com.dcjet.cs.dto.entry.GwstdEntryListThirdParam;
import com.dcjet.cs.entry.dao.GwstdEntryHeadThirdMapper;
import com.dcjet.cs.entry.dao.GwstdEntryListThirdMapper;
import com.dcjet.cs.entry.mapper.GwstdEntryListThirdDtoMapper;
import com.dcjet.cs.entry.model.GwstdEntryHeadThird;
import com.dcjet.cs.entry.model.GwstdEntryListThird;
import com.dcjet.cs.util.ConstantsStatus;
import org.apache.commons.collections4.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2023-3-17
 */
@Service
public class GwstdEntryListThirdService extends BaseService<GwstdEntryListThird> {
    @Resource
    private GwstdEntryListThirdMapper gwstdEntryListThirdMapper;

    @Resource
    private GwstdEntryHeadThirdMapper gwstdEntryHeadThirdMapper;

    @Resource
    private GwstdEntryListThirdDtoMapper gwstdEntryListThirdDtoMapper;

    @Override
    public Mapper<GwstdEntryListThird> getMapper() {
        return gwstdEntryListThirdMapper;
    }

    /**
     * 功能描述: grid分页查询
     *
     * @param gwstdEntryListThirdParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwstdEntryListThirdDto>> selectAllPaged(GwstdEntryListThirdParam gwstdEntryListThirdParam, PageParam pageParam) {
        // 启用分页查询
        GwstdEntryListThird gwstdEntryListThird = gwstdEntryListThirdDtoMapper.toPo(gwstdEntryListThirdParam);
        Page<GwstdEntryListThird> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdEntryListThirdMapper.getList(gwstdEntryListThird));
        List<GwstdEntryListThirdDto> gwstdEntryListThirdDtos = page.getResult().stream().map(head -> {
            GwstdEntryListThirdDto dto = gwstdEntryListThirdDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdEntryListThirdDto>> paged = ResultObject.createInstance(gwstdEntryListThirdDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwstdEntryListThirdDto> selectAll(GwstdEntryListThirdParam exportParam, UserInfoToken userInfo) {
        GwstdEntryListThird gwstdEntryListThird = gwstdEntryListThirdDtoMapper.toPo(exportParam);
        gwstdEntryListThird.setTradeCode(userInfo.getCompany());
        List<GwstdEntryListThirdDto> gwstdEntryListThirdDtos = new ArrayList<>();
        List<GwstdEntryListThird> gwstdEntryListThirds = gwstdEntryListThirdMapper.getList(gwstdEntryListThird);
        if (CollectionUtils.isNotEmpty(gwstdEntryListThirds)) {
            gwstdEntryListThirdDtos = gwstdEntryListThirds.stream().map(item -> {
                GwstdEntryListThirdDto dto = gwstdEntryListThirdDtoMapper.toDto(item);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwstdEntryListThirdDtos;
    }

    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public GwstdEntryListThirdDto insert(GwstdEntryListThirdParam model, UserInfoToken userInfo) {
        String sid = UUID.randomUUID().toString();
        GwstdEntryListThird gwstdEntryListThird = gwstdEntryListThirdDtoMapper.toPo(model);
        gwstdEntryListThird.setSid(sid);
        gwstdEntryListThird.setInsertUser(userInfo.getUserNo());
        gwstdEntryListThird.setInsertTime(new Date());
        int insertStatus = gwstdEntryListThirdMapper.insert(gwstdEntryListThird);
        return insertStatus > 0 ? gwstdEntryListThirdDtoMapper.toDto(gwstdEntryListThird) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwstdEntryListThirdParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdEntryListThirdDto update(GwstdEntryListThirdParam gwstdEntryListThirdParam, UserInfoToken userInfo) {
        GwstdEntryListThird gwstdEntryListThird = gwstdEntryListThirdMapper.selectByPrimaryKey(gwstdEntryListThirdParam.getSid());
        gwstdEntryListThirdDtoMapper.updatePo(gwstdEntryListThirdParam, gwstdEntryListThird);
        gwstdEntryListThird.setUpdateUser(userInfo.getUserNo());
        gwstdEntryListThird.setUpdateTime(new Date());
        // 更新数据
        int update = gwstdEntryListThirdMapper.updateByPrimaryKey(gwstdEntryListThird);
        if (update > 0) {
            // 表头相关
            GwstdEntryHeadThird gwstdEntryHeadThird = gwstdEntryHeadThirdMapper.selectByPrimaryKey(gwstdEntryListThird.getHeadId());
            if (ConstantsStatus.STATUS_1.equals(gwstdEntryHeadThird.getModifyStatus())) {
                gwstdEntryHeadThird.setModifyStatus(ConstantsStatus.STATUS_2);
                update = gwstdEntryHeadThirdMapper.updateByPrimaryKey(gwstdEntryHeadThird);
            }
        }
        return update > 0 ? gwstdEntryListThirdDtoMapper.toDto(gwstdEntryListThird) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids, UserInfoToken userInfo) {
        gwstdEntryListThirdMapper.deleteBySids(sids);
    }

    public int getListNumByHeadIds(List<String> sids) {
        return gwstdEntryListThirdMapper.getListNumByHeadIds(sids);
    }
}
