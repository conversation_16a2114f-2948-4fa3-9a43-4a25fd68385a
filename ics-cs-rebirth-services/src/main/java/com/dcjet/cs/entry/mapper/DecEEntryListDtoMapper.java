package com.dcjet.cs.entry.mapper;


import com.dcjet.cs.dto.entry.DecEEntryListDto;
import com.dcjet.cs.dto.entry.DecEEntryListParam;
import com.dcjet.cs.dto.imp.NormalEntryListParam;
import com.dcjet.cs.entry.model.DecEEntryList;
import com.dcjet.cs.imp.model.EntryList;
import org.mapstruct.*;

/**
* generated by Generate dcits
* 进口报关单表体
* @author: 鏈辨涓�
* @date: 2019-03-13
*/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecEEntryListDtoMapper {

   /**
   * 杞崲鏁版嵁搴撳璞″埌DTO
   * @param po
   * @return
   */
   DecEEntryListDto toDto(DecEEntryList po);

   /**
   * 杞崲Param鍒版暟鎹簱瀵硅薄
   * @param param
   * @return
   */
   DecEEntryList toPo(DecEEntryListParam param);

   /**
    * 数据库实体转换成报关立交桥接收的报关单表体json实体
    * @param param
    * @return
    */
   @Mappings({
           @Mapping(source = "GNo", target = "ContrItem"),
           @Mapping(source = "decPrice", target = "DeclPrice"),
           @Mapping(source = "decTotal", target = "DeclTotal"),
           @Mapping(source = "unit", target = "GUnit"),
           @Mapping(source = "qty", target = "GQty"),
           @Mapping(source = "serialNo", target = "GNo"),
           @Mapping(source = "insertUser", target = "ModifiedBy"),
           @Mapping(source = "mnufcrRegname", target = "Mnufctrregname"),
           @Mapping(source = "codeTS", target = "CodeTs")

   })
   EntryList toEnt(DecEEntryList param);

    NormalEntryListParam toEnt2(DecEEntryList decEEntryListItem);


   /**
    * JJL ADD 2020-03-20
    * 数据库原始数据更新
    * @param decEEntryListParam
    * @param decEEntryList
    */
   void updatePo(DecEEntryListParam decEEntryListParam, @MappingTarget DecEEntryList decEEntryList);
   default void patchPo(DecEEntryListParam decEEntryListParam, DecEEntryList decEEntryList) {
      // TODO 自行实现局部更新
   }
}
