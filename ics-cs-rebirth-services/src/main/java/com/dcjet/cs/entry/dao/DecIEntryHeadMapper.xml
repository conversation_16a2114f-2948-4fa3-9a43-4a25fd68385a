<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.entry.dao.DecIEntryHeadMapper">
    <resultMap id="tDecIEntryHeadResultMap" type="com.dcjet.cs.entry.model.DecIEntryHead">
        <result column="SID" jdbcType="VARCHAR" property="sid"/>
        <result column="COP_NO" jdbcType="VARCHAR" property="copNo"/>
        <result column="DECLARE_CODE" jdbcType="VARCHAR" property="declareCode"/>
        <result column="DECLARE_NAME" jdbcType="VARCHAR" property="declareName"/>
        <result column="APPR_NO" jdbcType="VARCHAR" property="apprNo"/>
        <result column="EMS_LIST_NO" jdbcType="VARCHAR" property="emsListNo"/>
        <result column="CONTR_NO" jdbcType="VARCHAR" property="contrNo"/>
        <result column="INPUT_CODE" jdbcType="VARCHAR" property="inputCode"/>
        <result column="INPUT_NAME" jdbcType="VARCHAR" property="inputName"/>
        <result column="MASTER_CUSTOMS" jdbcType="VARCHAR" property="masterCustoms"/>
        <result column="CUT_MODE" jdbcType="VARCHAR" property="cutMode"/>
        <result column="DATA_SOURCE" jdbcType="VARCHAR" property="dataSource"/>
        <result column="DECL_TRNREL" jdbcType="VARCHAR" property="declTrnrel"/>
        <result column="DEST_PORT" jdbcType="VARCHAR" property="destPort"/>
        <result column="DCLCUS_MARK" jdbcType="VARCHAR" property="dclcusMark"/>
        <result column="ENTRY_NO" jdbcType="VARCHAR" property="entryNo"/>
        <result column="ENTRY_TYPE" jdbcType="VARCHAR" property="entryType"/>
        <result column="FEE_CURR" jdbcType="VARCHAR" property="feeCurr"/>
        <result column="FEE_MARK" jdbcType="VARCHAR" property="feeMark"/>
        <result column="FEE_RATE" jdbcType="DECIMAL" property="feeRate"/>
        <result column="GROSS_WT" jdbcType="DECIMAL" property="grossWt"/>
        <result column="I_E_DATE" jdbcType="DATE" property="iedate"/>
        <result column="I_E_MARK" jdbcType="VARCHAR" property="IEMark"/>
        <result column="I_E_PORT" jdbcType="VARCHAR" property="IEPort"/>
        <result column="INPUTER_NAME" jdbcType="VARCHAR" property="inputerName"/>
        <result column="INSUR_CURR" jdbcType="VARCHAR" property="insurCurr"/>
        <result column="INSUR_MARK" jdbcType="VARCHAR" property="insurMark"/>
        <result column="INSUR_RATE" jdbcType="DECIMAL" property="insurRate"/>
        <result column="LICENSE_NO" jdbcType="VARCHAR" property="licenseNo"/>
        <result column="EMS_NO" jdbcType="VARCHAR" property="emsNo"/>
        <result column="NET_WT" jdbcType="DECIMAL" property="netWt"/>
        <result column="NOTE" jdbcType="VARCHAR" property="note"/>
        <result column="OTHER_CURR" jdbcType="VARCHAR" property="otherCurr"/>
        <result column="OTHER_MARK" jdbcType="VARCHAR" property="otherMark"/>
        <result column="OTHER_RATE" jdbcType="DECIMAL" property="otherRate"/>
        <result column="OWNER_CODE" jdbcType="VARCHAR" property="ownerCode"/>
        <result column="OWNER_NAME" jdbcType="VARCHAR" property="ownerName"/>
        <result column="PACK_NUM" jdbcType="DECIMAL" property="packNum"/>
        <result column="PARTENER_ID" jdbcType="VARCHAR" property="partenerId"/>
        <result column="PRINT_DATE" jdbcType="TIMESTAMP" property="printDate"/>
        <result column="PRE_ENTRY_NO" jdbcType="VARCHAR" property="preEntryNo"/>
        <result column="RISK" jdbcType="VARCHAR" property="risk"/>
        <result column="SEQ_NO" jdbcType="VARCHAR" property="seqNo"/>
        <result column="TGD_NO" jdbcType="VARCHAR" property="tgdNo"/>
        <result column="TRADE_COUNTRY" jdbcType="VARCHAR" property="tradeCountry"/>
        <result column="TRADE_MODE" jdbcType="VARCHAR" property="tradeMode"/>
        <result column="TRADE_CODE" jdbcType="VARCHAR" property="tradeCode"/>
        <result column="TRAF_MODE" jdbcType="VARCHAR" property="trafMode"/>
        <result column="TRAF_NAME" jdbcType="VARCHAR" property="trafName"/>
        <result column="TRADE_NAME" jdbcType="VARCHAR" property="tradeName"/>
        <result column="TRANS_MODE" jdbcType="VARCHAR" property="transMode"/>
        <result column="LIST_TYPE" jdbcType="VARCHAR" property="listType"/>
        <result column="TYPIST_NO" jdbcType="VARCHAR" property="typistNo"/>
        <result column="WRAP_TYPE" jdbcType="VARCHAR" property="wrapType"/>
        <result column="CHK_SURETY" jdbcType="VARCHAR" property="chkSurety"/>
        <result column="BILL_TYPE" jdbcType="VARCHAR" property="billType"/>
        <result column="INPUT_CREDIT_CODE" jdbcType="VARCHAR" property="inputCreditCode"/>
        <result column="TRADE_CREDIT_CODE" jdbcType="VARCHAR" property="tradeCreditCode"/>
        <result column="DECLARE_CREDIT_CODE" jdbcType="VARCHAR" property="declareCreditCode"/>
        <result column="OWNER_CREDIT_CODE" jdbcType="VARCHAR" property="ownerCreditCode"/>
        <result column="PROMISE_ITEMS" jdbcType="VARCHAR" property="promiseItems"/>
        <result column="TRADE_NATION" jdbcType="VARCHAR" property="tradeNation"/>
        <result column="CHECK_FLOW" jdbcType="VARCHAR" property="checkFlow"/>
        <result column="TAX_AAMIN_MARK" jdbcType="VARCHAR" property="taxAaminMark"/>
        <result column="MARK_NO" jdbcType="VARCHAR" property="markNo"/>
        <result column="DESP_PORT" jdbcType="VARCHAR" property="despPort"/>
        <result column="ENTRY_PORT" jdbcType="VARCHAR" property="entryPort"/>
        <result column="WAREHOUSE" jdbcType="VARCHAR" property="warehouse"/>
        <result column="BL_NO" jdbcType="VARCHAR" property="blNo"/>
        <result column="INSPORG_CODE" jdbcType="VARCHAR" property="insporgCode"/>
        <result column="SPECDECL_FLAG" jdbcType="VARCHAR" property="specdeclFlag"/>
        <result column="PURPORG_CODE" jdbcType="VARCHAR" property="purporgCode"/>
        <result column="DESP_DATE" jdbcType="VARCHAR" property="despDate"/>
        <result column="CMPLDSCHRG_DT" jdbcType="VARCHAR" property="cmpldschrgDt"/>
        <result column="CORRELATION_REASONFLAG" jdbcType="VARCHAR" property="correlationReasonflag"/>
        <result column="VSAORG_CODE" jdbcType="VARCHAR" property="vsaorgCode"/>
        <result column="ORIGBOX_FLAG" jdbcType="VARCHAR" property="origboxFlag"/>
        <result column="DECLARER_NAME" jdbcType="VARCHAR" property="declarerName"/>
        <result column="NOOTHER_PACK" jdbcType="VARCHAR" property="nootherPack"/>
        <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode"/>
        <result column="OVERSEAS_SHIPPER" jdbcType="VARCHAR" property="overseasShipper"/>
        <result column="OVERSEAS_SHIPPER_NAME" jdbcType="VARCHAR" property="overseasShipperName"/>
        <result column="OVERSEAS_SHIPPER_ENAME" jdbcType="VARCHAR" property="overseasShipperEname"/>
        <result column="OVERSEAS_SHIPPER_ADDR" jdbcType="VARCHAR" property="overseasShipperAddr"/>
        <result column="OVERSEASCONSIGNEE_CODE" jdbcType="VARCHAR" property="overseasconsigneeCode"/>
        <result column="OVERSEASCONSIGNEE_ENAME" jdbcType="VARCHAR" property="overseasconsigneeEname"/>
        <result column="TRADE_ENAME" jdbcType="VARCHAR" property="tradeEname"/>
        <result column="CORRELATION_NO" jdbcType="VARCHAR" property="correlationNo"/>
        <result column="EDI_REMARK" jdbcType="VARCHAR" property="ediRemark"/>
        <result column="EDI_REMARK2" jdbcType="VARCHAR" property="ediRemark2"/>
        <result column="TRADE_CIQ_CODE" jdbcType="VARCHAR" property="tradeCiqCode"/>
        <result column="OWNER_CIQ_CODE" jdbcType="VARCHAR" property="ownerCiqCode"/>
        <result column="DECL_CIQ_CODE" jdbcType="VARCHAR" property="declCiqCode"/>
        <result column="D_DATE" jdbcType="VARCHAR" property="DDate"/>
        <result column="VOYAGE_NO" jdbcType="VARCHAR" property="voyageNo"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="INSERT_USER" jdbcType="VARCHAR" property="insertUser"/>
        <result column="INSERT_TIME" jdbcType="TIMESTAMP" property="insertTime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="HAWB" jdbcType="VARCHAR" property="hawb"/>
        <result column="ERP_HEAD_ID" jdbcType="VARCHAR" property="erpHeadId"/>
        <result column="BOND_MARK" jdbcType="VARCHAR" property="bondMark"/>
        <result column="MASTER_CUSTOMS_NAME" jdbcType="VARCHAR" property="masterCustomsName"/>
        <result column="DUTY_MODE_NAME" jdbcType="VARCHAR" property="dutyModeName"/>
        <result column="DEST_PORT_NAME" jdbcType="VARCHAR" property="destPortName"/>
        <result column="I_E_PORT_NAME" jdbcType="VARCHAR" property="iEPortName"/>
        <result column="TRADE_COUNTRY_NAME" jdbcType="VARCHAR" property="tradeCountryName"/>
        <result column="TRADE_MODE_NAME" jdbcType="VARCHAR" property="tradeModeName"/>
        <result column="TRAF_MODE_NAME" jdbcType="VARCHAR" property="trafModeName"/>
        <result column="TRANS_MODE_NAME" jdbcType="VARCHAR" property="transModeName"/>
        <result column="WRAP_TYPE_NAME" jdbcType="VARCHAR" property="wrapTypeName"/>
        <result column="TRADE_NATION_NAME" jdbcType="VARCHAR" property="tradeNationName"/>
        <result column="DESP_PORT_NAME" jdbcType="VARCHAR" property="despPortName"/>
        <result column="ENTRY_PORT_NAME" jdbcType="VARCHAR" property="entryPortName"/>
        <result column="ITME1" jdbcType="VARCHAR" property="item1"/>
        <result column="ITME2" jdbcType="VARCHAR" property="item2"/>
        <result column="ITME3" jdbcType="VARCHAR" property="item3"/>
        <result column="ITME4" jdbcType="VARCHAR" property="item4"/>
        <result column="ITME5" jdbcType="VARCHAR" property="item5"/>
        <result column="ITME6" jdbcType="VARCHAR" property="item6"/>
        <result column="FEE_ALL" jdbcType="VARCHAR" property="feeAll"/>
        <result column="INSUR_ALL" jdbcType="VARCHAR" property="insurAll"/>
        <result column="OTHER_ALL" jdbcType="VARCHAR" property="otherAll"/>
        <result column="DCR_CYCLE" jdbcType="VARCHAR" property="dcrCycle"/>
        <result column="LJQ_NO" jdbcType="VARCHAR" property="ljqNo"/>
        <result column="SEND_API_STATUS" jdbcType="VARCHAR" property="sendApiStatus"/>
        <result column="SEND_API_TASK_ID" jdbcType="VARCHAR" property="sendApiTaskId"/>
        <result column="TOTAL_ALL" jdbcType="VARCHAR" property="totalAll"/>
        <result column="NET_WT_ALL" jdbcType="VARCHAR" property="netWtAll"/>
        <result column="QTYL_ALL" jdbcType="VARCHAR" property="qtyAll"/>
        <result column="DEC_TOTAL_ALL" jdbcType="VARCHAR" property="decTotalAll"/>
        <result column="CURR_ALL" jdbcType="VARCHAR" property="currAll"/>
        <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="ERP_INSERT_TIME" jdbcType="TIMESTAMP" property="erpInsertTime"/>
        <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName"/>
        <result column="G_NAME" jdbcType="VARCHAR" property="GName"/>
        <result column="WRAP_TYPE2" jdbcType="VARCHAR" property="wrapType2"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="ENTRY_STATUS" property="entryStatus" jdbcType="VARCHAR"/>
        <result column="ACCESS_TOKEN" property="accessToken" jdbcType="VARCHAR"/>
        <result column="ENTRY_STATUS_NAME" property="entryStatusName" jdbcType="VARCHAR"/>
        <result column="APPR_STATUS" property="apprStatus" jdbcType="VARCHAR"/>
        <result column="SEND_API_TIME" jdbcType="DATE" property="sendApiTime"/>
        <result column="ENTRY_DECLARE_DATE" property="entryDeclareDate" jdbcType="VARCHAR"/>
        <result column="CARD_MARK" property="cardMark" jdbcType="VARCHAR"/>
        <result column="CHECK_MARK" property="checkMark" jdbcType="VARCHAR"/>
        <result column="TAX_MARK" property="taxMark" jdbcType="VARCHAR"/>
        <result column="SEND_DEST" property="sendDest" jdbcType="VARCHAR"/>
        <result column="PREVENTIVE_DISINFECTION" property="preventiveDisinfection" jdbcType="VARCHAR"/>
        <result column="dec_personnel" property="decPersonnel" jdbcType="VARCHAR"/>
        <result column="dec_personnel_tel" property="decPersonnelTel" jdbcType="VARCHAR"/>
        <result column="DHL_DUTY_MODE" property="dhlDutyMode" jdbcType="VARCHAR"/>
        <result column="DESTINATION_CUSTOMS" property="destinationCustoms" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        t.SID
        ,t.COP_NO
        ,t.DECLARE_CODE
        ,t.DECLARE_NAME
        ,t.APPR_NO
        ,t.EMS_LIST_NO
        ,t.CONTR_NO
        ,t.INPUT_CODE
        ,t.INPUT_NAME
        ,t.MASTER_CUSTOMS
        ,t.CUT_MODE
        ,t.DATA_SOURCE
        ,t.DECL_TRNREL
        ,t.DEST_PORT
        ,t.DCLCUS_MARK
        ,t.ENTRY_NO
        ,t.ENTRY_TYPE
        ,t.FEE_CURR
        ,t.FEE_MARK
        ,t.FEE_RATE
        ,t.GROSS_WT
        ,t.I_E_DATE
        ,t.I_E_MARK
        ,t.I_E_PORT
        ,t.INPUTER_NAME
        ,t.INSUR_CURR
        ,t.INSUR_MARK
        ,t.INSUR_RATE
        ,t.LICENSE_NO
        ,t.EMS_NO
        ,t.NET_WT
        ,t.NOTE
        ,t.OTHER_CURR
        ,t.OTHER_MARK
        ,t.OTHER_RATE
        ,t.OWNER_CODE
        ,t.OWNER_NAME
        ,t.PACK_NUM
        ,t.PARTENER_ID
        ,t.PRINT_DATE
        ,t.PRE_ENTRY_NO
        ,t.RISK
        ,t.SEQ_NO
        ,t.TGD_NO
        ,t.TRADE_COUNTRY
        ,t.TRADE_MODE
        ,t.TRADE_CODE
        ,t.TRAF_MODE
        ,t.TRAF_NAME
        ,t.TRADE_NAME
        ,t.TRANS_MODE
        ,t.LIST_TYPE
        ,t.TYPIST_NO
        ,t.WRAP_TYPE
        ,t.CHK_SURETY
        ,t.BILL_TYPE
        ,t.INPUT_CREDIT_CODE
        ,t.TRADE_CREDIT_CODE
        ,t.DECLARE_CREDIT_CODE
        ,t.OWNER_CREDIT_CODE
        ,t.PROMISE_ITEMS
        ,t.TRADE_NATION
        ,t.CHECK_FLOW
        ,t.TAX_AAMIN_MARK
        ,t.MARK_NO
        ,t.DESP_PORT
        ,t.ENTRY_PORT
        ,t.WAREHOUSE
        ,t.BL_NO
        ,t.INSPORG_CODE
        ,t.SPECDECL_FLAG
        ,t.PURPORG_CODE
        ,t.DESP_DATE
        ,t.CMPLDSCHRG_DT
        ,t.CORRELATION_REASONFLAG
        ,t.VSAORG_CODE
        ,t.ORIGBOX_FLAG
        ,t.DECLARER_NAME
        ,t.NOOTHER_PACK
        ,t.ORG_CODE
        ,H.OVERSEAS_SHIPPER
        ,H.OVERSEAS_SHIPPER_NAME
        ,t.OVERSEAS_SHIPPER_ENAME
        ,t.OVERSEAS_SHIPPER_ADDR
        ,t.OVERSEASCONSIGNEE_CODE
        ,t.OVERSEASCONSIGNEE_ENAME
        ,t.TRADE_ENAME
        ,t.CORRELATION_NO
        ,t.EDI_REMARK
        ,t.EDI_REMARK2
        ,t.TRADE_CIQ_CODE
        ,t.OWNER_CIQ_CODE
        ,t.DECL_CIQ_CODE
        ,t.D_DATE
        ,t.VOYAGE_NO
        ,t.STATUS
        ,H.INSERT_USER
        ,t.INSERT_TIME
        ,t.UPDATE_USER
        ,t.UPDATE_TIME
        ,t.HAWB
        ,t.ERP_HEAD_ID
        ,t.BOND_MARK
        ,t.DCR_CYCLE
        ,t.LJQ_NO
        ,CASE t.BOND_MARK WHEN '0' THEN '' ELSE t.SEND_API_STATUS END  AS SEND_API_STATUS
        ,t.SEND_API_TASK_ID
        ,t.SEND_API_ERROR_MARK
        ,t.CARD_MARK
        ,t.CHECK_MARK
        ,t.TAX_MARK
        ,H.INVOICE_NO
        <if test='_databaseId == "postgresql" '>
            ,H.INSERT_TIME::timestamp(0)as ERP_INSERT_TIME
        </if>
        <if test='_databaseId != "postgresql" '>
            ,H.INSERT_TIME as ERP_INSERT_TIME
        </if>
        ,LIST.QTYL_ALL
        ,LIST.DEC_TOTAL_ALL
        ,LIST.CURR_ALL
        ,LIST.G_NAME
        ,H.INSERT_USER_NAME
        ,t.ENTRY_STATUS
        ,b.PARAMS_NAME as ENTRY_STATUS_NAME
        ,H.APPR_STATUS
        ,BILL_H.ENTRY_DECLARE_DATE
        ,t.SEND_DEST
        ,t.START_SHIPMENT_DATE
        ,T.PREVENTIVE_DISINFECTION
        ,T.SEND_ENTRY_DATE
        ,T.THIRD_DECLARE_CHANNEL
        ,T.DHL_DUTY_MODE
        ,T.DESTINATION_CUSTOMS
    </sql>
    <sql id="condition">
        <if test="isUpload != null and isUpload != ''">
            <choose>
                <when test='isUpload == "0"'>
                    and (t.is_upload = #{isUpload} or t.is_upload is null or t.is_upload = '')
                </when>
                <otherwise >
                    and t.is_upload = #{isUpload}
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="approvalStatus == '0'.toString()">
                and exists (select h.ems_list_no from t_gwstd_entry_head_third h left join t_gwstd_entry_head_third_ext e on h.sid=e.sid where (approval_status=#{approvalStatus} or approval_status is null) and h.ems_list_no=t.ems_list_no and h.trade_code=#{tradeCode,jdbcType=VARCHAR})
            </when>
            <otherwise>
                <if test="approvalStatus != null and approvalStatus != ''">
                    and exists (select h.ems_list_no from t_gwstd_entry_head_third h left join t_gwstd_entry_head_third_ext e on h.sid=e.sid where approval_status=#{approvalStatus} and h.ems_list_no=t.ems_list_no and h.trade_code=#{tradeCode,jdbcType=VARCHAR})
                </if>
            </otherwise>
        </choose>
        <if test="insertUserParam != null and insertUserParam != ''">
            and (H.INSERT_USER like concat(concat('%',#{insertUserParam}),'%') or H.INSERT_USER_NAME like concat(concat('%',#{insertUserParam}),'%'))
        </if>
        <if test="insertUserName != null and insertUserName != ''">
            and H.INSERT_USER_NAME like concat(concat('%',#{insertUserName}),'%')
        </if>
        <if test="copNo != null and copNo != ''">
            and t.COP_NO = #{copNo}
        </if>
        <if test="declareCode != null and declareCode != ''">
            and t.DECLARE_CODE = #{declareCode}
        </if>
        <choose>
            <when test="agentAuthType == 'Y'.toString()">
                and (t.DECLARE_CODE = #{agentDeclareCode} or H.FORWARD_CODE = #{agentForwardCode})
            </when>
            <otherwise>
                <if test="agentDeclareCode != null and agentDeclareCode != ''">
                    and t.DECLARE_CODE = #{agentDeclareCode}
                </if>
                <if test="agentForwardCode != null and agentForwardCode != ''">
                    and H.FORWARD_CODE = #{agentForwardCode}
                </if>
            </otherwise>
        </choose>
        <if test="declareName != null and declareName != ''">
            and t.DECLARE_NAME = #{declareName}
        </if>
        <if test="apprNo != null and apprNo != ''">
            and t.APPR_NO = #{apprNo}
        </if>
        <if test="emsListNo != null and emsListNo != ''">
            and t.EMS_LIST_NO like concat(concat('%',#{emsListNo}),'%')
        </if>
        <if test="contrNo != null and contrNo != ''">
            and t.CONTR_NO like concat(concat('%',#{contrNo}),'%')
        </if>
        <if test="inputCode != null and inputCode != ''">
            and t.INPUT_CODE = #{inputCode}
        </if>
        <if test="inputName != null and inputName != ''">
            and t.INPUT_NAME = #{inputName}
        </if>
        <if test="masterCustoms != null and masterCustoms != ''">
            and t.MASTER_CUSTOMS = #{masterCustoms}
        </if>
        <if test="cutMode != null and cutMode != ''">
            and t.CUT_MODE = #{cutMode}
        </if>
        <choose>
            <when test="dataSource=='0'.toString()">
                and THIRD.NEW_DATA_SOURCE is null
            </when>
            <otherwise>
                <if test="dataSource != null and dataSource != ''">
                    and THIRD.NEW_DATA_SOURCE = #{dataSource}
                </if>
            </otherwise>
        </choose>
        <if test="declTrnrel != null and declTrnrel != ''">
            and t.DECL_TRNREL = #{declTrnrel}
        </if>
        <if test="destPort != null and destPort != ''">
            and t.DEST_PORT = #{destPort}
        </if>
        <if test="dclcusMark != null and dclcusMark != ''">
            and t.DCLCUS_MARK = #{dclcusMark}
        </if>
        <if test="entryNo != null and entryNo != ''">
            and t.ENTRY_NO like '%'|| #{entryNo} || '%'
        </if>
        <if test="entryType != null and entryType != ''">
            and t.ENTRY_TYPE = #{entryType}
        </if>
        <if test="feeCurr != null and feeCurr != ''">
            and t.FEE_CURR = #{feeCurr}
        </if>
        <if test="feeMark != null and feeMark != ''">
            and t.FEE_MARK = #{feeMark}
        </if>
        <if test="feeRate != null and feeRate != ''">
            and t.FEE_RATE = #{feeRate}
        </if>
        <if test="grossWt != null and grossWt != ''">
            and t.GROSS_WT = #{grossWt}
        </if>
        <if test="IEMark != null and IEMark != ''">
            and t.I_E_MARK = #{IEMark}
        </if>
        <if test="IEPort != null and IEPort != ''">
            and t.I_E_PORT like '%'|| #{IEPort} || '%'
        </if>
        <if test="inputerName != null and inputerName != ''">
            and t.INPUTER_NAME = #{inputerName}
        </if>
        <if test="insurCurr != null and insurCurr != ''">
            and t.INSUR_CURR = #{insurCurr}
        </if>
        <if test="insurMark != null and insurMark != ''">
            and t.INSUR_MARK = #{insurMark}
        </if>
        <if test="insurRate != null and insurRate != ''">
            and t.INSUR_RATE = #{insurRate}
        </if>
        <if test="licenseNo != null and licenseNo != ''">
            and t.LICENSE_NO like '%'|| #{licenseNo} || '%'
        </if>
        <if test="emsNo != null and emsNo != ''">
            and t.EMS_NO = #{emsNo}
        </if>
        <if test="netWt != null and netWt != ''">
            and t.NET_WT = #{netWt}
        </if>
        <if test="note != null and note != ''">
            and t.NOTE = #{note}
        </if>
        <if test="otherCurr != null and otherCurr != ''">
            and t.OTHER_CURR = #{otherCurr}
        </if>
        <if test="otherMark != null and otherMark != ''">
            and t.OTHER_MARK = #{otherMark}
        </if>
        <if test="otherRate != null and otherRate != ''">
            and t.OTHER_RATE = #{otherRate}
        </if>
        <if test="ownerCode != null and ownerCode != ''">
            and t.OWNER_CODE = #{ownerCode}
        </if>
        <if test="ownerName != null and ownerName != ''">
            and t.OWNER_NAME = #{ownerName}
        </if>
        <if test="packNum != null and packNum != ''">
            and t.PACK_NUM = #{packNum}
        </if>
        <if test="partenerId != null and partenerId != ''">
            and t.PARTENER_ID = #{partenerId}
        </if>
        <if test="preEntryNo != null and preEntryNo != ''">
            and t.PRE_ENTRY_NO = #{preEntryNo}
        </if>
        <if test="risk != null and risk != ''">
            and t.RISK = #{risk}
        </if>
        <if test="seqNo != null and seqNo != ''">
            and t.SEQ_NO = #{seqNo}
        </if>
        <if test="tgdNo != null and tgdNo != ''">
            and t.TGD_NO = #{tgdNo}
        </if>
        <if test="tradeCountry != null and tradeCountry != ''">
            and t.TRADE_COUNTRY = #{tradeCountry}
        </if>
        <if test="tradeMode != null and tradeMode != ''">
            and t.TRADE_MODE = #{tradeMode}
        </if>
        and t.TRADE_CODE = #{tradeCode}
        <if test="trafMode != null and trafMode != ''">
            and t.TRAF_MODE = #{trafMode}
        </if>
        <if test="trafName != null and trafName != ''">
            and t.TRAF_NAME like '%'|| #{trafName} || '%'
        </if>
        <if test="tradeName != null and tradeName != ''">
            and t.TRADE_NAME like '%'|| #{tradeName} || '%'
        </if>
        <if test="transMode != null and transMode != ''">
            and t.TRANS_MODE = #{transMode}
        </if>
        <if test="listType != null and listType != ''">
            and t.LIST_TYPE = #{listType}
        </if>
        <if test="typistNo != null and typistNo != ''">
            and t.TYPIST_NO = #{typistNo}
        </if>
        <if test="wrapType != null and wrapType != ''">
            and t.WRAP_TYPE = #{wrapType}
        </if>
        <if test="chkSurety != null and chkSurety != ''">
            and t.CHK_SURETY = #{chkSurety}
        </if>
        <if test="billType != null and billType != ''">
            and t.BILL_TYPE = #{billType}
        </if>
        <if test="inputCreditCode != null and inputCreditCode != ''">
            and t.INPUT_CREDIT_CODE = #{inputCreditCode}
        </if>
        <if test="tradeCreditCode != null and tradeCreditCode != ''">
            and t.TRADE_CREDIT_CODE = #{tradeCreditCode}
        </if>
        <if test="declareCreditCode != null and declareCreditCode != ''">
            and t.DECLARE_CREDIT_CODE = #{declareCreditCode}
        </if>
        <if test="ownerCreditCode != null and ownerCreditCode != ''">
            and t.OWNER_CREDIT_CODE = #{ownerCreditCode}
        </if>
        <if test="promiseItems != null and promiseItems != ''">
            and t.PROMISE_ITEMS = #{promiseItems}
        </if>
        <if test="tradeNation != null and tradeNation != ''">
            and t.TRADE_NATION = #{tradeNation}
        </if>
        <if test="checkFlow != null and checkFlow != ''">
            and t.CHECK_FLOW = #{checkFlow}
        </if>
        <if test="taxAaminMark != null and taxAaminMark != ''">
            and t.TAX_AAMIN_MARK = #{taxAaminMark}
        </if>
        <if test="markNo != null and markNo != ''">
            and t.MARK_NO = #{markNo}
        </if>
        <if test="despPort != null and despPort != ''">
            and t.DESP_PORT = #{despPort}
        </if>
        <if test="entryPort != null and entryPort != ''">
            and t.ENTRY_PORT = #{entryPort}
        </if>
        <if test="warehouse != null and warehouse != ''">
            and t.WAREHOUSE = #{warehouse}
        </if>
        <if test="blNo != null and blNo != ''">
            and t.BL_NO = #{blNo}
        </if>
        <if test="insporgCode != null and insporgCode != ''">
            and t.INSPORG_CODE = #{insporgCode}
        </if>
        <if test="specdeclFlag != null and specdeclFlag != ''">
            and t.SPECDECL_FLAG = #{specdeclFlag}
        </if>
        <if test="purporgCode != null and purporgCode != ''">
            and t.PURPORG_CODE = #{purporgCode}
        </if>
        <if test="despDate != null and despDate != ''">
            and t.DESP_DATE = #{despDate}
        </if>
        <if test="cmpldschrgDt != null and cmpldschrgDt != ''">
            and t.CMPLDSCHRG_DT = #{cmpldschrgDt}
        </if>
        <if test="correlationReasonflag != null and correlationReasonflag != ''">
            and t.CORRELATION_REASONFLAG = #{correlationReasonflag}
        </if>
        <if test="vsaorgCode != null and vsaorgCode != ''">
            and t.VSAORG_CODE = #{vsaorgCode}
        </if>
        <if test="origboxFlag != null and origboxFlag != ''">
            and t.ORIGBOX_FLAG = #{origboxFlag}
        </if>
        <if test="declarerName != null and declarerName != ''">
            and t.DECLARER_NAME = #{declarerName}
        </if>
        <if test="nootherPack != null and nootherPack != ''">
            and t.NOOTHER_PACK = #{nootherPack}
        </if>
        <if test="orgCode != null and orgCode != ''">
            and t.ORG_CODE = #{orgCode}
        </if>
        <if test="overseasShipper != null and overseasShipper != ''">
            and H.OVERSEAS_SHIPPER = #{overseasShipper}
        </if>
        <if test="overseasShipperName != null and overseasShipperName != ''">
            and H.OVERSEAS_SHIPPER_NAME like '%'|| #{overseasShipperName} || '%'
        </if>
        <if test="overseasShipperEname != null and overseasShipperEname != ''">
            and t.OVERSEAS_SHIPPER_ENAME like '%'|| #{overseasShipperEname} || '%'
        </if>
        <if test="overseasShipperAddr != null and overseasShipperAddr != ''">
            and t.OVERSEAS_SHIPPER_ADDR = #{overseasShipperAddr}
        </if>
        <if test="overseasconsigneeCode != null and overseasconsigneeCode != ''">
            and t.OVERSEASCONSIGNEE_CODE = #{overseasconsigneeCode}
        </if>
        <if test="overseasconsigneeEname != null and overseasconsigneeEname != ''">
            and t.OVERSEASCONSIGNEE_ENAME = #{overseasconsigneeEname}
        </if>
        <if test="tradeEname != null and tradeEname != ''">
            and t.TRADE_ENAME = #{tradeEname}
        </if>
        <if test="correlationNo != null and correlationNo != ''">
            and t.CORRELATION_NO = #{correlationNo}
        </if>
        <if test="ediRemark != null and ediRemark != ''">
            and t.EDI_REMARK = #{ediRemark}
        </if>
        <if test="ediRemark2 != null and ediRemark2 != ''">
            and t.EDI_REMARK2 = #{ediRemark2}
        </if>
        <if test="tradeCiqCode != null and tradeCiqCode != ''">
            and t.TRADE_CIQ_CODE = #{tradeCiqCode}
        </if>
        <if test="ownerCiqCode != null and ownerCiqCode != ''">
            and t.OWNER_CIQ_CODE = #{ownerCiqCode}
        </if>
        <if test="declCiqCode != null and declCiqCode != ''">
            and t.DECL_CIQ_CODE = #{declCiqCode}
        </if>
        <if test="voyageNo != null and voyageNo != ''">
            and t.VOYAGE_NO = #{voyageNo}
        </if>
        <if test="status != null and status != ''">
            and t.STATUS = #{status}
        </if>
        <if test="hawb != null and hawb != ''">
            and t.HAWB like '%'|| #{hawb} || '%'
        </if>
        <if test="erpHeadId != null and erpHeadId != ''">
            and t.ERP_HEAD_ID = #{erpHeadId}
        </if>
        <if test="bondMark != null and bondMark != ''">
            and t.BOND_MARK = #{bondMark}
        </if>
        <if test="dcrCycle != null and dcrCycle != ''">
            and t.DCR_CYCLE = #{dcrCycle}
        </if>
        <if test="ljqNo != null and ljqNo != ''">
            and t.LJQ_NO = #{ljqNo}
        </if>
        <if test="sendApiStatus != null and sendApiStatus != ''">
            and t.SEND_API_STATUS = #{sendApiStatus} and t.BOND_MARK = '1'
        </if>
        <if test="entryStatus != null and entryStatus != ''">
            and t.ENTRY_STATUS = #{entryStatus}
        </if>
        <if test="apprStatus != null and apprStatus != ''">
            and h.APPR_STATUS = #{apprStatus}
        </if>
        <if test="insertUser != null and insertUser!=''">
            and t.INSERT_USER in
            <foreach item="item" index="index" collection="insertUser.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <sql id="conditionORACLE">
        <if test="iEDateFrom != null and iEDateFrom != ''">
            <![CDATA[ and t.I_E_DATE >= to_date(#{iEDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="iEDateTo != null and iEDateTo != ''">
            <![CDATA[ and t.I_E_DATE < to_date(#{iEDateTo}, 'yyyy-MM-dd hh24:mi:ss') + 1 ]]>
        </if>
        <if test="dDateFrom != null and dDateFrom != ''">
            <![CDATA[ and BILL_H.ENTRY_DECLARE_DATE >= to_date(#{dDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="dDateTo != null and dDateTo != ''">
            <![CDATA[ and BILL_H.ENTRY_DECLARE_DATE < to_date(#{dDateTo}, 'yyyy-MM-dd hh24:mi:ss') + 1 ]]>
        </if>
        <if test="erpInsertTimeFrom != null and erpInsertTimeFrom != ''">
            <![CDATA[ and H.INSERT_TIME >= to_date(#{erpInsertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="erpInsertTimeTo != null and erpInsertTimeTo != ''">
            <![CDATA[ and H.INSERT_TIME < to_date(#{erpInsertTimeTo}, 'yyyy-MM-dd hh24:mi:ss') + 1 ]]>
        </if>
        <if test="currAll != null and currAll != ''">
            and ( SELECT wm_concat ( DISTINCT L.CURR ) FROM T_DEC_I_ENTRY_LIST L WHERE L.HEAD_ID = t.SID ) like '%'|| #{currAll} || '%'
        </if>
    </sql>
    <sql id="conditionPG">
        <if test="iEDateFrom != null and iEDateFrom != ''">
            <![CDATA[ and t.I_E_DATE >= to_timestamp(#{iEDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="iEDateTo != null and iEDateTo != ''">
            <![CDATA[ and t.I_E_DATE < to_timestamp(#{iEDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="dDateFrom != null and dDateFrom != ''">
            <![CDATA[ and BILL_H.ENTRY_DECLARE_DATE >= to_timestamp(#{dDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="dDateTo != null and dDateTo != ''">
            <![CDATA[ and BILL_H.ENTRY_DECLARE_DATE < to_timestamp(#{dDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="erpInsertTimeFrom != null and erpInsertTimeFrom != ''">
            <![CDATA[ and H.INSERT_TIME >= to_timestamp(#{erpInsertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="erpInsertTimeTo != null and erpInsertTimeTo != ''">
            <![CDATA[ and H.INSERT_TIME < to_timestamp(#{erpInsertTimeTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="currAll != null and currAll != ''">
            and ( SELECT string_agg(distinct L.CURR,',') FROM T_DEC_I_ENTRY_LIST L WHERE L.HEAD_ID = t.SID ) like '%'|| #{currAll} || '%'
        </if>
    </sql>
    <!-- 标准表头、表体复杂逻辑查询功能-->
    <select id="getListAll" resultMap="tDecIEntryHeadResultMap" parameterType="com.dcjet.cs.entry.model.DecIEntryHead">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_DEC_I_ENTRY_HEAD t LEFT JOIN T_DEC_ERP_I_HEAD_N H ON h.SID = t.ERP_HEAD_ID
        left join (select head_id,
        max(L.curr)AS CURR_ALL,
        max(L.g_name)as G_NAME,
        SUM(L.QTY) as QTYL_ALL,
        SUM(L.DEC_TOTAL)as DEC_TOTAL_ALL
        from T_DEC_I_ENTRY_LIST L  inner join T_DEC_I_ENTRY_HEAD tt on tt.sid=l.head_id where tt.trade_code=#{tradeCode,jdbcType=VARCHAR}  group by head_id ) LIST on T.SID=LIST.HEAD_ID
        left join T_DEC_I_BILL_HEAD BILL_H on BILL_H.SID=t.BILL_HEAD_ID
        LEFT JOIN T_BI_CUSTOMER_PARAMS b ON t.ENTRY_STATUS = b.PARAMS_CODE
        AND b.PARAMS_TYPE = 'ENTRY_STATUS'
        <where>
            <include refid="condition"></include>
            <if test='_databaseId == "postgresql" '>
                <include refid="conditionPG"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="conditionORACLE"></include>
            </if>
        </where>
        order by H.INSERT_TIME desc,H.SID
    </select>
    <!-- 报关单复核查询功能-->
    <select id="getListDiff" resultMap="tDecIEntryHeadResultMap" parameterType="com.dcjet.cs.entry.model.DecIEntryHead">
        SELECT
        t.SID
        ,t.COP_NO
        ,t.DECLARE_CODE
        ,t.DECLARE_NAME
        ,t.APPR_NO
        ,t.EMS_LIST_NO
        ,t.CONTR_NO
        ,t.INPUT_CODE
        ,t.INPUT_NAME
        ,t.MASTER_CUSTOMS
        ,t.CUT_MODE
        ,THIRD.NEW_DATA_SOURCE DATA_SOURCE
        ,t.DECL_TRNREL
        ,t.DEST_PORT
        ,t.DCLCUS_MARK
        ,t.ENTRY_NO
        ,t.ENTRY_TYPE
        ,t.FEE_CURR
        ,t.FEE_MARK
        ,t.FEE_RATE
        ,t.GROSS_WT
        ,t.I_E_DATE
        ,t.I_E_MARK
        ,t.I_E_PORT
        ,t.INPUTER_NAME
        ,t.INSUR_CURR
        ,t.INSUR_MARK
        ,t.INSUR_RATE
        ,t.LICENSE_NO
        ,t.EMS_NO
        ,t.NET_WT
        ,t.NOTE
        ,t.OTHER_CURR
        ,t.OTHER_MARK
        ,t.OTHER_RATE
        ,t.OWNER_CODE
        ,t.OWNER_NAME
        ,t.PACK_NUM
        ,t.PARTENER_ID
        ,t.PRINT_DATE
        ,t.PRE_ENTRY_NO
        ,t.RISK
        ,t.SEQ_NO
        ,t.TGD_NO
        ,t.TRADE_COUNTRY
        ,t.TRADE_MODE
        ,t.TRADE_CODE
        ,t.TRAF_MODE
        ,t.TRAF_NAME
        ,t.TRADE_NAME
        ,t.TRANS_MODE
        ,t.LIST_TYPE
        ,t.TYPIST_NO
        ,t.WRAP_TYPE
        ,t.CHK_SURETY
        ,t.BILL_TYPE
        ,t.INPUT_CREDIT_CODE
        ,t.TRADE_CREDIT_CODE
        ,t.DECLARE_CREDIT_CODE
        ,t.OWNER_CREDIT_CODE
        ,t.PROMISE_ITEMS
        ,t.TRADE_NATION
        ,t.CHECK_FLOW
        ,t.TAX_AAMIN_MARK
        ,t.MARK_NO
        ,t.DESP_PORT
        ,t.ENTRY_PORT
        ,t.WAREHOUSE
        ,t.BL_NO
        ,t.INSPORG_CODE
        ,t.SPECDECL_FLAG
        ,t.PURPORG_CODE
        ,t.DESP_DATE
        ,t.CMPLDSCHRG_DT
        ,t.CORRELATION_REASONFLAG
        ,t.VSAORG_CODE
        ,t.ORIGBOX_FLAG
        ,t.DECLARER_NAME
        ,t.NOOTHER_PACK
        ,t.ORG_CODE
        ,H.OVERSEAS_SHIPPER
        ,H.OVERSEAS_SHIPPER_NAME
        ,t.OVERSEAS_SHIPPER_ENAME
        ,t.OVERSEAS_SHIPPER_ADDR
        ,t.OVERSEASCONSIGNEE_CODE
        ,t.OVERSEASCONSIGNEE_ENAME
        ,t.TRADE_ENAME
        ,t.CORRELATION_NO
        ,t.EDI_REMARK
        ,t.EDI_REMARK2
        ,t.TRADE_CIQ_CODE
        ,t.OWNER_CIQ_CODE
        ,t.DECL_CIQ_CODE
        ,t.D_DATE
        ,t.VOYAGE_NO
        ,t.STATUS
        ,H.INSERT_USER
        ,t.INSERT_TIME
        ,t.UPDATE_USER
        ,t.UPDATE_TIME
        ,t.HAWB
        ,t.ERP_HEAD_ID
        ,t.BOND_MARK
        ,t.DCR_CYCLE
        ,t.LJQ_NO
        ,CASE t.BOND_MARK WHEN '0' THEN '' ELSE t.SEND_API_STATUS END  AS SEND_API_STATUS
        ,t.SEND_API_TASK_ID
        ,t.SEND_API_ERROR_MARK
        ,t.CARD_MARK
        ,t.CHECK_MARK
        ,t.TAX_MARK
        ,H.INVOICE_NO
        <if test='_databaseId == "postgresql" '>
            ,H.INSERT_TIME::timestamp(0)as ERP_INSERT_TIME
        </if>
        <if test='_databaseId != "postgresql" '>
            ,H.INSERT_TIME as ERP_INSERT_TIME
        </if>
        ,H.INSERT_USER_NAME
        ,t.ENTRY_STATUS
        ,b.PARAMS_NAME as ENTRY_STATUS_NAME
        ,H.APPR_STATUS
        ,BILL_H.ENTRY_DECLARE_DATE
        ,t.SEND_DEST
        ,t.is_upload
        ,A.file_name as pathAddress
        FROM T_DEC_I_ENTRY_HEAD t
        LEFT JOIN T_DEC_ERP_I_HEAD_N H ON h.SID = t.ERP_HEAD_ID
        left join T_DEC_I_BILL_HEAD BILL_H on BILL_H.SID=t.BILL_HEAD_ID
        LEFT JOIN T_BI_CUSTOMER_PARAMS b ON t.ENTRY_STATUS = b.PARAMS_CODE AND b.PARAMS_TYPE = 'ENTRY_STATUS'
        LEFT JOIN (select case when DATA_SOURCE is null then '0' else DATA_SOURCE end NEW_DATA_SOURCE,G.* from T_GWSTD_ENTRY_HEAD_THIRD G) THIRD ON T.EMS_LIST_NO=THIRD.EMS_LIST_NO AND THIRD.I_E_MARK='I' AND T.TRADE_CODE=THIRD.TRADE_CODE
        LEFT JOIN t_attached A ON A.head_id = t.sid AND A.trade_code = t.trade_code
        <where>
            <include refid="condition"></include>
            <if test='_databaseId == "postgresql" '>
                <include refid="conditionPG"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="conditionORACLE"></include>
            </if>
        </where>
        order by H.INSERT_TIME desc,H.SID
    </select>
    <!-- 报关单表头简单查询功能-->
    <select id="getList" resultMap="tDecIEntryHeadResultMap" parameterType="com.dcjet.cs.entry.model.DecIEntryHead">
        SELECT
        t.SID
        ,t.COP_NO
        ,t.DECLARE_CODE
        ,t.DECLARE_NAME
        ,t.APPR_NO
        ,t.EMS_LIST_NO
        ,t.CONTR_NO
        ,t.INPUT_CODE
        ,t.INPUT_NAME
        ,t.MASTER_CUSTOMS
        ,t.CUT_MODE
        ,t.DATA_SOURCE
        ,t.DECL_TRNREL
        ,t.DEST_PORT
        ,t.DCLCUS_MARK
        ,t.ENTRY_NO
        ,t.ENTRY_TYPE
        ,t.FEE_CURR
        ,t.FEE_MARK
        ,t.FEE_RATE
        ,t.GROSS_WT
        ,t.I_E_DATE
        ,t.I_E_MARK
        ,t.I_E_PORT
        ,t.INPUTER_NAME
        ,t.INSUR_CURR
        ,t.INSUR_MARK
        ,t.INSUR_RATE
        ,t.LICENSE_NO
        ,t.EMS_NO
        ,t.NET_WT
        ,t.NOTE
        ,t.OTHER_CURR
        ,t.OTHER_MARK
        ,t.OTHER_RATE
        ,t.OWNER_CODE
        ,t.OWNER_NAME
        ,t.PACK_NUM
        ,t.PARTENER_ID
        ,t.PRINT_DATE
        ,t.PRE_ENTRY_NO
        ,t.RISK
        ,t.SEQ_NO
        ,t.TGD_NO
        ,t.TRADE_COUNTRY
        ,t.TRADE_MODE
        ,t.TRADE_CODE
        ,t.TRAF_MODE
        ,t.TRAF_NAME
        ,t.TRADE_NAME
        ,t.TRANS_MODE
        ,t.LIST_TYPE
        ,t.TYPIST_NO
        ,t.WRAP_TYPE
        ,t.WRAP_TYPE2
        ,t.CHK_SURETY
        ,t.BILL_TYPE
        ,t.INPUT_CREDIT_CODE
        ,t.TRADE_CREDIT_CODE
        ,t.DECLARE_CREDIT_CODE
        ,t.OWNER_CREDIT_CODE
        ,t.PROMISE_ITEMS
        ,t.TRADE_NATION
        ,t.CHECK_FLOW
        ,t.TAX_AAMIN_MARK
        ,t.MARK_NO
        ,t.DESP_PORT
        ,t.ENTRY_PORT
        ,t.WAREHOUSE
        ,t.BL_NO
        ,t.INSPORG_CODE
        ,t.SPECDECL_FLAG
        ,t.PURPORG_CODE
        ,t.DESP_DATE
        ,t.CMPLDSCHRG_DT
        ,t.CORRELATION_REASONFLAG
        ,t.VSAORG_CODE
        ,t.ORIGBOX_FLAG
        ,t.DECLARER_NAME
        ,t.NOOTHER_PACK
        ,t.ORG_CODE
        ,t.OVERSEAS_SHIPPER
        ,t.OVERSEAS_SHIPPER_NAME
        ,t.OVERSEAS_SHIPPER_ENAME
        ,t.OVERSEAS_SHIPPER_ADDR
        ,t.OVERSEASCONSIGNEE_CODE
        ,t.OVERSEASCONSIGNEE_ENAME
        ,t.TRADE_ENAME
        ,t.CORRELATION_NO
        ,t.EDI_REMARK
        ,t.EDI_REMARK2
        ,t.TRADE_CIQ_CODE
        ,t.OWNER_CIQ_CODE
        ,t.DECL_CIQ_CODE
        ,t.D_DATE
        ,t.VOYAGE_NO
        ,t.STATUS
        ,t.INSERT_USER
        ,t.INSERT_TIME
        ,t.UPDATE_USER
        ,t.UPDATE_TIME
        ,t.HAWB
        ,t.ERP_HEAD_ID
        ,t.BOND_MARK
        ,t.DCR_CYCLE
        ,t.LJQ_NO
        ,CASE t.BOND_MARK WHEN '0' THEN '' ELSE t.SEND_API_STATUS END  AS SEND_API_STATUS
        ,t.SEND_API_TASK_ID
        ,t.SEND_API_ERROR_MARK
        ,t.CARD_MARK
        ,t.CHECK_MARK
        ,t.TAX_MARK
        ,t.INSERT_USER_NAME
        ,t.ENTRY_STATUS
        ,t.SEND_DEST
        ,c.dec_personnel
        ,c.dec_personnel_tel
        FROM
        T_DEC_I_ENTRY_HEAD t
        left join t_dec_erp_i_head_n h on t.erp_head_id=h.sid
        LEFT JOIN t_bi_client_information C ON h.declare_code_customs = C.CUSTOMER_CODE and C.CUSTOMER_TYPE = 'CUT' AND T.trade_code = C.trade_code
        <where>
            and t.TRADE_CODE = #{tradeCode}
            <if test="seqNo != null and seqNo != ''">
                and t.SEQ_NO = #{seqNo}
            </if>
            <if test="emsListNo != null and emsListNo != ''">
                and t.EMS_LIST_NO = #{emsListNo}
            </if>
            <if test="erpHeadId != null and erpHeadId != ''">
                and t.ERP_HEAD_ID = #{erpHeadId}
            </if>
            <if test="sid != null and sid != ''">
                and t.SID = #{sid}
            </if>
            <if test="bondMark != null and bondMark!=''">
                and t.bond_mark =#{bondMark}
            </if>
        </where>
        order by t.INSERT_TIME desc,t.SID
    </select>
    <!-- 列表查询 and 条件 begin-->
    <select id="selectListSum" parameterType="com.dcjet.cs.entry.model.DecIEntryHead" resultMap="tDecIEntryHeadResultMap">
        select
        (select '申报数量:' || to_char(sum( L.QTY ),'fm999999999999999990.09999') || ' 申报总价:' || to_char(sum( L.DEC_TOTAL ),'fm999999999999999990.00999') || ' 法一数量:' ||
        to_char(sum(f_xdo_nvl( L.QTY_1, 0.0 )),'fm999999999999999990.09999') || ' 法二数量:' || to_char(sum(f_xdo_nvl( L.QTY_2, 0.0 )),'fm999999999999999990.09999')
        from T_DEC_I_ENTRY_LIST L where  t.SID=L.HEAD_ID ) as TOTAL_ALL,
        (select ' 净重:' ||to_char(f_xdo_nvl(sum(m.NET_WT),0.0),'fm999999999999999990.09999999') from T_DEC_I_BILL_List m where t.BILL_HEAD_ID=m.HEAD_ID)  as NET_WT_ALL
        from T_DEC_I_ENTRY_HEAD t
        <where>
            <if test="tradeCode != null and tradeCode!=''">
                and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
            </if>
            <if test="emsListNo != null and emsListNo!=''">
                and t.EMS_LIST_NO =#{emsListNo,jdbcType=VARCHAR}
            </if>
            <if test="erpHeadId != null and erpHeadId!=''">
                and t.ERP_HEAD_ID =#{erpHeadId,jdbcType=VARCHAR}
            </if>
            <if test="sid != null and sid!=''">
                and t.SID =#{sid,jdbcType=VARCHAR}
            </if>
            <if test="bondMark != null and bondMark!=''">
                and t.bond_mark = #{bondMark}
            </if>
        </where>
    </select>
    <select id="findByNotCost" resultType="java.util.Map">
      SELECT DISTINCT  t.SID as "SID", list.CURR as "CURR"
        FROM T_DEC_I_ENTRY_HEAD t LEFT JOIN T_DEC_I_ENTRY_LIST list ON t.SID = list.HEAD_ID
        WHERE t.TRADE_CODE = #{tradeCode} AND ENTRY_NO IS NOT NULL
        AND NOT EXISTS (SELECT SID FROM T_COST_EXCHANGE c WHERE c.ENTRY_HEAD_ID = t.SID AND c.TRADE_CODE = #{tradeCode} AND c.I_E_MARK = 'I')
    </select>
    <!-- 非保税（立交桥）报关单发送更新发送状态,发送后：清单表体、报关单表体、提单表体、清单归并表体、提单对应关系表需要置状态为1 不可修改-->
    <update id="updateHgNo">
        <if test='_databaseId != "postgresql" '>
            begin
        </if>
        UPDATE T_DEC_I_ENTRY_HEAD T SET
        <if test="sendApiStatus=='1'.toString()">
            ENTRY_STATUS='A0',
        </if>
        <if test="sendDest != null and sendDest!=''">
            SEND_DEST = #{sendDest},
        </if>
        LJQ_NO=#{ljqNo},
        <if test="sendApiStatus != null and sendApiStatus != ''">
            SEND_API_STATUS=#{sendApiStatus},
        </if>
        <if test="sendApiStatus=='1'.toString()">
            SEND_ENTRY_DATE = CURRENT_TIMESTAMP(3),
        </if>
        SEND_API_TASK_ID=#{taskId},
        ACCESS_TOKEN=#{accessToken},
        UPDATE_TIME = CURRENT_TIMESTAMP(3),
        SEND_API_TIME = CURRENT_TIMESTAMP(3),
        STATUS=#{status}
        WHERE T.SID =#{sid};
        UPDATE T_DEC_I_BILL_HEAD T SET STATUS=#{status} WHERE T.SID =(select E.BILL_HEAD_ID from T_DEC_I_ENTRY_HEAD E where E.SID =#{sid});
        UPDATE T_DEC_I_CUSTOMS_TRACK T SET STATUS=#{status} WHERE T.HEAD_ID =(select E.BILL_HEAD_ID from T_DEC_I_ENTRY_HEAD E where E.SID =#{sid});

        UPDATE T_DEC_ERP_I_LIST_N T SET STATUS=#{status} WHERE T.BILL_HEAD_ID =(select E.BILL_HEAD_ID from T_DEC_I_ENTRY_HEAD E where E.SID =#{sid});
        UPDATE T_DEC_ERP_I_LIST_BILL T SET STATUS=#{status} WHERE T.BILL_HEAD_ID =(select E.BILL_HEAD_ID from T_DEC_I_ENTRY_HEAD E where E.SID =#{sid});
        UPDATE T_DEC_ERP_I_RELATION T SET STATUS=#{status} WHERE T.ENTRY_SID =#{sid};
        <if test='_databaseId != "postgresql" '>
            END;
        </if>

    </update>
    <!-- 非保税（立交桥）报关单回执更新数据-->
    <update id="updateLjqReply" parameterType="com.dcjet.cs.entry.model.DecIEntryHead">
        <if test='_databaseId != "postgresql" '>
            begin
        </if>
        UPDATE T_DEC_I_ENTRY_HEAD
        <set>
            <if test="entryNo != null">ENTRY_NO = #{entryNo},</if>
            <if test="entryStatus != null">ENTRY_STATUS = #{entryStatus},</if>
            <if test="sendApiStatus != null">SEND_API_STATUS=#{sendApiStatus},</if>
            <if test="DDate != null">D_DATE = case when LENGTH(#{DDate})=8 then to_char(to_date(#{DDate},'yyyyMMdd'),'yyyy-MM-dd')  when instr(#{DDate},'月') > 0 then to_char(to_date(replace(#{DDate},'月',''),'dd-MM-yy'),'yyyy-MM-dd') when fn_isdate(#{DDate}) = 1 then to_char(to_date(#{DDate},'yyyy-MM-dd'),'yyyy-MM-dd') else null end,</if>
            SEQ_NO=#{seqNo}
        </set>
        WHERE SID=#{sid};

        <!-- 报关单追踪修改-->
        <if test="entryNo != null">
            <!-- 清单申报状态同步-->
            UPDATE T_DEC_I_BILL_HEAD T
            <set>
                <if test="seqNo != null">SEQ_NO=#{seqNo},</if>
                <if test="sendApiStatus != null">SEND_API_STATUS=#{sendApiStatus},</if>
                <if test="DDate != null">ENTRY_DECLARE_DATE = case when LENGTH(#{DDate})=8 then to_date(#{DDate},'yyyyMMdd')  when instr(#{DDate},'月') > 0 then to_date(replace(#{DDate},'月',''),'dd-MM-yy') when fn_isdate(#{DDate}) = 1 then to_date(#{DDate},'yyyy-MM-dd') else null end,
                </if>
                ENTRY_NO = #{entryNo}
            </set>
            WHERE T.HEAD_ID=#{erpHeadId} AND T.EMS_LIST_NO= #{emsListNo};

            UPDATE T_DEC_I_CUSTOMS_TRACK T
            <set>
                <if test="entryNo != null">ENTRY_NO = #{entryNo},</if>
                <if test="DDate != null">DECLARE_DATE = case when LENGTH(#{DDate})=8 then to_date(#{DDate},'yyyyMMdd')  when instr(#{DDate},'月') > 0 then to_date(replace(#{DDate},'月',''),'dd-MM-yy') when fn_isdate(#{DDate}) = 1 then to_date(#{DDate},'yyyy-MM-dd') else null end,
                </if>
            </set>
            WHERE T.ERP_HEAD_ID=#{erpHeadId}
            AND T.EMS_LIST_NO= #{emsListNo};
            <!-- 减免税免税申请表表头状态更新及报关单号回填-->
            UPDATE T_DEV_FREE_APPLY_HEAD
            SET ENTRY_NO = #{entryNo}, STATUS = '2', IS_DECLARE = '1'
            WHERE SID = #{erpHeadId};
        </if>
        <!-- 发送失败插入记录-->
        <if test="sendApiStatus=='0'.toString()">
            INSERT INTO T_POST_API_RETURN_INFO(TASK_ID,BILL_SID,TRADE_CODE,INSERT_USER,RETURN_JSON,BILL_TYPE)
            SELECT T.SEND_API_TASK_ID,T.SID,T.TRADE_CODE,T.INSERT_USER,#{auditResult},'ientry' FROM T_DEC_I_ENTRY_HEAD T
            WHERE T.SID=#{sid};
        </if>
        <if test='_databaseId != "postgresql" '>
            END;
        </if>
    </update>

    <!-- 获取进口报关单待发送状态数据-->
    <select id="getEntrySendCount" resultType="int" parameterType="map">
        SELECT COUNT(*) FROM t_dec_i_entry_head t
        <where>
            and (t.SEND_API_STATUS!='-1' and t.SEND_API_STATUS!='0')
            and t.ERP_HEAD_ID=#{sid,jdbcType=VARCHAR}
        </where>

    </select>

    <!-- 根据关联单号查询报关信息 -->
    <select id="queryEntryInfoByLinkedNo" parameterType="map" resultType="com.dcjet.cs.dto.returnEntry.ReturnEntryDataNew">
        select
          distinct
          li.linked_no as linkedNo,
          e.entry_no as entryNo,
          case
            when e.d_date is not null and length(e.D_DATE) = 8 and instr(e.D_DATE,'-') = 0 then to_date(e.D_DATE,'yyyyMMdd')
            when instr(e.D_DATE,'月') > 0 then to_date(replace(e.D_DATE,'月',''),'dd-MM-yy')
            when fn_isdate(e.D_DATE) = 1 then to_date(e.D_DATE,'yyyy-MM-dd') else null end as entryDeclareDate,
          e.ems_list_no as emsListNo,
          e.seq_no as seqNo,
          h.fee_mark as feeMark,
          h.fee_curr as feeCurr,
          h.fee_rate as feeRate,
          h.insur_mark as insurMark,
          h.insur_curr as insurCurr,
          h.insur_rate as insurRate
        from t_dec_erp_i_list_n li
        left join t_dec_erp_i_head_n h on li.head_id = h.sid
        left join t_dec_i_bill_head b on b.head_id = h.sid
        left join t_dec_i_entry_head e on e.bill_head_id = b.sid
        <where>
            li.trade_code = #{tradeCode}
            and li.linked_no is not null
            and e.entry_no is not null
            and li.linked_no in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectEntryDataByEmsListNo" resultType="com.dcjet.cs.dto.returnEntry.ReturnEntry">
        select entry_status as entryStatus, entry_no as entryNo from T_DEC_I_ENTRY_HEAD
        where trade_code = #{tradeCode} and EMS_LIST_NO = #{emsListNo}
    </select>

    <select id="getHgDefectData" resultType="com.dcjet.cs.entry.model.DecIEntryHead">
        select dec.ENTRY_NO, dec.EMS_LIST_NO, dec.TRADE_CODE,dec.insert_user
        from T_DEC_I_ENTRY_HEAD dec
        where dec.trade_code = #{tradeCode}
          and dec.ENTRY_NO is not null
          and not exists(
                select 1 from T_GWSTD_ENTRY_HEAD entry where entry.ENTRY_ID = dec.ENTRY_NO and entry.IS_DECLARE = '1'
            )
    </select>

    <select id="selectSyncEntryStatusCount" resultType="int">
        select count(1) from T_DEC_I_ENTRY_HEAD
        where sync_entry_status = '1' and trade_code = #{tradeCode} and ENTRY_NO in
        <foreach item="item" index="index" collection="entryNoList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateSyncStatusByEntryNo" parameterType="map">
        update T_DEC_I_ENTRY_HEAD
        <set>
            sync_entry_status = #{syncEntryStatus},
            update_time = #{updateTime},
            update_user = #{updateUser},
            update_user_name = #{updateUserName},
            <if test="syncEntryMessage != null and syncEntryMessage != ''">
                sync_entry_message = #{syncEntryMessage}
            </if>
        </set>
        where entry_no = #{entryNo} and trade_code = #{tradeCode}
    </update>

    <!--更新报关单状态-->
    <update id="updateStatus">
        <if test='IEMark=="I"'>
            <if test='_databaseId != "postgresql" '>
                begin
            </if>
            update t_dec_i_entry_head
            set entry_status = #{status},entry_no = #{entryNo}
            where sid =#{sid} and trade_code = #{tradeCode};

            update t_dec_i_bill_head bh
            set(seq_no,entry_no)=(
                select
                    eh.seq_no,#{entryNo}
                from t_dec_i_entry_head eh
                where
                    sid = #{sid}
                    and trade_code = #{tradeCode}
                    and eh.bill_head_id = bh.sid
            ) where exists(
                select 1 from t_dec_i_entry_head eh where eh.bill_head_id = bh.sid and eh.sid = #{sid} and eh.trade_code = #{tradeCode}
            );

            update t_dec_i_customs_track set entry_no = #{entryNo} where head_id = (
                select bill_head_id from t_dec_i_entry_head where sid = #{sid} and trade_code = #{tradeCode}
            );
            <if test='_databaseId != "postgresql" '>
                end;
            </if>
        </if>
        <if test='IEMark=="E"'>
            <if test='_databaseId != "postgresql" '>
                begin
            </if>
            update t_dec_e_entry_head
            set entry_status = #{status},entry_no = #{entryNo}
            where sid =#{sid} and trade_code = #{tradeCode};

            update t_dec_e_bill_head bh
            set(seq_no,entry_no)=(
                select
                    eh.seq_no,#{entryNo}
                from t_dec_e_entry_head eh
                where
                    sid = #{sid}
                    and trade_code = #{tradeCode}
                    and eh.bill_head_id = bh.sid
            ) where exists(
                select 1 from t_dec_e_entry_head eh where eh.bill_head_id = bh.sid and eh.sid = #{sid} and eh.trade_code = #{tradeCode}
            );

            update t_dec_e_customs_track set entry_no = #{entryNo} where head_id = (
                select bill_head_id from t_dec_e_entry_head where sid = #{sid} and trade_code = #{tradeCode}
            );
            <if test='_databaseId != "postgresql" '>
                end;
            </if>
        </if>
    </update>

    <select id="getEntryList" resultType="com.dcjet.cs.entry.model.DecIEntryHead">
        <if test='ieMark=="I"'>
            SELECT
            ERP_HEAD_ID,
            ENTRY_NO,
            EMS_LIST_NO,
            TRADE_CODE
            FROM
            T_DEC_I_ENTRY_HEAD
            WHERE ENTRY_NO=#{entryNo}
            AND TRADE_CODE = #{tradeCode}
        </if>
        <if test='ieMark=="E"'>
            SELECT
            ENTRY_NO,
            EMS_LIST_NO,
            TRADE_CODE
            FROM
            T_DEC_E_ENTRY_HEAD
            WHERE ENTRY_NO=#{entryNo}
            AND TRADE_CODE = #{tradeCode}
        </if>
    </select>

    <select id="getseqNoList" resultType="com.dcjet.cs.entry.model.DecIEntryHead">
        <if test='ieMark=="I"'>
            SELECT
            ERP_HEAD_ID,
            SEQ_NO,
            ENTRY_NO,
            EMS_LIST_NO,
            TRADE_CODE,
            sid,
            bill_head_id
            FROM
            T_DEC_I_ENTRY_HEAD
            WHERE SEQ_NO=#{seqNo}
            AND TRADE_CODE = #{tradeCode}
        </if>
        <if test='ieMark=="E"'>
            SELECT
            ERP_HEAD_ID,
            SEQ_NO,
            ENTRY_NO,
            EMS_LIST_NO,
            TRADE_CODE,
            sid,
            bill_head_id
            FROM
            T_DEC_E_ENTRY_HEAD
            WHERE SEQ_NO=#{seqNo}
            AND TRADE_CODE = #{tradeCode}
        </if>
    </select>
    <update id="updateIEDate">
        <if test='_databaseId != "postgresql" '>
            begin
        </if>
        UPDATE T_DEC_I_ENTRY_HEAD
        SET I_E_DATE =#{iedate}
        WHERE EMS_LIST_NO = #{emsListNo}
        AND TRADE_CODE = #{tradeCode};

        UPDATE T_DEC_ERP_I_HEAD_N
        SET I_E_DATE =#{iedate}
        WHERE SID = #{erpHeadId}
        AND TRADE_CODE = #{tradeCode};
        <if test='_databaseId != "postgresql" '>
            end;
        </if>
    </update>
    <update id="updateByEmsListNo">
        update T_DEC_I_ENTRY_HEAD set is_upload = '1' where ems_list_no = #{emsListNo} and trade_code = #{tradeCode}
    </update>

    <select id="getDecIEmsListNoBySeqNo" parameterType="string" resultType="string">
        select distinct(ylr.ems_list_no) ems_list_no
        from t_dec_erp_i_head_n ylr
        inner join t_dec_i_entry_head ih on ih.erp_head_id = ylr.sid
        where ih.seq_no = #{seqNo} and ih.trade_code = #{tradeCode}
    </select>

    <update id="recallSending">
        UPDATE t_dec_i_entry_head
        set send_api_status = #{sendApiStatus},
            entry_status='A0',
            status='0',
            send_api_task_id = #{sendApiTaskId},
            seq_no=null,
            send_entry_date=null
        where sid = #{sid}
          and trade_code = #{tradeCode}
    </update>

    <update id="updateDecHeads">
        BEGIN
        <choose>
        <when test="billHeadId != null and billHeadId!=''">
        UPDATE T_DEC_ERP_I_HEAD_N SET APPR_STATUS = #{apprStatus} WHERE SID =#{erpHeadId} and trade_code = #{ tradeCode };
        UPDATE T_DEC_ERP_I_LIST_N SET STATUS=#{status} WHERE BILL_HEAD_ID =#{billHeadId} and trade_code = #{ tradeCode };
        UPDATE T_DEC_I_CUSTOMS_TRACK SET STATUS=#{status} WHERE HEAD_ID =#{billHeadId};
        UPDATE T_DEC_I_BILL_HEAD SET STATUS=#{status} WHERE SID =#{billHeadId};
        UPDATE T_DEC_ERP_I_LIST_BILL SET STATUS=#{status} WHERE BILL_HEAD_ID =#{billHeadId};
        UPDATE T_DEC_ERP_I_RELATION SET STATUS=#{status} WHERE bill_sid =#{billHeadId};
        </when>
        <otherwise>
            UPDATE T_DEC_ERP_I_LIST_N SET STATUS=#{status} WHERE BILL_HEAD_ID =#{erpHeadId} and trade_code = #{ tradeCode };
            UPDATE T_DEC_I_CUSTOMS_TRACK SET STATUS=#{status} WHERE HEAD_ID =#{erpHeadId};
            UPDATE T_DEC_I_BILL_HEAD SET STATUS=#{status} WHERE SID =#{erpHeadId};
            UPDATE T_DEC_ERP_I_LIST_BILL SET STATUS=#{status} WHERE BILL_HEAD_ID =#{erpHeadId};
            UPDATE T_DEC_ERP_I_RELATION SET STATUS=#{status} WHERE HEAD_ID =#{erpHeadId};
        </otherwise>
        </choose>
        <if test="apprStatus != null and apprStatus!=''">
            UPDATE T_DEC_ERP_I_HEAD_N SET APPR_STATUS = #{apprStatus} WHERE SID =#{erpHeadId} and trade_code = #{ tradeCode };
        </if>
        END;
    </update>
    <update id="updateDecHeads" databaseId="postgresql">
        <choose>
        <when test="billHeadId != null and billHeadId!=''">
        UPDATE T_DEC_ERP_I_LIST_N SET STATUS=#{status} WHERE BILL_HEAD_ID =#{billHeadId} and trade_code = #{ tradeCode };
        UPDATE T_DEC_I_CUSTOMS_TRACK SET STATUS=#{status} WHERE HEAD_ID =#{billHeadId};
        UPDATE T_DEC_I_BILL_HEAD SET STATUS=#{status} WHERE SID =#{billHeadId};
        UPDATE T_DEC_ERP_I_LIST_BILL SET STATUS=#{status} WHERE BILL_HEAD_ID =#{billHeadId};
        UPDATE T_DEC_ERP_I_RELATION SET STATUS=#{status} WHERE bill_sid =#{billHeadId};
        </when>
        <otherwise>
            UPDATE T_DEC_ERP_I_LIST_N SET STATUS=#{status} WHERE BILL_HEAD_ID =#{erpHeadId} and trade_code = #{ tradeCode };
            UPDATE T_DEC_I_CUSTOMS_TRACK SET STATUS=#{status} WHERE HEAD_ID =#{erpHeadId};
            UPDATE T_DEC_I_BILL_HEAD SET STATUS=#{status} WHERE SID =#{erpHeadId};
            UPDATE T_DEC_ERP_I_LIST_BILL SET STATUS=#{status} WHERE BILL_HEAD_ID =#{erpHeadId};
            UPDATE T_DEC_ERP_I_RELATION SET STATUS=#{status} WHERE HEAD_ID =#{erpHeadId};
        </otherwise>
        </choose>
        <if test="apprStatus != null and apprStatus!=''">
            UPDATE T_DEC_ERP_I_HEAD_N SET APPR_STATUS = #{apprStatus} WHERE SID =#{erpHeadId} and trade_code = #{ tradeCode };
        </if>
    </update>

    <update id="updateDDateByEntryId" parameterType="com.dcjet.cs.entry.model.DecIEntryHead">
        <if test="_databaseId == 'oracle'">
            begin
        </if>
        <choose>
            <when test="IEMark == 'I'.toString()">
                update t_dec_i_entry_head set d_date = #{DDate} where sid = #{sid} and d_date is null;
                update t_dec_i_bill_head set entry_declare_date = #{entryDeclareDate} where sid = #{billHeadId} and entry_declare_date is null;
                update t_dec_i_customs_track set declare_date = #{entryDeclareDate} where head_id = #{billHeadId} and declare_date is null;
            </when>
            <otherwise>
                update t_dec_e_entry_head set d_date = #{DDate} where sid = #{sid} and d_date is null;
                update t_dec_e_bill_head set entry_declare_date = #{entryDeclareDate} where sid = #{billHeadId} and entry_declare_date is null;
                update t_dec_e_customs_track set declare_date = #{entryDeclareDate} where head_id = #{billHeadId} and declare_date is null;
            </otherwise>
        </choose>
        <if test="_databaseId == 'oracle'">
            end;
        </if>
    </update>
</mapper>
