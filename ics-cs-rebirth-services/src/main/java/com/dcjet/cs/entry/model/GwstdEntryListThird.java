package com.dcjet.cs.entry.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2023-3-17
 */
@Setter
@Getter
@Table(name = "t_gwstd_entry_list_third")
public class GwstdEntryListThird implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * 企业编码
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 表头主键
     */
    @Column(name = "head_id")
    private String headId;
    /**
     * 报关单中心统一编号（预录入号）
     */
    @Column(name = "seq_no")
    private String seqNo;
    /**
     * 检验检疫代码
     */
    @Column(name = "ciq_code")
    private String ciqCode;
    /**
     * 境内目的地（商检）
     */
    @Column(name = "ciqdest_code")
    private String ciqdestCode;
    /**
     * 检验检疫名称
     */
    @Column(name = "ciq_name")
    private String ciqName;
    /**
     * 商品编号
     */
    @Column(name = "code_ts")
    private String codeTs;
    /**
     * 合同商品项序号
     */
    @Column(name = "contr_item")
    private Integer contrItem;
    /**
     * 成交币制
     */
    @Column(name = "curr")
    private String curr;
    /**
     * 非危险化学品(危险货物信息)
     */
    @Column(name = "danger_flag")
    private String dangerFlag;
    /**
     * 危险货物名称(危险货物信息)
     */
    @Column(name = "dang_name")
    private String dangName;
    /**
     * 数据来源（0：固定 1：电子口岸 2：爬网 3：组合）
     */
    @Column(name = "data_source")
    private String dataSource;
    /**
     * 申报单价
     */
    @Column(name = "decl_price")
    private BigDecimal declPrice;
    /**
     * 申报总价
     */
    @Column(name = "decl_total")
    private BigDecimal declTotal;
    /**
     * 最终目的国
     */
    @Column(name = "destination_country")
    private String destinationCountry;
    /**
     * 目的地（产地）代码-行政区域
     */
    @Column(name = "district_code")
    private String districtCode;
    /**
     * 货物属性代码(检验检疫)
     */
    @Column(name = "goods_attr")
    private String goodsAttr;
    /**
     * 货物品牌(检验检疫)
     */
    @Column(name = "goods_brand")
    private String goodsBrand;
    /**
     * 货物型号(检验检疫)
     */
    @Column(name = "goods_model")
    private String goodsModel;
    /**
     * 货物规格(检验检疫)
     */
    @Column(name = "goods_spec")
    private String goodsSpec;
    /**
     * 商品名称
     */
    @Column(name = "g_name")
//    @JsonProperty("gname")
    private String gname;
    /**
     * 商品规格、型号
     */
    @Column(name = "g_model")
//    @JsonProperty("gmodel")
    private String gmodel;
    /**
     * 商品序号
     */
    @Column(name = "g_no")
//    @JsonProperty("gno")
    private Integer gno;
    /**
     * 申报数量
     */
    @Column(name = "g_qty")
//    @JsonProperty("gqty")
    private BigDecimal gqty;
    /**
     * 申报计量单位
     */
    @Column(name = "g_unit")
//    @JsonProperty("gunit")
    private String gunit;
    /**
     * 生产日期(检验检疫)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "produce_date")
    private Date produceDate;
    /**
     * 产销国
     */
    @Column(name = "origin_country")
    private String originCountry;
    /**
     * 危包规格(危险货物信息)
     */
    @Column(name = "pack_model")
    private String packModel;
    /**
     * 危包类别
     */
    @Column(name = "pack_type")
    private String packType;
    /**
     * 产品有效期(检验检疫)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "prod_validdt")
    private Date prodValiddt;
    /**
     * 第一（法定）数量
     */
    @Column(name = "qty_1")
    private BigDecimal qty1;
    /**
     * 第一(法定)计量单位
     */
    @Column(name = "unit_1")
    private String unit1;
    /**
     * 第二数量
     */
    @Column(name = "qty_2")
    private BigDecimal qty2;
    /**
     * 第二计量单位
     */
    @Column(name = "unit_2")
    private String unit2;
    /**
     * 成分/原料/组分(检验检疫)
     */
    @Column(name = "stuff")
    private String stuff;
    /**
     * 危险货物编码
     */
    @Column(name = "dang_code")
    private String dangCode;
    /**
     * 用途
     */
    @Column(name = "use_to")
    private String useTo;
    /**
     * 征减免税方式
     */
    @Column(name = "duty_mode")
    private String dutyMode;
    /**
     * 海关编号
     */
    @Column(name = "entry_id")
    private String entryId;
    /**
     * 归类标志 0自动归类1人工归类
     */
    @Column(name = "class_mark")
    private String classMark;
    /**
     * 目的地（产地）代码-行政区域
     */
    @Column(name = "district_post_code")
    private String districtPostCode;
    /**
     * 工缴费(美元)
     */
    @Column(name = "work_usd")
    private BigDecimal workUsd;
    /**
     * 加工料件/成品货号
     */
    @Column(name = "exg_no")
    private String exgNo;
    /**
     * 成交总价
     */
    @Column(name = "trade_total")
    private BigDecimal tradeTotal;
    /**
     * 生产单位注册号
     */
    @Column(name = "mnufctrre_gno")
    private String mnufctrreGno;
    /**
     * 原产地区代码
     */
    @Column(name = "orig_place_code")
    private String origPlaceCode;
    /**
     * UN编码
     */
    @Column(name = "un_code")
    private String unCode;
    /**
     * 创建人
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 创建人名称
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 产销国名称
     */
    @Column(name = "origin_country_name")
    private String originCountryName;
    /**
     * 最终目的国名称
     */
    @Column(name = "destination_country_name")
    private String destinationCountryName;
    /**
     * 征减免税方式名称
     */
    @Column(name = "duty_mode_name")
    private String dutyModeName;
    /**
     * 境内货源地/境内目的地名称
     */
    @Column(name = "district_code_name")
    private String districtCodeName;
    /**
     * 第一(法定)计量单位转换
     */
    @Column(name = "unit_1_convert")
    private String unit1Convert;
    /**
     * 第二(法定)计量单位转换
     */
    @Column(name = "unit_2_convert")
    private String unit2Convert;
    /**
     * 申报计量单位转换
     */
    @Column(name = "g_unit_convert")
//    @JsonProperty("gunitConvert")
    private String gunitConvert;
    /**
     * 币制转换
     */
    @Column(name = "curr_convert")
    private String currConvert;
    /**
     * 原产国转换
     */
    @Column(name = "origin_country_convert")
    private String originCountryConvert;
    /**
     * 最终目的国转换
     */
    @Column(name = "destination_country_convert")
    private String destinationCountryConvert;
    /**
     * 目的地（产地）代码-行政区域名称
     */
    @Column(name = "district_post_code_name")
    private String districtPostCodeName;
}
