package com.dcjet.cs.entry.service;

import com.dcjet.cs.dto.entry.GwstdEntryListThirdLocDto;
import com.dcjet.cs.dto.entry.GwstdEntryListThirdLocParam;
import com.dcjet.cs.entry.dao.GwstdEntryListThirdLocMapper;
import com.dcjet.cs.entry.mapper.GwstdEntryListThirdLocDtoMapper;
import com.dcjet.cs.entry.model.EntryListThirdLoc;
import org.apache.commons.collections4.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2023-3-17
 */
@Service
public class GwstdEntryListThirdLocService extends BaseService<EntryListThirdLoc> {
    @Resource
    private GwstdEntryListThirdLocMapper gwstdEntryListThirdLocMapper;
    @Resource
    private GwstdEntryListThirdLocDtoMapper gwstdEntryListThirdLocDtoMapper;

    @Override
    public Mapper<EntryListThirdLoc> getMapper() {
        return gwstdEntryListThirdLocMapper;
    }

    /**
     * 功能描述: grid分页查询
     *
     * @param gwstdEntryListThirdLocParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwstdEntryListThirdLocDto>> selectAllPaged(GwstdEntryListThirdLocParam gwstdEntryListThirdLocParam, PageParam pageParam) {
        // 启用分页查询
        EntryListThirdLoc entryListThirdLoc = gwstdEntryListThirdLocDtoMapper.toPo(gwstdEntryListThirdLocParam);
        Page<EntryListThirdLoc> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdEntryListThirdLocMapper.getList(entryListThirdLoc));
        List<GwstdEntryListThirdLocDto> gwstdEntryListThirdLocDtos = page.getResult().stream().map(head -> {
            GwstdEntryListThirdLocDto dto = gwstdEntryListThirdLocDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdEntryListThirdLocDto>> paged = ResultObject.createInstance(gwstdEntryListThirdLocDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @return
     */
    public List<GwstdEntryListThirdLocDto> selectAll(GwstdEntryListThirdLocParam exportParam) {
        EntryListThirdLoc entryListThirdLoc = gwstdEntryListThirdLocDtoMapper.toPo(exportParam);
        List<GwstdEntryListThirdLocDto> gwstdEntryListThirdLocDtos = new ArrayList<>();
        List<EntryListThirdLoc> gwstdEntryListThirdLocs = gwstdEntryListThirdLocMapper.getList(entryListThirdLoc);
        if (CollectionUtils.isNotEmpty(gwstdEntryListThirdLocs)) {
            gwstdEntryListThirdLocDtos = gwstdEntryListThirdLocs.stream().map(item -> {
                GwstdEntryListThirdLocDto dto = gwstdEntryListThirdLocDtoMapper.toDto(item);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwstdEntryListThirdLocDtos;
    }

    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public GwstdEntryListThirdLocDto insert(GwstdEntryListThirdLocParam model, UserInfoToken userInfo) {
        String sid = UUID.randomUUID().toString();
        EntryListThirdLoc entryListThirdLoc = gwstdEntryListThirdLocDtoMapper.toPo(model);
        entryListThirdLoc.setSid(sid);
        entryListThirdLoc.setInsertUser(userInfo.getUserNo());
        entryListThirdLoc.setInsertTime(new Date());
        int insertStatus = gwstdEntryListThirdLocMapper.insert(entryListThirdLoc);
        return insertStatus > 0 ? gwstdEntryListThirdLocDtoMapper.toDto(entryListThirdLoc) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwstdEntryListThirdLocParam
     * @param userInfo
     * @return
     */
    public GwstdEntryListThirdLocDto update(GwstdEntryListThirdLocParam gwstdEntryListThirdLocParam, UserInfoToken userInfo) {
        EntryListThirdLoc entryListThirdLoc = gwstdEntryListThirdLocMapper.selectByPrimaryKey(gwstdEntryListThirdLocParam.getSid());
        gwstdEntryListThirdLocDtoMapper.updatePo(gwstdEntryListThirdLocParam, entryListThirdLoc);
        entryListThirdLoc.setUpdateUser(userInfo.getUserNo());
        entryListThirdLoc.setUpdateTime(new Date());
        // 更新数据
        int update = gwstdEntryListThirdLocMapper.updateByPrimaryKey(entryListThirdLoc);
        return update > 0 ? gwstdEntryListThirdLocDtoMapper.toDto(entryListThirdLoc) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids, UserInfoToken userInfo) {
        gwstdEntryListThirdLocMapper.deleteBySids(sids);
    }

    public int getListNumByHeadIds(List<String> sids) {
        return gwstdEntryListThirdLocMapper.getListNumByHeadIds(sids);
    }

    /**
     * @param sid
     * @return
     */
    public GwstdEntryListThirdLocDto getOne(String sid) {
        EntryListThirdLoc loc = gwstdEntryListThirdLocMapper.selectByPrimaryKey(sid);
        if (loc == null) {
            return null;
        }
        return gwstdEntryListThirdLocDtoMapper.toDto(loc);
    }
}
