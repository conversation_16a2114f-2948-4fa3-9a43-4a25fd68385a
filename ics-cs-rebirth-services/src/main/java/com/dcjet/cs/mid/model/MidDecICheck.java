package com.dcjet.cs.mid.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2020-6-16
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_MID_DEC_I_CHECK")
public class MidDecICheck implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private String sid;
	/**
     * 物料类型标识
     */
	@Column(name = "G_MARK")
	private String GMark;
	/**
     * 保完税标志
     */
	@Column(name = "BOND_MARK")
	private String bondMark;
	/**
     * 企业料号
     */
	@Column(name = "FAC_G_NO")
	private String facGNo;
	/**
     * 备案料号
     */
	@Column(name = "COP_G_NO")
	private String copGNo;
	/**
     * 入库关联号
     */
	@Column(name = "LINKED_NO")
	private String linkedNo;
	/**
     * 申报数量
     */
	@Column(name = "QTY")
	private BigDecimal qty;
	/**
     * 入库数量
     */
	@Column(name = "WH_QTY")
	private BigDecimal whQty;
	/**
     * 转换申报单位数量
     */
	@Column(name = "QTY_CONVERT")
	private BigDecimal qtyConvert;
	/**
	 * 差异
	 */
	@Column(name = "DIFF")
	private BigDecimal diff;
	/**
	 * 业务排序字段
	 */
	@Column(name = "ORDER_ID")
	private BigDecimal orderId;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "INSERT_USER")
	private String insertUser;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private Date insertTime;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_USER")
	private String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private Date updateTime;
	/**
     * 制单人姓名
     */
	@Column(name = "INSERT_USER_NAME")
	private String insertUserName;
	/**
     * 修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private String updateUserName;
	/**
	 * 申报数量查询条件(全部；=0；≠0)
	 */
	@Transient
	private String qtyType;
	/**
	 * 差异查询条件(全部；=0；≠0；>0；<0)
	 */
	@Transient
	private String diffType;
	/**
	 * 申报开始日期
	 */
	@Column(name = "DECLARE_DATE_FROM")
	private Date declareDateFrom;
	/**
	 * 申报结束日期
	 */
	@Column(name = "DECLARE_DATE_TO")
	private Date declareDateTo;
	/**
	 * 入库开始日期
	 */
	@Column(name = "WH_DATE_FROM")
	private Date whDateFrom;
	/**
	 * 入库结束日期
	 */
	@Column(name = "WH_DATE_TO")
	private Date whDateTo;

	/* 此处解决页面传输的日期格式问题 */
	/**
	 * 申报开始日期
	 */
	@Transient
	private String declareDate2From;
	/**
	 * 申报结束日期
	 */
	@Transient
	private String declareDate2To;
	/**
	 * 出库开始日期
	 */
	@Transient
	private String whDate2From;
	/**
	 * 出库结束日期
	 */
	@Transient
	private String whDate2To;

	/**
	 * 申报日期查询标记
	 */
	@Transient
	private String declareDateCanSelect;
	/**
	 * 入库日期查询标记
	 */
	@Transient
	private String whDateCanSelect;
}
