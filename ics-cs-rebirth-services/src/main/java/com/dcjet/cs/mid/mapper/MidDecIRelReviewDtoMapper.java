package com.dcjet.cs.mid.mapper;

import com.dcjet.cs.dto.mid.MidDecIRelReviewDto;
import com.dcjet.cs.dto.mid.MidDecIRelReviewParam;
import com.dcjet.cs.mid.model.MidDecIRelReview;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-6-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MidDecIRelReviewDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    MidDecIRelReviewDto toDto(MidDecIRelReview po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    MidDecIRelReview toPo(MidDecIRelReviewParam param);
    /**
     * 数据库原始数据更新
     * @param midDecIReviewParam
     * @param midDecIReview
     */
    void updatePo(MidDecIRelReviewParam midDecIReviewParam, @MappingTarget MidDecIRelReview midDecIReview);
}
