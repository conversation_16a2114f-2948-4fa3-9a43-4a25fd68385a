package com.dcjet.cs.mid.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.mid.MidImgIListDto;
import com.dcjet.cs.dto.mid.MidImgIListParam;
import com.dcjet.cs.mid.dao.MidImgIListMapper;
import com.dcjet.cs.mid.mapper.MidImgIListDtoMapper;
import com.dcjet.cs.mid.model.MidImgIList;
import com.dcjet.cs.util.CommonEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: ERP入库明细
 * @author: WJ
 * @createDate: 2020/9/22 15:54
 */
@Component
public class ExportMidImgIService implements Exportable {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private MidImgIListMapper midImgIListMapper;

    @Resource
    private MidImgIListDtoMapper midImgIListDtoMapper;

    @Resource
    private PCodeHolder pCodeHolder;

    private final String taskName = xdoi18n.XdoI18nUtil.t("ERP入库明细(MID_IMG_I)");

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("MID_IMG_I");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        MidImgIListParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        MidImgIList midImgIList = midImgIListDtoMapper.toPo(exportParam);
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(midImgIList));
        Integer count = midImgIListMapper.selectDataCount(midImgIList);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        MidImgIListParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        MidImgIList midImgIList = midImgIListDtoMapper.toPo(exportParam);
        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(midImgIList));
        Page<MidImgIList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> midImgIListMapper.getList(midImgIList));

        List<MidImgIListDto> midImgIListDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            midImgIListDtos = page.getResult().stream().map(head -> {
                MidImgIListDto dto = midImgIListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }

        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(midImgIListDtos));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    private List<MidImgIListDto> convertForPrint(List<MidImgIListDto> list) {
        for (MidImgIListDto item : list) {
            if(StringUtils.isNotBlank(item.getBondMark())) {
                item.setBondMark(CommonEnum.BondMarkEnum.getValue(item.getBondMark()));
            }
            if (StringUtils.isNotBlank(item.getGMark())) {
                item.setGMark(CommonEnum.GMarkEnum.getValue(item.getGMark()));
            }
            if(StringUtils.isNotBlank(item.getWhUnit())) {
                String value = pCodeHolder.getValue(item.getWhUnit(), PCodeType.UNIT);
                item.setWhUnit(StringUtils.isNotBlank(value) ? item.getWhUnit()+" " + value : item.getWhUnit());
            }
            if(StringUtils.isNotBlank(item.getUnit())) {
//                String value = pCodeHolder.getValue(item.getUnit(), PCodeType.UNIT);
                String value = pCodeHolder.getValue(PCodeType.UNIT, item.getUnit());
                item.setUnit(StringUtils.isNotBlank(value) ? item.getUnit()+" " + value : item.getUnit());
            }
            if(StringUtils.isNotBlank(item.getInWay())) {
                item.setInWay(StringUtils.isNotBlank(CommonEnum.InOutWayEnum.getValue(item.getInWay())) ?
                        item.getInWay()+" "+ CommonEnum.InOutWayEnum.getValue(item.getInWay()) : item.getInWay());
            }
            if (StringUtils.isNotBlank(item.getDataSource())) {
                item.setDataSource(item.getDataSource()+""+CommonEnum.matSourceEnum.getValue(item.getDataSource()));
            }
        }
        return list;
    }

    private MidImgIListParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        MidImgIListParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, MidImgIListParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
