package com.dcjet.cs.mid.dao;

import com.dcjet.cs.mid.model.MidDecEReview;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* MidDecEReview
* <AUTHOR>
* @date: 2020-6-16
*/
public interface MidDecEReviewMapper extends Mapper<MidDecEReview> {
    /**
     * 查询获取数据
     * @param midDecEReview
     * @return
     */
    List<MidDecEReview> getList(MidDecEReview midDecEReview);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 查询分页集合
     */
    List<MidDecEReview> selectDataByPage(String tradeCode);
}
