package com.dcjet.cs.mid.dao;

import com.dcjet.cs.dto.mid.MidCheckParam;
import com.dcjet.cs.mid.model.MidDecECheck;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* MidDecECheck
* <AUTHOR>
* @date: 2020-6-16
*/
public interface MidDecECheckMapper extends Mapper<MidDecECheck> {
    /**
     * 查询获取数据
     * @param midDecECheck
     * @return
     */
    List<MidDecECheck> getList(MidDecECheck midDecECheck);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 删除化进口物料入库明细追溯表
     * @param midCheckParam
     */
    long deleteExgEReviewData(MidCheckParam midCheckParam);
    /**
     * 初始化出口物料核对追溯表
     */
    void initDecEReviewData(MidCheckParam midCheckParam);
    /**
     * 计算出口物料核对数据
     */
    void calDecEMatData(MidCheckParam midCheckParam);
}
