<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.mid.dao.MidDecERelReviewMapper">
    <resultMap id="midDecERelReviewResultMap" type="com.dcjet.cs.mid.model.MidDecERelReview">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="DECLARE_DATE" property="declareDate" jdbcType="DATE" />
		<result column="ENTRY_NO" property="entryNo" jdbcType="VARCHAR" />
		<result column="LIST_NO" property="listNo" jdbcType="VARCHAR" />
		<result column="EMS_LIST_NO" property="emsListNo" jdbcType="VARCHAR" />
		<result column="FAC_G_NO" property="facGNo" jdbcType="VARCHAR" />
		<result column="QTY" property="qty" jdbcType="NUMERIC" />
		<result column="EMS_NO" property="emsNo" jdbcType="VARCHAR" />
		<result column="SERIAL_NO" property="serialNo" jdbcType="NUMERIC" />
		<result column="COP_G_NO" property="copGNo" jdbcType="VARCHAR" />
		<result column="G_NAME" property="GName" jdbcType="VARCHAR" />
        <result column="LINKED_NO" property="linkedNo" jdbcType="VARCHAR" />
        <result column="BOND_MARK" property="bondMark" jdbcType="VARCHAR" />
        <result column="G_MARK" property="GMark" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
		<result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,DECLARE_DATE
     ,ENTRY_NO
     ,LIST_NO
     ,EMS_LIST_NO
     ,FAC_G_NO
     ,QTY
     ,EMS_NO
     ,SERIAL_NO
     ,COP_G_NO
     ,G_NAME
     ,TRADE_CODE
     ,LINKED_NO
     ,BOND_MARK
     ,G_MARK
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
    </sql>
    <sql id="condition">
        <if test="declareDateFrom != null and declareDateFrom != ''">
            <![CDATA[ and DECLARE_DATE >= to_date(#{declareDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="declareDateTo != null and declareDateTo != ''">
            <![CDATA[ and DECLARE_DATE <= to_date(#{declareDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="bondMark != null and bondMark != ''">
            and BOND_MARK = #{bondMark}
        </if>
        <if test="GMark != null and GMark != ''">
            and G_MARK = #{GMark}
        </if>
        <if test="entryNo != null and entryNo != ''">
            and ENTRY_NO like concat(concat('%', #{entryNo}),'%')
        </if>
        <if test="listNo != null and listNo != ''">
            and LIST_NO like concat(concat('%', #{listNo}),'%')
        </if>
        <if test="emsListNo != null and emsListNo != ''">
            and EMS_LIST_NO like concat(concat('%', #{emsListNo}),'%')
        </if>
        <if test='queryType == "1"'>
            <if test="facGNo != null and facGNo != ''">
                and FAC_G_NO = #{facGNo}
            </if>
            <if test="linkedNo != null and linkedNo != ''">
                and LINKED_NO = #{linkedNo}
            </if>
            <if test="linkedNo == null or linkedNo == ''">
                and LINKED_NO is null
            </if>
        </if>
        <if test='queryType != "1"'>
            <if test="facGNo != null and facGNo != ''">
                and FAC_G_NO like concat(concat('%', #{facGNo}),'%')
            </if>
            <if test="linkedNo != null and linkedNo != ''">
                and LINKED_NO like concat(concat('%', #{linkedNo}),'%')
            </if>
        </if>
        <if test="emsNo != null and emsNo != ''">
            and EMS_NO = #{emsNo}
        </if>
        <if test="serialNo != null and serialNo != ''">
            and SERIAL_NO = #{serialNo}
        </if>
        <if test="copGNo != null and copGNo != ''">
            and COP_G_NO like concat(concat('%', #{copGNo}),'%')
        </if>
        <if test="GName != null and GName != ''">
            and G_NAME like concat(concat('%', #{GName}),'%')
        </if>
            and TRADE_CODE = #{tradeCode}
        <if test="insertUserName != null and insertUserName != ''">
            and INSERT_USER_NAME = #{insertUserName}
        </if>
        <if test="updateUserName != null and updateUserName != ''">
            and UPDATE_USER_NAME = #{updateUserName}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="midDecERelReviewResultMap" parameterType="com.dcjet.cs.mid.model.MidDecERelReview">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_MID_DEC_E_REL_REVIEW t
        <where>
            <include refid="condition"></include>
        </where>
        ORDER BY t.INSERT_TIME DESC, t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_MID_DEC_E_REL_REVIEW t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="selectDataByPage" parameterType="string" resultMap="midDecERelReviewResultMap">
        select * from T_MID_DEC_E_REL_REVIEW where trade_code = #{tradeCode} order by insert_time desc,sid
    </select>
</mapper>
