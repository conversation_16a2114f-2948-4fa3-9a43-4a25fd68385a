package com.dcjet.cs.mid.service;

import com.dcjet.cs.dto.mid.MidDecECheckDto;
import com.dcjet.cs.dto.mid.MidDecECheckParam;
import com.dcjet.cs.mid.dao.MidDecECheckMapper;
import com.dcjet.cs.mid.mapper.MidDecECheckDtoMapper;
import com.dcjet.cs.mid.model.MidDecECheck;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-6-16
 */
@Service
public class MidDecECheckService extends BaseService<MidDecECheck> {
    @Resource
    private MidDecECheckMapper midDecECheckMapper;
    @Resource
    private MidDecECheckDtoMapper midDecECheckDtoMapper;
    @Override
    public Mapper<MidDecECheck> getMapper() {
        return midDecECheckMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param midDecECheckParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<MidDecECheckDto>> getListPaged(MidDecECheckParam midDecECheckParam, PageParam pageParam) {
        MidDecECheck midDecECheck = midDecECheckDtoMapper.toPo(midDecECheckParam);
        // 启用分页查询
        initDateParam(midDecECheck);
        Page<MidDecECheck> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> midDecECheckMapper.getList(midDecECheck));
        List<MidDecECheckDto> midDecECheckDtos = page.getResult().stream().map(head -> {
            MidDecECheckDto dto = midDecECheckDtoMapper.toDto(head);
            formatBondMarkAndGMark(dto," ");
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<MidDecECheckDto>> paged = ResultObject.createInstance(midDecECheckDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public void initDateParam(MidDecECheck midDecECheck) {
        if(StringUtils.isBlank(midDecECheck.getDeclareDate2From()) && StringUtils.isBlank(midDecECheck.getDeclareDate2To())) {
            midDecECheck.setDeclareDateCanSelect(ConstantsStatus.STATUS_0);
        }else {
            midDecECheck.setDeclareDateCanSelect(ConstantsStatus.STATUS_1);
            if(StringUtils.isBlank(midDecECheck.getDeclareDate2From())) {
                midDecECheck.setDeclareDate2From("2000-01-01");
            }
            if(StringUtils.isBlank(midDecECheck.getDeclareDate2To())) {
                midDecECheck.setDeclareDate2To("2099-12-12");
            }
        }
        if(StringUtils.isBlank(midDecECheck.getWhDate2From()) && StringUtils.isBlank(midDecECheck.getWhDate2To())) {
            midDecECheck.setWhDateCanSelect(ConstantsStatus.STATUS_0);
        }else {
            midDecECheck.setWhDateCanSelect(ConstantsStatus.STATUS_1);
            if(StringUtils.isBlank(midDecECheck.getWhDate2From())) {
                midDecECheck.setWhDate2From("2000-01-01");
            }
            if(StringUtils.isBlank(midDecECheck.getWhDate2To())) {
                midDecECheck.setWhDate2To("2099-12-12");
            }
        }
    }

    /**
     * 功能描述:新增
     *
     * @param midDecECheckParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MidDecECheckDto insert(MidDecECheckParam midDecECheckParam, UserInfoToken userInfo) {
        MidDecECheck midDecECheck = midDecECheckDtoMapper.toPo(midDecECheckParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        midDecECheck.setSid(sid);
        midDecECheck.setInsertUser(userInfo.getUserNo());
        midDecECheck.setInsertTime(new Date());
        // 新增数据
        int insertStatus = midDecECheckMapper.insert(midDecECheck);
        return  insertStatus > 0 ? midDecECheckDtoMapper.toDto(midDecECheck) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param midDecECheckParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MidDecECheckDto update(MidDecECheckParam midDecECheckParam, UserInfoToken userInfo) {
        MidDecECheck midDecECheck = midDecECheckMapper.selectByPrimaryKey(midDecECheckParam.getSid());
        midDecECheckDtoMapper.updatePo(midDecECheckParam, midDecECheck);
        midDecECheck.setUpdateUser(userInfo.getUserNo());
        midDecECheck.setUpdateTime(new Date());
        // 更新数据
        int update = midDecECheckMapper.updateByPrimaryKey(midDecECheck);
        return update > 0 ? midDecECheckDtoMapper.toDto(midDecECheck) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
		midDecECheckMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<MidDecECheckDto> selectAll(MidDecECheckParam exportParam, UserInfoToken userInfo) {
        MidDecECheck midDecECheck = midDecECheckDtoMapper.toPo(exportParam);
        midDecECheck.setTradeCode(userInfo.getCompany());
        List<MidDecECheckDto> midDecECheckDtos = new ArrayList<>();
        initDateParam(midDecECheck);
        List<MidDecECheck> midDecEChecks = midDecECheckMapper.getList(midDecECheck);
        if (CollectionUtils.isNotEmpty(midDecEChecks)) {
            midDecECheckDtos = midDecEChecks.stream().map(head -> {
                MidDecECheckDto dto = midDecECheckDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return midDecECheckDtos;
    }

    /**
     * 格式化保完税标记和物料类型
     * @param item
     * @param mark
     */
    public void formatBondMarkAndGMark(MidDecECheckDto item,String mark) {
        if(StringUtils.isNotBlank(item.getBondMark())) {
            if(item.getBondMark().indexOf(",")!= -1) {
                String[] bondMarks = item.getBondMark().split(",");
                String bondMarkStr = "";
                for(String bondMark : bondMarks) {
                    bondMarkStr += StringUtils.isNotBlank(CommonEnum.BondMarkEnum.getValue(bondMark)) ? CommonEnum.BondMarkEnum.getValue(bondMark) : bondMark;
                    bondMarkStr += ",";
                }
                item.setBondMark(bondMarkStr.substring(0,bondMarkStr.length()-1));
            }else {
                item.setBondMark(StringUtils.isNotBlank(CommonEnum.BondMarkEnum.getValue(item.getBondMark())) ? CommonEnum.BondMarkEnum.getValue(item.getBondMark()) : item.getBondMark());
            }
        }
        if(StringUtils.isNotBlank(item.getGMark())) {
            if(item.getGMark().indexOf(",")!= -1) {
                String[] gMarks = item.getGMark().split(",");
                String gMarkStr = "";
                for(String gmark : gMarks) {
                    gMarkStr += StringUtils.isNotBlank(CommonEnum.GMarkEnum.getValue(gmark)) ?CommonEnum.GMarkEnum.getValue(gmark) : gmark;
                    gMarkStr += ",";
                }
                item.setGMark(gMarkStr.substring(0,gMarkStr.length()-1));
            }else {
                item.setGMark(StringUtils.isNotBlank(CommonEnum.GMarkEnum.getValue(item.getGMark())) ? CommonEnum.GMarkEnum.getValue(item.getGMark()) : item.getGMark());
            }
        }
    }
}
