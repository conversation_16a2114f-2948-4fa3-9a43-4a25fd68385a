package com.dcjet.cs.mid.service;

import com.dcjet.cs.bi.model.BiTransform;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.mat.model.MatImgexg;
import com.dcjet.cs.mid.model.MidExgEList;
import com.dcjet.cs.mid.model.MidImgIList;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ConstantsStatus;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

@Service
public class MidCalQtyService {

    @Resource
    private PCodeHolder pCodeHolder;

    /**
     * 1、自定义参数库转换后ERP交易单位 = 物料中心的申报单位
     *    转换后申报数量 = 交易数量 ；
     * 2、自定义参数库转换后ERP交易单位 != 物料中心的申报单位 && （转换后的ERP交易单位+物料中心申报单位）存在T_BI_TRANSFORM对应关系，配置的ERP比例因子：
     *    转换后申报数量 = 交易数量*配置的ERP比例因子；
     * 3、如果物料中心有维护ERP比例因子&&(转换前的ERP交易单位或转换后ERP交易单位的中文 = 物料中心的ERP计量单位)
     *    转换后申报数量 = 交易数量*ERP比例因子；
     * 4、如果没有ERP计量单位或未维护ERP比例因子；
     *    转换后申报数量 = 交易数量
     */
    //计算ERP料件出入库数量
    public void calImgIEWhQty(MidImgIList tmp, Map<String,MatImgexg> oneMap, Map<String, MatImgexg> facGNoMap,Map<String, BiTransform> transformMap) {
        //初始化必填
        tmp.setSid(UUID.randomUUID().toString());
        tmp.setDataSource(ConstantsStatus.STATUS_1);
        tmp.setInsertTime(new Date());
        tmp.setInsertUser(Constants.SYSTEM);
        String tmpKey = tmp.getBondMark()+"|"+tmp.getGMark()+"|"+tmp.getWhGNo();
        if(oneMap.containsKey(tmpKey)) {//完全匹配保完税标记+物料类型+企业料号
            calImg(oneMap,tmp,tmpKey,transformMap);
        }else {//尝试只匹配企业料号
            if(facGNoMap.containsKey(tmp.getWhGNo())) {
                calImg(facGNoMap,tmp,tmp.getWhGNo(),transformMap);
            }else {//物料中心不存在,转换申报单位数量置为0
                tmp.setQty(BigDecimal.ZERO);
            }
        }
    }

    public void calImg(Map<String,MatImgexg> map,MidImgIList tmp,String key,Map<String, BiTransform> transformMap) {
        MatImgexg matImgexg = map.get(key);
        tmp.setCopGNo(matImgexg.getCopGNo());//取物料中心备案料号
        tmp.setUnit(matImgexg.getUnit());//取物料中心申报单位
        if(tmp.getWhUnit() != null && tmp.getWhUnit().equals(matImgexg.getUnit())) {
            tmp.setQty(tmp.getWhQty());
        }else {
            BiTransform transformAfter = transformMap.get(tmp.getWhUnit() + matImgexg.getUnit());
            if (transformMap != null && transformAfter != null) {
                if(StringUtils.isNotBlank(transformAfter.getRate())) {
                    tmp.setQty(tmp.getWhQty().multiply(new BigDecimal(transformAfter.getRate())).setScale(5, BigDecimal.ROUND_HALF_UP));
                }else {
                    tmp.setQty(tmp.getWhQty());
                }
            } else {
                //从PCode中获取ERP接口数据中ERP计量单位转换后的中文(如果企业传输的数据就是标准海关代码,则可以成功转出)
                String scValue = pCodeHolder.getValue(PCodeType.UNIT,tmp.getWhUnitOrignal());
                if(StringUtils.isNotBlank(scValue) && StringUtils.equals(scValue,matImgexg.getUnitErp())) {
                    matImgexg.setUnitErp(tmp.getWhUnitOrignal());
                }
                if(StringUtils.isNotBlank(tmp.getWhUnitOrignal())
                        && StringUtils.isNotBlank(matImgexg.getUnitErp())
                        && matImgexg.getFactorErp() != null
                        && tmp.getWhUnitOrignal().equals(matImgexg.getUnitErp())
                ) {
                    tmp.setQty(tmp.getWhQty().multiply(matImgexg.getFactorErp()).setScale(5, BigDecimal.ROUND_HALF_UP));
                }else {
                    tmp.setQty(tmp.getWhQty());
                }
            }
        }
    }

    //计算ERP成品出入库数量
    public void calExgIEWhQty(MidExgEList tmp, Map<String,MatImgexg> oneMap, Map<String, MatImgexg> facGNoMap,Map<String, BiTransform> transformMap) {
        //初始化必填
        tmp.setSid(UUID.randomUUID().toString());
        tmp.setDataSource(ConstantsStatus.STATUS_1);
        tmp.setInsertTime(new Date());
        tmp.setInsertUser(Constants.SYSTEM);
        String tmpKey = tmp.getBondMark()+"|"+tmp.getGMark()+"|"+tmp.getWhGNo();
        if(oneMap.containsKey(tmpKey)) {//完全匹配保完税标记+物料类型+企业料号
            calExg(oneMap,tmp,tmpKey,transformMap);
        }else {//尝试只匹配企业料号
            if(facGNoMap.containsKey(tmp.getWhGNo())) {
                calExg(facGNoMap,tmp,tmp.getWhGNo(),transformMap);
            }else {//物料中心不存在,转换申报单位数量置为0
                tmp.setQty(BigDecimal.ZERO);
            }
        }
    }

    public void calExg(Map<String,MatImgexg> map,MidExgEList tmp,String key,Map<String, BiTransform> transformMap) {
        MatImgexg matImgexg = map.get(key);
        tmp.setCopGNo(matImgexg.getCopGNo());//取物料中心备案料号
        tmp.setUnit(matImgexg.getUnit());//取物料中心申报单位
        if(tmp.getWhUnit() != null && tmp.getWhUnit().equals(matImgexg.getUnit())) {
            tmp.setQty(tmp.getWhQty());
        }else {
            BiTransform transformAfter = transformMap.get(tmp.getWhUnit() + matImgexg.getUnit());
            if (transformMap != null && transformAfter != null) {
                if(StringUtils.isNotBlank(transformAfter.getRate())) {
                    tmp.setQty(tmp.getWhQty().multiply(new BigDecimal(transformAfter.getRate())).setScale(5, BigDecimal.ROUND_HALF_UP));
                }else {
                    tmp.setQty(tmp.getWhQty());
                }
            } else {
                //从PCode中获取ERP接口数据中ERP计量单位转换后的中文(如果企业传输的数据就是标准海关代码,则可以成功转出)
                String scValue = pCodeHolder.getValue(PCodeType.UNIT,tmp.getWhUnitOrignal());
                if(StringUtils.isNotBlank(scValue) && StringUtils.equals(scValue,matImgexg.getUnitErp())) {
                    matImgexg.setUnitErp(tmp.getWhUnitOrignal());
                }
                if(StringUtils.isNotBlank(tmp.getWhUnitOrignal())
                        && StringUtils.isNotBlank(matImgexg.getUnitErp())
                        && matImgexg.getFactorErp() != null
                        && tmp.getWhUnitOrignal().equals(matImgexg.getUnitErp())
                ) {
                    tmp.setQty(tmp.getWhQty().multiply(matImgexg.getFactorErp()).setScale(5, BigDecimal.ROUND_HALF_UP));
                }else {
                    tmp.setQty(tmp.getWhQty());
                }
            }
        }
    }
}
