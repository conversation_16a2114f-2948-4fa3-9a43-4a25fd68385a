package com.dcjet.cs.mid.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2020-6-16
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_MID_DEC_I_REVIEW")
public class MidDecIRelReview implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private String sid;
	/**
     * 申报日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "DECLARE_DATE")
	private Date declareDate;
	/**
     * 申报日期-开始
     */
	@Transient
	private String declareDateFrom;
	/**
     * 申报日期-结束
     */
	@Transient
    private String declareDateTo;
	/**
     * 报关单号
     */
	@Column(name = "ENTRY_NO")
	private String entryNo;
	/**
     * 核注清单编号
     */
	@Column(name = "LIST_NO")
	private String listNo;
	/**
     * 单据内部编号
     */
	@Column(name = "EMS_LIST_NO")
	private String emsListNo;
	/**
     * 企业料号
     */
	@Column(name = "FAC_G_NO")
	private String facGNo;
	/**
     * 申报数量
     */
	@Column(name = "QTY")
	private BigDecimal qty;
	/**
     * 备案号
     */
	@Column(name = "EMS_NO")
	private String emsNo;
	/**
     * 备案序号
     */
	@Column(name = "SERIAL_NO")
	private Long serialNo;
	/**
     * 备案料号
     */
	@Column(name = "COP_G_NO")
	private String copGNo;
	/**
     * 商品名称
     */
	@Column(name = "G_NAME")
	private String GName;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "INSERT_USER")
	private String insertUser;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private Date insertTime;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_USER")
	private String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private Date updateTime;
	/**
     * 制单人姓名
     */
	@Column(name = "INSERT_USER_NAME")
	private String insertUserName;
	/**
     * 修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private String updateUserName;
	/**
	 * 入库关联单号
	 */
	@Column(name = "LINKED_NO")
	private String linkedNo;
	/**
	 * 保完税标记
	 */
	@Column(name = "BOND_MARK")
	private String bondMark;
	/**
	 * 物料类型
	 */
	@Column(name = "G_MARK")
	private String GMark;
	/**
	 * 查询类型(0:列表,1:链接)
	 */
	@Transient
	private String queryType;
}
