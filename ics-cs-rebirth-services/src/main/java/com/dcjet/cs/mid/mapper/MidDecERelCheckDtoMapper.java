package com.dcjet.cs.mid.mapper;

import com.dcjet.cs.dto.mid.MidDecERelCheckDto;
import com.dcjet.cs.dto.mid.MidDecERelCheckParam;
import com.dcjet.cs.mid.model.MidDecERelCheck;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-6-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MidDecERelCheckDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    MidDecERelCheckDto toDto(MidDecERelCheck po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    MidDecERelCheck toPo(MidDecERelCheckParam param);
    /**
     * 数据库原始数据更新
     * @param midDecERelCheckParam
     * @param midDecERelCheck
     */
    void updatePo(MidDecERelCheckParam midDecERelCheckParam, @MappingTarget MidDecERelCheck midDecERelCheck);
}
