package com.dcjet.cs.mid.dao;

import com.dcjet.cs.dto.mid.MidCheckParam;
import com.dcjet.cs.mid.model.MidExgEList;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* MidExgEList
* <AUTHOR>
* @date: 2020-6-16
*/
public interface MidExgEListMapper extends Mapper<MidExgEList> {
    /**
     * 查询获取数据
     *
     * @param midExgEList
     * @return
     */
    List<MidExgEList> getList(MidExgEList midExgEList);
    /**
     * 查询获取数据
     * @param midImgIList
     * @return
     */
    List<MidExgEList> getMidCheckList(MidExgEList midImgIList);
    /**
     * 查询获取数据
     * @param midImgIList
     * @return
     */
    List<MidExgEList> getMidRelCheckList(MidExgEList midImgIList);
    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 删除出库明细表数据
     * @param tradeCode
     */
    long deleteExgEData(String tradeCode);
    /**
     * 更新成品入库OR成品出库lockMark
     */
    long updateMidELockMark(MidCheckParam midCheckParam);
    /**
     * 查询成品出入库
     */
    List<MidExgEList> getMidEListByPage(MidCheckParam midCheckParam);

    Integer selectDataCount(MidExgEList midExgEList);
}