package com.dcjet.cs.entrustFile.dao;

import com.dcjet.cs.entrustFile.model.EntrustFileHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * DecEBillHead
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
public interface EntrustFileHeadMapper extends Mapper<EntrustFileHead> {
    /**
     * 查询获取数据
     *
     * @param entrustFileHead
     * @return
     */
    List<EntrustFileHead> getList(EntrustFileHead entrustFileHead);

    /**
     * 功能描述:获取表体信息
     *
     * @param: erpHeadId 提单表头id
     * @return:
     */
    List<Map<String, Object>> getBodyDataList(@Param("erpHeadIds") List<String> erpHeadIds, @Param("tradeCode") String tradeCode);

    /**
     * 获取表体的汇总数量，净重等
     */
    Map<String, BigDecimal> getSumQty(@Param("erpHeadIds") List<String> erpHeadIds, @Param("tradeCode") String tradeCode);
}
