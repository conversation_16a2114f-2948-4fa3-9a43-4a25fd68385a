package com.dcjet.cs.tariff.dao;

import com.dcjet.cs.tariff.model.GwstdTariffErvail;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwstdTariffErvail
* <AUTHOR>
* @date: 2019-11-26
*/
public interface GwstdTariffErvailMapper extends Mapper<GwstdTariffErvail> {
    /**
     * 查询获取数据
     * @param gwstdTariffErvail
     * @return
     */
    List<GwstdTariffErvail> getList(GwstdTariffErvail gwstdTariffErvail);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
