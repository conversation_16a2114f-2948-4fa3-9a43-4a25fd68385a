package com.dcjet.cs.tariff.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.tariff.GwstdTariffGeneralDto;
import com.dcjet.cs.dto.tariff.GwstdTariffGeneralParam;
import com.dcjet.cs.entry.dao.DecIEntryHeadMapper;
import com.dcjet.cs.entry.dao.DecIEntryListMapper;
import com.dcjet.cs.tax.exRate.dao.ExchangeRateMapper;
import com.dcjet.cs.tax.exRate.model.ExchangeRate;
import com.dcjet.cs.mat.dao.MatNonBondedMapper;
import com.dcjet.cs.tariff.dao.GwstdTariffGeneralMapper;
import com.dcjet.cs.tariff.mapper.GwstdTariffGeneralDtoMapper;
import com.dcjet.cs.tariff.model.GwstdTariffGeneral;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-11-22
 */
@Service
public class GwstdTariffGeneralService extends BaseService<GwstdTariffGeneral> {

    private static final Logger logger = LoggerFactory.getLogger("GwstdTariffGeneralService");
    @Resource
    private DecIEntryHeadMapper decIEntryHeadMapper;
    @Resource
    private DecIEntryListMapper decIEntryListMapper;
    @Resource
    private ExchangeRateMapper exchangeRateMapper;
    @Resource
    private MatNonBondedMapper matNonBondedMapper;
    @Resource
    private GwstdTariffGeneralMapper gwstdTariffGeneralMapper;
    @Resource
    private GwstdTariffGeneralDtoMapper gwstdTariffGeneralDtoMapper;
    @Resource
    private PCodeHolder pCodeHolder;
    @Override
    public Mapper<GwstdTariffGeneral> getMapper() {
        return gwstdTariffGeneralMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwstdTariffGeneralParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwstdTariffGeneralDto>> getListPaged(GwstdTariffGeneralParam gwstdTariffGeneralParam, PageParam pageParam) {
        // 启用分页查询
        GwstdTariffGeneral gwstdTariffGeneral = gwstdTariffGeneralDtoMapper.toPo(gwstdTariffGeneralParam);
        Page<GwstdTariffGeneral> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdTariffGeneralMapper.getList(gwstdTariffGeneral));
        List<GwstdTariffGeneralDto> gwstdTariffGeneralDtos = page.getResult().stream().map(head -> {
            GwstdTariffGeneralDto dto = gwstdTariffGeneralDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        convertForPrint(gwstdTariffGeneralDtos);
		ResultObject<List<GwstdTariffGeneralDto>> paged = ResultObject.createInstance(gwstdTariffGeneralDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwstdTariffGeneralParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdTariffGeneralDto insert(GwstdTariffGeneralParam gwstdTariffGeneralParam, UserInfoToken userInfo) {
        GwstdTariffGeneral gwstdTariffGeneral = gwstdTariffGeneralDtoMapper.toPo(gwstdTariffGeneralParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwstdTariffGeneral.setSid(sid);
        gwstdTariffGeneral.setInsertUser(userInfo.getUserNo());
        gwstdTariffGeneral.setInsertTime(new Date());
        // 新增数据
        int insertStatus = gwstdTariffGeneralMapper.insert(gwstdTariffGeneral);
        return  insertStatus > 0 ? gwstdTariffGeneralDtoMapper.toDto(gwstdTariffGeneral) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param gwstdTariffGeneralParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdTariffGeneralDto update(GwstdTariffGeneralParam gwstdTariffGeneralParam, UserInfoToken userInfo) {
        GwstdTariffGeneral gwstdTariffGeneral = gwstdTariffGeneralMapper.selectByPrimaryKey(gwstdTariffGeneralParam.getSid());
        gwstdTariffGeneralDtoMapper.updatePo(gwstdTariffGeneralParam, gwstdTariffGeneral);
        gwstdTariffGeneral.setUpdateUser(userInfo.getUserNo());
        gwstdTariffGeneral.setUpdateTime(new Date());
        // 更新数据
        int update = gwstdTariffGeneralMapper.updateByPrimaryKey(gwstdTariffGeneral);
        return update > 0 ? gwstdTariffGeneralDtoMapper.toDto(gwstdTariffGeneral) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
		gwstdTariffGeneralMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwstdTariffGeneralDto> selectAll(GwstdTariffGeneralParam exportParam, UserInfoToken userInfo) {
        GwstdTariffGeneral gwstdTariffGeneral = gwstdTariffGeneralDtoMapper.toPo(exportParam);
        // gwstdTariffGeneral.setTradeCode(userInfo.getCompany());
        List<GwstdTariffGeneralDto> gwstdTariffGeneralDtos = new ArrayList<>();
        List<GwstdTariffGeneral> gwstdTariffGenerals = gwstdTariffGeneralMapper.getList(gwstdTariffGeneral);
        if (CollectionUtils.isNotEmpty(gwstdTariffGenerals)) {
            gwstdTariffGeneralDtos = gwstdTariffGenerals.stream().map(head -> {
                GwstdTariffGeneralDto dto = gwstdTariffGeneralDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwstdTariffGeneralDtos;
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<GwstdTariffGeneralDto> list) {
        for(GwstdTariffGeneralDto item : list) {
            Map<String, String> mapTs = pCodeHolder.getTable(PCodeType.COMPLEX,item.getCodeTS()).orElse(null);
            if(!Objects.isNull(mapTs)){
                item.setUnit1(pCodeHolder.getValue(PCodeType.UNIT, mapTs.get("UNIT_1")));
                if(StringUtils.isNotBlank(mapTs.get("UNIT_2"))){
                    item.setUnit2(pCodeHolder.getValue(PCodeType.UNIT,mapTs.get("UNIT_2")));
                }
                if(StringUtils.isNotBlank(mapTs.get("CONTROL_MA"))){
                    item.setControlMa(mapTs.get("CONTROL_MA"));
                }
            }
        }
    }

    /**
     * 税金计算
     *     暂时未用到，先注掉。因表与实体对象不符，无税金相关字估
     * @param erp_no
     * @throws ParseException
     */
    public void rateCalculate(String erp_no) throws ParseException {
//        if (StringUtils.isNotBlank(erp_no)) {
//            DecIEntryHead headPara = new DecIEntryHead();
//            headPara.setErpHeadId(erp_no);
//            //通过提单表头id获取报关单表头
//            List<DecIEntryHead> decIEntryHeads = decIEntryHeadMapper.getList(headPara);
//            if (CollectionUtils.isNotEmpty(decIEntryHeads)) {
//                for (DecIEntryHead decIEntryHead : decIEntryHeads) {
//
//                    //通过报关单表头获取关联表体数据
//                    DecIEntryList listPara = new DecIEntryList();
//                    listPara.setHeadId(decIEntryHead.getSid());
//                    List<DecIEntryList> decIEntryLists = decIEntryListMapper.
//                            selectListByParam(listPara);
//
//                    if (CollectionUtils.isNotEmpty(decIEntryLists)) {
//                        for (DecIEntryList decIEntryList : decIEntryLists) {
//
//                            BigDecimal qty = decIEntryList.getQty();
//                            BigDecimal rateCN = transitionCurr(decIEntryList.getCurr());
//
//                            //获取表体申报总价的RMB金额
//                            BigDecimal listTotal = decIEntryList.getDecTotal().multiply(rateCN);
//
//                            //获取当前商品编码对应的税率数据
//                            GwstdTariffGeneral tariffParam = new GwstdTariffGeneral();
//                            tariffParam.setCountryCode(decIEntryList.getOrigplaceCode());
//                            tariffParam.setCodeTS(decIEntryList.getCodeTS());
//                            List<GwstdTariffGeneral> gwstdTariffGenerals =
//                                    gwstdTariffGeneralMapper.getAllByCodeTs(tariffParam);
//                            //比例因子
//                            BigDecimal scaleFactor = new BigDecimal(1);
//                            MatNonBonded matNonBondedPara = new MatNonBonded();
//                            matNonBondedPara.setCodeTS(decIEntryList.getCodeTS());
//                            List<MatNonBonded> matNonBondeds = matNonBondedMapper.getList(matNonBondedPara);
//                            if(matNonBondeds.size()>0){
//                                for(MatNonBonded matNonBonded : matNonBondeds){
//                                    if(matNonBonded.getFactorQty()!=null&&
//                                            !"0".equals(matNonBonded.getFactorQty())){
//                                        scaleFactor = matNonBonded.getFactorQty();
//                                        break;
//                                    }
//                                }
//                            }
//                            //普通关税税金
//                            BigDecimal tariffTaxes = BigDecimal.ZERO;
//                            //最终普通税率从价定率
//                            BigDecimal tariffRatePerc= BigDecimal.ZERO;
//                            //最终普通税率从量定额
//                            BigDecimal tariffRatePrice = BigDecimal.ZERO;
//                            //判断普通关税率的取值
//                            if(CollectionUtils.isNotEmpty(gwstdTariffGenerals)){
//                                GwstdTariffGeneral rate = gwstdTariffGenerals.get(0);
//                                String impOrdinaryRate = rate.getImpOrdinaryRate();
//                                String impDiscountRate = rate.getImpDiscountRate();
//                                String impTempRate = rate.getImpTempRate();
//                                String impConsumeRate = rate.getImpConsumeRate();
//                                BigDecimal impAgreementRate = rate.getImpAgreementRate();
//                                BigDecimal impConsumeRatePerc = rate.getImpConsumeRatePerc();
//                                if(impConsumeRatePerc!=null){
//                                    impConsumeRatePerc = impConsumeRatePerc.divide(new BigDecimal(100));
//                                }
//                                BigDecimal impConsumeRatePrice = rate.getImpConsumeRatePrice();
//                                BigDecimal addTaxRate = rate.getAddTaxRate();
//                                if(addTaxRate!=null){
//                                    addTaxRate = addTaxRate.divide(new BigDecimal(100));
//                                }
//                                if(StringUtils.isNotBlank(impOrdinaryRate)
//                                        &&impOrdinaryRate.length()>12){
//                                    logger.info("计算预估税金失败:商品编号"+
//                                            decIEntryList.getCodeTS()+"普通税率存在复合税率，不给与运算");
//                                    return;
//                                }else if(StringUtils.isNotBlank(impDiscountRate)
//                                        &&impDiscountRate.length()>12){
//                                    logger.info("计算预估税金失败:商品编号"+
//                                            decIEntryList.getCodeTS()+"最惠国税率存在复合税率，不给与运算");
//                                    return;
//                                }else if(StringUtils.isNotBlank(impTempRate)
//                                        &&impTempRate.length()>12){
//                                    logger.info("计算预估税金失败:商品编号"+
//                                            decIEntryList.getCodeTS()+"暂定税率存在复合税率，不给与运算");
//                                    return;
//                                }else if(StringUtils.isNotBlank(impConsumeRate)
//                                        &&impConsumeRate.length()>12){
//                                    logger.info("计算预估税金失败:商品编号"+
//                                            decIEntryList.getCodeTS()+"消费税税率存在复合税率，不给与运算");
//                                    return;
//                                }else{
//                                    //计算关税
//                                    if(StringUtils.isNotBlank(decIEntryList.getOriginCountry())&&
//                                            decIEntryList.getOriginCountry().equals("701")){
//                                        if(StringUtils.isNotBlank(impOrdinaryRate)){
//                                            tariffRatePerc = rate.getImpOrdinaryRatePerc();
//                                            tariffRatePrice = rate.getImpOrdinaryRatePrice();
//                                            decIEntryList.setTariffRate(impOrdinaryRate);
//                                        }else{
//                                            decIEntryList.setTariffRate(null);
//                                        }
//                                    }else if(StringUtils.isNotBlank(rate.getAgreementName())&&
//                                            rate.getAgreementName().equals("特别协定")){
//                                        if(StringUtils.isNotBlank(impAgreementRate.toString())){
//                                            tariffRatePerc = impAgreementRate;
//                                            decIEntryList.setTariffRate(impAgreementRate+"%");
//                                        }else{
//                                            decIEntryList.setTariffRate(null);
//                                        }
//                                    }else if(StringUtils.isNotBlank(impTempRate)){
//                                        tariffRatePerc = rate.getImpTempRatePerc();
//                                        tariffRatePrice = rate.getImpTempRatePrice();
//                                        decIEntryList.setTariffRate(impTempRate);
//                                    }else if(impAgreementRate!=null){
//                                        tariffRatePerc = impAgreementRate;
//                                        decIEntryList.setTariffRate(impAgreementRate.toString());
//                                    }else if(StringUtils.isNotBlank(impDiscountRate)){
//                                        tariffRatePerc = rate.getImpDiscountRatePerc();
//                                        tariffRatePrice = rate.getImpDiscountRatePrice();
//                                        decIEntryList.setTariffRate(impDiscountRate);
//                                    }
//                                    //从价定率不为0的时候
//                                    if(tariffRatePerc.compareTo(BigDecimal.ZERO)!=0){
//                                        tariffTaxes = tariffTaxes.add(listTotal.
//                                                multiply(tariffRatePerc.divide(new BigDecimal(100)))).
//                                                setScale(2,BigDecimal.ROUND_HALF_UP);
//                                    }
//                                    if(tariffRatePrice.compareTo(BigDecimal.ZERO)!=0){
//                                        tariffTaxes = tariffTaxes.add(tariffRatePrice.
//                                                multiply(scaleFactor).multiply(qty).multiply(rateCN)).
//                                                setScale(2,BigDecimal.ROUND_HALF_UP);;
//                                    }
//                                    decIEntryList.setTariffTaxes(tariffTaxes);
//
//                                    //计算消费税
//                                    BigDecimal consumeTaxes = new BigDecimal(0);
//                                    if(impConsumeRatePerc.compareTo(BigDecimal.ZERO)!=0){
//                                        BigDecimal consumeRateTaxes = listTotal.add(tariffTaxes).
//                                                divide(new BigDecimal(1).subtract(impConsumeRatePerc)).
//                                                multiply(impConsumeRatePerc).setScale(2,BigDecimal.ROUND_HALF_UP);
//                                        consumeTaxes = consumeTaxes.add(consumeRateTaxes);
//                                    }
//                                    if(impConsumeRatePrice.compareTo(BigDecimal.ZERO)!=0){
//                                        consumeTaxes = consumeTaxes  .add(impConsumeRatePrice.
//                                                multiply(rateCN).multiply(scaleFactor).multiply(qty)).
//                                                setScale(2,BigDecimal.ROUND_HALF_UP);
//                                    }
//                                    decIEntryList.setConsumeRate(impConsumeRate);
//                                    decIEntryList.setConsumeTaxes(consumeTaxes);
//                                    //计算增值税
//                                    BigDecimal addTaxes = new BigDecimal(0);
//                                    if(io.micrometer.core.instrument.util.StringUtils.isNotBlank(rate.getAddTaxRate().toString())){
//                                        if(addTaxRate.compareTo(BigDecimal.ZERO)!=0){
//                                            addTaxes = listTotal.add(tariffTaxes).add(consumeTaxes).
//                                                    multiply(addTaxRate).setScale(2,BigDecimal.ROUND_HALF_UP);
//                                        }
//                                    }else{
//                                        decIEntryList.setAddTaxRate(null);
//                                    }
//                                    decIEntryList.setAddTaxes(addTaxes);
//                                    decIEntryListMapper.updateByPrimaryKey(decIEntryList);
//                                }
//                            }else{
//                                logger.info("计算预估税金失败：商品编码"+decIEntryList.getCodeTS()+"无对应税率");
//                            }
//                        }
//                    } else {
//                        logger.info("计算预估税金失败:提单表头" +
//                                decIEntryHead.getSid() + "关联报关单表体为空");
//                    }
//                }
//            } else {
//                logger.info("计算预估税金失败:提单表体" + erp_no + "关联报关单表头不存在");
//            }
//        } else {
//            logger.info("计算预估税金失败，提单表体id为空");
//        }
    }

    /**
     * 币制转换
     */
    public BigDecimal transitionCurr(String curr) {
        BigDecimal currRate = BigDecimal.ONE;
        ExchangeRate exchangeRate = new ExchangeRate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH,-1);
        exchangeRate.setCurrDate(sdf.format(new Date(calendar.getTimeInMillis())));
        exchangeRate.setCurrCodeNum(curr);
        List<ExchangeRate> exchangeRates = exchangeRateMapper.getList(exchangeRate);
        if(exchangeRates.size()>0){
            currRate = exchangeRates.get(0).getCurrPriceRmb();
        }
        return currRate;
    }
}
