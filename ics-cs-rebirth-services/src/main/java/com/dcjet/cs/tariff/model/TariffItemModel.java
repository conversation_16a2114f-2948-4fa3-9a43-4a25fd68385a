package com.dcjet.cs.tariff.model;

import lombok.Data;
import xdo.interceptor.decimal.RemoveTailingZero;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@RemoveTailingZero
public class TariffItemModel implements Serializable {

    public enum OperatorEnum {
        // 小于
        less,
        // 不高于
        lessEqual,
        // 高于
        greater,
        // 大于等于
        greaterEqual
    }

    private OperatorEnum operatorEnum;
    /***
     * 税率的阈值
     */
    private BigDecimal threshold;
    /***
     * 税率
     */
    private BigDecimal rate;
    /***
     * 
     */
    private BigDecimal price;
    /**
     * 单价对应的币制名称
     */
    private String unit;
}
