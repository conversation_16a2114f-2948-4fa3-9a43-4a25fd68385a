package com.dcjet.cs.eml.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2023-2-27
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "t_gw_eml_balance")
public class GwEmlBalance implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 唯一键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 企业代码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 插入时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 插入用户
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 制单人姓名
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 更新用户
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 修改人姓名
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 手册编号
     */
	@Column(name = "ems_no")
	private  String emsNo;
	/**
     * 进出口标记
     */
	@Column(name = "i_e_mark")
	private  String iemark;
	/**
     * 监管方式
     */
	@Column(name = "trade_mode")
	private  String tradeMode;
	/**
	 * 成品序号
	 */
	@Column(name = "exg_g_no")
	private  BigDecimal exgGNo;
	/**
	 * 成品料号
	 */
	@Column(name = "exg_cop_g_no")
	private  String exgCopGNo;
	/**
     * 料件序号
     */
	@Column(name = "img_g_no")
	private  BigDecimal imgGNo;
	/**
     * 料件料号
     */
	@Column(name = "img_cop_g_no")
	private  String imgCopGNo;
	/**
     * 成品商品编码
     */
	@Column(name = "exg_code_t_s")
	private  String exgCodeTS;
	/**
     * 成品商品名称
     */
	@Column(name = "exg_g_name")
	private  String exgGName;
	/**
     * 成品计量单位
     */
	@Column(name = "exg_unit")
	private  String exgUnit;
	/**
     * 料件商品编码
     */
	@Column(name = "img_code_t_s")
	private  String imgCodeTS;
	/**
     * 料件商品名称
     */
	@Column(name = "img_g_name")
	private  String imgGName;
	/**
     * 料件计量单位
     */
	@Column(name = "img_unit")
	private  String imgUnit;
	/**
     * 申报数量
     */
	@Column(name = "qty")
	private  BigDecimal qty;
	/**
     * 清单内部编号
     */
	@Column(name = "ems_list_no")
	private  String emsListNo;
	/**
     * 提单内部编号
     */
	@Column(name = "erp_ems_list_no")
	private  String erpEmsListNo;
	/**
     * 提单创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "erp_insert_time")
	private  Date erpInsertTime;
	/**
     * 是否关联单损耗 0 否 1 是
     */
	@Column(name = "is_bom")
	private  String isBom;
	/**
     * 净耗
     */
	@Column(name = "dec_cm")
	private  BigDecimal decCm;
	/**
     * 有形损耗率%
     */
	@Column(name = "dec_dm_visiable")
	private  BigDecimal decDmVisiable;
	/**
     * 无形损耗率%
     */
	@Column(name = "dec_dm_invisiable")
	private  BigDecimal decDmInvisiable;
	/**
     * 保税料件比例%
     */
	@Column(name = "bond_mtpck_prpr")
	private  BigDecimal bondMtpckPrpr;
	/**
     * 工艺损耗
     */
	@Column(name = "loss_rate")
	private  BigDecimal lossRate;
	/**
     * 出口耗用
     */
	@Column(name = "exp_direct_con")
	private  BigDecimal expDirectCon;
	/**
     * 保税出口耗用
     */
	@Column(name = "exp_direct_con_bond")
	private  BigDecimal expDirectConBond;
	/**
     * 非保税出口耗用
     */
	@Column(name = "exp_direct_con_nobond")
	private  BigDecimal expDirectConNobond;
	@Transient
	private  String theorySurplus;
	/**
	 * 监管方式类型
	 */
	@Transient
	private  String tradeType;

}
