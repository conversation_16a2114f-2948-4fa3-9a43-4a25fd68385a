<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.eml.dao.GwEmlBalanceMapper">
    <resultMap id="gwEmlBalanceResultMap" type="com.dcjet.cs.eml.model.GwEmlBalance">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="ems_no" property="emsNo" jdbcType="VARCHAR" />
		<result column="i_e_mark" property="iemark" jdbcType="VARCHAR" />
		<result column="trade_mode" property="tradeMode" jdbcType="VARCHAR" />
		<result column="exg_g_no" property="exgGNo" jdbcType="NUMERIC" />
		<result column="exg_cop_g_no" property="exgCopGNo" jdbcType="VARCHAR" />
		<result column="img_g_no" property="imgGNo" jdbcType="NUMERIC" />
		<result column="img_cop_g_no" property="imgCopGNo" jdbcType="VARCHAR" />
		<result column="exg_code_t_s" property="exgCodeTS" jdbcType="VARCHAR" />
		<result column="exg_g_name" property="exgGName" jdbcType="VARCHAR" />
		<result column="exg_unit" property="exgUnit" jdbcType="VARCHAR" />
		<result column="img_code_t_s" property="imgCodeTS" jdbcType="VARCHAR" />
		<result column="img_g_name" property="imgGName" jdbcType="VARCHAR" />
		<result column="img_unit" property="imgUnit" jdbcType="VARCHAR" />
		<result column="qty" property="qty" jdbcType="NUMERIC" />
		<result column="ems_list_no" property="emsListNo" jdbcType="VARCHAR" />
		<result column="erp_ems_list_no" property="erpEmsListNo" jdbcType="VARCHAR" />
		<result column="erp_insert_time" property="erpInsertTime" jdbcType="TIMESTAMP" />
		<result column="is_bom" property="isBom" jdbcType="VARCHAR" />
		<result column="dec_cm" property="decCm" jdbcType="NUMERIC" />
		<result column="dec_dm_visiable" property="decDmVisiable" jdbcType="NUMERIC" />
		<result column="dec_dm_invisiable" property="decDmInvisiable" jdbcType="NUMERIC" />
		<result column="bond_mtpck_prpr" property="bondMtpckPrpr" jdbcType="NUMERIC" />
		<result column="loss_rate" property="lossRate" jdbcType="NUMERIC" />
		<result column="exp_direct_con" property="expDirectCon" jdbcType="NUMERIC" />
		<result column="exp_direct_con_bond" property="expDirectConBond" jdbcType="NUMERIC" />
		<result column="exp_direct_con_nobond" property="expDirectConNobond" jdbcType="NUMERIC" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,trade_code
     ,insert_time
     ,insert_user
     ,insert_user_name
     ,update_time
     ,update_user
     ,update_user_name
     ,ems_no
     ,i_e_mark
     ,trade_mode
     ,exg_g_no
     ,exg_cop_g_no
     ,img_g_no
     ,img_cop_g_no
     ,exg_code_t_s
     ,exg_g_name
     ,exg_unit
     ,img_code_t_s
     ,img_g_name
     ,img_unit
     ,qty
     ,ems_list_no
     ,erp_ems_list_no
     ,erp_insert_time
     ,is_bom
     ,dec_cm
     ,dec_dm_visiable
     ,dec_dm_invisiable
     ,bond_mtpck_prpr
     ,loss_rate
     ,exp_direct_con
     ,exp_direct_con_bond
     ,exp_direct_con_nobond
    </sql>
    <sql id="condition">
        and trade_code = #{tradeCode}
        <if test="emsNo != null and emsNo !=''">
            and ems_no = #{emsNo}
        </if>
        <if test="imgGNo != null and imgGNo !=''">
            and img_g_no = #{imgGNo}
        </if>
        <if test="imgCopGNo != null and imgCopGNo !=''">
            and img_cop_g_no like concat(concat('%', #{imgCopGNo}),'%')
        </if>
        <if test="imgCodeTS != null and imgCodeTS !=''">
            and img_code_t_s like concat(concat('%', #{imgCodeTS}),'%')
        </if>
        <if test="imgGName != null and imgGName !=''">
            and img_g_name like concat(concat('%', #{imgGName}),'%')
        </if>
        <if test="exgGNo != null and exgGNo !=''">
            and exg_g_no = #{exgGNo}
        </if>
        <if test="exgCopGNo != null and exgCopGNo !=''">
            and exg_cop_g_no like concat(concat('%', #{exgCopGNo}),'%')
        </if>
        <if test="exgCodeTS != null and exgCodeTS !=''">
            and exg_code_t_s like concat(concat('%', #{exgCodeTS}),'%')
        </if>
        <if test="exgGName != null and exgGName !=''">
            and exg_g_name like concat(concat('%', #{exgGName}),'%')
        </if>
        <choose>
            <when test='iemark=="I" and tradeType=="qtyTm1"'>
                and i_e_mark = 'I' and trade_mode in ('0615','0715','1371','0214')
            </when>
            <when test='iemark=="I" and tradeType=="qtyTm2"'>
                and i_e_mark = 'I' and trade_mode in ('0654','0255')
            </when>
            <when test='iemark=="I" and tradeType=="qtyTm3"'>
                and i_e_mark = 'I' and trade_mode in ('0657','0258')
            </when>
            <when test='iemark=="I" and tradeType=="qtyTm4"'>
                and i_e_mark = 'I' and trade_mode in ('0700','0300')
            </when>
            <when test='iemark=="I" and tradeType=="qtyTm5"'>
                and i_e_mark = 'IE' and trade_mode in ('0700','0300')
            </when>
            <when test='iemark=="I" and tradeType=="qtyTm6"'>
                and i_e_mark = 'I' and trade_mode in ('0644','0245','9700')
            </when>
            <when test='iemark=="I" and tradeType=="qtyTm7"'>
                and i_e_mark = 'IE' and trade_mode in ('0664','0265')
            </when>
            <when test='iemark=="I" and tradeType=="qtyTm8"'>
                and i_e_mark = 'IE' and trade_mode in ('0657','0258')
            </when>
            <when test='iemark=="I" and tradeType=="qtyTm9"'>
                and i_e_mark = 'I' and trade_mode ='0200'
            </when>

            <when test='iemark=="E" and tradeType=="qtyTm1"'>
                and i_e_mark = 'E' and trade_mode in ('0615','0715','1371','0214')
            </when>
            <when test='iemark=="E" and tradeType=="qtyTm2"'>
                and i_e_mark = 'E' and trade_mode in ('0654','0255')
            </when>
            <when test='iemark=="E" and tradeType=="qtyTm3"'>
                and i_e_mark = 'E' and trade_mode in ('4600','4400')
            </when>
            <when test='iemark=="E" and tradeType=="qtyTm4"'>
                and i_e_mark = 'EI' and trade_mode in ('4600','4400')
            </when>
        </choose>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwEmlBalanceResultMap" parameterType="com.dcjet.cs.eml.model.GwEmlBalance">
        SELECT
        trade_code
        ,ems_no
        ,i_e_mark
        ,trade_mode
        ,exg_g_no
        ,exg_cop_g_no
        ,img_g_no
        ,img_cop_g_no
        ,qty
        ,ems_list_no
        ,erp_ems_list_no
        ,erp_insert_time
        ,insert_time
        FROM
        (select insert_time,i_e_mark,trade_code,ems_no,img_g_no,img_cop_g_no,null::numeric exg_g_no,null exg_cop_g_no,ems_list_no,erp_ems_list_no,erp_insert_time,trade_mode,qty,out_list_sid from t_gw_eml_balance where i_e_mark in ('I','IE')
        union
        select distinct insert_time,i_e_mark,trade_code,ems_no,null::numeric img_g_no,null img_cop_g_no,exg_g_no,exg_cop_g_no,ems_list_no,erp_ems_list_no,erp_insert_time,trade_mode,qty,out_list_sid from t_gw_eml_balance where i_e_mark in ('E','EI')
        ) t
        <where>
            <include refid="condition"></include>
        </where>
        order by erp_insert_time desc,erp_ems_list_no desc
    </select>
    <select id="getBalanceIn" parameterType="com.dcjet.cs.eml.model.GwEmlBalance"
            resultType="com.dcjet.cs.eml.model.GwBalanceList">
        select * from (select a.*,
        coalesce(qty_tm1,0)+coalesce(qty_tm2,0)+coalesce(qty_tm3,0)+coalesce(qty_tm4,0)-coalesce(qty_tm5,0)-coalesce(qty_tm6,0)-coalesce(qty_tm7,0)-coalesce(qty_tm8,0)-coalesce(qty_tm9,0) as qty_total,
        coalesce(qty_tm1,0)+coalesce(qty_tm2,0)+coalesce(qty_tm3,0)+coalesce(qty_tm4,0)-coalesce(qty_tm5,0)-coalesce(qty_tm6,0)-coalesce(qty_tm7,0)-coalesce(qty_tm8,0)-coalesce(qty_tm9,0)-coalesce(exp_direct_con_bond,0) as theory_surplus
        from (SELECT
        DISTINCT
        eml.trade_code,
        'I' as i_e_mark,
        eml.ems_no,
        eml.img_g_no,
        eml.img_cop_g_no,
        eml.img_code_t_s,
        eml.img_g_name,
        eml.img_unit,
        eml.insert_time,
        tm1.qty qty_tm1,
        tm2.qty qty_tm2,
        tm3.qty qty_tm3,
        tm4.qty qty_tm4,
        tm5.qty qty_tm5,
        tm6.qty qty_tm6,
        tm7.qty qty_tm7,
        tm8.qty qty_tm8,
        tm9.qty qty_tm9,
        exp.exp_direct_con_bond
        FROM
        t_gw_eml_balance eml
        left join (select ems_no,img_g_no,img_cop_g_no,sum(qty) qty from t_gw_eml_balance where i_e_mark='I' and trade_code=#{tradeCode} and trade_mode in ('0615','0715','1371','0214') group by ems_no,img_g_no,img_cop_g_no) tm1 on tm1.ems_no=eml.ems_no and tm1.img_g_no=eml.img_g_no and tm1.img_cop_g_no=eml.img_cop_g_no
        left join (select ems_no,img_g_no,img_cop_g_no,sum(qty) qty from t_gw_eml_balance where i_e_mark='I' and trade_code=#{tradeCode} and trade_mode in ('0654','0255') group by ems_no,img_g_no,img_cop_g_no) tm2 on tm2.ems_no=eml.ems_no and tm2.img_g_no=eml.img_g_no and tm2.img_cop_g_no=eml.img_cop_g_no
        left join (select ems_no,img_g_no,img_cop_g_no,sum(qty) qty from t_gw_eml_balance where i_e_mark='I' and trade_code=#{tradeCode} and trade_mode in ('0657','0258') group by ems_no,img_g_no,img_cop_g_no) tm3 on tm3.ems_no=eml.ems_no and tm3.img_g_no=eml.img_g_no and tm3.img_cop_g_no=eml.img_cop_g_no
        left join (select ems_no,img_g_no,img_cop_g_no,sum(qty) qty from t_gw_eml_balance where i_e_mark='I' and trade_code=#{tradeCode} and trade_mode in ('0700','0300') group by ems_no,img_g_no,img_cop_g_no) tm4 on tm4.ems_no=eml.ems_no and tm4.img_g_no=eml.img_g_no and tm4.img_cop_g_no=eml.img_cop_g_no
        left join (select ems_no,img_g_no,img_cop_g_no,sum(qty) qty from t_gw_eml_balance where i_e_mark='IE' and trade_code=#{tradeCode} and trade_mode in ('0700','0300') group by ems_no,img_g_no,img_cop_g_no) tm5 on tm5.ems_no=eml.ems_no and tm5.img_g_no=eml.img_g_no and tm5.img_cop_g_no=eml.img_cop_g_no
        left join (select ems_no,img_g_no,img_cop_g_no,sum(qty) qty from t_gw_eml_balance where i_e_mark='I' and trade_code=#{tradeCode} and trade_mode in ('0644','9700','0245') group by ems_no,img_g_no,img_cop_g_no) tm6 on tm6.ems_no=eml.ems_no and tm6.img_g_no=eml.img_g_no and tm6.img_cop_g_no=eml.img_cop_g_no
        left join (select ems_no,img_g_no,img_cop_g_no,sum(qty) qty from t_gw_eml_balance where i_e_mark='IE' and trade_code=#{tradeCode} and trade_mode in ('0664','0265') group by ems_no,img_g_no,img_cop_g_no) tm7 on tm7.ems_no=eml.ems_no and tm7.img_g_no=eml.img_g_no and tm7.img_cop_g_no=eml.img_cop_g_no
        left join (select ems_no,img_g_no,img_cop_g_no,sum(qty) qty from t_gw_eml_balance where i_e_mark='IE' and trade_code=#{tradeCode} and trade_mode in ('0657','0258') group by ems_no,img_g_no,img_cop_g_no) tm8 on tm8.ems_no=eml.ems_no and tm8.img_g_no=eml.img_g_no and tm8.img_cop_g_no=eml.img_cop_g_no
        left join (select ems_no,img_g_no,img_cop_g_no,sum(qty) qty from t_gw_eml_balance where i_e_mark='I' and trade_code=#{tradeCode} and trade_mode ='0200' group by ems_no,img_g_no,img_cop_g_no) tm9 on tm9.ems_no=eml.ems_no and tm9.img_g_no=eml.img_g_no and tm9.img_cop_g_no=eml.img_cop_g_no
        left join (select ems_no,img_g_no,img_cop_g_no,sum(exp_direct_con_bond) exp_direct_con_bond from (select distinct out_list_sid,ems_no,img_g_no,img_cop_g_no,case when i_e_mark='E' then exp_direct_con_bond else -1*exp_direct_con_bond end exp_direct_con_bond from
        t_gw_eml_balance where i_e_mark in ('E','EI') and trade_code=#{tradeCode}) Z group by ems_no,img_g_no,img_cop_g_no) exp on exp.ems_no=eml.ems_no and exp.img_g_no=eml.img_g_no and exp.img_cop_g_no=eml.img_cop_g_no
        <where>
            and trade_code = #{tradeCode}
            and i_e_mark in('I','IE')
        </where>) a) M
        <where>

            <choose>
                <when test='theorySurplus=="1".toString()'>
                    and theory_surplus>0
                </when>
                <when test='theorySurplus=="2".toString()'>
                    and theory_surplus=0
                </when>
                <when test='theorySurplus=="3".toString()'>
                    and 0>theory_surplus
                </when>
            </choose>
            <if test="emsNo != null and emsNo !=''">
                and ems_no = #{emsNo}
            </if>
            <if test="imgGNo != null and imgGNo !=''">
                and img_g_no = #{imgGNo}
            </if>
            <if test="imgCopGNo != null and imgCopGNo !=''">
                and img_cop_g_no like concat(concat('%', #{imgCopGNo}),'%')
            </if>
            <if test="imgCodeTS != null and imgCodeTS !=''">
                and img_code_t_s like concat(concat('%', #{imgCodeTS}),'%')
            </if>
            <if test="imgGName != null and imgGName !=''">
                and img_g_name like concat(concat('%', #{imgGName}),'%')
            </if>
        </where>
        order by ems_no desc,img_g_no
    </select>
    <select id="getBalanceOut" parameterType="com.dcjet.cs.eml.model.GwEmlBalance"
            resultType="com.dcjet.cs.eml.model.GwBalanceList">
        select a.*,
        coalesce(qty_tm1,0)+coalesce(qty_tm2,0)+coalesce(qty_tm3,0)-coalesce(qty_tm4,0) as qty_total from(SELECT
        DISTINCT
        eml.trade_code,
        'E' as i_e_mark,
        eml.ems_no,
        eml.exg_g_no,
        eml.exg_cop_g_no,
        eml.exg_code_t_s,
        eml.exg_g_name,
        eml.exg_unit,
        eml.insert_time,
        tm1.qty qty_tm1,
        tm2.qty qty_tm2,
        tm3.qty qty_tm3,
        tm4.qty qty_tm4
        FROM
        t_gw_eml_balance eml
        left join (select ems_no,exg_g_no,exg_cop_g_no,sum(qty) qty from (select distinct out_list_sid,ems_no,exg_g_no,exg_cop_g_no,qty from t_gw_eml_balance where i_e_mark='E' and trade_code=#{tradeCode} and trade_mode in ('0615','0715','1371','0214')) a group by ems_no,exg_g_no,exg_cop_g_no) tm1 on tm1.ems_no=eml.ems_no and tm1.exg_g_no=eml.exg_g_no and tm1.exg_cop_g_no=eml.exg_cop_g_no
        left join (select ems_no,exg_g_no,exg_cop_g_no,sum(qty) qty from (select distinct out_list_sid,ems_no,exg_g_no,exg_cop_g_no,qty from t_gw_eml_balance where i_e_mark='E' and trade_code=#{tradeCode} and trade_mode in ('0654','0255')) b group by ems_no,exg_g_no,exg_cop_g_no) tm2 on tm2.ems_no=eml.ems_no and tm2.exg_g_no=eml.exg_g_no and tm2.exg_cop_g_no=eml.exg_cop_g_no
        left join (select ems_no,exg_g_no,exg_cop_g_no,sum(qty) qty from (select distinct out_list_sid,ems_no,exg_g_no,exg_cop_g_no,qty from t_gw_eml_balance where i_e_mark='E' and trade_code=#{tradeCode} and trade_mode in ('4600','4400')) c group by ems_no,exg_g_no,exg_cop_g_no) tm3 on tm3.ems_no=eml.ems_no and tm3.exg_g_no=eml.exg_g_no and tm3.exg_cop_g_no=eml.exg_cop_g_no
        left join (select ems_no,exg_g_no,exg_cop_g_no,sum(qty) qty from (select distinct out_list_sid,ems_no,exg_g_no,exg_cop_g_no,qty from t_gw_eml_balance where i_e_mark='EI' and trade_code=#{tradeCode} and trade_mode in ('4600','4400')) d	group by ems_no,exg_g_no,exg_cop_g_no) tm4 on tm4.ems_no=eml.ems_no and tm4.exg_g_no=eml.exg_g_no and tm4.exg_cop_g_no=eml.exg_cop_g_no
        <where>
        and trade_code = #{tradeCode}
        and i_e_mark in('E','EI')
        </where>) a
        <where>
            <if test="emsNo != null and emsNo !=''">
                and ems_no = #{emsNo}
            </if>
            <if test="exgGNo != null and exgGNo !=''">
                and exg_g_no = #{exgGNo}
            </if>
            <if test="exgCopGNo != null and exgCopGNo !=''">
                and exg_cop_g_no like concat(concat('%', #{exgCopGNo}),'%')
            </if>
            <if test="exgCodeTS != null and exgCodeTS !=''">
                and exg_code_t_s like concat(concat('%', #{exgCodeTS}),'%')
            </if>
            <if test="exgGName != null and exgGName !=''">
                and exg_g_name like concat(concat('%', #{exgGName}),'%')
            </if>
        </where>
        order by ems_no desc,exg_g_no
    </select>
    <select id="getBalanceListOut" parameterType="com.dcjet.cs.eml.model.GwEmlBalance"
            resultType="com.dcjet.cs.eml.model.GwEmlBalance">
        select
        eml.insert_time,
        eml.trade_code,
        eml.ems_no,
        eml.exg_g_no,
        eml.exg_cop_g_no,
        eml.img_g_no,
        eml.img_cop_g_no,
        eml.exg_code_t_s,
        eml.exg_g_name,
        eml.exg_unit,
        eml.img_code_t_s,
        eml.img_g_name,
        eml.img_unit,
        l.qty,
        eml.is_bom,
        eml.dec_cm,
        eml.dec_dm_visiable,
        eml.dec_dm_invisiable,
        eml.bond_mtpck_prpr,
        l.loss_rate,
        l.exp_direct_con,
        l.exp_direct_con_bond,
        l.exp_direct_con_nobond
        from (select max(insert_time) insert_time,
        trade_code,
        ems_no,
        exg_g_no,
        exg_cop_g_no,
        img_g_no,
        img_cop_g_no,
        exg_code_t_s,
        exg_g_name,
        exg_unit,
        img_code_t_s,
        img_g_name,
        img_unit,
        is_bom,
        dec_cm,
        dec_dm_visiable,
        dec_dm_invisiable,
        bond_mtpck_prpr from t_gw_eml_balance
        <where>
            and i_e_mark in('E','EI')
            and trade_code=#{tradeCode}
            <if test="emsNo != null and emsNo !=''">
                and ems_no = #{emsNo}
            </if>
            <if test="imgGNo != null and imgGNo !=''">
                and img_g_no = #{imgGNo}
            </if>
            <if test="imgCopGNo != null and imgCopGNo !=''">
                and img_cop_g_no like concat(concat('%', #{imgCopGNo}),'%')
            </if>
            <if test="imgCodeTS != null and imgCodeTS !=''">
                and img_code_t_s like concat(concat('%', #{imgCodeTS}),'%')
            </if>
            <if test="imgGName != null and imgGName !=''">
                and img_g_name like concat(concat('%', #{imgGName}),'%')
            </if>
            <if test="exgGNo != null and exgGNo !=''">
                and exg_g_no = #{exgGNo}
            </if>
            <if test="exgCopGNo != null and exgCopGNo !=''">
                and exg_cop_g_no like concat(concat('%', #{exgCopGNo}),'%')
            </if>
            <if test="exgCodeTS != null and exgCodeTS !=''">
                and exg_code_t_s like concat(concat('%', #{exgCodeTS}),'%')
            </if>
            <if test="exgGName != null and exgGName !=''">
                and exg_g_name like concat(concat('%', #{exgGName}),'%')
            </if>
            <if test="isBom != null and isBom !=''">
                and is_bom =#{isBom}
            </if>
        </where>
        group by
        trade_code,
        ems_no,
        exg_g_no,
        exg_cop_g_no,
        img_g_no,
        img_cop_g_no,
        exg_code_t_s,
        exg_g_name,
        exg_unit,
        img_code_t_s,
        img_g_name,
        img_unit,
        is_bom,
        dec_cm,
        dec_dm_visiable,
        dec_dm_invisiable,
        bond_mtpck_prpr) eml
        left join (select ems_no,exg_g_no,exg_cop_g_no,img_g_no,sum(case when i_e_mark='E' then qty else -1*qty end) qty,
                          sum(case when i_e_mark='E' then loss_rate else -1*loss_rate end) loss_rate,
                          sum(case when i_e_mark='E' then exp_direct_con else -1*exp_direct_con end) exp_direct_con,
                          sum(case when i_e_mark='E' then exp_direct_con_bond else -1*exp_direct_con_bond end) exp_direct_con_bond,
                          sum(case when i_e_mark='E' then exp_direct_con_nobond else -1*exp_direct_con_nobond end) exp_direct_con_nobond from
        (select distinct out_list_sid,i_e_mark,ems_no,exg_g_no,exg_cop_g_no,img_g_no,qty,loss_rate,exp_direct_con,exp_direct_con_bond,exp_direct_con_nobond from t_gw_eml_balance where i_e_mark in('E','EI')) a  group by ems_no,exg_g_no,exg_cop_g_no,img_g_no) l
        on l.ems_no=eml.ems_no and l.exg_g_no=eml.exg_g_no and l.exg_cop_g_no=eml.exg_cop_g_no

        <if test='_databaseId == "postgresql" '>
               and case when is_bom='1' then l.img_g_no = eml.img_g_no else 1=1 end
        </if>
        <if test='_databaseId != "postgresql" '>
            and (( is_bom = '1' and l.img_g_no = eml.img_g_no) or (is_bom != '1'))
        </if>
        order by eml.ems_no desc,eml.exg_g_no,eml.img_g_no
    </select>
    <insert id="insertEmlBalance">
        delete from t_gw_eml_balance
        where trade_code=#{tradeCode}
        and ems_no=#{emsNo};

        /*进口清单、料件退换、料件复出、余料结转（出口）*/
        insert into t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, img_g_no, img_cop_g_no, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time)
        select
            sys_guid(),
            #{tradeCode},
            current_timestamp,
            'system',
            'system',
            head.ems_no,
            'IE',
            head.trade_mode,
            list.g_no,
            list.cop_g_no,
            list.code_t_s,
            list.g_name,
            list.unit,
            sum ( list.qty ) qty,
            head.ems_list_no,
            erp.ems_list_no as erp_ems_list_no,
            erp.insert_time as erp_insert_time
        from
            t_dec_e_bill_list list
                left join t_dec_e_bill_head head on head.sid = list.head_id
                left join t_dec_erp_e_head_n erp on head.head_id = erp.sid
        where
            erp.sid is not null
          and head.trade_code=#{tradeCode}
          and head.ems_no=#{emsNo}
          and head.trade_mode in ('0700','0300','0664','0265','0657','0258')
        group by
            erp.ems_list_no,
            erp.insert_time,
            head.ems_list_no,
            head.trade_mode,
            head.ems_no,
            list.g_no,
            list.cop_g_no,
            list.g_name,
            list.code_t_s,
            list.unit;
        /*进口预录入、料件退换、料件复出、余料结转（出口）*/
        insert into t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, img_g_no, img_cop_g_no, img_code_t_s, img_g_name, img_unit, qty, erp_ems_list_no, erp_insert_time)
        select
            sys_guid(),
            #{tradeCode},
            current_timestamp,
            'system',
            'system',
            list.ems_no,
            'IE',
            list.trade_mode,
            list.g_no,
            list.cop_g_no,
            list.code_t_s,
            list.g_name,
            list.unit,
            sum ( list.qty ) qty,
            erp.ems_list_no as erp_ems_list_no,
            erp.insert_time as erp_insert_time
        from
            t_dec_erp_e_list_n list
                left join t_dec_erp_e_head_n erp on list.head_id = erp.sid
        where
            erp.appr_status='0'
          and list.trade_code=#{tradeCode}
          and list.ems_no=#{emsNo}
          and list.trade_mode in ('0700','0300','0664','0265','0657','0258')
        group by
            erp.ems_list_no,
            erp.insert_time,
            list.ems_list_no,
            list.trade_mode,
            list.ems_no,
            list.g_no,
            list.cop_g_no,
            list.g_name,
            list.code_t_s,
            list.unit;

        /*进口清单*/
        insert into t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, img_g_no, img_cop_g_no, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time)
        select
            sys_guid(),
            #{tradeCode},
            current_timestamp,
            'system',
            'system',
            head.ems_no,
            'I',
            head.trade_mode,
            list.g_no,
            list.cop_g_no,
            list.code_t_s,
            list.g_name,
            list.unit,
            sum ( list.qty ) qty,
            head.ems_list_no,
            erp.ems_list_no as erp_ems_list_no,
            erp.insert_time as erp_insert_time
        from
            t_dec_i_bill_list list
                left join t_dec_i_bill_head head on head.sid = list.head_id
                left join t_dec_erp_i_head_n erp on head.head_id = erp.sid
        where
            erp.sid is not null
          and head.trade_code=#{tradeCode}
          and head.ems_no=#{emsNo}
          and head.trade_mode in ('0615','0715','1371','0214','0654','0255','0657','0258','0700','0300','0644','9700','0245','0200')
        group by
            erp.ems_list_no,
            erp.insert_time,
            head.ems_list_no,
            head.trade_mode,
            head.ems_no,
            list.g_no,
            list.cop_g_no,
            list.g_name,
            list.code_t_s,
            list.unit;
        /*进口预录入*/
        insert into t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, img_g_no, img_cop_g_no, img_code_t_s, img_g_name, img_unit, qty, erp_ems_list_no, erp_insert_time)
        select
            sys_guid(),
            #{tradeCode},
            current_timestamp,
            'system',
            'system',
            list.ems_no,
            'I',
            list.trade_mode,
            list.g_no,
            list.cop_g_no,
            list.code_t_s,
            list.g_name,
            list.unit,
            sum ( list.qty ) qty,
            erp.ems_list_no as erp_ems_list_no,
            erp.insert_time as erp_insert_time
        from
            t_dec_erp_i_list_n list
                left join t_dec_erp_i_head_n erp on list.head_id = erp.sid
        where
            erp.appr_status='0'
          and list.trade_code=#{tradeCode}
          and list.ems_no=#{emsNo}
          and list.trade_mode in ('0615','0715','1371','0214','0654','0255','0657','0258','0700','0300','0644','9700','0245','0200')
        group by
            erp.ems_list_no,
            erp.insert_time,
            list.ems_list_no,
            list.trade_mode,
            list.ems_no,
            list.g_no,
            list.cop_g_no,
            list.g_name,
            list.code_t_s,
            list.unit;

        /*出口清单、成品退换（进口）*/
        INSERT INTO t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no, img_g_no, img_cop_g_no,
                                      exg_code_t_s, exg_g_name, exg_unit, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time, is_bom,
                                      dec_cm, dec_dm_visiable, dec_dm_invisiable, bond_mtpck_prpr,loss_rate,exp_direct_con,exp_direct_con_bond,exp_direct_con_nobond,out_list_sid)
        SELECT
            sys_guid(),
            #{tradeCode},
            CURRENT_TIMESTAMP,
            'system',
            'system',
            head.ems_no,
            'EI',
            head.trade_mode,
            list.g_no,
            list.cop_g_no,
            consume.img_g_no,
            consume.cop_img_no,
            list.code_t_s,
            list.g_name,
            list.unit,
            org.code_t_s,
            org.g_name,
            org.unit,
            sum(list.qty) qty,
            head.ems_list_no,
            erp.ems_list_no AS erp_ems_list_no,
            erp.insert_time AS erp_insert_time,
            case when consume.sid is null then '0' else '1' end is_bom,
            consume.dec_cm,
            consume.dec_dm_visiable,
            consume.dec_dm_invisiable,
            consume.bond_mtpck_prpr,
--             sum(list.qty)*consume.dec_cm*(consume.dec_dm_visiable+consume.dec_dm_invisiable)/100,
--             sum(list.qty)*consume.dec_cm,
--             sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,
--             sum(list.qty)*consume.dec_cm-sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * consume.bond_mtpck_prpr*0.01 * (consume.dec_dm_visiable*0.01 + consume.dec_dm_invisiable*0.01),
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01),
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * consume.bond_mtpck_prpr*0.01,
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * (100 - consume.bond_mtpck_prpr)*0.01,
            list.sid
        FROM
            t_dec_i_bill_list list
                LEFT JOIN t_dec_i_bill_head head ON head.sid = list.head_id
                LEFT JOIN t_dec_erp_i_head_n erp ON head.head_id = erp.sid
                left join t_gwstd_ems_consume_pre consume on consume.ems_no=head.ems_no and consume.exg_g_no=list.g_no
                left join t_mat_imgexg_org org on org.ems_no=consume.ems_no and org.g_mark='I' and consume.img_g_no=org.serial_no
        where erp.sid is not null
          and head.trade_code=#{tradeCode}
          and head.ems_no=#{emsNo}
          and head.trade_mode in ('4600','4400')
        group by
            list.sid,
            erp.ems_list_no,
            erp.insert_time,
            head.ems_list_no,
            head.trade_mode,
            head.ems_no,
            list.g_no,
            list.cop_g_no,
            list.g_name,
            list.code_t_s,
            list.unit,
            consume.img_g_no,
            consume.cop_img_no,
            consume.dec_cm,
            consume.dec_dm_visiable,
            consume.dec_dm_invisiable,
            consume.bond_mtpck_prpr,
            org.code_t_s,
            org.g_name,
            org.unit,
            case when consume.sid is null then '0' else '1' end;
        /*出口预录入、成品退换（进口）*/
        INSERT INTO t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no, img_g_no, img_cop_g_no,
                                      exg_code_t_s, exg_g_name, exg_unit, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,
                                      is_bom, dec_cm, dec_dm_visiable, dec_dm_invisiable, bond_mtpck_prpr,loss_rate,exp_direct_con,exp_direct_con_bond,exp_direct_con_nobond,out_list_sid)
        SELECT
            sys_guid(),
            #{tradeCode},
            CURRENT_TIMESTAMP,
            'system',
            'system',
            list.ems_no,
            'EI',
            list.trade_mode,
            list.g_no,
            list.cop_g_no,
            consume.img_g_no,
            consume.cop_img_no,
            list.code_t_s,
            list.g_name,
            list.unit,
            org.code_t_s,
            org.g_name,
            org.unit,
            sum(list.qty) qty,
            null,
            erp.ems_list_no AS erp_ems_list_no,
            erp.insert_time AS erp_insert_time,
            case when consume.sid is null then '0' else '1' end is_bom,
            consume.dec_cm,
            consume.dec_dm_visiable,
            consume.dec_dm_invisiable,
            consume.bond_mtpck_prpr,
--             sum(list.qty)*consume.dec_cm*(consume.dec_dm_visiable+consume.dec_dm_invisiable)/100,
--             sum(list.qty)*consume.dec_cm,
--             sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,
--             sum(list.qty)*consume.dec_cm-sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * consume.bond_mtpck_prpr*0.01 * (consume.dec_dm_visiable*0.01 + consume.dec_dm_invisiable*0.01),
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01),
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * consume.bond_mtpck_prpr*0.01,
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * (100 - consume.bond_mtpck_prpr)*0.01,
            list.sid
        FROM
            t_dec_erp_i_list_n list
                LEFT JOIN t_dec_erp_i_head_n erp ON list.head_id = erp.sid
                left join t_gwstd_ems_consume_pre consume on consume.ems_no=list.ems_no and consume.exg_g_no=list.g_no
                left join t_mat_imgexg_org org on org.ems_no=consume.ems_no and org.g_mark='I' and consume.img_g_no=org.serial_no
        where erp.appr_status='0'
          and erp.trade_code=#{tradeCode}
          and list.ems_no=#{emsNo}
          and list.trade_mode in ('4600','4400')
        group by
            list.sid,
            erp.ems_list_no,
            erp.insert_time,
            list.trade_mode,
            list.ems_no,
            list.g_no,
            list.cop_g_no,
            list.g_name,
            list.code_t_s,
            list.unit,
            consume.img_g_no,
            consume.cop_img_no,
            consume.dec_cm,
            consume.dec_dm_visiable,
            consume.dec_dm_invisiable,
            consume.bond_mtpck_prpr,
            org.code_t_s,
            org.g_name,
            org.unit,
            case when consume.sid is null then '0' else '1' end;

        /*出口清单*/
        INSERT INTO t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no, img_g_no, img_cop_g_no,
                                      exg_code_t_s, exg_g_name, exg_unit, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time, is_bom,
                                      dec_cm, dec_dm_visiable, dec_dm_invisiable, bond_mtpck_prpr,loss_rate,exp_direct_con,exp_direct_con_bond,exp_direct_con_nobond,out_list_sid)
        SELECT
            sys_guid(),
            #{tradeCode},
            CURRENT_TIMESTAMP,
            'system',
            'system',
            head.ems_no,
            'E',
            head.trade_mode,
            list.g_no,
            list.cop_g_no,
            consume.img_g_no,
            consume.cop_img_no,
            list.code_t_s,
            list.g_name,
            list.unit,
            org.code_t_s,
            org.g_name,
            org.unit,
            sum(list.qty) qty,
            head.ems_list_no,
            erp.ems_list_no AS erp_ems_list_no,
            erp.insert_time AS erp_insert_time,
            case when consume.sid is null then '0' else '1' end is_bom,
            consume.dec_cm,
            consume.dec_dm_visiable,
            consume.dec_dm_invisiable,
            consume.bond_mtpck_prpr,
--             sum(list.qty)*consume.dec_cm*(consume.dec_dm_visiable+consume.dec_dm_invisiable)/100,
--             sum(list.qty)*consume.dec_cm,
--             sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,
--             sum(list.qty)*consume.dec_cm-sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * consume.bond_mtpck_prpr*0.01 * (consume.dec_dm_visiable*0.01 + consume.dec_dm_invisiable*0.01),
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01),
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * consume.bond_mtpck_prpr*0.01,
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * (100 - consume.bond_mtpck_prpr)*0.01,
            list.sid
        FROM
            t_dec_e_bill_list list
                LEFT JOIN t_dec_e_bill_head head ON head.sid = list.head_id
                LEFT JOIN t_dec_erp_e_head_n erp ON head.head_id = erp.sid
                left join t_gwstd_ems_consume_pre consume on consume.ems_no=head.ems_no and consume.exg_g_no=list.g_no
                left join t_mat_imgexg_org org on org.ems_no=consume.ems_no and org.g_mark='I' and consume.img_g_no=org.serial_no
        where erp.sid is not null
          and head.trade_code=#{tradeCode}
          and head.ems_no=#{emsNo}
          and head.trade_mode in ('0615','0715','1371','0214','0654','0255','4600','4400')
        group by
            list.sid,
            erp.ems_list_no,
            erp.insert_time,
            head.ems_list_no,
            head.trade_mode,
            head.ems_no,
            list.g_no,
            list.cop_g_no,
            list.g_name,
            list.code_t_s,
            list.unit,
            consume.img_g_no,
            consume.cop_img_no,
            consume.dec_cm,
            consume.dec_dm_visiable,
            consume.dec_dm_invisiable,
            consume.bond_mtpck_prpr,
            org.code_t_s,
            org.g_name,
            org.unit,
            case when consume.sid is null then '0' else '1' end;
        /*出口预录入*/
        INSERT INTO t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no, img_g_no, img_cop_g_no,
                                      exg_code_t_s, exg_g_name, exg_unit, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,
                                      is_bom, dec_cm, dec_dm_visiable, dec_dm_invisiable, bond_mtpck_prpr,loss_rate,exp_direct_con,exp_direct_con_bond,exp_direct_con_nobond,out_list_sid)
        SELECT
            sys_guid(),
            #{tradeCode},
            CURRENT_TIMESTAMP,
            'system',
            'system',
            list.ems_no,
            'E',
            list.trade_mode,
            list.g_no,
            list.cop_g_no,
            consume.img_g_no,
            consume.cop_img_no,
            list.code_t_s,
            list.g_name,
            list.unit,
            org.code_t_s,
            org.g_name,
            org.unit,
            sum(list.qty) qty,
            null,
            erp.ems_list_no AS erp_ems_list_no,
            erp.insert_time AS erp_insert_time,
            case when consume.sid is null then '0' else '1' end is_bom,
            consume.dec_cm,
            consume.dec_dm_visiable,
            consume.dec_dm_invisiable,
            consume.bond_mtpck_prpr,
--             sum(list.qty)*consume.dec_cm*(consume.dec_dm_visiable+consume.dec_dm_invisiable)/100,
--             sum(list.qty)*consume.dec_cm,
--             sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,
--             sum(list.qty)*consume.dec_cm-sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * consume.bond_mtpck_prpr*0.01 * (consume.dec_dm_visiable*0.01 + consume.dec_dm_invisiable*0.01),
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01),
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * consume.bond_mtpck_prpr*0.01,
            sum(list.qty)*consume.dec_cm/(1 - consume.dec_dm_visiable*0.01 - consume.dec_dm_invisiable*0.01) * (100 - consume.bond_mtpck_prpr)*0.01,
            list.sid
        FROM
            t_dec_erp_e_list_n list
                LEFT JOIN t_dec_erp_e_head_n erp ON list.head_id = erp.sid
                left join t_gwstd_ems_consume_pre consume on consume.ems_no=list.ems_no and consume.exg_g_no=list.g_no
                left join t_mat_imgexg_org org on org.ems_no=consume.ems_no and org.g_mark='I' and consume.img_g_no=org.serial_no
        where erp.appr_status='0'
          and erp.trade_code=#{tradeCode}
          and list.ems_no=#{emsNo}
          and list.trade_mode in ('0615','0715','1371','0214','0654','0255','4600','4400')
        group by
            list.sid,
            erp.ems_list_no,
            erp.insert_time,
            list.trade_mode,
            list.ems_no,
            list.g_no,
            list.cop_g_no,
            list.g_name,
            list.code_t_s,
            list.unit,
            consume.img_g_no,
            consume.cop_img_no,
            consume.dec_cm,
            consume.dec_dm_visiable,
            consume.dec_dm_invisiable,
            consume.bond_mtpck_prpr,
            org.code_t_s,
            org.g_name,
            org.unit,
            case when consume.sid is null then '0' else '1' end;
    </insert>


<!--    <insert id="insertEmlBalance">-->
<!--        delete from t_gw_eml_balance where trade_code=#{tradeCode} and ems_no=#{emsNo};-->
<!--        delete from t_gw_eml_balance_list where trade_code=#{tradeCode} and ems_no=#{emsNo};-->

<!--        /*进口清单、料件退换、料件复出、余料结转（出口）*/-->
<!--        insert into t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, img_g_no, img_cop_g_no, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,add_mark)-->
<!--            /*进口清单、料件退换、料件复出、余料结转（出口）*/-->
<!--        select-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            current_timestamp,-->
<!--            'system',-->
<!--            'system',-->
<!--            head.ems_no,-->
<!--            'I',-->
<!--            head.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            sum ( list.qty ) qty,-->
<!--            head.ems_list_no,-->
<!--            erp.ems_list_no as erp_ems_list_no,-->
<!--            erp.insert_time as erp_insert_time,-->
<!--            '1'-->
<!--        from-->
<!--            t_dec_e_bill_list list-->
<!--                left join t_dec_e_bill_head head on head.sid = list.head_id-->
<!--                left join t_dec_erp_e_head_n erp on head.head_id = erp.sid-->
<!--        where-->
<!--            erp.sid is not null-->
<!--          and head.trade_code=#{tradeCode}-->
<!--          and head.ems_no=#{emsNo}-->
<!--          and head.trade_mode in ('0700','0300','0664','0265','0657','0258')-->
<!--        group by-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            head.ems_list_no,-->
<!--            head.trade_mode,-->
<!--            head.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit;-->
<!--        /*进口预录入、料件退换、料件复出、余料结转（出口）*/-->
<!--        insert into t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, img_g_no, img_cop_g_no, img_code_t_s, img_g_name, img_unit, qty, erp_ems_list_no, erp_insert_time,add_mark)-->
<!--        select-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            current_timestamp,-->
<!--            'system',-->
<!--            'system',-->
<!--            list.ems_no,-->
<!--            'I',-->
<!--            list.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            sum ( list.qty ) qty,-->
<!--            erp.ems_list_no as erp_ems_list_no,-->
<!--            erp.insert_time as erp_insert_time,-->
<!--            '1'-->
<!--        from-->
<!--            t_dec_erp_e_list_n list-->
<!--                left join t_dec_erp_e_head_n erp on list.head_id = erp.sid-->
<!--        where-->
<!--            erp.appr_status='0'-->
<!--          and list.trade_code=#{tradeCode}-->
<!--          and list.ems_no=#{emsNo}-->
<!--          and list.trade_mode in ('0700','0300','0664','0265','0657','0258')-->
<!--        group by-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            list.ems_list_no,-->
<!--            list.trade_mode,-->
<!--            list.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit;-->

<!--        /*进口清单*/-->
<!--        insert into t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, img_g_no, img_cop_g_no, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,add_mark)-->
<!--        select-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            current_timestamp,-->
<!--            'system',-->
<!--            'system',-->
<!--            head.ems_no,-->
<!--            'I',-->
<!--            head.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            sum ( list.qty ) qty,-->
<!--            head.ems_list_no,-->
<!--            erp.ems_list_no as erp_ems_list_no,-->
<!--            erp.insert_time as erp_insert_time,-->
<!--            '0'-->
<!--        from-->
<!--            t_dec_i_bill_list list-->
<!--                left join t_dec_i_bill_head head on head.sid = list.head_id-->
<!--                left join t_dec_erp_i_head_n erp on head.head_id = erp.sid-->
<!--        where-->
<!--            erp.sid is not null-->
<!--          and head.trade_code=#{tradeCode}-->
<!--          and head.ems_no=#{emsNo}-->
<!--          and head.trade_mode in ('0615','0715','1371','0214','0654','0255','0657','0258','0700','0300','0644','9700','0245','0200')-->
<!--        group by-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            head.ems_list_no,-->
<!--            head.trade_mode,-->
<!--            head.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit;-->
<!--        /*进口预录入*/-->
<!--        insert into t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, img_g_no, img_cop_g_no, img_code_t_s, img_g_name, img_unit, qty, erp_ems_list_no, erp_insert_time,add_mark)-->
<!--        select-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            current_timestamp,-->
<!--            'system',-->
<!--            'system',-->
<!--            list.ems_no,-->
<!--            'I',-->
<!--            list.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            sum ( list.qty ) qty,-->
<!--            erp.ems_list_no as erp_ems_list_no,-->
<!--            erp.insert_time as erp_insert_time,-->
<!--            '0'-->
<!--        from-->
<!--            t_dec_erp_i_list_n list-->
<!--                left join t_dec_erp_i_head_n erp on list.head_id = erp.sid-->
<!--        where-->
<!--            erp.appr_status='0'-->
<!--          and list.trade_code=#{tradeCode}-->
<!--          and list.ems_no=#{emsNo}-->
<!--          and list.trade_mode in ('0615','0715','1371','0214','0654','0255','0657','0258','0700','0300','0644','9700','0245','0200')-->
<!--        group by-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            list.ems_list_no,-->
<!--            list.trade_mode,-->
<!--            list.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit;-->

<!--        /*出口清单、成品退换（进口）*/-->
<!--        INSERT INTO t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no,-->
<!--                                      exg_code_t_s, exg_g_name, exg_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,add_mark)-->
<!--        SELECT-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            CURRENT_TIMESTAMP,-->
<!--            'system',-->
<!--            'system',-->
<!--            head.ems_no,-->
<!--            'E',-->
<!--            head.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            sum(list.qty) qty,-->
<!--            head.ems_list_no,-->
<!--            erp.ems_list_no AS erp_ems_list_no,-->
<!--            erp.insert_time AS erp_insert_time,-->
<!--            '1'-->
<!--        FROM-->
<!--            t_dec_i_bill_list list-->
<!--                LEFT JOIN t_dec_i_bill_head head ON head.sid = list.head_id-->
<!--                LEFT JOIN t_dec_erp_i_head_n erp ON head.head_id = erp.sid-->
<!--        where erp.sid is not null-->
<!--          and head.trade_code=#{tradeCode}-->
<!--          and head.ems_no=#{emsNo}-->
<!--          and head.trade_mode in ('4600','4400')-->
<!--        group by-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            head.ems_list_no,-->
<!--            head.trade_mode,-->
<!--            head.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit;-->
<!--        /*出口预录入、成品退换（进口）*/-->
<!--        INSERT INTO t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no,-->
<!--                                      exg_code_t_s, exg_g_name, exg_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,add_mark)-->
<!--        SELECT-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            CURRENT_TIMESTAMP,-->
<!--            'system',-->
<!--            'system',-->
<!--            list.ems_no,-->
<!--            'E',-->
<!--            list.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            sum(list.qty) qty,-->
<!--            null,-->
<!--            erp.ems_list_no AS erp_ems_list_no,-->
<!--            erp.insert_time AS erp_insert_time,-->
<!--            '1'-->
<!--        FROM-->
<!--            t_dec_erp_i_list_n list-->
<!--                LEFT JOIN t_dec_erp_i_head_n erp ON list.head_id = erp.sid-->
<!--        where erp.appr_status='0'-->
<!--          and erp.trade_code=#{tradeCode}-->
<!--          and list.ems_no=#{emsNo}-->
<!--          and list.trade_mode in ('4600','4400')-->
<!--        group by-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            list.trade_mode,-->
<!--            list.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit;-->

<!--        /*出口清单*/-->
<!--        INSERT INTO t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no,-->
<!--                                      exg_code_t_s, exg_g_name, exg_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,add_mark)-->
<!--        SELECT-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            CURRENT_TIMESTAMP,-->
<!--            'system',-->
<!--            'system',-->
<!--            head.ems_no,-->
<!--            'E',-->
<!--            head.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            sum(list.qty) qty,-->
<!--            head.ems_list_no,-->
<!--            erp.ems_list_no AS erp_ems_list_no,-->
<!--            erp.insert_time AS erp_insert_time,-->
<!--            '0'-->
<!--        FROM-->
<!--            t_dec_e_bill_list list-->
<!--                LEFT JOIN t_dec_e_bill_head head ON head.sid = list.head_id-->
<!--                LEFT JOIN t_dec_erp_e_head_n erp ON head.head_id = erp.sid-->
<!--        where erp.sid is not null-->
<!--          and head.trade_code=#{tradeCode}-->
<!--          and head.ems_no=#{emsNo}-->
<!--          and head.trade_mode in ('0615','0715','1371','0214','0654','0255','4600','4400')-->
<!--        group by-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            head.ems_list_no,-->
<!--            head.trade_mode,-->
<!--            head.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit;-->
<!--        /*出口预录入*/-->
<!--        INSERT INTO t_gw_eml_balance (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no,-->
<!--                                      exg_code_t_s, exg_g_name, exg_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,add_mark)-->
<!--        SELECT-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            CURRENT_TIMESTAMP,-->
<!--            'system',-->
<!--            'system',-->
<!--            list.ems_no,-->
<!--            'E',-->
<!--            list.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            sum(list.qty) qty,-->
<!--            null,-->
<!--            erp.ems_list_no AS erp_ems_list_no,-->
<!--            erp.insert_time AS erp_insert_time,-->
<!--            '0'-->
<!--        FROM-->
<!--            t_dec_erp_e_list_n list-->
<!--                LEFT JOIN t_dec_erp_e_head_n erp ON list.head_id = erp.sid-->
<!--        where erp.appr_status='0'-->
<!--          and erp.trade_code=#{tradeCode}-->
<!--          and list.ems_no=#{emsNo}-->
<!--          and list.trade_mode in ('0615','0715','1371','0214','0654','0255','4600','4400')-->
<!--        group by-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            list.trade_mode,-->
<!--            list.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit;-->

<!--        /*明细数据-->
<!--          出口清单、成品退换（进口）*/-->
<!--        INSERT INTO t_gw_eml_balance_list (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no, img_g_no, img_cop_g_no,-->
<!--                                           exg_code_t_s, exg_g_name, exg_unit, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,-->
<!--                                           dec_cm, dec_dm_visiable, dec_dm_invisiable, bond_mtpck_prpr,loss_rate,exp_direct_con,exp_direct_con_bond,exp_direct_con_nobond,add_mark)-->
<!--        SELECT-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            CURRENT_TIMESTAMP,-->
<!--            'system',-->
<!--            'system',-->
<!--            head.ems_no,-->
<!--            'E',-->
<!--            head.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            consume.img_g_no,-->
<!--            consume.cop_img_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            org.code_t_s,-->
<!--            org.g_name,-->
<!--            org.unit,-->
<!--            sum(list.qty) qty,-->
<!--            head.ems_list_no,-->
<!--            erp.ems_list_no AS erp_ems_list_no,-->
<!--            erp.insert_time AS erp_insert_time,-->
<!--            consume.dec_cm,-->
<!--            consume.dec_dm_visiable,-->
<!--            consume.dec_dm_invisiable,-->
<!--            consume.bond_mtpck_prpr,-->
<!--            sum(list.qty)*consume.dec_cm*(consume.dec_dm_visiable+consume.dec_dm_invisiable)/100,-->
<!--            sum(list.qty)*consume.dec_cm,-->
<!--            sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,-->
<!--            sum(list.qty)*consume.dec_cm-sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,-->
<!--            '1'-->
<!--        FROM-->
<!--            t_dec_i_bill_list list-->
<!--                LEFT JOIN t_dec_i_bill_head head ON head.sid = list.head_id-->
<!--                LEFT JOIN t_dec_erp_i_head_n erp ON head.head_id = erp.sid-->
<!--                inner join t_gwstd_ems_consume_pre consume on consume.ems_no=head.ems_no and consume.exg_g_no=list.g_no-->
<!--                left join t_mat_imgexg_org org on org.ems_no=consume.ems_no and org.g_mark='I' and consume.img_g_no=org.serial_no-->
<!--        where erp.sid is not null-->
<!--          and head.trade_code=#{tradeCode}-->
<!--          and head.ems_no=#{emsNo}-->
<!--          and head.trade_mode in ('4600','4400')-->
<!--        group by-->
<!--            list.sid,-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            head.ems_list_no,-->
<!--            head.trade_mode,-->
<!--            head.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit,-->
<!--            consume.img_g_no,-->
<!--            consume.cop_img_no,-->
<!--            consume.dec_cm,-->
<!--            consume.dec_dm_visiable,-->
<!--            consume.dec_dm_invisiable,-->
<!--            consume.bond_mtpck_prpr,-->
<!--            org.code_t_s,-->
<!--            org.g_name,-->
<!--            org.unit;-->
<!--        /*出口预录入、成品退换（进口）*/-->
<!--        INSERT INTO t_gw_eml_balance_list (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no, img_g_no, img_cop_g_no,-->
<!--                                           exg_code_t_s, exg_g_name, exg_unit, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,-->
<!--                                           dec_cm, dec_dm_visiable, dec_dm_invisiable, bond_mtpck_prpr,loss_rate,exp_direct_con,exp_direct_con_bond,exp_direct_con_nobond,add_mark)-->
<!--        SELECT-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            CURRENT_TIMESTAMP,-->
<!--            'system',-->
<!--            'system',-->
<!--            list.ems_no,-->
<!--            'E',-->
<!--            list.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            consume.img_g_no,-->
<!--            consume.cop_img_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            org.code_t_s,-->
<!--            org.g_name,-->
<!--            org.unit,-->
<!--            sum(list.qty) qty,-->
<!--            null,-->
<!--            erp.ems_list_no AS erp_ems_list_no,-->
<!--            erp.insert_time AS erp_insert_time,-->
<!--            consume.dec_cm,-->
<!--            consume.dec_dm_visiable,-->
<!--            consume.dec_dm_invisiable,-->
<!--            consume.bond_mtpck_prpr,-->
<!--            sum(list.qty)*consume.dec_cm*(consume.dec_dm_visiable+consume.dec_dm_invisiable)/100,-->
<!--            sum(list.qty)*consume.dec_cm,-->
<!--            sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,-->
<!--            sum(list.qty)*consume.dec_cm-sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,-->
<!--            '1'-->
<!--        FROM-->
<!--            t_dec_erp_i_list_n list-->
<!--                LEFT JOIN t_dec_erp_i_head_n erp ON list.head_id = erp.sid-->
<!--                inner join t_gwstd_ems_consume_pre consume on consume.ems_no=list.ems_no and consume.exg_g_no=list.g_no-->
<!--                left join t_mat_imgexg_org org on org.ems_no=consume.ems_no and org.g_mark='I' and consume.img_g_no=org.serial_no-->
<!--        where erp.appr_status='0'-->
<!--          and erp.trade_code=#{tradeCode}-->
<!--          and list.ems_no=#{emsNo}-->
<!--          and list.trade_mode in ('4600','4400')-->
<!--        group by-->
<!--            list.sid,-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            list.trade_mode,-->
<!--            list.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit,-->
<!--            consume.img_g_no,-->
<!--            consume.cop_img_no,-->
<!--            consume.dec_cm,-->
<!--            consume.dec_dm_visiable,-->
<!--            consume.dec_dm_invisiable,-->
<!--            consume.bond_mtpck_prpr,-->
<!--            org.code_t_s,-->
<!--            org.g_name,-->
<!--            org.unit;-->

<!--        /*出口清单*/-->
<!--        INSERT INTO t_gw_eml_balance_list (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no, img_g_no, img_cop_g_no,-->
<!--                                           exg_code_t_s, exg_g_name, exg_unit, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,-->
<!--                                           dec_cm, dec_dm_visiable, dec_dm_invisiable, bond_mtpck_prpr,loss_rate,exp_direct_con,exp_direct_con_bond,exp_direct_con_nobond,add_mark)-->
<!--        SELECT-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            CURRENT_TIMESTAMP,-->
<!--            'system',-->
<!--            'system',-->
<!--            head.ems_no,-->
<!--            'E',-->
<!--            head.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            consume.img_g_no,-->
<!--            consume.cop_img_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            org.code_t_s,-->
<!--            org.g_name,-->
<!--            org.unit,-->
<!--            sum(list.qty) qty,-->
<!--            head.ems_list_no,-->
<!--            erp.ems_list_no AS erp_ems_list_no,-->
<!--            erp.insert_time AS erp_insert_time,-->
<!--            consume.dec_cm,-->
<!--            consume.dec_dm_visiable,-->
<!--            consume.dec_dm_invisiable,-->
<!--            consume.bond_mtpck_prpr,-->
<!--            sum(list.qty)*consume.dec_cm*(consume.dec_dm_visiable+consume.dec_dm_invisiable)/100,-->
<!--            sum(list.qty)*consume.dec_cm,-->
<!--            sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,-->
<!--            sum(list.qty)*consume.dec_cm-sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,-->
<!--            '0'-->
<!--        FROM-->
<!--            t_dec_e_bill_list list-->
<!--                LEFT JOIN t_dec_e_bill_head head ON head.sid = list.head_id-->
<!--                LEFT JOIN t_dec_erp_e_head_n erp ON head.head_id = erp.sid-->
<!--                inner join t_gwstd_ems_consume_pre consume on consume.ems_no=head.ems_no and consume.exg_g_no=list.g_no-->
<!--                left join t_mat_imgexg_org org on org.ems_no=consume.ems_no and org.g_mark='I' and consume.img_g_no=org.serial_no-->
<!--        where erp.sid is not null-->
<!--          and head.trade_code=#{tradeCode}-->
<!--          and head.ems_no=#{emsNo}-->
<!--          and head.trade_mode in ('0615','0715','1371','0214','0654','0255','4600','4400')-->
<!--        group by-->
<!--            list.sid,-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            head.ems_list_no,-->
<!--            head.trade_mode,-->
<!--            head.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit,-->
<!--            consume.img_g_no,-->
<!--            consume.cop_img_no,-->
<!--            consume.dec_cm,-->
<!--            consume.dec_dm_visiable,-->
<!--            consume.dec_dm_invisiable,-->
<!--            consume.bond_mtpck_prpr,-->
<!--            org.code_t_s,-->
<!--            org.g_name,-->
<!--            org.unit;-->
<!--        /*出口预录入*/-->
<!--        INSERT INTO t_gw_eml_balance_list (sid, trade_code, insert_time, insert_user, insert_user_name, ems_no, i_e_mark, trade_mode, exg_g_no, exg_cop_g_no, img_g_no, img_cop_g_no,-->
<!--                                           exg_code_t_s, exg_g_name, exg_unit, img_code_t_s, img_g_name, img_unit, qty, ems_list_no, erp_ems_list_no, erp_insert_time,-->
<!--                                           dec_cm, dec_dm_visiable, dec_dm_invisiable, bond_mtpck_prpr,loss_rate,exp_direct_con,exp_direct_con_bond,exp_direct_con_nobond,add_mark)-->
<!--        SELECT-->
<!--            sys_guid(),-->
<!--            #{tradeCode},-->
<!--            CURRENT_TIMESTAMP,-->
<!--            'system',-->
<!--            'system',-->
<!--            list.ems_no,-->
<!--            'E',-->
<!--            list.trade_mode,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            consume.img_g_no,-->
<!--            consume.cop_img_no,-->
<!--            list.code_t_s,-->
<!--            list.g_name,-->
<!--            list.unit,-->
<!--            org.code_t_s,-->
<!--            org.g_name,-->
<!--            org.unit,-->
<!--            sum(list.qty) qty,-->
<!--            null,-->
<!--            erp.ems_list_no AS erp_ems_list_no,-->
<!--            erp.insert_time AS erp_insert_time,-->
<!--            consume.dec_cm,-->
<!--            consume.dec_dm_visiable,-->
<!--            consume.dec_dm_invisiable,-->
<!--            consume.bond_mtpck_prpr,-->
<!--            sum(list.qty)*consume.dec_cm*(consume.dec_dm_visiable+consume.dec_dm_invisiable)/100,-->
<!--            sum(list.qty)*consume.dec_cm,-->
<!--            sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,-->
<!--            sum(list.qty)*consume.dec_cm-sum(list.qty)*consume.dec_cm/(1-consume.dec_dm_visiable/100-consume.dec_dm_invisiable/100)*consume.bond_mtpck_prpr/100,-->
<!--            '0'-->
<!--        FROM-->
<!--            t_dec_erp_e_list_n list-->
<!--                LEFT JOIN t_dec_erp_e_head_n erp ON list.head_id = erp.sid-->
<!--                inner join t_gwstd_ems_consume_pre consume on consume.ems_no=list.ems_no and consume.exg_g_no=list.g_no-->
<!--                left join t_mat_imgexg_org org on org.ems_no=consume.ems_no and org.g_mark='I' and consume.img_g_no=org.serial_no-->
<!--        where erp.appr_status='0'-->
<!--          and erp.trade_code=#{tradeCode}-->
<!--          and list.ems_no=#{emsNo}-->
<!--          and list.trade_mode in ('0615','0715','1371','0214','0654','0255','4600','4400')-->
<!--        group by-->
<!--            list.sid,-->
<!--            erp.ems_list_no,-->
<!--            erp.insert_time,-->
<!--            list.trade_mode,-->
<!--            list.ems_no,-->
<!--            list.g_no,-->
<!--            list.cop_g_no,-->
<!--            list.g_name,-->
<!--            list.code_t_s,-->
<!--            list.unit,-->
<!--            consume.img_g_no,-->
<!--            consume.cop_img_no,-->
<!--            consume.dec_cm,-->
<!--            consume.dec_dm_visiable,-->
<!--            consume.dec_dm_invisiable,-->
<!--            consume.bond_mtpck_prpr,-->
<!--            org.code_t_s,-->
<!--            org.g_name,-->
<!--            org.unit;-->
<!--    </insert>-->
</mapper>
