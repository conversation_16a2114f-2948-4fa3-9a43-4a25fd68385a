package com.dcjet.cs.base.service.impl;

import com.dcjet.cs.base.mapper.BasicConverter;
import com.dcjet.cs.base.model.BasicModel;
import com.dcjet.cs.base.repository.BasicMapper;
import com.dcjet.cs.base.service.BasicService;
import com.dcjet.cs.dto.base.BasicDto;
import com.dcjet.cs.dto.base.BasicParam;
import com.dcjet.cs.util.BatchUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.lang.invoke.MethodHandles;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class BasicServiceImpl<M extends BasicMapper<E>, E extends BasicModel, D extends BasicDto, P extends BasicParam, S extends BasicParam, C extends BasicConverter<E, D, P, S>>
        implements BasicService<E, D, P, S> {

    /**
     *
     */
    protected static final Logger log = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    protected M mapper;

    @Autowired
    protected C converter;

    /**
     * 功能描述: grid分页查询
     *
     * @param searchParam
     * @param pageParam
     * @return
     */
    @Override
    public ResultObject<List<D>> findByPage(S searchParam, PageParam pageParam, UserInfoToken token) {
        E e = converter.fromSearchParam(searchParam);
        e.setTradeCode(token.getCompany());

        findHook(e, pageParam, token);
        Page<E> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
               .doSelectPage(() -> mapper.getList(e));
        List<D> dtoList = page.getResult().stream().map(converter::toDto).collect(Collectors.toList());
        findResultHook(dtoList, searchParam, token);
        ResultObject<List<D>> paged = ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    @Override
    public List<D> getAll(S searchParam, PageParam pageParam, UserInfoToken token) {
        E e = converter.fromSearchParam(searchParam);
        e.setTradeCode(token.getCompany());
        if (pageParam != null) {
            PageHelper.orderBy(pageParam.getSortOrderContent());
        }
        List<E> list = mapper.getList(e);
        List<D> dtoList = list.stream().map(converter::toDto).collect(Collectors.toList());

        return dtoList;
    }



    @Override
    public D get(String sid, UserInfoToken token) {
        if (Strings.isNullOrEmpty(sid)) {
            throw new ArgumentException();
        }

        E entity = mapper.selectByPrimaryKey(sid);
        return converter.toDto(entity);
    }


    /**
     * 功能描述:新增
     *
     * @param param
     * @param userInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public D insert(P param, UserInfoToken userInfo) {
        if (param == null) {
            throw new ArgumentException();
        }
        E entity = converter.toPo(param);
        entity.preInsert(userInfo);
        insertEntityHook(entity, param, userInfo);
        // 新增数据
        int insertStatus = mapper.insert(entity);
        return insertStatus > 0 ? converter.toDto(entity) : null;
    }

    /***
     *
     * @param insertList
     * @param token
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<E> insertList, UserInfoToken token) {
        if (insertList == null) {
            throw new ArgumentException();
        }

        if (token != null) {
            insertList.forEach(it -> {
                it.preUpdate(token);
            });
        }

        return BatchUtil.batchPage(insertList).map(it -> mapper.insertList(it)).reduce((a, b) -> a + b).orElse(0);
    }

    /**
     * 功能描述:修改
     *
     * @param sid
     * @param param
     * @param userInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public D update(String sid, P param, UserInfoToken userInfo) {
        if (Strings.isNullOrEmpty(sid)) {
            throw new ArgumentException();
        }
        E entity = mapper.selectByPrimaryKey(sid);
        if (entity == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("找不到要修改的记录"));
        }
        converter.updatePo(param, entity);
        entity.preUpdate(userInfo);
        updateEntityHook(entity, param, userInfo);

        // 更新数据
        int update = mapper.updateByPrimaryKey(entity);
        return update > 0 ? converter.toDto(entity) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public D update(String sid, P param, List<String> columnList, UserInfoToken token) {
        if (Strings.isNullOrEmpty(sid)) {
            throw new ArgumentException();
        }

        if (columnList == null || columnList.size() == 0) {
            throw new ArgumentException();
        }

        E entity = mapper.selectByPrimaryKey(sid);
        if (entity == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("找不到要修改的记录"));
        }

        converter.updatePo(param, entity);
        entity.preUpdate(token);

        updateEntityHook(entity, param, columnList, token);

        columnList.addAll(entity.preUpdateProperties());
        int update = mapper.updateByPrimaryKeySelectiveColumns(entity, columnList);
        return update > 0 ? converter.toDto(entity) : null;
    }

    /***
     *
     * @param updateList
     * @param token
     * @return
     */
    @Override
    public int batchUpdate(List<E> updateList, UserInfoToken token) {
        if (updateList == null) {
            throw new ArgumentException();
        }
        if (token != null) {
            updateList.forEach(it -> {
                it.preUpdate(token);
            });
        }

        //  fix pg java.io.IOException: Tried to send an out-of-range integer as a 2-byte value: 104500
        return BatchUtil.batchPage(updateList, 100).map(it -> mapper.updateListByPrimaryKey(it)).reduce((a, b) -> a + b).orElse(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String sid, UserInfoToken userInfo) {
        if (Strings.isNullOrEmpty(sid)) {
            throw new ArgumentException();
        }
        this.deleteBatchIds(Arrays.asList(sid), userInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatchIds(List<String> idList, UserInfoToken userInfo) {
        if (idList == null) {
            return;
        }
        deleteEntityHook(idList, userInfo);
        mapper.deleteByIdList(idList);
    }

    @Override
    @Transactional
    public void deleteBySearchParam(S searchParam, UserInfoToken userInfoToken) {

    }

    /***
     * 删除
     *
     * @param sids
     * @param userInfo
     */
    protected void deleteEntityHook(List<String> sids, UserInfoToken userInfo) {

    }

    /***
     *
     * @param entity
     * @param pageParam
     * @param userInfo
     */
    protected void findHook(E entity, PageParam pageParam, UserInfoToken userInfo) {

    }

    /***
     * 处理查询结果
     *
     * @param dtoList
     * @param searchParam
     * @param token
     */
    protected void findResultHook(List<D> dtoList, S searchParam, UserInfoToken token) {

    }


    /***
     *
     * @param entity
     * @param param
     * @param userInfo
     */
    protected void insertEntityHook(E entity, P param, UserInfoToken userInfo) {
    }



    /***
     *
     * @param entity
     * @param param
     * @param userInfo
     */
    protected void updateEntityHook(E entity, P param, UserInfoToken userInfo) {
    }

    protected void updateEntityHook(E entity, P param, List<String> columnList, UserInfoToken token) {

    }

}
