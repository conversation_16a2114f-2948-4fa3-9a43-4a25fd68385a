package com.dcjet.cs.base.repository;

import com.dcjet.cs.base.model.BasicModel;
import tk.mybatis.mapper.additional.idlist.IdListMapper;
import tk.mybatis.mapper.additional.update.differ.UpdateByDifferMapper;
import tk.mybatis.mapper.additional.update.force.UpdateByPrimaryKeySelectiveForceMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BasicMapper<E extends BasicModel>
        extends Mapper<E>, BatchMapper<E>, IdListMapper<E, String>,
        UpdateByDifferMapper<E>, UpdateByPrimaryKeySelectiveForceMapper<E>, UpdateByPrimaryKeySelectiveColumnMapper<E> {


    /***
     * 获取查询数据
     * @param param
     * @return
     */
    List<E> getList(E param);

}
