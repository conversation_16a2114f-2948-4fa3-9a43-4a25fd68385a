package com.dcjet.cs.util;

import com.xdo.common.exception.ArgumentException;
import com.xdo.domain.ResultObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.lang.invoke.MethodHandles;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: 高士勇
 * @date: 2019-06-20
 */
@Component
public class ValidatorUtil {

    private static final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Resource
    private Validator validator;

    public <T> String validation(T param) {
        Set<ConstraintViolation<T>> set = validator.validate(param);
        if (set.size() > 0) {
            String err = set.stream().map(it ->  it.getMessage()).collect(Collectors.joining(";"));
            return  err;
        }

        return null;
    }

    public <T> String validationList(List<T> params) {
        List<String> errList = new ArrayList<>();
        for (int i = 0; i < params.size(); i++) {
            Set<ConstraintViolation<T>> set = validator.validate(params.get(i));
            if (set.size() > 0) {
                String err = set.stream().map(it -> it.getMessage()).collect(Collectors.joining(";"));
                errList.add(i + "|" + err);
            }
        }
        if (errList.size() == 0) {
            return null;
        }
        return errList.stream().collect(Collectors.joining(","));
    }

    public <T> void validator(List<T> params) {
        List<String> errList = new ArrayList<>();
        for (int i = 0; i < params.size(); i++) {
            Set<ConstraintViolation<T>> set = validator.validate(params.get(i));
            if (set.size() > 0) {
                String err = set.stream().map(it -> String.format("%s[%s]:%s", it.getPropertyPath(), it.getInvalidValue(), it.getMessage())).collect(Collectors.joining(";"));
                errList.add(i + "|" + err);
            }
        }

        if (errList.size() > 0) {
            throw new ArgumentException(errList.stream().collect(Collectors.joining(",")));
        }
    }

    public <T> ResultObject validator(List<T> params,List<T> successList,ResultObject resultObject,String tradeCode) {
        List<String> errList = new ArrayList<>();
        List<T> errParamList = new ArrayList<>();
        for (int i = 0; i < params.size(); i++) {
            Set<ConstraintViolation<T>> set = validator.validate(params.get(i));
            if (set.size() > 0) {
                String err = set.stream().map(it -> String.format("%s[%s]:%s", it.getPropertyPath(), it.getInvalidValue(), it.getMessage())).collect(Collectors.joining(";"));
                errList.add(i + "|" + err);
                try {
                    for(Field f:params.get(i).getClass().getSuperclass().getDeclaredFields()) {
                        f.setAccessible(true);
                        if("success".equals(f.getName())) {
                            f.set(params.get(i), false);
                        }
                        if("message".equals(f.getName())) {
                            f.set(params.get(i), err);
                        }
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                    logger.error("反射success或message属性出错");
                    throw new ArgumentException(e.getMessage());
                }
                errParamList.add(params.get(i));
            }else {
                successList.add(params.get(i));
            }
        }
        if (errList.size() > 0) {
            logger.error("企业["+tradeCode+"]，存在入库失败的数据，错误信息为："+errList.stream().collect(Collectors.joining(",")));
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("企业[")+tradeCode+xdoi18n.XdoI18nUtil.t("]，存在入库失败的数据，错误信息为：")+errList.stream().collect(Collectors.joining(",")));
            resultObject.setData(errParamList);
        }
        return resultObject;
    }
}
