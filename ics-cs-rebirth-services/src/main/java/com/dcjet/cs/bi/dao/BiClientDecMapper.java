package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.BiClientDec;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * BiClientDec
 *
 * <AUTHOR>
 * @date: 2020-8-17
 */
public interface BiClientDecMapper extends Mapper<BiClientDec> {
    /**
     * 查询获取数据
     *
     * @param biClientDec
     * @return
     */
    List<BiClientDec> getList(BiClientDec biClientDec);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);


    /**
     * 查询海关是为代码是否重复
     * @param ownerCode
     * @param customerType
     * @param tradeCode
     * @return
     */
    Integer selectCountOwberCode(@Param("ownerCode") String ownerCode, @Param("customerType") String customerType, @Param("tradeCode") String tradeCode, @Param("sid") String sid);

    /**
     * 查询社会信用代码是否重复
     * @param ownerCreditCode
     * @param customerType
     * @param tradeCode
     * @return
     */
    Integer selectCountOwberCreditCode(@Param("ownerCreditCode") String ownerCreditCode,@Param("customerType") String customerType, @Param("tradeCode") String tradeCode, @Param("sid") String sid);

    /**
     * 查询下拉列表信息
     * @param pame
     * @return
     */
    List<BiClientDec> selectList(Map<String,Object> pame);
}
