package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.BiLogisticsAttribute;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * BiLogisticsAttribute
 *
 * <AUTHOR>
 * @date: 2021-9-5
 */
public interface BiLogisticsAttributeMapper extends Mapper<BiLogisticsAttribute> {
    /**
     * 查询获取数据
     *
     * @param biLogisticsAttribute
     * @return
     */
    List<BiLogisticsAttribute> getList(BiLogisticsAttribute biLogisticsAttribute);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 根据sid获取使用情况
     */
    int useCount(String sid);

}
