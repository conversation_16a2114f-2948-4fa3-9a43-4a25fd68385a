package com.dcjet.cs.bi.service;

import com.dcjet.cs.bi.dao.BiTemplateConfigMapper;
import com.dcjet.cs.bi.mapper.BiTemplateConfigDtoMapper;
import com.dcjet.cs.bi.model.BiTemplateConfig;
import com.dcjet.cs.dto.bi.BiTemplateConfigDto;
import com.dcjet.cs.dto.bi.BiTemplateConfigParam;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-5-8
 */
@Service
public class BiTemplateConfigService extends BaseService<BiTemplateConfig> {
    @Resource
    private BiTemplateConfigMapper biTemplateConfigMapper;
    @Resource
    private BiTemplateConfigDtoMapper biTemplateConfigDtoMapper;
    @Override
    public Mapper<BiTemplateConfig> getMapper() {
        return biTemplateConfigMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param biTemplateConfigParam
     * @return
     */
    public ResultObject<List<BiTemplateConfigDto>> getList(BiTemplateConfigParam biTemplateConfigParam) {
        // 启用分页查询
        BiTemplateConfig biTemplateConfig = biTemplateConfigDtoMapper.toPo(biTemplateConfigParam);
        List<BiTemplateConfigDto> biTemplateConfigDtos = biTemplateConfigMapper.getList(biTemplateConfig).stream().map(head -> {
            BiTemplateConfigDto dto = biTemplateConfigDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BiTemplateConfigDto>> paged = ResultObject.createInstance(true);
		paged.setData(biTemplateConfigDtos);
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param biTemplateConfigParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BiTemplateConfigDto insert(BiTemplateConfigParam biTemplateConfigParam, UserInfoToken userInfo) {
        BiTemplateConfig biTemplateConfig = biTemplateConfigDtoMapper.toPo(biTemplateConfigParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        biTemplateConfig.setSid(sid);
        biTemplateConfig.setInsertUser(userInfo.getUserNo());
        biTemplateConfig.setInsertUserName(userInfo.getUserName());
        biTemplateConfig.setInsertTime(new Date());
        // 新增数据
        int insertStatus = biTemplateConfigMapper.insert(biTemplateConfig);
        return  insertStatus > 0 ? biTemplateConfigDtoMapper.toDto(biTemplateConfig) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param biTemplateConfigParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BiTemplateConfigDto update(BiTemplateConfigParam biTemplateConfigParam, UserInfoToken userInfo) {
        BiTemplateConfig biTemplateConfig = biTemplateConfigMapper.selectByPrimaryKey(biTemplateConfigParam.getSid());
        biTemplateConfigDtoMapper.updatePo(biTemplateConfigParam, biTemplateConfig);
        biTemplateConfig.setUpdateUser(userInfo.getUserNo());
        biTemplateConfig.setUpdateUserName(userInfo.getUserName());
        biTemplateConfig.setUpdateTime(new Date());
        // 更新数据
        int update = biTemplateConfigMapper.updateByPrimaryKey(biTemplateConfig);
        return update > 0 ? biTemplateConfigDtoMapper.toDto(biTemplateConfig) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
		biTemplateConfigMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BiTemplateConfigDto> selectAll(BiTemplateConfigParam exportParam, UserInfoToken userInfo) {
        BiTemplateConfig biTemplateConfig = biTemplateConfigDtoMapper.toPo(exportParam);
        biTemplateConfig.setTradeCode(userInfo.getCompany());
        List<BiTemplateConfigDto> biTemplateConfigDtos = new ArrayList<>();
        List<BiTemplateConfig> biTemplateConfigs = biTemplateConfigMapper.getList(biTemplateConfig);
        if (CollectionUtils.isNotEmpty(biTemplateConfigs)) {
            biTemplateConfigDtos = biTemplateConfigs.stream().map(head -> {
                BiTemplateConfigDto dto = biTemplateConfigDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return biTemplateConfigDtos;
    }
}
