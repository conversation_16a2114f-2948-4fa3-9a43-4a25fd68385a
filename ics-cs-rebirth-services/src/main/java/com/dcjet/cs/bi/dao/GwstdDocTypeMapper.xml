<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.GwstdDocTypeMapper">
    <resultMap id="gwstdDocTypeResultMap" type="com.dcjet.cs.bi.model.GwstdDocType">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="DATE"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="TYPE_CODE" property="typeCode" jdbcType="VARCHAR"/>
        <result column="TYPE_NAME" property="typeName" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
      SID
     ,TRADE_CODE
     ,INSERT_USER
     ,INSERT_TIME
     ,INSERT_USER_NAME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,UPDATE_USER_NAME
     ,STATUS
     ,TYPE_CODE
     ,TYPE_NAME
     ,NOTE
    </sql>
    <sql id="condition">
        <if test="tradeCode != null and tradeCode != ''">
            and TRADE_CODE = #{tradeCode}
        </if>
        <if test="insertUserName != null and insertUserName != ''">
            and INSERT_USER_NAME = #{insertUserName}
        </if>
        <if test="updateUserName != null and updateUserName != ''">
            and UPDATE_USER_NAME = #{updateUserName}
        </if>
        <if test="status != null and status != ''">
            and STATUS = #{status}
        </if>
        <if test="typeCode != null and typeCode != ''">
            and TYPE_CODE = #{typeCode}
        </if>
        <if test="typeName != null and typeName != ''">
            and TYPE_NAME = #{typeName}
        </if>
        <if test="note != null and note != ''">
            and NOTE = #{note}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwstdDocTypeResultMap" parameterType="com.dcjet.cs.bi.model.GwstdDocType">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_GWSTD_DOC_TYPE t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_GWSTD_DOC_TYPE t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--获取下拉数据-->
    <select id="selectList" resultType="com.dcjet.cs.bi.model.GwstdDocType">
        SELECT TYPE_CODE,TYPE_NAME FROM T_GWSTD_DOC_TYPE WHERE TRADE_CODE=#{tradeCode}
    </select>

    <!--查询所有数据-->
    <select id="selectlistAll" resultType="com.dcjet.cs.bi.model.GwstdDocType">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_GWSTD_DOC_TYPE t
        WHERE SID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="chekType" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM T_GWSTD_DOC_TYPE WHERE TYPE_CODE=#{typeCode} AND TRADE_CODE= #{tradeCode} AND SID != #{sid}
    </select>

</mapper>
