<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.BiShipfromMapper">
    <resultMap id="biShipfromResultMap" type="com.dcjet.cs.bi.model.BiShipfrom">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="SHIP_FROM_CODE" property="shipFromCode" jdbcType="VARCHAR"/>
        <result column="SHIP_FROM_NAME" property="shipFromName" jdbcType="VARCHAR"/>
        <result column="SHIP_FROM_ADDRESS" property="shipFromAddress" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="DATE"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="HEAD_ID" property="headId" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="SUPPLIER_CODE" property="supplierCode" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     t.SID
     ,t.SHIP_FROM_CODE
     ,t.SHIP_FROM_NAME
     ,t.SHIP_FROM_ADDRESS
     ,t.REMARK
     ,t.INSERT_USER
     ,t.INSERT_TIME
     ,t.UPDATE_USER
     ,t.UPDATE_TIME
     ,t.TRADE_CODE
     ,t.HEAD_ID
     ,t.INSERT_USER_NAME
     ,t.UPDATE_USER_NAME
     ,t.SUPPLIER_CODE
    </sql>
    <sql id="condition">
        <if test="shipFromCode != null and shipFromCode != ''">
            and t.SHIP_FROM_CODE =#{shipFromCode,jdbcType=VARCHAR}
        </if>
        <if test="shipFromName != null and shipFromName != ''">
            and t.SHIP_FROM_NAME = #{shipFromName}
        </if>
        <if test="shipFromAddress != null and shipFromAddress != ''">
            and t.SHIP_FROM_ADDRESS = #{shipFromAddress}
        </if>
        <if test="remark != null and remark != ''">
            and t.REMARK = #{remark}
        </if>
        and t.TRADE_CODE = #{tradeCode}
        <if test="headId != null and headId != ''">
            and t.HEAD_ID = #{headId}
        </if>
        <if test="insertUserName != null and insertUserName != ''">
            and t.INSERT_USER_NAME = #{insertUserName}
        </if>
        <if test="updateUserName != null and updateUserName != ''">
            and t.UPDATE_USER_NAME = #{updateUserName}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            and t.SUPPLIER_CODE = #{supplierCode}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="biShipfromResultMap" parameterType="com.dcjet.cs.bi.model.BiShipfrom">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BI_SHIPFROM t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.SHIP_FROM_CODE desc, t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BI_SHIPFROM t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadId">
        delete from T_BI_SHIPFROM t where t.TRADE_CODE = #{tradeCode} and t.HEAD_ID = #{headId}
    </delete>
    <select id="selectShipfrom" resultType="int" parameterType="map">
        SELECT COUNT(*) FROM T_BI_SHIPFROM
        <where>
            <if test="shipFromCode != null and shipFromCode != ''">
                and SHIP_FROM_CODE like concat(concat('%',#{shipFromCode,jdbcType=VARCHAR}),'%')
            </if>
            AND TRADE_CODE = #{tradeCode}
            <if test="headId != null and headId != ''">
                and HEAD_ID = #{headId}
            </if>
        </where>
    </select>

    <select id="selectComboByCode" resultType="map" parameterType="map">
        select distinct
        t.SHIP_FROM_CODE as "VALUE",
        t.SHIP_FROM_NAME as "LABEL"
        from T_BI_SHIPFROM t
        left join T_BI_CLIENT_INFORMATION B on t.head_ID=B.SID
        <where>
            and B.CUSTOMER_CODE = #{customerCode}
            and B.TRADE_CODE = #{tradeCode}
        </where>
        order BY t.SHIP_FROM_CODE
    </select>

</mapper>
