package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.BiShipfrom;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * BiShipfrom
 *
 * <AUTHOR>
 * @date: 2019-12-17
 */
public interface BiShipfromMapper extends Mapper<BiShipfrom> {
    /**
     * 查询获取数据
     *
     * @param biShipfrom
     * @return
     */
    List<BiShipfrom> getList(BiShipfrom biShipfrom);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int deleteByHeadId(BiShipfrom biShipfrom);

    /**
     * 查询数据条数
     *
     * @param map
     * @return
     */
    Integer selectShipfrom(Map<String, Object> map);

    /**
     * 根据参数查询
     *
     * @param param 传入参数
     * @return list数据
     */
    List<Map<String, Object>> selectComboByCode(Map<String, Object> param);

}
