package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.GwstdRoyaltyFee;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwstdRoyaltyFee
* <AUTHOR>
* @date: 2020-5-22
*/
public interface GwstdRoyaltyFeeMapper extends Mapper<GwstdRoyaltyFee> {
    /**
     * 查询获取数据
     * @param gwstdRoyaltyFee
     * @return
     */
    List<GwstdRoyaltyFee> getList(GwstdRoyaltyFee gwstdRoyaltyFee);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 功能描述 校验是否存在特许权使用管理单号
     * <AUTHOR>
     * @date 2020/5/25
     * @version 1.0
     * @param gwstdRoyaltyFee 1
     * @return java.lang.String
    */
    Integer checkRoyalityNo(GwstdRoyaltyFee gwstdRoyaltyFee);
}
