package com.dcjet.cs.bi.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-6-12
 */
@Setter
@Getter
@Table(name = "T_BI_TRANSFORM")
public class BiTransform extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 转换前值
     */
    @Column(name = "ORIGIN")
    private String origin;
    /**
     * 转换后值
     */
    @Column(name = "DEST")
    private String dest;
    /**
     * 比例
     */
    @Column(name = "RATE")
    private String rate;
    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 类型
     */
    @Column(name = "TYPE")
    private String type;
}
