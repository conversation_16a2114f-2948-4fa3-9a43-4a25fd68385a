<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.BiOrdernoGenRuleMapper">
    <resultMap id="biOrdernoGenRuleResultMap" type="com.dcjet.cs.bi.model.BiOrdernoGenRule">
        <result column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="MODULE_NAME" property="moduleName" jdbcType="VARCHAR"/>
        <result column="TRADE_MODE" property="tradeMode" jdbcType="VARCHAR"/>
        <result column="FIXED_PREFIX" property="fixedPrefix" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="DATE_FMT" property="dateFmt" jdbcType="VARCHAR"/>
        <result column="SEQ_NO_DIGITS" property="seqNoDigits" jdbcType="NUMERIC"/>
        <result column="CURR_VALUE" property="currValue" jdbcType="NUMERIC"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,TRADE_CODE
     ,MODULE_NAME
     ,TRADE_MODE
     ,FIXED_PREFIX
     ,NOTE
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,DATE_FMT
     ,SEQ_NO_DIGITS
     ,CURR_VALUE
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
    </sql>
    <sql id="condition">
        and TRADE_CODE = #{tradeCode}
        <if test="moduleName != null and moduleName != ''">
            and MODULE_NAME = #{moduleName}
        </if>
        <if test="tradeMode != null and tradeMode != ''">
            and TRADE_MODE = #{tradeMode}
        </if>
        <if test="fixedPrefix != null and fixedPrefix != ''">
            and FIXED_PREFIX like '%'|| #{fixedPrefix} || '%'
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="biOrdernoGenRuleResultMap" parameterType="com.dcjet.cs.bi.model.BiOrdernoGenRule">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BI_ORDERNO_GEN_RULE t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BI_ORDERNO_GEN_RULE t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 列表查询 and 条件 begin-->
    <select id="getMaxOrderNo"  resultType="java.lang.String">
        select max(t.emsListNo) from (
        select max(EMS_LIST_NO) emsListNo from T_DEC_ERP_E_HEAD_N where TRADE_CODE = #{tradeCode} and EMS_LIST_NO like #{prefix} || '%' and lengthb(EMS_LIST_NO)=#{listNoLen}
         UNION select max(EMS_LIST_NO) emsListNo from T_DEC_ERP_I_HEAD_N where TRADE_CODE = #{tradeCode} and EMS_LIST_NO like #{prefix} || '%' and lengthb(EMS_LIST_NO)=#{listNoLen}
         UNION select max(EMS_LIST_NO) emsListNo from T_SJG_IN_BILL_HEAD where TRADE_CODE_IN = #{tradeCode} and EMS_LIST_NO like #{prefix} || '%' and lengthb(EMS_LIST_NO)=#{listNoLen}
         UNION select max(EMS_LIST_NO) emsListNo from T_SJG_OUT_BILL_HEAD where TRADE_CODE_OUT = #{tradeCode} and EMS_LIST_NO like #{prefix} || '%' and lengthb(EMS_LIST_NO)=#{listNoLen}
         UNION select max(EMS_LIST_NO) emsListNo from T_DEV_FREE_APPLY_HEAD where TRADE_CODE = #{tradeCode} and EMS_LIST_NO like #{prefix} || '%' and lengthb(EMS_LIST_NO)=#{listNoLen}
        ) t
    </select>

    <!-- 列表查询 and 条件 begin-PG-->
    <select id="getMaxOrderNo" resultType="java.lang.String" databaseId="postgresql">
        select max(t.emsListNo) from (
        select max(EMS_LIST_NO) emsListNo from T_DEC_ERP_E_HEAD_N where TRADE_CODE = #{tradeCode} and EMS_LIST_NO like #{prefix} || '%' and octet_length(EMS_LIST_NO)=#{listNoLen}
         UNION select max(EMS_LIST_NO) emsListNo from T_DEC_ERP_I_HEAD_N where TRADE_CODE = #{tradeCode} and EMS_LIST_NO like #{prefix} || '%' and octet_length(EMS_LIST_NO)=#{listNoLen}
         UNION select max(EMS_LIST_NO) emsListNo from T_SJG_IN_BILL_HEAD where TRADE_CODE_IN = #{tradeCode} and EMS_LIST_NO like #{prefix} || '%' and octet_length(EMS_LIST_NO)=#{listNoLen}
         UNION select max(EMS_LIST_NO) emsListNo from T_SJG_OUT_BILL_HEAD where TRADE_CODE_OUT = #{tradeCode} and EMS_LIST_NO like #{prefix} || '%' and octet_length(EMS_LIST_NO)=#{listNoLen}
         UNION select max(EMS_LIST_NO) emsListNo from T_DEV_FREE_APPLY_HEAD where TRADE_CODE = #{tradeCode} and EMS_LIST_NO like #{prefix} || '%' and octet_length(EMS_LIST_NO)=#{listNoLen}
        ) t
    </select>
</mapper>
