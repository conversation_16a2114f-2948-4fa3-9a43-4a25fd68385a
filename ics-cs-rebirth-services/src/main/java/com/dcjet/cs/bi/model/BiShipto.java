package com.dcjet.cs.bi.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-7-26
 */
@Setter
@Getter
@Table(name = "T_BI_SHIPTO")
public class BiShipto extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * shipto代码
     */
	@Column(name = "SHIP_TO_CODE")
	private  String shipToCode;
	/**
     * shipto名称
     */
	@Column(name = "SHIP_TO_NAME")
	private  String shipToName;
	/**
     * shipto地址
     */
	@Column(name = "SHIP_TO_ADDRESS")
	private  String shipToAddress;
	/**
     * 备注
     */
	@Column(name = "REMARK")
	private  String remark;
	/**
     * 所属企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 客户SID
     */
	@Column(name = "HEAD_ID")
	private  String headId;

	@Column(name = "CLIENT_CODE")
	private  String clientCode;
}
