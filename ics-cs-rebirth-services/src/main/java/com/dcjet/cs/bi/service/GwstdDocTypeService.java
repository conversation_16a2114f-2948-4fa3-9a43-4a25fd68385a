package com.dcjet.cs.bi.service;

import com.dcjet.cs.bi.dao.GwstdDocCenterMapper;
import com.dcjet.cs.bi.dao.GwstdDocTypeMapper;
import com.dcjet.cs.bi.mapper.GwstdDocTypeDtoMapper;
import com.dcjet.cs.bi.model.GwstdDocCenter;
import com.dcjet.cs.bi.model.GwstdDocType;
import com.dcjet.cs.dto.bi.GwstdDocTypeDto;
import com.dcjet.cs.dto.bi.GwstdDocTypeParam;
import com.dcjet.cs.util.ConstantsStatus;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-5-24
 */
@Service
public class GwstdDocTypeService extends BaseService<GwstdDocType> {
    @Resource
    private GwstdDocTypeMapper gwstdDocTypeMapper;
    @Resource
    private GwstdDocTypeDtoMapper gwstdDocTypeDtoMapper;
    @Resource
    private GwstdDocCenterMapper gwstdDocCenterMapper;

    @Override
    public Mapper<GwstdDocType> getMapper() {
        return gwstdDocTypeMapper;
    }

    /**
     * 获取分页信息
     *
     * @param gwstdDocTypeParam
     * @param pageParam
     * @param userInfo
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwstdDocTypeDto>> getListPaged(GwstdDocTypeParam gwstdDocTypeParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        GwstdDocType gwstdDocType = gwstdDocTypeDtoMapper.toPo(gwstdDocTypeParam);
        gwstdDocType.setTradeCode(userInfo.getCompany());
        Page<GwstdDocType> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwstdDocTypeMapper.getList(gwstdDocType));
        List<GwstdDocTypeDto> gwstdDocTypeDtos = page.getResult().stream().map(head -> {
            GwstdDocTypeDto dto = gwstdDocTypeDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwstdDocTypeDto>> paged = ResultObject.createInstance(gwstdDocTypeDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param gwstdDocTypeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdDocTypeDto insert(GwstdDocTypeParam gwstdDocTypeParam, UserInfoToken userInfo) {
        GwstdDocType gwstdDocType = gwstdDocTypeDtoMapper.toPo(gwstdDocTypeParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwstdDocType.setSid(sid);
        gwstdDocType.setInsertUser(userInfo.getUserNo());
        gwstdDocType.setInsertUserName(userInfo.getUserName());
        gwstdDocType.setTradeCode(userInfo.getCompany());
        gwstdDocType.setInsertTime(new Date());
        gwstdDocType.setStatus(ConstantsStatus.STATUS_0);
        //校验文档类型是否存在
//        boolean type = chekType(gwstdDocType.getTypeCode(),userInfo);
        int sum = gwstdDocTypeMapper.chekType(gwstdDocType.getTypeCode(), userInfo.getCompany(),sid);
        if (sum > 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("文档类型代码已存在"));
        }
        // 新增数据
        int insertStatus = gwstdDocTypeMapper.insert(gwstdDocType);
        return insertStatus > 0 ? gwstdDocTypeDtoMapper.toDto(gwstdDocType) : null;
    }

    /*private boolean chekType(String typeCode, UserInfoToken userInfo) {
        int sum = gwstdDocTypeMapper.chekType(typeCode, userInfo.getCompany());
        return sum > 0;
    }*/

    /**
     * 功能描述:修改
     *
     * @param gwstdDocTypeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwstdDocTypeDto update(GwstdDocTypeParam gwstdDocTypeParam, UserInfoToken userInfo) {
        GwstdDocType gwstdDocType = gwstdDocTypeMapper.selectByPrimaryKey(gwstdDocTypeParam.getSid());
        //获取修改之前的文件类型
        String typeCode = gwstdDocType.getTypeCode();

        gwstdDocTypeDtoMapper.updatePo(gwstdDocTypeParam, gwstdDocType);
        gwstdDocType.setUpdateUser(userInfo.getUserNo());
        gwstdDocType.setUpdateUserName(userInfo.getUserName());
        gwstdDocType.setUpdateTime(new Date());

        //校验文档类型是否存在
        //boolean type = chekType(gwstdDocType.getTypeCode(),userInfo);
        int sum = gwstdDocTypeMapper.chekType(gwstdDocType.getTypeCode(), userInfo.getCompany(), gwstdDocType.getSid());
        if (sum >= 1) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("文档类型代码已存在"));
        }
        //更新文件管理中的文件类型
        GwstdDocCenter gwstdDocCenter =  new GwstdDocCenter();
        gwstdDocCenter.setDocumentType(typeCode);
        gwstdDocCenter.setTradeCode(userInfo.getCompany());
        List<GwstdDocCenter> gwstdDocCenters = gwstdDocCenterMapper.getList(gwstdDocCenter);
        List<String> sids = gwstdDocCenters.stream().map(GwstdDocCenter::getSid).collect(Collectors.toList());
        if (sids.size() != 0) {
            gwstdDocCenterMapper.updateDocCenter(sids, gwstdDocType.getTypeCode(), userInfo.getCompany());
        }
        // 更新数据
        int update = gwstdDocTypeMapper.updateByPrimaryKey(gwstdDocType);
        return update > 0 ? gwstdDocTypeDtoMapper.toDto(gwstdDocType) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        List<GwstdDocType> gwstdDocTypes = gwstdDocTypeMapper.selectlistAll(sids);
        List<String> typeCodes = gwstdDocTypes.stream().map(GwstdDocType::getTypeCode).collect(Collectors.toList());

        for (String typeCode : typeCodes) {
            if (typeCode != null) {
                //校验删除数据中的文档类型是否在文档管理中是否使用
                boolean type = statisticsType(typeCode, userInfo);
                if (type) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("文档类型已使用，不允许删除"));
                }
            }
        }
        gwstdDocTypeMapper.deleteBySids(sids);
    }

    private boolean statisticsType(String typeCode, UserInfoToken userInfo) {
        int sum = gwstdDocCenterMapper.statisticsType(typeCode, userInfo.getCompany());
        return sum > 0;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwstdDocTypeDto> selectAll(GwstdDocTypeParam exportParam, UserInfoToken userInfo) {
        GwstdDocType gwstdDocType = gwstdDocTypeDtoMapper.toPo(exportParam);
        gwstdDocType.setTradeCode(userInfo.getCompany());
        List<GwstdDocTypeDto> gwstdDocTypeDtos = new ArrayList<>();
        List<GwstdDocType> gwstdDocTypes = gwstdDocTypeMapper.getList(gwstdDocType);
        if (CollectionUtils.isNotEmpty(gwstdDocTypes)) {
            gwstdDocTypeDtos = gwstdDocTypes.stream().map(head -> {
                GwstdDocTypeDto dto = gwstdDocTypeDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwstdDocTypeDtos;
    }

    /**
     * 获取下拉数据
     * @param userInfo
     * @return
     */
    public ResultObject selectList(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"");
        List<GwstdDocType> gwstdDocTypes = gwstdDocTypeMapper.selectList(userInfo.getCompany());
        resultObject.setData(gwstdDocTypes);
        return resultObject;
    }

}
