package com.dcjet.cs.bi.mapper;
import com.dcjet.cs.bi.model.BiOrdernoGenRule;
import com.dcjet.cs.dto.bi.BiOrdernoGenRuleDto;
import com.dcjet.cs.dto.bi.BiOrdernoGenRuleParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-9-4
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BiOrdernoGenRuleDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BiOrdernoGenRuleDto toDto(BiOrdernoGenRule po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BiOrdernoGenRule toPo(BiOrdernoGenRuleParam param);
    /**
     * 数据库原始数据更新
     * @param biOrdernoGenRuleParam
     * @param biOrdernoGenRule
     */
    void updatePo(BiOrdernoGenRuleParam biOrdernoGenRuleParam, @MappingTarget BiOrdernoGenRule biOrdernoGenRule);
    default void patchPo(BiOrdernoGenRuleParam biOrdernoGenRuleParam, BiOrdernoGenRule biOrdernoGenRule) {
        // TODO 自行实现局部更新
    }
}
