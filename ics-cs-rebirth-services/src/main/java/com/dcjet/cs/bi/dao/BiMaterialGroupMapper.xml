<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.BiMaterialGroupMapper">
    <resultMap id="biMaterialGroupResultMap" type="com.dcjet.cs.bi.model.BiMaterialGroup">
        <id column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="mat_group" property="matGroup" jdbcType="VARCHAR"/>
        <result column="mat_group_name" property="matGroupName" jdbcType="VARCHAR"/>
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR"/>
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     sid
     ,mat_group
     ,mat_group_name
     ,note
     ,insert_user
     ,insert_time
     ,update_user
     ,update_time
     ,trade_code
     ,insert_user_name
     ,update_user_name
    </sql>
    <sql id="condition">
        and T.TRADE_CODE = #{tradeCode}
        <if test="matGroup != null and matGroup != ''">
            and T.mat_group like concat(concat('%',#{matGroup}),'%')
        </if>
        <if test="matGroupName != null and matGroupName != ''">
            and T.mat_group_name like concat(concat('%',#{matGroupName}),'%')
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="biMaterialGroupResultMap" parameterType="com.dcjet.cs.bi.model.BiMaterialGroup">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_bi_material_group t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_bi_material_group t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_bi_material_group
        WHERE mat_group = #{matGroup}
          AND trade_code = #{tradeCode}
          AND sid!=#{sid}
    </select>
    <select id="selectMatGroup" resultType="java.lang.String">
        select mat_group from t_bi_material_group where trade_code=#{tradeCode}
    </select>

</mapper>
