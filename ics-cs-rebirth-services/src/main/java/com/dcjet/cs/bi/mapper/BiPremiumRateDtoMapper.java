package com.dcjet.cs.bi.mapper;

import com.dcjet.cs.bi.model.BiCabinetType;
import com.dcjet.cs.bi.model.BiPremiumRate;
import com.dcjet.cs.dto.bi.BiCabinetTypeDto;
import com.dcjet.cs.dto.bi.BiCabinetTypeParam;
import com.dcjet.cs.dto.bi.BiPremiumRateDto;
import com.dcjet.cs.dto.bi.BiPremiumRateParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-5
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BiPremiumRateDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BiPremiumRateDto toDto(BiPremiumRate po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BiPremiumRate toPo(BiPremiumRateParam param);

    /**
     * 数据库原始数据更新
     *
     * @param biPremiumRateParam
     * @param biPremiumRate
     */
    void updatePo(BiPremiumRateParam biPremiumRateParam, @MappingTarget BiPremiumRate biPremiumRate);

    default void patchPo(BiPremiumRateParam biPremiumRateParam, BiPremiumRate biPremiumRate) {
        // TODO 自行实现局部更新
    }
}
