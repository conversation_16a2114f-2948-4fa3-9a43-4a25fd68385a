package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.GwstdSeal;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwstdSeal
* <AUTHOR>
* @date: 2020-10-26
*/
public interface GwstdSealMapper extends Mapper<GwstdSeal> {
    /**
     * 查询获取数据
     * @param gwstdSeal
     * @return
     */
    List<GwstdSeal> getList(GwstdSeal gwstdSeal);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
