<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.BiOutEnterpriseMapper">
    <resultMap id="biOutEnterpriseResultMap" type="com.dcjet.cs.bi.model.BiOutEnterprise">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="DECLARE_CODE" property="declareCode" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="CREDIT_CODE" property="creditCode" jdbcType="VARCHAR"/>
        <result column="MASTER_CUSTOMS" property="masterCustoms" jdbcType="VARCHAR"/>
        <result column="DISTRICT_CODE" property="districtCode" jdbcType="VARCHAR"/>
        <result column="TELEPHONE_NO" property="telephoneNo" jdbcType="VARCHAR"/>
        <result column="MOBILE_PHONE" property="mobilePhone" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="DATE"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="COMPANY_CODE" property="companyCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,DECLARE_CODE
     ,COMPANY_NAME
     ,CREDIT_CODE
     ,MASTER_CUSTOMS
     ,DISTRICT_CODE
     ,TELEPHONE_NO
     ,MOBILE_PHONE
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,TRADE_CODE
     ,COMPANY_CODE
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
    </sql>
    <sql id="condition">
        <if test="declareCode != null and declareCode != ''">
            and DECLARE_CODE = #{declareCode}
        </if>
        <if test="companyName != null and companyName != ''">
            and COMPANY_NAME like concat(concat('%',#{companyName}),'%')
        </if>
        <if test="creditCode != null and creditCode != ''">
            and CREDIT_CODE = #{creditCode}
        </if>
        <if test="masterCustoms != null and masterCustoms != ''">
            and MASTER_CUSTOMS = #{masterCustoms}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and DISTRICT_CODE = #{districtCode}
        </if>
        <if test="telephoneNo != null and telephoneNo != ''">
            and TELEPHONE_NO = #{telephoneNo}
        </if>
        <if test="mobilePhone != null and mobilePhone != ''">
            and MOBILE_PHONE = #{mobilePhone}
        </if>
        and TRADE_CODE = #{tradeCode}
        <if test="companyCode != null and companyCode != ''">
            and COMPANY_CODE like concat(concat('%',#{companyCode}),'%')
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="biOutEnterpriseResultMap" parameterType="com.dcjet.cs.bi.model.BiOutEnterprise">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BI_OUT_ENTERPRISE t
        <where>
            <include refid="condition"></include>
        </where>
        order by INSERT_TIME desc, t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BI_OUT_ENTERPRISE t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getcompanyCode" resultType="int" parameterType="map">
        SELECT COUNT(*) FROM T_BI_OUT_ENTERPRISE t
        <where>
            and t.TRADE_CODE = #{tradeCode}
            <if test="companyCode != null and companyCode != ''">
                and t.COMPANY_CODE = #{companyCode}
            </if>
            <if test="sid != null and sid != ''">
                and t.SID != #{sid}
            </if>
        </where>
    </select>

    <select id="selectComboByCode" resultType="map" parameterType="map">
        select distinct t.COMPANY_CODE as "VALUE", t.COMPANY_NAME as "LABEL"
        from T_BI_OUT_ENTERPRISE t
        <where>
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        </where>
        order BY t.COMPANY_CODE
    </select>
</mapper>
