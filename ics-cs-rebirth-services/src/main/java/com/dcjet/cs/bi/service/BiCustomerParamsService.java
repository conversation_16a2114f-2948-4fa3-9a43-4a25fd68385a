package com.dcjet.cs.bi.service;
import com.dcjet.cs.bi.dao.BiCustomerParamsMapper;
import com.dcjet.cs.bi.mapper.BiCustomerParamsDtoMapper;
import com.dcjet.cs.bi.model.BiCustomerParams;
import com.dcjet.cs.dto.bi.BiCountryRegionParam;
import com.dcjet.cs.dto.bi.BiCustomerParamsDto;
import com.dcjet.cs.dto.bi.BiCustomerParamsParam;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-6-6
 */
@Service
public class BiCustomerParamsService extends BaseService<BiCustomerParams> {
    @Resource
    private BiCustomerParamsMapper biCustomerParamsMapper;
    @Resource
    private BiCustomerParamsDtoMapper biCustomerParamsDtoMapper;
    @Override
    public Mapper<BiCustomerParams> getMapper() {
        return biCustomerParamsMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param biCustomerParamsParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BiCustomerParamsDto>> getListPaged(BiCustomerParamsParam biCustomerParamsParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BiCustomerParams biCustomerParams = biCustomerParamsDtoMapper.toPo(biCustomerParamsParam);
        biCustomerParams.setTradeCode(userInfo.getCompany());
        Page<BiCustomerParams> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> biCustomerParamsMapper.getList(biCustomerParams));
        List<BiCustomerParamsDto> biCustomerParamsDtos = page.getResult().stream().map(head -> {
            BiCustomerParamsDto dto = biCustomerParamsDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BiCustomerParamsDto>> paged = ResultObject.createInstance(biCustomerParamsDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param biCustomerParamsParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BiCustomerParamsDto> insert(BiCustomerParamsParam biCustomerParamsParam, UserInfoToken userInfo) {
        ResultObject<BiCustomerParamsDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        BiCustomerParams biCustomerParams = biCustomerParamsDtoMapper.toPo(biCustomerParamsParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        biCustomerParams.setSid(sid);
        biCustomerParams.setTradeCode(userInfo.getCompany());
        biCustomerParams.setInsertUser(userInfo.getUserNo());
        biCustomerParams.setInsertUserName(userInfo.getUserName());
        biCustomerParams.setInsertTime(new Date());
        checkData(biCustomerParams,userInfo);

        // 新增数据
        int insertStatus = biCustomerParamsMapper.insert(biCustomerParams);
        BiCustomerParamsDto biCustomerParamsDto = insertStatus > 0 ? biCustomerParamsDtoMapper.toDto(biCustomerParams) : null;
        if (biCustomerParamsDto != null) {
            resultObject.setData(biCustomerParamsDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    public boolean getParamsCode(Map<String, Object> param) {
        int sum = biCustomerParamsMapper.getParamsCode(param);
        return sum > 0;
    }

    public boolean getCustomsCode(Map<String, Object> param) {
        int sum = biCustomerParamsMapper.getCustomsCode(param);
        return sum > 0;
    }

    /**
     * 功能描述:修改
     *
     * @param biCustomerParamsParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BiCustomerParamsDto> update(BiCustomerParamsParam biCustomerParamsParam, UserInfoToken userInfo) {
        ResultObject<BiCustomerParamsDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        BiCustomerParams biCustomerParams = biCustomerParamsMapper.selectByPrimaryKey(biCustomerParamsParam.getSid());
        biCustomerParamsDtoMapper.updatePo(biCustomerParamsParam, biCustomerParams);

        biCustomerParams.setUpdateUser(userInfo.getUserNo());
        biCustomerParams.setUpdateUserName(userInfo.getUserName());
        biCustomerParams.setUpdateTime(new Date());

        checkData(biCustomerParams,userInfo);
        // 更新数据
        int update = biCustomerParamsMapper.updateByPrimaryKey(biCustomerParams);

        BiCustomerParamsDto biCustomerParamsDto = update > 0 ? biCustomerParamsDtoMapper.toDto(biCustomerParams) : null;
        if (biCustomerParamsDto != null) {
            resultObject.setData(biCustomerParamsDto);
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
        biCustomerParamsMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BiCustomerParamsDto> selectAll(BiCustomerParamsParam exportParam, UserInfoToken userInfo) {
        BiCustomerParams biCustomerParams = biCustomerParamsDtoMapper.toPo(exportParam);
        biCustomerParams.setTradeCode(userInfo.getCompany());
        List<BiCustomerParamsDto> biCustomerParamsDtos = new ArrayList<>();
        List<BiCustomerParams> biCustomerParamss = biCustomerParamsMapper.getList(biCustomerParams);
        if (CollectionUtils.isNotEmpty(biCustomerParamss)) {
            biCustomerParamsDtos = biCustomerParamss.stream().map(head -> {
                BiCustomerParamsDto dto = biCustomerParamsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return biCustomerParamsDtos;
    }

    private void checkData(BiCustomerParams biCustomerParams,UserInfoToken userInfo) {
        if ("UNIT".equals(biCustomerParams.getParamsType())) {
            if (StringUtils.isBlank(biCustomerParams.getParamsCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("企业计量单位不能为空"));
            }
            if (StringUtils.isBlank(biCustomerParams.getCustomParamCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("海关计量单位不能为空"));
            }
        }
        if ("CURR".equals(biCustomerParams.getParamsType())) {
            if (StringUtils.isBlank(biCustomerParams.getParamsCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("企业币制不能为空"));
            }
            if (StringUtils.isBlank(biCustomerParams.getCustomParamCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("海关币制不能为空"));
            }
        }
        if ("GMARK".equals(biCustomerParams.getParamsType())) {
            if (StringUtils.isBlank(biCustomerParams.getParamsCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("企业物料类型不能为空"));
            }
            if (StringUtils.isBlank(biCustomerParams.getCustomParamCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("海关物料类型不能为空"));
            }
        }
        if ("BONDMARK".equals(biCustomerParams.getParamsType())) {
            if (StringUtils.isBlank(biCustomerParams.getParamsCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("企业保完税标识不能为空"));
            }
            if (StringUtils.isBlank(biCustomerParams.getCustomParamCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("海关保完税标识不能为空"));
            }
        }
        if ("COUNTRY".equals(biCustomerParams.getParamsType())) {
            if (StringUtils.isBlank(biCustomerParams.getParamsCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("企业国别不能为空"));
            }
            if (StringUtils.isBlank(biCustomerParams.getCustomParamCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("海关国别不能为空"));
            }
        }
        if ("IN_WAY".equals(biCustomerParams.getParamsType())) {
            if (StringUtils.isBlank(biCustomerParams.getParamsCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("进口方式不能为空"));
            }
            if (StringUtils.isBlank(biCustomerParams.getCustomParamCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("转换后进口方式不能为空"));
            }
        }
        if ("OUT_WAY".equals(biCustomerParams.getParamsType())) {
            if (StringUtils.isBlank(biCustomerParams.getParamsCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("出口方式不能为空"));
            }
            if (StringUtils.isBlank(biCustomerParams.getCustomParamCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("转换后出口方式不能为空"));
            }
        }
        if ("AVOID_PARAMS".equals(biCustomerParams.getParamsType())) {
            if (StringUtils.isBlank(biCustomerParams.getParamsCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("参数代码不能为空"));
            }
            if (StringUtils.isBlank(biCustomerParams.getCustomParamCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("参数类型不能为空"));
            }
        }
        if ("GOODS_ATTRIBUTE".equals(biCustomerParams.getParamsType())) {
            if (StringUtils.isBlank(biCustomerParams.getParamsCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("货物属性不能为空"));
            }
            if (StringUtils.isBlank(biCustomerParams.getCustomParamCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("货物属性名称不能为空"));
            }
        }
        if ("PORT".equals(biCustomerParams.getParamsType())) {
            if (StringUtils.isBlank(biCustomerParams.getParamsCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("企业港口不能为空"));
            }
            if (StringUtils.isBlank(biCustomerParams.getCustomParamCode())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("海关港口不能为空"));
            }
        }
        String errorMessage = "";

        Map<String, Object> map = new HashMap<>(4);
        map.put("sid", biCustomerParams.getSid());
        map.put("tradeCode", userInfo.getCompany());
        map.put("paramsCode", biCustomerParams.getParamsCode());
        map.put("paramsType", biCustomerParams.getParamsType());
        if (getParamsCode(map)) {
            if ("UNIT".equals(biCustomerParams.getParamsType())) {
                errorMessage = xdoi18n.XdoI18nUtil.t("企业计量单位已存在");
            }
            if ("CURR".equals(biCustomerParams.getParamsType())) {
                errorMessage = xdoi18n.XdoI18nUtil.t("企业币制已存在");
            }
            if ("COUNTRY".equals(biCustomerParams.getParamsType())) {
                errorMessage = xdoi18n.XdoI18nUtil.t("企业国别已存在");
            }
            if ("BONDMARK".equals(biCustomerParams.getParamsType())) {
                errorMessage = xdoi18n.XdoI18nUtil.t("企业保完税标志已存在");
            }
            if ("GMARK".equals(biCustomerParams.getParamsType())) {
                errorMessage = xdoi18n.XdoI18nUtil.t("企业物料类型标识已存在");
            }
            if ("IN_WAY".equals(biCustomerParams.getParamsType())) {
                errorMessage = xdoi18n.XdoI18nUtil.t("企业进口方式已存在");
            }
            if ("OUT_WAY".equals(biCustomerParams.getParamsType())) {
                errorMessage = xdoi18n.XdoI18nUtil.t("企业出口方式已存在");
            }
            if ("AVOID_PARAMS".equals(biCustomerParams.getParamsType())) {
                errorMessage = xdoi18n.XdoI18nUtil.t("参数代码已存在");
            }
            if ("GOODS_ATTRIBUTE".equals(biCustomerParams.getParamsType())) {
                errorMessage = xdoi18n.XdoI18nUtil.t("货物属性已存在");
            }
            if ("PORT".equals(biCustomerParams.getParamsType())) {
                errorMessage = xdoi18n.XdoI18nUtil.t("企业港口已存在");
            }
            if (StringUtils.isNotBlank(errorMessage)) {
                throw new ErrorException(400, errorMessage);
            }
        }
    }
    /**
     * 功能描述:根据paramType 获取 keyvalue值
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/6/6
     * @param: paramType 参数类型
     * @return:
     */
    public List<KeyValuePair> selectKeyValuesByParamType(String paramType, UserInfoToken userInfo) {
        BiCustomerParams biCustomerParams = new BiCustomerParams();
        biCustomerParams.setParamsType(paramType);
        biCustomerParams.setTradeCode(userInfo.getCompany());
        List<BiCustomerParams> biCustomerParamss = biCustomerParamsMapper.getList(biCustomerParams);
        if (CollectionUtils.isNotEmpty(biCustomerParamss)) {
            return biCustomerParamss.parallelStream().map(x -> new KeyValuePair(x.getParamsCode(), x.getParamsName())).collect(Collectors.toList());
        }
        return new ArrayList<KeyValuePair>();
    }

    /**
     * 功能描述:根据paramType 获取 keyvalue值
     *
     * @auther: zhangjunlin
     * @version :  1.0
     * @date: 2019/12/24
     * @param: biCustomerParamsParam
     * @return: ResultObject<List < BiCustomerParamsDto>>
     */
    public ResultObject<List<BiCustomerParamsDto>> getCustomerParamsData(BiCustomerParamsParam biCustomerParamsParam, UserInfoToken userInfo) {
        ResultObject<List<BiCustomerParamsDto>> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        try {
            if (StringUtils.isEmpty(biCustomerParamsParam.getParamsType())) {
                resultObject.setData(null);
                resultObject.setMessage(xdoi18n.XdoI18nUtil.t("企业自定义参数类型不能为空"));
                return resultObject;
            }
            BiCustomerParams biCustomerParams = new BiCustomerParams();
            biCustomerParams.setParamsType(biCustomerParamsParam.getParamsType());
            biCustomerParams.setParamsCode(biCustomerParamsParam.getParamsCode());
            biCustomerParams.setTradeCode(userInfo.getCompany());
            List<BiCustomerParams> biCustomerParamsList = biCustomerParamsMapper.getListByParams(biCustomerParams);
            List<BiCustomerParamsDto> biCustomerParamsDtos = biCustomerParamsList.stream().map(head -> {
                BiCustomerParamsDto dto = biCustomerParamsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
            resultObject.setData(biCustomerParamsDtos);
        } catch (Exception ex) {
            ex.printStackTrace();
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("查询失败") + xdoi18n.XdoI18nUtil.t("异常：") + ex);
        }
        return resultObject;
    }

    /**
     * 功能描述:查询企业所有自定义参数数据
     *
     * @auther: zhangjunlin
     * @version :  1.0
     * @date: 2019/12/24
     * @param: biCustomerParamsParam
     * @return: ResultObject<List < BiCustomerParamsDto>>
     */
    public ResultObject<List<BiCustomerParamsDto>> getAllCustomerParamsData(UserInfoToken userInfo) {
        ResultObject<List<BiCustomerParamsDto>> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        try {
            List<BiCustomerParams> biCustomerParamsList = biCustomerParamsMapper.getAllParams();
            List<BiCustomerParamsDto> biCustomerParamsDtos = biCustomerParamsList.stream().map(head -> {
                BiCustomerParamsDto dto = biCustomerParamsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
            resultObject.setData(biCustomerParamsDtos);
        } catch (Exception ex) {
            ex.printStackTrace();
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("查询失败") + xdoi18n.XdoI18nUtil.t("异常：") + ex);
        }
        return resultObject;
    }

    /**
     * 功能描述:根据paramType 获取 keyvalue值
     *
     * @auther: zhangjunlin
     * @version :  1.0
     * @date: 2019/12/24
     * @param: biCustomerParamsParam
     * @return: ResultObject<List < BiCustomerParamsDto>>
     */
    public ResultObject checkParam(BiCustomerParamsParam biCustomerParamsParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        try {
            if (StringUtils.isEmpty(biCustomerParamsParam.getParamsType())) {
                resultObject.setData(null);
                resultObject.setMessage(xdoi18n.XdoI18nUtil.t("企业自定义参数类型不能为空"));
                return resultObject;
            }
            BiCustomerParams biCustomerParams = new BiCustomerParams();
            biCustomerParams.setParamsType(biCustomerParamsParam.getParamsType());
            biCustomerParams.setParamsCode(biCustomerParamsParam.getParamsCode());
            biCustomerParams.setCustomParamCode(biCustomerParamsParam.getCustomParamCode());
            biCustomerParams.setTradeCode(userInfo.getCompany());
            List<BiCustomerParams> biCustomerParamsList = biCustomerParamsMapper.getList(biCustomerParams);
            if (biCustomerParamsList.size() > 0) {
                resultObject.setData(true);
            } else {
                resultObject.setData(false);
            }
        } catch (Exception ex) {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("查询失败") + xdoi18n.XdoI18nUtil.t("异常：") + ex);
        }
        return resultObject;
    }

    /**
     * 获取国家地区分区信息
     */
    public ResultObject<List<BiCustomerParamsDto>> getCountryRegion(BiCountryRegionParam biCountryRegionParam, PageParam pageParam) {
        biCountryRegionParam.setParamsType("COUNTRY_REGION_DHL");
        Page<BiCustomerParams> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> biCustomerParamsMapper.getCountryRegion(biCountryRegionParam));
        ResultObject<List<BiCustomerParamsDto>> paged = ResultObject.createInstance(page.getResult(), (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 获取车辆类型分页信息
     *
     * @param biCustomerParamsParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<BiCustomerParamsDto>> getVehicleList(BiCustomerParamsParam biCustomerParamsParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BiCustomerParams biCustomerParams = biCustomerParamsDtoMapper.toPo(biCustomerParamsParam);
        biCustomerParams.setTradeCode(userInfo.getCompany());
        Page<BiCustomerParams> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> biCustomerParamsMapper.getVehicleList(biCustomerParams));
        List<BiCustomerParamsDto> biCustomerParamsDtos = page.getResult().stream().map(head -> {
            BiCustomerParamsDto dto = biCustomerParamsDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BiCustomerParamsDto>> paged = ResultObject.createInstance(biCustomerParamsDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有车辆类型数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BiCustomerParamsDto> selectVehicleAll(BiCustomerParamsParam exportParam, UserInfoToken userInfo) {
        BiCustomerParams biCustomerParams = biCustomerParamsDtoMapper.toPo(exportParam);
        biCustomerParams.setTradeCode(userInfo.getCompany());
        biCustomerParams.setParamsType("GOODS_ATTRIBUTE");
        List<BiCustomerParamsDto> biCustomerParamsDtos = new ArrayList<>();
        List<BiCustomerParams> biCustomerParamss = biCustomerParamsMapper.getVehicleList(biCustomerParams);
        if (CollectionUtils.isNotEmpty(biCustomerParamss)) {
            biCustomerParamsDtos = biCustomerParamss.stream().map(head -> {
                BiCustomerParamsDto dto = biCustomerParamsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return biCustomerParamsDtos;
    }

    /**
     * 获取基础参数
     *
     * @param paraType
     * @param token
     * @return
     */
    public Map<String, String> getBiCusParaMap(String paraType, UserInfoToken token) {
        Example example = new Example(BiCustomerParams.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("tradeCode", token.getCompany());
        criteria.andEqualTo("paramsType", paraType);

        List<BiCustomerParams> cusParas = biCustomerParamsMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(cusParas)) {
            return cusParas.stream().collect(Collectors.toMap(BiCustomerParams::getParamsCode, BiCustomerParams::getCustomParamCode, (oldV, newV) -> newV));
        }
        return new HashMap<>();
    }

    public String getConvertedValue(String paraType, String code, UserInfoToken token) {
        Example example = new Example(BiCustomerParams.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("tradeCode", token.getCompany());
        criteria.andEqualTo("paramsType", paraType);
        criteria.andEqualTo("paramsCode", code);

        List<BiCustomerParams> cusParas = biCustomerParamsMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(cusParas)) {
            return cusParas.get(0).getCustomParamCode();
        }
        return null;
    }

    public ResultObject<BiCustomerParamsDto> getCategoryParameter(UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance();
        List<BiCustomerParamsDto> biCustomerParamsDtos = biCustomerParamsMapper.getCategoryParameter(token.getCompany());
        resultObject.setData(biCustomerParamsDtos);
        return resultObject;

    }

    public ResultObject<BiCustomerParamsDto> getBodyTypeParameters(UserInfoToken token) {
        ResultObject resultObject = ResultObject.createInstance();
        List<BiCustomerParamsDto> biCustomerParamsDtos = biCustomerParamsMapper.getBodyTypeParameters(token.getCompany());
        resultObject.setData(biCustomerParamsDtos);
        return resultObject;
    }
}
