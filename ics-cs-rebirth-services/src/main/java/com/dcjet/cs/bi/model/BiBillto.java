package com.dcjet.cs.bi.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-4-21
 */
@Setter
@Getter
@Table(name = "T_BI_BILLTO")
public class BiBillto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * billto代码
     */
    @Column(name = "BILL_TO_CODE")
    private String billToCode;
    /**
     * billto名称
     */
    @Column(name = "BILL_TO_NAME")
    private String billToName;
    /**
     * billto地址
     */
    @Column(name = "BILL_TO_ADDRESS")
    private String billToAddress;
    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 修改人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 所属企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 客户SID
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 制单人姓名
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;
    /**
     * 客户CODE
     */
    @Column(name = "CLIENT_CODE")
    private String clientCode;
}
