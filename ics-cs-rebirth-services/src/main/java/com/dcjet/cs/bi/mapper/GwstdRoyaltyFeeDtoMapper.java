package com.dcjet.cs.bi.mapper;

import com.dcjet.cs.bi.model.GwstdRoyaltyFee;
import com.dcjet.cs.dto.bi.GwstdRoyaltyFeeDto;
import com.dcjet.cs.dto.bi.GwstdRoyaltyFeeParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-5-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwstdRoyaltyFeeDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwstdRoyaltyFeeDto toDto(GwstdRoyaltyFee po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwstdRoyaltyFee toPo(GwstdRoyaltyFeeParam param);
    /**
     * 数据库原始数据更新
     * @param gwstdRoyaltyFeeParam
     * @param gwstdRoyaltyFee
     */
    void updatePo(GwstdRoyaltyFeeParam gwstdRoyaltyFeeParam, @MappingTarget GwstdRoyaltyFee gwstdRoyaltyFee);
    default void patchPo(GwstdRoyaltyFeeParam gwstdRoyaltyFeeParam, GwstdRoyaltyFee gwstdRoyaltyFee) {
        // TODO 自行实现局部更新
    }
}
