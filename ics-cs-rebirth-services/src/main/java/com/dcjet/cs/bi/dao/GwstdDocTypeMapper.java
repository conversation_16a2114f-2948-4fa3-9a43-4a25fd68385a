package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.GwstdDocType;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwstdDocType
* <AUTHOR>
* @date: 2021-5-24
*/
public interface GwstdDocTypeMapper extends Mapper<GwstdDocType> {
    /**
     * 查询获取数据
     * @param gwstdDocType
     * @return
     */
    List<GwstdDocType> getList(GwstdDocType gwstdDocType);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 查询下拉数据
     * @param tradeCode
     * @return
     */
    List<GwstdDocType> selectList(@Param("tradeCode") String tradeCode);

    /**
     * 查询所有需要删除的数据
     * @param sids
     * @return
     */
    List<GwstdDocType> selectlistAll(List<String> sids);

    int chekType(@Param("typeCode") String typeCode, @Param("tradeCode") String tradeCode,@Param("sid") String sid);
}
