package com.dcjet.cs.mrp.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.*;

import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 内销单价计算及审核
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2020-03-16
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MrpBalance extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    @Transient
    private String emsNo;

    @Transient
    private String facGNo;

    /**
     *备案料号
     */
    @Transient
    private String copGNo;

    /**
     * 序号
     */
    @Transient
    private Integer gNo;

    /**
     *商品编码
     */
    @Transient
    private String codeTS;

    /**
     * 料件商品名称
     */
    @Transient
    private String gName;

    /**
     * 余料结转
     */
    @Transient
    private BigDecimal ylImpQty;

    /**
     * 直接进口
     */
    @Transient
    private BigDecimal impQty;

    /***
     *
     * 深加工进口
     */
    @Transient
    private BigDecimal deepImpQty;

    /**
     * 料件退换
     */
    @Transient
    private BigDecimal ljthImpQty;

    /**
     * 料件内销
     */
    @Transient
    private BigDecimal mrpImpQty;

    /**
     * 料件复出
     */
    @Transient
    private BigDecimal ljfcExpQty;

    /**
     * 料件退换
     */
    @Transient
    private BigDecimal ljthExpQty;

    @Transient
    public BigDecimal getSumQty() {
        BigDecimal sumQty = BigDecimal.ZERO;
        if (ylImpQty != null) {
            sumQty = sumQty.add(ylImpQty);
        }

        if (impQty != null) {
            sumQty = sumQty.add(impQty);
        }

        if (deepImpQty != null) {
            sumQty = sumQty.add(deepImpQty);
        }

        if (ljthImpQty != null) {
            sumQty = sumQty.add(ljthImpQty);
        }

        if (mrpImpQty != null) {
            sumQty = sumQty.subtract(mrpImpQty);
        }

        if (ljfcExpQty != null) {
            sumQty = sumQty.subtract(ljfcExpQty);
        }

        if (ljthExpQty != null) {
            sumQty = sumQty.subtract(ljthExpQty);
        }
        return sumQty;
    }

}