package com.dcjet.cs.mrp.mapper;

import com.dcjet.cs.base.mapper.BasicConverter;
import com.dcjet.cs.dto.mrp.MrpTrialDto;
import com.dcjet.cs.dto.mrp.MrpTrialParam;
import com.dcjet.cs.dto.mrp.MrpTrialSearchParam;
import com.dcjet.cs.mrp.model.MrpMatch;
import com.dcjet.cs.mrp.model.MrpTrial;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MrpTrialDtoMapper extends BasicConverter<MrpTrial, MrpTrialDto, MrpTrialParam, MrpTrialSearchParam> {

     /***
      * to po
      *
      * @param match
      * @return
      */
     MrpTrial toPo(MrpMatch match);

     /**
      * to param
      * @param param
      * @return
      */
     MrpTrial toParm(MrpTrialParam param);
     /**
      * to param
      * @param mrpTrial
      * @return
      */
     MrpTrialDto toDto(MrpTrial mrpTrial);
}
