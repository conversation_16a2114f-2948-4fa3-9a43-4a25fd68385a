package com.dcjet.cs.mrp.model;

import com.dcjet.cs.base.model.BasicModel;
import com.dcjet.cs.mrp.util.MrpConstant;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.google.common.base.Strings;
import com.xdo.common.exception.ErrorException;
import lombok.Getter;
import lombok.Setter;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.annotation.Nonnull;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * None
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2019-11-21
 */
@Setter @Getter
@RemoveTailingZero
@Table(name = "T_MRP_IMG_EXG")
public class MrpImgExg extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String DATA_SOURCE_ADD = "ADD";
    public static final String DATA_SOURCE_IMPORT = "IMPORT";
    public static final String DATA_SOURCE_ERP_EXG_IE = "ERP_EXG_IE";
    public static final String DATA_SOURCE_ERP_IMG_IE ="ERP_IMG_IE";
    public static final String DATA_SOURCE_ERP_MRP ="ERP_MRP";
    public static final String DATA_SOURCE_ERP_WO_DOWN ="ERP_WO_DOWN";

    public static final Map<String, String> dataSourceMap = new HashMap<String, String>(5) {{
        put(DATA_SOURCE_ADD, "新增");
        put(DATA_SOURCE_IMPORT, "导入");
        put(DATA_SOURCE_ERP_EXG_IE, "成品出入库");
        put(DATA_SOURCE_ERP_IMG_IE, "料件出入库");
        put(DATA_SOURCE_ERP_MRP, "内销明细");
        put(DATA_SOURCE_ERP_WO_DOWN, "下线报废");
    }};

    /***
     * 为啥要放这一坨，就是框架的快速入库他不支持从基类拿值
     */
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private  String sid;

    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private  String insertUser;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER_NAME")
    private  String insertUserName;
    /**
     * 创建时间
     */
    @Column(name = "INSERT_TIME")
    private Date insertTime;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private  String updateUser;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER_NAME")
    private  String updateUserName;
    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    private  Date updateTime;


    @Column(name = "HEAD_ID")
    private String headId;

    /**
     * 商品编码
     */
    @Column(name = "CODE_T_S")
    private String codeTS;
    
    /**
     * 币制
     */
    @Column(name = "CURR")
    private String curr;
    
    /**
     * BOM版本号
     */
    @Column(name = "EXG_VERSION")
    private String exgVersion;
    
    /**
     * 企业料件料号
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;

    /***
     * 备案料号
     */
    @Column(name = "COP_G_NO")
    private String copGNo;

    /**
     * 序号
     */
    @Column(name = "G_NO")
    private String gNo;
    
    /**
     * 成品半成品标识 I，E， 4
     */
    @Column(name = "G_MARK")
    private String gMark;
    
    /**
     * 规格型号
     */
    @Column(name = "G_MODEL")
    private String gModel;
    
    /**
     * 商品名称
     */
    @Column(name = "G_NAME")
    private String gName;

    /**
     * 内销数量
     */
    @Column(name = "QTY")
    private BigDecimal qty;
    
    /**
     * 补税原因
     */
    @Column(name = "NOTE")
    private String note;
    
    /**
     * 参考单价
     */
    @Column(name = "DEC_PRICE")
    private BigDecimal decPrice;
    
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    
    /**
     * 单位
     */
    @Column(name = "UNIT")
    private String unit;

    /***
     * 数据来源 (ADD, IMPORT, ERP_EXG_IE, ERP_IMG_IE, ERP_MRP)
     */
    @Column(name = "DATA_SOURCE")
    private String dataSource;

    /***
     * 单据号
     */
    @Column(name = "BILL_NO")
    private String billNo;

    /***
     *
     */
    @Column(name = "ERP_SID")
    private String erpSid;

    /***
     * 自增序号
     */
    @Column(name = "SERIAL_NO")
    private Integer serialNo;


    @Transient
    private int tempFlag;

    @Transient
    private String tempRemark;

    @Transient
    private int tempIndex;


    @Transient
    private MrpBom bom;



    /***
     * 成品
     * 备案号E开头：需判断BOM版本号是否有值且小于等于8个字符，如不符合，需提示“BOM版本号不能为空或不能超过30个字符”；如接口数据BOM版本号为空时，则不允许提取，并提示“BOM版本号有为空的数据”[9.17新增]
     * 备案号B/C开头（手册企业）：不用校验BOM版本号，如ERP接口此栏位有值，以企业传值为准，如无值则插入“内销物料维护”时赋予默认值“0”
     *
     * 半成品
     * 账册和手册：不用校验BOM版本号，如ERP接口此栏位有值，以企业传值为准，如无值在插入“内销物料维护”时赋予默认值“0”
     *
     * @param emsNo
     */
    public void validationAndUpdateExgVersion(@Nonnull String emsNo) {
        if (!CommonEnum.GMarkEnum.EXG.getCode().equals(gMark) && !CommonEnum.GMarkEnum.HALF.getCode().equals(gMark)) {
            return;
        }
        if (!MrpConstant.isEml(emsNo) && CommonEnum.GMarkEnum.EXG.getCode().equals(this.getGMark())) {
            if (Strings.isNullOrEmpty(this.getExgVersion())) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("BOM版本号有为空的数据"));
            }
            if (this.getExgVersion().length() > MrpConstant.BOM_VERSION_LENGTH) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("BOM版本号不能为空或不能超过") + MrpConstant.BOM_VERSION_LENGTH + xdoi18n.XdoI18nUtil.t("个字符"));
            }
        } else {
            if (Strings.isNullOrEmpty(this.getExgVersion())) {
                this.setExgVersion(ConstantsStatus.STATUS_0);
            }
        }
    }

    /***
     * 验证BOM
     *
     * @return
     */
    public boolean validationExgVersion() {
        if (CommonEnum.GMarkEnum.EXG.getCode().equals(gMark) || CommonEnum.GMarkEnum.HALF.getCode().equals(gMark)) {
            if (Strings.isNullOrEmpty(exgVersion)) {
                return false;
            }

            if (exgVersion.length() > MrpConstant.BOM_VERSION_LENGTH) {
                return false;
            }
        }

        return true;
    }

}