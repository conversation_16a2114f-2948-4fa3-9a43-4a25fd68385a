package com.dcjet.cs.mrp.service.impl;

import com.dcjet.cs.base.service.impl.BasicServiceImpl;
import com.dcjet.cs.common.component.DistributedLockComponent;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.mrp.*;
import com.dcjet.cs.tax.exRate.component.ExchangeRateComponent;
import com.dcjet.cs.mrp.dao.MrpManageMapper;
import com.dcjet.cs.mrp.dao.MrpMatchMapper;
import com.dcjet.cs.mrp.dao.MrpPriceMapper;
import com.dcjet.cs.mrp.dao.MrpTrialMapper;
import com.dcjet.cs.mrp.mapper.MrpTrialDtoMapper;
import com.dcjet.cs.mrp.model.MrpManage;
import com.dcjet.cs.mrp.model.MrpMatch;
import com.dcjet.cs.mrp.model.MrpPrice;
import com.dcjet.cs.mrp.model.MrpTrial;
import com.dcjet.cs.mrp.service.MrpTrialService;
import com.dcjet.cs.tariff.component.PCodeTariffComponent;
import com.dcjet.cs.tariff.model.TariffModel;
import com.dcjet.cs.tax.dao.TaxRateExclusionListMapper;
import com.dcjet.cs.tax.model.TaxRateExclusionList;
import com.dcjet.cs.util.BatchUtil;
import com.dcjet.cs.util.variable.CommonVariable;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MrpTrialServiceImpl
        extends BasicServiceImpl<MrpTrialMapper, MrpTrial, MrpTrialDto, MrpTrialParam, MrpTrialSearchParam, MrpTrialDtoMapper>
        implements MrpTrialService {

    private static final Logger log = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private MrpManageMapper mrpManageMapper;

    @Resource
    private DistributedLockComponent distributedLockComponent;

    @Resource
    private ExchangeRateComponent exchangeRateComponent;

    @Resource
    private MrpPriceMapper mrpPriceMapper;

    @Resource
    private MrpMatchMapper mrpMatchMapper;

    @Resource
    private TaxRateExclusionListMapper taxRateExclusionListMapper;

    @Autowired
    private PCodeTariffComponent tariffComponent;

    @Resource
    DataSourceTransactionManager dataSourceTransactionManager;

    @Resource
    TransactionDefinition transactionDefinition;
    @Resource
    private CommonService commonService;

    @Override
    public List<MrpTrialDto> start(MrpTrialStartParam param, UserInfoToken token) {
        if (param == null || Strings.isNullOrEmpty(param.getHeadId())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("内销批次主键为空！"));
        }


        String lockKey = "mrp-trial".concat(param.getHeadId());
        boolean lock = distributedLockComponent.tryLock(lockKey, 30, token.getCompany(), token.getUserNo());
        if (!lock) {
            throw new ErrorException(429, xdoi18n.XdoI18nUtil.t("服务器正在计算中，请稍后！"));
        }

        try {
            MrpManage manage = mrpManageMapper.selectByPrimaryKey(param.getHeadId());
            if (manage == null) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("无法根据内销批次主键找到对应的内销批次信息！"));
            }

            if (!(MrpManage.STATUS_STAGING.equals(manage.getStatus()) ||
                    MrpManage.STATUS_TRIAL_DONE.equals(manage.getStatus()))) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前批次的数据状态不允许进行此操作"));
            }

            MrpPrice priceParam = MrpPrice.builder()
                    .headId(manage.getSid())
                    .build();
            List<MrpPrice> priceList = mrpPriceMapper.select(priceParam);
            if (priceList.size() == 0) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请先完成单价计算"));
            }

            MrpMatch matchParam = MrpMatch.builder()
                    .headId(manage.getSid())
                    .build();
            matchParam.setTradeCode(token.getCompany());
            List<MrpMatch> matchList = mrpMatchMapper.getList(matchParam);
            if (matchList.size() == 0) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请先匹配核注清单"));
            }

            if (Strings.isNullOrEmpty(matchList.get(0).getPriceId())) {
                // 2020-07-27 之前的版本匹配走的facGNo，加了单价拆分后，facGNo无法在用于业务主键，在匹配增加priceId关联单价
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("匹配核注清单数据失效，请重新匹配"));
            }

            final Map<String, MrpPrice> priceMap = priceList.stream().collect(Collectors.toMap(MrpPrice::getSid, Function.identity(), (oldVal, newVal) -> oldVal));
            final Map<String, List<MrpMatch>> matchMap = matchList.stream().collect(Collectors.groupingBy(MrpMatch::getPriceId));

            final Map<String, Long> taxRateExclusionListMap = getTaxRateExclusionMap(token);

            List<MrpTrial> trialList = new ArrayList<>(matchList.size());
            matchMap.keySet().forEach(it -> {
                List<MrpMatch> facGNoMatchList = matchMap.get(it);
                MrpPrice price = priceMap.get(it);

                if (price == null) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("单价计算发生变化，请重新匹配核注清单"));
                }

                // 按照剩余数量倒序，为了计算未匹配到核注清单的最后一条数据
                facGNoMatchList.sort(Comparator.comparing(MrpMatch::getRemainQty).reversed());
                for (int i = 0; i < facGNoMatchList.size(); i++) {
                    MrpMatch match = facGNoMatchList.get(i);
                    MrpTrial trial = converter.toPo(match);
                    trial.setMatchId(match.getSid());
                    trial.setCertificateNo(price.getCertificateNo());
//                    trial.setRmbDecTotal(price.getRmbDecTotal());
                    trial.setHeadId(manage.getSid());
                    trial.preInsert(token);
                    calcTaxation(trial, price.getUsdApprDecPrice(), taxRateExclusionListMap);
                    trialList.add(trial);
                }
            });
            TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
            try {
                insertBatch(manage.getSid(), trialList);
                dataSourceTransactionManager.commit(transactionStatus);
            } catch (Exception e) {
                dataSourceTransactionManager.rollback(transactionStatus);
                throw e;
            }


            // 更新内销批次信息
            StringBuilder sb = new StringBuilder()
                    .append(xdoi18n.XdoI18nUtil.t("计算完成时间："))
                    .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));

            manage.setTrialMark(sb.toString());
            manage.preUpdate(token);
            manage.setStatus(MrpManage.STATUS_TRIAL_DONE);
            mrpManageMapper.updateByPrimaryKey(manage);

            return trialList.stream().map(converter::toDto).collect(Collectors.toList());
        } finally {
            distributedLockComponent.unlock(lockKey);
        }
    }



    /***
     *
     * @param headId
     * @param trialList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(String headId, List<MrpTrial> trialList) {
        MrpTrial deleteTrial = new MrpTrial();
        deleteTrial.setHeadId(headId);
        mapper.delete(deleteTrial);

        int count = BatchUtil.batchPage(trialList).map(mapper::insertList).reduce((a, b) -> a + b).orElse(0);
        if (count != trialList.size()) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("批量新增试算出错"));
        }
    }

    @Override
    public MrpTrialStatDto stat(MrpTrialSearchParam param, UserInfoToken token) {
        param.setTradeCode(token.getCompany());
        MrpTrialStatDto dto = mapper.stat(param);
        if (dto == null) {
            dto = new MrpTrialStatDto();
        }
        MrpTrialStatDto taxDto = mapper.statTax(param);
        if (taxDto != null) {
            dto.setTaxCount(taxDto.getTaxCount());
            dto.setNoTaxCount(taxDto.getNoTaxCount());
        }

        return dto;
    }

    /***
     * 取 *协定税率* 时关联“税率排除目录”，如果存在目录中，则不取协定税率，按“暂定税率、最惠国税率、普通税率，取最低值”。
     *
     * @param trial
     * @param decPrice
     * @param taxRateExclusionListMap
     * @return
     */
    private MrpTrial calcTaxation(MrpTrial trial, BigDecimal decPrice, Map<String, Long> taxRateExclusionListMap) {
        if (decPrice == null) {
            return trial;
        }
        trial.setDecPrice(decPrice.setScale(4, RoundingMode.UP));

        if (trial.getMatchQty() == null) {
            return trial;
        }
        trial.setUsdDecTotal(trial.getDecPrice().multiply(trial.getMatchQty()).setScale(2, RoundingMode.UP));
        trial.setRmbDecTotal(exchangeRateComponent.rmb(CommonVariable.CURR_USD, trial.getUsdDecTotal()).setScale(2, RoundingMode.UP));

        if (Strings.isNullOrEmpty(trial.getOriginCountry())) {
            return trial;
        }

        TariffModel tariffModel = tariffComponent.getTariffModel(trial.getCodeTS(), trial.getOriginCountry());
        if (tariffModel == null) {
            log.error("商品编码【{}】原产国 【{}】的税率没有维护！", trial.getCodeTS(), trial.getOriginCountry());
            return trial;
        }

        boolean isTaxRateExclusion =  taxRateExclusionListMap.containsKey(trial.getCodeTS());
        TariffModel.OptimalRate optimalRate = tariffModel.getOptimalRate(trial.getDecPrice(), trial.getOriginCountry(), !Strings.isNullOrEmpty(trial.getCertificateNo()), isTaxRateExclusion);
        if (optimalRate != null) {
            trial.setTaxRate(optimalRate.getDescribe());
            trial.setTaxType(optimalRate.getRateTypeDesc());

            BigDecimal taxPredict = null;
            if (optimalRate.getRate() != null) {
                taxPredict = optimalRate.getRate().multiply(trial.getRmbDecTotal());
            }
            if (optimalRate.getPrice() != null) {
                taxPredict = taxPredict.add(optimalRate.getPrice().multiply(trial.getMatchQty()));
            }
            trial.setTaxPredict(taxPredict);
        } else {
            log.error("商品编码[{}]，原产国[{}], 单价[{}] 获取的关税计算公式为空!", trial.getCodeTS(), trial.getOriginCountry(), decPrice);
        }

        if (tariffModel.getAddRate() != null) {
            trial.setAddTaxRate(tariffModel.getAddRate().multiply(new BigDecimal("100")));
        }
        if (trial.getRmbDecTotal() != null && trial.getTaxPredict() != null && tariffModel.getAddRate() != null) {
            trial.setAddTaxPredict(trial.getRmbDecTotal().add(trial.getTaxPredict()).multiply(tariffModel.getAddRate()));
        }
        return trial;
    }

    private Map<String, Long> getTaxRateExclusionMap(UserInfoToken token) {
        TaxRateExclusionList queryParam = new TaxRateExclusionList();
        queryParam.setTradeCode(token.getCompany());
        queryParam.setStatus("1");
        List<TaxRateExclusionList> list = taxRateExclusionListMapper.select(queryParam);
        return list.stream().collect(Collectors.groupingBy(TaxRateExclusionList::getCodeTS, Collectors.counting()));
    }

    /**
     * 功能描述：查询所有数据
     * @param exportColumns
     * @param token
     * @return
     */
    @Override
    public List<MrpTrialDto> getListAll(MrpTrialParam exportColumns, UserInfoToken token) {
        MrpTrial mrpTrial = converter.toParm(exportColumns);
        mrpTrial.setTradeCode(token.getCompany());
        List<MrpTrialDto> mrpTrialDtos = new ArrayList<>();
        List<MrpTrial> mrpTrials = mapper.getListAll(mrpTrial);
        if (CollectionUtils.isNotEmpty(mrpTrials)) {
            mrpTrialDtos = mrpTrials.stream().map(h -> {
                h.setOriginCountry(commonService.convertPCode(h.getOriginCountry(), PCodeType.COUNTRY_OUTDATED));
                if (h.getAddTaxPredict() != null) {
                    h.setAddTaxPredict(h.getAddTaxPredict().setScale(2, RoundingMode.UP));
                }
                if (h.getTaxPredict() != null) {
                    h.setTaxPredict(h.getTaxPredict().setScale(2, RoundingMode.UP));
                }
                MrpTrialDto dto = converter.toDto(h);
                return dto;
            }).collect(Collectors.toList());
        }
        return mrpTrialDtos;
    }

}
