package com.dcjet.cs.mrp.mapper;

import com.dcjet.cs.base.mapper.BasicConverter;
import com.dcjet.cs.dto.mrp.MrpBalanceDto;
import com.dcjet.cs.dto.mrp.MrpBalanceParam;
import com.dcjet.cs.dto.mrp.MrpBalanceSearchParam;
import com.dcjet.cs.mrp.model.MrpBalance;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MrpBalanceDtoMapper extends BasicConverter<MrpBalance, MrpBalanceDto, MrpBalanceParam, MrpBalanceSearchParam> {
}
