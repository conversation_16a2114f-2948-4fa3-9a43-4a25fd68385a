package com.dcjet.cs.mrp.manager.impl;

import com.dcjet.cs.common.dao.GwstdHttpConfigMapper;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.model.RequestLog;
import com.dcjet.cs.common.model.RequestLogEvent;
import com.dcjet.cs.mrp.manager.MrpPriceFetchManager;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;

/**
 * <AUTHOR>
 */
public abstract class AbstractMrpPriceFetchManager implements MrpPriceFetchManager {
    protected final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    protected final String EML_PRICE_URL = "EML_PRICE_URL";

    protected final String EMS_PRICE_URL = "EMS_PRICE_URL";

    protected final String EMS_CAV_URL = "EMS_CAV_URL";

    @Resource
    protected GwstdHttpConfigMapper httpConfigMapper;

    @Resource
    protected ApplicationEventPublisher applicationEventPublisher;

    public String getUrl(String type) {
        GwstdHttpConfig httpConfig = httpConfigMapper.selectByType(type);
        if (httpConfig == null) {
            throw new ArgumentException(String.format("[%s]%s",type,xdoi18n.XdoI18nUtil.t("未找到连接配置信息")));
        }
        return httpConfig.getBaseUrl() + httpConfig.getServiceUrl();
    }

    public void audit(String bizId, String url, String requestBody, String responseBody, long duration, UserInfoToken token) {
        RequestLog dto = new RequestLog();
        dto.setProtocol("http");
        dto.setMethod("post");
        dto.setBizId(bizId);
        dto.setUrl(url);
        dto.setBizType(RequestLog.BIZ_MRP);
        dto.setStatus(200);
        dto.setTime((int)duration);
        dto.setRequestBody(requestBody);
        dto.setResponseBody(responseBody);
        dto.preInsert(token);
        applicationEventPublisher.publishEvent(new RequestLogEvent(RequestLog.BIZ_MRP, dto));
    }
}
