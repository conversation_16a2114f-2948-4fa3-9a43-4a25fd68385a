<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.mrp.dao.MrpMatchMapper">
    <sql id="columns">
        t.SID,
		t.CODE_T_S,
		t.COP_G_NO,
		t.DEC_BILL_REMAIN_QTY,
		t.DCR_TIMES,
		t.DEC_I_BILL_LIST_SID,
		t.EMS_LIST_NO,
		t.FAC_G_NO,
		t.G_NAME,
		t.G_NO,
		t.HEAD_ID,
		t.INSERT_TIME,
		t.INSERT_USER,
		t.INSERT_USER_NAME,
		t.MATCH_QTY,
		t.ORIGIN_COUNTRY,
		t.ORIGIN_MRP_QTY,
		t.ORIGIN_QTY,
		t.ORIGIN_REMAIN_QTY,
		t.QTY,
		t.REMAIN_QTY,
		t.TRADE_CODE,
		t.PRICE_ID,
		t.UPDATE_TIME,
		t.UPDATE_USER,
		t.UPDATE_USER_NAME
    </sql>
    <sql id="condition">
		<if test="headId != null and headId != ''">
            and t.HEAD_ID = #{headId,jdbcType=VARCHAR}
        </if>
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
		<if test="facGNo != null and facGNo != ''">
			and t.FAC_G_NO like concat(concat('%', #{facGNo,jdbcType=VARCHAR}),'%')
		</if>
		<if test="copGNo != null and copGNo != ''">
			and t.COP_G_NO like concat(concat('%', #{copGNo,jdbcType=VARCHAR}),'%')
		</if>
		<if test="gNo != null and gNo != ''">
			and t.G_NO like concat(concat('%', #{gNo,jdbcType=VARCHAR}),'%')
		</if>
		<if test="codeTS != null and codeTS != ''">
			and t.CODE_T_S like concat(concat('%', #{codeTS,jdbcType=VARCHAR}),'%')
		</if>
		<if test="gName != null and gName != ''">
			and t.G_NAME like concat(concat('%', #{gName,jdbcType=VARCHAR}),'%')
		</if>
		<if test="emsListNo != null and emsListNo != ''">
		    and t.EMS_LIST_NO like concat(concat('%', #{emsListNo, jdbcType=VARCHAR}),'%')
		</if>
		<if test="entryNo != null and entryNo != ''">
		    and t.ENTRY_NO like concat(concat('%', #{entryNo, jdbcType=VARCHAR}),'%')
		</if>
		<if test="declareDateFrom != null and declareDateFrom != ''">
			<![CDATA[ and t.DECLARE_DATE >= to_date(#{declareDateFrom}, 'yyyy-MM-dd')]]>
		</if>
		<if test="declareDateTo != null and declareDateTo != ''">
			<![CDATA[ and t.DECLARE_DATE < to_date(#{declareDateTo}, 'yyyy-MM-dd')+1 ]]>
		</if>
		<if test="originCountry != null and originCountry != ''">
			and t.ORIGIN_COUNTRY = #{originCountry,jdbcType=VARCHAR}
		</if>
		<if test='matchQty != null and matchQty == "0"'>
			and t.MATCH_QTY = 0
		</if>
		<if test='matchQty != null and matchQty == "1"'>
			and t.MATCH_QTY > 0
		</if>
		<if test='remainQty != null and remainQty == "0"'>
			and t.REMAIN_QTY = 0
		</if>
		<if test='remainQty != null and remainQty == "1"'>
			and t.REMAIN_QTY > 0
		</if>
		<if test='decBillRemainQty != null and decBillRemainQty == "0"'>
			and t.DEC_BILL_REMAIN_QTY = 0
		</if>
		<if test='decBillRemainQty != null and decBillRemainQty == "1"'>
			and t.DEC_BILL_REMAIN_QTY > 0
		</if>
		<if test='done != null and done == "0"'>
            and t.COP_G_NO in (
			select COP_G_NO FROM T_MRP_MATCH
			where HEAD_ID = #{headId,jdbcType=VARCHAR} and TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
			group by COP_G_NO HAVING MIN(REMAIN_QTY) = 0)
		</if>
		<if test='done != null and done == "1"'>
			and t.COP_G_NO in (
			select COP_G_NO FROM T_MRP_MATCH
			where HEAD_ID = #{headId,jdbcType=VARCHAR} and TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
			group by COP_G_NO HAVING MIN(REMAIN_QTY) > 0)
		</if>
    </sql>
    <select id="getList" parameterType="map" resultType="com.dcjet.cs.mrp.model.MrpMatch">
		SELECT * FROM (
			SELECT
				t.SID,
				t.CODE_T_S,
				t.COP_G_NO,
				t.DEC_BILL_REMAIN_QTY,
				t.DCR_TIMES,
				t.DEC_I_BILL_LIST_SID,
				t.EMS_LIST_NO,
				t.FAC_G_NO,
				t.G_NAME,
				t.G_NO,
				t.HEAD_ID,
				t.INSERT_TIME,
				t.INSERT_USER,
				t.INSERT_USER_NAME,
				t.MATCH_QTY,
				t.ORIGIN_COUNTRY,
				t.ORIGIN_MRP_QTY,
				t.ORIGIN_QTY,
				t.ORIGIN_REMAIN_QTY,
				t.QTY,
				t.PRICE_ID,
				t.REMAIN_QTY,
				t.TRADE_CODE,
				t.UPDATE_TIME,
				t.UPDATE_USER,
				t.UPDATE_USER_NAME,
				head.ENTRY_NO,
				head.DECLARE_DATE,
				head.LIST_NO
			FROM T_MRP_MATCH t left join T_DEC_I_BILL_LIST list on t.DEC_I_BILL_LIST_SID = list.sid
			 left join T_DEC_I_BILL_HEAD head on list.HEAD_ID = head.SID
			WHERE t.HEAD_ID = #{headId,jdbcType=VARCHAR}
		) t
        <where>
            <include refid="condition"></include>
        </where>
		ORDER BY t.G_NO, t.FAC_G_NO, t.REMAIN_QTY desc
    </select>

	<select id="sumByBillListSid" resultType="java.math.BigDecimal">
		SELECT
		    SUM(t.MATCH_QTY)
		from T_MRP_MATCH t
		WHERE t.TRADE_CODE = #{tradeCode}
		AND t.DEC_I_BILL_LIST_SID = #{billListSid}
	</select>
	<select id="stat" databaseId="postgresql" resultType="com.dcjet.cs.dto.mrp.MrpMatchStatDto">
	select sum(g.remainCount) as remainCount,sum(g.matchCount) as matchCount
	from (
		SELECT
			CASE WHEN MIN(coalesce(t.REMAIN_QTY, 1)) > 0 THEN 1 ELSE 0 END as remainCount,
			CASE WHEN MIN(coalesce(t.REMAIN_QTY, 1)) = 0 THEN 1 ELSE 0 END as matchCount
			FROM T_MRP_MATCH t
			<where>
				<include refid="condition"></include>
			</where>
			GROUP BY COP_G_NO
		) g
	</select>

	<select id="stat" resultType="com.dcjet.cs.dto.mrp.MrpMatchStatDto">
		SELECT
			SUM(CASE WHEN MIN(NVL(t.REMAIN_QTY, 1)) > 0 THEN 1 ELSE 0 END) remainCount,
			SUM(CASE WHEN MIN(NVL(t.REMAIN_QTY, 1)) = 0 THEN 1 ELSE 0 END) matchCount
		FROM (
			SELECT
				t.SID,
				t.CODE_T_S,
				t.COP_G_NO,
				t.DEC_BILL_REMAIN_QTY,
				t.DCR_TIMES,
				t.DEC_I_BILL_LIST_SID,
				t.EMS_LIST_NO,
				t.FAC_G_NO,
				t.G_NAME,
				t.G_NO,
				t.HEAD_ID,
				t.INSERT_TIME,
				t.INSERT_USER,
				t.INSERT_USER_NAME,
				t.MATCH_QTY,
				t.ORIGIN_COUNTRY,
				t.ORIGIN_MRP_QTY,
				t.ORIGIN_QTY,
				t.ORIGIN_REMAIN_QTY,
				t.QTY,
				t.PRICE_ID,
				t.REMAIN_QTY,
				t.TRADE_CODE,
				t.UPDATE_TIME,
				t.UPDATE_USER,
				t.UPDATE_USER_NAME
			FROM T_MRP_MATCH t
			WHERE t.HEAD_ID = #{headId,jdbcType=VARCHAR}
		) t
		<where>
			<include refid="condition"></include>
		</where>
		GROUP BY COP_G_NO
	</select>
	<select id="history" parameterType="com.dcjet.cs.dto.mrp.MrpMatchHistorySearchParam" resultType="com.dcjet.cs.dto.mrp.MrpMatchHistoryDto">
		SELECT
			t.FAC_G_NO,
			t.COP_G_NO,
			t.G_NO,
			t.CODE_T_S,
			t.G_NAME,
			t.QTY,
			t.MATCH_QTY,
			bh.LIST_NO,
			bh.EMS_LIST_NO,
			bh.ENTRY_NO,
        	bl.QTY billQty,
			m.BATCH_NO,
			mbh.LIST_NO mrpListNo,
			mbh.ENTRY_NO mrpEntryNo,
			mbh.DECLARE_DATE mrpDeclareDate,
			mbh.DECLARE_CODE mrpDeclareCode,
			mbh.DECLARE_NAME mrpDeclareName
		FROM T_MRP_MATCH t
		LEFT JOIN T_MRP_MANAGE m ON t.HEAD_ID = m.SID
		LEFT JOIN T_MRP_DEC_ERP md ON md.HEAD_ID  = m.SID
		LEFT JOIN T_DEC_I_BILL_LIST bl on t.DEC_I_BILL_LIST_SID = bl.sid
		LEFT JOIN T_DEC_I_BILL_HEAD bh ON bl.HEAD_ID  = bh.SID
		LEFT JOIN T_DEC_I_BILL_HEAD mbh ON mbh.HEAD_ID = md.DEC_ERP_HEAD_ID
		<where>
			t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
			<if test="facGNo != null and facGNo != ''">
				and t.FAC_G_NO like concat(concat('%', #{facGNo,jdbcType=VARCHAR}),'%')
			</if>
			<if test="copGNo != null and copGNo != ''">
				and t.COP_G_NO like concat(concat('%', #{copGNo,jdbcType=VARCHAR}),'%')
			</if>
			<if test="gNo != null and gNo != ''">
				and t.G_NO like concat(concat('%', #{gNo,jdbcType=VARCHAR}),'%')
			</if>
			<if test="codeTS != null and codeTS != ''">
				and t.CODE_T_S like concat(concat('%', #{codeTS,jdbcType=VARCHAR}),'%')
			</if>
			<if test="gName != null and gName != ''">
				and t.G_NAME like concat(concat('%', #{gName,jdbcType=VARCHAR}),'%')
			</if>
			<if test="listNo != null and listNo != ''">
				and bh.LIST_NO like concat(concat('%', #{listNo, jdbcType=VARCHAR}),'%')
			</if>
			<if test="entryNo != null and entryNo != ''">
				and bh.ENTRY_NO like concat(concat('%', #{entryNo, jdbcType=VARCHAR}),'%')
			</if>
			<if test="batchNo != null and batchNo != ''">
				and m.BATCH_NO like concat(concat('%', #{batchNo}),'%')
			</if>
			<if test="mrpListNo != null and mrpListNo != ''">
				and mbh.LIST_NO like concat(concat('%', #{mrpListNo, jdbcType=VARCHAR}),'%')
			</if>
			<if test="mrpEntryNo != null and mrpEntryNo != ''">
				and mbh.ENTRY_NO like concat(concat('%', #{mrpEntryNo, jdbcType=VARCHAR}),'%')
			</if>
			<if test="mrpDeclareDateFrom != null and mrpDeclareDateFrom != ''">
				<![CDATA[ and mbh.DECLARE_DATE >= to_date(#{mrpDeclareDateFrom}, 'yyyy-MM-dd')]]>
			</if>
			<if test="mrpDeclareDateTo != null and mrpDeclareDateTo != ''">
				<![CDATA[ and mbh.DECLARE_DATE < to_date(#{mrpDeclareDateTo}, 'yyyy-MM-dd')+1 ]]>
			</if>
		</where>
		order by t.G_NO, t.SID
	</select>
	<select id="sumDcrMatch" resultType="java.math.BigDecimal">
		SELECT SUM(m.MATCH_QTY) FROM T_MRP_MATCH m
		LEFT JOIN T_MRP_MANAGE h ON m.HEAD_ID = h.SID
		WHERE h.TRADE_CODE = #{tradeCode} AND h.EMS_NO = #{emsNo} AND COP_G_NO = #{copGNo} AND h.DCR_TIMES = #{dcrTimes}
		AND m.MATCH_QTY > 0 and h.MATCH_DCR_TIMES_TYPE = 1
		AND cast(m.DCR_TIMES as numeric) <![CDATA[ < ]]> #{dcrTimes}
	</select>
	<delete id="deleteByHeadId" parameterType="java.lang.String">
        DELETE FROM T_MRP_MATCH T WHERE T.HEAD_ID = #{headId}
    </delete>

</mapper>