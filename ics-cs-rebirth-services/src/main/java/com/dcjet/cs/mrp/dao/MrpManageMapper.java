package com.dcjet.cs.mrp.dao;

import com.dcjet.cs.base.repository.BasicMapper;
import com.dcjet.cs.mrp.model.MrpManage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/***
 *
 * <AUTHOR>
 */
public interface MrpManageMapper extends BasicMapper<MrpManage> {


    /**
     *
     * @param tradeCode
     * @param batchNo
     * @return
     */
    List<MrpManage> findByBatchNo(@Param("tradeCode") String tradeCode, @Param("batchNo") String batchNo);

} 