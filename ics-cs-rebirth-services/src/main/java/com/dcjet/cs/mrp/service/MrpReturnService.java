package com.dcjet.cs.mrp.service;

import com.dcjet.cs.base.service.BasicService;
import com.dcjet.cs.dto.mrp.*;
import com.dcjet.cs.mrp.model.MrpImgExg;
import com.dcjet.cs.mrp.model.MrpReturn;
import com.xdo.common.token.UserInfoToken;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MrpReturnService extends BasicService<MrpReturn, MrpReturnDto, MrpReturnParam, MrpReturnSearchParam> {

    /***
     *
     * @param headId
     * @return
     */
    List<MrpImgExg> bomCheck(String headId);

    /***
     * 折料
     * @param headId
     * @param userInfo
     */
    Pair<List<MrpImgExgDto>, String> start(String headId, MrpReturnStartParam param, UserInfoToken userInfo);

    /***
     *
     *
     * @param searchParam
     * @param token
     * @return
     */
    MrpReturnStatDto stat(MrpReturnSearchParam searchParam, UserInfoToken token);
}
