package com.dcjet.cs.mrp.manager.impl;

import com.dcjet.cs.dto.mrp.MrpPriceCavDto;
import com.dcjet.cs.dto.mrp.MrpPriceFetchDto;
import com.dcjet.cs.mrp.model.valueobject.*;
import com.dcjet.cs.mrp.util.MrpConstant;
import com.dcjet.cs.util.OkHttpUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 账册单价
 *
 * <AUTHOR>
 */
@Component
public class MrpPriceFetchEmsManager extends AbstractMrpPriceFetchManager  {

    @Override
    public List<MrpPriceFetchDto> fetch(MrpPriceFetchRequest param, UserInfoToken token) {
        Map<String, Object> httpParam = new HashMap<>(5);
        httpParam.put("emsNo", param.getEmsNo());
        httpParam.put("cavMark", param.getCavMark());
        httpParam.put("arcType", param.getDcrStage() ? 1 : 0);
        httpParam.put("taskID", UUID.randomUUID().toString());
        httpParam.put("data", param.getList().stream().map(it -> Collections.singletonMap("copGNo", it.getCopGNo()))
                .collect(Collectors.toList()));

        String url = getUrl(EMS_PRICE_URL);
        String requestBody = JsonObjectMapper.getInstance().toJson(httpParam);
        Instant startInstant = Instant.now();
        String json = OkHttpUtils.httpPostJson(url, MrpConstant.internalHttpHeader(token.getAccessToken()), requestBody);
        audit(param.getHeadId(), url, requestBody, json, Duration.between(startInstant, Instant.now()).toMillis(), token);
        TypeReference<ResultObject<MrpEmsPriceFetchResponse>> typeReference = new TypeReference<ResultObject<MrpEmsPriceFetchResponse>>() {};
        ResultObject<MrpEmsPriceFetchResponse> resultObject = JsonObjectMapper.getInstance().fromJson(json, typeReference);
        if (resultObject == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("获取单价出错！返回数据无法解析"));
        }

        if (!resultObject.isSuccess()) {
            throw new ArgumentException(resultObject.getMessage());
        }
        List<MrpEmsPriceFetchItem> list = resultObject.getData().getList();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream().map(it -> {
            MrpPriceFetchDto dto = new MrpPriceFetchDto();
            dto.setGNo(it.getGNo());
            dto.setCopGNo(it.getCopGNo());
            dto.setMaxPrice(it.getMaxDecPrice());
            dto.setMinPrice(it.getMinDecPrice());
            dto.setWeiAvgPrice(it.getAvgPrice());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<MrpPriceCavDto> cavs(String headId, String emsNo, UserInfoToken token) {
        Map<String, String> param = new HashMap<>(2);
        param.put("emsNo", emsNo);
        param.put("taskID", UUID.randomUUID().toString());

//        String json = "{\"success\":true,\"code\":200,\"message\":null,\"data\":{\"emsNo\":\"E221219A0002\",\"taskId\":\"xxx1x\",\"list\":[{\"tradeCode\":\"3117961898\",\"emsNo\":\"E221219A0002\",\"dcrTimes\":2,\"cavType\":\"BOM\",\"cavMark\":\"E221219A0002_02_01\",\"beginDate\":\"2020-09-02\",\"endDate\":\"2020-11-30\"},{\"tradeCode\":\"3117961898\",\"emsNo\":\"E221219A0002\",\"dcrTimes\":1,\"cavType\":\"BOM\",\"cavMark\":\"E221219A0002_01_02\",\"beginDate\":\"2020-04-28\",\"endDate\":\"2020-08-29\"},{\"tradeCode\":\"3117961898\",\"emsNo\":\"E221219A0002\",\"dcrTimes\":1,\"cavType\":\"BOM\",\"cavMark\":\"E221219A0002_01_01\",\"beginDate\":\"2020-04-28\",\"endDate\":\"2020-07-27\"},{\"tradeCode\":\"3117961898\",\"emsNo\":\"E221219A0002\",\"dcrTimes\":1,\"cavType\":\"BOM\",\"cavMark\":\"E221219A0002_01_03\",\"beginDate\":\"2020-04-28\",\"endDate\":\"2020-09-01\"}]},\"errorField\":null,\"pageIndex\":0,\"total\":0}";
//        String json ="<!doctype html><html lang=\\\"en\\\"><head><title>HTTP Status 404 – Not Found</title><style type=\\\"text/css\\\">h1 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:22px;} h2 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:16px;} h3 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:14px;} body {font-family:Tahoma,Arial,sans-serif;color:black;background-color:white;} b {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;} p {font-family:Tahoma,Arial,sans-serif;background:white;color:black;font-size:12px;} a {color:black;} a.name {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>";

        String url = getUrl(EMS_CAV_URL);
        String requestBody = JsonObjectMapper.getInstance().toJson(param);
        Instant startInstant = Instant.now();
        String json = OkHttpUtils.httpPostJson(url, MrpConstant.internalHttpHeader(token.getAccessToken()), requestBody);
        audit(headId, url,  requestBody, json, Duration.between(startInstant, Instant.now()).toMillis(), token);
        TypeReference<ResultObject<MrpEmsCavMarkResponse>> typeReference = new TypeReference<ResultObject<MrpEmsCavMarkResponse>>() {};
        ResultObject<MrpEmsCavMarkResponse> resultObject = JsonObjectMapper.getInstance().fromJson(json, typeReference);
        if (resultObject == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("获取报核标记出错！返回数据无法解析"));
        }

        if (!resultObject.isSuccess()) {
            throw new ArgumentException(resultObject.getMessage());
        }

        List<MrpEmsCavMark> list = resultObject.getData().getList();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(it -> {
          MrpPriceCavDto dto = new MrpPriceCavDto();
          dto.setCavMark(it.getCavMark());
          dto.setCavType(it.getCavType());
          dto.setBeginDate(it.getBeginDate());
          dto.setDcrTimes(it.getDcrTimes());
          dto.setEndDate(it.getEndDate());
          return dto;
        }).collect(Collectors.toList());

    }
}
