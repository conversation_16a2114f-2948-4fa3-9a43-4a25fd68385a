package com.dcjet.cs.mrp.model;

import com.dcjet.cs.base.model.BasicModel;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * bom
 * <p>
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-11-25
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_MRP_BOM")
public class MrpBom extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    public static final String DATA_SOURCE_ADD = "ADD";
    public static final String DATA_SOURCE_IMPORT = "IMPORT";
    public static final String DATA_SOURCE_COPY = "COPY";
    public static final String DATA_SOURCE_SWO = "SWO";
    public static final String DATA_SOURCE_BOM = "BOM";

    public static final Map<String, String> dataSourceMap = new HashMap<String, String>(4) {{
        put(DATA_SOURCE_ADD, "新增");
        put(DATA_SOURCE_IMPORT, "导入");
        put(DATA_SOURCE_COPY, "复制");
        put(DATA_SOURCE_SWO, "核销");
        put(DATA_SOURCE_BOM, "原始BOM接口");
    }};

    /**
     * 保税料件比例
     */
    @Column(name = "BOND_MTPCK_PRPR")
    private BigDecimal bondMtpckPrpr;

    /**
     * 料件商品编码
     */
    @Column(name = "CODE_T_S")
    private String codeTS;

    /**
     * 备案成品/半成品料号
     */
    @Column(name = "COP_EXG_NO")
    private String copExgNo;

    /**
     * 备案料件料号
     */
    @Column(name = "COP_IMG_NO")
    private String copImgNo;

    /**
     * 单耗
     */
    @Column(name = "DEC_ALL_CONSUME")
    private BigDecimal decAllConsume;

    /**
     * 净耗
     */
    @Column(name = "DEC_CM")
    private BigDecimal decCm;

    /**
     * 备案号
     */
    @Column(name = "EMS_NO")
    private String emsNo;

    /**
     * 成品商品编码
     */
    @Column(name = "EXG_CODE_T_S")
    private String exgCodeTS;

    /**
     * 成品商品名称
     */
    @Column(name = "EXG_G_NAME")
    private String exgGName;

    /**
     * 成品序号
     */
    @Column(name = "EXG_G_NO")
    private String exgGNo;

    /**
     * 版本号
     */
    @Column(name = "EXG_VERSION")
    private String exgVersion;

    /**
     * 企业成品/半成品料号
     */
    @Column(name = "FAC_EXG_NO")
    private String facExgNo;

    /**
     * 企业料号
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;

    /**
     * 成品半成品标识 I，E， 4
     */
    @Column(name = "G_MARK")
    private String gMark;

    /**
     * 料件商品名称
     */
    @Column(name = "G_NAME")
    private String gName;

    /**
     * 料件项号
     */
    @Column(name = "IMG_G_NO")
    private String imgGNo;


    /**
     * 无形损耗率
     */
    @Column(name = "DEC_DM_INVISIABLE")
    private BigDecimal decDmInvisiable;

    /**
     * 有形损耗率
     */
    @Column(name = "DEC_DM_VISIABLE")
    private BigDecimal decDmVisiable;

    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

    /***
     * 数据来源 (ADD, IMPORT, COPY, ERP,BOM)
     */
    @Column(name = "DATA_SOURCE")
    private String dataSource;

    @Transient
    private String hasUse;


    @Transient
    private int tempFlag;

    @Transient
    private String tempRemark;

    @Transient
    private int tempIndex;


    @Override
    public void preUpdate(UserInfoToken token) {
        super.preUpdate(token);
        initDefault();
    }

    @Override
    public void preInsert(UserInfoToken token) {
        super.preInsert(token);
        initDefault();
    }

    private void initDefault() {
        if (bondMtpckPrpr == null) {
            this.bondMtpckPrpr = BigDecimal.valueOf(100);
        }

        if (decDmInvisiable == null) {
            this.decDmInvisiable = BigDecimal.ZERO;
        }

        if (decDmVisiable == null) {
            this.decDmVisiable = BigDecimal.ZERO;
        }

        // 保税料件单耗=净耗/[1-（有形损耗+无形损耗）/100]*保税料件比例
        BigDecimal divisor = BigDecimal.ONE.subtract(this.getDecDmVisiable().add(this.getDecDmInvisiable()).divide(BigDecimal.valueOf(100)));
        if (BigDecimal.ZERO.equals(divisor)) {
            this.decAllConsume = BigDecimal.ZERO;
        } else {
            this.decAllConsume = this.getDecCm().divide(divisor, 9, BigDecimal.ROUND_HALF_UP).multiply(this.getBondMtpckPrpr().divide(BigDecimal.valueOf(100)));
        }
    }

    public MrpBom copy() {
        MrpBom bom = new MrpBom();
        BeanUtils.copyProperties(this, bom);
        bom.setDataSource(MrpBom.DATA_SOURCE_COPY);
        bom.setSid(null);
        return bom;
    }
}
