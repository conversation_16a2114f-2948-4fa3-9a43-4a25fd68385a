
package com.dcjet.cs.mrp.service;

import com.dcjet.cs.base.service.BasicService;
import com.dcjet.cs.base.service.ImportService;
import com.dcjet.cs.dto.dec.DecECustomsTrackDto;
import com.dcjet.cs.dto.mrp.*;
import com.dcjet.cs.mrp.model.MrpPrice;
import com.dcjet.cs.mrp.model.MrpReturnSummary;
import com.xdo.common.token.UserInfoToken;

import java.util.List;


/***
 * 内销单价计算及审核 service 接口
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2020-03-16
 */
public interface MrpPriceService 
        extends BasicService<MrpPrice, MrpPriceDto, MrpPriceParam, MrpPriceSearchParam>, ImportService<MrpPriceParam> {

    /***
     *
     * @param priceList
     * @param token
     */
    void insertBatch(List<MrpPrice> priceList, String headId, UserInfoToken token);

    /***
     * 明细拆分数据
     *
     * @param param
     * @param userInfoToken
     */
    void split(MrpPriceSplitParam param, UserInfoToken userInfoToken);

    /***
     * 明细拆分还原
     *
     * @param headId
     * @param sid
     * @param token
     */
    void restoreSplit(String headId, String sid, UserInfoToken token);

    /***
     * 获取核销标志数据源
     *
     * @param headId
     * @param token
     * @return
     */
    List<MrpPriceCavDto> getCavMark(String headId, UserInfoToken token);

    /***
     * 获取单价
     *
     * @param param
     * @param token
     */
    void fetch(MrpPriceFetchParam param, UserInfoToken token);


    /***
     * 获取备案料号计算单价的范围
     *
     * @param headId
     * @param copGNo
     * @return
     */
    List<MrpPriceCalcRangeDto> calcRange(String headId, String copGNo);

    List<MrpPriceDto> selectAll(MrpReturnSummary exportColumns, UserInfoToken userInfo);
}