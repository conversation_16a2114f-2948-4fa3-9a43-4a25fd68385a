
package com.dcjet.cs.mrp.service.impl;

import com.dcjet.cs.base.service.impl.BasicServiceImpl;
import com.dcjet.cs.common.component.DistributedLockComponent;
import com.dcjet.cs.common.model.ImportData;
import com.dcjet.cs.common.model.ImportValidation;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto.mrp.*;
import com.dcjet.cs.erp.dao.DecIBillHeadMapper;
import com.dcjet.cs.erp.dao.DecIBillListMapper;
import com.dcjet.cs.erp.model.DecIBillHead;
import com.dcjet.cs.erp.model.DecIBillList;
import com.dcjet.cs.mrp.dao.MrpAuditMapper;
import com.dcjet.cs.mrp.dao.MrpManageMapper;
import com.dcjet.cs.mrp.dao.MrpPriceMapper;
import com.dcjet.cs.mrp.dao.MrpReturnSummaryMapper;
import com.dcjet.cs.mrp.manager.MrpPriceFetchManager;
import com.dcjet.cs.mrp.manager.MrpPriceFetchManagerFactory;
import com.dcjet.cs.mrp.mapper.MrpPriceDtoMapper;
import com.dcjet.cs.mrp.model.*;
import com.dcjet.cs.mrp.model.valueobject.MrpPriceFetchItem;
import com.dcjet.cs.mrp.model.valueobject.MrpPriceFetchRequest;
import com.dcjet.cs.mrp.service.MrpPriceService;
import com.dcjet.cs.tax.exRate.component.ExchangeRateComponent;
import com.dcjet.cs.util.BatchUtil;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.variable.CommonVariable;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/***
 * 内销单价计算及审核 service 默认实现
 *
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2020-03-16
 */
@Service
public class MrpPriceServiceImpl
        extends BasicServiceImpl<MrpPriceMapper, MrpPrice, MrpPriceDto, MrpPriceParam, MrpPriceSearchParam, MrpPriceDtoMapper>
        implements MrpPriceService {

    @Resource
    private ExchangeRateComponent exchangeRateComponent;

    @Resource
    private MrpManageMapper mrpManageMapper;

    @Resource
    private MrpPriceFetchManagerFactory mrpPriceFetchManagerFactory;

    @Resource
    private MrpReturnSummaryMapper mrpReturnSummaryMapper;

    @Resource
    private MrpPriceDtoMapper mrpPriceDtoMapper;

    @Resource
    private MrpAuditMapper mrpAuditMapper;

    @Resource
    private DecIBillHeadMapper decIBillHeadMapper;

    @Resource
    private DecIBillListMapper decIBillListMapper;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private DistributedLockComponent distributedLockComponent;

    @Resource
    protected ApplicationEventPublisher applicationEventPublisher;
//    @Resource
//    protected BasicMapper basicMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(List<MrpPrice> priceList, String headId, UserInfoToken token) {
        mapper.deleteByHeadId(headId);
        int count = BatchUtil.batchPage(priceList).map(mapper::insertList).reduce((a, b) -> a + b).orElse(0);
        if (count != priceList.size()) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("批量新增试算出错"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void split(MrpPriceSplitParam param, UserInfoToken userInfo) {
        MrpPrice mrpPrice = mapper.selectByPrimaryKey(param.getSid());
        if (mrpPrice == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("找不到要拆分的数据"));
        }

        if (param.getQty().doubleValue() <= 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("拆分数量必须大于0"));
        }

        if (mrpPrice.getUsdApprDecPrice() == null) {
            throw new ArithmeticException(xdoi18n.XdoI18nUtil.t("审核单价为空的数据无法拆分"));
        }

        if (param.getOriginCountry().equals(mrpPrice.getOriginCountry()) && Objects.equals(param.getCertificateNo(), mrpPrice.getCertificateNo())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("存在相同的企业料号、原产国、原产地证号，无法再次拆分"));
        }

       MrpManage mrpManage = mrpManageMapper.selectByPrimaryKey(mrpPrice.getHeadId());
       if (!mrpManage.canUpdate()) {
           throw new ArgumentException(xdoi18n.XdoI18nUtil.t("只有处于【暂存】和【试算完成】的数据才可以进行拆分"));
       }
        String splitId = mrpPrice.getSplitId();
        int splitSerialNo;
        if (Strings.isNullOrEmpty(splitId)) {
            splitId = mrpPrice.getSid();
            mrpPrice.setSplitId(splitId);
            mrpPrice.setSplitSerialNo(1);
            splitSerialNo = 2;
        } else {
            MrpPrice record = new MrpPrice();
            record.setTradeCode(userInfo.getCompany());
            record.setHeadId(mrpPrice.getHeadId());
            record.setSplitId(splitId);
            List<MrpPrice> priceList = mapper.select(record);
            for (MrpPrice item : priceList) {
                if (item.getOriginCountry().equals(param.getOriginCountry()) && Objects.equals(param.getCertificateNo(), mrpPrice.getCertificateNo())) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("存在相同的企业料号、原产国、原产地证号，无法再次拆分"));
                }
            }

            splitSerialNo = priceList.size() + 1;
        }

        mrpPrice.setAdjustmentQty(mrpPrice.getAdjustmentQty().subtract(param.getQty()));
        mrpPrice.setUsdDecTotal(mrpPrice.getAdjustmentQty().multiply(mrpPrice.getUsdApprDecPrice()));
        mrpPrice.setRmbDecTotal(exchangeRateComponent.rmb(CommonVariable.CURR_USD, mrpPrice.getUsdDecTotal()));
        mrpPrice.preUpdate(userInfo);
        mapper.updateByPrimaryKey(mrpPrice);

        // 重新再次保存新拆分出来的数据
        mrpPrice.setSplitSerialNo(splitSerialNo);
        mrpPrice.setAdjustmentQty(param.getQty());
        mrpPrice.setUsdDecTotal(mrpPrice.getAdjustmentQty().multiply(mrpPrice.getUsdApprDecPrice()));
        mrpPrice.setRmbDecTotal(exchangeRateComponent.rmb(CommonVariable.CURR_USD, mrpPrice.getUsdDecTotal()));
        mrpPrice.setOriginCountry(param.getOriginCountry());
        mrpPrice.setCertificateNo(param.getCertificateNo());
        mrpPrice.setSid(null);
        mrpPrice.preInsert(userInfo);
        mapper.insert(mrpPrice);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreSplit(String headId, String sid, UserInfoToken token) {
        MrpManage mrpManage = mrpManageMapper.selectByPrimaryKey(headId);
        if (!mrpManage.canUpdate()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("只有处于【暂存】和【试算完成】的数据才可以进行还原拆分"));
        }

        List<MrpPrice> priceList = null;
        if (Strings.isNullOrEmpty(sid)) {
            // 如果SID为空，则清理所有已经拆分的数据
            Sqls sqls = Sqls.custom().andEqualTo("tradeCode", token.getCompany())
                    .andEqualTo("headId", headId)
                    .andIsNotNull("splitId");
            priceList = mapper.selectByExample(Example.builder(MrpPrice.class).where(sqls).build());
        } else {
            MrpPrice mrpPrice = mapper.selectByPrimaryKey(sid);
            if (mrpPrice == null) {
                throw new ArgumentException(xdoi18n.XdoI18nUtil.t("找不到要还原拆分的数据"));
            }
            if (Strings.isNullOrEmpty(mrpPrice.getSplitId())) {
                    throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前数据不是拆分后的数据"));
            }

            MrpPrice record = new MrpPrice();
            record.setTradeCode(token.getCompany());
            record.setHeadId(mrpPrice.getHeadId());
            record.setSplitId(mrpPrice.getSplitId());
            priceList = mapper.select(record);
        }

        Map<String, List<MrpPrice>> splitMap = priceList.stream().collect(Collectors.groupingBy(MrpPrice::getSplitId));
        splitMap.keySet().forEach(item -> {
            List<MrpPrice> splitList = splitMap.get(item);
            BigDecimal sumQty = splitList.stream().map(MrpPrice::getAdjustmentQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            List<String> idList = new ArrayList<>(splitList.size());
            MrpPrice restorePrice = null;
            for (MrpPrice mrpPrice : splitList) {
                if (mrpPrice.getSid().equals(mrpPrice.getSplitId())) {
                    restorePrice = mrpPrice;
                } else {
                    idList.add(mrpPrice.getSid());
                }
            }

            restorePrice.setSplitId(null);
            restorePrice.setSplitSerialNo(null);
            restorePrice.setAdjustmentQty(sumQty);
            if (restorePrice.getUsdApprDecPrice() != null) {
                restorePrice.setUsdDecTotal(restorePrice.getAdjustmentQty().multiply(restorePrice.getUsdApprDecPrice()));
            }
            restorePrice.setRmbDecTotal(exchangeRateComponent.rmb(CommonVariable.CURR_USD, restorePrice.getUsdDecTotal()));
            restorePrice.preUpdate(token);
            mapper.deleteByIdList(idList);
            mapper.updateByPrimaryKey(restorePrice);
        });
    }

    @Override
    public List<MrpPriceCavDto> getCavMark(@Nonnull String headId, UserInfoToken token) {
        if (Strings.isNullOrEmpty(headId)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("内销批次主键为空！"));
        }

        MrpManage manage = mrpManageMapper.selectByPrimaryKey(headId);
        if (manage == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("无法根据内销批次主键找到对应的内销批次信息！"));
        }

        if (!manage.canUpdate()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前批次的数据状态不允许进行此操作"));
        }

        return mrpPriceFetchManagerFactory.getFetchManager(manage.getEmsNo()).cavs(headId, manage.getEmsNo(), token);
    }

    @Override
    public void fetch(@Nonnull MrpPriceFetchParam param, UserInfoToken token) {
        if (Strings.isNullOrEmpty(param.getHeadId())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("内销批次主键为空！"));
        }

        MrpManage manage = mrpManageMapper.selectByPrimaryKey(param.getHeadId());
        if (manage == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("无法根据内销批次主键找到对应的内销批次信息！"));
        }

        if (!manage.canUpdate()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前批次的数据状态不允许进行此操作"));
        }



        List<MrpReturnSummary> mrpSummaryList = mrpReturnSummaryMapper.findByHeadId(param.getHeadId());


        String lockKey = "mrp-price".concat(param.getHeadId());
        boolean lock = distributedLockComponent.tryLock(lockKey, 60, token.getCompany(), token.getUserNo());
        if (!lock) {
            throw new ErrorException(429, xdoi18n.XdoI18nUtil.t("服务器正在计算中，请稍后！"));
        }

        try {

            List<MrpPriceFetchItem> itemList = mrpSummaryList.stream()
                    .filter(it -> !Strings.isNullOrEmpty(it.getCopGNo()))
                    .map(it -> {
                        MrpPriceFetchItem item = new MrpPriceFetchItem();
                        item.setGno(it.getGNo());
                        item.setCopGNo(it.getCopGNo());
                        return item;
                    }).distinct().collect(Collectors.toList());


            MrpPriceFetchManager fetchManager = mrpPriceFetchManagerFactory.getFetchManager(manage.getEmsNo());
            List<MrpPriceFetchDto> priceFetchList = BatchUtil.batchPage(itemList, 50)
                    .map(it -> {
                        MrpPriceFetchRequest fetchRequest = new MrpPriceFetchRequest();
                        fetchRequest.setHeadId(manage.getSid());
                        fetchRequest.setEmsNo(manage.getEmsNo());
                        fetchRequest.setDcrStage(param.getDcrStage());
                        fetchRequest.setCavMark(param.getCavMark());
                        fetchRequest.setList(it);
                        return fetchManager.fetch(fetchRequest, token);
                    }).flatMap(List::stream).collect(Collectors.toList());

            Map<String, MrpPriceFetchDto> priceMap = priceFetchList.stream().collect(Collectors.toMap(it -> it.getCopGNo(), Function.identity(), (a, b) -> a));

            List<MrpPrice> priceList = mrpSummaryList.stream().map(it -> {
                MrpPrice mrpPrice = mrpPriceDtoMapper.toPo(it);
                mrpPrice.setQty(it.getSumQty());
                mrpPrice.setAdjustmentQty(it.getAdjustmentQty());
                mrpPrice.preInsert(token);

                if (Strings.isNullOrEmpty(mrpPrice.getCopGNo())) {
                    return mrpPrice;
                }

                MrpPriceFetchDto priceFetchDto = priceMap.get(it.getCopGNo());
                if (priceFetchDto != null) {

                    BigDecimal price = null;
                    if (Constants.MRP_MIN.equals(param.getPriceType())) {
                        price = priceFetchDto.getMinPrice();
                    }

                    if (Constants.MRP_MAX.equals(param.getPriceType())) {
                        price = priceFetchDto.getMaxPrice();
                    }

                    if (Constants.MRP_WEI_AVG.equals(param.getPriceType())) {
                        price = priceFetchDto.getWeiAvgPrice();
                    }
                    if (price != null) {
                        mrpPrice.setUsdDecPrice(price.setScale(4, RoundingMode.UP));
                        mrpPrice.setUsdApprDecPrice(mrpPrice.getUsdDecPrice());
                        mrpPrice.setUsdDecTotal(mrpPrice.getUsdDecPrice().multiply(it.getAdjustmentQty()).setScale(2, RoundingMode.UP));
                        mrpPrice.setRmbDecTotal(exchangeRateComponent.rmb(CommonVariable.CURR_USD, mrpPrice.getUsdDecTotal()).setScale(2, RoundingMode.UP));
                    }
                }
                return mrpPrice;
            }).collect(Collectors.toList());
            insertBatch(priceList, param.getHeadId(), token);

            manage.setStatus(MrpManage.STATUS_STAGING);
            StringBuilder sb = new StringBuilder()
                    .append(xdoi18n.XdoI18nUtil.t("计算完成时间："))
                    .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")))
                    .append(xdoi18n.XdoI18nUtil.t("  单价折算汇率："))
                    .append(Optional.ofNullable(exchangeRateComponent.rmbRate(CommonVariable.CURR_USD)).map(BigDecimal::toString).orElse(""))
                    .append(xdoi18n.XdoI18nUtil.t("  选择清单范围："))
                    .append(manage.getEmsNo())
                    .append(" ")
                    .append(param.toDescription());
            manage.setPriceMark(sb.toString());
            manage.preUpdate(token);
            mrpManageMapper.updateByPrimaryKey(manage);

            MrpAudit mrpAudit = new MrpAudit(manage.getSid(), MrpAudit.M_MRP_PRICE, sb.toString());
            mrpAudit.setTag(MrpAudit.TAG_FETCH);
            mrpAudit.setMeta(JsonObjectMapper.getInstance().toJson(param));
            applicationEventPublisher.publishEvent(new MrpAuditEvent(mrpAudit, token));
        } finally {
            distributedLockComponent.unlock(lockKey);
        }

    }

    @Override
    public List<MrpPriceCalcRangeDto> calcRange(String headId, String copGNo) {
        if (Strings.isNullOrEmpty(headId)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("表头主键不能为空"));
        }
        if (Strings.isNullOrEmpty(copGNo)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("备案料号不能为空"));
        }

        List<MrpAudit> mrpAudits = mrpAuditMapper.findLatestByModule(headId, MrpAudit.M_MRP_PRICE);
        if (mrpAudits.size() == 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前数据无法获取单价计算范围"));
        }
        MrpAudit mrpAudit = mrpAudits.get(0);
        if (MrpAudit.TAG_FETCH.equals(mrpAudit.getTag())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("获取单价的数据无法获取单价计算范围"));
        }

        MrpPriceStartParam param = JsonObjectMapper.getInstance().fromJson(mrpAudit.getMeta(), MrpPriceStartParam.class);
        if (param == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("无法获取上次计算单价的参数，无法确定计算单价的参数"));
        }

        MrpManage mrpManage=mrpManageMapper.selectByPrimaryKey(headId);
        if(mrpManage!=null)
        {
            param.setEmsNo(mrpManage.getEmsNo());
        }
        param.setNo(copGNo);
        param.setTradeCode(mrpAudit.getTradeCode());
        List<MrpPriceCalcRangeDto> dtoList = new ArrayList<>(Constants.LIST_INITIAL_CAPACITY);
        List<DecIBillHead> billHeadList = decIBillHeadMapper.getMrpHeadList(param);
        for (DecIBillHead billHead : billHeadList) {
            Sqls sqls = Sqls.custom().andEqualTo("headId", billHead.getSid())
                    .andEqualTo("copGNo", copGNo);
            List<DecIBillList> decIBillLists = decIBillListMapper.selectByExample(Example.builder(DecIBillList.class).andWhere(sqls).build());
            for (DecIBillList billList : decIBillLists) {
                MrpPriceCalcRangeDto dto = new MrpPriceCalcRangeDto();
                dto.setListNo(billHead.getListNo());
                dto.setTradeMode(billHead.getTradeMode() + " " + pCodeHolder.getName(PCodeType.TRADE, billHead.getTradeMode()).orElse(""));
                dto.setDeclareDate(billHead.getDeclareDate());
                dto.setEntryNo(billHead.getEntryNo());
                if (!Strings.isNullOrEmpty(billHead.getTransMode())) {
                    dto.setTransMode(billHead.getTransMode() + " " + pCodeHolder.getName(PCodeType.TRANSAC, billHead.getTransMode()).orElse(""));
                }
                if (!Strings.isNullOrEmpty(billHead.getFeeMark())) {
                    dto.setFee(Objects.toString(billHead.getFeeCurr(), "") + "/" + Objects.toString(billHead.getFeeRate(), "") + "/" + billHead.getFeeMark());
                }
                if (!Strings.isNullOrEmpty(billHead.getInsurMark())) {
                    dto.setInsur(Objects.toString(billHead.getInsurCurr(), "") + "/" + Objects.toString(billHead.getInsurRate(), "") + "/" + billHead.getInsurMark());
                }
                if (!Strings.isNullOrEmpty(billHead.getOtherMark())) {
                    dto.setOther(Objects.toString(billHead.getOtherCurr(), "") + "/" + Objects.toString(billHead.getOtherRate(), "") + "/" + billHead.getOtherMark());
                }
                dto.setCopGNo(billList.getCopGNo());
                dto.setCodeTS(billList.getCodeTS());
                dto.setGName(billList.getGName());
                dto.setQty(billList.getQty());
                dto.setUnit(billList.getUnit() + " " + pCodeHolder.getName(PCodeType.UNIT, billList.getUnit()).orElse(""));
                dto.setOriginCountry(billList.getOriginCountry() + " " + pCodeHolder.getName(PCodeType.COUNTRY_OUTDATED, billList.getOriginCountry()).orElse(""));
                dto.setDecPrice(billList.getDecPrice());
                dto.setDecTotal(billList.getDecTotal());
                dto.setCurr(billList.getCurr() + " " + pCodeHolder.getName(PCodeType.CURR_OUTDATED, billList.getCurr()).orElse(""));

                Date rateDate = null;
                if (MrpPriceStartParam.EXCHANGE_RATE_DECLARE_DATE.equals(param.getExchangeRateType())) {
                    rateDate = billHead.getDeclareDate();
                }
                dto.setExchangeRate(exchangeRateComponent.usdRate(rateDate, billList.getCurr()));
                dtoList.add(dto);
            }
        }

        return dtoList;
    }

    @Override
    protected void updateEntityHook(MrpPrice entity, MrpPriceParam param, List<String> columnList, UserInfoToken token) {

        if (param.getUsdApprDecPrice() != null) {
            entity.setUsdDecTotal(entity.getAdjustmentQty().multiply(param.getUsdApprDecPrice()).setScale(2, RoundingMode.UP));
            entity.setRmbDecTotal(exchangeRateComponent.rmb(CommonVariable.CURR_USD, entity.getUsdDecTotal()).setScale(2, RoundingMode.UP));

            columnList.add("usdDecTotal");
            columnList.add("rmbDecTotal");
        }
    }

    @Override
    public ImportValidation<MrpPriceParam> importValidation(@Valid ImportData<MrpPriceParam> importData, ExcelImportParam param, UserInfoToken token) {
        List<MrpPriceParam>  correctList= importData.getCorrectData();

        Map<String, Object> bizParam = importData.getBizParam();

        List<MrpPriceParam> corrects = new ArrayList<>(correctList.size());
        String headId = Optional.ofNullable(bizParam.get("headId")).orElseThrow(() -> new ArgumentException(xdoi18n.XdoI18nUtil.t("headId不能为空"))).toString();
        Sqls sqls = Sqls.custom().andEqualTo("headId", headId);
        List<MrpPrice> priceList = mapper.selectByExample(Example.builder(MrpPrice.class).andWhere(sqls).build());

        Map<String, MrpPrice> map = priceList.stream().collect(Collectors.toMap(it -> it.getSid(), Function.identity(), (oldVal, newVal) -> oldVal));
        List<MrpPriceParam> errors = new ArrayList<>(correctList.size());
        for (MrpPriceParam val : correctList) {
            if (Strings.isNullOrEmpty(val.getSid())) {
                errors.add(val);
            } else {
                if (map.containsKey(val.getSid())) {
                    corrects.add(val);
                } else {
                    val.setErrMsg(xdoi18n.XdoI18nUtil.t("sid无效, 请重新导出再导入"));
                    errors.add(val);
                }
            }
        }

        return new ImportValidation<>(corrects, errors);
    }

    @Override
    public int importSave(ImportData<MrpPriceParam> data, UserInfoToken token) {
        if (data.getBizParam() == null) {
            throw new ArgumentException();
        }

        String headId = Optional.ofNullable(data.getBizParam().get("headId")).orElseThrow(() -> new ArgumentException(xdoi18n.XdoI18nUtil.t("headId不能为空"))).toString();
        Sqls sqls = Sqls.custom().andEqualTo("headId", headId);
        List<MrpPrice> priceList = mapper.selectByExample(Example.builder(MrpPrice.class).andWhere(sqls).build());
        Map<String, MrpPriceParam> map = data.getCorrectData().stream().collect(Collectors.toMap(it -> it.getSid(), Function.identity(), (oldVal, newVal) -> newVal));
        List<MrpPrice> updateList = new ArrayList<>(Constants.LIST_INITIAL_CAPACITY);
        priceList.forEach(it -> {
            if (map.containsKey(it.getSid())) {
                MrpPriceParam param = map.get(it.getSid());
                it.setCertificateNo(param.getCertificateNo());
                it.setUsdApprDecPrice(param.getUsdApprDecPrice());
                it.setOriginCountry(param.getOriginCountry());
                if (it.getUsdApprDecPrice() != null) {
                    it.setUsdDecTotal(it.getAdjustmentQty().multiply(it.getUsdApprDecPrice()).setScale(2, RoundingMode.UP));
                    it.setRmbDecTotal(exchangeRateComponent.rmb(CommonVariable.CURR_USD, it.getUsdDecTotal()).setScale(2, RoundingMode.UP));
                }
                it.preUpdate(token);
                updateList.add(it);
            }
        });
        updateStatusStaging(headId);
        return batchUpdate(updateList, null);
    }

    private void updateStatusStaging(String sid) {
        MrpManage manage = mrpManageMapper.selectByPrimaryKey(sid);
        if (manage == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("找不到要修改的内销批次"));
        }

        if (!manage.canUpdate()) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("当前批次的数据状态不允许进行此操作"));
        }

        manage.setStatus(MrpManage.STATUS_STAGING);
        List<String> updateProperties = manage.preUpdateProperties();
        updateProperties.add("status");
        mrpManageMapper.updateByPrimaryKeySelectiveColumns(manage, updateProperties);
    }


    @Override
    public List<MrpPriceDto> selectAll(MrpReturnSummary exportColumns, UserInfoToken userInfo) {
        MrpPrice mrpPrice = mrpPriceDtoMapper.toPo(exportColumns);
        mrpPrice.setTradeCode(userInfo.getCompany());
        List<MrpPriceDto> mrpPriceDtos = new ArrayList<>();
        List<MrpPrice> mrpPrices = mapper.getList(mrpPrice);
        if (CollectionUtils.isNotEmpty(mrpPrices)) {
            mrpPriceDtos = mrpPrices.stream().map(head -> {
                MrpPriceDto dto = mrpPriceDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return mrpPriceDtos;
    }


}
