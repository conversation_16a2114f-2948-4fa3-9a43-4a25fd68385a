package com.dcjet.cs.mrp.dao;

import com.dcjet.cs.base.repository.BasicMapper;
import com.dcjet.cs.dto.mrp.MrpImgExgSearchParam;
import com.dcjet.cs.dto.mrp.MrpImgExgStatDto;
import com.dcjet.cs.mrp.model.MrpImgExg;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/***
 *
 * <AUTHOR>
 */
public interface MrpImgExgMapper extends BasicMapper<MrpImgExg> {

    /***
     *
     * @param headId
     * @return
     */
    List<MrpImgExg> findByHeadId(@Param("headId") String headId);

    /***
     * bom完整性检查
     * @see MrpImgExgMapper#bomCheck_optimize(String, String)
     *
     * @param headId
     * @param emsNo
     * @return
     */
    @Deprecated
    List<MrpImgExg> bomCheck(@Param("headId") String headId, @Param("emsNo") String emsNo);

    /***
     * bom完整性检查（语义优化）
     *
     * <AUTHOR>
     * @param headId
     * @param emsNo
     * @return
     */
    List<MrpImgExg> bomCheck_optimize(@Param("headId") String headId, @Param("emsNo") String emsNo);

    /**
     * 获取需要折料的数据
     *
     * @param headId
     * @param emsNo
     * @return
     */
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = Integer.MIN_VALUE)
    void findReturnByBom(@Param("headId") String headId, @Param("emsNo") String emsNo, ResultHandler<MrpImgExg> handler);


    /***
     * 获取最大的序号
     *
     * @param headId
     * @return
     */
    Integer getMaxSerialNo(@Param("headId") String headId);

    /***
     *
     * @param searchParam
     * @return
     */
    MrpImgExgStatDto statGMark(MrpImgExgSearchParam searchParam);

    /***
     * 查询提取的根据汇总的企业料号数量为负数的所有数据
     */
    List<MrpImgExg> selectExgByReturnSummaryQtyLteZero(@Param("headId") String headId, @Param("facGNoList") List<String> facGNoList);

    /***
     * 查询提取的所有提取的指定料号的数据
     *
     * @param headId
     * @param facGNoList
     * @return
     */
    List<MrpImgExg> selectImgByReturnSummaryQtyLteZero(@Param("headId") String headId, @Param("facGNoList") List<String> facGNoList);


    /***
     * 删除根据汇总的企业料号数量为负数的所有数据
     *
     * @param headId
     * @param facGNoList
     */
    void deleteExgByReturnSummaryQtyLteZero(@Param("headId") String headId, @Param("facGNoList") List<String> facGNoList);
}