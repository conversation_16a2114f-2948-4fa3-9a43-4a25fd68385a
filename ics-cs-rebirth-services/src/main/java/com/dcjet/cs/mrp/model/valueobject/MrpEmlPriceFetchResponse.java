package com.dcjet.cs.mrp.model.valueobject;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Setter @Getter
public class MrpEmlPriceFetchResponse {
    /***
     * "copGNo": "string",
     * "decPrice": 0,
     * "emsNo": "string",
     * "gName": "string",
     * "gNo": 0,
     * "gname": "string",
     * "gno": 0,
     * "insertUser": "string",
     * "inserttime": "2020-12-23T08:55:37.027Z",
     * "maxDecPrice": 0,
     * "minDecPrice": 0,
     * "sid": "string",
     * "unit": "string",
     * "unitName": "string",
     * "updateTime": "2020-12-23T08:55:37.027Z",
     * "updateUser": "string",
     * "weiAvgPrice": 0,
     * "weiAvgPriceCif": 0,
     * "weiAvgPriceCifCn": "string"
     */

    private String copGNo;
    private String decPrice;
    private String emsNo;
    private String GNo;
    private String GName;
    private BigDecimal maxDecPrice;
    private BigDecimal minDecPrice;
    private BigDecimal weiAvgPrice;
    private BigDecimal weiAvgPriceCif;
    private String weiAvgPriceCifCn;

}
