<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.ocrInvoice.dao.GwOcrLogMapper">
    <resultMap id="gwOcrLogResultMap" type="com.dcjet.cs.ocrInvoice.model.GwOcrLog">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="bussiness_pk" property="bussinessPk" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="error_message" property="errorMessage" jdbcType="VARCHAR" />
		<result column="bussiness_type" property="bussinessType" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="upload_res" property="uploadRes" jdbcType="VARCHAR" />
		<result column="task_id" property="taskId" jdbcType="VARCHAR" />
        <result column="ems_list_no" property="emsListNo" jdbcType="VARCHAR" />
        <result column="extend_filed1" property="extendFiled1" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,bussiness_pk
     ,status
     ,error_message
     ,bussiness_type
     ,trade_code
     ,insert_user
     ,insert_user_name
     ,insert_time
     ,update_user
     ,update_user_name
     ,update_time
     ,upload_res
     ,task_id
     ,ems_list_no
     ,extend_filed1
    </sql>
    <sql id="condition">
        and t.trade_code = #{tradeCode}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwOcrLogResultMap" parameterType="com.dcjet.cs.ocrInvoice.model.GwOcrLog">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_gw_ocr_log t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getTaskListByStatus" resultType="com.dcjet.cs.ocrInvoice.model.GwOcrLog">
        select <include refid="Base_Column_List" /> from t_gw_ocr_log where status = #{status}
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_gw_ocr_log t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
