package com.dcjet.cs.ocrInvoice.mapper;

import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIHeadLocDto;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIHeadLocParam;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHeadLoc;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwOcrInvoiceIHeadLocDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwOcrInvoiceIHeadLocDto toDto(GwOcrInvoiceIHeadLoc po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrInvoiceIHeadLoc toPo(GwOcrInvoiceIHeadLocParam param);
    /**
     * 数据库原始数据更新
     * @param gwOcrInvoiceIHeadLocParam
     * @param gwOcrInvoiceIHeadLoc
     */
    void updatePo(GwOcrInvoiceIHeadLocParam gwOcrInvoiceIHeadLocParam, @MappingTarget GwOcrInvoiceIHeadLoc gwOcrInvoiceIHeadLoc);
    default void patchPo(GwOcrInvoiceIHeadLocParam gwOcrInvoiceIHeadLocParam, GwOcrInvoiceIHeadLoc gwOcrInvoiceIHeadLoc) {
        // TODO 自行实现局部更新
    }
}
