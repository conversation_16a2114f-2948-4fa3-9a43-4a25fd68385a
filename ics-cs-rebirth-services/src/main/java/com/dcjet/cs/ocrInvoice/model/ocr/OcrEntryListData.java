package com.dcjet.cs.ocrInvoice.model.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 报关单草单表头
 * @author: WJ
 * @createDate: 2022/8/1 16:44
 */
@Data
public class OcrEntryListData implements Serializable {
    @ApiModelProperty("境内货源地")
    @JsonProperty("BGDCommodity_Gds_DOMESTIC")
    private OcrFieldInfo BGDCommodity_Gds_DOMESTIC;

    @ApiModelProperty("最终目的国代码")
    @JsonProperty("BGDCommodity_Gds_DEST_CNT_CODE")
    private OcrFieldInfo BGDCommodity_Gds_DEST_CNT_CODE;

    @ApiModelProperty("原产国代码")
    @JsonProperty("BGDCommodity_Gds_COO_CODE")
    private OcrFieldInfo BGDCommodity_Gds_COO_CODE;

    @ApiModelProperty("原产国")
    @JsonProperty("BGDCommodity_Gds_COO")
    private OcrFieldInfo BGDCommodity_Gds_COO;

    @ApiModelProperty("申报数量")
    @JsonProperty("BGDCommodity_Gds_QTY")
    private OcrFieldInfo BGDCommodity_Gds_QTY;

    @ApiModelProperty("申报单位")
    @JsonProperty("BGDCommodity_Gds_UNIT")
    private OcrFieldInfo BGDCommodity_Gds_UNIT;

    @ApiModelProperty("法定数量")
    @JsonProperty("BGDCommodity_Gds_QTY1")
    private OcrFieldInfo BGDCommodity_Gds_QTY1;

    @ApiModelProperty("法定单位")
    @JsonProperty("BGDCommodity_Gds_UNIT1")
    private OcrFieldInfo BGDCommodity_Gds_UNIT1;

    @ApiModelProperty("征免代码")
    @JsonProperty("BGDCommodity_Gds_TAX_CODE")
    private OcrFieldInfo BGDCommodity_Gds_TAX_CODE;

    @ApiModelProperty("规格型号")
    @JsonProperty("BGDCommodity_Gds_Desc")
    private OcrFieldInfo BGDCommodity_Gds_Desc;

    @ApiModelProperty("总价")
    @JsonProperty("BGDCommodity_Gds_AMT")
    private OcrFieldInfo BGDCommodity_Gds_AMT;

    @ApiModelProperty("商品编码")
    @JsonProperty("BGDCommodity_Gds_HS")
    private OcrFieldInfo BGDCommodity_Gds_HS;

    @ApiModelProperty("最终目的国")
    @JsonProperty("BGDCommodity_Gds_DEST_CNT")
    private OcrFieldInfo BGDCommodity_Gds_DEST_CNT;

    @ApiModelProperty("项号")
    @JsonProperty("BGDCommodity_Gds_ITEM")
    private OcrFieldInfo BGDCommodity_Gds_ITEM;

    @ApiModelProperty("征免方式")
    @JsonProperty("BGDCommodity_Gds_TAX")
    private OcrFieldInfo BGDCommodity_Gds_TAX;

    @ApiModelProperty("境内货源地代码")
    @JsonProperty("BGDCommodity_Gds_DOMESTIC_CODE")
    private OcrFieldInfo BGDCommodity_Gds_DOMESTIC_CODE;

    @ApiModelProperty("商品名称")
    @JsonProperty("BGDCommodity_Gds_NAME")
    private OcrFieldInfo BGDCommodity_Gds_NAME;

    @ApiModelProperty("单价")
    @JsonProperty("BGDCommodity_Gds_PRICE")
    private OcrFieldInfo BGDCommodity_Gds_PRICE;

    @ApiModelProperty("币制")
    @JsonProperty("BGDCommodity_Gds_CURR")
    private OcrFieldInfo BGDCommodity_Gds_CURR;

    @ApiModelProperty("法二数量")
    @JsonProperty("BGDCommodity_Gds_QTY2")
    private OcrFieldInfo BGDCommodity_Gds_QTY2;

    @ApiModelProperty("法二单位")
    @JsonProperty("BGDCommodity_Gds_UNIT2")
    private OcrFieldInfo BGDCommodity_Gds_UNIT2;
}
