package com.dcjet.cs.ocrInvoice.dao;

import com.dcjet.cs.ocrInvoice.model.GwOcrLogDetail;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwOcrLogDetail
* <AUTHOR>
* @date: 2021-9-29
*/
public interface GwOcrLogDetailMapper extends Mapper<GwOcrLogDetail> {
    /**
     * 查询获取数据
     * @param gwOcrLogDetail
     * @return
     */
    List<GwOcrLogDetail> getList(GwOcrLogDetail gwOcrLogDetail);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
