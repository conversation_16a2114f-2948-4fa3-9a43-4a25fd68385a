package com.dcjet.cs.ocrInvoice.model.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: WJ
 * @createDate: 2022/4/24 16:23
 */
@Data
public class LvOcrFPListData {

    @ApiModelProperty("数量")
    @JsonProperty("FPCommodity_Gds_Qty")
    private OcrFieldInfo FPCommodity_Gds_Qty;

    @ApiModelProperty("单价")
    @JsonProperty("FPCommodity_Gds_Price")
    private OcrFieldInfo FPCommodity_Gds_Price;

    @ApiModelProperty("金额")
    @JsonProperty("FPCommodity_Gds_AMT")
    private OcrFieldInfo FPCommodity_Gds_AMT;

    @ApiModelProperty("商品描述")
    @JsonProperty("FPCommodity_Gds_Desc")
    private OcrFieldInfo FPCommodity_Gds_Desc;

    @ApiModelProperty("料号")
    @JsonProperty("FPCommodity_Gds_PartNo")
    private OcrFieldInfo FPCommodity_Gds_PartNo;

    @ApiModelProperty("原产国")
    @JsonProperty("FPCommodity_Gds_COO")
    private OcrFieldInfo FPCommodity_Gds_COO;

    @ApiModelProperty("数量单位")
    @JsonProperty("FPCommodity_Gds_Unit")
    private OcrFieldInfo FPCommodity_Gds_Unit;

    @ApiModelProperty("表体")
    @JsonProperty("FPCommodity_Attach")
    private List<LvOcrFPListCountryData> FPCommodity_Attach;

}
