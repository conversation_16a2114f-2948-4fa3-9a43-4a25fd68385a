package com.dcjet.cs.ocrInvoice.mapper;

import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListLocDto;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIListLocParam;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIListLoc;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-30
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwOcrInvoiceIListLocDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwOcrInvoiceIListLocDto toDto(GwOcrInvoiceIListLoc po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrInvoiceIListLoc toPo(GwOcrInvoiceIListLocParam param);
    /**
     * 数据库原始数据更新
     * @param gwOcrInvoiceIListLocParam
     * @param gwOcrInvoiceIListLoc
     */
    void updatePo(GwOcrInvoiceIListLocParam gwOcrInvoiceIListLocParam, @MappingTarget GwOcrInvoiceIListLoc gwOcrInvoiceIListLoc);
    default void patchPo(GwOcrInvoiceIListLocParam gwOcrInvoiceIListLocParam, GwOcrInvoiceIListLoc gwOcrInvoiceIListLoc) {
        // TODO 自行实现局部更新
    }
}
