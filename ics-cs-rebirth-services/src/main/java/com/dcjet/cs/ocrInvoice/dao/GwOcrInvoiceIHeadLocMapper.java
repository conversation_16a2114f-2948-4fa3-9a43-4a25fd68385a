package com.dcjet.cs.ocrInvoice.dao;

import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHeadLoc;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwOcrInvoiceIHeadLoc
* <AUTHOR>
* @date: 2021-9-29
*/
public interface GwOcrInvoiceIHeadLocMapper extends Mapper<GwOcrInvoiceIHeadLoc> {
    /**
     * 查询获取数据
     * @param gwOcrInvoiceIHeadLoc
     * @return
     */
    List<GwOcrInvoiceIHeadLoc> getList(GwOcrInvoiceIHeadLoc gwOcrInvoiceIHeadLoc);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
