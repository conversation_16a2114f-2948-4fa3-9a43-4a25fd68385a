<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceEListMapper">
    <resultMap id="gwOcrInvoiceEListResultMap" type="com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEList">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="serial_no" property="serialNo" jdbcType="NUMERIC" />
		<result column="order_no" property="orderNo" jdbcType="VARCHAR" />
		<result column="order_serial_no" property="orderSerialNo" jdbcType="VARCHAR" />
		<result column="fac_g_no" property="facGNo" jdbcType="VARCHAR" />
		<result column="good_desc" property="goodDesc" jdbcType="VARCHAR" />
		<result column="qty" property="qty" jdbcType="NUMERIC" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="unit_convert" property="unitConvert" jdbcType="VARCHAR" />
		<result column="qty_1" property="qty1" jdbcType="NUMERIC" />
		<result column="dec_price" property="decPrice" jdbcType="NUMERIC" />
		<result column="dec_price_h" property="decPriceH" jdbcType="NUMERIC" />
		<result column="dec_price_t" property="decPriceT" jdbcType="NUMERIC" />
		<result column="dec_total" property="decTotal" jdbcType="NUMERIC" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="curr_convert" property="currConvert" jdbcType="VARCHAR" />
		<result column="net_wt" property="netWt" jdbcType="NUMERIC" />
		<result column="net_wt_unit" property="netWtUnit" jdbcType="VARCHAR" />
		<result column="gross_wt" property="grossWt" jdbcType="NUMERIC" />
		<result column="gross_wt_unit" property="grossWtUnit" jdbcType="VARCHAR" />
		<result column="origin_country" property="originCountry" jdbcType="VARCHAR" />
		<result column="origin_country_convert" property="originCountryConvert" jdbcType="VARCHAR" />
		<result column="pack_num" property="packNum" jdbcType="NUMERIC" />
		<result column="wrap_type" property="wrapType" jdbcType="VARCHAR" />
		<result column="wrap_no" property="wrapNo" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="bussiness_type" property="bussinessType" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="net_wt_unit_convert" property="netWtUnitConvert" jdbcType="VARCHAR" />
		<result column="gross_wt_unit_convert" property="grossWtUnitConvert" jdbcType="VARCHAR" />
        <result column="task_id" property="taskId" jdbcType="VARCHAR" />

        <result column="order_no_loc" property="loc.orderNoLoc" jdbcType="VARCHAR" />
        <result column="order_serial_no_loc" property="loc.orderSerialNoLoc" jdbcType="VARCHAR" />
        <result column="fac_g_no_loc" property="loc.facGNoLoc" jdbcType="VARCHAR" />
        <result column="good_desc_loc" property="loc.goodDescLoc" jdbcType="VARCHAR" />
        <result column="qty_loc" property="loc.qtyLoc" jdbcType="VARCHAR" />
        <result column="unit_loc" property="loc.unitLoc" jdbcType="VARCHAR" />
        <result column="qty_1_loc" property="loc.qty1Loc" jdbcType="VARCHAR" />
        <result column="dec_price_loc" property="loc.decPriceLoc" jdbcType="VARCHAR" />
        <result column="dec_price_h_loc" property="loc.decPriceHLoc" jdbcType="VARCHAR" />
        <result column="dec_price_t_loc" property="loc.decPriceTLoc" jdbcType="VARCHAR" />
        <result column="dec_total_loc" property="loc.decTotalLoc" jdbcType="VARCHAR" />
        <result column="curr_loc" property="loc.currLoc" jdbcType="VARCHAR" />
        <result column="net_wt_loc" property="loc.netWtLoc" jdbcType="VARCHAR" />
        <result column="net_wt_unit_loc" property="loc.netWtUnitLoc" jdbcType="VARCHAR" />
        <result column="gross_wt_loc" property="loc.grossWtLoc" jdbcType="VARCHAR" />
        <result column="gross_wt_unit_loc" property="loc.grossWtUnitLoc" jdbcType="VARCHAR" />
        <result column="origin_country_loc" property="loc.originCountryLoc" jdbcType="VARCHAR" />
        <result column="pack_num_loc" property="loc.packNumLoc" jdbcType="VARCHAR" />
        <result column="wrap_type_loc" property="loc.wrapTypeLoc" jdbcType="VARCHAR" />
        <result column="wrap_no_loc" property="loc.wrapNoLoc" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     t.sid
     ,t.serial_no
     ,t.order_no
     ,t.order_serial_no
     ,t.fac_g_no
     ,t.good_desc
     ,t.qty
     ,t.unit
     ,t.unit_convert
     ,t.qty_1
     ,t.dec_price
     ,t.dec_price_h
     ,t.dec_price_t
     ,t.dec_total
     ,t.curr
     ,t.curr_convert
     ,t.net_wt
     ,t.net_wt_unit
     ,t.gross_wt
     ,t.gross_wt_unit
     ,t.origin_country
     ,t.origin_country_convert
     ,t.pack_num
     ,t.wrap_type
     ,t.wrap_no
     ,t.head_id
     ,t.bussiness_type
     ,t.trade_code
     ,t.insert_user
     ,t.insert_user_name
     ,t.insert_time
     ,t.update_user
     ,t.update_user_name
     ,t.update_time
     ,t.net_wt_unit_convert
     ,t.gross_wt_unit_convert
     ,t.task_id
    </sql>
    <sql id="Loc_Column_List">
        ,loc.order_no_loc
        ,loc.order_serial_no_loc
        ,loc.fac_g_no_loc
        ,loc.good_desc_loc
        ,loc.qty_loc
        ,loc.unit_loc
        ,loc.qty_1_loc
        ,loc.dec_price_loc
        ,loc.dec_price_h_loc
        ,loc.dec_price_t_loc
        ,loc.dec_total_loc
        ,loc.curr_loc
        ,loc.net_wt_loc
        ,loc.net_wt_unit_loc
        ,loc.gross_wt_loc
        ,loc.gross_wt_unit_loc
        ,loc.origin_country_loc
        ,loc.pack_num_loc
        ,loc.wrap_type_loc
        ,loc.wrap_no_loc
    </sql>
    <sql id="condition">
        and t.trade_code = #{tradeCode}
        <if test="sid != null and sid != ''">
            and t.SID = #{sid}
        </if>
        <if test="headId != null and headId != ''">
            and t.HEAD_ID = #{headId}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwOcrInvoiceEListResultMap" parameterType="com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEList">
        SELECT
        <include refid="Base_Column_List" />
        <if test="needLoc != null and needLoc == true">
            <include refid="Loc_Column_List"></include>
        </if>
        FROM
            t_gw_ocr_invoice_e_list t
            <if test="needLoc != null and needLoc == true">
                LEFT JOIN t_gw_ocr_invoice_e_list_loc loc ON loc.SID = t.SID
            </if>
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_gw_ocr_invoice_e_list t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="selectByHeadIds" resultMap="gwOcrInvoiceEListResultMap">
        select
        <include refid="Base_Column_List" />
        from t_gw_ocr_invoice_e_list t
        where t.HEAD_ID in
        <foreach collection="headIds" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by t.serial_no
    </select>
</mapper>
