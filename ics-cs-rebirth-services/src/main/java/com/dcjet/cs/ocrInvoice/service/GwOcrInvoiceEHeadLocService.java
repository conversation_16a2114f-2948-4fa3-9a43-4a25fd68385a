package com.dcjet.cs.ocrInvoice.service;

import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEHeadLocDto;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEHeadLocParam;
import com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceEHeadLocMapper;
import com.dcjet.cs.ocrInvoice.mapper.GwOcrInvoiceEHeadLocDtoMapper;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEHeadLoc;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2023-02-13
 */
@Service
public class GwOcrInvoiceEHeadLocService extends BaseService<GwOcrInvoiceEHeadLoc> {
    @Resource
    private GwOcrInvoiceEHeadLocMapper mapper;
    @Resource
    private GwOcrInvoiceEHeadLocDtoMapper dtoMapper;
    @Override
    public Mapper<GwOcrInvoiceEHeadLoc> getMapper() {
        return mapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwOcrInvoiceEHeadLocParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwOcrInvoiceEHeadLocDto>> getListPaged(GwOcrInvoiceEHeadLocParam gwOcrInvoiceEHeadLocParam, PageParam pageParam) {
        // 启用分页查询
        GwOcrInvoiceEHeadLoc gwOcrInvoiceEHeadLoc = dtoMapper.toPo(gwOcrInvoiceEHeadLocParam);
        Page<GwOcrInvoiceEHeadLoc> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(gwOcrInvoiceEHeadLoc));
        List<GwOcrInvoiceEHeadLocDto> gwOcrInvoiceEHeadLocDtos = page.getResult().stream().map(head -> {
            GwOcrInvoiceEHeadLocDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwOcrInvoiceEHeadLocDto>> paged = ResultObject.createInstance(gwOcrInvoiceEHeadLocDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwOcrInvoiceEHeadLocParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceEHeadLocDto insert(GwOcrInvoiceEHeadLocParam gwOcrInvoiceEHeadLocParam, UserInfoToken userInfo) {
        GwOcrInvoiceEHeadLoc gwOcrInvoiceEHeadLoc = dtoMapper.toPo(gwOcrInvoiceEHeadLocParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwOcrInvoiceEHeadLoc.setSid(sid);
        gwOcrInvoiceEHeadLoc.setInsertUser(userInfo.getUserNo());
        gwOcrInvoiceEHeadLoc.setInsertTime(new Date());
        // 新增数据
        int insertStatus = mapper.insert(gwOcrInvoiceEHeadLoc);
        return  insertStatus > 0 ? dtoMapper.toDto(gwOcrInvoiceEHeadLoc) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param gwOcrInvoiceEHeadLocParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwOcrInvoiceEHeadLocDto update(GwOcrInvoiceEHeadLocParam gwOcrInvoiceEHeadLocParam, UserInfoToken userInfo) {
        GwOcrInvoiceEHeadLoc gwOcrInvoiceEHeadLoc = mapper.selectByPrimaryKey(gwOcrInvoiceEHeadLocParam.getSid());
        dtoMapper.updatePo(gwOcrInvoiceEHeadLocParam, gwOcrInvoiceEHeadLoc);
        gwOcrInvoiceEHeadLoc.setUpdateUser(userInfo.getUserNo());
        gwOcrInvoiceEHeadLoc.setUpdateTime(new Date());
        // 更新数据
        int update = mapper.updateByPrimaryKey(gwOcrInvoiceEHeadLoc);
        return update > 0 ? dtoMapper.toDto(gwOcrInvoiceEHeadLoc) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		mapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwOcrInvoiceEHeadLocDto> selectAll(GwOcrInvoiceEHeadLocParam exportParam, UserInfoToken userInfo) {
        GwOcrInvoiceEHeadLoc gwOcrInvoiceEHeadLoc = dtoMapper.toPo(exportParam);
        gwOcrInvoiceEHeadLoc.setTradeCode(userInfo.getCompany());
        List<GwOcrInvoiceEHeadLocDto> gwOcrInvoiceEHeadLocDtos = new ArrayList<>();
        List<GwOcrInvoiceEHeadLoc> gwOcrInvoiceEHeadLocs = mapper.getList(gwOcrInvoiceEHeadLoc);
        if (CollectionUtils.isNotEmpty(gwOcrInvoiceEHeadLocs)) {
            gwOcrInvoiceEHeadLocDtos = gwOcrInvoiceEHeadLocs.stream().map(head -> {
                GwOcrInvoiceEHeadLocDto dto = dtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwOcrInvoiceEHeadLocDtos;
    }

    /**
     *
     * @param sid
     * @return
     */
    public GwOcrInvoiceEHeadLocDto getOne(String sid) {
        GwOcrInvoiceEHeadLoc loc = mapper.selectByPrimaryKey(sid);
        if (loc == null) {
            return null;
        }
        return dtoMapper.toDto(loc);
    }
}
