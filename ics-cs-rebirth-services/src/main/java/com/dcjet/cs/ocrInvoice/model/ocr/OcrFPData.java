package com.dcjet.cs.ocrInvoice.model.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("")
public class OcrFPData implements Serializable {

    @ApiModelProperty("发票号码")
    @JsonProperty("FPINVOICE_NO")
    private OcrFieldInfo FPINVOICE_NO;

    @ApiModelProperty("发票日期")
    @JsonProperty("FPINVOICE_DATE")
    private OcrFieldInfo FPINVOICE_DATE;

    @ApiModelProperty("装箱单号码")
    @JsonProperty("FPPACKING_NO")
    private OcrFieldInfo FPPACKING_NO;

    @ApiModelProperty("关联号码")
    @JsonProperty("FPREFERENCE_NO")
    private OcrFieldInfo FPREFERENCE_NO;

    @ApiModelProperty("发货人")
    @JsonProperty("FPSHIPPER")
    private OcrFieldInfo FPSHIPPER;

    @ApiModelProperty("发货人代码")
    @JsonProperty("FPSHIPPER_CODE")
    private OcrFieldInfo FPSHIPPER_CODE;

    @ApiModelProperty("收货人")
    @JsonProperty("FPCONSIGNEE")
    private OcrFieldInfo FPCONSIGNEE;

    @ApiModelProperty("成交方式")
    @JsonProperty("FPINCOTERMS")
    private OcrFieldInfo FPINCOTERMS;

    @ApiModelProperty("总数量")
    @JsonProperty("FPTOTAL_QTY")
    private OcrFieldInfo FPTOTAL_QTY;

    @ApiModelProperty("总数量单位")
    @JsonProperty("FPTOTAL_QTYUNIT")
    private OcrFieldInfo FPTOTAL_QTYUNIT;

    @ApiModelProperty("总金额")
    @JsonProperty("FPTOTAL_AMOUNT")
    private OcrFieldInfo FPTOTAL_AMOUNT;

    @ApiModelProperty("币别")
    @JsonProperty("FPTOTAL_CURR")
    private OcrFieldInfo FPTOTAL_CURR;

    @ApiModelProperty("统一原产国")
    @JsonProperty("FPHEAD_COO")
    private OcrFieldInfo FPHEAD_COO;

    @ApiModelProperty("统一订单")
    @JsonProperty("FPHEAD_PO")
    private OcrFieldInfo FPHEAD_PO;

    @ApiModelProperty("托盘数")
    @JsonProperty("FPTOTAL_PALLET")
    private OcrFieldInfo FPTOTAL_PALLET;

    @ApiModelProperty("散箱数")
    @JsonProperty("FPBULK_CTNS")
    private OcrFieldInfo FPBULK_CTNS;

    @ApiModelProperty("总箱数")
    @JsonProperty("FPTOTAL_CTNS")
    private OcrFieldInfo FPTOTAL_CTNS;

    @ApiModelProperty("总件数")
    @JsonProperty("FPTOTAL_PKG")
    private OcrFieldInfo FPTOTAL_PKG;

    @ApiModelProperty("总净重")
    @JsonProperty("FPTOTAL_NW")
    private OcrFieldInfo FPTOTAL_NW;

    @ApiModelProperty("净重单位")
    @JsonProperty("FPTOTAL_NWUnit")
    private OcrFieldInfo FPTOTAL_NWUnit;

    @ApiModelProperty("总毛重")
    @JsonProperty("FPTOTAL_GW")
    private OcrFieldInfo FPTOTAL_GW;

    @ApiModelProperty("毛重单位")
    @JsonProperty("FPTOTAL_GWUnit")
    private OcrFieldInfo FPTOTAL_GWUnit;

    @ApiModelProperty("总体积")
    @JsonProperty("FPTOTAL_MEAS")
    private OcrFieldInfo FPTOTAL_MEAS;

    @ApiModelProperty("送货单号")
    @JsonProperty("FPCommodity_Delivery_no")
    private OcrFieldInfo FPCommodity_Delivery_no;

    @JsonProperty("FPCommodity")
    private List<OcrFPListData> FPCommodity;
    
}
