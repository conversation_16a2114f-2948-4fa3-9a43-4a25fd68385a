<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.ocrInvoice.dao.GwOcrInvoiceIHeadMapper">
    <resultMap id="gwOcrInvoiceIHeadResultMap" type="com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHead">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR" />
		<result column="invoice_date" property="invoiceDate" jdbcType="TIMESTAMP" />
		<result column="packing_no" property="packingNo" jdbcType="VARCHAR" />
		<result column="reference_no" property="referenceNo" jdbcType="VARCHAR" />
		<result column="shipper" property="shipper" jdbcType="VARCHAR" />
		<result column="shipper_code" property="shipperCode" jdbcType="VARCHAR" />
		<result column="consignee" property="consignee" jdbcType="VARCHAR" />
		<result column="trade_terms" property="tradeTerms" jdbcType="VARCHAR" />
		<result column="qty" property="qty" jdbcType="NUMERIC" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="unit_convert" property="unitConvert" jdbcType="VARCHAR" />
		<result column="total_amount" property="totalAmount" jdbcType="NUMERIC" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="curr_convert" property="currConvert" jdbcType="VARCHAR" />
		<result column="origin_country" property="originCountry" jdbcType="VARCHAR" />
		<result column="origin_country_convert" property="originCountryConvert" jdbcType="VARCHAR" />
		<result column="order_no" property="orderNo" jdbcType="VARCHAR" />
		<result column="pallet_num" property="palletNum" jdbcType="NUMERIC" />
		<result column="bulk_ctns" property="bulkCtns" jdbcType="NUMERIC" />
		<result column="total_ctns" property="totalCtns" jdbcType="NUMERIC" />
		<result column="pack_num" property="packNum" jdbcType="NUMERIC" />
		<result column="net_wt" property="netWt" jdbcType="NUMERIC" />
		<result column="net_wt_unit" property="netWtUnit" jdbcType="VARCHAR" />
		<result column="net_wt_unit_convert" property="netWtUnitConvert" jdbcType="VARCHAR" />
		<result column="gross_wt" property="grossWt" jdbcType="NUMERIC" />
		<result column="gross_wt_unit" property="grossWtUnit" jdbcType="VARCHAR" />
		<result column="gross_wt_unit_convert" property="grossWtUnitConvert" jdbcType="VARCHAR" />
		<result column="volume" property="volume" jdbcType="NUMERIC" />
		<result column="create_status" property="createStatus" jdbcType="VARCHAR" />
		<result column="modify_status" property="modifyStatus" jdbcType="VARCHAR" />
		<result column="scan_time" property="scanTime" jdbcType="TIMESTAMP" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="data_type" property="dataType" jdbcType="VARCHAR" />
        <result column="task_id" property="taskId" jdbcType="VARCHAR" />
        <result column="sub_task_id" property="subTaskId" jdbcType="VARCHAR" />
        <result column="ems_list_no" property="emsListNo" jdbcType="VARCHAR" />
        <result column="mawb" property="mawb" jdbcType="VARCHAR" />
        <result column="hawb" property="hawb" jdbcType="VARCHAR" />
        <result column="forwarder" property="forwarder" jdbcType="VARCHAR" />
        <result column="depport" property="depport" jdbcType="VARCHAR" />
        <result column="desport" property="desport" jdbcType="VARCHAR" />
        <result column="bill_date" property="billDate" jdbcType="VARCHAR" />
        <result column="flt_no" property="fltNo" jdbcType="VARCHAR" />
        <result column="flt_date" property="fltDate" jdbcType="VARCHAR" />
        <result column="flt_no_1st" property="fltNo1st" jdbcType="VARCHAR" />
        <result column="flt_date_1st" property="fltDate1st" jdbcType="VARCHAR" />
        <result column="freight" property="freight" jdbcType="NUMERIC" />
        <result column="total_chwt" property="totalChwt" jdbcType="NUMERIC" />
        <result column="delivery_note_no" property="deliveryNoteNo" jdbcType="VARCHAR" />


        <result column="invoice_no_loc" property="loc.invoiceNoLoc" jdbcType="VARCHAR" />
        <result column="invoice_date_loc" property="loc.invoiceDateLoc" jdbcType="VARCHAR" />
        <result column="packing_no_loc" property="loc.packingNoLoc" jdbcType="VARCHAR" />
        <result column="reference_no_loc" property="loc.referenceNoLoc" jdbcType="VARCHAR" />
        <result column="shipper_loc" property="loc.shipperLoc" jdbcType="VARCHAR" />
        <result column="shipper_code_loc" property="loc.shipperCodeLoc" jdbcType="VARCHAR" />
        <result column="consignee_loc" property="loc.consigneeLoc" jdbcType="VARCHAR" />
        <result column="trade_terms_loc" property="loc.tradeTermsLoc" jdbcType="VARCHAR" />
        <result column="qty_loc" property="loc.qtyLoc" jdbcType="VARCHAR" />
        <result column="unit_loc" property="loc.unitLoc" jdbcType="VARCHAR" />
        <result column="total_amount_loc" property="loc.totalAmountLoc" jdbcType="VARCHAR" />
        <result column="curr_loc" property="loc.currLoc" jdbcType="VARCHAR" />
        <result column="origin_country_loc" property="loc.originCountryLoc" jdbcType="VARCHAR" />
        <result column="order_no_loc" property="loc.orderNoLoc" jdbcType="VARCHAR" />
        <result column="pallet_num_loc" property="loc.palletNumLoc" jdbcType="VARCHAR" />
        <result column="bulk_ctns_loc" property="loc.bulkCtnsLoc" jdbcType="VARCHAR" />
        <result column="total_ctns_loc" property="loc.totalCtnsLoc" jdbcType="VARCHAR" />
        <result column="pack_num_loc" property="loc.packNumLoc" jdbcType="VARCHAR" />
        <result column="net_wt_loc" property="loc.netWtLoc" jdbcType="VARCHAR" />
        <result column="net_wt_unit_loc" property="loc.netWtUnitLoc" jdbcType="VARCHAR" />
        <result column="gross_wt_loc" property="loc.grossWtLoc" jdbcType="VARCHAR" />
        <result column="gross_wt_unit_loc" property="loc.grossWtUnitLoc" jdbcType="VARCHAR" />
        <result column="volume_loc" property="loc.volumeLoc" jdbcType="VARCHAR" />
        <result column="mawb_loc" property="loc.mawbLoc" jdbcType="VARCHAR" />
        <result column="hawb_loc" property="loc.hawbLoc" jdbcType="VARCHAR" />
        <result column="forwarder_loc" property="loc.forwarderLoc" jdbcType="VARCHAR" />
        <result column="depport_loc" property="loc.depportLoc" jdbcType="VARCHAR" />
        <result column="desport_loc" property="loc.desportLoc" jdbcType="VARCHAR" />
        <result column="bill_date_loc" property="loc.billDateLoc" jdbcType="VARCHAR" />
        <result column="flt_no_loc" property="loc.fltNoLoc" jdbcType="VARCHAR" />
        <result column="flt_date_loc" property="loc.fltDateLoc" jdbcType="VARCHAR" />
        <result column="flt_no_1st_loc" property="loc.fltNo1stLoc" jdbcType="VARCHAR" />
        <result column="flt_date_1st_loc" property="loc.fltDate1stLoc" jdbcType="VARCHAR" />
        <result column="freight_loc" property="loc.freightLoc" jdbcType="VARCHAR" />
        <result column="total_chwt_loc" property="loc.totalChwtLoc" jdbcType="VARCHAR" />
        <result column="delivery_note_no_loc" property="loc.deliveryNoteNoLoc" jdbcType="VARCHAR" />


        <result column="BOND_MARK" property="bondMark" jdbcType="VARCHAR" />
        <result column="TRADE_MODE" property="tradeMode" jdbcType="VARCHAR" />
        <result column="EMS_NO" property="emsNo" jdbcType="VARCHAR" />

	</resultMap>
	<sql id="Base_Column_List" >
     t.sid
     ,t.invoice_no
     ,t.invoice_date
     ,t.packing_no
     ,t.reference_no
     ,t.shipper
     ,t.shipper_code
     ,t.consignee
     ,t.trade_terms
     ,t.qty
     ,t.unit
     ,t.unit_convert
     ,t.total_amount
     ,t.curr
     ,t.curr_convert
     ,t.origin_country
     ,t.origin_country_convert
     ,t.order_no
     ,t.pallet_num
     ,t.bulk_ctns
     ,t.total_ctns
     ,t.pack_num
     ,t.net_wt
     ,t.net_wt_unit
     ,t.net_wt_unit_convert
     ,t.gross_wt
     ,t.gross_wt_unit
     ,t.gross_wt_unit_convert
     ,t.volume
     ,t.create_status
     ,t.modify_status
     ,t.scan_time
     ,t.head_id
     ,t.trade_code
     ,t.insert_user
     ,t.insert_user_name
     ,t.insert_time
     ,t.update_user
     ,t.update_user_name
     ,t.update_time
     ,t.data_type
     ,t.task_id
     ,t.sub_task_id
     ,t.ems_list_no
     ,t.mawb
	 ,t.hawb
	 ,t.forwarder
	 ,t.depport
	 ,t.desport
	 ,t.bill_date
	 ,t.flt_no
	 ,t.flt_date
	 ,t.flt_no_1st
	 ,t.flt_date_1st
	 ,t.freight
	 ,t.total_chwt
	 ,t.delivery_note_no
    </sql>
    <sql id="Loc_Column_List">
     ,loc.invoice_no_loc
     ,loc.invoice_date_loc
     ,loc.packing_no_loc
     ,loc.reference_no_loc
     ,loc.shipper_loc
     ,loc.shipper_code_loc
     ,loc.consignee_loc
     ,loc.trade_terms_loc
     ,loc.qty_loc
     ,loc.unit_loc
     ,loc.total_amount_loc
     ,loc.curr_loc
     ,loc.origin_country_loc
     ,loc.order_no_loc
     ,loc.pallet_num_loc
     ,loc.bulk_ctns_loc
     ,loc.total_ctns_loc
     ,loc.pack_num_loc
     ,loc.net_wt_loc
     ,loc.net_wt_unit_loc
     ,loc.gross_wt_loc
     ,loc.gross_wt_unit_loc
     ,loc.volume_loc
     ,loc.mawb_loc
	 ,loc.hawb_loc
	 ,loc.forwarder_loc
	 ,loc.depport_loc
	 ,loc.desport_loc
	 ,loc.bill_date_loc
	 ,loc.flt_no_loc
	 ,loc.flt_date_loc
	 ,loc.flt_no_1st_loc
	 ,loc.flt_date_1st_loc
	 ,loc.freight_loc
	 ,loc.total_chwt_loc
	 ,loc.delivery_note_no_loc
    </sql>
    <sql id="condition">
        <if test="sid != null and sid != ''">
            and t.sid = #{sid}
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
	      and t.invoice_no like '%'|| #{invoiceNo} || '%'
	    </if>
            <if test="_databaseId == 'oracle' and invoiceDateFrom != null and invoiceDateFrom != ''">
                <![CDATA[ and t.invoice_date >= to_date(#{invoiceDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="_databaseId == 'oracle' and invoiceDateTo != null and invoiceDateTo != ''">
                <![CDATA[ and t.invoice_date < to_date(#{invoiceDateTo}, 'yyyy-MM-dd hh24:mi:ss') + 1]]>
            </if>
            <if test="_databaseId == 'postgresql' and invoiceDateFrom != null and invoiceDateFrom != ''">
                <![CDATA[ and t.invoice_date >= to_timestamp(#{invoiceDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
            </if>
            <if test="_databaseId == 'postgresql' and invoiceDateTo != null and invoiceDateTo != ''">
                <![CDATA[ and t.invoice_date < to_timestamp(#{invoiceDateTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp + INTERVAL '1 day']]>
            </if>
        <if test="packingNo != null and packingNo != ''">
	      and t.packing_no like '%'|| #{packingNo} || '%'
	    </if>
        <if test="shipper != null and shipper != ''">
	      and t.shipper like '%'|| #{shipper} || '%'
	    </if>
        <if test="shipperCode != null and shipperCode != ''">
	    	and t.shipper_code = #{shipperCode}
	    </if>
        <if test="createStatus != null and createStatus != ''">
	    	and t.create_status = #{createStatus}
	    </if>
        <if test="modifyStatus != null and modifyStatus != ''">
	    	and t.modify_status = #{modifyStatus}
	    </if>
        <if test="dataType != null and dataType != ''">
	    	and t.data_type = #{dataType}
	    </if>
        <if test="taskId != null and taskId != ''">
            and t.task_Id = #{taskId}
        </if>
        <if test="subTaskId != null and subTaskId != ''">
            and t.sub_task_Id = #{subTaskId}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwOcrInvoiceIHeadResultMap" parameterType="com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHead">
        SELECT
        <include refid="Base_Column_List" />
        <if test="needLoc != null and needLoc == true">
            <include refid="Loc_Column_List" />
        </if>
        ,deci.BOND_MARK
        ,deci.TRADE_MODE
        ,deci.EMS_NO
        ,deci.entry_no
        , (select extend_filed3 from t_gwstd_http_config where type = 'OCR_RESULT') extend_filed3
        FROM
        t_gw_ocr_invoice_i_head t
            <if test="needLoc != null and needLoc == true">
                LEFT JOIN t_gw_ocr_invoice_i_head_loc loc ON loc.SID = t.SID
            </if>
            LEFT JOIN t_dec_erp_i_head_n deci ON deci.SID = t.HEAD_ID
        <where>
            and t.TRADE_CODE = #{tradeCode}
            <include refid="condition"></include>
            <if test="emsListNo != null and emsListNo != ''">
                and t.EMS_LIST_NO like '%'|| #{emsListNo} ||'%'
            </if>
            <if test="entryNo != null and entryNo != ''">
                and deci.entry_no like '%'|| #{entryNo} ||'%'
            </if>
        </where>
        order by scan_time desc ,data_type,invoice_no
    </select>

    <select id="selectSubTaskId" resultType="string">
        select
        distinct sub_task_id
        from t_gw_ocr_invoice_i_head t
        where t.sid in
        <foreach collection="sids" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_gw_ocr_invoice_i_head t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="selectBySids" resultMap="gwOcrInvoiceIHeadResultMap">
        select
        <include refid="Base_Column_List" />
        from t_gw_ocr_invoice_i_head t
        where t.sid in
        <foreach collection="sids" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by invoice_no
    </select>
    <select id="selectSumBySids" resultMap="gwOcrInvoiceIHeadResultMap">
        SELECT
        COALESCE ( A.invoice_no, B.invoice_no ) invoice_no,
        COALESCE ( A.shipper_code, B.shipper_code ) shipper_code,
        COALESCE ( A.shipper, B.shipper ) shipper,
        COALESCE ( A.trade_terms, B.trade_terms ) trade_terms,
        COALESCE ( A.origin_country_convert, B.origin_country_convert ) origin_country_convert,
        COALESCE ( A.qty, B.qty ) qty,
        COALESCE ( A.total_amount, B.total_amount ) total_amount,
        COALESCE ( A.pack_num, B.pack_num ) pack_num,
        COALESCE ( A.net_wt, B.net_wt ) net_wt,
        COALESCE ( A.gross_wt, B.gross_wt ) gross_wt,
        COALESCE ( A.volume, B.volume ) volume
        FROM
        ( SELECT
        MAX(invoice_no) invoice_no,
        MAX(shipper_code) shipper_code,
        MAX(shipper) shipper,
        MAX(trade_terms) trade_terms,
        MAX(origin_country_convert) origin_country_convert,
        SUM(qty) qty,
        SUM(total_amount) total_amount,
        SUM(pack_num) pack_num,
        SUM(net_wt) net_wt,
        SUM(gross_wt) gross_wt,
        SUM(volume) volume FROM t_gw_ocr_invoice_i_head T WHERE data_type = 'INVOICE'
        AND T.sid IN <foreach collection="sids" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach> ) A
        ,( SELECT
        MAX(invoice_no) invoice_no,
        MAX(shipper_code) shipper_code,
        MAX(shipper) shipper,
        MAX(trade_terms) trade_terms,
        MAX(origin_country_convert) origin_country_convert,
        SUM(qty) qty,
        SUM(total_amount) total_amount,
        SUM(pack_num) pack_num,
        SUM(net_wt) net_wt,
        SUM(gross_wt) gross_wt,
        SUM(volume) volume FROM t_gw_ocr_invoice_i_head tp WHERE data_type = 'PACKING'
        AND tp.sid IN <foreach collection="sids" index="index" item="item" open="(" close=")" separator=",">
        #{item}
        </foreach> ) B
    </select>

    <update id="updateStatus" parameterType="java.util.List">
        update t_gw_ocr_invoice_i_head set CREATE_STATUS = '1'
        where HEAD_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </update>
</mapper>
