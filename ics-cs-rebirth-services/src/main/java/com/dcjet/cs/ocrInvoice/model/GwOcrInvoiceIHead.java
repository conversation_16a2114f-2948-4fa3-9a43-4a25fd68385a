package com.dcjet.cs.ocrInvoice.model;

import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIHeadLocDto;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrFPData;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrTDData;
import com.dcjet.cs.ocrInvoice.model.ocr.OcrXDData;
import com.dcjet.cs.util.ExtractNumUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Setter
@Getter
@Table(name = "t_gw_ocr_invoice_i_head")
public class GwOcrInvoiceIHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 发票号码
     */
	@Column(name = "invoice_no")
	private  String invoiceNo;
	/**
     * 发票日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "invoice_date")
	private  Date invoiceDate;
	/**
     * 发票日期-开始
     */
	@Transient
	private String invoiceDateFrom;
	/**
     * 发票日期-结束
     */
	@Transient
    private String invoiceDateTo;
	/**
     * 装箱单号码
     */
	@Column(name = "packing_no")
	private  String packingNo;
	/**
     * 关联号码
     */
	@Column(name = "reference_no")
	private  String referenceNo;
	/**
     * 发货人
     */
	@Column(name = "shipper")
	private  String shipper;
	/**
     * 境外发货人代码
     */
	@Column(name = "shipper_code")
	private  String shipperCode;
	/**
     * 收货人
     */
	@Column(name = "consignee")
	private  String consignee;
	/**
     * 贸易条款
     */
	@Column(name = "trade_terms")
	private  String tradeTerms;
	/**
     * 总数量
     */
	@Column(name = "qty")
	private  BigDecimal qty;
	/**
     * 总数量单位
     */
	@Column(name = "unit")
	private  String unit;
	/**
     * 转换后单位
     */
	@Column(name = "unit_convert")
	private  String unitConvert;
	/**
     * 总金额
     */
	@Column(name = "total_amount")
	private  BigDecimal totalAmount;
	/**
     * 币制
     */
	@Column(name = "curr")
	private  String curr;
	/**
     * 转换后币制
     */
	@Column(name = "curr_convert")
	private  String currConvert;
	/**
     * 统一原产国
     */
	@Column(name = "origin_country")
	private  String originCountry;
	/**
     * 转换后原产国
     */
	@Column(name = "origin_country_convert")
	private  String originCountryConvert;
	/**
     * 统一订单号
     */
	@Column(name = "order_no")
	private  String orderNo;
	/**
     * 托盘数(件数)
     */
	@Column(name = "pallet_num")
	private  Integer palletNum;
	/**
     * 散箱数
     */
	@Column(name = "bulk_ctns")
	private  Integer bulkCtns;
	/**
     * 总箱数
     */
	@Column(name = "total_ctns")
	private  Integer totalCtns;
	/**
     * 总件数
     */
	@Column(name = "pack_num")
	private  Integer packNum;
	/**
     * 总净重
     */
	@Column(name = "net_wt")
	private  BigDecimal netWt;
	/**
     * 净重单位
     */
	@Column(name = "net_wt_unit")
	private  String netWtUnit;
	/**
     * 转换后净重单位
     */
	@Column(name = "net_wt_unit_convert")
	private  String netWtUnitConvert;
	/**
     * 总毛重
     */
	@Column(name = "gross_wt")
	private  BigDecimal grossWt;
	/**
     * 毛重单位
     */
	@Column(name = "gross_wt_unit")
	private  String grossWtUnit;
	/**
     * 转换后毛重单位
     */
	@Column(name = "gross_wt_unit_convert")
	private  String grossWtUnitConvert;
	/**
     * 总体积
     */
	@Column(name = "volume")
	private  BigDecimal volume;
	/**
     * 生成状态(1已识别 2已生成)
     */
	@Column(name = "create_status")
	private  String createStatus;
	/**
     * 修改状态（1未修改；2已修改）
     */
	@Column(name = "modify_status")
	private  String modifyStatus;
	/**
     * 扫描时间(暂时写入插入时间)
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "scan_time")
	private  Date scanTime;
	/**
     * 生成到预录入单后返回的表头ID
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 创建人名称
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 修改人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 修改人名称
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 修改数据时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 数据类型(INVOICE.发票 PACKING.箱单)
     */
	@Column(name = "data_type")
	private  String dataType;

	@Column(name = "task_id")
	private String taskId;

	@Column(name = "sub_task_id")
	private String subTaskId;

	/**
	 * 单据内部编号
	 */
	@Column(name = "ems_list_no")
	private  String emsListNo;

	@Column(name = "mawb")
	private String mawb;
	@Column(name = "hawb")
	private String hawb;
	@Column(name = "forwarder")
	private String forwarder;
	@Column(name = "depport")
	private String depport;
	@Column(name = "desport")
	private String desport;
	@Column(name = "bill_date")
	private String billDate;
	@Column(name = "flt_no")
	private String fltNo;
	@Column(name = "flt_date")
	private String fltDate;
	@Column(name = "flt_no_1st")
	private String fltNo1st ;
	@Column(name = "flt_date_1st")
	private String fltDate1st;
	@Column(name = "freight")
	private BigDecimal freight;
	@Column(name = "total_chwt")
	private BigDecimal totalChwt;

	/**
	 * 送货单号
	 */
	@Column(name = "delivery_note_no")
	private String deliveryNoteNo;

	@Transient
	private String bondMark;

	/**
	 * 报关单号
	 */
	@Transient
	private String entryNo;

	@Transient
	private String tradeMode;
	@Transient
	private String extendFiled3;


	@Transient
	private String emsNo;

	@Transient
	private Boolean needLoc;

	@Transient
	private GwOcrInvoiceIHeadLocDto loc;

	public GwOcrInvoiceIHead initData(GwOcrLog log) {

		this.sid = UUID.randomUUID().toString();
		this.tradeCode = log.getTradeCode();
		this.emsListNo = log.getEmsListNo();
		this.insertUser = log.getInsertUser();
		this.insertUserName = log.getInsertUserName();
		this.insertTime = new Date();

		this.taskId = log.getTaskId();
		this.subTaskId = log.getSubTaskId();
		this.createStatus = "1"; // 初始值：已识别
		this.modifyStatus = "1"; // 初始值：未修改

		this.scanTime = this.insertTime;
		return this;

	}

	public void assembly(OcrFPData ocrFPData) {
		if (ocrFPData.getFPINVOICE_NO() != null) {
			this.setInvoiceNo(ocrFPData.getFPINVOICE_NO().getValue());
		}
		this.setInvoiceDate(null);
		if (ocrFPData.getFPPACKING_NO() != null) {
			this.setPackingNo(ocrFPData.getFPPACKING_NO().getValue());
		}
		if (ocrFPData.getFPREFERENCE_NO() != null) {
			this.setReferenceNo(ocrFPData.getFPREFERENCE_NO().getValue());
		}
		if (ocrFPData.getFPSHIPPER() != null) {
			this.setShipper(ocrFPData.getFPSHIPPER().getValue());
		}
		if (ocrFPData.getFPSHIPPER_CODE() != null) {
			this.setShipperCode(ocrFPData.getFPSHIPPER_CODE().getValue());
		}
		if (ocrFPData.getFPCONSIGNEE() != null) {
			this.setConsignee(ocrFPData.getFPCONSIGNEE().getValue());
		}
		if (ocrFPData.getFPINCOTERMS() != null) {
			this.setTradeTerms(ocrFPData.getFPINCOTERMS().getValue());
		}
		if (ocrFPData.getFPTOTAL_QTY() != null) {
			this.setQty(ExtractNumUtil.extractBigDecimal(ocrFPData.getFPTOTAL_QTY().getValue()));
		}
		if (ocrFPData.getFPTOTAL_QTYUNIT() != null) {
			this.setUnit(ocrFPData.getFPTOTAL_QTYUNIT().getValue());
		}
		if (ocrFPData.getFPTOTAL_AMOUNT() != null) {
			this.setTotalAmount(ExtractNumUtil.extractBigDecimal(ocrFPData.getFPTOTAL_AMOUNT().getValue()));
		}
		if (ocrFPData.getFPTOTAL_CURR() != null) {
			this.setCurr(ocrFPData.getFPTOTAL_CURR().getValue());
		}
		if (ocrFPData.getFPHEAD_COO() != null) {
			this.setOriginCountry(ocrFPData.getFPHEAD_COO().getValue());
		}
		if (ocrFPData.getFPHEAD_PO() != null) {
			this.setOrderNo(ocrFPData.getFPHEAD_PO().getValue());
		}
		if (ocrFPData.getFPTOTAL_PALLET() != null) {
			this.setPalletNum(ExtractNumUtil.extractInteger(ocrFPData.getFPTOTAL_PALLET().getValue()));
		}
		if (ocrFPData.getFPBULK_CTNS() != null) {
			this.setBulkCtns(ExtractNumUtil.extractInteger(ocrFPData.getFPBULK_CTNS().getValue()));
		}
		if (ocrFPData.getFPTOTAL_CTNS() != null) {
			this.setTotalCtns(ExtractNumUtil.extractInteger(ocrFPData.getFPTOTAL_CTNS().getValue()));
		}
		if (ocrFPData.getFPTOTAL_PKG() != null) {
			this.setPackNum(ExtractNumUtil.extractInteger(ocrFPData.getFPTOTAL_PKG().getValue()));
		}
		if (ocrFPData.getFPTOTAL_NW() != null) {
			this.setNetWt(ExtractNumUtil.extractBigDecimal(ocrFPData.getFPTOTAL_NW().getValue()));
		}
		if (ocrFPData.getFPTOTAL_NWUnit() != null) {
			this.setNetWtUnit(ocrFPData.getFPTOTAL_NWUnit().getValue());
		}
		if (ocrFPData.getFPTOTAL_GW() != null) {
			this.setGrossWt(ExtractNumUtil.extractBigDecimal(ocrFPData.getFPTOTAL_GW().getValue()));
		}
		if (ocrFPData.getFPTOTAL_GWUnit() != null) {
			this.setGrossWtUnit(ocrFPData.getFPTOTAL_GWUnit().getValue());
		}
		if (ocrFPData.getFPTOTAL_MEAS() != null) {
			this.setVolume(ExtractNumUtil.extractBigDecimal(ocrFPData.getFPTOTAL_MEAS().getValue()));
		}
		if (ocrFPData.getFPCommodity_Delivery_no() != null) {
			this.setDeliveryNoteNo(ocrFPData.getFPCommodity_Delivery_no().getValue());
		}

	}

	public void assembly(OcrXDData ocrXDData) {
		if (ocrXDData.getXDINVOICE_NO() != null) {
			this.setInvoiceNo(ocrXDData.getXDINVOICE_NO().getValue());
		}
		this.setInvoiceDate(null);
		if (ocrXDData.getXDPACKING_NO() != null) {
			this.setPackingNo(ocrXDData.getXDPACKING_NO().getValue());
		}
		if (ocrXDData.getXDREFERENCE_NO() != null) {
			this.setReferenceNo(ocrXDData.getXDREFERENCE_NO().getValue());
		}
		if (ocrXDData.getXDSHIPPER() != null) {
			this.setShipper(ocrXDData.getXDSHIPPER().getValue());
		}
		if (ocrXDData.getXDSHIPPER_CODE() != null) {
			this.setShipperCode(ocrXDData.getXDSHIPPER_CODE().getValue());
		}
		if (ocrXDData.getXDCONSIGNEE() != null) {
			this.setConsignee(ocrXDData.getXDCONSIGNEE().getValue());
		}
		if (ocrXDData.getXDINCOTERMS() != null) {
			this.setTradeTerms(ocrXDData.getXDINCOTERMS().getValue());
		}
		if (ocrXDData.getXDTOTAL_QTY() != null) {
			this.setQty(ExtractNumUtil.extractBigDecimal(ocrXDData.getXDTOTAL_QTY().getValue()));
		}
		if (ocrXDData.getXDTOTAL_QTYUNIT() != null) {
			this.setUnit(ocrXDData.getXDTOTAL_QTYUNIT().getValue());
		}
		if (ocrXDData.getXDTOTAL_AMOUNT() != null) {
			this.setTotalAmount(ExtractNumUtil.extractBigDecimal(ocrXDData.getXDTOTAL_AMOUNT().getValue()));
		}
		if (ocrXDData.getXDTOTAL_CURR() != null) {
			this.setCurr(ocrXDData.getXDTOTAL_CURR().getValue());
		}
		if (ocrXDData.getXDHEAD_COO() != null) {
			this.setOriginCountry(ocrXDData.getXDHEAD_COO().getValue());
		}
		if (ocrXDData.getXDHEAD_PO() != null) {
			this.setOrderNo(ocrXDData.getXDHEAD_PO().getValue());
		}
		if (ocrXDData.getXDTOTAL_PALLET() != null) {
			this.setPalletNum(ExtractNumUtil.extractInteger(ocrXDData.getXDTOTAL_PALLET().getValue()));
		}
		if (ocrXDData.getXDBULK_CTNS() != null) {
			this.setBulkCtns(ExtractNumUtil.extractInteger(ocrXDData.getXDBULK_CTNS().getValue()));
		}
		if (ocrXDData.getXDTOTAL_CTNS() != null) {
			this.setTotalCtns(ExtractNumUtil.extractInteger(ocrXDData.getXDTOTAL_CTNS().getValue()));
		}
		if (ocrXDData.getXDTOTAL_PKG() != null) {
			this.setPackNum(ExtractNumUtil.extractInteger(ocrXDData.getXDTOTAL_PKG().getValue()));
		}
		if (ocrXDData.getXDTOTAL_NW() != null) {
			this.setNetWt(ExtractNumUtil.extractBigDecimal(ocrXDData.getXDTOTAL_NW().getValue()));
		}
		if (ocrXDData.getXDTOTAL_NWUnit() != null) {
			this.setNetWtUnit(ocrXDData.getXDTOTAL_NWUnit().getValue());
		}
		if (ocrXDData.getXDTOTAL_GW() != null) {
			this.setGrossWt(ExtractNumUtil.extractBigDecimal(ocrXDData.getXDTOTAL_GW().getValue()));
		}
		if (ocrXDData.getXDTOTAL_GWUnit() != null) {
			this.setGrossWtUnit(ocrXDData.getXDTOTAL_GWUnit().getValue());
		}
		if (ocrXDData.getXDTOTAL_MEAS() != null) {
			this.setVolume(ExtractNumUtil.extractBigDecimal(ocrXDData.getXDTOTAL_MEAS().getValue()));
		}

	}

	public void assembly(OcrTDData ocrTDData) {
		if (ocrTDData.getTDINVOICE_NO() != null) {
			this.setInvoiceNo(ocrTDData.getTDINVOICE_NO().getValue());
		}
		this.setInvoiceDate(null);
		if (ocrTDData.getTDPACKING_NO() != null) {
			this.setPackingNo(ocrTDData.getTDPACKING_NO().getValue());
		}
		if (ocrTDData.getTDREFERENCE_NO() != null) {
			this.setReferenceNo(ocrTDData.getTDREFERENCE_NO().getValue());
		}
		if (ocrTDData.getTDSHIPPER() != null) {
			this.setShipper(ocrTDData.getTDSHIPPER().getValue());
		}
		if (ocrTDData.getTDSHIPPER_CODE() != null) {
			this.setShipperCode(ocrTDData.getTDSHIPPER_CODE().getValue());
		}
		if (ocrTDData.getTDCONSIGNEE() != null) {
			this.setConsignee(ocrTDData.getTDCONSIGNEE().getValue());
		}
		if (ocrTDData.getTDINCOTERMS() != null) {
			this.setTradeTerms(ocrTDData.getTDINCOTERMS().getValue());
		}
		if (ocrTDData.getTDTOTAL_QTY() != null) {
			this.setQty(ExtractNumUtil.extractBigDecimal(ocrTDData.getTDTOTAL_QTY().getValue()));
		}
		if (ocrTDData.getTDTOTAL_QTYUNIT() != null) {
			this.setUnit(ocrTDData.getTDTOTAL_QTYUNIT().getValue());
		}
		if (ocrTDData.getTDTOTAL_AMOUNT() != null) {
			this.setTotalAmount(ExtractNumUtil.extractBigDecimal(ocrTDData.getTDTOTAL_AMOUNT().getValue()));
		}
		if (ocrTDData.getTDTOTAL_CURR() != null) {
			this.setCurr(ocrTDData.getTDTOTAL_CURR().getValue());
		}
		if (ocrTDData.getTDHEAD_COO() != null) {
			this.setOriginCountry(ocrTDData.getTDHEAD_COO().getValue());
		}
		if (ocrTDData.getTDHEAD_PO() != null) {
			this.setOrderNo(ocrTDData.getTDHEAD_PO().getValue());
		}
		if (ocrTDData.getTDTOTAL_PALLET() != null) {
			this.setPalletNum(ExtractNumUtil.extractInteger(ocrTDData.getTDTOTAL_PALLET().getValue()));
		}
		if (ocrTDData.getTDBULK_CTNS() != null) {
			this.setBulkCtns(ExtractNumUtil.extractInteger(ocrTDData.getTDBULK_CTNS().getValue()));
		}
		if (ocrTDData.getTDTOTAL_CTNS() != null) {
			this.setTotalCtns(ExtractNumUtil.extractInteger(ocrTDData.getTDTOTAL_CTNS().getValue()));
		}
		if (ocrTDData.getTDTOTAL_PKG() != null) {
			this.setPackNum(ExtractNumUtil.extractInteger(ocrTDData.getTDTOTAL_PKG().getValue()));
		}
		if (ocrTDData.getTDTOTAL_NW() != null) {
			this.setNetWt(ExtractNumUtil.extractBigDecimal(ocrTDData.getTDTOTAL_NW().getValue()));
		}
		if (ocrTDData.getTDTOTAL_NWUnit() != null) {
			this.setNetWtUnit(ocrTDData.getTDTOTAL_NWUnit().getValue());
		}
		if (ocrTDData.getTDTOTAL_GW() != null) {
			this.setGrossWt(ExtractNumUtil.extractBigDecimal(ocrTDData.getTDTOTAL_GW().getValue()));
		}
		if (ocrTDData.getTDTOTAL_GWUnit() != null) {
			this.setGrossWtUnit(ocrTDData.getTDTOTAL_GWUnit().getValue());
		}
		if (ocrTDData.getTDTOTAL_MEAS() != null) {
			this.setVolume(ExtractNumUtil.extractBigDecimal(ocrTDData.getTDTOTAL_MEAS().getValue()));
		}

		if (ocrTDData.getTDMAWB() != null) {
			this.setMawb(ocrTDData.getTDMAWB().getValue());
		}
		if (ocrTDData.getTDHAWB() != null) {
			this.setHawb(ocrTDData.getTDHAWB().getValue());
		}
		if (ocrTDData.getTDFORWARDER() != null) {
			this.setForwarder(ocrTDData.getTDFORWARDER().getValue());
		}
		if (ocrTDData.getTDDEPPORT() != null) {
			this.setDepport(ocrTDData.getTDDEPPORT().getValue());
		}
		if (ocrTDData.getTDDESPORT() != null) {
			this.setDesport(ocrTDData.getTDDESPORT().getValue());
		}
		if (ocrTDData.getTDBILL_DATE() != null) {
			this.setBillDate(ocrTDData.getTDBILL_DATE().getValue());
		}
		if (ocrTDData.getTDFLT_NO() != null) {
			this.setFltNo(ocrTDData.getTDFLT_NO().getValue());
		}
		if (ocrTDData.getTDFLT_DATE() != null) {
			this.setFltDate(ocrTDData.getTDFLT_DATE().getValue());
		}
		if (ocrTDData.getTDFLT_NO_1ST() != null) {
			this.setFltNo1st(ocrTDData.getTDFLT_NO_1ST().getValue());
		}
		if (ocrTDData.getTDFLT_DATE_1ST() != null) {
			this.setFltDate1st(ocrTDData.getTDFLT_DATE_1ST().getValue());
		}
		if (ocrTDData.getTDFREIGHT() != null) {
			this.setFreight(ExtractNumUtil.extractBigDecimal(ocrTDData.getTDFREIGHT().getValue()));
		}
		if (ocrTDData.getTDTOTAL_CHWT() != null) {
			this.setTotalChwt(ExtractNumUtil.extractBigDecimal(ocrTDData.getTDTOTAL_CHWT().getValue()));
		}


	}
}
