package com.dcjet.cs.ocrInvoice.mapper;

import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIHeadDto;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIHeadParam;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIHeadQueryParam;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwOcrInvoiceIHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwOcrInvoiceIHeadDto toDto(GwOcrInvoiceIHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrInvoiceIHead toPo(GwOcrInvoiceIHeadParam param);

    /**
     * model -> param
     * @param model
     * @return
     */
    GwOcrInvoiceIHeadParam poToEntity(GwOcrInvoiceIHead model);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrInvoiceIHead toPo(GwOcrInvoiceIHeadQueryParam param);
    /**
     * 数据库原始数据更新
     * @param gwOcrInvoiceIHeadParam
     * @param gwOcrInvoiceIHead
     */
    void updatePo(GwOcrInvoiceIHeadParam gwOcrInvoiceIHeadParam, @MappingTarget GwOcrInvoiceIHead gwOcrInvoiceIHead);
    default void patchPo(GwOcrInvoiceIHeadParam gwOcrInvoiceIHeadParam, GwOcrInvoiceIHead gwOcrInvoiceIHead) {
        // TODO 自行实现局部更新
    }
}
