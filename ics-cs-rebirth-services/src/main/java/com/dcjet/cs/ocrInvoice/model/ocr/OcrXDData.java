package com.dcjet.cs.ocrInvoice.model.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("")
public class OcrXDData implements Serializable {

    @ApiModelProperty("发票号码")
    @JsonProperty("XDINVOICE_NO")
    private OcrFieldInfo XDINVOICE_NO;

    @ApiModelProperty("发票日期")
    @JsonProperty("XDINVOICE_DATE")
    private OcrFieldInfo XDINVOICE_DATE;

    @ApiModelProperty("装箱单号码")
    @JsonProperty("XDPACKING_NO")
    private OcrFieldInfo XDPACKING_NO;

    @ApiModelProperty("关联号码")
    @JsonProperty("XDREFERENCE_NO")
    private OcrFieldInfo XDREFERENCE_NO;

    @ApiModelProperty("发货人")
    @JsonProperty("XDSHIPPER")
    private OcrFieldInfo XDSHIPPER;

    @ApiModelProperty("发货人代码")
    @JsonProperty("XDSHIPPER_CODE")
    private OcrFieldInfo XDSHIPPER_CODE;

    @ApiModelProperty("收货人")
    @JsonProperty("XDCONSIGNEE")
    private OcrFieldInfo XDCONSIGNEE;

    @ApiModelProperty("成交方式")
    @JsonProperty("XDINCOTERMS")
    private OcrFieldInfo XDINCOTERMS;

    @ApiModelProperty("总数量")
    @JsonProperty("XDTOTAL_QTY")
    private OcrFieldInfo XDTOTAL_QTY;

    @ApiModelProperty("总数量单位")
    @JsonProperty("XDTOTAL_QTYUNIT")
    private OcrFieldInfo XDTOTAL_QTYUNIT;

    @ApiModelProperty("总金额")
    @JsonProperty("XDTOTAL_AMOUNT")
    private OcrFieldInfo XDTOTAL_AMOUNT;

    @ApiModelProperty("币别")
    @JsonProperty("XDTOTAL_CURR")
    private OcrFieldInfo XDTOTAL_CURR;

    @ApiModelProperty("统一原产国")
    @JsonProperty("XDHEAD_COO")
    private OcrFieldInfo XDHEAD_COO;

    @ApiModelProperty("统一订单")
    @JsonProperty("XDHEAD_PO")
    private OcrFieldInfo XDHEAD_PO;

    @ApiModelProperty("托盘数")
    @JsonProperty("XDTOTAL_PALLET")
    private OcrFieldInfo XDTOTAL_PALLET;

    @ApiModelProperty("散箱数")
    @JsonProperty("XDBULK_CTNS")
    private OcrFieldInfo XDBULK_CTNS;

    @ApiModelProperty("总箱数")
    @JsonProperty("XDTOTAL_CTNS")
    private OcrFieldInfo XDTOTAL_CTNS;

    @ApiModelProperty("总件数")
    @JsonProperty("XDTOTAL_PKG")
    private OcrFieldInfo XDTOTAL_PKG;

    @ApiModelProperty("总净重")
    @JsonProperty("XDTOTAL_NW")
    private OcrFieldInfo XDTOTAL_NW;

    @ApiModelProperty("净重单位")
    @JsonProperty("XDTOTAL_NWUnit")
    private OcrFieldInfo XDTOTAL_NWUnit;

    @ApiModelProperty("总毛重")
    @JsonProperty("XDTOTAL_GW")
    private OcrFieldInfo XDTOTAL_GW;

    @ApiModelProperty("毛重单位")
    @JsonProperty("XDTOTAL_GWUnit")
    private OcrFieldInfo XDTOTAL_GWUnit;

    @ApiModelProperty("总体积")
    @JsonProperty("XDTOTAL_MEAS")
    private OcrFieldInfo XDTOTAL_MEAS;

    @JsonProperty("XDCommodity")
    private List<OcrXDListData> XDCommodity;
    
}
