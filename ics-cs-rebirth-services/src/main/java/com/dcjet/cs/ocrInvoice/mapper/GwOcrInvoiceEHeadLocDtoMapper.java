package com.dcjet.cs.ocrInvoice.mapper;

import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEHeadLocDto;
import com.dcjet.cs.dto.ocrInvoice.ocrE.GwOcrInvoiceEHeadLocParam;
import com.dcjet.cs.dto.ocrInvoice.ocrI.GwOcrInvoiceIHeadLocParam;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceEHeadLoc;
import com.dcjet.cs.ocrInvoice.model.GwOcrInvoiceIHeadLoc;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwOcrInvoiceEHeadLocDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwOcrInvoiceEHeadLocDto toDto(GwOcrInvoiceEHeadLoc po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwOcrInvoiceEHeadLoc toPo(GwOcrInvoiceEHeadLocParam param);
    /**
     * 数据库原始数据更新
     * @param gwOcrInvoiceEHeadLocParam
     * @param gwOcrInvoiceEHeadLoc
     */
    void updatePo(GwOcrInvoiceEHeadLocParam gwOcrInvoiceEHeadLocParam, @MappingTarget GwOcrInvoiceEHeadLoc gwOcrInvoiceEHeadLoc);
    default void patchPo(GwOcrInvoiceEHeadLocParam gwOcrInvoiceEHeadLocParam, GwOcrInvoiceEHeadLoc gwOcrInvoiceEHeadLoc) {
        // TODO 自行实现局部更新
    }
}
