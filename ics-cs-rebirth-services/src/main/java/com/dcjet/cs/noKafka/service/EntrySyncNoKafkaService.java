package com.dcjet.cs.noKafka.service;

import com.dcjet.cs.gwstdentry.model.GwstdEntryMessage;
import com.dcjet.cs.gwstdentry.service.GwstdEntryHeadService;
import com.dcjet.cs.imp.model.KafkaEntryStatusMessage;
import com.dcjet.cs.noKafka.model.ConsumeResult;
import com.dcjet.cs.noKafka.model.Message;
import com.dcjet.cs.noKafka.util.HttpUtil;
import com.google.gson.Gson;
import com.xdo.common.json.JsonObjectMapper;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;


import javax.annotation.Resource;

@Service
public class EntrySyncNoKafkaService {

    private static final Logger logger = LoggerFactory.getLogger(EntrySyncNoKafkaService.class);

    @Resource
    private HttpUtil httpUtil;

    @Resource
    private GwstdEntryHeadService gwstdEntryHeadService;

    @Value("${dc.topics.entry:ICS-MCDATA-ENTRY-GW-3106945070}")
    private String topics;

    public void doHandle(String data){
        String token = httpUtil.getToken();

        if (StringUtils.isBlank(token)) {
            logger.error("获取token失败");
            return;
        }

        ConsumeResult result = httpUtil.consume(topics, token);
        if (result == null) {
            return;
        }
        logger.info("消费成功: {}", result.getMessageId());
        if (!httpUtil.commit(result.getMessageId(), token)) {
            return;
        }
        if (result.isFile()) {
            httpUtil.downloadFile(result.getFile(), token);
        } else {
            logger.info(new Gson().toJson(result.getMessage()));
            XxlJobLogger.log(topics + "消息:" + new Gson().toJson(result.getMessage()));

            // 将API返回的结果转换为KafkaEntryStatusMessage对象并处理
            try {
                // 创建一个模拟的ConsumerRecord对象
                Message record = result.getMessage();
                ConsumerRecord<String, String> mockRecord = new ConsumerRecord<>(topics, record.getPartition(), record.getOffset(), record.getKey(), record.getValue());

                // 将API返回的消息转换为KafkaEntryStatusMessage对象
                GwstdEntryMessage message = JsonObjectMapper.getInstance().fromJson(mockRecord.value(), GwstdEntryMessage.class);

                gwstdEntryHeadService.saveToKafka(message,mockRecord);
                logger.info("成功处理报关单数据消息: {}", result.getMessageId());
            } catch (Exception e) {
                logger.error("处理报关单数据消息时发生错误: {}", e.getMessage(), e);
            }
        }
    }
}
