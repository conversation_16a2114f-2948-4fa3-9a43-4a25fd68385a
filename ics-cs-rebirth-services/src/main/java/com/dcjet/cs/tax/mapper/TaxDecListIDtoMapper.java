package com.dcjet.cs.tax.mapper;

import com.dcjet.cs.dto.tax.param.TaxDecListIParam;
import com.dcjet.cs.tax.model.TaxDecListI;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TaxDecListIDtoMapper {
    @Mapping(source = "sid", target = "sid", ignore = true)
    void copyToEntity(TaxDecListIParam param,@MappingTarget TaxDecListI target);
}
