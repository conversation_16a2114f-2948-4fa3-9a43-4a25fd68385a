package com.dcjet.cs.tax.exRate.dao;

import com.dcjet.cs.tax.exRate.model.ExchangeRate;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* ExchangeRate
* <AUTHOR>
* @date: 2019-12-5
*/
public interface ExchangeRateMapper extends Mapper<ExchangeRate> {
    /**
     * 查询获取数据
     * @param exchangeRate
     * @return
     */
    List<ExchangeRate> getList(ExchangeRate exchangeRate);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /***
     * 根据code和currDate删除数据
     * @param code
     * @param currDate
     * @return
     */
    int deleteByCodeAndDate(@Param("code") String code, @Param("currDate") String currDate);
    /**
     * 获取临时表中正式表不存在的汇率数据
     */
    List<ExchangeRate> getOtherList();

    /**
     * 获取汇率最大日期
     * @return
     */
    ExchangeRate selectMax();
}
