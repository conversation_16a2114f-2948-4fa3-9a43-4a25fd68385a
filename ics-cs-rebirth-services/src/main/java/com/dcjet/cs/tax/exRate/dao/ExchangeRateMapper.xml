<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.tax.exRate.dao.ExchangeRateMapper">
    <resultMap id="exchangeRateResultMap" type="com.dcjet.cs.tax.exRate.model.ExchangeRate">
		<result column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="CODE" property="code" jdbcType="VARCHAR" />
		<result column="CURR_CODE_NUM" property="currCodeNum" jdbcType="VARCHAR" />
		<result column="NAME" property="name" jdbcType="VARCHAR" />
		<result column="CURR_PRICE_RMB" property="currPriceRmb" jdbcType="NUMERIC" />
		<result column="CURR_PRICE_USD" property="currPriceUsd" jdbcType="NUMERIC" />
		<result column="CURR_DATE" property="currDate" jdbcType="VARCHAR" />
		<result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR" />
		<result column="INSERT_TIME" property="insertTime" jdbcType="DATE" />
		<result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="DATE" />
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,CODE
     ,CURR_CODE_NUM
     ,NAME
     ,CURR_PRICE_RMB
     ,CURR_PRICE_USD
     ,CURR_DATE
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
    </sql>
    <sql id="condition">
    <if test="code != null and code != ''">
		and CODE = #{code}
	</if>
    <if test="currCodeNum != null and currCodeNum != ''">
		and CURR_CODE_NUM = #{currCodeNum}
	</if>
    <if test="name != null and name != ''">
		and NAME = #{name}
	</if>
    <if test="currPriceRmb != null and currPriceRmb != ''">
		and CURR_PRICE_RMB = #{currPriceRmb}
	</if>
    <if test="currPriceUsd != null and currPriceUsd != ''">
		and CURR_PRICE_USD = #{currPriceUsd}
	</if>
    <if test="currDate != null and currDate != ''">
       and CURR_DATE = #{currDate}
    </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="exchangeRateResultMap" parameterType="com.dcjet.cs.tax.exRate.model.ExchangeRate">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_EXCHANGE_RATE t
        <where>
            <include refid="condition"></include>
        </where>
        ORDER BY CURR_DATE DESC,sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_EXCHANGE_RATE t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByCodeAndDate">
        DELETE FROM T_EXCHANGE_RATE t WHERE t.CURR_CODE_NUM = #{code} AND t.CURR_DATE = #{currDate}
    </delete>
    <select id="getOtherList" resultMap="exchangeRateResultMap">
        select code,CURR_CODE_NUM,name,CURR_PRICE_RMB,CURR_PRICE_USD,curr_date
        from T_EXCHANGE_RATE_TEMP
            where sid not in(
                SELECT t.SID  FROM
                    T_EXCHANGE_RATE_TEMP t
                join T_EXCHANGE_RATE r
                    on t.CODE=r.CODE and t.CURR_DATE=r.CURR_DATE)
    </select>

    <select id="selectMax" resultMap="exchangeRateResultMap">
	    SELECT MAX(CURR_DATE) AS CURR_DATE FROM T_EXCHANGE_RATE
    </select>

</mapper>
