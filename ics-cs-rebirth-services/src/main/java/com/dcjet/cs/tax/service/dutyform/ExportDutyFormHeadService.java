package com.dcjet.cs.tax.service.dutyform;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.tax.dutyform.DutyFormDto;
import com.dcjet.cs.dto.tax.dutyform.DutyFormQueryParam;
import com.dcjet.cs.tax.dao.dutyform.DutyFormHeadMapper;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

@Component
public class ExportDutyFormHeadService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    private final String taskName = "实际税金异步导出(TAX_ACTUAL)";
    @Resource
    private CommonService commonService;

    @Resource
    DutyFormHeadMapper mapper;

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("TAX_ACTUAL");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        DutyFormQueryParam dutyFormQueryParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        dutyFormQueryParam.setTradeCode(userInfo.getCompany());
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(dutyFormQueryParam));
        Integer count = mapper.selectDataCount(dutyFormQueryParam);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        DutyFormQueryParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(exportParam));
        Page<DutyFormDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(exportParam));

        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrintHead(page.getResult()));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<DutyFormDto> convertForPrintHead(List<DutyFormDto> list) {
        for (DutyFormDto item : list) {
            item.setTradeMode(commonService.convertPCode(item.getTradeMode(), PCodeType.TRADE));
            item.setMasterCustoms(commonService.convertPCode(item.getMasterCustoms(), PCodeType.CUSTOMS_REL));
            if (StringUtils.isNotBlank(item.getDownloadStatus())) {
                item.setDownloadStatus(CommonEnum.DutyFormStatusEnum.getDesc(item.getDownloadStatus()));
            }
            if (StringUtils.isNotBlank(item.getCheckFinalPath())) {
                item.setCheckFinalPath(CommonVariable.YES_EN);
            }
            if (StringUtils.isNotBlank(item.getPayFinalPath())) {
                item.setPayFinalPath(CommonVariable.YES_EN);
            }
            if (StringUtils.isNotBlank(item.getTransStatus())) {
                item.setTransStatus(CommonEnum.TRANS_STATUS.getValue(item.getTransStatus()));
            }
            if (StringUtils.isNotBlank(item.getSubjectCode())) {
                item.setSubjectName(item.getSubjectName());
            }
            if (StringUtils.isNotBlank(item.getPayType())) {
                item.setPayType(item.getPayType() + "" + "普通支付");
            }
            if (StringUtils.isNotBlank(item.getTaxType())) {
                item.setTaxType(item.getTaxType() + "" + CommonEnum.TAX_TYPE.getValue(item.getTaxType()));
            }
            if (StringUtils.isNotBlank(item.getSumTaxMark())) {
                if (StringUtils.equals(item.getSumTaxMark(), "COLLPAY")) {
                    item.setSumTaxMark("是");
                }
            }
        }
        return list;
    }

    private DutyFormQueryParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        DutyFormQueryParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, DutyFormQueryParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
