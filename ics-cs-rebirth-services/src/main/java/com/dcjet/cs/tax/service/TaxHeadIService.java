package com.dcjet.cs.tax.service;

import com.dcjet.cs.dto.tax.TaxHeadIDto;
import com.dcjet.cs.dto.tax.param.*;
import com.dcjet.cs.tax.dao.TaxHeadIMapper;
import com.dcjet.cs.tax.mapper.TaxHeadIDtoMapper;
import com.dcjet.cs.tax.model.TaxHeadI;
import com.dcjet.cs.tax.model.TaxListI;
import com.dcjet.cs.util.ConstantsStatus;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TaxHeadIService extends BaseService<TaxHeadI> {
    public static final String ATTACHED_BIZ_TYPE = "TAX_I_ATTACHED";
    @Resource
    TaxHeadIMapper mapper;
    @Resource
    TaxHeadIDtoMapper dtoMapper;
    @Resource
    TaxListIService listService;

    @Override
    public Mapper<TaxHeadI> getMapper() {
        return mapper;
    }

    public ResultObject<List<TaxHeadIDto>> getListPaged(TaxHeadIQueryParam queryParam, PageParam pageParam, UserInfoToken userInfo) {
        queryParam.setTradeCode(userInfo.getCompany());
        Page<TaxHeadI> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(queryParam));
        List<TaxHeadIDto> dtoList = page.getResult().stream().map(head -> {
            TaxHeadIDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<TaxHeadIDto>> paged = ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public List<TaxHeadIDto> listAll(TaxHeadIQueryParam queryParam, UserInfoToken userInfo) {
        queryParam.setTradeCode(userInfo.getCompany());
        List<TaxHeadI> result = mapper.getList(queryParam);
        List<TaxHeadIDto> dtoList = result.stream().map(head -> {
            TaxHeadIDto dto = dtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        return dtoList;
    }

    public List<TaxHeadI> listAllHead(String tradeCode) {
        TaxHeadIQueryParam queryParam = new TaxHeadIQueryParam();
        queryParam.setTradeCode(tradeCode);
        List<TaxHeadI> headList = mapper.getList(queryParam);
        return headList == null ? Collections.EMPTY_LIST : headList;
    }

    @Transactional(rollbackFor = Exception.class)
    public TaxHeadIDto update(TaxHeadIParam taxHeadIParam, UserInfoToken userInfo) {
        TaxHeadI taxHeadI = mapper.getById(taxHeadIParam.getSid());
        if (taxHeadI != null) {
            BigDecimal taxTotal = BigDecimal.ZERO;
            BigDecimal dutyValue = taxHeadIParam.getDutyValue();
            BigDecimal exciseTax = taxHeadIParam.getExciseTax();
            BigDecimal taxValue = taxHeadIParam.getTaxValue();
            if (dutyValue != null) {
                taxTotal = taxTotal.add(dutyValue);
            }
            if (exciseTax != null) {
                taxTotal = taxTotal.add(exciseTax);
            }
            if (taxValue != null) {
                taxTotal = taxTotal.add(taxValue);
            }
            taxHeadI.setTaxTotal(taxTotal);
            dtoMapper.updatePo(taxHeadIParam, taxHeadI);
            taxHeadI.setUpdateUser(userInfo.getUserNo());
            taxHeadI.setUpdateUserName(userInfo.getUserName());
            taxHeadI.setUpdateTime(new Date());
            taxHeadI.setStatus(ConstantsStatus.STATUS_1);
            // 更新数据
            int update = mapper.updateByPrimaryKey(taxHeadI);
            return update > 0 ? dtoMapper.toDto(taxHeadI) : null;
        } else {
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSetDutyType(TaxHeadBatchSetDutyTypeParam param, UserInfoToken userInfo) {
        if (CollectionUtils.isEmpty(param.getSids())) {
            return;
        }
        TaxHeadBatchSetDutyTypeDto dto = new TaxHeadBatchSetDutyTypeDto();
        BeanUtils.copyProperties(param, dto);
        dto.setTradeCode(userInfo.getCompany());
        dto.setUpdateUser(userInfo.getUserNo());
        dto.setUpdateUserName(userInfo.getUserName());
        dto.setUpdateTime(new Date());
        dto.setDutyType(param.getDutyType());
        dto.setPayDate(param.getPayDate());
        mapper.batchSetDutyType(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void clearTax(TaxHeadBatchOperateParam param, UserInfoToken userInfo) {
        if (CollectionUtils.isEmpty(param.getSids())) {
            return;
        }
        TaxHeadClearTaxDto dto = new TaxHeadClearTaxDto();
        BeanUtils.copyProperties(param, dto);
        dto.setTradeCode(userInfo.getCompany());
        dto.setUpdateUser(userInfo.getUserNo());
        dto.setUpdateUserName(userInfo.getUserName());
        dto.setUpdateTime(new Date());
        mapper.clearTax(dto);
        listService.clearTax(param.getSids(), userInfo);
    }

    public void calTrulyTax(TaxHeadBatchOperateParam param, UserInfoToken userInfo) {
        if (CollectionUtils.isEmpty(param.getSids())) {
            return;
        }
        String tradeCode = userInfo.getCompany();
        String updateUser = userInfo.getUserNo();
        String updateUserName = userInfo.getUserName();
        Date updateTime = new Date();
        param.getSids().stream().forEach(headId -> {
            calHeadTax(headId, tradeCode, updateUser, updateUserName, updateTime);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public String estimateTax(TaxHeadBatchOperateParam param, UserInfoToken userInfo) {
        if (CollectionUtils.isEmpty(param.getSids())) {
            return xdoi18n.XdoI18nUtil.t("传入SID不存在");
        }
        final String[] errMessage = {""};
        param.getSids().stream().forEach(headId -> {
            errMessage[0] = errMessage[0] + listService.estimateTax(headId, userInfo);
        });
        return errMessage[0];
    }

    public void calHeadTax(String headId, String tradeCode, String updateUser, String updateUserName, Date updateTime) {
        TaxHeadI taxHead = mapper.selectByPrimaryKey(headId);
        List<TaxListI> taxLists = listService.getListByHeadId(headId, tradeCode);
        //同时计算表头税金
        BigDecimal headTaxTotal = BigDecimal.ZERO;
        BigDecimal headDutyValue = BigDecimal.ZERO;
        BigDecimal headExciseTax = BigDecimal.ZERO;
        BigDecimal headTaxValue = BigDecimal.ZERO;
        for (TaxListI taxList : taxLists) {
            if (taxList.getDutyValue() != null) {
                headDutyValue = headDutyValue.add(taxList.getDutyValue());
            }
            if (taxList.getExciseTax() != null) {
                headExciseTax = headExciseTax.add(taxList.getExciseTax());
            }
            if (taxList.getTaxValue() != null) {
                headTaxValue = headTaxValue.add(taxList.getTaxValue());
            }
            if (taxList.getTaxTotal() != null) {
                headTaxTotal = headTaxTotal.add(taxList.getTaxTotal());
            }
        }
        taxHead.setTaxTotal(headTaxTotal);
        taxHead.setDutyValue(headDutyValue);
        taxHead.setExciseTax(headExciseTax);
        taxHead.setTaxValue(headTaxValue);
        taxHead.setUpdateUser(updateUser);
        taxHead.setUpdateUserName(updateUserName);
        taxHead.setUpdateTime(updateTime);
        mapper.updateByPrimaryKey(taxHead);
    }

    @Transactional
    public void importFromTmp(String tempOwner) {
        mapper.importFromTmp(tempOwner);
        mapper.clearTmp(tempOwner);
    }
}
