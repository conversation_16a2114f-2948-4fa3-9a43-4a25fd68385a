package com.dcjet.cs.tax.model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-7-23
 */
@Setter
@Getter
@Table(name = "T_TAX_RATE_EXCLUSION_LIST")
public class TaxRateExclusionList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 唯一键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 商品编码
     */
	@Column(name = "CODE_T_S")
	private  String codeTS;
	/**
     * 状态(1 有效；0无效)
     */
	@Column(name = "STATUS")
	private  String status;
	/**
	 * 企业编码
	 */
	@Column(name = "TRADE_CODE")
	private String tradeCode;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 创建人
     */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 制单人姓名
     */
	@Column(name = "INSERT_USER_NAME")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
}
