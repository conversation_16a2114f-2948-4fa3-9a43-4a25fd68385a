package com.dcjet.cs.tax.dao.dutyform;

import com.dcjet.cs.dto.tax.dutyform.DutyFormListDto;
import com.dcjet.cs.dto.tax.dutyform.DutyFormQueryParam;
import com.dcjet.cs.dto.tax.dutyform.DutyListDto;
import com.dcjet.cs.tax.model.dutyform.DutyFormList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface DutyFormListMapper extends Mapper<DutyFormList> {
    List<DutyListDto> list(@Param("taxVouNo") String taxVouNo);
    void clear(@Param("taxVouNo") String taxVouNo);

    List<DutyFormListDto> getList(DutyFormQueryParam queryParam);

    Integer selectDataCount(DutyFormQueryParam exportParam);
}
