<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.tax.dao.paratariff.ChinaTariffConvertMapper">
<!--    <update id="convertCountryName">
        select F_CONVERT_COUNTRY_NAME() from dual
    </update>-->

    <delete id="deleteErvail" >
        delete from  T_GWSTD_TARIFF_ERVAIL
    </delete>

    <delete id="deleteAntiDump" >
        delete from  T_GWSTD_TARIFF_ANTIDUMP
    </delete>

    <delete id="deleteAccord" >
        delete from  T_GWSTD_TARIFF_ACCORD
    </delete>

    <delete id="deleteGeneral" >
        delete from  T_GWSTD_TARIFF_GENERAL
    </delete>
    <insert id="insertErvail" databaseId="postgresql">
insert into T_GWSTD_TARIFF_ERVAIL(CODE_T_S,COUNTRY_NAME,
	IMP_ERVAIL_RATE,COMPANY_NAME_CN,COMPANY_NAME_EN)
select c.TS_CODE as CODE_T_S,
	e.COUNTRYNAME as COUNTRY_NAME,
	case when e.IMPCOUNTERVAILINGRATE='0'
		then 0 else
			to_number(substr(e.IMPCOUNTERVAILINGRATE,0,instr(e.IMPCOUNTERVAILINGRATE,'%')),'999.99')
	end as IMP_ERVAIL_RATE,
	e.COMPANYNAMECN as COMPANY_NAME_CN,
	e.COMPANYNAMEEN as COMPANY_NAME_EN
from T_PARA_TARIFF_CHINA_ERVAIL e
	join T_PARA_TARIFF_CHINA c on e.CHINAOID=c.OID
    </insert>

    <insert id="insertErvail" >
insert into T_GWSTD_TARIFF_ERVAIL(CODE_T_S,COUNTRY_NAME,
	IMP_ERVAIL_RATE,COMPANY_NAME_CN,COMPANY_NAME_EN)
select c.TS_CODE as CODE_T_S,
	e.COUNTRYNAME as COUNTRY_NAME,
	case when e.IMPCOUNTERVAILINGRATE='0'
		then 0 else
			to_number(substr(e.IMPCOUNTERVAILINGRATE,0,instr(e.IMPCOUNTERVAILINGRATE,'%')-1))
	end as IMP_ERVAIL_RATE,
	e.COMPANYNAMECN as COMPANY_NAME_CN,
	e.COMPANYNAMEEN as COMPANY_NAME_EN
from T_PARA_TARIFF_CHINA_ERVAIL e
	join T_PARA_TARIFF_CHINA c on e.CHINAOID=c.OID
    </insert>

    <insert id="insertAntiDump" databaseId="postgresql">
insert into T_GWSTD_TARIFF_ANTIDUMP(CODE_T_S,IMP_ANTIDUMP_RATE,
	COUNTRY_NAME,COUNTRY_CODE,COMPANY_NAME_CN,COMPANY_NAME_EN)
select c.TS_CODE as CODE_T_S,
	case when a.IMPANTIDDUTIESRATE='0'
		then 0 else
			to_number(substr(a.IMPANTIDDUTIESRATE,0,instr(a.IMPANTIDDUTIESRATE,'%')),'999.99')
	end as IMP_ANTIDUMP_RATE,
	a.COUNTRYNAME as COUNTRY_NAME,
	a.COUNTRYCODE as COUNTRY_CODE,
	a.COMPANYNAMECN as COMPANY_NAME_CN,
	a.COMPANYNAMEEN as COMPANY_NAME_EN
from T_PARA_TARIFF_CHINA_ANTIDUMP a
	join T_PARA_TARIFF_CHINA c on a.CHINAOID=c.OID
    </insert>

    <insert id="insertAntiDump"  >
insert into T_GWSTD_TARIFF_ANTIDUMP(CODE_T_S,IMP_ANTIDUMP_RATE,
	COUNTRY_NAME,COUNTRY_CODE,COMPANY_NAME_CN,COMPANY_NAME_EN)
select c.TS_CODE as CODE_T_S,
	case when a.IMPANTIDDUTIESRATE='0'
		then 0 else
			to_number(substr(a.IMPANTIDDUTIESRATE,0,instr(a.IMPANTIDDUTIESRATE,'%')-1))
	end as IMP_ANTIDUMP_RATE,
	a.COUNTRYNAME as COUNTRY_NAME,
	a.COUNTRYCODE as COUNTRY_CODE,
	a.COMPANYNAMECN as COMPANY_NAME_CN,
	a.COMPANYNAMEEN as COMPANY_NAME_EN
from T_PARA_TARIFF_CHINA_ANTIDUMP a
	join T_PARA_TARIFF_CHINA c on a.CHINAOID=c.OID
    </insert>

    <insert id="insertAccord" databaseId="postgresql">
        insert into T_GWSTD_TARIFF_ACCORD(code_t_s,
	agreement_name,agreement_code,country_name,country_code,imp_agreement_rate)
select c.TS_CODE as code_t_s,
	i.AGREEMENTNAME as agreement_name,
	i.AGREEMENTCODE as agreement_code,
	it.COUNTRYNAME as country_name,
	it.COUNTRYCODE as country_code,
	case when it.IMPAGREEMENTRATE='0'
		then 0 else
			to_number(substr(it.IMPAGREEMENTRATE,0,instr(it.IMPAGREEMENTRATE,'%')),'999.99')
	end as imp_agreement_rate
from T_PARA_TARIFF_CHINA_IMPORT i
	join T_PARA_TARIFF_CHINA c on i.CHINAOID=c.OID
	join T_PARA_TARIFF_CHINA_IMPORTITEM it on i.OID=it.IMPORTOID
    </insert>

    <insert id="insertAccord" >
        insert into T_GWSTD_TARIFF_ACCORD(code_t_s,
	agreement_name,agreement_code,country_name,COUNTRY_CODE,imp_agreement_rate)
select c.TS_CODE as code_t_s,
	i.AGREEMENTNAME as agreement_name,
	i.AGREEMENTCODE as agreement_code,
	it.COUNTRYNAME as country_name,
	it.COUNTRYCODE as country_code,
	case when it.IMPAGREEMENTRATE='0'
		then 0 else
			to_number(substr(it.IMPAGREEMENTRATE,0,instr(it.IMPAGREEMENTRATE,'%')-1))
	end as imp_agreement_rate
from T_PARA_TARIFF_CHINA_IMPORT i
	join T_PARA_TARIFF_CHINA c on i.CHINAOID=c.OID
	join T_PARA_TARIFF_CHINA_IMPORTITEM it on i.OID=it.IMPORTOID
    </insert>

    <insert id="insertGeneral" databaseId="postgresql" >
insert into T_GWSTD_TARIFF_GENERAL(sid,code_t_s,g_name,add_tax_rate,antidump_type,ervail_type,
        accord_type,imp_consume_rate,imp_discount_rate,imp_ordinary_rate,imp_temp_rate,
        exp_tax_rate,exp_tax_rate_PERC,exp_tax_rate_PRICE,exp_tax_rate_UNIT,exp_temp_rate,
        exp_temp_rate_PERC,exp_temp_rate_PRICE,exp_temp_rate_UNIT,imp_fund_rate,imp_fund_rate_PERC,
        imp_fund_rate_PRICE,imp_fund_rate_UNIT,imp_safeguard_rate,imp_safeguard_rate_PERC,
        imp_safeguard_rate_PRICE,imp_safeguard_rate_UNIT)
select
    uuid_generate_v1(),
    c.TS_CODE as code_t_s,
    c.G_NAME as g_name,
    c.IMPVATRATE as add_tax_rate,
    case when (select count(*) from T_PARA_TARIFF_CHINA_ANTIDUMP where CHINAOID = c.oid)>0
         then '1' else '0'
    end antidump_type,
    case when (select count(*) from T_PARA_TARIFF_CHINA_ERVAIL where CHINAOID = c.oid)>0
        then '1' else '0'
    end ervail_type,
    case when (select count(*) from T_PARA_TARIFF_CHINA_IMPORT where CHINAOID = c.oid)>0
        then '1' else '0'
    end accord_type,
    c.IMPCONSUMERATE as imp_consume_rate,
    c.IMPDISCOUNTRATE as imp_discount_rate,
    c.IMPORDINARYRATE as imp_ordinary_rate,
    c.IMPTEMPRATE as imp_temp_rate,
    c.EXPTARIFFRATE as exp_tax_rate,
    case when length(c.EXPTARIFFRATE)>15 or c.exptariffrate = '0' or c.EXPTARIFFRATE is null or c.EXPTARIFFRATE=''
        then 0 else
            to_number(substr(c.EXPTARIFFRATE,'0',instr(c.EXPTARIFFRATE,'%')),'999.99')
    end as exp_tax_rate_PERC,
    case when length(c.EXPTARIFFRATE)>15 or c.EXPTARIFFRATE is null or instr(c.EXPTARIFFRATE,'元')=0 or c.EXPTARIFFRATE=''
        then 0 else
            to_number(substr(c.EXPTARIFFRATE,instr(c.EXPTARIFFRATE,'+')+1,instr(c.EXPTARIFFRATE,'元')-instr(c.EXPTARIFFRATE,'+')),'9999999.99')
    end as exp_tax_rate_PRICE,
    case when length(c.EXPTARIFFRATE)>15 or instr(c.EXPTARIFFRATE,'元')=0 or c.EXPTARIFFRATE is null or c.EXPTARIFFRATE=''
        then null else
            substr(c.EXPTARIFFRATE,instr(c.EXPTARIFFRATE,'元'))
    end as exp_tax_rate_UNIT,
    c.EXPTEMPRATE as exp_temp_rate,
    case when length(c.EXPTEMPRATE)>15 or c.EXPTEMPRATE='0' or c.EXPTEMPRATE is null or c.EXPTEMPRATE=''
        then 0 else
             to_number(substr(c.EXPTEMPRATE,'0',instr(c.EXPTEMPRATE,'%')),'999.99')
    end as exp_temp_rate_PERC,
    case when length(c.EXPTEMPRATE)>15 or c.EXPTEMPRATE is null or instr(EXPTEMPRATE,'元')=0 or c.EXPTEMPRATE=''
        then 0 else
            to_number(substr(c.EXPTEMPRATE,instr(c.EXPTEMPRATE,'+')+1,instr(c.EXPTEMPRATE,'元')-instr(c.EXPTEMPRATE,'+')),'9999999.99')
    end as exp_temp_rate_PRICE,
    case when length(c.EXPTEMPRATE)>15 or instr(c.EXPTEMPRATE,'元')=0 or c.EXPTEMPRATE is null or c.EXPTEMPRATE=''
        then null else
            substr(c.EXPTEMPRATE,instr(c.EXPTEMPRATE,'元'))
    end as exp_temp_rate_UNIT,
    f.IMPFUNDRATE as imp_fund_rate,
    case when length(f.IMPFUNDRATE)>15 or f.IMPFUNDRATE = '0' or f.IMPFUNDRATE is null or f.IMPFUNDRATE=''
        then 0 else
            to_number(substr(f.IMPFUNDRATE,'0',instr(f.IMPFUNDRATE,'%')),'9999.99')
    end as imp_fund_rate_PERC,
    case when length(f.IMPFUNDRATE)>15 or f.IMPFUNDRATE is null or instr(f.IMPFUNDRATE,'元')=0 or f.IMPFUNDRATE=''
        then 0 else
            to_number(substr(f.IMPFUNDRATE,instr(f.IMPFUNDRATE,'+')+1,instr(f.IMPFUNDRATE,'元')-instr(f.IMPFUNDRATE,'+')),'9999999.99')
    end as imp_fund_rate_PRICE,
    case when length(f.IMPFUNDRATE)>15 or instr(f.IMPFUNDRATE,'元')=0 or f.IMPFUNDRATE is null or f.IMPFUNDRATE=''
        then null else
            substr(f.IMPFUNDRATE,instr(f.IMPFUNDRATE,'元'))
    end as imp_fund_rate_UNIT,
    f.IMPSAFEGUARDRATE as imp_safeguard_rate,
    case when length(f.IMPSAFEGUARDRATE)>15 or f.IMPSAFEGUARDRATE = '0' or f.IMPSAFEGUARDRATE is null or f.IMPSAFEGUARDRATE=''
        then 0 else
            to_number(substr(f.IMPSAFEGUARDRATE,'0',instr(f.IMPSAFEGUARDRATE,'%')),'999.99')
    end as imp_safeguard_rate_PERC,
    case when length(f.IMPSAFEGUARDRATE)>15 or f.IMPSAFEGUARDRATE is null or instr(f.IMPSAFEGUARDRATE,'元')=0 or f.IMPSAFEGUARDRATE=''
        then 0 else
            to_number(substr(f.IMPSAFEGUARDRATE,instr(f.IMPSAFEGUARDRATE,'+')+1,instr(f.IMPSAFEGUARDRATE,'元')-instr(f.IMPSAFEGUARDRATE,'+')),'9999999.99')
    end as imp_safeguard_rate_PRICE,
    case when length(f.IMPSAFEGUARDRATE)>15 or instr(f.IMPSAFEGUARDRATE,'元')=0 or f.IMPSAFEGUARDRATE is null or f.IMPSAFEGUARDRATE=''
        then null else
            substr(f.IMPSAFEGUARDRATE,instr(f.IMPSAFEGUARDRATE,'元'))
    end as imp_safeguard_rate_UNIT
from T_PARA_TARIFF_CHINA c
left join T_PARA_TARIFF_CHINA_FUND f on f.CHINAOID = c.OID
    </insert>

    <insert id="insertGeneral" >
        insert into T_GWSTD_TARIFF_GENERAL(code_t_s,g_name,add_tax_rate,antidump_type,ervail_type,
        accord_type,imp_consume_rate,imp_discount_rate,imp_ordinary_rate,imp_temp_rate,
        exp_tax_rate,exp_tax_rate_PERC,exp_tax_rate_PRICE,exp_tax_rate_UNIT,exp_temp_rate,
        exp_temp_rate_PERC,exp_temp_rate_PRICE,exp_temp_rate_UNIT,imp_fund_rate,imp_fund_rate_PERC,
        imp_fund_rate_PRICE,imp_fund_rate_UNIT,imp_safeguard_rate,imp_safeguard_rate_PERC,
        imp_safeguard_rate_PRICE,imp_safeguard_rate_UNIT)
select
    c.TS_CODE as code_t_s,
    c.G_NAME as g_name,
    c.IMPVATRATE as add_tax_rate,
    case when (select count(*) from T_PARA_TARIFF_CHINA_ANTIDUMP where CHINAOID = c.oid)>0
         then '1' else '0'
    end antidump_type,
    case when (select count(*) from T_PARA_TARIFF_CHINA_ERVAIL where CHINAOID = c.oid)>0
        then '1' else '0'
    end ervail_type,
    case when (select count(*) from T_PARA_TARIFF_CHINA_IMPORT where CHINAOID = c.oid)>0
        then '1' else '0'
    end accord_type,
    c.IMPCONSUMERATE as imp_consume_rate,
    c.IMPDISCOUNTRATE as imp_discount_rate,
    c.IMPORDINARYRATE as imp_ordinary_rate,
    c.IMPTEMPRATE as imp_temp_rate,
    c.EXPTARIFFRATE as exp_tax_rate,
    case when length(c.EXPTARIFFRATE)>15 or c.exptariffrate = '0' or c.EXPTARIFFRATE is null
        then 0 else
            to_number(substr(c.EXPTARIFFRATE,'0',instr(c.EXPTARIFFRATE,'%')-1))
    end as exp_tax_rate_PERC,
    case when length(c.EXPTARIFFRATE)>15 or c.EXPTARIFFRATE is null or instr(c.EXPTARIFFRATE,'元')=0
        then 0 else
            to_number(substr(c.EXPTARIFFRATE,instr(c.EXPTARIFFRATE,'+')+1,instr(c.EXPTARIFFRATE,'元')-1-instr(c.EXPTARIFFRATE,'+')))
    end as exp_tax_rate_PRICE,
    case when length(c.EXPTARIFFRATE)>15 or instr(c.EXPTARIFFRATE,'元')=0 or c.EXPTARIFFRATE is null
        then null else
            substr(c.EXPTARIFFRATE,instr(c.EXPTARIFFRATE,'元'))
    end as exp_tax_rate_UNIT,
    c.EXPTEMPRATE as exp_temp_rate,
    case when length(c.EXPTEMPRATE)>15 or c.EXPTEMPRATE='0' or c.EXPTEMPRATE is null
        then 0 else
             to_number(substr(c.EXPTEMPRATE,'0',instr(c.EXPTEMPRATE,'%')-1))
    end as exp_temp_rate_PERC,
    case when length(c.EXPTEMPRATE)>15 or c.EXPTEMPRATE is null or instr(EXPTEMPRATE,'元')=0
        then 0 else
            to_number(substr(c.EXPTEMPRATE,instr(c.EXPTEMPRATE,'+')+1,instr(c.EXPTEMPRATE,'元')-1-instr(c.EXPTEMPRATE,'+')))
    end as exp_temp_rate_PRICE,
    case when length(c.EXPTEMPRATE)>15 or instr(c.EXPTEMPRATE,'元')=0 or c.EXPTEMPRATE is null
        then null else
            substr(c.EXPTEMPRATE,instr(c.EXPTEMPRATE,'元'))
    end as exp_temp_rate_UNIT,
    f.IMPFUNDRATE as imp_fund_rate,
    case when length(f.IMPFUNDRATE)>15 or f.IMPFUNDRATE = '0' or f.IMPFUNDRATE is null
        then 0 else
            to_number(substr(f.IMPFUNDRATE,'0',instr(f.IMPFUNDRATE,'%')-1))
    end as imp_fund_rate_PERC,
    case when length(f.IMPFUNDRATE)>15 or f.IMPFUNDRATE is null or instr(f.IMPFUNDRATE,'元')=0
        then 0 else
            to_number(substr(f.IMPFUNDRATE,instr(f.IMPFUNDRATE,'+')+1,instr(f.IMPFUNDRATE,'元')-1-instr(f.IMPFUNDRATE,'+')))
    end as imp_fund_rate_PRICE,
    case when length(f.IMPFUNDRATE)>15 or instr(f.IMPFUNDRATE,'元')=0 or f.IMPFUNDRATE is null
        then null else
            substr(f.IMPFUNDRATE,instr(f.IMPFUNDRATE,'元'))
    end as imp_fund_rate_UNIT,
    f.IMPSAFEGUARDRATE as imp_safeguard_rate,
    case when length(f.IMPSAFEGUARDRATE)>15 or f.IMPSAFEGUARDRATE = '0' or f.IMPSAFEGUARDRATE is null
        then 0 else
            to_number(substr(f.IMPSAFEGUARDRATE,'0',instr(f.IMPSAFEGUARDRATE,'%')-1))
    end as imp_safeguard_rate_PERC,
    case when length(f.IMPSAFEGUARDRATE)>15 or f.IMPSAFEGUARDRATE is null or instr(f.IMPSAFEGUARDRATE,'元')=0
        then 0 else
            to_number(substr(f.IMPSAFEGUARDRATE,instr(f.IMPSAFEGUARDRATE,'+')+1,instr(f.IMPSAFEGUARDRATE,'元')-1-instr(f.IMPSAFEGUARDRATE,'+')))
    end as imp_safeguard_rate_PRICE,
    case when length(f.IMPSAFEGUARDRATE)>15 or instr(f.IMPSAFEGUARDRATE,'元')=0 or f.IMPSAFEGUARDRATE is null
        then null else
            substr(f.IMPSAFEGUARDRATE,instr(f.IMPSAFEGUARDRATE,'元'))
    end as imp_safeguard_rate_UNIT
from T_PARA_TARIFF_CHINA c
left join T_PARA_TARIFF_CHINA_FUND f on f.CHINAOID = c.OID
    </insert>
</mapper>