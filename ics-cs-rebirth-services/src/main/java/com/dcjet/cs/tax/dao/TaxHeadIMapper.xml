<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.tax.dao.TaxHeadIMapper">
    <resultMap id="thisResultMap" type="com.dcjet.cs.tax.model.TaxHeadI">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" jdbcType="VARCHAR" property="tradeCode"/>
        <result column="INSERT_USER" jdbcType="VARCHAR" property="insertUser"/>
        <result column="INSERT_TIME" jdbcType="TIMESTAMP" property="insertTime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="INSERT_USER_NAME" jdbcType="VARCHAR" property="insertUserName"/>
        <result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="HEAD_ID" jdbcType="VARCHAR" property="headId"/>
        <result column="DUTY_TYPE" jdbcType="VARCHAR" property="dutyType"/>
        <result column="PAY_DATE" jdbcType="TIMESTAMP" property="payDate"/>
        <result column="DUTY_VALUE" jdbcType="DECIMAL" property="dutyValue"/>
        <result column="TAX_VALUE" jdbcType="DECIMAL" property="taxValue"/>
        <result column="EXCISE_TAX" jdbcType="DECIMAL" property="exciseTax"/>
        <result column="TAX_TOTAL" jdbcType="DECIMAL" property="taxTotal"/>
        <result column="DUTY_VALUE_ESTIMATE" jdbcType="DECIMAL" property="dutyValueEstimate"/>
        <result column="TAX_VALUE_ESTIMATE" jdbcType="DECIMAL" property="taxValueEstimate"/>
        <result column="EXCISE_TAX_ESTIMATE" jdbcType="DECIMAL" property="exciseTaxEstimate"/>
        <result column="TAX_TOTAL_ESTIMATE" jdbcType="DECIMAL" property="taxTotalEstimate"/>
        <result column="NOTE" jdbcType="VARCHAR" property="note"/>
        <!-- 其他表-->
        <result column="DECLARE_NAME" jdbcType="VARCHAR" property="declareName"/>
        <result column="EMS_LIST_NO" jdbcType="VARCHAR" property="emsListNo"/>
        <result column="LIST_NO" jdbcType="VARCHAR" property="listNo"/>
        <result column="DEC_INSERT_TIME" jdbcType="TIMESTAMP" property="decInsertTime"/>
        <result column="ENTRY_NO" jdbcType="VARCHAR" property="entryNo"/>
        <result column="D_DATE" jdbcType="TIMESTAMP" property="declareDate"/>
        <result column="TRADE_MODE" jdbcType="VARCHAR" property="tradeMode"/>
    </resultMap>
    <sql id="fullEntitySelect">
        head.SID,head.TRADE_CODE,head.INSERT_USER,head.INSERT_TIME,head.UPDATE_USER,head.UPDATE_TIME,head.INSERT_USER_NAME,head.UPDATE_USER_NAME,
        head.STATUS,head.HEAD_ID,head.DUTY_TYPE,head.DUTY_VALUE,head.TAX_VALUE,head.EXCISE_TAX,head.TAX_TOTAL,head.DUTY_VALUE_ESTIMATE,
        head.TAX_VALUE_ESTIMATE,head.EXCISE_TAX_ESTIMATE,head.TAX_TOTAL_ESTIMATE,head.NOTE,head.PAY_DATE,
        entry.DECLARE_NAME,entry.EMS_LIST_NO,n.EMS_LIST_NO as HEAD_EMS_LIST_NO,entry.INSERT_TIME as DEC_INSERT_TIME,entry.ENTRY_NO,entry.TRADE_MODE,
        to_date(entry.D_DATE, 'yyyy-MM-dd') as D_DATE , bill.LIST_NO
    </sql>

    <select id="getById" resultMap="thisResultMap">
        select
        <include refid="fullEntitySelect"/>
        from T_TAX_I_HEAD head
        join T_DEC_I_ENTRY_HEAD entry ON entry.SID = head.HEAD_ID
        join T_DEC_I_BILL_HEAD bill on entry.BILL_HEAD_ID = bill.SID
        join T_DEC_ERP_I_HEAD_N n on bill.head_id = n.sid
        where head.SID = #{sid}
    </select>

    <select id="getList" parameterType="com.dcjet.cs.dto.tax.param.TaxHeadIQueryParam" resultMap="thisResultMap">
        select
        <include refid="fullEntitySelect"/>
        from T_TAX_I_HEAD head
        join T_DEC_I_ENTRY_HEAD entry ON entry.SID = head.HEAD_ID
        join T_DEC_I_BILL_HEAD bill on entry.BILL_HEAD_ID = bill.SID
        join T_DEC_ERP_I_HEAD_N n on bill.head_id = n.sid
        <where>
            head.TRADE_CODE = #{tradeCode}
            <if test="emsListNo != null and emsListNo != '' ">
                and entry.EMS_LIST_NO like '%'|| #{emsListNo} || '%'
            </if>
            <if test="headEmsListNo != null and headEmsListNo != '' ">
                and n.EMS_LIST_NO like '%'|| #{headEmsListNo} || '%'
            </if>
            <if test="entryNo != null and entryNo != '' ">
                and entry.ENTRY_NO like '%'|| #{entryNo} || '%'
            </if>
            <if test="declareDateFrom != null and declareDateFrom != ''">
                <![CDATA[ and to_date(entry.D_DATE, 'yyyy-MM-dd') >= to_date(#{declareDateFrom}, 'yyyy-MM-dd') ]]>
            </if>
            <if test="declareDateTo != null and declareDateTo != ''">
                <![CDATA[ and to_date(entry.D_DATE, 'yyyy-MM-dd') < to_date(#{declareDateTo}, 'yyyy-MM-dd') + 1 ]]>
            </if>
            <if test="tradeMode != null and tradeMode != '' ">
                and entry.TRADE_MODE = #{tradeMode}
            </if>
            <if test="declareName != null and declareName != '' ">
                and entry.DECLARE_NAME like '%'|| #{declareName} || '%'
            </if>
            <if test="dutyType != null and dutyType != '' ">
                and head.DUTY_TYPE = #{dutyType}
            </if>
            <if test="payDateFrom != null and payDateFrom != ''">
                <![CDATA[ and head.PAY_DATE >= to_date(#{payDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="payDateTo != null and payDateTo != ''">
                <![CDATA[ and head.PAY_DATE <= to_date(#{payDateTo}, 'yyyy-MM-dd hh24:mi:ss') ]]>
            </if>
            <if test="sid != null and sid != '' ">
                and head.SID = #{sid}
            </if>
        </where>
        order by head.INSERT_TIME desc, head.SID
    </select>
    <select id="selectHeadId" resultType="java.lang.String">
        select distinct a.head_id from T_TAX_I_LIST a
        inner join T_TAX_I_LIST_IMPORT b on a.sid = b.PARENT_ID and b.TEMP_OWNER = #{tempOwner}
    </select>

    <update id="batchSetDutyType" parameterType="com.dcjet.cs.dto.tax.param.TaxHeadBatchSetDutyTypeDto">
        update T_TAX_I_HEAD
        set
        <if test="dutyType != null and dutyType != '' ">
            DUTY_TYPE = #{dutyType},
        </if>
        <if test="payDate != null">
            PAY_DATE = #{payDate},
        </if>
        UPDATE_USER=#{updateUser}, UPDATE_TIME = #{updateTime}, UPDATE_USER_NAME=#{updateUserName}
        where TRADE_CODE = #{tradeCode}
        and SID in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="clearTax" parameterType="com.dcjet.cs.dto.tax.param.TaxHeadClearTaxDto">
        update T_TAX_I_HEAD
        set DUTY_VALUE = NULL, TAX_VALUE = NULL, EXCISE_TAX = NULL, TAX_TOTAL = NULL,
        UPDATE_USER=#{updateUser}, UPDATE_TIME = #{updateTime}, UPDATE_USER_NAME=#{updateUserName}
        where TRADE_CODE = #{tradeCode}
        and SID in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="importFromTmp">
        UPDATE T_TAX_I_HEAD a
        set (TAX_TOTAL,DUTY_VALUE,TAX_VALUE,EXCISE_TAX,DUTY_TYPE,PAY_DATE,NOTE,UPDATE_TIME,UPDATE_USER) = (
        select b.TAX_TOTAL,b.DUTY_VALUE,b.TAX_VALUE,b.EXCISE_TAX,b.DUTY_TYPE,b.PAY_DATE,b.NOTE,b.UPDATE_TIME,b.UPDATE_USER
         from T_TAX_I_HEAD_IMPORT b where a.SID = b.PARENT_ID and b.TEMP_OWNER = #{tempOwner} )
				 where exists (select b.PARENT_ID from  T_TAX_I_HEAD_IMPORT b where b.PARENT_ID = a.SID and b.TEMP_OWNER = #{tempOwner} )
    </update>

    <update id="importFromTmp" databaseId="postgresql">
        update t_tax_i_head a
        set (tax_total,duty_value,tax_value,excise_tax,duty_type,pay_date,note,update_time,update_user) = (
        select b.tax_total,b.duty_value,b.tax_value,b.excise_tax,b.duty_type,b.pay_date,b.note,b.update_time,b.update_user
         from t_tax_i_head_import b where a.sid = b.parent_id  and b.temp_owner = #{tempOwner} )
         where exists (select b.parent_id from  t_tax_i_head_import b where b.parent_id = a.sid and b.temp_owner = #{tempOwner} )
    </update>
    <update id="updateTaxDec">
        UPDATE T_TAX_I_HEAD
        SET (DUTY_VALUE, TAX_VALUE, EXCISE_TAX, TAX_TOTAL, UPDATE_TIME, UPDATE_USER) = (
            SELECT SUM(COALESCE(list.DUTY_VALUE, 0)), SUM(COALESCE(list.TAX_VALUE, 0)),
                   SUM(COALESCE(list.EXCISE_TAX, 0)), SUM(COALESCE(list.TAX_TOTAL, 0))
            <if test='_databaseId == "postgresql" '>
                ,CURRENT_TIMESTAMP
            </if>
            <if test='_databaseId != "postgresql" '>
                ,SYSDATE
            </if>
            , #{insertUser}
            FROM T_TAX_I_HEAD head
            LEFT JOIN T_DEC_I_ENTRY_HEAD entry ON entry.SID = head.HEAD_ID
            INNER JOIN T_DEC_ERP_I_HEAD_N n on n.SID = entry.ERP_HEAD_ID
            LEFT JOIN T_DEC_ERP_I_LIST_N nl on nl.BILL_HEAD_ID = entry.BILL_HEAD_ID
            LEFT JOIN T_TAX_DEC_I_LIST list on nl.SID = list.ERP_LIST_ID
            WHERE head.SID = #{headId}
        ) WHERE SID = #{headId}
    </update>
    <update id="updateTax">
        <foreach collection="list" item="item" open="" separator=";" close="">
            UPDATE T_TAX_I_HEAD
            SET (DUTY_VALUE, TAX_VALUE, EXCISE_TAX, TAX_TOTAL, UPDATE_TIME, UPDATE_USER) = (
                SELECT  a.DUTY_VALUE, a.TAX_VALUE, a.EXCISE_TAX, a.DUTY_VALUE + a.TAX_VALUE + a.EXCISE_TAX
                <if test='_databaseId == "postgresql" '>
                    ,CURRENT_TIMESTAMP
                </if>
                <if test='_databaseId != "postgresql" '>
                    ,SYSDATE
                </if>
                , #{insertUser}
                FROM (
                    SELECT SUM(COALESCE(DUTY_VALUE, 0)) as DUTY_VALUE, SUM(COALESCE(TAX_VALUE, 0)) as TAX_VALUE,
                           SUM(COALESCE(EXCISE_TAX, 0)) as EXCISE_TAX
                    FROM T_TAX_I_LIST
                    WHERE HEAD_ID = #{item}
                ) a
            ) WHERE SID = #{item}
        </foreach>
    </update>
    <update id="updateDataByTaxVouNo">
        update t_tax_i_head a
        set
            <if test='taxType == "A"'>
                duty_value = z.payment_amount,
            </if>
            <if test='taxType == "L"'>
                tax_value = z.payment_amount,
            </if>
            <if test='taxType == "Y"'>
                excise_tax = z.payment_amount,
            </if>
            update_time = CURRENT_TIMESTAMP(0),
            update_user_name = 'GW',
            update_user = 'GW'
        from (
            select b.sid, sum(d.payment_amount) payment_amount
            from t_tax_i_head b
            inner join t_dec_i_entry_head c on c.sid = b.head_id
            inner join t_gwstd_dutyform_head d on d.entry_id = c.entry_no and d.trade_code = c.trade_code
            where d.entry_id = #{entryId} and d.tax_type = #{taxType}
            group by b.sid
        ) z
        where z.sid = a.sid
    </update>
    <update id="updateTaxTotalByTaxVouNo">
        update t_tax_i_head a
        set
            tax_total = z.payment_amount,
            update_time = CURRENT_TIMESTAMP(0),
            update_user_name = 'GW',
            update_user = 'GW'
        from (
            select b.sid, sum(d.payment_amount) payment_amount
            from t_tax_i_head b
            inner join t_dec_i_entry_head c on c.sid = b.head_id
            inner join t_gwstd_dutyform_head d on d.entry_id = c.entry_no and d.trade_code = c.trade_code
            where d.entry_id = #{entryId}
            group by b.sid
        ) z
        where z.sid = a.sid
    </update>

    <delete id="clearTmp">
        delete from T_TAX_I_HEAD_IMPORT where TEMP_OWNER = #{tempOwner}
    </delete>
</mapper>