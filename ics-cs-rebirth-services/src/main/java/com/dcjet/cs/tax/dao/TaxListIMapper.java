package com.dcjet.cs.tax.dao;

import com.dcjet.cs.dto.tax.param.TaxListClearTaxDto;
import com.dcjet.cs.dto.tax.param.TaxListIQueryParam;
import com.dcjet.cs.tax.model.TaxListI;
import com.dcjet.cs.tax.model.dutyform.DutyFormHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TaxListIMapper extends Mapper<TaxListI> {

    List<TaxListI> getList(TaxListIQueryParam queryParam);
    TaxListI getByListId(@Param("listId") String listId);

    void clearTax(TaxListClearTaxDto dto);

    String getEntryHeadId(@Param("headId") String headId, @Param("tradeCode") String tradeCode);


    void importFromTmp(@Param("tempOwner") String tempOwner);

    void clearTmp(@Param("tempOwner") String tempOwner);

    int updateDataByTaxVouNo(DutyFormHead head);
}
