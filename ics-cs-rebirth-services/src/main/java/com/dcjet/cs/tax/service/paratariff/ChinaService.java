package com.dcjet.cs.tax.service.paratariff;

import com.dcjet.cs.tax.dao.paratariff.ChinaMapper;
import com.dcjet.cs.tax.model.paratariff.China;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.xdo.common.base.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ChinaService extends BaseService<China> {
    @Resource
    ChinaMapper mapper;

    @Override
    public Mapper<China> getMapper() {
        return mapper;
    }

    public void insert(List<China> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        BulkSqlOpt.batchInsertAndThrowException(list, ChinaMapper.class);
    }
}
