package com.dcjet.cs.tax.service;

import com.dcjet.cs.dto.tax.TaxDecListIDto;
import com.dcjet.cs.dto.tax.param.TaxDecListIParam;
import com.dcjet.cs.dto.tax.param.TaxDecListIQueryParam;
import com.dcjet.cs.tax.dao.TaxDecListIMapper;
import com.dcjet.cs.tax.dao.TaxHeadIMapper;
import com.dcjet.cs.tax.mapper.TaxDecListIDtoMapper;
import com.dcjet.cs.tax.model.TaxDecListI;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.war.dao.WarringPassConfigMapper;
import com.dcjet.cs.war.model.WarringPassConfig;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
public class TaxDecListIService {
    @Resource
    TaxDecListIMapper taxDecListIMapper;
    @Resource
    TaxDecListIDtoMapper taxDecListIDtoMapper;
    @Resource
    private WarringPassConfigMapper warringPassConfigMapper;
    @Resource
    TaxHeadIMapper taxHeadIMapper;

    public ResultObject<List<TaxDecListIDto>> getListPaged(TaxDecListIQueryParam queryParam, PageParam pageParam, UserInfoToken userInfo) {
//        queryParam.setTradeCode(userInfo.getCompany());
        Page<TaxDecListIDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> taxDecListIMapper.getList(queryParam));
        ResultObject<List<TaxDecListIDto>> paged = ResultObject.createInstance(page.getResult(), (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public List<TaxDecListIDto> getList(TaxDecListIQueryParam queryParam) {
//        queryParam.setTradeCode(userInfo.getCompany());
        return taxDecListIMapper.getList(queryParam);
    }

    public List<TaxDecListIDto> listAllList(String headId) {
        TaxDecListIQueryParam queryParam = new TaxDecListIQueryParam();
        queryParam.setHeadId(headId);
//        queryParam.setTradeCode(tradeCode);
        List<TaxDecListIDto> list = taxDecListIMapper.getList(queryParam);
        return list == null ? Collections.EMPTY_LIST : list;
    }

    @Transactional
    public void importFromTmp(String tempOwner) {
        taxDecListIMapper.insertFromTmp(tempOwner);
        taxDecListIMapper.updateFromTmp(tempOwner);
        taxDecListIMapper.clearTmp(tempOwner);
    }

    public TaxDecListI save(TaxDecListIParam param, UserInfoToken userInfoToken) {
        TaxDecListI taxDecListI;
        if (StringUtils.isEmpty(param.getSid())) {
            taxDecListI = insert(param, userInfoToken);
        } else {
            taxDecListI = update(param, userInfoToken);
        }

        // 回填表头
        WarringPassConfig warringPassConfig = new WarringPassConfig(){{
            setTaxSummary(ConstantsStatus.STATUS_2);
            setTradeCode(userInfoToken.getCompany());
        }};
        int num = warringPassConfigMapper.selectCount(warringPassConfig);
        if (num > 0) {
            taxHeadIMapper.updateTaxDec(param.getHeadId(), userInfoToken.getUserNo());
        }

        return taxDecListI;
    }

    private TaxDecListI insert(TaxDecListIParam param, UserInfoToken userInfoToken) {
        TaxDecListI data = new TaxDecListI();
        data.setTradeCode(userInfoToken.getCompany());
        data.setInsertTime(new Date());
        data.setInsertUser(userInfoToken.getUserNo());
        data.setSid(UUID.randomUUID().toString());
        taxDecListIDtoMapper.copyToEntity(param, data);
        calTaxTotal(data);
        taxDecListIMapper.insert(data);
        return data;
    }

    private TaxDecListI update(TaxDecListIParam param, UserInfoToken userInfoToken) {
        TaxDecListI oldData = taxDecListIMapper.getById(param.getSid());
        if (oldData == null) {
            return null;
        }
        oldData.setUpdateTime(new Date());
        oldData.setUpdateUser(userInfoToken.getUserNo());
        taxDecListIDtoMapper.copyToEntity(param, oldData);
        calTaxTotal(oldData);
        taxDecListIMapper.updateByPrimaryKey(oldData);
        return oldData;
    }

    private void calTaxTotal(TaxDecListI tmp) {
        BigDecimal taxTotal = BigDecimal.ZERO;
        if (tmp.getTaxValue() != null) {
            taxTotal = taxTotal.add(tmp.getTaxValue());
        }
        if (tmp.getDutyValue() != null) {
            taxTotal = taxTotal.add(tmp.getDutyValue());
        }
        if (tmp.getExciseTax() != null) {
            taxTotal = taxTotal.add(tmp.getExciseTax());
        }
        if (tmp.getDutyValueInterest() != null) {
            taxTotal = taxTotal.add(tmp.getDutyValueInterest());
        }
        if (tmp.getTaxValueInterest() != null) {
            taxTotal = taxTotal.add(tmp.getTaxValueInterest());
        }
        if (tmp.getOtherTax() != null) {
            taxTotal = taxTotal.add(tmp.getOtherTax());
        }
        tmp.setTaxTotal(taxTotal);
    }

    /**
     * 获取汇总统计
     *
     * @param param
     * @return
     * <AUTHOR>
     */
    public ResultObject getListTotal(TaxDecListIQueryParam param){
        String totals = taxDecListIMapper.getListTotal(param);
        String lang = "";
        RequestAttributes attribs = RequestContextHolder.getRequestAttributes();
        if (attribs instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) attribs).getRequest();
            lang = request.getParameter("lang");
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(lang)) {
            if(totals != null) {
                totals = totals.replaceAll("关税:",xdoi18n.XdoI18nUtil.t("关税:")).
                        replaceAll("增值税:",xdoi18n.XdoI18nUtil.t("增值税:")).
                        replaceAll("总金额:",xdoi18n.XdoI18nUtil.t("总金额:"));
            }
        }
        ResultObject paged = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"),totals);
        return  paged;
    }

}
