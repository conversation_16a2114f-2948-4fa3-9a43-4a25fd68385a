package com.dcjet.cs.tax.model.paratariff;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Setter
@Getter
@Table(name = "T_PARA_TARIFF_CHINA_FUND")
public class ChinaFund {
    @Id
    @Column(name = "OID")
    @JsonProperty("OID")
    private String oid;
    @Column(name = "INSERT_TIME")
    @JsonProperty("INSERT_TIME")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone="GMT+8")
    private Date insertTime;
    @Column(name = "CHINAOID")
    @JsonProperty("CHINAOID")
    private String chinaOid;
    @Column(name = "COUNTRYNAME")
    @JsonProperty("COUNTRYNAME")
    private String countryName;//原产国（地区）
    @Column(name = "COMPANYNAMECN")
    @JsonProperty("COMPANYNAMECN")
    private String companyNameCn;//厂商（中文）
    @Column(name = "COMPANYNAMEEN")
    @JsonProperty("COMPANYNAMEEN")
    private String companyNameEn;//厂商（英文）
    @Column(name= "IMPFUNDRATE")
    @JsonProperty("IMPFUNDRATE")
    private String impFundRate;//进口废弃电器电子基金
    @Column(name= "IMPSAFEGUARDRATE")
    @JsonProperty("IMPSAFEGUARDRATE")
    private String impSafeGuardRate;//保障措施关税税率
}
