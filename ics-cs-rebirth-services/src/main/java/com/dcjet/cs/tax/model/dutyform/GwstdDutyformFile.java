package com.dcjet.cs.tax.model.dutyform;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-4-21
 */
@Setter
@Getter
@Table(name = "t_gwstd_dutyform_file")
public class GwstdDutyformFile implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * 税费单号
     */
    @Column(name = "tax_vou_no")
    private String taxVouNo;
    /**
     * 华为云的核对单文件地址
     */
    @Column(name = "check_obs_url")
    private String checkObsUrl;
    /**
     * 最终的核对单文件地址(云环境存obs地址,本地存本地路径)
     */
    @Column(name = "check_final_path")
    private String checkFinalPath;
    /**
     * 本地部署核对单下载出错的原因
     */
    @Column(name = "check_down_err")
    private String checkDownErr;
    /**
     * 华为云的缴款书文件地址
     */
    @Column(name = "pay_obs_url")
    private String payObsUrl;
    /**
     * 最终的缴款书文件地址(云环境存obs地址,本地存本地路径)
     */
    @Column(name = "pay_final_path")
    private String payFinalPath;
    /**
     * 本地部署缴款书下载出错的原因
     */
    @Column(name = "pay_down_err")
    private String payDownErr;
    /**
     * 创建人
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 核对单文件名(税单号+_+核对单)
     */
    @Column(name = "check_file_name")
    private String checkFileName;
    /**
     * 缴款书文件名(税单号+_+缴款书)
     */
    @Column(name = "pay_file_name")
    private String payFileName;
}
