package com.dcjet.cs.tax.service;

import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.dto.tax.TaxDecListIDto;
import com.dcjet.cs.tax.dao.TaxHeadIMapper;
import com.dcjet.cs.tax.model.ImportTaxDecListImport;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.war.dao.WarringPassConfigMapper;
import com.dcjet.cs.war.model.WarringPassConfig;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.exception.ErrorException;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.EnumCollection;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
public class ImportTaxDecListImportService implements ImportHandlerInterface {
//    private static final FastDateFormat fsdf = FastDateFormat.getInstance("yyyyMMdd");

    @Resource
    TaxDecListIService taxDecListIService;
    @Resource
    private BulkInsertConfig bulkInsertConfig;
    @Resource
    private WarringPassConfigMapper warringPassConfigMapper;
    @Resource
    TaxHeadIMapper taxHeadIMapper;

    public IBulkInsert getBulkInsertInstance() throws Exception {
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }

    //料件、成品、非保
    private static final String TAX_HEAD_I_TASK_CODE = "TAX-DEC-LIST-I-UPDATE";
    public final int CORRECT_FLAG = EnumCollection.EntityListType.CORRECT_LIST.ordinal();
    public final int WRONG_FLAG = EnumCollection.EntityListType.WRONG_LIST.ordinal();
    public final int WARN_FLAG = EnumCollection.EntityListType.WARN_LIST.ordinal();

    @Override
    public String getTaskCode() {
        return TAX_HEAD_I_TASK_CODE;
    }

    @Override
    public List<ExcelImportDto> checkData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        if (mapObjectList.size() <= 0) {
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        String insertUser = mapBusinessParam.get("insertUser").toString();
        String headId = mapBusinessParam.get("headId").toString();

        List<ExcelImportDto> excelImportDtoList = new ArrayList<>(1);

        excelImportDtoList.add(generateTmpList(mapObjectList.entrySet().iterator().next().getValue(), tradeCode, insertUser, headId));
        return excelImportDtoList;
    }

    private ExcelImportDto generateTmpList(
            List<Object> objectList, String tradeCode, String updateUser, String headId) {
        int intObjectListSize = objectList.size();
        XdoImportLogger.log("共获取实体：" + intObjectListSize);

        List<ImportTaxDecListImport> tmpList = Collections.synchronizedList(new ArrayList<>(intObjectListSize));

        String tempOwner = UUID.randomUUID().toString();
        Date now = new Date();
        objectList.forEach(pa -> {
            ImportTaxDecListImport tmp = (ImportTaxDecListImport) pa;
            if (tmp.getInputDate() == null) {
                tmp.setInputDate(now);
            }
            tmp.setUpdateUser(updateUser);
            tmp.setUpdateTime(now);
            tmp.setTempOwner(tempOwner);
            tmp.setTradeCode(tradeCode);
            tmpList.add(tmp);
            checkBizColumns(tmp);
        });
        uniqueCheck(tmpList, headId);
        return doImport(tempOwner, tmpList);
    }

    private void calTaxTotal(ImportTaxDecListImport tmp) {
        BigDecimal taxTotal = BigDecimal.ZERO;
        if (tmp.getTaxValue() != null) {
            taxTotal = taxTotal.add(tmp.getTaxValue());
        }
        if (tmp.getDutyValue() != null) {
            taxTotal = taxTotal.add(tmp.getDutyValue());
        }
        if (tmp.getExciseTax() != null) {
            taxTotal = taxTotal.add(tmp.getExciseTax());
        }
        if (tmp.getDutyValueInterest() != null) {
            taxTotal = taxTotal.add(tmp.getDutyValueInterest());
        }
        if (tmp.getTaxValueInterest() != null) {
            taxTotal = taxTotal.add(tmp.getTaxValueInterest());
        }
        if (tmp.getOtherTax() != null) {
            taxTotal = taxTotal.add(tmp.getOtherTax());
        }
        tmp.setTaxTotal(taxTotal);
    }

    private ExcelImportDto doImport(String tempOwner, List<ImportTaxDecListImport> tmpList) {
        ExcelImportDto excelImportDto = new ExcelImportDto();
        List<ImportTaxDecListImport> correctList = new LinkedList<>();
        List<ImportTaxDecListImport> wrongList = new LinkedList<>();
        tmpList.stream().forEach(tmp -> {
            if (WRONG_FLAG == tmp.getTempFlag()) {
                wrongList.add(tmp);
            } else {
                calTaxTotal(tmp);
                correctList.add(tmp);
            }
        });

        excelImportDto.setTempOwnerId(tempOwner);
        excelImportDto.setCorrectNumber(correctList.size());
        excelImportDto.setCorrectList(correctList);
        excelImportDto.setWrongNumber(wrongList.size());
        excelImportDto.setWrongList(wrongList);

        //出错数据不应该和成功数据一起插入，出错的数据有可能导致插入失败！包括但不限于字段长度限制等问题
        try {
            IBulkInsert bulkInsert = getBulkInsertInstance();
            bulkInsert.fastImport(correctList);
        } catch (BulkInsertException ex) {
            XdoImportLogger.log(ex);
        } catch (Exception ex) {
            XdoImportLogger.log(ex);
        }
        return excelImportDto;
    }

    @Override
    public ResultObject persistData(Map<String, Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        if (tempOwnerIdList == null || tempOwnerIdList.size() <= 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("临时表处理批次Id为空，无法执行持久化数据方法"));
        }
        //按顺序获取临时表批次Id，单sheet时只需获取第一个即可
        String tempOwnerId = tempOwnerIdList.get(0);
        taxDecListIService.importFromTmp(tempOwnerId);

        // 回填表头
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        String headId = mapBusinessParam.get("headId").toString();
        String insertUser = mapBusinessParam.get("insertUser").toString();
        WarringPassConfig warringPassConfig = new WarringPassConfig(){{
            setTaxSummary(ConstantsStatus.STATUS_2);
            setTradeCode(tradeCode);
        }};
        int num = warringPassConfigMapper.selectCount(warringPassConfig);
        if (num > 0) {
            taxHeadIMapper.updateTaxDec(headId, insertUser);
        }

        return resultObject;
    }

    /**
     * 业务主键 唯一性校验
     *
     * @param tmpList
     */
    private void uniqueCheck(List<ImportTaxDecListImport> tmpList, String headId) {
        Set<String> uniqueSet = new HashSet<>();
        final List<TaxDecListIDto> listList = taxDecListIService.listAllList(headId);
        tmpList.stream().forEach(tmp -> {
            //已经标记为失败的就不做判断了，浪费性能
            if (WRONG_FLAG != tmp.getTempFlag()) {
                String uniqueKey = uniqueKey(tmp.getSerialNo());
                if (uniqueSet.contains(uniqueKey)) {
                    tmp.setTempRemark(tmp.getTempRemark() + xdoi18n.XdoI18nUtil.t("同批次存在重复的业务主键：顺序号|"));
                    tmp.setTempFlag(WRONG_FLAG);
                } else {
                    uniqueSet.add(uniqueKey);
                    if (listList.stream().noneMatch(list -> uniqueKey.equals(uniqueKey(list.getSerialNo())))) {
                        tmp.setTempRemark(tmp.getTempRemark() + xdoi18n.XdoI18nUtil.t("无效的顺序号|"));
                        tmp.setTempFlag(WRONG_FLAG);
                    } else {
                        listList.stream().forEach(list -> {
                            if (uniqueKey.equals(uniqueKey(list.getSerialNo()))) {
                                tmp.setSid(UUID.randomUUID().toString());
                                String sid = list.getSid();
                                tmp.setHeadId(list.getHeadId());
                                tmp.setErpListId(list.getErpListId());
                                if (StringUtils.isEmpty(sid)) {
                                    tmp.setIsInsert("true");
                                    tmp.setParentId(tmp.getSid());
                                } else {
                                    tmp.setParentId(sid);
                                }
                            }
                        });
                    }
                }
            }
        });
    }

    private String uniqueKey(BigDecimal serialNo) {
//        String key = "";
        if (serialNo != null) {
            return String.valueOf(serialNo.longValue());
        }
        return "";
    }

    /**
     * 业务校验
     *
     * @param tmp
     */
    public void checkBizColumns(ImportTaxDecListImport tmp) {

    }

}
