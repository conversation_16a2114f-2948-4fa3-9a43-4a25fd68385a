package com.dcjet.cs.tax.service.paratariff;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.tax.dao.paratariff.ChinaImportItemMapper;
import com.dcjet.cs.tax.model.paratariff.ChinaImportItem;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.xdo.common.base.service.BaseService;
import com.xdo.pcode.service.PCodeHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ChinaImportItemService extends BaseService<ChinaImportItem> {
    @Resource
    ChinaImportItemMapper mapper;
    @Resource
    PCodeHolder pCodeHolder;

    @Override
    public Mapper<ChinaImportItem> getMapper() {
        return mapper;
    }

    public void insert(List<ChinaImportItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.parallelStream().forEach(item -> {
            item.setCountryCode(pCodeHolder.getByName(PCodeType.COUNTRY_OUTDATED, item.getCountryName()).orElse(null));
        });
        BulkSqlOpt.batchInsertAndThrowException(list, ChinaImportItemMapper.class);
    }
}
