package com.dcjet.cs.ocrrecord.dao;

import com.dcjet.cs.dto.ocrrecord.GwLvOcrInvoiceListDto;
import com.dcjet.cs.ocrrecord.model.GwLvOcrInvoiceList;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate dcits
 * GwLvOcrInvoiceList
 *
 * <AUTHOR>
 * @date: 2022-4-27
 */
public interface GwLvOcrInvoiceListMapper extends Mapper<GwLvOcrInvoiceList> {
    /**
     * 根据参数查询
     *
     * @param gwLvOcrInvoiceList
     * @return
     */
    List<GwLvOcrInvoiceListDto> getList(GwLvOcrInvoiceList gwLvOcrInvoiceList);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 根据表头headId批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    void deleteByHeadIds(List<String> sids);

    /**
     * 根据表头headId查询是否存在表体数据
     *
     * @param sids
     * @return
     */
    int getListNumByHeadIds(List<String> sids);
}
