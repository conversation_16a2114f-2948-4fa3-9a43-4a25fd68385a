package com.dcjet.cs.ocrrecord.model;

import com.dcjet.cs.dto.ocrrecord.AxPoHead;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-5-12
 */
@Setter
@Getter
@Table(name = "t_gw_lv_ax_invoice_head")
public class GwLvAxInvoiceHead implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * shipment ID
     */
    @Column(name = "shipment_id")
    private String shipmentId;
    /**
     * 品牌
     */
    @Column(name = "brand_code")
    private String brandCode;
    /**
     * 发票号码
     */
    @Column(name = "invoice_no")
    private String invoiceNo;
    /**
     * vendor code
     */
    @Column(name = "vendor_code")
    private String vendorCode;
    /**
     * 境外发货人
     */
    @Column(name = "consignee_name")
    private String consigneeName;
    /**
     * 发票类型 0: invoice 1:speial
     */
    @Column(name = "invoice_type")
    private String invoiceType;
    /**
     * ERP PO No.
     */
    @Column(name = "po_no")
    private String poNo;
    /**
     * 成交方式
     */
    @Column(name = "trans_mode")
    private String transMode;
    @Column(name = "trans_mode_convert")
    private String transModeConvert;
    /**
     * 发票总数量
     */
    @Column(name = "total_qty")
    private BigDecimal totalQty;
    /**
     * 发票总金额/FOB
     */
    @Column(name = "total_fob_amt")
    private BigDecimal totalFobAmt;
    /**
     * 发票Freight charge
     */
    @Column(name = "charge_total")
    private BigDecimal chargeTotal;
    /**
     * 发票总金额/CIF
     */
    @Column(name = "total_amount")
    private BigDecimal totalAmount;
    /**
     * 币制
     */
    @Column(name = "curr")
    private String curr;
    @Column(name = "curr_convert")
    private String currConvert;
    /**
     * 发票总毛重
     */
    @Column(name = "gross_wt")
    private BigDecimal grossWt;
    /**
     * 发票总净重
     */
    @Column(name = "net_wt")
    private BigDecimal netWt;
    /**
     * On-hold
     */
    @Column(name = "on_hold_status")
    private String onHoldStatus;
    /**
     * 发票状态
     */
    @Column(name = "invoice_status")
    private String invoiceStatus;
    /**
     * PO类型
     */
    @Column(name = "po_type")
    private String poType;
    /**
     * Site
     */
    @Column(name = "site")
    private String site;
    /**
     * Warhouse
     */
    @Column(name = "ware_house")
    private String wareHouse;
    /**
     * ocr表头sid
     */
    @Column(name = "ocr_head_id")
    private String ocrHeadId;
    /**
     * 企业编码
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 创建人名称
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 创建时间-开始
     */
    @Transient
    private String insertTimeFrom;
    /**
     * 创建时间-结束
     */
    @Transient
    private String insertTimeTo;
    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 修改时间-开始
     */
    @Transient
    private String updateTimeFrom;
    /**
     * 修改时间-结束
     */
    @Transient
    private String updateTimeTo;


    @Column(name = "company")
    private String company;//区域
    @Column(name = "exists_batch")
    private String existsBatch;
    /**
     * 发票日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "invoice_date")
    private Date invoiceDate;
    /**
     * 送货单号
     */
    @Column(name = "delivery_no")
    private String deliveryNo;

    @Transient
    private List<GwLvAxInvoiceList> gwLvAxInvoiceLists;


    public GwLvAxInvoiceHead initData(GwLvOcrInvoiceHead ocrInvoiceHead) {
        this.sid = UUID.randomUUID().toString();
        this.ocrHeadId = ocrInvoiceHead.getSid();
        this.tradeCode = ocrInvoiceHead.getTradeCode();
        this.insertUser = ocrInvoiceHead.getInsertUser();
        this.insertUserName = ocrInvoiceHead.getInsertUserName();
        this.insertTime = new Date();
        return this;
    }


    public GwLvAxInvoiceHead initAxData(AxPoHead head, UserInfoToken userInfo) {
        this.shipmentId = head.getPciscmsapshipmentnumber();
        this.brandCode = head.getDimension1();
        this.invoiceNo = head.getInvoicenumber();
        this.vendorCode = head.getOrderaccount();
        this.consigneeName = head.getPurchname();
        this.invoiceType = head.getInvoicetype();
        this.poNo = head.getPurchid();
        this.totalQty = head.getTotalqty();
        this.totalFobAmt = head.getTotalmoneyfob();
        this.chargeTotal = head.getFreightcharge();
        this.totalAmount = head.getTotalmoneycif();
        this.curr = head.getCurrencycode();
        this.netWt = head.getNetwt();
        this.grossWt = head.getGrosswt();
        this.onHoldStatus = head.getPciscmonholditems();
        this.invoiceStatus = head.getPurchstatus();
        this.poType = head.getPciscmpoordercategory();
        this.site = head.getInventsiteid();
        this.wareHouse = head.getInventlocationid();
        this.company = head.getDataareaid();
        this.invoiceDate = head.getInvoiceDate();
        this.deliveryNo = head.getDeliveryNo();
        this.existsBatch = head.getExistsBatch();

        this.sid = UUID.randomUUID().toString();
        this.ocrHeadId = UUID.randomUUID().toString();
        this.tradeCode = userInfo.getCompany();
        this.insertUser = userInfo.getUserNo();
        this.insertUserName = userInfo.getUserName();
        this.insertTime = new Date();
        return this;
    }
}
