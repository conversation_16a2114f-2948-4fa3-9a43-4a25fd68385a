package com.dcjet.cs.ocrrecord.model;

import com.dcjet.cs.ocrInvoice.model.ocr.LvOcrFPData;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-4-26
 */
@Setter
@Getter
@Table(name = "t_gw_lv_ocr_record_list")
public class GwLvOcrRecordList implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * record表对应sid
     */
    @Column(name = "head_id")
    private String headId;
    /**
     * shipment ID
     */
    @Column(name = "shipment_id")
    private String shipmentId;
    @Column(name = "shipment_id_convert")
    private String shipmentIdConvert;
    /**
     * 品牌
     */
    @Column(name = "brand_code")
    private String brandCode;
    @Column(name = "brand_name")
    private String brandName;
    @Transient
    private String brandOcr;
    @Transient
    private String brandAbbr;
    @Column(name = "brand_folder")
    private String brandFolder;
    /**
     * 发票号码
     */
    @Column(name = "invoice_no")
    private String invoiceNo;
    /**
     * 执行状态(00所有异常、10识别异常、20pdf中发票号相同、30发票号已存在、40未知异常、50OCR识别成功、60OCR识别中)
     */
    @Column(name = "status")
    private String status;
    /**
     * 调用OCR上传接口成功后返回的信息(JSON存储)
     */
    @Column(name = "upload_res")
    private String uploadRes;
    /**
     * 解析失败的原因
     */
    @Column(name = "error_message")
    private String errorMessage;
    /**
     * 原始路径
     */
    @Column(name = "original_path")
    private String originalPath;
    /**
     * 备份路径
     */
    @Column(name = "bak_path")
    private String bakPath;
    @Column(name = "upload_path")
    private String uploadPath;
    /**
     * 企业编码
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 创建人名称
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 创建时间-开始
     */
    @Transient
    private String insertTimeFrom;
    /**
     * 创建时间-结束
     */
    @Transient
    private String insertTimeTo;
    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 修改时间-开始
     */
    @Transient
    private String updateTimeFrom;
    /**
     * 修改时间-结束
     */
    @Transient
    private String updateTimeTo;
    @Transient
    private String fileName;

    public GwLvOcrRecordList initData(GwLvOcrRecord record) {
        this.sid = UUID.randomUUID().toString();
        this.headId = record.getSid();
        this.shipmentId = record.getShipmentId();
        this.shipmentIdConvert = record.getShipmentIdConvert();
        this.brandCode = record.getBrandCode();
        this.brandName = record.getBrandName();
        this.brandOcr = record.getBrandOcr();
        this.tradeCode = record.getTradeCode();
        this.insertUser = record.getInsertUser();
        this.insertUserName = record.getInsertUserName();
        this.insertTime = record.getInsertTime();

        return this;
    }

    public GwLvOcrRecordList initData(LvOcrFPData lvOcrFPData) {
        this.sid = lvOcrFPData.getRecordListId();
        this.shipmentId = lvOcrFPData.getShipmentId();
        this.shipmentIdConvert = lvOcrFPData.getShipmentIdConvert();
        this.brandCode = lvOcrFPData.getBrandCode();
        this.brandName = lvOcrFPData.getBrandName();
        this.brandOcr = lvOcrFPData.getBrandOcr();
        this.tradeCode = lvOcrFPData.getTradeCode();
        this.insertUser = lvOcrFPData.getInsertUser();
        this.insertUserName = lvOcrFPData.getInsertUserName();
        this.insertTime = lvOcrFPData.getInsertTime();
        this.originalPath = lvOcrFPData.getOriginalPath();

        return this;
    }
}
