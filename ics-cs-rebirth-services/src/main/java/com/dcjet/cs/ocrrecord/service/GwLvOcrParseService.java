package com.dcjet.cs.ocrrecord.service;

import com.dcjet.cs.dto.ocrrecord.GwLvOcrParseDto;
import com.dcjet.cs.dto.ocrrecord.GwLvOcrParseParam;
import com.dcjet.cs.ocrrecord.dao.GwLvOcrParseMapper;
import com.dcjet.cs.ocrrecord.mapper.GwLvOcrParseDtoMapper;
import com.dcjet.cs.ocrrecord.model.GwLvOcrParse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-4-25
 */
@Service
public class GwLvOcrParseService extends BaseService<GwLvOcrParse> {
    @Resource
    private GwLvOcrParseMapper gwLvOcrParseMapper;
    @Resource
    private GwLvOcrParseDtoMapper gwLvOcrParseDtoMapper;

    @Override
    public Mapper<GwLvOcrParse> getMapper() {
        return gwLvOcrParseMapper;
    }

    /**
     * 获取分页信息
     *
     * @param gwLvOcrParseParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwLvOcrParseDto>> getListPaged(GwLvOcrParseParam gwLvOcrParseParam, PageParam pageParam) {
        // 启用分页查询
        GwLvOcrParse gwLvOcrParse = gwLvOcrParseDtoMapper.toPo(gwLvOcrParseParam);
        Page<GwLvOcrParse> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwLvOcrParseMapper.getList(gwLvOcrParse));
        List<GwLvOcrParseDto> gwLvOcrParseDtos = page.getResult().stream().map(head -> {
            GwLvOcrParseDto dto = gwLvOcrParseDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwLvOcrParseDto>> paged = ResultObject.createInstance(gwLvOcrParseDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param gwLvOcrParseParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwLvOcrParseDto insert(GwLvOcrParseParam gwLvOcrParseParam, UserInfoToken userInfo) {
        GwLvOcrParse gwLvOcrParse = gwLvOcrParseDtoMapper.toPo(gwLvOcrParseParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwLvOcrParse.setSid(sid);
        gwLvOcrParse.setInsertUser(userInfo.getUserNo());
        gwLvOcrParse.setInsertTime(new Date());
        // 新增数据
        int insertStatus = gwLvOcrParseMapper.insert(gwLvOcrParse);
        return insertStatus > 0 ? gwLvOcrParseDtoMapper.toDto(gwLvOcrParse) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwLvOcrParseParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwLvOcrParseDto update(GwLvOcrParseParam gwLvOcrParseParam, UserInfoToken userInfo) {
        GwLvOcrParse gwLvOcrParse = gwLvOcrParseMapper.selectByPrimaryKey(gwLvOcrParseParam.getSid());
        gwLvOcrParseDtoMapper.updatePo(gwLvOcrParseParam, gwLvOcrParse);
        gwLvOcrParse.setUpdateUser(userInfo.getUserNo());
        gwLvOcrParse.setUpdateTime(new Date());
        // 更新数据
        int update = gwLvOcrParseMapper.updateByPrimaryKey(gwLvOcrParse);
        return update > 0 ? gwLvOcrParseDtoMapper.toDto(gwLvOcrParse) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        gwLvOcrParseMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwLvOcrParseDto> selectAll(GwLvOcrParseParam exportParam, UserInfoToken userInfo) {
        GwLvOcrParse gwLvOcrParse = gwLvOcrParseDtoMapper.toPo(exportParam);
        // gwLvOcrParse.setTradeCode(userInfo.getCompany());
        List<GwLvOcrParseDto> gwLvOcrParseDtos = new ArrayList<>();
        List<GwLvOcrParse> gwLvOcrParses = gwLvOcrParseMapper.getList(gwLvOcrParse);
        if (CollectionUtils.isNotEmpty(gwLvOcrParses)) {
            gwLvOcrParseDtos = gwLvOcrParses.stream().map(head -> {
                GwLvOcrParseDto dto = gwLvOcrParseDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwLvOcrParseDtos;
    }
}
