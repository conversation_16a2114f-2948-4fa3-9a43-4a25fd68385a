package com.dcjet.cs.ocrrecord.dao;

import com.dcjet.cs.dto.ocrrecord.GwLvOcrRecordDto;
import com.dcjet.cs.dto.ocrrecord.GwLvOcrRecordParam;
import com.dcjet.cs.ocrrecord.model.GwLvOcrRecord;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * GwLvOcrRecord
 *
 * <AUTHOR>
 * @date: 2022-4-25
 */
public interface GwLvOcrRecordMapper extends Mapper<GwLvOcrRecord> {
    /**
     * 查询获取数据
     *
     * @param gwLvOcrRecord
     * @return
     */
    List<GwLvOcrRecord> getList(GwLvOcrRecord gwLvOcrRecord);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    List<GwLvOcrRecordDto> getRecordList(GwLvOcrRecordParam gwLvOcrRecord);

    int updateByLockMark(GwLvOcrRecord record);

    int updateRecordStatusBySid(@Param("sid") String sid);

    int updateParseErrorMsg(@Param("sid") String sid, @Param("msg") String msg);
}
