package com.dcjet.cs.ocrrecord.dao;

import com.dcjet.cs.ocrrecord.model.GwLvOcrInvoiceHeadLoc;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * GwLvOcrInvoiceHeadLoc
 *
 * <AUTHOR>
 * @date: 2022-4-27
 */
public interface GwLvOcrInvoiceHeadLocMapper extends Mapper<GwLvOcrInvoiceHeadLoc> {
    /**
     * 查询获取数据
     *
     * @param gwLvOcrInvoiceHeadLoc
     * @return
     */
    List<GwLvOcrInvoiceHeadLoc> getList(GwLvOcrInvoiceHeadLoc gwLvOcrInvoiceHeadLoc);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
