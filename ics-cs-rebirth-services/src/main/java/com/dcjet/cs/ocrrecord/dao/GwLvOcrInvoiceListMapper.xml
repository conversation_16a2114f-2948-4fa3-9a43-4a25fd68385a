<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.ocrrecord.dao.GwLvOcrInvoiceListMapper">
    <resultMap id="gwLvOcrInvoiceListResultMap" type="com.dcjet.cs.ocrrecord.model.GwLvOcrInvoiceList">
        <id column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
        <result column="qty" property="qty" jdbcType="VARCHAR"/>
        <result column="dec_price" property="decPrice" jdbcType="VARCHAR"/>
        <result column="dec_total" property="decTotal" jdbcType="VARCHAR"/>
        <result column="qty_convert" property="qtyConvert" jdbcType="NUMERIC"/>
        <result column="dec_price_convert" property="decPriceConvert" jdbcType="NUMERIC"/>
        <result column="dec_total_convert" property="decTotalConvert" jdbcType="NUMERIC"/>
        <result column="good_desc" property="goodDesc" jdbcType="VARCHAR"/>
        <result column="fac_g_no" property="facGNo" jdbcType="VARCHAR"/>
        <result column="origin_country" property="originCountry" jdbcType="VARCHAR"/>
        <result column="origin_country_convert" property="originCountryConvert" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="unit_convert" property="unitConvert" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
      t.sid
     ,t.head_id
     ,t.qty_convert
     ,t.dec_price_convert
     ,t.dec_total_convert
     ,t.qty
     ,t.dec_price
     ,t.dec_total
     ,t.good_desc
     ,t.fac_g_no
     ,t.origin_country
     ,t.origin_country_convert
     ,t.unit
     ,t.unit_convert
     ,t.trade_code
     ,t.insert_user
     ,t.insert_user_name
     ,t.insert_time
     ,t.update_user
     ,t.update_user_name
     ,t.update_time
    </sql>
    <sql id="condition">
        <if test="headId != null and headId != ''">
            t.head_id = #{headId}
        </if>
        <if test="sid != null and sid != ''">
            and t.sid = #{sid}
        </if>
        <if test="qty != null and qty != ''">
            and t.qty = #{qty}
        </if>
        <if test="decPrice != null and decPrice != ''">
            and t.dec_price = #{decPrice}
        </if>
        <if test="decTotal != null and decTotal != ''">
            and t.dec_total = #{decTotal}
        </if>
        <if test="goodDesc != null and goodDesc != ''">
            and t.good_desc = #{goodDesc}
        </if>
        <if test="facGNo != null and facGNo != ''">
            and t.fac_g_no = #{facGNo}
        </if>
        <if test="originCountry != null and originCountry != ''">
            and t.origin_country = #{originCountry}
        </if>
        <if test="originCountryConvert != null and originCountryConvert != ''">
            and t.origin_country_convert = #{originCountryConvert}
        </if>
        <if test="unit != null and unit != ''">
            and t.unit = #{unit}
        </if>
        <if test="unitConvert != null and unitConvert != ''">
            and t.unit_convert = #{unitConvert}
        </if>
        <if test="tradeCode != null and tradeCode != ''">
            and t.trade_code = #{tradeCode}
        </if>
        <if test="insertUser != null and insertUser != ''">
            and t.insert_user = #{insertUser}
        </if>
        <if test="insertUserName != null and insertUserName != ''">
            and t.insert_user_name = #{insertUserName}
        </if>
        <if test="_databaseId == 'oracle' and insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and t.insert_time >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and t.insert_time < to_date(#{insertTimeTo}, 'yyyy-MM-dd hh24:mi:ss') + 1 ]]>
        </if>
        <if test="_databaseId == 'postgresql' and insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and t.insert_time >= to_timestamp(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="_databaseId == 'postgresql' and insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and t.insert_time < to_timestamp(#{insertTimeTo}, 'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day' ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultType="com.dcjet.cs.dto.ocrrecord.GwLvOcrInvoiceListDto"
            parameterType="com.dcjet.cs.ocrrecord.model.GwLvOcrInvoiceList">
        select
        <include refid="Base_Column_List"/>
        , loc.qty_loc
        , loc.dec_price_loc
        , loc.dec_total_loc
        , loc.good_desc_loc
        , loc.fac_g_no_loc
        , loc.origin_country_loc
        , loc.unit_loc
        from t_gw_lv_ocr_invoice_list t
        inner join t_gw_lv_ocr_invoice_list_loc loc on loc.sid = t.sid
        <where>
            <!-- 用户Grid查询 and 条件-->
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_gw_lv_ocr_invoice_list t
        where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete from t_gw_lv_ocr_invoice_list t
        where
        t.head_id
        in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="getListNumByHeadIds" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from t_gw_lv_ocr_invoice_list t where t.head_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
