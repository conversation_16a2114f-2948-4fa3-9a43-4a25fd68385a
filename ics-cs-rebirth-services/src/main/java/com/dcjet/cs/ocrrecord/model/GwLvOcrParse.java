package com.dcjet.cs.ocrrecord.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-4-25
 */
@Setter
@Getter
@Table(name = "t_gw_lv_ocr_parse")
public class GwLvOcrParse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * record表对应sid
     */
    @Column(name = "record_list_id")
    private String recordListId;
    /**
     * OCR返回的信息
     */
    @Column(name = "receive_data")
    private String receiveData;
    /**
     * 企业编码
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 创建人
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 创建人名称
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 修改人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;

    public GwLvOcrParse initData(GwLvOcrRecordList recordList) {
        this.sid = UUID.randomUUID().toString();
        this.recordListId = recordList.getSid();
        this.tradeCode = recordList.getTradeCode();
        this.insertUser = recordList.getInsertUser();
        this.insertUserName = recordList.getInsertUserName();
        this.insertTime = new Date();

        return this;
    }
}
