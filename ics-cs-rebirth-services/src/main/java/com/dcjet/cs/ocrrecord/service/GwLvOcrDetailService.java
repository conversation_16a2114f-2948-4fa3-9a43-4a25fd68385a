package com.dcjet.cs.ocrrecord.service;

import com.dcjet.cs.dto.ocrrecord.GwLvOcrDetailDto;
import com.dcjet.cs.dto.ocrrecord.GwLvOcrDetailParam;
import com.dcjet.cs.ocrrecord.dao.GwLvOcrDetailMapper;
import com.dcjet.cs.ocrrecord.mapper.GwLvOcrDetailDtoMapper;
import com.dcjet.cs.ocrrecord.model.GwLvOcrDetail;
import com.dcjet.cs.util.ConstantsStatus;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-4-27
 */
@Service
public class GwLvOcrDetailService extends BaseService<GwLvOcrDetail> {
    @Resource
    private GwLvOcrDetailMapper gwLvOcrDetailMapper;
    @Resource
    private GwLvOcrDetailDtoMapper gwLvOcrDetailDtoMapper;

    @Override
    public Mapper<GwLvOcrDetail> getMapper() {
        return gwLvOcrDetailMapper;
    }

    /**
     * 获取分页信息
     *
     * @param gwLvOcrDetailParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<GwLvOcrDetailDto>> getListPaged(GwLvOcrDetailParam gwLvOcrDetailParam, PageParam pageParam) {
        // 启用分页查询
        GwLvOcrDetail gwLvOcrDetail = gwLvOcrDetailDtoMapper.toPo(gwLvOcrDetailParam);
        Page<GwLvOcrDetail> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwLvOcrDetailMapper.getList(gwLvOcrDetail));
        List<GwLvOcrDetailDto> gwLvOcrDetailDtos = page.getResult().stream().map(head -> {
            GwLvOcrDetailDto dto = gwLvOcrDetailDtoMapper.toDto(head);
            if ("发票类型".equals(dto.getFieldName())) {
                dto.setAxValue(ConstantsStatus.STATUS_1.endsWith(dto.getAxValue()) ? "CREDIT NOTE" : "INVOICE");
                dto.setOcrValue(ConstantsStatus.STATUS_1.endsWith(dto.getOcrValue()) ? "CREDIT NOTE" : "INVOICE");
            }
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwLvOcrDetailDto>> paged = ResultObject.createInstance(gwLvOcrDetailDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param gwLvOcrDetailParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwLvOcrDetailDto insert(GwLvOcrDetailParam gwLvOcrDetailParam, UserInfoToken userInfo) {
        GwLvOcrDetail gwLvOcrDetail = gwLvOcrDetailDtoMapper.toPo(gwLvOcrDetailParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwLvOcrDetail.setSid(sid);
        gwLvOcrDetail.setInsertUser(userInfo.getUserNo());
        gwLvOcrDetail.setInsertTime(new Date());
        // 新增数据
        int insertStatus = gwLvOcrDetailMapper.insert(gwLvOcrDetail);
        return insertStatus > 0 ? gwLvOcrDetailDtoMapper.toDto(gwLvOcrDetail) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param gwLvOcrDetailParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwLvOcrDetailDto update(GwLvOcrDetailParam gwLvOcrDetailParam, UserInfoToken userInfo) {
        GwLvOcrDetail gwLvOcrDetail = gwLvOcrDetailMapper.selectByPrimaryKey(gwLvOcrDetailParam.getSid());
        gwLvOcrDetailDtoMapper.updatePo(gwLvOcrDetailParam, gwLvOcrDetail);
        gwLvOcrDetail.setUpdateUser(userInfo.getUserNo());
        gwLvOcrDetail.setUpdateTime(new Date());
        // 更新数据
        int update = gwLvOcrDetailMapper.updateByPrimaryKey(gwLvOcrDetail);
        return update > 0 ? gwLvOcrDetailDtoMapper.toDto(gwLvOcrDetail) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        gwLvOcrDetailMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwLvOcrDetailDto> selectAll(GwLvOcrDetailParam exportParam, UserInfoToken userInfo) {
        GwLvOcrDetail gwLvOcrDetail = gwLvOcrDetailDtoMapper.toPo(exportParam);
        // gwLvOcrDetail.setTradeCode(userInfo.getCompany());
        List<GwLvOcrDetailDto> gwLvOcrDetailDtos = new ArrayList<>();
        List<GwLvOcrDetail> gwLvOcrDetails = gwLvOcrDetailMapper.getList(gwLvOcrDetail);
        if (CollectionUtils.isNotEmpty(gwLvOcrDetails)) {
            gwLvOcrDetailDtos = gwLvOcrDetails.stream().map(head -> {
                GwLvOcrDetailDto dto = gwLvOcrDetailDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwLvOcrDetailDtos;
    }
}
