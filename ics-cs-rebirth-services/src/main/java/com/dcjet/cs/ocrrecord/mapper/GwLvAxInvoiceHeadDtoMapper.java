package com.dcjet.cs.ocrrecord.mapper;

import com.dcjet.cs.dto.ocrrecord.GwLvAxInvoiceHeadDto;
import com.dcjet.cs.ocrrecord.model.GwLvAxInvoiceHead;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-5-12
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwLvAxInvoiceHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwLvAxInvoiceHeadDto toDto(GwLvAxInvoiceHead po);

    GwLvAxInvoiceHead toModel(GwLvAxInvoiceHeadDto po);
//    /***
//     * 转换DTO到数据库对象
//     * @param param
//     * @return
//     */
//    GwLvAxInvoiceHead toPo(GwLvAxInvoiceHeadParam param);
//    /**
//     * 数据库原始数据更新
//     * @param gwLvAxInvoiceHeadParam
//     * @param gwLvAxInvoiceHead
//     */
//    void updatePo(GwLvAxInvoiceHeadParam gwLvAxInvoiceHeadParam, @MappingTarget GwLvAxInvoiceHead gwLvAxInvoiceHead);
//    default void patchPo(GwLvAxInvoiceHeadParam gwLvAxInvoiceHeadParam, GwLvAxInvoiceHead gwLvAxInvoiceHead) {
//        // TODO 自行实现局部更新
//    }
}
