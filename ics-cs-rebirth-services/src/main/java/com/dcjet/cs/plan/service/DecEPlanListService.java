package com.dcjet.cs.plan.service;

import com.dcjet.cs.dto.plan.DecEPlanListDto;
import com.dcjet.cs.dto.plan.DecEPlanListParam;
import com.dcjet.cs.erp.dao.sap.ErpSapDecEPlanListMapper;
import com.dcjet.cs.plan.dao.DecEPlanListMapper;
import com.dcjet.cs.plan.mapper.DecEPlanListDtoMapper;
import com.dcjet.cs.plan.model.DecEPlanList;
import com.dcjet.cs.util.ConstantsStatus;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-9-5
 */
@Service
public class DecEPlanListService extends BaseService<DecEPlanList> {
    @Resource
    private DecEPlanListMapper decEPlanListMapper;
    @Resource
    private DecEPlanListDtoMapper decEPlanListDtoMapper;
    @Override
    public Mapper<DecEPlanList> getMapper() {
        return decEPlanListMapper;
    }
    @Resource
    private ErpSapDecEPlanListMapper erpSapDecEPlanListMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param decEPlanListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<DecEPlanListDto>> getListPaged(DecEPlanListParam decEPlanListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        DecEPlanList decEPlanList = decEPlanListDtoMapper.toPo(decEPlanListParam);
        decEPlanList.setTradeCode(userInfo.getCompany());
        Page<DecEPlanList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decEPlanListMapper.getList(decEPlanList));
        List<DecEPlanListDto> decEPlanListDtos = page.getResult().stream().map(head -> {
            DecEPlanListDto dto = decEPlanListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<DecEPlanListDto>> paged = ResultObject.createInstance(decEPlanListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param decEPlanListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecEPlanListDto insert(DecEPlanListParam decEPlanListParam, UserInfoToken userInfo) {
        // to do校验主键唯一性
        DecEPlanList decEPlanList = decEPlanListDtoMapper.toPo(decEPlanListParam);
        /**
         * 规范固定字段
         */
        decEPlanList.preInsert(userInfo);
        decEPlanList.setDataSource(ConstantsStatus.STATUS_1);
        // 新增数据
        int insertStatus = decEPlanListMapper.insert(decEPlanList);
        return  insertStatus > 0 ? decEPlanListDtoMapper.toDto(decEPlanList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param decEPlanListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecEPlanListDto update(DecEPlanListParam decEPlanListParam, UserInfoToken userInfo) {
        DecEPlanList decEPlanList = decEPlanListMapper.selectByPrimaryKey(decEPlanListParam.getSid());
        // to do校验主键唯一性
        decEPlanListDtoMapper.updatePo(decEPlanListParam, decEPlanList);
        decEPlanList.preUpdate(userInfo);
        // 更新数据
        int update = decEPlanListMapper.updateByPrimaryKey(decEPlanList);
        return update > 0 ? decEPlanListDtoMapper.toDto(decEPlanList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> sids, UserInfoToken userInfo) {
        // 更新erp数据状态
        erpSapDecEPlanListMapper.updateStatus(sids, userInfo.getCompany());
        // 删除表体
        decEPlanListMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecEPlanListDto> selectAll(DecEPlanListParam exportParam, UserInfoToken userInfo) {
        DecEPlanList decEPlanList = decEPlanListDtoMapper.toPo(exportParam);
        decEPlanList.setTradeCode(userInfo.getCompany());
        List<DecEPlanListDto> decEPlanListDtos = new ArrayList<>();
        List<DecEPlanList> decEPlanLists = decEPlanListMapper.getList(decEPlanList);
        if (CollectionUtils.isNotEmpty(decEPlanLists)) {
            decEPlanListDtos = decEPlanLists.stream().map(head -> {
                DecEPlanListDto dto = decEPlanListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decEPlanListDtos;
    }
}
