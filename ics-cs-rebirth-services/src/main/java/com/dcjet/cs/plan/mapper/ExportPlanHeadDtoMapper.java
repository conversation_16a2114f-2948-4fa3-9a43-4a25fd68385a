package com.dcjet.cs.plan.mapper;
import com.dcjet.cs.dto.plan.*;
import com.dcjet.cs.plan.model.ExportPlanHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-7-27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExportPlanHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    ExportPlanHeadDto toDto(ExportPlanHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    ExportPlanHead toPo(ExportPlanHeadParam param);
    /**
     * 数据库原始数据更新
     * @param exportPlanHeadParam
     * @param exportPlanHead
     */
    void updatePo(ExportPlanHeadParam exportPlanHeadParam, @MappingTarget ExportPlanHead exportPlanHead);
    default void patchPo(ExportPlanHeadParam exportPlanHeadParam, ExportPlanHead exportPlanHead) {
        // TODO 自行实现局部更新
    }
}
