package com.dcjet.cs.plan.service;

import com.dcjet.cs.dto.mat.MatPalletDto;
import com.dcjet.cs.dto.plan.ExportPlanPackingDto;
import com.dcjet.cs.dto.plan.ExportPlanPackingParam;
import com.dcjet.cs.mat.dao.MatPalletMapper;
import com.dcjet.cs.plan.dao.ExportPlanHeadMapper;
import com.dcjet.cs.plan.dao.ExportPlanListMapper;
import com.dcjet.cs.plan.dao.ExportPlanPackingMapper;
import com.dcjet.cs.plan.mapper.ExportPlanPackingDtoMapper;
import com.dcjet.cs.plan.model.ExportPlanHead;
import com.dcjet.cs.plan.model.ExportPlanList;
import com.dcjet.cs.plan.model.ExportPlanPacking;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-7-27
 */
@Service
public class ExportPlanPackingService extends BaseService<ExportPlanPacking> {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Resource
    private ExportPlanPackingMapper exportPlanPackingMapper;
    @Resource
    private ExportPlanPackingDtoMapper exportPlanPackingDtoMapper;
    @Resource
    private ExportPlanListMapper exportPlanListMapper;
    @Resource
    private MatPalletMapper matPalletMapper;
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    @Resource
    private ExportPlanHeadMapper exportPlanHeadMapper;

    @Override
    public Mapper<ExportPlanPacking> getMapper() {
        return exportPlanPackingMapper;
    }

    /**
     * 获取分页信息
     *
     * @param exportPlanPackingParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<ExportPlanPackingDto>> getListPaged(ExportPlanPackingParam exportPlanPackingParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        exportPlanPackingParam.setTradeCode(userInfo.getCompany());
        ExportPlanPacking exportPlanPacking = exportPlanPackingDtoMapper.toPo(exportPlanPackingParam);
        Page<ExportPlanPacking> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> exportPlanPackingMapper.getList(exportPlanPacking));
        List<ExportPlanPackingDto> exportPlanPackingDtos = page.getResult().stream().map(head -> {
            ExportPlanPackingDto dto = exportPlanPackingDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<ExportPlanPackingDto>> paged = ResultObject.createInstance(exportPlanPackingDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param exportPlanPackingParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ExportPlanPackingDto insert(ExportPlanPackingParam exportPlanPackingParam, UserInfoToken userInfo) {
        ExportPlanPacking exportPlanPacking = exportPlanPackingDtoMapper.toPo(exportPlanPackingParam);
        /**
         * 规范固定字段
         */
        exportPlanPacking.preInsert(userInfo);
        // 新增数据
        int insertStatus = exportPlanPackingMapper.insert(exportPlanPacking);
        return insertStatus > 0 ? exportPlanPackingDtoMapper.toDto(exportPlanPacking) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param exportPlanPackingParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ExportPlanPackingDto update(ExportPlanPackingParam exportPlanPackingParam, UserInfoToken userInfo) {
        ExportPlanPacking exportPlanPacking = exportPlanPackingMapper.selectByPrimaryKey(exportPlanPackingParam.getSid());
        exportPlanPackingDtoMapper.updatePo(exportPlanPackingParam, exportPlanPacking);
        exportPlanPacking.preUpdate(userInfo);
        // 更新数据
        int update = exportPlanPackingMapper.updateByPrimaryKey(exportPlanPacking);
        return update > 0 ? exportPlanPackingDtoMapper.toDto(exportPlanPacking) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> sids, UserInfoToken userInfo) {
        ExportPlanHead head = exportPlanHeadMapper.selectBypackingId(sids.get(0));
        if (ConstantsStatus.STATUS_3.equals(head.getStatus()) || ConstantsStatus.STATUS_4.equals(head.getStatus())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("当前状态不可删除"));
        }
        exportPlanPackingMapper.deleteBySids(sids);
        head.setStatus(ConstantsStatus.STATUS_0);
        exportPlanHeadMapper.updateByPrimaryKeySelective(head);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<ExportPlanPackingDto> selectAll(ExportPlanPackingParam exportParam, UserInfoToken userInfo) {
        ExportPlanPacking exportPlanPacking = exportPlanPackingDtoMapper.toPo(exportParam);
        exportPlanPacking.setTradeCode(userInfo.getCompany());
        List<ExportPlanPackingDto> exportPlanPackingDtos = new ArrayList<>();
        List<ExportPlanPacking> exportPlanPackings = exportPlanPackingMapper.getList(exportPlanPacking);
        if (CollectionUtils.isNotEmpty(exportPlanPackings)) {
            exportPlanPackingDtos = exportPlanPackings.stream().map(head -> {
                ExportPlanPackingDto dto = exportPlanPackingDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return exportPlanPackingDtos;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject generatePacking(String sid, UserInfoToken userInfo) {
        // 删除之前装箱数据
        exportPlanPackingMapper.deleteByHeadId(sid);
        Example example = new Example(ExportPlanList.class);
        example.createCriteria().andEqualTo("headId", sid);
        List<ExportPlanList> list = exportPlanListMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("表体没有数据，不可生成"));
        }

        List<MatPalletDto> dtos = matPalletMapper.selectPackingData(userInfo.getCompany());
        if (CollectionUtils.isEmpty(dtos)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未维护包装信息，请维护后再点击生成"));
        }

        // key: gamrk+facGNo+ClientCode
        Map<String, MatPalletDto> map = dtos.stream().collect(
                Collectors.toMap(d -> d.getGMark() + d.getFacGNo() + d.getClientCode(), Function.identity(), (oldValue, newValue) -> oldValue));

        AtomicInteger start = new AtomicInteger(1);
        List<ExportPlanPacking> packings = list.stream().map(body -> {
            ExportPlanPacking packing = new ExportPlanPacking();
            packing.preInsert(userInfo);
            packing.setHeadId(sid);
            packing.setListId(body.getSid());
            packing.setLinkedNo(body.getLinkedNo());
            packing.setFacGNo(body.getFacGNo());
            packing.setCustomerGNo(body.getCustomerGNo());
            packing.setQty(body.getQty());
            MatPalletDto palletDto = map.get(body.getGmark() + body.getFacGNo() + body.getSupplierCode());
            if (null == palletDto) {
                throw new ErrorException(400, String.format(xdoi18n.XdoI18nUtil.t("料号")+":[%s],"+xdoi18n.XdoI18nUtil.t("客户")+":[%s]，%s", body.getFacGNo(), body.getSupplierCode(),xdoi18n.XdoI18nUtil.t("未维护包装信息，请维护后再点击生成")));
            }

            try {
                packing.setWrapType(palletDto.getCartonNo());
                BigDecimal cartonQty = new BigDecimal(palletDto.getCartonQty());
                if (BigDecimal.ZERO.compareTo(cartonQty) == 0) {
                    throw new ErrorException(400, String.format(xdoi18n.XdoI18nUtil.t("料号")+":[%s],"+xdoi18n.XdoI18nUtil.t("客户")+":[%s]，%s", body.getFacGNo(), body.getSupplierCode(),xdoi18n.XdoI18nUtil.t("数量/每包装为0，请维护后再点击生成")));
                }
                BigDecimal packNum = body.getQty().divide(cartonQty, 5, BigDecimal.ROUND_HALF_UP);
                BigDecimal cartonNum = new BigDecimal(String.valueOf(Math.ceil(Double.valueOf(packNum.toString()))));
                // 箱数 =数量/每箱数量，计算结果有小数点时，按1进位保留整数显示
                packing.setCartonNum(cartonNum);
                packing.setPackingFrom(start.get());
                start.set(start.get() + Integer.valueOf(cartonNum.toString().split("\\.")[0]) - 1);
                packing.setPackingTo(start.get());
                packing.setPackingUnit(palletDto.getCartonUnit());
                // 整箱数
//            packing.setWholeCaseNum();
                // 单箱数量
                packing.setCartonQty(cartonQty);
                packing.setCartonQtyTotal(body.getQty());
                // 销售单位(申报单位)
                packing.setUnit(body.getUnitErp());
//                packing.setNetWt(palletDto.getNetWt());
//                // 净重小计
//                packing.setNetWtTotal(palletDto.getNetWt().multiply(cartonNum));
//                packing.setGrossWt(body.getGrossWt());
//                packing.setGrossWtTotal(body.getGrossWt().multiply(cartonNum));
                // 重量单位
                packing.setWeightUnit("KG");
                // 单箱-长 宽 高
                packing.setCartonL(palletDto.getCartonL());
                packing.setCartonW(palletDto.getCartonW());
                packing.setCartonH(palletDto.getCartonH());
                // 含栈板总毛重(KG)
//                packing.setAllGrossWt(packing.getGrossWtTotal().add(palletDto.getCartonWtAll()));
                // 包装栈板-长 宽 高
                packing.setPalletL(palletDto.getPalletL());
                packing.setPalletW(palletDto.getPalletW());
                packing.setPalletH(palletDto.getPalletH());
                packing.setPalletUnit("CM");
                // 箱毛重=箱数*纸箱毛重+净重
                packing.setCartonGrossWt(cartonNum.multiply(palletDto.getCartonWt()).add(palletDto.getNetWt()));
                // 箱体积=纸箱尺寸（长*宽*高）*箱数
                if (null == palletDto.getVolume() || BigDecimal.ZERO.compareTo(palletDto.getVolume()) == 0) {
                    throw new ErrorException(400, String.format(xdoi18n.XdoI18nUtil.t("料号")+":[%s],"+xdoi18n.XdoI18nUtil.t("客户")+":[%s]，%s", body.getFacGNo(), body.getSupplierCode(),xdoi18n.XdoI18nUtil.t("包装体积为空，请维护后再点击生成")));
                }
                packing.setCartonVolume(palletDto.getVolume().multiply(cartonNum));

                // 托盘数(件数)=箱数/托盘箱数，计算结果有小数点时，按1进位保留整数显示
                if (palletDto.getPalletQty() != null) {
                    BigDecimal palletNum = cartonNum.divide(palletDto.getPalletQty(), 5, BigDecimal.ROUND_HALF_UP);
                    packing.setPalletNum(new BigDecimal(String.valueOf(Math.ceil(Double.valueOf(palletNum.toString())))));
                    // 托毛重=托盘数*托盘净重+箱毛重
                    if (null != palletDto.getPalletWt()) {
                        packing.setPalletGrossWt(packing.getPalletNum().multiply(palletDto.getPalletWt()).add(packing.getCartonGrossWt()));
                    }
                }
                if (null != palletDto.getLayersNum()) {
                    // 托盘纸箱层数=箱数/每层箱数 进1取整
                    BigDecimal layersDeci = cartonNum.divide(palletDto.getLayersNum(), 5, BigDecimal.ROUND_HALF_UP);
                    int layersInt = Double.valueOf(Math.ceil(Double.valueOf(layersDeci.toString()))).intValue();
                    // 托体积=托盘尺寸（长*宽）*箱高*托盘纸箱层数
                    if (null != palletDto.getPalletL() && null != palletDto.getPalletW() && null != palletDto.getCartonH()) {
                        packing.setPalletVolume(palletDto.getPalletL().multiply(palletDto.getPalletW()).multiply(palletDto.getCartonH()).multiply(new BigDecimal(String.valueOf(layersInt))));
                    }
                }
            } catch (Exception e) {
                logger.error("装箱配置计算异常：", e);
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("请检查装装箱配置") + e.getMessage());
            }

            return packing;
        }).collect(Collectors.toList());

        bulkSqlOpt.batchInsert(packings, ExportPlanPackingMapper.class);

        ExportPlanHead head = new ExportPlanHead() {{
            setStatus(ConstantsStatus.STATUS_1);
            setSid(sid);
        }};
        exportPlanHeadMapper.updateByPrimaryKeySelective(head);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("生成成功"));
    }
}
