package com.dcjet.cs.war.mapper;

import com.dcjet.cs.dto.war.WarringEquipDto;
import com.dcjet.cs.dto.war.WarringEquipParam;
import com.dcjet.cs.war.model.WarringEquip;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WarringEquipDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    WarringEquipDto toDto(WarringEquip po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    WarringEquip toPo(WarringEquipParam param);
    /**
     * 数据库原始数据更新
     * @param warringEquipParam
     * @param warringEquip
     */
    void updatePo(WarringEquipParam warringEquipParam, @MappingTarget WarringEquip warringEquip);
    default void patchPo(WarringEquipParam warringEquipParam, WarringEquip warringEquip) {
        // TODO 自行实现局部更新
    }
}
