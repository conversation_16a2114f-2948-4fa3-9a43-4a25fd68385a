package com.dcjet.cs.war.dao;

import com.dcjet.cs.cert.model.Certificate;
import com.dcjet.cs.dto.cert.CertificateSearchParam;
import com.dcjet.cs.war.model.WarringCard;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* WarringCard
* <AUTHOR>
* @date: 2019-4-19
*/
public interface WarringCardMapper extends Mapper<WarringCard> {
    /**
     * 查询获取数据
     * @param warringCard
     * @return
     */
    List<WarringCard> getList(WarringCard warringCard);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 获取预警相关信息
     *
     * @param param
     * @return 返回结果
     */
    String getMaxNo(Map<String, Object> param);

    List<Certificate> getCertList(CertificateSearchParam certificate);

    int insertByCertSid(@Param("sids") List<String> sids,@Param("tradeCode") String tradeCode,@Param("insertUser") String insertUser,@Param("insertUserName") String insertUserName);
    int insertByCert(@Param("tradeCode") String tradeCode, @Param("insertUser") String insertUser, @Param("insertUserName") String insertUserName, @Param("documentNo") String documentNo, @Param("documentName") String documentName, @Param("validDateFrom") Date validDateFrom, @Param("validDateTo") Date validDateTo);

}
