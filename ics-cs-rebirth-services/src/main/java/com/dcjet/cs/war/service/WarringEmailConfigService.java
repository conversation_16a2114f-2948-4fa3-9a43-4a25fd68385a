package com.dcjet.cs.war.service;

import com.dcjet.cs.dto.war.WarringEmailConfigDto;
import com.dcjet.cs.dto.war.WarringEmailConfigParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.dcjet.cs.war.dao.WarringEmailConfigBodyMapper;
import com.dcjet.cs.war.dao.WarringEmailConfigMapper;
import com.dcjet.cs.war.mapper.WarringEmailConfigDtoMapper;
import com.dcjet.cs.war.model.WarringEmailConfig;
import com.dcjet.cs.war.model.WarringEmailConfigBody;
import com.dcjet.cs.war.model.WarringEquip;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-5-16
 */
@Service
public class WarringEmailConfigService extends BaseService<WarringEmailConfig> {
    @Resource
    private WarringEmailConfigMapper warringEmailConfigMapper;
    @Resource
    private WarringEmailConfigBodyMapper warringEmailConfigBodyMapper;
    @Resource
    private WarringEmailConfigDtoMapper warringEmailConfigDtoMapper;
    @Override
    public Mapper<WarringEmailConfig> getMapper() {
        return warringEmailConfigMapper;
    }
    @Resource
    private AuditLogUtil auditLogUtil;
    private final static String MODEL = xdoi18n.XdoI18nUtil.t("风险预警-预警通知人设置");
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param warringEmailConfigParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<WarringEmailConfigDto>> getListPaged(WarringEmailConfigParam warringEmailConfigParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        WarringEmailConfig warringEmailConfig = warringEmailConfigDtoMapper.toPo(warringEmailConfigParam);
        warringEmailConfig.setTradeCode(userInfo.getCompany());
        Page<WarringEmailConfig> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> warringEmailConfigMapper.getList(warringEmailConfig));
        List<WarringEmailConfigDto> warringEmailConfigDtos = page.getResult().stream().map(head -> warringEmailConfigDtoMapper.toDto(head)).collect(Collectors.toList());
        return (ResultObject<List<WarringEmailConfigDto>>) ResultObject.createInstance(warringEmailConfigDtos, (int) page.getTotal(), page.getPageNum());
    }
    /**
     * 功能描述:新增
     *
     * @param warringEmailConfigParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WarringEmailConfigDto insert(WarringEmailConfigParam warringEmailConfigParam, UserInfoToken userInfo) {
        WarringEmailConfig warringEmailConfig = warringEmailConfigDtoMapper.toPo(warringEmailConfigParam);
        /*
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        warringEmailConfig.setSid(sid);
        warringEmailConfig.setEnableMark(ConstantsStatus.STATUS_1);
        warringEmailConfig.setWarringLimitMark(ConstantsStatus.STATUS_1);
        warringEmailConfig.setInsertUser(userInfo.getUserNo());
        warringEmailConfig.setInsertTime(new Date());
        warringEmailConfig.setTradeCode(userInfo.getCompany());
        //循环插入表体
        for (String warringTYpe:warringEmailConfigParam.getWarringTypes()) {
            WarringEmailConfigBody warringEmailConfigBody=new WarringEmailConfigBody();
            warringEmailConfigBody.setSid(UUID.randomUUID().toString());
            warringEmailConfigBody.setHeadId(sid);
            warringEmailConfigBody.setWarringType(warringTYpe);
            warringEmailConfigBody.setInsertUser(userInfo.getUserNo());
            warringEmailConfigBody.setInsertTime(new Date());
            warringEmailConfigBody.setTradeCode(userInfo.getCompany());
            warringEmailConfigBody.setWarringId(CommonEnum.WarringEnum.getValue(warringTYpe));
            warringEmailConfigBodyMapper.insert(warringEmailConfigBody);
        }
        // 新增数据
        int insertStatus = warringEmailConfigMapper.insert(warringEmailConfig);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(),MODEL,userInfo,warringEmailConfig);
        return  insertStatus > 0 ? warringEmailConfigDtoMapper.toDto(warringEmailConfig) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param warringEmailConfigParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WarringEmailConfigDto update(WarringEmailConfigParam warringEmailConfigParam, UserInfoToken userInfo) {
        WarringEmailConfig warringEmailConfig = warringEmailConfigMapper.selectByPrimaryKey(warringEmailConfigParam.getSid());
        warringEmailConfigDtoMapper.updatePo(warringEmailConfigParam, warringEmailConfig);
        warringEmailConfig.setUpdateUser(userInfo.getUserNo());
        warringEmailConfig.setUpdateUserName(userInfo.getUserName());
        warringEmailConfig.setUpdateTime(new Date());
        //删除已有表体
        warringEmailConfigBodyMapper.deleteByHeadId(warringEmailConfig.getSid());
        //循环插入表体
        for (String warringTYpe:warringEmailConfigParam.getWarringTypes()) {
            WarringEmailConfigBody warringEmailConfigBody=new WarringEmailConfigBody();
            warringEmailConfigBody.setSid(UUID.randomUUID().toString());
            warringEmailConfigBody.setHeadId(warringEmailConfig.getSid());
            warringEmailConfigBody.setWarringType(warringTYpe);
            warringEmailConfigBody.setInsertUser(userInfo.getUserNo());
            warringEmailConfigBody.setInsertTime(new Date());
            warringEmailConfigBody.setTradeCode(userInfo.getCompany());
            warringEmailConfigBody.setWarringId(CommonEnum.WarringEnum.getValue(warringTYpe));
            warringEmailConfigBodyMapper.insert(warringEmailConfigBody);
        }
        // 更新数据
        int update = warringEmailConfigMapper.updateByPrimaryKey(warringEmailConfig);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(),MODEL,userInfo,warringEmailConfig);
        return update > 0 ? warringEmailConfigDtoMapper.toDto(warringEmailConfig) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        Example exampleList = new Example(WarringEquip.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andIn("sid", sids);
        List<WarringEmailConfig> warringEmailConfigList = warringEmailConfigMapper.selectByExample(exampleList);
		warringEmailConfigMapper.deleteBySids(sids);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.DELETE.getValue(),MODEL,userInfo,warringEmailConfigList);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<WarringEmailConfigDto> selectAll(WarringEmailConfigParam exportParam, UserInfoToken userInfo) {
        WarringEmailConfig warringEmailConfig = warringEmailConfigDtoMapper.toPo(exportParam);
        warringEmailConfig.setTradeCode(userInfo.getCompany());
        List<WarringEmailConfigDto> warringEmailConfigDtos = new ArrayList<>();
        List<WarringEmailConfig> warringEmailConfigs = warringEmailConfigMapper.getList(warringEmailConfig);
        if (CollectionUtils.isNotEmpty(warringEmailConfigs)) {
            warringEmailConfigDtos = warringEmailConfigs.stream().map(head -> warringEmailConfigDtoMapper.toDto(head)).collect(Collectors.toList());
        }
        return warringEmailConfigDtos;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param param
     * @param userInfo
     * @return
     */
    public List<WarringEmailConfigDto> selectAllHead(WarringEmailConfigParam param, UserInfoToken userInfo) {
        WarringEmailConfig warringEmailConfig = warringEmailConfigDtoMapper.toPo(param);
        warringEmailConfig.setTradeCode(userInfo.getCompany());
        List<WarringEmailConfigDto> warringEmailConfigDtos = new ArrayList<>();
        List<WarringEmailConfig> warringEmailConfigs = warringEmailConfigMapper.getHeadList(warringEmailConfig);
        if (CollectionUtils.isNotEmpty(warringEmailConfigs)) {
            warringEmailConfigDtos = warringEmailConfigs.stream().map(head -> warringEmailConfigDtoMapper.toDto(head)).collect(Collectors.toList());
        }
        return warringEmailConfigDtos;
    }
    /**
     * 功能描述:根据邮箱获取信息
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/5/27
     * @param:  strEmail 邮箱
     * @param:  userInfo 用户信息
     * @return:
     */
    public WarringEmailConfigDto selectByEmail(String strEmail, UserInfoToken userInfo){
        WarringEmailConfigParam param=new WarringEmailConfigParam();
        param.setUserEmail(strEmail);
        List<WarringEmailConfigDto> warringEmailConfigs =  selectAllHead(param,userInfo);
        return warringEmailConfigs.size()>0?warringEmailConfigs.get(0):null;
    }
    /**
     * 功能描述:获取邮箱
     * @param:  userInfo 用户信息
     * @return:
     */
    public List<String> selectEmails(UserInfoToken userInfo) {
        List<String> emails = warringEmailConfigMapper.selectEmails(userInfo.getCompany());
        return emails;
    }
}
