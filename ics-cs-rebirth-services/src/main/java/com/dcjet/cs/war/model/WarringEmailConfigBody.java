package com.dcjet.cs.war.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-5-26
 */
@Setter
@Getter
@Table(name = "T_WARRING_EMAIL_CONFIG_BODY")
public class WarringEmailConfigBody extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
     * 预警类型
     */
	@Column(name = "WARRING_TYPE")
	private  String warringType;
	/**
     * 表头id
     */
	@Column(name = "HEAD_ID")
	private  String headId;
	/**
     * 邮件预警限制次数  （暂时以表头为准）
     */
	@Column(name = "WARRING_LIMIT_COUNT")
	private  Integer warringLimitCount;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 最后一次发送时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "LAST_SEND_TIME")
	private  Date lastSendTime;
	/**
     * 最后一次发送时间-开始
     */
	@Transient
	private String lastSendTimeFrom;
	/**
     * 最后一次发送时间-结束
     */
	@Transient
    private String lastSendTimeTo;


	@Transient
	private String warringTypeCn;

	/**
	 * 预警类型菜单ID
	 */
	@Column(name = "WARRING_ID")
	private  String warringId;
}
