package com.dcjet.cs.war.service;

import com.dcjet.cs.dto.war.WarringBailDto;
import com.dcjet.cs.dto.war.WarringBailParam;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.mat.model.Attached;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.dcjet.cs.war.dao.WarringBailMapper;
import com.dcjet.cs.war.mapper.WarringBailDtoMapper;
import com.dcjet.cs.war.model.WarringBail;
import com.dcjet.cs.war.model.WarringEquip;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Service
public class WarringBailService extends BaseService<WarringBail> {
    @Resource
    private WarringBailMapper warringBailMapper;
    @Resource
    private WarringBailDtoMapper warringBailDtoMapper;
    @Override
    public Mapper<WarringBail> getMapper() {
        return warringBailMapper;
    }
    @Resource
    private AttachedMapper attachedMapper;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource
    private AuditLogUtil auditLogUtil;
    private final static String MODEL = xdoi18n.XdoI18nUtil.t("风险预警-保金保函预警管理");
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param warringBailParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<WarringBailDto>> getListPaged(WarringBailParam warringBailParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        WarringBail warringBail = warringBailDtoMapper.toPo(warringBailParam);
        warringBail.setTradeCode(userInfo.getCompany());
        Page<WarringBail> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> warringBailMapper.getList(warringBail));
        List<WarringBailDto> warringBailDtos = page.getResult().stream().map(head -> warringBailDtoMapper.toDto(head)).collect(Collectors.toList());
        return (ResultObject<List<WarringBailDto>>) ResultObject.createInstance(warringBailDtos, (int) page.getTotal(), page.getPageNum());
    }
    /**
     * 功能描述:新增
     *
     * @param warringBailParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WarringBailDto insert(WarringBailParam warringBailParam, UserInfoToken userInfo) {
        WarringBail warringBail = warringBailDtoMapper.toPo(warringBailParam);
        /*
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        warringBail.setTradeCode(userInfo.getCompany());
        warringBail.setSid(sid);
        warringBail.setInsertUser(userInfo.getUserNo());
        warringBail.setInsertTime(new Date());
        // 新增数据
        int insertStatus = warringBailMapper.insert(warringBail);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(),MODEL,userInfo,warringBail);
        return  insertStatus > 0 ? warringBailDtoMapper.toDto(warringBail) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param warringBailParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WarringBailDto update(WarringBailParam warringBailParam, UserInfoToken userInfo) {
        WarringBail warringBail = warringBailMapper.selectByPrimaryKey(warringBailParam.getSid());
        warringBailDtoMapper.updatePo(warringBailParam, warringBail);
        warringBail.setUpdateUser(userInfo.getUserNo());
        warringBail.setUpdateUserName(userInfo.getUserName());
        warringBail.setUpdateTime(new Date());
        // 更新数据
        int update = warringBailMapper.updateByPrimaryKey(warringBail);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(),MODEL,userInfo,warringBail);
        return update > 0 ? warringBailDtoMapper.toDto(warringBail) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) throws Exception {
        // 删除附件
        Example example = new Example(Attached.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("businessSid", sids);
        List<Attached> list = attachedMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            for (Attached attached : list) {
                fileHandler.deleteFile(attached.getFileName());
                attachedMapper.deleteByPrimaryKey(attached.getSid());
            }
        }
        Example exampleList = new Example(WarringEquip.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andIn("sid", sids);
        List<WarringBail> warringBailList = warringBailMapper.selectByExample(exampleList);

		warringBailMapper.deleteBySids(sids);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.DELETE.getValue(),MODEL,userInfo,warringBailList);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<WarringBailDto> selectAll(WarringBailParam exportParam, UserInfoToken userInfo) {
        WarringBail warringBail = warringBailDtoMapper.toPo(exportParam);
        if(null!=userInfo){
            warringBail.setTradeCode(userInfo.getCompany());
        }
        List<WarringBailDto> warringBailDtos = new ArrayList<>();
        List<WarringBail> warringBails = warringBailMapper.getList(warringBail);
        if (CollectionUtils.isNotEmpty(warringBails)) {
            warringBailDtos = warringBails.stream().map(head -> warringBailDtoMapper.toDto(head)).collect(Collectors.toList());
        }
        return warringBailDtos;
    }

    /**
     * get Max SERIAL_NO
     * @return
     */
    public String getMaxNo(UserInfoToken userInfo){
        Map<String, Object> map = new HashMap<>(1);
        map.put("tradeCode",userInfo.getCompany());
        return warringBailMapper.getMaxNo(map);
    }

    /**
     * 功能描述: 注销预警
     *
     * <AUTHOR> 朱正东
     * @version :   1.0
     * @date： 2019-1-8
     * @param: model 实体(List)
     * @return:
     */
    public void setStatus(List<String> model, UserInfoToken userInfo) {

        for (String aModel : model) {
            WarringBail warringBail=new WarringBail();
            warringBail.setSid(aModel);
            warringBail.setStatus(ConstantsStatus.STATUS_1);
            warringBailMapper.updateByPrimaryKeySelective(warringBail);
        }
    }
}
