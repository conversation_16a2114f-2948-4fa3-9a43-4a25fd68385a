package com.dcjet.cs.war.service;
import com.dcjet.cs.dto.war.WarringSetDto;
import com.dcjet.cs.dto.war.WarringSetParam;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.war.dao.WarringSetMapper;
import com.dcjet.cs.war.mapper.WarringSetDtoMapper;
import com.dcjet.cs.war.model.WarringSet;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-5-16
 */
@Service
public class WarringSetService extends BaseService<WarringSet> {
    @Resource
    private WarringSetMapper warringSetMapper;
    @Resource
    private WarringSetDtoMapper warringSetDtoMapper;
    @Override
    public Mapper<WarringSet> getMapper() {
        return warringSetMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param warringSetParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<WarringSetDto>> getListPaged(WarringSetParam warringSetParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        WarringSet warringSet = warringSetDtoMapper.toPo(warringSetParam);
        warringSet.setTradeCode(userInfo.getCompany());
        Page<WarringSet> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> warringSetMapper.getList(warringSet));
        List<WarringSetDto> warringSetDtos = page.getResult().stream().map(head -> warringSetDtoMapper.toDto(head)).collect(Collectors.toList());
        return (ResultObject<List<WarringSetDto>>) ResultObject.createInstance(warringSetDtos, (int) page.getTotal(), page.getPageNum());
    }
    /**
     * 功能描述:新增
     *
     * @param warringSetParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WarringSetDto insert(WarringSetParam warringSetParam, UserInfoToken userInfo) {
        WarringSet warringSet = warringSetDtoMapper.toPo(warringSetParam);
        WarringSet warringSetDel=new WarringSet();
        warringSetDel.setTradeCode(userInfo.getCompany());
        warringSetDel.setWarringType(warringSet.getWarringType());
        warringSetDel.setBusinessId(warringSet.getBusinessId());
        warringSetMapper.deleteByParam(warringSetDel);
        /*
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        warringSet.setSid(sid);
        warringSet.setInsertUser(userInfo.getUserNo());
        warringSet.setInsertUserName(userInfo.getUserName());
        warringSet.setInsertTime(new Date());
        //默认启动
        warringSet.setEnableMark(ConstantsStatus.STATUS_1);
        warringSet.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = warringSetMapper.insert(warringSet);
        return  insertStatus > 0 ? warringSetDtoMapper.toDto(warringSet) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param warringSetParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WarringSetDto update(WarringSetParam warringSetParam, UserInfoToken userInfo) {
        WarringSet warringSet = warringSetMapper.selectByPrimaryKey(warringSetParam.getSid());
        warringSetDtoMapper.updatePo(warringSetParam, warringSet);
        warringSet.setUpdateUser(userInfo.getUserNo());
        warringSet.setUpdateTime(new Date());
        // 更新数据
        int update = warringSetMapper.updateByPrimaryKey(warringSet);
        return update > 0 ? warringSetDtoMapper.toDto(warringSet) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
		warringSetMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<WarringSetDto> selectAll(WarringSetParam exportParam, UserInfoToken userInfo) {
        WarringSet warringSet = warringSetDtoMapper.toPo(exportParam);
         warringSet.setTradeCode(userInfo.getCompany());
        List<WarringSetDto> warringSetDtos = new ArrayList<>();
        List<WarringSet> warringSets = warringSetMapper.getList(warringSet);
        if (CollectionUtils.isNotEmpty(warringSets)) {
            warringSetDtos = warringSets.stream().map(head -> warringSetDtoMapper.toDto(head)).collect(Collectors.toList());
        }
        return warringSetDtos;
    }
}
