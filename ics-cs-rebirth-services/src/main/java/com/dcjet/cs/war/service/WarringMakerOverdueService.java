package com.dcjet.cs.war.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.dto.war.WarringMakerOverdueDto;
import com.dcjet.cs.dto.war.WarringMakerOverdueParam;
import com.dcjet.cs.war.dao.WarringMakerOverdueMapper;
import com.dcjet.cs.war.dao.WarringSetMapper;
import com.dcjet.cs.war.model.WarringDataRecord;
import com.dcjet.cs.war.model.WarringSet;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WarringMakerOverdueService extends BaseService<WarringMakerOverdueDto> {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private WarringMakerOverdueMapper warringMakerOverdueMapper;
    @Resource
    private WarringSetMapper warringSetMapper;
    @Resource
    private BulkInsertConfig bulkInsertConfig;
    @Resource
    private CommonService commonService;

    public IBulkInsert getBulkInsertInstance() throws Exception {
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }

    @Override
    public Mapper<WarringMakerOverdueDto> getMapper() {
        return warringMakerOverdueMapper;
    }
    /**
     * 获取分页信息
     *
     * @param warringMakerOverdueParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<WarringMakerOverdueDto>> getListPaged(WarringMakerOverdueParam warringMakerOverdueParam, PageParam pageParam, UserInfoToken userInfo) {
        warringMakerOverdueParam.setTradeCode(userInfo.getCompany());
        Page<WarringMakerOverdueDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> warringMakerOverdueMapper.getList(warringMakerOverdueParam));
        return (ResultObject<List<WarringMakerOverdueDto>>) ResultObject.createInstance(page.getResult(), (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 获取所有数据
     *
     * @param warringMakerOverdueParam
     * @return
     * <AUTHOR>
     */
    public List<WarringMakerOverdueDto> selectAll(WarringMakerOverdueParam warringMakerOverdueParam, UserInfoToken userInfo) {
        warringMakerOverdueParam.setTradeCode(userInfo.getCompany());
        List<WarringMakerOverdueDto> makerOverdueDtos = warringMakerOverdueMapper.getList(warringMakerOverdueParam);
        convertData(makerOverdueDtos);
        return makerOverdueDtos;
    }

    public void convertData(List<WarringMakerOverdueDto> list) {
        list.forEach(e -> {
            if (StringUtils.isNotEmpty(e.getTrafMode())) {
                e.setTrafMode(commonService.convertPCode(e.getTrafMode(), PCodeType.TRANSF));
            }
        });
    }

    /**
     * 计算达到预警的进口预录入单信息
     */
    public void checkData(String companyCode) {
        log.info("**************进口制单超期预警分析任务开始**************");
        Example example = new Example(WarringSet.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("warringType", "OVERDUE");
        if (StringUtils.isNotEmpty(companyCode)) {
            criteria.andEqualTo("tradeCode", companyCode);
        }
        List<WarringSet> warringSets = warringSetMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(warringSets)) {
            logger.debug("暂无进口制单超期预警需要分析");
            log.info("暂无进口制单超期预警需要分析");
            return;
        }
        Map<String, List<WarringSet>> warringMap = warringSets.stream().collect(Collectors.groupingBy(e -> e.getTradeCode()));
        List<WarringDataRecord> warringDataRecords = new ArrayList<>();
        for (String tradeCode : warringMap.keySet()) {
            logger.info("开始处理企业【" + tradeCode + "】制单超期预警分析任务！");
            log.info("开始处理企业【" + tradeCode + "】制单超期预警分析任务！");
            warringMakerOverdueMapper.deleteByTradeCode(tradeCode);
            for (WarringSet warringSet : warringMap.get(tradeCode)) {
                WarringMakerOverdueParam warringMakerOverdueParam = new WarringMakerOverdueParam();
                if (StringUtils.isEmpty(warringSet.getBusinessId())) {
                    logger.info("进口制单超期预警参数错误！");
                    log.info("进口制单超期预警参数错误！");
                    return;
                }
                warringMakerOverdueParam.setGoodsCategory(warringSet.getBusinessId());
                warringMakerOverdueParam.setTradeCode(warringSet.getTradeCode());
                if (warringSet.getWarringMin() == null || warringSet.getWarringMax() == null) {
                    continue;
                }
                LocalDate localDate = LocalDate.now();
                DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                if (warringSet.getWarringMin() != null) {
                    LocalDate insertTimeTo = localDate.plusDays(warringSet.getWarringMin().intValue());
                    warringMakerOverdueParam.setArrivalPortDateTo(insertTimeTo.format(format));
                }
                if (warringSet.getWarringMax() != null) {
                    LocalDate insertTimeFrom = localDate.plusDays(-warringSet.getWarringMax().intValue());
                    warringMakerOverdueParam.setArrivalPortDateFrom(insertTimeFrom.format(format));
                }
                List<String> result = warringMakerOverdueMapper.checkData(warringMakerOverdueParam);
                if (CollectionUtils.isNotEmpty(result)) {
                    result.forEach(e -> {
                        WarringDataRecord warringDataRecord = new WarringDataRecord();
                        warringDataRecord.setSid(UUID.randomUUID().toString());
                        warringDataRecord.setBusinessId(e);
                        warringDataRecord.setTradeCode(warringSet.getTradeCode());
                        warringDataRecord.setWarringType("OVERDUE");
                        warringDataRecords.add(warringDataRecord);
                    });
                }
            }
            logger.info("企业【" + tradeCode + "】制单超期预警分析任务结束！");
            log.info("企业【" + tradeCode + "】制单超期预警分析任务结束！");
        }
        if (CollectionUtils.isEmpty(warringDataRecords)) {
            logger.info("暂无需要预警的进口预录入单信息！");
            log.info("暂无需要预警的进口预录入单信息！");
            return;
        }
        try {
            IBulkInsert bulkInsert = getBulkInsertInstance();
            bulkInsert.fastImport(warringDataRecords);
        } catch (BulkInsertException ex) {
            XdoImportLogger.log(ex);
        } catch (Exception ex) {
            XdoImportLogger.log(ex);
        }
        log.info("**************进口制单超期预警分析任务结束**************");
    }
}
