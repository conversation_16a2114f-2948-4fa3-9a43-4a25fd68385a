package com.dcjet.cs.war.service;
import com.dcjet.cs.cert.dao.CertificateMapper;
import com.dcjet.cs.cert.model.Certificate;
import com.dcjet.cs.dto.cert.CertificateDto;
import com.dcjet.cs.dto.cert.CertificateSearchParam;
import com.dcjet.cs.dto.war.WarringCardDto;
import com.dcjet.cs.dto.war.WarringCardParam;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.mat.model.Attached;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.dcjet.cs.war.dao.WarringCardMapper;
import com.dcjet.cs.war.mapper.WarringCardDtoMapper;
import com.dcjet.cs.war.model.WarringCard;
import com.dcjet.cs.war.model.WarringEquip;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Service
public class WarringCardService extends BaseService<WarringCard> {
    @Resource
    private WarringCardMapper warringCardMapper;
    @Resource
    private WarringCardDtoMapper warringCardDtoMapper;
    @Override
    public Mapper<WarringCard> getMapper() {
        return warringCardMapper;
    }
    @Resource
    private AttachedMapper attachedMapper;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource
    private AuditLogUtil auditLogUtil;
    @Resource
    private CertificateMapper certificateMapper;

    private final static String MODEL = xdoi18n.XdoI18nUtil.t("风险预警-证件卡类预警管理");
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param warringCardParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<WarringCardDto>> getListPaged(WarringCardParam warringCardParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        WarringCard warringCard = warringCardDtoMapper.toPo(warringCardParam);
        warringCard.setTradeCode(userInfo.getCompany());
        Page<WarringCard> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> warringCardMapper.getList(warringCard));
        List<WarringCardDto> warringCardDtos = page.getResult().stream().map(head -> warringCardDtoMapper.toDto(head)).collect(Collectors.toList());
        return (ResultObject<List<WarringCardDto>>) ResultObject.createInstance(warringCardDtos, (int) page.getTotal(), page.getPageNum());
    }
    /**
     * 功能描述:新增
     *
     * @param warringCardParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WarringCardDto insert(WarringCardParam warringCardParam, UserInfoToken userInfo) {
        WarringCard warringCard = warringCardDtoMapper.toPo(warringCardParam);
        /*
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        warringCard.setTradeCode(userInfo.getCompany());
        warringCard.setSid(sid);
        warringCard.setInsertUser(userInfo.getUserNo());
        warringCard.setInsertUserName(userInfo.getUserName());
        warringCard.setInsertTime(new Date());
        // 新增数据
        int insertStatus = warringCardMapper.insert(warringCard);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(),MODEL,userInfo,warringCard);
        return  insertStatus > 0 ? warringCardDtoMapper.toDto(warringCard) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param warringCardParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WarringCardDto update(WarringCardParam warringCardParam, UserInfoToken userInfo) {
        WarringCard warringCard = warringCardMapper.selectByPrimaryKey(warringCardParam.getSid());
        warringCardDtoMapper.updatePo(warringCardParam, warringCard);
        warringCard.setUpdateUser(userInfo.getUserNo());
        warringCard.setUpdateUserName(userInfo.getUserName());
        warringCard.setUpdateTime(new Date());
        // 更新数据
        int update = warringCardMapper.updateByPrimaryKey(warringCard);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(),MODEL,userInfo,warringCard);
        return update > 0 ? warringCardDtoMapper.toDto(warringCard) : null;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updates(List<String> sids, WarringCardParam warringCardParam, UserInfoToken userInfo) {
        for(String sid:sids)
        {
            WarringCard warringCard = warringCardMapper.selectByPrimaryKey(sid);
            warringCard.setWarringDays(warringCardParam.getWarringDays());
            warringCard.setUpdateUser(userInfo.getUserNo());
            warringCard.setUpdateUserName(userInfo.getUserName());
            warringCard.setUpdateTime(new Date());
            // 更新数据
            warringCardMapper.updateByPrimaryKey(warringCard);
        }
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) throws Exception {
        // 删除附件
        Example example = new Example(Attached.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("businessSid", sids);
        List<Attached> list = attachedMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            for (Attached attached : list) {
                fileHandler.deleteFile(attached.getFileName());
                attachedMapper.deleteByPrimaryKey(attached.getSid());
            }
        }
        Example exampleList = new Example(WarringEquip.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andIn("sid", sids);
        List<WarringCard> warringCardList = warringCardMapper.selectByExample(exampleList);
        warringCardMapper.deleteBySids(sids);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.DELETE.getValue(),MODEL,userInfo,warringCardList);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<WarringCardDto> selectAll(WarringCardParam exportParam, UserInfoToken userInfo) {
        WarringCard warringCard = warringCardDtoMapper.toPo(exportParam);
        if(null!=userInfo){
            warringCard.setTradeCode(userInfo.getCompany());
        }
        List<WarringCardDto> warringCardDtos = new ArrayList<>();
        List<WarringCard> warringCards = warringCardMapper.getList(warringCard);
        if (CollectionUtils.isNotEmpty(warringCards)) {
            warringCardDtos = warringCards.stream().map(head -> warringCardDtoMapper.toDto(head)).collect(Collectors.toList());
        }
        return warringCardDtos;
    }

    /**
     * get Max SERIAL_NO
     * @return
     */
    public String getMaxNo(UserInfoToken userInfo){
        Map<String, Object> map = new HashMap<>(1);
        map.put("tradeCode",userInfo.getCompany());
        return warringCardMapper.getMaxNo(map);
    }

    /**
     * 功能描述: 注销预警
     *
     * <AUTHOR> 朱正东
     * @version :   1.0
     * @date： 2019-1-8
     * @param: model 实体(List)
     * @return:
     */
    public void setStatus(List<String> model, UserInfoToken userInfo) {

        for (String aModel : model) {
            WarringCard warringCard=new WarringCard();
            warringCard.setSid(aModel);
            warringCard.setStatus(ConstantsStatus.STATUS_1);
            warringCardMapper.updateByPrimaryKeySelective(warringCard);
        }
    }

    public ResultObject<List<CertificateDto>> getCertListPaged(CertificateSearchParam certificateParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        certificateParam.setTradeCode(userInfo.getCompany());
        Page<Certificate> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> warringCardMapper.getCertList(certificateParam));
        List<CertificateDto> warringCardDtos = page.getResult().stream().map(head -> warringCardDtoMapper.toCertDto(head)).collect(Collectors.toList());
        return (ResultObject<List<CertificateDto>>) ResultObject.createInstance(warringCardDtos, (int) page.getTotal(), page.getPageNum());
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject extractSelect(List<String> sids, UserInfoToken userInfo){
        WarringCard warringCard =new WarringCard();
        warringCard.setTradeCode(userInfo.getCompany());
        warringCard.setCertSid(sids);
        List<WarringCard> warringCards = warringCardMapper.getList(warringCard);
        if(warringCards.size()>0)
        {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("现有预警中已存在选中数据的证件编号！"));
        }

        ResultObject result = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("数据提取成功"));
        warringCardMapper.insertByCertSid(sids,userInfo.getCompany(),userInfo.getUserNo(),userInfo.getUserName());
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject extractTerm(CertificateSearchParam certificateSearchParam, UserInfoToken userInfo){
        certificateSearchParam.setTradeCode(userInfo.getCompany());
        List<Certificate> list= warringCardMapper.getCertList(certificateSearchParam);
        List<String> sids=list.stream().map(Certificate::getSid)
                .collect(Collectors.toList());
        WarringCard warringCard =new WarringCard();
        warringCard.setTradeCode(userInfo.getCompany());
        warringCard.setCertSid(sids);
        List<WarringCard> warringCards = warringCardMapper.getList(warringCard);
        if(warringCards.size()>0)
        {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("现有预警中已存在选中数据的证件编号！"));
        }
        ResultObject result = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("数据提取成功"));

        warringCardMapper.insertByCert(userInfo.getCompany(),userInfo.getUserNo(),userInfo.getUserName(),certificateSearchParam.getDocumentNo(),certificateSearchParam.getDocumentName(),certificateSearchParam.getValidDateFrom(),certificateSearchParam.getValidDateTo());
        return result;
    }
}
