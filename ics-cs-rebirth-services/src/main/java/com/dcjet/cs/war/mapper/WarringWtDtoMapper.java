package com.dcjet.cs.war.mapper;

import com.dcjet.cs.dto.war.WarringWtDto;
import com.dcjet.cs.dto.war.WarringWtParam;
import com.dcjet.cs.war.model.WarringWt;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WarringWtDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    WarringWtDto toDto(WarringWt po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    WarringWt toPo(WarringWtParam param);
    /**
     * 数据库原始数据更新
     * @param warringWtParam
     * @param warringWt
     */
    void updatePo(WarringWtParam warringWtParam, @MappingTarget WarringWt warringWt);
    default void patchPo(WarringWtParam warringWtParam, WarringWt warringWt) {
        // TODO 自行实现局部更新
    }
}
