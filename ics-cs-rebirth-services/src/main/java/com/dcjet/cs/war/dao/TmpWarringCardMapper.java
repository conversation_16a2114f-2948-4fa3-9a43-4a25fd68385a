package com.dcjet.cs.war.dao;

import com.dcjet.cs.war.model.TmpWarringCard;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* TmpWarringCard
* <AUTHOR>
* @date: 2019-11-4
*/
public interface TmpWarringCardMapper extends Mapper<TmpWarringCard> {

    /**
     * 根据传入的数据类型获取相应数据
     *
     * @param param
     * @return 返回结果
     */
    List<TmpWarringCard> selectByFlag(Map<String, Object> param);
}
