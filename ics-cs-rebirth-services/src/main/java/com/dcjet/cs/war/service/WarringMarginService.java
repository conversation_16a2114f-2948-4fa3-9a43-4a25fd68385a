package com.dcjet.cs.war.service;

import com.dcjet.cs.dto.war.WarringMarginDto;
import com.dcjet.cs.dto.war.WarringMarginParam;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.war.dao.WarringMarginMapper;
import com.dcjet.cs.war.mapper.WarringMarginDtoMapper;
import com.dcjet.cs.war.model.WarringMargin;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Service
public class WarringMarginService extends BaseService<WarringMargin> {
    @Resource
    private WarringMarginMapper warringMarginMapper;
    @Resource
    private WarringMarginDtoMapper warringMarginDtoMapper;

    @Override
    public Mapper<WarringMargin> getMapper() {
        return warringMarginMapper;
    }

    /**
     * 获取分页信息
     *
     * @param warringMarginParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<WarringMarginDto>> getListPaged(WarringMarginParam warringMarginParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        WarringMargin warringMargin = warringMarginDtoMapper.toPo(warringMarginParam);
        warringMargin.setTradeCode(userInfo.getCompany());
        Page<WarringMargin> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> warringMarginMapper.getList(warringMargin));
        List<WarringMarginDto> warringMarginDtos = page.getResult().stream().map(head -> warringMarginDtoMapper.toDto(head)).collect(Collectors.toList());
        return (ResultObject<List<WarringMarginDto>>) ResultObject.createInstance(warringMarginDtos, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<WarringMarginDto> selectAll(WarringMarginParam exportParam, UserInfoToken userInfo) {
        WarringMargin warringMargin = warringMarginDtoMapper.toPo(exportParam);
        if (null != userInfo) {
            warringMargin.setTradeCode(userInfo.getCompany());
        }
        List<WarringMarginDto> warringMarginDtos = new ArrayList<>();
        List<WarringMargin> warringMargins = warringMarginMapper.getList(warringMargin);
        if (CollectionUtils.isNotEmpty(warringMargins)) {
            warringMarginDtos = warringMargins.stream().map(head -> {
                WarringMarginDto dto = warringMarginDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return warringMarginDtos;
    }

    public ResultObject changeWarnStatus(WarringMarginParam warringMarginParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        if (StringUtils.isBlank(warringMarginParam.getEmsNo()) || StringUtils.isBlank(warringMarginParam.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("手册号或状态不可为空！"));
            return resultObject;
        }
        if (ConstantsStatus.STATUS_0.equals(warringMarginParam.getStatus())) {
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("恢复预警成功"));
        } else {
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("取消预警成功"));
        }
        warringMarginParam.setTradeCode(userInfo.getCompany());
        int count = warringMarginMapper.changeWarnStatus(warringMarginDtoMapper.toPo(warringMarginParam));
        if (count == 0) {
            resultObject.setSuccess(false);
            if (ConstantsStatus.STATUS_0.equals(warringMarginParam.getStatus())) {
                resultObject.setMessage(xdoi18n.XdoI18nUtil.t("恢复预警失败"));
            } else {
                resultObject.setMessage(xdoi18n.XdoI18nUtil.t("取消预警失败"));
            }
        }
        return resultObject;
    }

    public ResultObject checkRelease(List<String> sids, String status, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true,"");
        if (CollectionUtils.isNotEmpty(sids)){
            int sum = warringMarginMapper.getSelect(sids,status,userInfo.getCompany());
            if (sum>0){
                if (StringUtils.equals(ConstantsStatus.STATUS_1,status)) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("勾选数据中已存在预警数据！"));
                }else {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("勾选数据中已存在预警恢复数据！"));
                }
            }
            if (StringUtils.equals(ConstantsStatus.STATUS_1,status)) {
                result.setMessage(xdoi18n.XdoI18nUtil.t("恢复预警成功"));
            } else {
                result.setMessage(xdoi18n.XdoI18nUtil.t("取消预警成功"));
            }
            warringMarginMapper.updateCheckRelease(sids,status,userInfo.getCompany());
        }
        return result;
    }
}
