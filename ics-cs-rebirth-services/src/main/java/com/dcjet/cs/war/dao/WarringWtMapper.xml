<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.war.dao.WarringWtMapper">
    <resultMap id="warringWtResultMap" type="com.dcjet.cs.war.model.WarringWt">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="SERIAL_NO" property="serialNo" jdbcType="VARCHAR"/>
        <result column="COP_G_NO" property="copGNo" jdbcType="VARCHAR"/>
        <result column="NET_WT" property="netWt" jdbcType="VARCHAR"/>
        <result column="UNIT" property="unit" jdbcType="VARCHAR"/>
        <result column="UPPER_LIMIT" property="upperLimit" jdbcType="VARCHAR"/>
        <result column="LOWER_LIMIT" property="lowerLimit" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,SERIAL_NO
     ,COP_G_NO
     ,NET_WT
     ,UNIT
     ,UPPER_LIMIT
     ,LOWER_LIMIT
     ,NOTE
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,TRADE_CODE
     ,STATUS
    ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
    </sql>
    <sql id="condition">
        <if test="sid != null and sid != ''">
            and SID = #{sid}
        </if>
        <if test="serialNo != null and serialNo != ''">
            and SERIAL_NO = #{serialNo}
        </if>
        <if test="copGNo != null and copGNo != ''">
            and COP_G_NO = #{copGNo}
        </if>
        <if test="netWt != null and netWt != ''">
            and NET_WT = #{netWt}
        </if>
        <if test="unit != null and unit != ''">
            and UNIT = #{unit}
        </if>
        <if test="upperLimit != null and upperLimit != ''">
            and UPPER_LIMIT = #{upperLimit}
        </if>
        <if test="lowerLimit != null and lowerLimit != ''">
            and LOWER_LIMIT = #{lowerLimit}
        </if>
        <if test="note != null and note != ''">
            and NOTE = #{note}
        </if>
        and TRADE_CODE = #{tradeCode}
        <if test="status != null and status != ''">
            and STATUS = #{status}
        </if>
        <if test="insertUser != null and insertUser != ''">
            and t.INSERT_USER like concat('%', concat(#{insertUser}, '%'))
        </if>
        <if test='_databaseId == "postgresql" '>
            <if test="insertTimeFrom != null and insertTimeFrom != ''">
                <![CDATA[ and t.INSERT_TIME >= to_timestamp(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="insertTimeTo != null and insertTimeTo != ''">
                <![CDATA[ and t.INSERT_TIME < to_timestamp(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="insertTimeFrom != null and insertTimeFrom != ''">
                <![CDATA[ and t.INSERT_TIME >= to_date(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="insertTimeTo != null and insertTimeTo != ''">
                <![CDATA[ and t.INSERT_TIME < to_date(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss')+1 ]]>
            </if>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="warringWtResultMap" parameterType="com.dcjet.cs.war.model.WarringWt">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_WARRING_WT t
        <where>
            <include refid="condition"></include>
        </where>
        ORDER BY T.INSERT_TIME DESC, t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_WARRING_WT t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="getMaxNo" resultType="string" parameterType="map">
        SELECT f_xdo_nvl ( MAX ( T.SERIAL_NO ) + 1, '1' ) AS SERIAL_NO FROM T_WARRING_WT t
        <where>
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        </where>
    </select>
</mapper>
