package com.dcjet.cs.war.mapper;

import com.dcjet.cs.dto.war.WarringMarginDto;
import com.dcjet.cs.dto.war.WarringMarginParam;
import com.dcjet.cs.war.model.WarringMargin;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WarringMarginDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    WarringMarginDto toDto(WarringMargin po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    WarringMargin toPo(WarringMarginParam param);
    /**
     * 数据库原始数据更新
     * @param warringMarginParam
     * @param warringMargin
     */
    void updatePo(WarringMarginParam warringMarginParam, @MappingTarget WarringMargin warringMargin);
    default void patchPo(WarringMarginParam warringMarginParam, WarringMargin warringMargin) {
        // TODO 自行实现局部更新
    }
}
