package com.dcjet.cs.war.dao;

import com.dcjet.cs.war.model.WarringExpire;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* WarringExpire
* <AUTHOR>
* @date: 2019-4-19
*/
public interface WarringExpireMapper extends Mapper<WarringExpire> {
    /**
     * 查询获取数据
     * @param warringExpire
     * @return
     */
    List<WarringExpire> getList(WarringExpire warringExpire);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 获取预警相关信息
     *
     * @param param
     * @return 返回结果
     */
    String getMaxNo(Map<String, Object> param);
}
