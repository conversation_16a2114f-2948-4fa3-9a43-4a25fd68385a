package com.dcjet.cs.war.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_WARRING_EXPORT")
public class WarringExport extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
     * 序号
     */
	@Column(name = "SERIAL_NO")
	private java.math.BigDecimal serialNo;
	/**
     * 暂时进出口批次号
     */
	@Column(name = "DOCUMENT_NO")
	private  String documentNo;
	/**
     * 暂时进出口商品
     */
	@Column(name = "DOCUMENT_NAME")
	private  String documentName;
	/**
     * 暂时进出口复出日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "DATE_END")
	private  Date dateEnd;
	/**
     * 暂时进出口复出日期-开始
     */
	@Transient
	private String dateEndFrom;
	/**
     * 暂时进出口复出日期-结束
     */
	@Transient
    private String dateEndTo;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 状态 0 正常 1 注销
     */
	@Column(name = "STATUS")
	private  String status;
	/**
     * 所属企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;

	@Transient
	private String days;
	@Transient
	private String dataStatus;
	@Transient
	private String dataStatusName;
	/**
	 * 有效标志 1 有效 0 无效
	 */
	@Transient
	private String validMark;
}
