package com.dcjet.cs.entCompare.service;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.entCompare.EntryCompareResDto;
import com.dcjet.cs.dto.entCompare.EntryCompareResParam;
import com.dcjet.cs.entCompare.dao.EntryCompareDetailMapper;
import com.dcjet.cs.entCompare.dao.EntryCompareResMapper;
import com.dcjet.cs.entCompare.mapper.EntryCompareResDtoMapper;
import com.dcjet.cs.entCompare.model.Compare;
import com.dcjet.cs.entCompare.model.EntryCompareDetail;
import com.dcjet.cs.entCompare.model.EntryCompareRes;
import com.dcjet.cs.entry.dao.DecEEntryHeadMapper;
import com.dcjet.cs.entry.dao.DecEEntryListMapper;
import com.dcjet.cs.entry.dao.DecIEntryHeadMapper;
import com.dcjet.cs.entry.dao.DecIEntryListMapper;
import com.dcjet.cs.entry.model.DecEEntryHead;
import com.dcjet.cs.entry.model.DecEEntryList;
import com.dcjet.cs.entry.model.DecIEntryHead;
import com.dcjet.cs.entry.model.DecIEntryList;
import com.dcjet.cs.gwstd.dao.GwstdJobAllotConfigMapper;
import com.dcjet.cs.gwstd.dao.GwstdJobMapper;
import com.dcjet.cs.gwstd.model.GwstdJob;
import com.dcjet.cs.gwstd.model.GwstdJobAllotConfig;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryHeadMapper;
import com.dcjet.cs.gwstdentry.dao.GwstdEntryListMapper;
import com.dcjet.cs.gwstdentry.model.GwstdEntryHead;
import com.dcjet.cs.gwstdentry.model.GwstdEntryList;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.LoggerUtil;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-10-14
 */
@Service
public class EntryCompareResService extends BaseService<EntryCompareRes> {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    private static final String JOB_TYPE = "ENT_COMPARE";
//    private Cache<String, GwstdJobAllotConfig> gwstdJobAllotCache;

    private Map<String, String> currCode = new HashMap<>();
    private Map<String, String> countryCode = new HashMap<>();

    @Resource
    private EntryCompareResMapper entryCompareResMapper;
    @Resource
    private EntryCompareResDtoMapper entryCompareResDtoMapper;
    @Resource
    private GwstdJobMapper gwstdJobMapper;
    @Resource
    private GwstdEntryHeadMapper gwstdEntryHeadMapper;
    @Resource
    private GwstdEntryListMapper gwstdEntryListMapper;
    @Resource
    private DecEEntryHeadMapper decEEntryHeadMapper;
    @Resource
    private EntryCompareDetailMapper entryCompareDetailMapper;
    @Resource
    private DecEEntryListMapper decEEntryListMapper;
    @Resource
    private DecIEntryHeadMapper decIEntryHeadMapper;
    @Resource
    private DecIEntryListMapper decIEntryListMapper;
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    @Resource
    private DataSourceTransactionManager dataSourceTransactionManager;
    @Resource
    private TransactionDefinition transactionDefinition;
    @Resource
    private PCodeHolder pCodeHolder;
//    @Resource
//    private GwstdJobAllotMapperConfig gwstdJobAllotMapperConfig;
    @Resource
    private CommonService commonService;
    @Resource
    private GwstdJobAllotConfigMapper gwstdJobAllotConfigMapper;

    @Override
    public Mapper<EntryCompareRes> getMapper() {
        return entryCompareResMapper;
    }

    /**
     * 获取分页信息
     *
     * @param entryCompareResParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<EntryCompareResDto>> getListPaged(EntryCompareResParam entryCompareResParam, PageParam pageParam) {
        if (StringUtils.isEmpty(entryCompareResParam.getIEMark()) || !(CommonVariable.IE_MARK_I.equals(entryCompareResParam.getIEMark())
                || CommonVariable.IE_MARK_E.equals(entryCompareResParam.getIEMark()))) {
            throw new RuntimeException(xdoi18n.XdoI18nUtil.t("必须输入进出口标记参数"));
        }
        entryCompareResParam.setEntFlag(ConstantsStatus.STATUS_1);
        // 启用分页查询
        EntryCompareRes entryCompareRes = entryCompareResDtoMapper.toPo(entryCompareResParam);
        Page<EntryCompareRes> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> entryCompareResMapper.getList(entryCompareRes));
        List<EntryCompareResDto> entryCompareResDtos = page.getResult().stream().map(head -> {
            EntryCompareResDto dto = entryCompareResDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        convertForPrint(entryCompareResDtos);
        ResultObject<List<EntryCompareResDto>> paged = ResultObject.createInstance(entryCompareResDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<EntryCompareResDto> selectAll(EntryCompareResParam exportParam, UserInfoToken userInfo) {
        if (StringUtils.isEmpty(exportParam.getIEMark()) || !(CommonVariable.IE_MARK_I.equals(exportParam.getIEMark())
                || CommonVariable.IE_MARK_E.equals(exportParam.getIEMark()))) {
            throw new RuntimeException(xdoi18n.XdoI18nUtil.t("必须输入进出口标记参数"));
        }
        EntryCompareRes entryCompareRes = entryCompareResDtoMapper.toPo(exportParam);
        entryCompareRes.setEntFlag(ConstantsStatus.STATUS_1);
        entryCompareRes.setTradeCode(userInfo.getCompany());
        List<EntryCompareResDto> entryCompareResDtos = new ArrayList<>();
        List<EntryCompareRes> entryCompareRess = entryCompareResMapper.getList(entryCompareRes);
        if (CollectionUtils.isNotEmpty(entryCompareRess)) {
            entryCompareResDtos = entryCompareRess.stream().map(head -> {
                EntryCompareResDto dto = entryCompareResDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        convertForPrint(entryCompareResDtos);
        return entryCompareResDtos;
    }

    /**
     * 导出pCode转换中文名称
     *
     * @param list
     * @return
     */
    public List<EntryCompareResDto> convertForPrint(List<EntryCompareResDto> list) {
        for (EntryCompareResDto item : list) {
            if (item.getTradeMode() != null) {
                item.setTradeMode(item.getTradeMode() + "|" + pCodeHolder.getValue(PCodeType.TRADE, item.getTradeMode()));
            }
            if (item.getTrafMode() != null) {
                item.setTrafMode(item.getTrafMode() + "|" + pCodeHolder.getValue(PCodeType.TRANSF, item.getTrafMode()));
            }
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject assignTask(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("任务分配成功！"));
        GwstdJobAllotConfig config = null;

        List<String> newJobs = new ArrayList<>();
        //页面触发，当前企业下该任务为未执行或执行中，无法插入新的任务
        if (userInfo != null) {
            GwstdJob gwstdJob = new GwstdJob() {{
                setJobType(JOB_TYPE);
                setTradeCode(userInfo.getCompany());
            }};
            List<GwstdJob> gwstdJobs = gwstdJobMapper.getList(gwstdJob);

            if (CollectionUtils.isNotEmpty(gwstdJobs)) {
                if (ConstantsStatus.STATUS_0.equals(gwstdJobs.get(0).getStatus())
                        || ConstantsStatus.STATUS_1.equals(gwstdJobs.get(0).getStatus())) {
                    resultObject.setSuccess(false);
                    resultObject.setMessage(xdoi18n.XdoI18nUtil.t("当前企业已有任务，请等待！"));
                    return resultObject;
                } else {
                    GwstdJob jobPara = new GwstdJob() {{
                        setStatus(ConstantsStatus.STATUS_0);
                        setSid(gwstdJobs.get(0).getSid());
                    }};
                    gwstdJobMapper.resetStatusBySid(jobPara);
                }
            } else {
                newJobs.add(userInfo.getCompany());
            }
        } else {
//            gwstdJobAllotCache = gwstdJobAllotMapperConfig.getGwstdJobAllotCache();
//            if (0 == gwstdJobAllotCache.estimatedSize()) {
//                gwstdJobAllotCache = gwstdJobAllotMapperConfig.refreshCache();
//            }
//            config = gwstdJobAllotCache.getIfPresent(JOB_TYPE);
            config = new GwstdJobAllotConfig(){{
                setJobType(JOB_TYPE);
                setTradeCode(CommonVariable.TRADE_CODE_9999999999);
            }};

            config = gwstdJobAllotConfigMapper.selectOne(config);
            if (null == config) {
                LoggerUtil.logInfo(logger, String.format("未配置[%s]类型任务", JOB_TYPE));
                resultObject.setMessage(String.format("[%s]%s",JOB_TYPE,xdoi18n.XdoI18nUtil.t("该类型任务未配置")));
                resultObject.setSuccess(false);
                return resultObject;
            }
            if (null == config.getLastExecTime()) {
                long time = (new Date()).getTime()-3600*1000;
                config.setLastExecTime(new Date(time));
            }

            String msg = commonService.checkTime(config, JOB_TYPE);
            if (StringUtils.isNotBlank(msg)) {
                resultObject.setSuccess(false);
                resultObject.setMessage(msg);
                return resultObject;
            }

            XxlJobLogger.log("**************报关复核任务分配开始**************");
            List<String> tradeCodes = entryCompareResMapper.assignTask();
            for (String tradeCode : tradeCodes) {
                GwstdJob gwstdJobPara = new GwstdJob() {{
                    setJobType(JOB_TYPE);
                    setTradeCode(tradeCode);
                }};
                List<GwstdJob> gwstdJobs = gwstdJobMapper.getList(gwstdJobPara);
                if (CollectionUtils.isNotEmpty(gwstdJobs)) {
                    if (ConstantsStatus.STATUS_0.equals(gwstdJobs.get(0).getStatus())
                            || ConstantsStatus.STATUS_1.equals(gwstdJobs.get(0).getStatus())) {
                        XxlJobLogger.log("当前分配任务企业为：[" + tradeCode + "]已有任务");
                        continue;
                    } else {
                        GwstdJob gwstdJob = new GwstdJob() {{
                            setSid(gwstdJobs.get(0).getSid());
                            setStatus(ConstantsStatus.STATUS_0);
                        }};
                        gwstdJobMapper.resetStatusBySid(gwstdJob);
                    }
                } else {
                    newJobs.add(tradeCode);
                }
            }
            config.setLastExecTime(new Date());
            gwstdJobAllotConfigMapper.updateByPrimaryKey(config);
//            gwstdJobAllotCache.put(JOB_TYPE, config);
        }
        if (CollectionUtils.isNotEmpty(newJobs)) {
            for (String jobTrade : newJobs) {
                GwstdJob gwstdJob = new GwstdJob() {{
                    setSid(UUID.randomUUID().toString());
                    setJobType(JOB_TYPE);
                    setTradeCode(jobTrade);
                    setStatus(ConstantsStatus.STATUS_0);
                    setInsertTime(new Date());
                    setInsertUser("GW");
                }};
                gwstdJobMapper.insert(gwstdJob);
            }
        }
        XxlJobLogger.log("**************报关复核任务分配结束**************");
        return resultObject;
    }

    public void checkData() {
        XxlJobLogger.log("**************报关复核任务调度开始**************");
        String lockMark = UUID.randomUUID().toString();
        int count = gwstdJobMapper.updateLockMark(lockMark, JOB_TYPE);
        if (count > 0) {
            List<GwstdJob> gwstdJobs = gwstdJobMapper.getJobListByuuid(lockMark);
            if (CollectionUtils.isNotEmpty(gwstdJobs)) {
                initCurrAndCountry();
                if (MapUtils.isEmpty(currCode) || MapUtils.isEmpty(countryCode)) {
                    XxlJobLogger.log("**************Pcode获取失败！**************");
                    logger.info("**************Pcode获取失败！**************");
                    return;
                }
                for (GwstdJob gwstdJob : gwstdJobs) {
                    TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
                    try {
                        logger.info("企业[" + gwstdJob.getTradeCode() + "]报关复核任务开始！");
                        XxlJobLogger.log("企业[" + gwstdJob.getTradeCode() + "]报关复核任务开始！");
                        gwstdJob.setStatus(Constants.JOB_STATUS_1);
                        gwstdJobMapper.updateByPrimaryKey(gwstdJob);
                        entryCompare(gwstdJob);
                        gwstdJob.setEndTime(new Date());
                        gwstdJob.setStatus(Constants.JOB_STATUS_2);
                        gwstdJobMapper.updateByPrimaryKey(gwstdJob);
                        dataSourceTransactionManager.commit(transactionStatus);
                        logger.info("企业[" + gwstdJob.getTradeCode() + "]报关复核任务结束！");
                        XxlJobLogger.log("企业[" + gwstdJob.getTradeCode() + "]报关复核任务结束！");
                    } catch (Exception e) {
                        if (transactionStatus != null) {
                            dataSourceTransactionManager.rollback(transactionStatus);
                        }
                        gwstdJob.setEndTime(new Date());
                        gwstdJob.setStatus(Constants.JOB_STATUS_3);
                        gwstdJobMapper.updateByPrimaryKey(gwstdJob);
                        e.printStackTrace();
                        logger.info("报关复核任务调度失败");
                        XxlJobLogger.log("报关复核任务调度失败");
                    }
                }
            } else {
                logger.info("当前无需要执行任务");
                XxlJobLogger.log("当前无需要执行任务");
            }
        }
        XxlJobLogger.log("**************报关复核任务调度结束**************");
    }

    public void entryCompare(GwstdJob gwstdJob) {
        GwstdEntryHead gwstdEntryHead = new GwstdEntryHead() {{
            setTradeCode(gwstdJob.getTradeCode());
            setLockMark(UUID.randomUUID().toString());
        }};
        TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
        try {
            /*
             * 以订阅报关单数据为准进行比对
             */
            int count = gwstdEntryHeadMapper.updateCompareMark(gwstdEntryHead);
            if (count > 0) {
                logger.info("企业[" + gwstdJob.getTradeCode() + "]共有待比对数据" + count + "条");
                XxlJobLogger.log("企业[" + gwstdJob.getTradeCode() + "]共有待比对数据" + count + "条");
                int pageSize = 1000;
                int pageNum = count / pageSize + 1;
                for (int i = 1; i <= pageNum; i++) {
                    logger.info("企业[" + gwstdJob.getTradeCode() + "]开始比对第" + i + "页数据");
                    XxlJobLogger.log("企业[" + gwstdJob.getTradeCode() + "]开始比对第" + i + "页数据");
                    Page<GwstdEntryHead> page = PageHelper.startPage(i, pageSize)
                            .doSelectPage(() -> gwstdEntryHeadMapper.getList(gwstdEntryHead));
                    //过滤进出口标记为空的垃圾数据
                    List<GwstdEntryHead> gwstdEntryHeads = page.getResult().stream().filter(
                            e -> StringUtils.isNotEmpty(e.getIEMark())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(gwstdEntryHeads)) {
                        Map<String, List<GwstdEntryHead>> map = gwstdEntryHeads.stream()
                                .collect(Collectors.groupingBy(e -> e.getIEMark()));
                        List<String> entryNos = gwstdEntryHeads.stream().map(e -> e.getEntryNo()).collect(Collectors.toList());

                        //清空历史数据
                        entryCompareResMapper.deleteByEntryNos(entryNos, gwstdJob.getTradeCode());
                        entryCompareDetailMapper.deleteByEntryNos(entryNos, gwstdJob.getTradeCode());

                        //订阅数据区分进出口
                        List<GwstdEntryHead> gwIEntryHeads = map.get(CommonVariable.IE_MARK_I);
                        if(CollectionUtils.isNotEmpty(gwIEntryHeads)){
                            //获取对应的进口报关单表头数据
                            Example decIExample = new Example(DecIEntryHead.class);
                            Example.Criteria decICriteria = decIExample.createCriteria();
                            decICriteria.andEqualTo("tradeCode", gwstdJob.getTradeCode());
                            decICriteria.andIn("entryNo", gwIEntryHeads.stream().map(e -> e.getEntryNo()).collect(Collectors.toList()));
                            List<DecIEntryHead> decIEntryHeads = decIEntryHeadMapper.selectByExample(decIExample);

                            //表头比对
                            entryIHeadCompare(gwIEntryHeads, decIEntryHeads);
                        }

                        List<GwstdEntryHead> gwEEntryHeads = map.get(CommonVariable.IE_MARK_E);
                        if(CollectionUtils.isNotEmpty(gwEEntryHeads)){
                            //获取对应的出口报关单表头数据
                            Example decEExample = new Example(DecEEntryHead.class);
                            Example.Criteria decECriteria = decEExample.createCriteria();
                            decECriteria.andEqualTo("tradeCode", gwstdJob.getTradeCode());
                            decECriteria.andIn("entryNo", gwEEntryHeads.stream().map(e -> e.getEntryNo()).collect(Collectors.toList()));
                            List<DecEEntryHead> decEEntryHeads = decEEntryHeadMapper.selectByExample(decEExample);

                            //表头比对
                            entryEHeadCompare(gwEEntryHeads, decEEntryHeads);
                        }
                    }
                }
                Example example = new Example(GwstdEntryHead.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("tradeCode", gwstdEntryHead.getTradeCode());
                criteria.andEqualTo("lockMark", gwstdEntryHead.getLockMark());
                GwstdEntryHead entryHeadParam = new GwstdEntryHead() {{
                    setEntryCompareFlag(ConstantsStatus.STATUS_2);
                }};
                gwstdEntryHeadMapper.updateByExampleSelective(entryHeadParam, example);
            } else {
                logger.info("企业[" + gwstdJob.getTradeCode() + "]无需待比对数据");
                XxlJobLogger.log("企业[" + gwstdJob.getTradeCode() + "]无需待比对数据");
            }
            List<EntryCompareRes> entryCompareRess = new ArrayList<>();
            List<EntryCompareDetail> entryCompareDetails = new ArrayList<>();
            EntryCompareDetail compareDetailTemp = new EntryCompareDetail();
            {
                compareDetailTemp.setTradeCode(gwstdEntryHead.getTradeCode());
                compareDetailTemp.setInsertTime(new Date());
                compareDetailTemp.setInsertUser("GW");
                compareDetailTemp.setDataType(ConstantsStatus.STATUS_1);
            }

            EntryCompareRes entryCompareRes = new EntryCompareRes();
            {
                entryCompareRes.setTradeCode(gwstdEntryHead.getTradeCode());
                entryCompareRes.setInsertTime(new Date());
                entryCompareRes.setInsertUser("GW");
                entryCompareRes.setEntFlag(ConstantsStatus.STATUS_3);
            }

            /*
             * 本地进口报关单缺失订阅数据处理
             */
            List<DecEEntryHead> entryEHeads = decEEntryHeadMapper.getHgDefectData(gwstdJob.getTradeCode());
            if (CollectionUtils.isNotEmpty(entryEHeads)) {
                List<String> entryNos = entryEHeads.stream().map(e -> e.getEntryNo()).collect(Collectors.toList());
                entryCompareResMapper.deleteByEntryNos(entryNos, gwstdJob.getTradeCode());
                entryCompareDetailMapper.deleteByEntryNos(entryNos, gwstdJob.getTradeCode());
                entryEHeads.forEach(e -> {
                    try {
                        EntryCompareRes res = (EntryCompareRes) entryCompareRes.clone();
                        res.setSid(UUID.randomUUID().toString());
                        res.setIEMark(CommonVariable.IE_MARK_E);
                        res.setEntryNo(e.getEntryNo());
                        res.setEmsListNo(e.getEmsListNo());
                        res.setInsertUser(e.getInsertUser());
                        entryCompareRess.add(res);
                        EntryCompareDetail detail = (EntryCompareDetail) compareDetailTemp.clone();
                        detail.setSid(UUID.randomUUID().toString());
                        detail.setIEMark(CommonVariable.IE_MARK_E);
                        detail.setHeadId(e.getSid());
                        detail.setEmsListNo(e.getEmsListNo());
                        detail.setEntryNo(e.getEntryNo());
                        detail.setHgField(xdoi18n.XdoI18nUtil.t("无海关报关数据"));
                        detail.setInsertUser(e.getInsertUser());
                        entryCompareDetails.add(detail);
                    } catch (CloneNotSupportedException ex) {
                        ex.printStackTrace();
                    }
                });
            }
            /*
             * 本地出口报关单缺失订阅数据处理
             */
            List<DecIEntryHead> entryIHeads = decIEntryHeadMapper.getHgDefectData(gwstdJob.getTradeCode());
            if (CollectionUtils.isNotEmpty(entryIHeads)) {
                List<String> entryNos = entryIHeads.stream().map(e -> e.getEntryNo()).collect(Collectors.toList());
                entryCompareResMapper.deleteByEntryNos(entryNos, gwstdJob.getTradeCode());
                entryCompareDetailMapper.deleteByEntryNos(entryNos, gwstdJob.getTradeCode());
                entryIHeads.forEach(e -> {
                    try {
                        EntryCompareRes res = (EntryCompareRes) entryCompareRes.clone();
                        res.setSid(UUID.randomUUID().toString());
                        res.setIEMark(CommonVariable.IE_MARK_I);
                        res.setEntryNo(e.getEntryNo());
                        res.setEmsListNo(e.getEmsListNo());
                        res.setInsertUser(e.getInsertUser());
                        entryCompareRess.add(res);
                        EntryCompareDetail detail = (EntryCompareDetail) compareDetailTemp.clone();
                        detail.setSid(UUID.randomUUID().toString());
                        detail.setIEMark(CommonVariable.IE_MARK_I);
                        detail.setHeadId(e.getSid());
                        detail.setEmsListNo(e.getEmsListNo());
                        detail.setEntryNo(e.getEntryNo());
                        detail.setHgField(xdoi18n.XdoI18nUtil.t("无海关报关数据"));
                        detail.setInsertUser(e.getInsertUser());
                        entryCompareDetails.add(detail);
                    } catch (CloneNotSupportedException ex) {
                        ex.printStackTrace();
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(entryCompareRess)) {
                bulkSqlOpt.batchInsert(entryCompareRess, EntryCompareResMapper.class);
            }
            if (CollectionUtils.isNotEmpty(entryCompareDetails)) {
                bulkSqlOpt.batchInsert(entryCompareDetails, EntryCompareDetailMapper.class);
            }

        } catch (Exception e) {
            if (transactionStatus != null) {
                dataSourceTransactionManager.rollback(transactionStatus);
            }
            Example example = new Example(GwstdEntryHead.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("tradeCode", gwstdEntryHead.getTradeCode());
            criteria.andEqualTo("lockMark", gwstdEntryHead.getLockMark());
            GwstdEntryHead entryHeadParam = new GwstdEntryHead() {{
                setEntryCompareFlag(ConstantsStatus.STATUS_3);
            }};
            gwstdEntryHeadMapper.updateByExampleSelective(entryHeadParam, example);
            e.printStackTrace();
            throw new RuntimeException(xdoi18n.XdoI18nUtil.t("对比错误！") + e.getMessage());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void entryIHeadCompare(List<GwstdEntryHead> gwstdEntryHeads, List<DecIEntryHead> decIEntryHeads) throws IllegalAccessException, CloneNotSupportedException {
        if (CollectionUtils.isNotEmpty(gwstdEntryHeads)) {
            //比对结果集合
            List<EntryCompareRes> entryCompareRess = new ArrayList<>();
            //比对详情集合
            List<EntryCompareDetail> entryCompareDetails = new ArrayList<>();
            Map<String, List<DecIEntryHead>> decEntryMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(decIEntryHeads)) {
                decEntryMap = decIEntryHeads.stream().collect(Collectors.groupingBy(e -> e.getEntryNo()));
            }
            for (GwstdEntryHead gwstdEntryHead : gwstdEntryHeads) {
                EntryCompareDetail compareDetailTemp = new EntryCompareDetail();
                {
                    compareDetailTemp.setSeqNo(gwstdEntryHead.getSeqNo());
                    compareDetailTemp.setIEMark(CommonVariable.IE_MARK_I);
                    compareDetailTemp.setTradeCode(gwstdEntryHead.getTradeCode());
                    compareDetailTemp.setInsertTime(new Date());
                    compareDetailTemp.setInsertUser("GW");
                    compareDetailTemp.setDeclareDate(gwstdEntryHead.getEntryDeclareDate());
                    compareDetailTemp.setEntryNo(gwstdEntryHead.getEntryNo());
                    compareDetailTemp.setTradeMode(gwstdEntryHead.getTradeMode());
                    compareDetailTemp.setTrafMode(gwstdEntryHead.getTrafMode());
                    compareDetailTemp.setDataType(ConstantsStatus.STATUS_1);
                    compareDetailTemp.setHeadId(gwstdEntryHead.getSid());
                    compareDetailTemp.setAgentCode(gwstdEntryHead.getAgentCode() + "|" + gwstdEntryHead.getAgentName());
                }

                EntryCompareRes entryCompareRes = new EntryCompareRes();
                {
                    entryCompareRes.setSid(UUID.randomUUID().toString());
                    entryCompareRes.setSeqNo(gwstdEntryHead.getSeqNo());
                    entryCompareRes.setIEMark(CommonVariable.IE_MARK_I);
                    entryCompareRes.setTradeCode(gwstdEntryHead.getTradeCode());
                    entryCompareRes.setInsertTime(new Date());
                    entryCompareRes.setInsertUser("GW");
                    entryCompareRes.setDeclareDate(gwstdEntryHead.getEntryDeclareDate());
                    entryCompareRes.setEntryNo(gwstdEntryHead.getEntryNo());
                    entryCompareRes.setTradeMode(gwstdEntryHead.getTradeMode());
                    entryCompareRes.setTrafMode(gwstdEntryHead.getTrafMode());
                    entryCompareRes.setEntFlag(ConstantsStatus.STATUS_1);
                    entryCompareRes.setAgentCode(gwstdEntryHead.getAgentCode() + "|" + gwstdEntryHead.getAgentName());
                }

                //关务无对应报关单数据处理
                if (MapUtils.isEmpty(decEntryMap) || !decEntryMap.containsKey(gwstdEntryHead.getEntryNo())) {
                    entryCompareRes.setEntFlag(ConstantsStatus.STATUS_3);
                    compareDetailTemp.setSid(UUID.randomUUID().toString());
                    compareDetailTemp.setGwField(xdoi18n.XdoI18nUtil.t("无内部草单数据"));
                    entryCompareDetails.add(compareDetailTemp);
                } else {
                    //表头比对开始
                    DecIEntryHead decIEntryHead = decEntryMap.get(gwstdEntryHead.getEntryNo()).get(0);
//                    //运输工具及航次号组装
//                    if(StringUtils.isNotBlank(decIEntryHead.getVoyageNo())) {
//                        decIEntryHead.setTrafName(StringUtils.isBlank(decIEntryHead.getTrafName())?"":decIEntryHead.getTrafName()+"/"+decIEntryHead.getVoyageNo());
//                    }
                    //通过注解实现字段值通过Map展示
                    Map<String, KeyValuePair> decMap = getDataToMap(decIEntryHead);
                    entryCompareRes.setInsertUser(decIEntryHead.getInsertUser());
                    entryCompareRes.setEmsListNo(decIEntryHead.getEmsListNo());
                    entryCompareRes.setEmsListNo(decIEntryHead.getEmsListNo());
                    Class cls = gwstdEntryHead.getClass();
                    Field[] fields = cls.getDeclaredFields();
                    for (Field field : fields) {
                        field.setAccessible(true);
                        Compare compare = field.getAnnotation(Compare.class);
                        if (compare != null) {
                            String value = "";
                            value = field.get(gwstdEntryHead) == null ? null: field.get(gwstdEntryHead).toString();

                            String column = compare.column();
                            if ("destPort".equals(column)) {
                                continue;
                            }
                            String columnName = "";
                            String gwField = "";
                            //enrtyPortCode为特殊字段，特殊处理
                            if ("entryPortCode".equals(column)) {
                                columnName = decMap.get("entryPort").getKey().toString();
                                gwField = decMap.get("entryPort").getValue() == null ? "" : decMap.get("entryPort").getValue().toString();
                            } else {
                                columnName = decMap.get(column).getKey().toString();
                                gwField = decMap.get(column).getValue() == null ? "" : decMap.get(column).getValue().toString();
                            }
                            boolean flag = false;
                            if (Arrays.asList(new String[]{"feeCurr", "otherCurr", "insurCurr"}).contains(column)) {
                                if (compareSpacialValue(value, gwField, "CURR")) {
                                    flag = true;
                                }
                            } else if (Arrays.asList(new String[]{"tradeCountry", "tradeNation"}).contains(column)) {
                                if (compareSpacialValue(value, gwField, "COUNTRY")) {
                                    flag = true;
                                }
                            } else if (Arrays.asList(new String[]{"feeRate", "grossWt", "insurRate", "netWt", "otherRate", "packNum"}).contains(column)) {
                                if (columnCompareBigDecimal(value, gwField)) {
                                    flag = true;
                                }
                            } else {
                                if (columnCompare(value, gwField)) {
                                    flag = true;
                                }
                            }
                            if (flag) {
                                //比较栏位的值是否相同，不同则新增记录
                                EntryCompareDetail detail = (EntryCompareDetail) compareDetailTemp.clone();
                                detail.setEmsListNo(entryCompareRes.getEmsListNo());
                                detail.setInsertUser(decIEntryHead.getInsertUser());
                                detail.setFieldName(columnName);
                                detail.setGwField(gwField);
                                detail.setHgField(value);
                                detail.setSid(UUID.randomUUID().toString());
                                entryCompareDetails.add(detail);
                                entryCompareRes.setEntFlag(ConstantsStatus.STATUS_2);
                            }
                        }
                    }
                    //表头对应表体比对开始
                    List<EntryCompareDetail> details = entryIListCompare(gwstdEntryHead, decEntryMap.get(gwstdEntryHead.getEntryNo()).get(0));
                    if (CollectionUtils.isNotEmpty(details)) {
                        gwstdEntryHead.setEntryCompareFlag(ConstantsStatus.STATUS_2);
                        entryCompareRes.setEntFlag(ConstantsStatus.STATUS_2);
                        entryCompareDetails.addAll(details);
                    }
                }
                entryCompareRess.add(entryCompareRes);
            }
            if (CollectionUtils.isNotEmpty(entryCompareRess)) {
                bulkSqlOpt.batchInsert(entryCompareRess, EntryCompareResMapper.class);
            }
            if (CollectionUtils.isNotEmpty(entryCompareDetails)) {
                bulkSqlOpt.batchInsert(entryCompareDetails, EntryCompareDetailMapper.class);
            }
        }
    }
    public boolean columnCompareBigDecimal(String val1, String val2) {
        if (StringUtils.isNotEmpty(val1) && StringUtils.isNotEmpty(val2)) {
            BigDecimal valueB = new BigDecimal(val1);
            BigDecimal gwFieldB = new BigDecimal(val2);
            return valueB.compareTo(gwFieldB) != 0;
        } else if (StringUtils.isEmpty(val1) && StringUtils.isEmpty(val2)) {
            return false;
        } else {
            return true;
        }
    }

    public boolean columnCompare(String val1, String val2) {
        if ((StringUtils.isNotEmpty(val1) && StringUtils.isNotEmpty(val2) && val1.equals(val2))
                || (StringUtils.isEmpty(val1) && StringUtils.isEmpty(val2))) {
            return false;
        } else {
            return true;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void entryEHeadCompare(List<GwstdEntryHead> gwstdEntryHeads, List<DecEEntryHead> decEEntryHeads) throws IllegalAccessException, CloneNotSupportedException {
        if (CollectionUtils.isNotEmpty(gwstdEntryHeads)) {
            List<EntryCompareRes> entryCompareRess = new ArrayList<>();
            List<EntryCompareDetail> entryCompareDetails = new ArrayList<>();
            Map<String, List<DecEEntryHead>> decEntryMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(decEEntryHeads)) {
                decEntryMap = decEEntryHeads.stream().collect(Collectors.groupingBy(e -> e.getEntryNo()));
            }
            for (GwstdEntryHead gwstdEntryHead : gwstdEntryHeads) {
                EntryCompareDetail entryCompareDetail = new EntryCompareDetail();
                {
                    entryCompareDetail.setSeqNo(gwstdEntryHead.getSeqNo());
                    entryCompareDetail.setIEMark(CommonVariable.IE_MARK_E);
                    entryCompareDetail.setTradeCode(gwstdEntryHead.getTradeCode());
                    entryCompareDetail.setInsertTime(new Date());
                    entryCompareDetail.setInsertUser("GW");
                    entryCompareDetail.setDeclareDate(gwstdEntryHead.getEntryDeclareDate());
                    entryCompareDetail.setEntryNo(gwstdEntryHead.getEntryNo());
                    entryCompareDetail.setTradeMode(gwstdEntryHead.getTradeMode());
                    entryCompareDetail.setTrafMode(gwstdEntryHead.getTrafMode());
                    entryCompareDetail.setDataType(ConstantsStatus.STATUS_1);
                    entryCompareDetail.setHeadId(gwstdEntryHead.getSid());
                    entryCompareDetail.setAgentCode(gwstdEntryHead.getAgentCode() + "|" + gwstdEntryHead.getAgentName());
                }

                EntryCompareRes entryCompareRes = new EntryCompareRes();
                {
                    entryCompareRes.setSid(UUID.randomUUID().toString());
                    entryCompareRes.setSeqNo(gwstdEntryHead.getSeqNo());
                    entryCompareRes.setIEMark(CommonVariable.IE_MARK_E);
                    entryCompareRes.setTradeCode(gwstdEntryHead.getTradeCode());
                    entryCompareRes.setInsertTime(new Date());
                    entryCompareRes.setInsertUser("GW");
                    entryCompareRes.setDeclareDate(gwstdEntryHead.getEntryDeclareDate());
                    entryCompareRes.setEntryNo(gwstdEntryHead.getEntryNo());
                    entryCompareRes.setTradeMode(gwstdEntryHead.getTradeMode());
                    entryCompareRes.setTrafMode(gwstdEntryHead.getTrafMode());
                    entryCompareRes.setEntFlag(ConstantsStatus.STATUS_1);
                    entryCompareRes.setAgentCode(gwstdEntryHead.getAgentCode());
                }

                if (MapUtils.isEmpty(decEntryMap) || !decEntryMap.containsKey(gwstdEntryHead.getEntryNo())) {
                    entryCompareRes.setEntFlag(ConstantsStatus.STATUS_3);
                    entryCompareDetail.setSid(UUID.randomUUID().toString());
                    entryCompareDetail.setGwField(xdoi18n.XdoI18nUtil.t("无内部草单数据"));
                    entryCompareDetails.add(entryCompareDetail);
                } else {
                    DecEEntryHead decEEntryHead = decEntryMap.get(gwstdEntryHead.getEntryNo()).get(0);
//                    //运输工具及航次号组装
//                    if(StringUtils.isNotBlank(decEEntryHead.getVoyageNo())) {
//                        decEEntryHead.setTrafName(StringUtils.isBlank(decEEntryHead.getTrafName())?"":decEEntryHead.getTrafName()+"/"+decEEntryHead.getVoyageNo());
//                    }
                    Map<String, KeyValuePair> decMap = getDataToMap(decEEntryHead);
                    entryCompareRes.setEmsListNo(decEEntryHead.getEmsListNo());
                    entryCompareRes.setInsertUser(decEEntryHead.getInsertUser());
                    Class cls = gwstdEntryHead.getClass();
                    Field[] fields = cls.getDeclaredFields();
                    for (Field field : fields) {
                        field.setAccessible(true);
                        Compare compare = field.getAnnotation(Compare.class);
                        if (compare != null) {
                            String value = "";
                            value = field.get(gwstdEntryHead) == null ? "" : field.get(gwstdEntryHead).toString();
                            String column = compare.column();
                            String columnName = "";
                            String gwField = "";
                            if ("entryPortCode".equals(column)) {
                                continue;
                            }
                            if ("despPort".equals(column)) {
                                columnName = decMap.get("entryPort").getKey().toString();
                                gwField = decMap.get("entryPort").getValue() == null ? "" : decMap.get("entryPort").getValue().toString();
                            } else {
                                columnName = decMap.get(column).getKey().toString();
                                gwField = decMap.get(column).getValue() == null ? "" : decMap.get(column).getValue().toString();
                            }
                            boolean flag = false;
                            if (Arrays.asList(new String[]{"feeCurr", "otherCurr", "insurCurr"}).contains(column)) {
                                if (compareSpacialValue(value, gwField, "CURR")) {
                                    flag = true;
                                }
                            } else if (Arrays.asList(new String[]{"tradeCountry", "tradeNation"}).contains(column)) {
                                if (compareSpacialValue(value, gwField, "COUNTRY")) {
                                    flag = true;
                                }
                            } else if (Arrays.asList(new String[]{"feeRate", "grossWt", "insurRate", "netWt", "otherRate", "packNum"}).contains(column)) {
                                if (columnCompareBigDecimal(value, gwField)) {
                                    flag = true;
                                }
                            }else {
                                if (columnCompare(value, gwField)) {
                                    flag = true;
                                }
                            }
                            if (flag) {
                                EntryCompareDetail detail = (EntryCompareDetail) entryCompareDetail.clone();
                                detail.setEmsListNo(entryCompareRes.getEmsListNo());
                                detail.setInsertUser(decEEntryHead.getInsertUser());
                                detail.setFieldName(columnName);
                                detail.setGwField(gwField);
                                detail.setHgField(value);
                                detail.setSid(UUID.randomUUID().toString());
                                entryCompareDetails.add(detail);
                                entryCompareRes.setEntFlag(ConstantsStatus.STATUS_2);
                            }
                        }
                    }
                    List<EntryCompareDetail> details = entryEListCompare(gwstdEntryHead, decEntryMap.get(gwstdEntryHead.getEntryNo()).get(0));
                    if (CollectionUtils.isNotEmpty(details)) {
                        gwstdEntryHead.setEntryCompareFlag(ConstantsStatus.STATUS_2);
                        entryCompareRes.setEntFlag(ConstantsStatus.STATUS_2);
                        entryCompareDetails.addAll(details);
                    }
                }
                entryCompareRess.add(entryCompareRes);
            }
            if (CollectionUtils.isNotEmpty(entryCompareRess)) {
                bulkSqlOpt.batchInsert(entryCompareRess, EntryCompareResMapper.class);
            }
            if (CollectionUtils.isNotEmpty(entryCompareDetails)) {
                bulkSqlOpt.batchInsert(entryCompareDetails, EntryCompareDetailMapper.class);
            }
        }
    }

    public <T> Map<String, KeyValuePair> getDataToMap(T obj) throws IllegalAccessException {
        Map<String, KeyValuePair> map = new HashMap<>();
        Class cls = obj.getClass();
        Field[] fields = cls.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Compare compare = field.getAnnotation(Compare.class);
            if (compare != null) {
                KeyValuePair<String, String> k = new KeyValuePair();
                k.setKey(compare.columnName());
                if (field.get(obj) instanceof BigDecimal) {
                    k.setValue(field.get(obj) == null ? "" : ((BigDecimal) field.get(obj)).setScale(5, BigDecimal.ROUND_HALF_UP).toString());
                } else {
                    k.setValue(field.get(obj) == null ? "" : field.get(obj).toString());
                }
                map.put(compare.column(), k);
            }
            String name = field.getName();
            if ("voyage_no".equals(name)) {

            }
            if ("promiseItems".equals(name)) {
                String promiseItems = field.get(obj) == null ? "" : field.get(obj).toString();
                KeyValuePair k1 = new KeyValuePair();
                k1.setKey("特殊关系确认");
                KeyValuePair k2 = new KeyValuePair();
                k2.setKey("价格影响确认");
                KeyValuePair k3 = new KeyValuePair();
                k3.setKey("支付特许权使用费确认");
                if (StringUtils.isEmpty(promiseItems)) {
                    k1.setValue(null);
                    k2.setValue(null);
                    k3.setValue(null);
                } else {
                    if (promiseItems.contains(DecVariable.PROMISE_ITEM_1)) {
                        k1.setValue(CommonVariable.YES_EN);
                    } else if (promiseItems.contains(DecVariable.PROMISE_ITEM_9)) {
                        k1.setValue(CommonVariable.NO_EN);
                    } else {
                        k1.setValue(null);
                    }
                    if (promiseItems.contains(DecVariable.PROMISE_ITEM_2)) {
                        k2.setValue(CommonVariable.YES_EN);
                    } else if (promiseItems.contains(DecVariable.PROMISE_ITEM_8)) {
                        k2.setValue(CommonVariable.NO_EN);
                    } else {
                        k2.setValue(null);
                    }
                    if (promiseItems.contains(DecVariable.PROMISE_ITEM_3)) {
                        k3.setValue(CommonVariable.YES_EN);
                    } else if (promiseItems.contains(DecVariable.PROMISE_ITEM_7)) {
                        k3.setValue(CommonVariable.NO_EN);
                    } else {
                        k3.setValue(null);
                    }
                }
                map.put("confirmSpecial", k1);
                map.put("confirmPrice", k2);
                map.put("confirmRoyalties", k3);
            }
        }
        return map;
    }


    public List<EntryCompareDetail> entryIListCompare(GwstdEntryHead gwstdEntryHead, DecIEntryHead decIEntryHead) throws CloneNotSupportedException, IllegalAccessException {
        List<EntryCompareDetail> entryCompareDetails = new ArrayList<>();
        if (gwstdEntryHead != null && decIEntryHead != null) {
            GwstdEntryList gwstdEntryListPara = new GwstdEntryList();
            gwstdEntryListPara.setSeqNo(gwstdEntryHead.getSeqNo());
            List<GwstdEntryList> gwstdEntryLists = gwstdEntryListMapper.getList(gwstdEntryListPara);

            DecIEntryList decIEntryListPara = new DecIEntryList();
            decIEntryListPara.setHeadId(decIEntryHead.getSid());
            decIEntryListPara.setTradeCode(decIEntryHead.getTradeCode());
            List<DecIEntryList> decIEntryLists = decIEntryListMapper.selectListByParam(decIEntryListPara);

            EntryCompareDetail detailTemplate = new EntryCompareDetail();
            {
                detailTemplate.setSeqNo(gwstdEntryHead.getSeqNo());
                detailTemplate.setIEMark(CommonVariable.IE_MARK_I);
                detailTemplate.setTradeCode(gwstdEntryHead.getTradeCode());
                detailTemplate.setInsertTime(new Date());
                detailTemplate.setInsertUser("GW");
                detailTemplate.setDeclareDate(gwstdEntryHead.getEntryDeclareDate());
                detailTemplate.setEntryNo(gwstdEntryHead.getEntryNo());
                detailTemplate.setTradeMode(gwstdEntryHead.getTradeMode());
                detailTemplate.setTrafMode(gwstdEntryHead.getTrafMode());
                detailTemplate.setDataType(ConstantsStatus.STATUS_2);
                detailTemplate.setAgentCode(gwstdEntryHead.getAgentCode() + "|" + gwstdEntryHead.getAgentName());
            }

            /*if (CollectionUtils.isEmpty(decIEntryLists)) {
                EntryCompareDetail detail = (EntryCompareDetail) detailTemplate.clone();
                detail.setSid(UUID.randomUUID().toString());
                entryCompareDetails.add(detail);
                return entryCompareDetails;
            }*/

            if ((CollectionUtils.isNotEmpty(gwstdEntryLists) ? gwstdEntryLists.size() : 0) > decIEntryLists.size()) {
                Map<String, List<DecIEntryList>> decIMap = decIEntryLists.stream().filter(e ->
                        e.getSerialNo() != null).collect(Collectors.groupingBy(e -> e.getSerialNo().toString()));
                for (GwstdEntryList gwstdEntryList : gwstdEntryLists) {
                    if (!decIMap.containsKey(gwstdEntryList.getGno() == null ? "" : gwstdEntryList.getGno().toString())) {
                        EntryCompareDetail detail = (EntryCompareDetail) detailTemplate.clone();
                        detail.setGNo(gwstdEntryList.getGno() == null ? "" : gwstdEntryList.getGno().toString());
                        detail.setSid(UUID.randomUUID().toString());
                        detail.setHeadId(gwstdEntryList.getSid());
                        detail.setGwField(xdoi18n.XdoI18nUtil.t("无内部草单数据"));
                        entryCompareDetails.add(detail);
                    } else {
                        DecIEntryList decIEntryList = decIMap.get(gwstdEntryList.getGno().toString()).get(0);
                        Map<String, KeyValuePair> decMap = getDataToMap(decIEntryList);
                        Class cls = gwstdEntryList.getClass();
                        Field[] fields = cls.getDeclaredFields();
                        for (Field field : fields) {
                            field.setAccessible(true);
                            Compare compare = field.getAnnotation(Compare.class);
                            if (compare != null) {
                                String value = "";
                                value = field.get(gwstdEntryList) == null ? "" : field.get(gwstdEntryList).toString();
                                String column = compare.column();
                                String columnName = decMap.get(column).getKey().toString();
                                String gwField = decMap.get(column).getValue() == null ? "" : decMap.get(column).getValue().toString();
                                boolean flag = false;
                                if ("curr".equals(column)) {
                                    if (compareSpacialValue(value, gwField, "CURR")) {
                                        flag = true;
                                    }
                                } else if (Arrays.asList(new String[]{"destinationCountry", "originCountry"}).contains(column)) {
                                    if (compareSpacialValue(value, gwField, "COUNTRY")) {
                                        flag = true;
                                    }
                                }else if (Arrays.asList(new String[]{"decPrice", "decTotal", "qty", "qty1", "qty2"}).contains(column)) {
                                    if (columnCompareBigDecimal(value, gwField)) {
                                        flag = true;
                                    }
                                } else {
                                    if (columnCompare(value, gwField)) {
                                        flag = true;
                                    }
                                }
                                if (flag) {
                                    EntryCompareDetail detail = (EntryCompareDetail) detailTemplate.clone();
                                    detail.setEmsListNo(decIEntryHead.getEmsListNo());
                                    detail.setHeadId(gwstdEntryList.getSid());
                                    detail.setFieldName(columnName);
                                    detail.setGwField(gwField);
                                    detail.setHgField(value);
                                    detail.setGNo(gwstdEntryList.getGno().toString());
                                    detail.setSid(UUID.randomUUID().toString());
                                    detail.setInsertUser(decIEntryHead.getInsertUser());
                                    entryCompareDetails.add(detail);
                                }
                            }
                        }
                    }
                }
            } else if(CollectionUtils.isNotEmpty(decIEntryLists)){
                Map<String, List<GwstdEntryList>> gwEntryMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(gwstdEntryLists)) {
                    gwEntryMap = gwstdEntryLists.stream().filter(e -> e.getGno() != null).collect(Collectors.groupingBy(e -> e.getGno().toString()));
                }
                for (DecIEntryList decIEntryList : decIEntryLists) {
                    if (!gwEntryMap.containsKey(decIEntryList.getSerialNo() == null ? "" : decIEntryList.getSerialNo().toString())) {
                        EntryCompareDetail detail = (EntryCompareDetail) detailTemplate.clone();
                        detail.setGNo(decIEntryList.getSerialNo() == null ? "" : decIEntryList.getSerialNo().toString());
                        detail.setSid(UUID.randomUUID().toString());
                        detail.setHeadId(decIEntryList.getSid());
                        detail.setEmsListNo(decIEntryHead.getEmsListNo());
                        detail.setHgField(xdoi18n.XdoI18nUtil.t("无海关报关数据"));
                        detail.setInsertUser(decIEntryHead.getInsertUser());
                        entryCompareDetails.add(detail);
                    } else {
                        GwstdEntryList gwstdEntryList = gwEntryMap.get(decIEntryList.getSerialNo().toString()).get(0);
                        Map<String, KeyValuePair> decMap = getDataToMap(gwstdEntryList);
                        Class cls = decIEntryList.getClass();
                        Field[] fields = cls.getDeclaredFields();
                        for (Field field : fields) {
                            Compare compare = field.getAnnotation(Compare.class);
                            field.setAccessible(true);
                            if (compare != null) {
                                String value = "";
                                value = field.get(decIEntryList) == null ? "" : field.get(decIEntryList).toString();
                                String column = compare.column();
                                String columnName = compare.columnName();
                                String hgField = decMap.get(column).getValue() == null ? "" : decMap.get(column).getValue().toString();
                                boolean flag = false;
                                if ("curr".equals(column)) {
                                    if (compareSpacialValue(hgField, value, "CURR")) {
                                        flag = true;
                                    }
                                } else if (Arrays.asList(new String[]{"destinationCountry", "originCountry"}).contains(column)) {
                                    if (compareSpacialValue(hgField, value, "COUNTRY")) {
                                        flag = true;
                                    }
                                }else if (Arrays.asList(new String[]{"decPrice", "decTotal", "qty", "qty1", "qty2"}).contains(column)) {
                                    if (columnCompareBigDecimal(value, hgField)) {
                                        flag = true;
                                    }
                                } else {
                                    if (columnCompare(value, hgField)) {
                                        flag = true;
                                    }
                                }
                                if (flag) {
                                    EntryCompareDetail detail = (EntryCompareDetail) detailTemplate.clone();
                                    detail.setEmsListNo(decIEntryHead.getEmsListNo());
                                    detail.setHeadId(decIEntryList.getSid());
                                    detail.setFieldName(columnName);
                                    detail.setGwField(value);
                                    detail.setHgField(hgField);
                                    detail.setGNo(decIEntryList.getSerialNo().toString());
                                    detail.setSid(UUID.randomUUID().toString());
                                    detail.setInsertUser(decIEntryHead.getInsertUser());
                                    entryCompareDetails.add(detail);
                                }
                            }
                        }
                    }
                }
            }

        }
        return entryCompareDetails;
    }

    public List<EntryCompareDetail> entryEListCompare(GwstdEntryHead gwstdEntryHead, DecEEntryHead decEEntryHead) throws CloneNotSupportedException, IllegalAccessException {
        List<EntryCompareDetail> entryCompareDetails = new ArrayList<>();
        if (gwstdEntryHead != null && decEEntryHead != null) {

            GwstdEntryList gwstdEntryListPara = new GwstdEntryList();
            gwstdEntryListPara.setSeqNo(gwstdEntryHead.getSeqNo());
            List<GwstdEntryList> gwstdEntryLists = gwstdEntryListMapper.getList(gwstdEntryListPara);

            DecEEntryList decEEntryListPara = new DecEEntryList();
            decEEntryListPara.setHeadId(decEEntryHead.getSid());
            decEEntryListPara.setTradeCode(decEEntryHead.getTradeCode());
            List<DecEEntryList> decEEntryLists = decEEntryListMapper.selectListByParam(decEEntryListPara);

            EntryCompareDetail detailTemplate = new EntryCompareDetail();
            {
                detailTemplate.setSeqNo(gwstdEntryHead.getSeqNo());
                detailTemplate.setIEMark(CommonVariable.IE_MARK_E);
                detailTemplate.setTradeCode(gwstdEntryHead.getTradeCode());
                detailTemplate.setInsertTime(new Date());
                detailTemplate.setInsertUser("GW");
                detailTemplate.setDeclareDate(gwstdEntryHead.getEntryDeclareDate());
                detailTemplate.setEntryNo(gwstdEntryHead.getEntryNo());
                detailTemplate.setTradeMode(gwstdEntryHead.getTradeMode());
                detailTemplate.setTrafMode(gwstdEntryHead.getTrafMode());
                detailTemplate.setDataType(ConstantsStatus.STATUS_2);
                detailTemplate.setAgentCode(gwstdEntryHead.getAgentCode() + "|" + gwstdEntryHead.getAgentName());
            }

            if (CollectionUtils.isEmpty(decEEntryLists)) {
                EntryCompareDetail detail = (EntryCompareDetail) detailTemplate.clone();
                detail.setSid(UUID.randomUUID().toString());
                entryCompareDetails.add(detail);
                return entryCompareDetails;
            }
            if ((CollectionUtils.isNotEmpty(gwstdEntryLists) ? gwstdEntryLists.size() : 0) > decEEntryLists.size()) {
                Map<String, List<DecEEntryList>> decEMap = decEEntryLists.stream().filter(e ->
                        e.getSerialNo() != null).collect(Collectors.groupingBy(e -> e.getSerialNo().toString()));
                for (GwstdEntryList gwstdEntryList : gwstdEntryLists) {
                    if (!decEMap.containsKey(gwstdEntryList.getGno() == null ? "" : gwstdEntryList.getGno().toString())) {
                        EntryCompareDetail detail = (EntryCompareDetail) detailTemplate.clone();
                        detail.setGNo(gwstdEntryList.getGno() == null ? "" : gwstdEntryList.getGno().toString());
                        detail.setHeadId(gwstdEntryList.getSid());
                        detail.setSid(UUID.randomUUID().toString());
                        detail.setGwField(xdoi18n.XdoI18nUtil.t("无内部草单数据"));
                        entryCompareDetails.add(detail);
                    } else {
                        DecEEntryList decEEntryList = decEMap.get(gwstdEntryList.getGno().toString()).get(0);
                        Map<String, KeyValuePair> decMap = getDataToMap(decEEntryList);
                        Class cls = gwstdEntryList.getClass();
                        Field[] fields = cls.getDeclaredFields();
                        for (Field field : fields) {
                            field.setAccessible(true);
                            Compare compare = field.getAnnotation(Compare.class);
                            if (compare != null) {
                                String value = "";
                                value = field.get(gwstdEntryList) == null ? "" : field.get(gwstdEntryList).toString();
                                String column = compare.column();
                                String columnName = decMap.get(column).getKey().toString();
                                String gwField = decMap.get(column).getValue() == null ? "" : decMap.get(column).getValue().toString();
                                boolean flag = false;
                                if ("curr".equals(column)) {
                                    if (compareSpacialValue(value, gwField, "CURR")) {
                                        flag = true;
                                    }
                                } else if (Arrays.asList(new String[]{"destinationCountry", "originCountry"}).contains(column)) {
                                    if (compareSpacialValue(value, gwField, "COUNTRY")) {
                                        flag = true;
                                    }
                                }else if (Arrays.asList(new String[]{"decPrice", "decTotal", "qty", "qty1", "qty2"}).contains(column)) {
                                    if (columnCompareBigDecimal(value, gwField)) {
                                        flag = true;
                                    }
                                }  else {
                                    if (columnCompare(value, gwField)) {
                                        flag = true;
                                    }
                                }
                                if (flag) {
                                    EntryCompareDetail detail = (EntryCompareDetail) detailTemplate.clone();
                                    detail.setEmsListNo(decEEntryHead.getEmsListNo());
                                    detail.setHeadId(gwstdEntryList.getSid());
                                    detail.setFieldName(columnName);
                                    detail.setGwField(gwField);
                                    detail.setGNo(gwstdEntryList.getGno().toString());
                                    detail.setHgField(value);
                                    detail.setSid(UUID.randomUUID().toString());
                                    detail.setInsertUser(decEEntryHead.getInsertUser());
                                    entryCompareDetails.add(detail);
                                }
                            }
                        }
                    }
                }
            } else {
                Map<String, List<GwstdEntryList>> gwEntryMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(gwstdEntryLists)) {
                    gwEntryMap = gwstdEntryLists.stream().filter(e -> e.getGno() != null).collect(Collectors.groupingBy(e -> e.getGno().toString()));
                }
                for (DecEEntryList decEEntryList : decEEntryLists) {
                    if (!gwEntryMap.containsKey(decEEntryList.getSerialNo() == null ? "" : decEEntryList.getSerialNo().toString())) {
                        EntryCompareDetail detail = (EntryCompareDetail) detailTemplate.clone();
                        detail.setGNo(decEEntryList.getSerialNo() == null ? "" : decEEntryList.getSerialNo().toString());
                        detail.setHeadId(decEEntryList.getSid());
                        detail.setEmsListNo(decEEntryHead.getEmsListNo());
                        detail.setSid(UUID.randomUUID().toString());
                        detail.setInsertUser(decEEntryHead.getInsertUser());
                        detail.setHgField(xdoi18n.XdoI18nUtil.t("无海关报关数据"));
                        detail.setInsertUser(decEEntryList.getInsertUser());
                        entryCompareDetails.add(detail);
                    } else {
                        GwstdEntryList gwstdEntryList = gwEntryMap.get(decEEntryList.getSerialNo().toString()).get(0);
                        Map<String, KeyValuePair> decMap = getDataToMap(gwstdEntryList);
                        Class cls = decEEntryList.getClass();
                        Field[] fields = cls.getDeclaredFields();
                        for (Field field : fields) {
                            field.setAccessible(true);
                            Compare compare = field.getAnnotation(Compare.class);
                            if (compare != null) {
                                String value = "";
                                value = field.get(decEEntryList) == null ? "" : field.get(decEEntryList).toString();
                                String column = compare.column();
                                String columnName = compare.columnName();
                                String hgField = decMap.get(column).getValue() == null ? "" : decMap.get(column).getValue().toString();
                                boolean flag = false;
                                if ("curr".equals(column)) {
                                    if (compareSpacialValue(hgField, value, "CURR")) {
                                        flag = true;
                                    }
                                } else if (Arrays.asList(new String[]{"destinationCountry", "originCountry"}).contains(column)) {
                                    if (compareSpacialValue(hgField, value, "COUNTRY")) {
                                        flag = true;
                                    }
                                }else if (Arrays.asList(new String[]{"decPrice", "decTotal", "qty", "qty1", "qty2"}).contains(column)) {
                                    if (columnCompareBigDecimal(value, hgField)) {
                                        flag = true;
                                    }
                                } else {
                                    if (columnCompare(value, hgField)) {
                                        flag = true;
                                    }
                                }
                                if (flag) {
                                    EntryCompareDetail detail = (EntryCompareDetail) detailTemplate.clone();
                                    detail.setEmsListNo(decEEntryHead.getEmsListNo());
                                    detail.setHeadId(decEEntryList.getSid());
                                    detail.setFieldName(columnName);
                                    detail.setGwField(value);
                                    detail.setGNo(decEEntryList.getSerialNo().toString());
                                    detail.setHgField(hgField);
                                    detail.setSid(UUID.randomUUID().toString());
                                    detail.setInsertUser(decEEntryHead.getInsertUser());
                                    entryCompareDetails.add(detail);
                                }
                            }
                        }
                    }
                }
            }
        }
        return entryCompareDetails;
    }

    public boolean compareSpacialValue(String hgFiled, String gwFiled, String type) {
        if ((StringUtils.isEmpty(hgFiled) && StringUtils.isNotEmpty(gwFiled))
                || (StringUtils.isNotEmpty(hgFiled) && StringUtils.isEmpty(gwFiled))) {
            return true;
        } else if (StringUtils.isNotEmpty(gwFiled) && StringUtils.isNotEmpty(hgFiled)) {
            if ("CURR".equals(type)) {
                hgFiled = currCode.containsKey(hgFiled) ? currCode.get(hgFiled) : hgFiled;
                if (!gwFiled.equals(hgFiled)) {
                    return true;
                }
            } else if ("COUNTRY".equals(type)) {
                hgFiled = countryCode.containsKey(hgFiled) ? countryCode.get(hgFiled) : hgFiled;
                if (!gwFiled.equals(hgFiled)) {
                    return true;
                }
            }
        }
        return false;
    }

    public void initCurrAndCountry() {
        Optional<List<Map<String, String>>> pcodeCurr = pCodeHolder.getTypeList(PCodeType.CURR_ALL);
        List<Map<String, String>> currList = pcodeCurr.get();
        if (CollectionUtils.isNotEmpty(currList)) {
            for (Map<String, String> currMap : currList) {
                currCode.put(currMap.get("CODE"), currMap.get("CODE_NUM"));
            }
        }
        Optional<List<Map<String, String>>> pcodeCountry = pCodeHolder.getTypeList(PCodeType.COUNTRY_ALL);
        List<Map<String, String>> countryList = pcodeCountry.get();
        if (CollectionUtils.isNotEmpty(countryList)) {
            for (Map<String, String> currMap : countryList) {
                countryCode.put(currMap.get("CODE"), currMap.get("CODE_NUM"));
            }
        }
    }
}