<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.gwLvCitesBatchFileMain.dao.GwLvCitesBatchFileMainMapper">
    <resultMap id="gwLvCitesBatchFileMainResultMap" type="com.dcjet.cs.gwLvCitesBatchFileMain.model.GwLvCitesBatchFileMain">
		<result column="out_side_no" property="outSideNo" jdbcType="VARCHAR" />
		<result column="out_side_proof_valid" property="outSideProofValid" jdbcType="TIMESTAMP" />
		<result column="is_quota" property="isQuota" jdbcType="VARCHAR" />
		<result column="transport_no" property="transportNo" jdbcType="VARCHAR" />
        <result column="customs_type" property="customsType" jdbcType="VARCHAR" />
        <result column="batch_file_no" property="batchFileNo" jdbcType="VARCHAR" />
		<result column="batch_file_punish_date" property="batchFilePunishDate" jdbcType="TIMESTAMP" />
        <result column="batch_file_valid_date" property="batchFileValidDate" jdbcType="TIMESTAMP" />
		<result column="week" property="week" jdbcType="VARCHAR" />
        <result column="is_open" property="isOpen" jdbcType="VARCHAR" />
        <result column="brandCodeNameAll" property="brandCodeNameAll" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="is_expired" property="isExpired" jdbcType="VARCHAR" />
		<result column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="operate_date" property="operateDate" jdbcType="TIMESTAMP" />
		<result column="brand_code" property="brandCode" jdbcType="VARCHAR" />
		<result column="tr_mark" property="trMark" jdbcType="VARCHAR" />
		<result column="batch_file_serial" property="batchFileSerial" jdbcType="VARCHAR" />
        <result column="create_bill_date" property="createBillDate" jdbcType="TIMESTAMP" />
        <result column="is_expired" property="isExpired" jdbcType="VARCHAR" />
        <result column="statusAll" property="statusAll" jdbcType="VARCHAR" />
        <result column="greelight_create_date" property="greelightCreateDate" jdbcType="TIMESTAMP" />
        <result column="quota_greelight_create_date" property="qtotaQreelightCreateDate" jdbcType="TIMESTAMP" />
	</resultMap>

    <sql id="condition">
    and m.trade_code= #{tradeCode}
    <if test="brandCodes != null">
        and m.brand_code in
        <foreach collection="brandCodes"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </if>
    <if test="batchFileSerial != null and batchFileSerial != ''">
        <if test="isSearchLikeMatch != null and isSearchLikeMatch">
            and m.batch_file_serial like '%'|| #{batchFileSerial} || '%'
        </if>
        <if test="isSearchLikeMatch == null or !isSearchLikeMatch">
            and m.batch_file_serial = #{batchFileSerial}
        </if>
    </if>
    <if test="operateDate != null and operateDate != ''">
        and to_char(m.operate_date,'yyyy-MM-dd') = to_char(to_date(#{operateDate}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
    </if>
    <if test="isExpired != null and isExpired != ''">
        and l.is_expired = #{isExpired}
    </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwLvCitesBatchFileMainResultMap" parameterType="com.dcjet.cs.gwLvCitesBatchFileMain.model.GwLvCitesBatchFileMain">
        select
        l.out_side_no,
        l.out_side_proof_valid,
        l.is_quota,
        l.transport_no,
        l2.customs_type,
        l.batch_file_no,
        l.batch_file_punish_date,
        l.batch_file_valid_date,
        l.week,
        l.status,
        l.is_open,
               l.is_expired,l.statusAll,
        (bb.brand_code || '(' || bb.brand_abbr ||')') brandCodeNameAll,
               l.greelight_create_date,
        l.quota_greelight_create_date,
            m.*
        from
            t_gw_lv_cites_batch_file_main m
        left join t_gw_lv_base_brand bb on bb.brand_code = m.brand_code
        inner join (
            select
                l.head_id,
                string_agg(distinct l.out_side_no,'/') out_side_no,
                min(l.out_side_proof_valid) out_side_proof_valid,
                string_agg(distinct l.is_quota,'/') is_quota,
                string_agg(distinct l.transport_no,'/') transport_no,
                string_agg(distinct l.batch_file_no,'/') batch_file_no,
                max(l.batch_file_punish_date) batch_file_punish_date,
                min(l.batch_file_valid_date) batch_file_valid_date,
                string_agg(distinct l.week,'/')  week,
                (
                    case when to_char(min(l.out_side_proof_valid),'yyyy-MM-dd') &lt; to_char(CURRENT_TIMESTAMP, 'yyyy-MM-dd')
                    or to_char(min(l.batch_file_valid_date),'yyyy-MM-dd') &lt; to_char(CURRENT_TIMESTAMP, 'yyyy-MM-dd')
                    then '已过期' else '未过期' end
                ) is_expired,
                min(l.status) status,
                min(l.is_open) is_open,
                string_agg(distinct l.status,'/') statusAll,
                max(l.greelight_create_date) greelight_create_date,
        max(l.quota_greelight_create_date) quota_greelight_create_date
            from t_gw_lv_cites_batch_file_list l
            where l.trade_code = #{tradeCode}
            <if test="isOpen != null and isOpen != ''">
                and l.is_open = #{isOpen}
            </if>
            <if test="status != null and status != ''">
                and l.status = #{status}
            </if>
            <if test="transportNo != null and transportNo != ''">
                and l.transport_no like '%'|| #{transportNo} || '%'
            </if>
            <if test="isQuota != null and isQuota != ''">
                and l.is_quota = #{isQuota}
            </if>
            <if test="transportNo != null and transportNo != ''">
                and l.transport_no like '%'|| #{transportNo} || '%'
            </if>
            <if test="outSideNo != null and outSideNo != ''">
                and l.out_side_no like '%'|| #{outSideNo} || '%'
            </if>
            <if test="batchFileNo != null and batchFileNo != ''">
                and l.batch_file_no like '%'|| #{batchFileNo} || '%'
            </if>
            <if test="batchFileValidDate != null and batchFileValidDate != ''">
                and to_char(l.batch_file_valid_date,'yyyy-MM-dd') = to_char(to_date(#{batchFileValidDate}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
            </if>
            group by l.head_id
        ) l on l.head_id = m.sid
        left join (
            select
            string_agg(distinct p.name,'/') customs_type,l.head_id
            from t_gw_lv_cites_batch_file_sku_list l
            LEFT JOIN t_gw_lv_customer_params P ON P.code = l.customs_type AND P.TYPE='CUSTOM_TYPE'
            where l.trade_code = #{tradeCode}
            group by l.head_id
        ) l2 on l2.head_id = m.sid
        <where>
            <include refid="condition"></include>
        </where>
        order by m.update_time desc
    </select>

    <select id="getBatchFileDetailByValid" resultMap="gwLvCitesBatchFileMainResultMap" parameterType="com.dcjet.cs.gwLvCitesBatchFileMain.model.GwLvCitesBatchFileMain">
        select
        l.out_side_no,
        l.out_side_proof_valid,
        l.is_quota,
        l.transport_no,
        l.batch_file_no,
        l.batch_file_punish_date,
        l.batch_file_valid_date,
        l.week,
        l.status,
        m.*
        from
        t_gw_lv_cites_batch_file_main m
        inner join t_gw_lv_cites_batch_file_list l on l.head_id = m.sid
        <where>
            <include refid="condition"></include>
            and to_char(l.out_side_proof_valid,'yyyy-MM-dd') >= to_char(CURRENT_TIMESTAMP, 'yyyy-MM-dd')
            and to_char(l.batch_file_valid_date,'yyyy-MM-dd') >= to_char(CURRENT_TIMESTAMP, 'yyyy-MM-dd')
            and l.is_open = 'open'
            <if test="status != null and status != ''">
                and l.status = #{status}
            </if>
            <if test="transportNo != null and transportNo != ''">
                and l.transport_no like '%'|| #{transportNo} || '%'
            </if>
            <if test="batchFileNo != null and batchFileNo != ''">
                and l.batch_file_no like '%'|| #{batchFileNo} || '%'
            </if>
            <if test="isQuota != null and isQuota != ''">
                and l.is_quota = #{isQuota}
            </if>
            <if test="outSideNo != null and outSideNo != ''">
                and l.out_side_no like '%'|| #{outSideNo} || '%'
            </if>
        </where>
        order by l.transport_no,m.batch_file_serial,l.out_side_no
    </select>

    <delete id="deleteBySids" parameterType="java.util.List">
        update t_gw_lv_cites_outcites o set status=0
        from t_gw_lv_cites_batch_file_sku_list l
        where l.head_id in <foreach collection="list"  item="item" open="(" separator="," close=")"  >
                #{item}
            </foreach>
        and o.cop_g_no=l.cop_g_no and o.out_side_no=l.out_side_no;
        delete from t_gw_lv_cites_batch_file_list  where head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        delete from t_gw_lv_cites_batch_file_sku_list  where head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        delete from t_gw_lv_cites_batch_file_main  where SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
    </delete>

    <select id="getSequenceNext" resultType="String">
        select nextval('lv_cites_batch_file')
    </select>
</mapper>
