package com.dcjet.cs.gwLvCitesBatchFileList.mapper;
import com.dcjet.cs.dto.gwLvCitesBatchFileList.GwLvCitesBatchFileListDto;
import com.dcjet.cs.dto.gwLvCitesBatchFileList.GwLvCitesBatchFileListParam;
import com.dcjet.cs.gwLvCitesBatchFileList.model.GwLvCitesBatchFileList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2022-4-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwLvCitesBatchFileListDtoMapper {
    /***
     * 转换DTO到数据库对象
     * @param po
     * @return
     */
    GwLvCitesBatchFileListDto toDto(GwLvCitesBatchFileList po);

    GwLvCitesBatchFileList toDto(GwLvCitesBatchFileListDto po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwLvCitesBatchFileList toPo(GwLvCitesBatchFileListParam param);
    void updatePo(GwLvCitesBatchFileListParam gwLvCitesBatchFileListParam, @MappingTarget GwLvCitesBatchFileList gwLvCitesBatchFileList);
}
