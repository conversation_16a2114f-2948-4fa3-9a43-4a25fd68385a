package com.dcjet.cs.gwLvCitesBatchFileList.service;
import com.dcjet.cs.dto.gwLvCitesBatchFileList.GwLvCitesBatchFileListDto;
import com.dcjet.cs.dto.gwLvCitesBatchFileList.GwLvCitesBatchFileListParam;
import com.dcjet.cs.gwLvCitesBatchFileList.dao.GwLvCitesBatchFileListMapper;
import com.dcjet.cs.gwLvCitesBatchFileList.mapper.GwLvCitesBatchFileListDtoMapper;
import com.dcjet.cs.gwLvCitesBatchFileList.model.GwLvCitesBatchFileList;
import com.dcjet.cs.gwLvCitesProofMain.service.EnumLvDictionary;
import org.apache.commons.collections4.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2022-4-24
 */
@Service
public class GwLvCitesBatchFileListService extends BaseService<GwLvCitesBatchFileList> {
    @Resource
    private GwLvCitesBatchFileListMapper gwLvCitesBatchFileListMapper;
    @Resource
    private GwLvCitesBatchFileListDtoMapper gwLvCitesBatchFileListDtoMapper;
    @Override
    public Mapper<GwLvCitesBatchFileList> getMapper() {
        return gwLvCitesBatchFileListMapper;
    }

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /**
     * 功能描述: grid分页查询
     *
     * @param gwLvCitesBatchFileListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwLvCitesBatchFileListDto>> selectAllPaged(GwLvCitesBatchFileListParam gwLvCitesBatchFileListParam, PageParam pageParam) {
        // 启用分页查询
        GwLvCitesBatchFileList gwLvCitesBatchFileList = gwLvCitesBatchFileListDtoMapper.toPo(gwLvCitesBatchFileListParam);
        Page<GwLvCitesBatchFileList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwLvCitesBatchFileListMapper.getList(gwLvCitesBatchFileList));
        List<GwLvCitesBatchFileListDto> gwLvCitesBatchFileListDtos = page.getResult().stream().map(head -> {
            GwLvCitesBatchFileListDto dto = gwLvCitesBatchFileListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		 ResultObject<List<GwLvCitesBatchFileListDto>> paged = ResultObject.createInstance(gwLvCitesBatchFileListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @return
     */
    public List<GwLvCitesBatchFileListDto> selectAll(GwLvCitesBatchFileListParam exportParam) {
        GwLvCitesBatchFileList gwLvCitesBatchFileList = gwLvCitesBatchFileListDtoMapper.toPo(exportParam);
        List<GwLvCitesBatchFileListDto> gwLvCitesBatchFileListDtos = new ArrayList<>();
        List<GwLvCitesBatchFileList> gwLvCitesBatchFileLists = gwLvCitesBatchFileListMapper.getList(gwLvCitesBatchFileList);
        if (CollectionUtils.isNotEmpty(gwLvCitesBatchFileLists)) {
            gwLvCitesBatchFileListDtos = gwLvCitesBatchFileLists.stream().map(item -> {
                GwLvCitesBatchFileListDto dto = gwLvCitesBatchFileListDtoMapper.toDto(item);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwLvCitesBatchFileListDtos;
    }

    public void updateGreelightCreateDate(String outSideNo,String headSid,UserInfoToken userInfo) {
        GwLvCitesBatchFileList searchPar = new GwLvCitesBatchFileList();
        searchPar.setTradeCode(userInfo.getCompany());
        searchPar.setOutSideNo(outSideNo);
        searchPar.setHeadId(headSid);
        List<GwLvCitesBatchFileList> list = gwLvCitesBatchFileListMapper.getList(searchPar);

        for(GwLvCitesBatchFileList item : list) {
            if (item.getGreelightCreateDate() != null)
                continue;
            item.setUpdateUser(userInfo.getUserNo());
            item.setUpdateTime(new Date());
            item.setGreelightCreateDate(new Date());
            // 更新数据
            gwLvCitesBatchFileListMapper.updateByPrimaryKey(item);
        }
    }

    public void updateQuotaGreelightCreateDate(String outSideNo,String headSid,UserInfoToken userInfo) {
        GwLvCitesBatchFileList searchPar = new GwLvCitesBatchFileList();
        searchPar.setTradeCode(userInfo.getCompany());
        searchPar.setOutSideNo(outSideNo);
        searchPar.setHeadId(headSid);
        List<GwLvCitesBatchFileList> list = gwLvCitesBatchFileListMapper.getList(searchPar);

        for(GwLvCitesBatchFileList item : list) {
            if (item.getQuotaQreelightCreateDate() != null)
                continue;
            item.setUpdateUser(userInfo.getUserNo());
            item.setUpdateTime(new Date());
            item.setQuotaQreelightCreateDate(new Date());
            // 更新数据
            gwLvCitesBatchFileListMapper.updateByPrimaryKey(item);
        }
    }

    /**
     * 获取生成greenlight数据
     * @param headSids
     * @param tradeCode
     * @return
     */
    public List<GwLvCitesBatchFileListDto> createGreenLight(List<String> headSids,List<String> transportNo,String tradeCode,String userNo) {
        GwLvCitesBatchFileList gwLvCitesBatchFileList = new GwLvCitesBatchFileList();
        gwLvCitesBatchFileList.setHeadSids(headSids);
        gwLvCitesBatchFileList.setTransportNos(transportNo);
        gwLvCitesBatchFileList.setTradeCode(tradeCode);

        List<GwLvCitesBatchFileListDto> gwLvCitesBatchFileListDtos = new ArrayList<>();
        List<GwLvCitesBatchFileList> gwLvCitesBatchFileLists = gwLvCitesBatchFileListMapper.getList(gwLvCitesBatchFileList);
        if (gwLvCitesBatchFileLists != null && gwLvCitesBatchFileLists.size() > 0) {
            Date greelightCreateDate = new Date();
            for(GwLvCitesBatchFileList dto : gwLvCitesBatchFileLists) {
                if(dto.getGreelightCreateDate() != null) {
                    greelightCreateDate = dto.getGreelightCreateDate();
                    break;
                }
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for(GwLvCitesBatchFileList item : gwLvCitesBatchFileLists) {
                GwLvCitesBatchFileListDto dto = gwLvCitesBatchFileListDtoMapper.toDto(item);
                //CITES编号，取前2位，如不是FR；取3、4位，如不是SG，给空值
                if(dto.getOutSideNo().length()>2 && dto.getOutSideNo().substring(0,2).equals("FR"))
                    dto.setCertificateFrSgp("FR");
                if(dto.getOutSideNo().length() > 4 && dto.getOutSideNo().substring(2,4).equals("SG"))
                    dto.setCertificateFrSgp("SG");

                if(greelightCreateDate != null) {
                    Calendar date = Calendar.getInstance();
//                    date.setTime(item.getBatchFilePunishDate());
                    date.setTime(greelightCreateDate);
                    date.add(Calendar.DAY_OF_WEEK, 7);
                    dto.setEstimatedPermitDate(sdf.format(date.getTime()));

                    dto.setBatchFilePunishDateStr(sdf.format(item.getBatchFilePunishDate()));
                }
                gwLvCitesBatchFileListDtos.add(dto);
            };
        }
        return gwLvCitesBatchFileListDtos;
    }


    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public GwLvCitesBatchFileListDto insert(GwLvCitesBatchFileListParam model, UserInfoToken userInfo) {
        String sid = UUID.randomUUID().toString();
        GwLvCitesBatchFileList gwLvCitesBatchFileList = gwLvCitesBatchFileListDtoMapper.toPo(model);
        gwLvCitesBatchFileList.setSid(sid);
        gwLvCitesBatchFileList.setInsertUser(userInfo.getUserNo());
        gwLvCitesBatchFileList.setInsertTime(new Date());
        gwLvCitesBatchFileList.setUpdateUser(userInfo.getUserNo());
        gwLvCitesBatchFileList.setUpdateTime(new Date());
        gwLvCitesBatchFileList.setStatus("0");
        gwLvCitesBatchFileList.setIsOpen("open");

//        String batchFileNo = createBatchFileNo();
//        gwLvCitesBatchFileList.setBatchFileNo(batchFileNo);

        int insertStatus = gwLvCitesBatchFileListMapper.insert(gwLvCitesBatchFileList);
       return insertStatus > 0 ? gwLvCitesBatchFileListDtoMapper.toDto(gwLvCitesBatchFileList) : null;
    }

    /**
     * 生成流水号
     * 流水号规则：品牌简称+CITES+YYYY+4位流  如：GUE-CITES-2021-0174
     * @return
     */
    public String createBatchFileNo() {
        String serialNumber = "";

        //获取流水号
        serialNumber = gwLvCitesBatchFileListMapper.getSequenceNext();
        if(StringUtils.isEmpty(serialNumber))
            serialNumber = "1";
        serialNumber = String.format("%04d",Integer.parseInt(serialNumber));

        return String.format("GUE-CITES-%s-%s",Calendar.getInstance().getWeekYear(),serialNumber);
    }

    public List<Pair<String,String>> checkData(GwLvCitesBatchFileListParam par, EnumLvDictionary.OperateType operateType) {
        List<Pair<String,String>> result = new ArrayList();

        if(par.getIsQuota().equals("0")) {
            //QUOTA为“否”的，同一批文流水号下，只有一个TRNASPORT NO
            if(!StringUtils.isEmpty(par.getTransportNo())) {
                GwLvCitesBatchFileListParam searchPar = new GwLvCitesBatchFileListParam();
                searchPar.setTradeCode(par.getTradeCode());
                searchPar.setNotTransportNo(par.getTransportNo());
                searchPar.setHeadId(par.getHeadId());
                List<GwLvCitesBatchFileListDto> listDto = selectAll(searchPar);
                if(listDto != null && listDto.size() > 0) {
                    result.add(Pair.of("errorMess","quota为否时，Transport NO不能维护多个！"));
                }
            }
        }


        return result;
    }

    /**
     * 功能描述:修改
     *
     * @param gwLvCitesBatchFileListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwLvCitesBatchFileListDto update(GwLvCitesBatchFileListParam gwLvCitesBatchFileListParam, UserInfoToken userInfo) {
        //获取表体
        GwLvCitesBatchFileList gwLvCitesBatchFileList = gwLvCitesBatchFileListMapper.selectByPrimaryKey(gwLvCitesBatchFileListParam.getSid());
        //更新表体
        gwLvCitesBatchFileListDtoMapper.updatePo(gwLvCitesBatchFileListParam, gwLvCitesBatchFileList);

        return update(gwLvCitesBatchFileList,userInfo);
    }

    public GwLvCitesBatchFileListDto update(GwLvCitesBatchFileList gwLvCitesBatchFileList, UserInfoToken userInfo) {
        gwLvCitesBatchFileList.setUpdateUser(userInfo.getUserNo());
        gwLvCitesBatchFileList.setUpdateTime(new Date());

        //已维护相关信息，则修改状态
        if(gwLvCitesBatchFileList.getIsQuota().equals("0")) {
            if(!StringUtils.isEmpty(gwLvCitesBatchFileList.getBatchFileNo()) && gwLvCitesBatchFileList.getBatchFileValidDate() != null
                    && gwLvCitesBatchFileList.getBatchFilePunishDate() != null && !StringUtils.isEmpty(gwLvCitesBatchFileList.getTransportNo())) {
                //单证状态 0-暂存，1-批文办结，2-已使用
                gwLvCitesBatchFileList.setStatus("1");
            }
        } else {
            if(!StringUtils.isEmpty(gwLvCitesBatchFileList.getBatchFileNo())
                    && gwLvCitesBatchFileList.getBatchFileValidDate() != null
                    && gwLvCitesBatchFileList.getBatchFilePunishDate() != null) {
                //单证状态 0-暂存，1-批文办结，2-已使用
                gwLvCitesBatchFileList.setStatus("1");
            }
        }

        // 更新数据
        gwLvCitesBatchFileListMapper.updateByPrimaryKey(gwLvCitesBatchFileList);
        return gwLvCitesBatchFileListDtoMapper.toDto(gwLvCitesBatchFileList);
    }

    /**
     * 批文办结维护保存
     * @param Listpar
     * @param userInfo
     * @return
     */
    public List<Pair<String, String>> batchFilePunishSave(List<GwLvCitesBatchFileListParam> Listpar,String saveType,UserInfoToken userInfo) {

        List<Pair<String, String>> resultCheck = new ArrayList();
        for (GwLvCitesBatchFileListParam par : Listpar) {
            try {
                GwLvCitesBatchFileList gwLvCitesBatchFileList = new GwLvCitesBatchFileList();
                gwLvCitesBatchFileList.setHeadId(par.getHeadId());
                //TransportNO:2 BatchFile:1
                if(saveType.equals("1"))
                    gwLvCitesBatchFileList.setExcludeStatus(Arrays.asList("2"));
                List<GwLvCitesBatchFileList> gwLvCitesBatchFileLists = gwLvCitesBatchFileListMapper.getList(gwLvCitesBatchFileList);
                if(gwLvCitesBatchFileLists.size() <= 0) {
                    resultCheck.add(Pair.of("errorMess",par.getOutSideNo().concat(",未找到可更新的数据！")));
                    continue;
                }
                for(GwLvCitesBatchFileList item : gwLvCitesBatchFileLists) {
                    if(!StringUtils.isEmpty(par.getTransportNo()))
                        item.setTransportNo(par.getTransportNo());
                    if(!StringUtils.isEmpty(par.getBatchFileNo()))
                        item.setBatchFileNo(par.getBatchFileNo());
                    if(par.getBatchFilePunishDate() != null)
                        item.setBatchFilePunishDate(par.getBatchFilePunishDate());
                    if(par.getBatchFileValidDate() != null)
                        item.setBatchFileValidDate(par.getBatchFileValidDate());

                    update(item, userInfo);
                }
            } catch (Exception t) {
                logger.error("batchFilePunishSave", t);
                resultCheck.add(Pair.of("errorMess",t.getMessage()));
                return resultCheck;
            }
        }
        return resultCheck;
    }


    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids, UserInfoToken userInfo) {
        gwLvCitesBatchFileListMapper.deleteBySids(sids);
    }
	public int getListNumByHeadIds(List<String> sids) {
        return gwLvCitesBatchFileListMapper.getListNumByHeadIds(sids);
    }
}
