package com.dcjet.cs.erp.service.sap;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.erp.sap.ErpExgStockDto;
import com.dcjet.cs.dto.erp.sap.ErpExgStockParam;
import com.dcjet.cs.erp.dao.sap.ErpExgStockMapper;
import com.dcjet.cs.erp.mapper.sap.ErpExgStockDtoMapper;
import com.dcjet.cs.erp.model.sap.ErpExgStock;
import com.dcjet.cs.util.CommonEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 成品库存异步导出
 * @author: WJ
 * @createDate: 2020/9/14 9:35
 */
@Component
public class ExportExgStockService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private ErpExgStockMapper erpExgStockMapper;

    @Resource
    private ErpExgStockDtoMapper erpExgStockDtoMapper;

    @Resource
    private PCodeHolder pCodeHolder;

    private final String taskName = xdoi18n.XdoI18nUtil.t("成品库存异步导出(EXG_STOCK)");

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("EXG_STOCK");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        ErpExgStockParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        ErpExgStock stock = erpExgStockDtoMapper.toPo(exportParam);
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(stock));
        Integer count = erpExgStockMapper.selectDataCount(stock);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        ErpExgStockParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        ErpExgStock stock = erpExgStockDtoMapper.toPo(exportParam);
        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(stock));
        Page<ErpExgStock> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> erpExgStockMapper.getList(stock));

        List<ErpExgStockDto> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            dtos = page.getResult().stream().map(head -> {
                ErpExgStockDto dto = erpExgStockDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(dtos));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    private List<ErpExgStockDto> convertForPrint(List<ErpExgStockDto> list) {
        for (ErpExgStockDto item : list) {
            if (StringUtils.isNotBlank(item.getStatus())) {
                item.setStatus(item.getStatus() + " " + CommonEnum.takeStatusEnum.getValue(item.getStatus()));
            }
            if (StringUtils.isNotBlank(item.getModifyMark())) {
                item.setModifyMark(CommonEnum.modifyMarkEnum.getValue(item.getModifyMark()));
            }
            if(StringUtils.isNotBlank(item.getBondMark())) {
                item.setBondMark(CommonEnum.BondMarkEnum.getValue(item.getBondMark()));
            }
            if(item.getUnit() != null) {
                String unitText = pCodeHolder.getValue(PCodeType.UNIT, item.getUnit());
                if(unitText != null) {
                    item.setUnit(item.getUnit() + " " + unitText);
                }
            }
            if (item.getCurr() != null) {
                String currText = pCodeHolder.getValue(PCodeType.CURR, item.getCurr());
                if(currText != null) {
                    item.setCurr(item.getCurr() + " " + currText);
                }
            }
        }
        return list;
    }

    private ErpExgStockParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        ErpExgStockParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, ErpExgStockParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
