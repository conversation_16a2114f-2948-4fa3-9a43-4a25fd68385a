package com.dcjet.cs.erp.mapper;

import com.dcjet.cs.dto.erp.ManifestInfoDto;
import com.dcjet.cs.dto.erp.ManifestInfoParam;
import com.dcjet.cs.erp.model.ManifestInfo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-11-1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ManifestInfoDtoMapper {
    /***
     * 转换数据库对象到 DTO
     * @param po
     * @return
     */
    ManifestInfoDto toDto(ManifestInfo po);

    /***
     * 转换 DTO 到数据库对象
     * @param param
     * @return
     */
    ManifestInfo toPo(ManifestInfoParam param);

    /**
     * 数据库原始数据更新
     *
     * @param gwManifestInfoParam
     * @param gwManifestInfo
     */
    void updatePo(ManifestInfoParam gwManifestInfoParam, @MappingTarget ManifestInfo gwManifestInfo);

    default void patchPo(ManifestInfoParam gwManifestInfoParam, ManifestInfo gwManifestInfo) {
        // TODO 自行实现局部更新
    }
}
