package com.dcjet.cs.erp.service;

import com.dcjet.cs.dto.erp.DecErpQtyCalcRecordDto;
import com.dcjet.cs.dto.erp.DecErpQtyCalcRecordParam;
import com.dcjet.cs.erp.dao.DecErpQtyCalcRecordMapper;
import com.dcjet.cs.erp.mapper.DecErpQtyCalcRecordDtoMapper;
import com.dcjet.cs.erp.model.DecErpQtyCalcRecord;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-24
 */
@Service
public class DecErpQtyCalcRecordService extends BaseService<DecErpQtyCalcRecord> {
    @Resource
    private DecErpQtyCalcRecordMapper decErpQtyCalcRecordMapper;
    @Resource
    private DecErpQtyCalcRecordDtoMapper decErpQtyCalcRecordDtoMapper;
    @Override
    public Mapper<DecErpQtyCalcRecord> getMapper() {
        return decErpQtyCalcRecordMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param decErpQtyCalcRecordParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<DecErpQtyCalcRecordDto>> getListPaged(DecErpQtyCalcRecordParam decErpQtyCalcRecordParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        DecErpQtyCalcRecord decErpQtyCalcRecord = decErpQtyCalcRecordDtoMapper.toPo(decErpQtyCalcRecordParam);
        decErpQtyCalcRecord.setTradeCode(userInfo.getCompany());
        Page<DecErpQtyCalcRecord> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decErpQtyCalcRecordMapper.getList(decErpQtyCalcRecord));
        List<DecErpQtyCalcRecordDto> decErpQtyCalcRecordDtos = page.getResult().stream().map(head -> {
            DecErpQtyCalcRecordDto dto = decErpQtyCalcRecordDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<DecErpQtyCalcRecordDto>> paged = ResultObject.createInstance(decErpQtyCalcRecordDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param decErpQtyCalcRecordParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecErpQtyCalcRecordDto insert(DecErpQtyCalcRecordParam decErpQtyCalcRecordParam, UserInfoToken userInfo) {
        DecErpQtyCalcRecord decErpQtyCalcRecord = decErpQtyCalcRecordDtoMapper.toPo(decErpQtyCalcRecordParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        decErpQtyCalcRecord.setTradeCode(userInfo.getCompany());
        decErpQtyCalcRecord.setSid(sid);
        decErpQtyCalcRecord.setInsertUser(userInfo.getUserNo());
        decErpQtyCalcRecord.setInsertTime(new Date());
        // 新增数据
        int insertStatus = decErpQtyCalcRecordMapper.insert(decErpQtyCalcRecord);
        return  insertStatus > 0 ? decErpQtyCalcRecordDtoMapper.toDto(decErpQtyCalcRecord) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param decErpQtyCalcRecordParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecErpQtyCalcRecordDto update(DecErpQtyCalcRecordParam decErpQtyCalcRecordParam, UserInfoToken userInfo) {
        DecErpQtyCalcRecord decErpQtyCalcRecord = decErpQtyCalcRecordMapper.selectByPrimaryKey(decErpQtyCalcRecordParam.getSid());
        decErpQtyCalcRecordDtoMapper.updatePo(decErpQtyCalcRecordParam, decErpQtyCalcRecord);
        decErpQtyCalcRecord.setUpdateUser(userInfo.getUserNo());
        decErpQtyCalcRecord.setUpdateTime(new Date());
        // 更新数据
        int update = decErpQtyCalcRecordMapper.updateByPrimaryKey(decErpQtyCalcRecord);
        return update > 0 ? decErpQtyCalcRecordDtoMapper.toDto(decErpQtyCalcRecord) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
		decErpQtyCalcRecordMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecErpQtyCalcRecordDto> selectAll(DecErpQtyCalcRecordParam exportParam, UserInfoToken userInfo) {
        DecErpQtyCalcRecord decErpQtyCalcRecord = decErpQtyCalcRecordDtoMapper.toPo(exportParam);
        decErpQtyCalcRecord.setTradeCode(userInfo.getCompany());
        List<DecErpQtyCalcRecordDto> decErpQtyCalcRecordDtos = new ArrayList<>();
        List<DecErpQtyCalcRecord> decErpQtyCalcRecords = decErpQtyCalcRecordMapper.getList(decErpQtyCalcRecord);
        if (CollectionUtils.isNotEmpty(decErpQtyCalcRecords)) {
            decErpQtyCalcRecordDtos = decErpQtyCalcRecords.stream().map(head -> {
                DecErpQtyCalcRecordDto dto = decErpQtyCalcRecordDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decErpQtyCalcRecordDtos;
    }
}
