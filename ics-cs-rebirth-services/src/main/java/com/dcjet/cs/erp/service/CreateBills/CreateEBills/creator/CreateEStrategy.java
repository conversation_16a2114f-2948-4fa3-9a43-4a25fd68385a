package com.dcjet.cs.erp.service.CreateBills.CreateEBills.creator;

import com.dcjet.cs.entry.model.DecEEntryList;
import com.dcjet.cs.erp.model.DecEBillList;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpEListN;

import java.util.List;

/**
 * CreateModelStrategy
 *
 * <AUTHOR>
 * @date: 2020-5-29
 */
public interface CreateEStrategy {
    /**
     * 清单生成模式
     *
     * @return
     */
    String createModel();

    /**
     * 生成清单具体实现
     */
    void create(Integer maxBillIdx, DecErpEHeadN erpHead, List<DecErpEListN> erpList, List<DecEBillList> billList, List<DecEEntryList> entryList);
}
