package com.dcjet.cs.erp.model.sap;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户对账单
 */
@Setter
@Getter
@Table(name = "T_SAP_TAX_CIP")
public class ErpTaxCip implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业代码
     **/
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 创建人
     **/
    @Column(name = "INSERT_USER")
    private String insertUser;

    /**
     * 创建时间
     **/
    @Column(name = "INSERT_TIME")
    private Date insertTime;

    /**
     * 更新人
     **/
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 更新时间
     **/
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    @Column(name = "SID")
    @Id
    private String sid;

    @Column(name = "M_DATE")
    private Date MDate;//日期

    @Column(name = "BILL_NO")
    private String billNo;//发运单号

    @Column(name = "CUST_ID")
    private String custId;//客户代码

    @Column(name = "PO")
    private String po;

    @Column(name = "C_COP_G_NO")
    private String CCopGNo;

    @Column(name = "C_G_NO")
    private BigDecimal CGNo;

    @Column(name = "G_MARK")
    private String GMark;

    @Column(name = "QTY1")
    private BigDecimal qty1;

    @Column(name = "UNIT")
    private String unit;

    @Column(name = "NET")
    private BigDecimal net;

    @Column(name = "GROSS")
    private BigDecimal gross;

    @Column(name = "DEC_TOTAL")
    private BigDecimal decTotal;

    @Column(name = "DEC_CURR")
    private String decCurr;

    @Column(name = "SAP_COP_G_NO")
    private String sapCopGNo;

    @Column(name = "COP_G_NO")
    private String copGNo;

    @Column(name = "QTY2")
    private BigDecimal qty2;

    @Column(name = "VERSION_NO")
    private String versionNo;

    @Column(name = "SYNC_BATCH_NO")
    @JsonIgnore
    private String syncBatchNo;
}
