package com.dcjet.cs.erp.model.sap;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@Table(name = "T_ERP_DEC_E_HEAD")
public class ErpDecEHead implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 同步批次号
     */
    @Column(name = "SYNC_BATCH_NO")
    @JsonIgnore
    private String syncBatchNo;
    @Column(name = "STATUS")
    private String status;
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 单据号 关联提单编号
     */
    @Column(name = "LINKED_NO")
    private String linkedNo;
    /**
     * 单据内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;
    /**
     * 出货日期
     **/
    @Column(name = "SHIP_DATE")
    private Date shipDate;
    /**
     * 货运代理代码
     **/
    @Column(name = "FORWARD_CODE")
    private String forwardCode;

    /**
     * 发票号
     **/
    @Column(name = "INVOICE_NO")
    private String invoiceNo;

    /**
     * 合同协议号
     **/
    @Column(name = "CONTR_NO")
    private String contrNo;

    /**
     * 包装种类
     **/
    @Column(name = "WRAP_TYPE")
    private String wrapType;

    /**
     * 件数
     **/
    @Column(name = "PACK_NUM")
    private BigDecimal packNum;

    /**
     * 总净重
     **/
    @Column(name = "NET_WT")
    private BigDecimal netWt;

    /**
     * 总毛重
     **/
    @Column(name = "GROSS_WT")
    private BigDecimal grossWt;

    /**
     * 体积
     **/
    @Column(name = "VOLUME")
    private BigDecimal volume;

    /**
     * 运输方式
     **/
    @Column(name = "TRAF_MODE")
    private String trafMode;

    /**
     * 境内货源地
     **/
    @Column(name = "DISTRICT_CODE")
    private String districtCode;

    /**
     * 境内货源地行政区划
     **/
    @Column(name = "DISTRICT_POST_CODE")
    private String districtPostCode;

    /**
     * 贸易国别
     **/
    @Column(name = "TRADE_NATION")
    private String tradeNation;

    /**
     * 运抵国（地区）
     **/
    @Column(name = "TRADE_COUNTRY")
    private String tradeCountry;

    /**
     * 指运港
     **/
    @Column(name = "DEST_PORT")
    private String destPort;

    /**
     * 出境关别
     **/
    @Column(name = "IE_PORT")
    private String IEPort;

    /**
     * 离境口岸
     **/
    @Column(name = "ENTRY_PORT")
    private String entryPort;

    /**
     * 最终目的国
     **/
    @Column(name = "DESTINATION_COUNTRY")
    private String destinationCountry;

    /**
     * 申报单位代码
     **/
    @Column(name = "DECLARE_CODE")
    private String declareCode;

    /**
     * 社会信用代码
     **/
    @Column(name = "TRADE_CREDIT_CODE")
    private String tradeCreditCode;

    /**
     * 申报单位名称
     **/
    @Column(name = "DECLARE_NAME")
    private String declareName;

    /**
     * 备案号
     **/
    @Column(name = "EMS_NO")
    private String emsNo;

    /**
     * 监管方式
     **/
    @Column(name = "TRADE_MODE")
    private String tradeMode;

    /**
     * 征免性质
     **/
    @Column(name = "CUT_MODE")
    private String cutMode;

    /**
     * 许可证号
     **/
    @Column(name = "LICENSE_NO")
    private String licenseNo;

    /**
     * 报关单归并类型
     **/
    @Column(name = "MERGE_TYPE")
    private String mergeType;

    /**
     * 航班日期
     **/
    @Column(name = "VOYAGE_DATE")
    private Date voyageDate;

    /**
     * 提运单号
     **/
    @Column(name = "MAWB")
    private String mawb;

    /**
     * 分提运单
     **/
    @Column(name = "HAWB")
    private String hawb;

    /**
     * 运输工具名称
     **/
    @Column(name = "TRAF_NAME")
    private String trafName;

    /**
     * 航次号
     **/
    @Column(name = "VOYAGE_NO")
    private String voyageNo;

    /**
     * 境外发货人代码
     **/
    @Column(name = "OVERSEAS_SHIPPER")
    private String overseasShipper;

    /**
     * 境内发货人代码
     **/
    @Column(name = "RECEIVE_CODE")
    private String receiveCode;

    /**
     * 境内发货人名称
     **/
    @Column(name = "RECEIVE_NAME")
    private String receiveName;

    /**
     * 申报地海关
     **/
    @Column(name = "MASTER_CUSTOMS")
    private String masterCustoms;

    /**
     * 出口日期
     **/
    @Column(name = "IE_DATE")
    private Date ieDate;

    /**
     * 成交方式
     **/
    @Column(name = "TRANS_MODE")
    private String transMode;

    /**
     * 运费
     **/
    @Column(name = "FEE_RATE")
    private BigDecimal feeRate;

    /**
     * 保费
     **/
    @Column(name = "INSUR_RATE")
    private BigDecimal insurRate;

    /**
     * 杂费
     **/
    @Column(name = "OTHER_RATE")
    private BigDecimal otherRate;

    /**
     * 报关单备注
     **/
    @Column(name = "NOTE")
    private String note;

    /**
     * 进出口标记
     **/
    @Column(name = "IE_FLAG")
    private String ieFlag;

    /**
     * 物料类型
     **/
    @Column(name = "G_MARK")
    private String GMark;

    /**
     * 传输批次号
     **/
    @Column(name = "TEMP_OWNER")
    private String tempOwner;

    /**
     * ERP创建时间
     **/
    @Column(name = "LAST_MODIFY_DATE")
    @JsonProperty("lastUpdateTime")
    private Date lastModifyDate;

    /**
     * 集装箱号
     **/
    @Column(name = "CONTAINER_MD")
    private String containerMd;

    /**
     * 集装箱规格
     **/
    @Column(name = "CONTAINER_TYPE")
    private String containerType;

    /**
     * 自重
     **/
    @Column(name = "CONTAINER_WT")
    private String containerWt;

    /**
     * 拼箱标识
     **/
    @Column(name = "CONTAINER_LCL")
    private String containerLcl;

    /**
     * 商品项号关系
     **/
    @Column(name = "CONTAINER_G_NO")
    private String containerGNo;

    /**
     * 境外收货人代码
     **/
    @Column(name = "OVERSEAS_SHIPPER_AEO")
    private String overseasShipperAeo;

    /**
     * 境外收货人名称
     **/
    @Column(name = "OVERSEAS_SHIPPER_ENAME")
    private String overseasShipperEname;

    /**
     * 报关标志
     **/
    @Column(name = "DCLCUS_MARK")
    private String dclcusMark;

    /**
     * 报关类型
     **/
    @Column(name = "DCLCUS_TYPE")
    private String dclcusType;

    /**
     * 核注清单报关单类型
     **/
    @Column(name = "ENTRY_TYPE")
    private String entryType;

    /**
     * 关联手账册编号
     **/
    @Column(name = "REL_EMS_NO")
    private String relEmsNo;

    /**
     * 运费类型
     **/
    @Column(name = "FEE_MARK")
    private String feeMark;

    /**
     * 运费币制
     **/
    @Column(name = "FEE_CURR")
    private String feeCurr;

    /**
     * 保费类型
     **/
    @Column(name = "INSUR_MARK")
    private String insurMark;

    /**
     * 保费币制
     **/
    @Column(name = "INSUR_CURR")
    private String insurCurr;

    /**
     * 杂费类型
     **/
    @Column(name = "OTHER_MARK")
    private String otherMark;

    /**
     * 杂费币制
     **/
    @Column(name = "OTHER_CURR")
    private String otherCurr;

    /**
     * 报关单类型
     **/
    @Column(name = "ENTRY_MARK")
    private String entryMark;

    /**
     * 转换后进出口标志
     **/
    @Column(name = "IE_FLAG_CONVERT")
    private String ieFlagConvert;

    /**
     * 转换后物料类型
     **/
    @Column(name = "G_MARK_CONVERT")
    private String GMarkConvert;

    /**
     *  1 ERP
     **/
    @Column(name = "DATA_SOURCE")
    private String dataSource="1";

    /**
     * 企业代码
     **/
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 创建人
     **/
    @Column(name = "INSERT_USER")
    private String insertUser;

    /**
     * 创建时间
     **/
    @Column(name = "INSERT_TIME")
    private Date insertTime;

    /**
     * 更新人
     **/
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 更新时间
     **/
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 流水号
     **/
    @Column(name = "SERIAL_NO")
    private BigDecimal serialNo;

    /**
     * 数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）
     **/
    @Column(name = "MODIFY_MARK")
    private Integer modifyMark;

    /**
     * 推送状态，1-已推送 0-未推送
     **/
    @Column(name = "PUSH_STATUS")
    private String pushStatus;

    /**
     * 转换后最终目的国
     **/
    @Column(name = "DESTINATION_COUNTRY_CONVERT")
    private String destinationCountryConvert;

    /**
     * 货物存放地点
     **/
    @Column(name = "GOODS_STROE_PLACE")
    private String goodsStroePlace;

    /**
     * 企业名称外文
     **/
    @Column(name = "OVERSEAS_SHIPPER_NAME")
    private String overseasShipperName;

    /**
     * 境内发货人代码
     **/
    @Column(name = "OWNER_CODE")
    private String ownerCode;

    /**
     * 境内发货人名称
     **/
    @Column(name = "OWNER_NAME")
    private String ownerName;

    @Column(name = "C_WEIGHT")
    private BigDecimal CWeight;//计费重量

    @Column(name = "TRADE_TERMS")
    private String tradeTerms;//实际贸易条款

    @Column(name = "INVITE_DATE")
    private Date inviteDate;//计划出口日期(邀请日期)

    @Transient
    private String insertTimeFrom;
    @Transient
    private String insertTimeTo;

    @Transient
    private String shipDateFrom;
    @Transient
    private String shipDateTo;

    /**
     * 特许权关联号
     */
    @Transient
    private String royalityNo;

    @Transient
    private  String tempRemark;
    @Transient
    private Integer tempMark;
}


