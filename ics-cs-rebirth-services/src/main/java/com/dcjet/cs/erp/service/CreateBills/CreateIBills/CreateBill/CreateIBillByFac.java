package com.dcjet.cs.erp.service.CreateBills.CreateIBills.CreateBill;

import com.dcjet.cs.erp.model.DecErpIListN;
import com.dcjet.cs.erp.model.DecIBillList;
import com.dcjet.cs.erp.service.CreateBills.config.CreateBillsConfig;
import com.dcjet.cs.erp.service.CreateBills.model.DataForRank;
import com.dcjet.cs.erp.service.DecErpIListNService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * CreateBillByFac
 *
 * <AUTHOR>
 * @date: 2020-5-29
 */
@Component
public class CreateIBillByFac implements CreateIBillStrategy {
    @Resource
    private DecErpIListNService decErpIListNService;

    @Resource
    private PublicCreateIBill publicCreateIBill;

    @Override
    public String billType() {
        return CreateBillsConfig.BILL_MERGE_TYPE_FAC;
    }

    @Override
    public List<DecIBillList> createBillSeqNo(List<DecErpIListN> decErpIListNS) {
        String headId = decErpIListNS.get(0).getHeadId();
        String bondMark = decErpIListNS.get(0).getBondMark();
        Map<String, DataForRank> map = decErpIListNService.getRankForBill(headId, bondMark, "FAC_G_NO, APPLY_G_NO");

        decErpIListNS.stream().forEach(e -> {
            if (map.containsKey(e.getSid())) {
                e.setBillGNo(new BigDecimal(map.get(e.getSid()).getRank()));
            }
        });

        return publicCreateIBill.handle(decErpIListNS);
    }
}
