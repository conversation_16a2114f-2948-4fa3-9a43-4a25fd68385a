package com.dcjet.cs.erp.service.sap;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.erp.sap.ErpExgIeListDto;
import com.dcjet.cs.dto.erp.sap.ErpExgIeListParam;
import com.dcjet.cs.erp.dao.sap.ErpSapExgIeListMapper;
import com.dcjet.cs.util.CommonEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 成品出入库异步导出
 * @author: WJ
 * @createDate: 2020/9/11 17:03
 */
@Component
public class ExportExgService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private ErpSapExgIeListMapper erpSapExgIeListMapper;

    @Resource
    private PCodeHolder pCodeHolder;

    private final String taskName = xdoi18n.XdoI18nUtil.t("成品出入库异步导出(EXG_DETAIL)");

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("EXG_DETAIL");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        ErpExgIeListParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(exportParam));
        Integer count = erpSapExgIeListMapper.selectDataCount(exportParam);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        ErpExgIeListParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(exportParam));
        Page<ErpExgIeListDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> erpSapExgIeListMapper.getList(exportParam));

        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(page.getResult()));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    private List<ErpExgIeListDto> convertForPrint(List<ErpExgIeListDto> list) {
        for (ErpExgIeListDto item : list) {
            if (StringUtils.isNotBlank(item.getStatus())) {
                item.setStatus(item.getStatus() + " " + CommonEnum.IMG_EXG_STATUS.getValue(item.getStatus()));
            }
            if (StringUtils.isNotBlank(item.getModifyMark())) {
                item.setModifyMark(CommonEnum.modifyMarkEnum.getValue(item.getModifyMark()));
            }
//            if (StringUtils.isNotBlank(item.getBondMark())) {
//                item.setBondMark(item.getBondMark() + " " + CommonEnum.BondMarkEnum.getValue(item.getBondMark()));
//            }
            if (StringUtils.isNotBlank(item.getBondMarkConvert())) {
                item.setBondMarkConvert(CommonEnum.BondMarkEnum.getValue(item.getBondMarkConvert()));
            }
            if (StringUtils.isNotBlank(item.getIeType())) {
                item.setIeType(item.getIeType() + " " + CommonEnum.EXG_TYPE_ENUM.getValue(item.getIeType()));
            }
//            if (StringUtils.isNotBlank(item.getCurr())) {
//                item.setCurr(item.getCurr() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurr()));
//            }
            if (StringUtils.isNotBlank(item.getCurrConvert())) {
                item.setCurrConvert(item.getCurrConvert() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurrConvert()));
            }
            if (StringUtils.isNotBlank(item.getOutWay())) {
                item.setOutWay(item.getOutWay() + " " + CommonEnum.InOutWayEnum.getValue(item.getOutWay()));
            }
        }
        return list;
    }

    private ErpExgIeListParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        ErpExgIeListParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, ErpExgIeListParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
