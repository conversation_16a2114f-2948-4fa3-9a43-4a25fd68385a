package com.dcjet.cs.erp.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import lombok.Getter;
import lombok.Setter;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate dcits
 *
 * @author: cc
 * @date: 2019-07-23
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_IMP_TMP_DEC_ERP_I_LIST")
public class ImpTmpDecErpIList extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户
     */
    @Column(name = "TEMP_OWNER")
    private String tempOwner;
    /**
     * 标识
     */
    @Column(name = "TEMP_MARK")
    private Integer tempMark;
    /**
     * 提示
     */
    @Column(name = "TEMP_REMARK")
    private String tempRemark;
    /**
     * 表头SID
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 提单企业内部编号 emsListNo
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;
    /**
     * 商品序号（流水号）
     */
    @Column(name = "SERIAL_NO")
    private BigDecimal serialNo;
    /**
     * 备案序号（对应底账序号）
     */
    @Column(name = "G_NO")
    private BigDecimal GNo;
    /**
     * 商品料号
     */
    @Column(name = "COP_G_NO")
    private String copGNo;
    /**
     * 商品编码
     */
    @Column(name = "CODE_T_S")
    private String codeTS;
    /**
     * 商品名称
     */
    @Column(name = "G_NAME")
    private String GName;
    /**
     * 商品规格型号
     */
    @Column(name = "G_MODEL")
    private String GModel;
    /**
     * 申报计量单位代码
     */
    @Column(name = "UNIT")
    private String unit;
    /**
     * 法定单位代码
     */
    @Column(name = "UNIT_1")
    private String unit1;
    /**
     * 法定第二单位代码
     */
    @Column(name = "UNIT_2")
    private String unit2;
    /**
     * 原产国
     */
    @XdoSize(max = 3, message = "原产国长度不能超过3位字节长度!")
    @Column(name = "ORIGIN_COUNTRY")
    private String originCountry;
    /**
     * 最终目的国
     */
    @XdoSize(max = 3, message = "最终目的国长度不能超过3位字节长度!")
    @Column(name = "DESTINATION_COUNTRY")
    private String destinationCountry;
    /**
     * 企业申报单价
     */
    @Column(name = "DEC_PRICE")
    private String decPrice;
    /**
     * 企业申报总价
     */
    @Digits(integer = 12, fraction = 5, message = "申报总价必须为数字,整数位最大12位,小数最大5位!")
    @Column(name = "DEC_TOTAL")
    private String decTotal;
    /**
     * 美元统计总金额 usdPrice
     */
    @Column(name = "USD_PRICE")
    private BigDecimal usdDecTotal;
    /**
     * 币制代码
     */
    @XdoSize(max = 3, message = "币制长度不能超过3位字节长度!")
    @Column(name = "CURR")
    private String curr;
    /**
     * 法定数量
     */
    //@Digits(integer = 11, fraction = 5, message = "法一数量必须为数字,整数位最大11位,小数最大5位!")
    @Column(name = "QTY_1")
    private String qty1;
    /**
     * 第二法定数量
     */
    //@Digits(integer =11, fraction = 5, message = "法二数量必须为数字,整数位最大11位,小数最大5位!")
    @Column(name = "QTY_2")
    private String qty2;
    /**
     * 重量比例因子
     */
    @Column(name = "FACTOR_WT")
    private BigDecimal factorWt;
    /**
     * 第一比例因子
     */
    @Column(name = "FACTOR_1")
    private BigDecimal factor1;
    /**
     * 第二比例因子
     */
    @Column(name = "FACTOR_2")
    private BigDecimal factor2;
    /**
     * 申报数量
     */
    //@Digits(integer = 11, fraction = 5, message = "申报数量必须为数字,整数位最大11位,小数最大5位!")
    @Column(name = "QTY")
    private String qty;
    /**
     * 毛重
     */
    //@Digits(integer = 11, fraction = 5, message = "毛重必须为数字,整数位最大11位,小数最大5位!")
    @Column(name = "GROSS_WT")
    private String grossWt;
    /**
     * 净重
     */
    //@Digits(integer = 14, fraction = 5, message = "净重必须为数字,整数位最大14位,小数最大5位!")
    @Column(name = "NET_WT")
    private String netWt;
    /**
     * 用途代码
     */
    @Column(name = "USE_TYPE")
    private String useType;
    /**
     * 征免方式代码
     */
    @XdoSize(max = 6, message = "征免方式长度不能超过6位字节长度!")
    @Column(name = "DUTY_MODE")
    private String dutyMode;
    /**
     * 单耗版本号
     */
    @XdoSize(max = 8, message = "单耗版本号长度不能超过8位字节长度!")
    @Column(name = "EXG_VERSION")
    private String exgVersion;
    /**
     * 报关单商品序号
     */
    //@Digits(integer = 19, fraction = 0, message = "归并序号必须为数字,整数位最大19位,小数最大0位!")
    @Column(name = "ENTRY_G_NO")
    private BigDecimal entryGNo;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 账册企业内部编号
     */
    @Column(name = "COP_EMS_NO")
    private String copEmsNo;
    /**
     * 归类标记
     */
    @Column(name = "CLASS_MARK")
    private String classMark;
    /**
     * 入库时间 arrivalDate
     */
    @Column(name = "ARRIVAL_DATE")
    private Date addTime;
    /**
     * 保税标识 bondMark
     */
    @XdoSize(max = 1, message = "保税标识长度不能超过1位字节长度!")
    @Column(name = "BOND_MARK")
    private String bondMark;
    /**
     * 物料类型标识
     */
    @XdoSize(max = 2, message = "物料类型长度不能超过2位字节长度!")
    @Column(name = "G_MARK")
    private String GMark;
    /**
     * 备案号
     */
    @XdoSize(max = 12, message = "备案号长度不能超过12位字节长度!")
    @Column(name = "EMS_NO")
    private String emsNo;
    /**
     * 中文名称 copGName
     */
    @Column(name = "COP_G_NAME")
    private String copGName;
    /**
     * 中文规格型号 copGModel
     */
    @Column(name = "COP_G_MODEL")
    private String copGModel;
    /**
     * 订单号码
     */
    @XdoSize(max = 30, message = "订单号码长度不能超过30位字节长度!")
    @Column(name = "ORDER_NO")
    private String orderNo;
    /**
     * 发票号码 invoiceNo
     */
    @XdoSize(max = 30, message = "发票号码长度不能超过30位字节长度!")
    @Column(name = "INVOICE_NO")
    private String invoiceNo;
    /**
     * 体积
     */
    //@Digits(integer = 14, fraction = 5, message = "体积必须为数字,整数位最大14位,小数最大5位!")
    @Column(name = "VOLUME")
    private String volume;
    /**
     * 供应商编码
     */
    @XdoSize(max = 50, message = "境外收货人长度不能超过50位字节长度!")
    @Column(name = "SUPPLIER_CODE")
    private String supplierCode;
    /**
     * 供应商名称 supplierName
     */
    @Column(name = "SUPPLIER_NAME")
    private String supplierName;
    /**
     * 监管方式(贸易方式)代码 tradeMode
     */
    @XdoSize(max = 6, message = "监管方式长度不能超过6位字节长度!")
    @Column(name = "TRADE_MODE")
    private String tradeMode;
    /**
     * 企业料号
     */
    @XdoSize(max = 100, message = "企业料号长度不能超过100位字节长度!")
    @Column(name = "FAC_G_NO")
    @JsonProperty("facGNo")
    private String facGNo;
    /**
     * 关联单号
     */
    @Column(name = "LINKED_NO")
    private String linkedNo;
    /**
     * 成本中心
     */
    @Column(name = "COST_CENTER")
    private String costCenter;
    /**
     * 备注 1
     */
    @XdoSize(max = 255, message = "REMARK1长度不能超过50位字节长度!")
    @Column(name = "NOTE_1")
    private String note1;
    /**
     * 备注 2
     */
    @XdoSize(max = 255, message = "REMARK1长度不能超过50位字节长度!")
    @Column(name = "NOTE_2")
    private String note2;
    /**
     * 备注 3
     */
    @XdoSize(max = 255, message = "REMARK1长度不能超过50位字节长度!")
    @Column(name = "NOTE_3")
    private String note3;
    /**
     * 境内目的地(国内地区)
     */
    @XdoSize(max = 5, message = "境内目的地(国内地区)长度不能超过5位字节长度!")
    @Column(name = "DISTRICT_CODE")
    private String districtCode;
    /**
     * 境内目的地(行政区域)
     */
    @XdoSize(max = 5, message = "境内目的地(行政区域)长度不能超过5位字节长度!")
    @Column(name = "DISTRICT_POST_CODE")
    private String districtPostCode;
    /**
     * 标识
     */
    @Column(name = "TEMP_FLAG")
    private int tempFlag;

    @Column(name = "LINE_NO")
    private String lineNo;
    /**
     * 序号
     */
    @Column(name = "TEMP_INDEX")
    private int tempIndex;
    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 清单归并序号
     */
    @Digits(integer = 11, fraction = 0, message = "归并序号不能超过总长度11，不能包含小数位")
    @Column(name = "BILL_G_NO")
    private BigDecimal billGNo;

    @Column(name = "UNIT_ERP")
    private String unitErp;
    /**
     * 特许权使用费确认
     */
    @Column(name = "CONFIRM_ROYALTIES")
    private String confirmRoyalties;
    /**
     * 特许权关联单号
     */
    @Column(name = "CONFIRM_ROYALTIES_NO")
    private String confirmRoyaltiesNo;
    /**
     * 征免性质
     */
    @Column(name = "CUT_MODE")
    private String cutMode;
    /**
     * 入库关联单号
     */
    @Column(name = "IN_OUT_NO")
    private String inOutNo;
    /**
     * CIQ 编码
     */
    @Column(name = "CIQ_NO")
    private String ciqNo;
    /**
     * 采购订单行号
     */
    @Column(name = "ORDER_LINE_NO")
    private String orderLineNo;
    /**
     * 件数
     */
    @Column(name = "PACK_NUM")
    private String packNum;
    /**
     * 分送清单编号
     */
    @Column(name = "FS_LIST_NO")
    private String fsListNo;
    /**
     * 采购人员
     */
    @Column(name = "BUYER")
    private String buyer;
    /**
     * 重点商品标识
     */
    @Column(name = "KEY_PRODUCT_MARK")
    private String keyProductMark;
}
