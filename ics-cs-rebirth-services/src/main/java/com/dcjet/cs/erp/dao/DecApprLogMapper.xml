<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.erp.dao.DecApprLogMapper">
    <delete id="deleteByHeadId">
		delete from T_GWSTD_DEC_APPR_LOG t
		<where>
		and t.HEAD_ID=#{headId} and TRADE_CODE = #{tradeCode}
		<if test="listId != null and listId != ''">
			and t.LIST_ID = #{listId}
		</if>
		</where>

	</delete>
</mapper>
