package com.dcjet.cs.erp.service.mqsync;

import com.dcjet.cs.erp.dao.sap.ErpSapSupplierMapper;
import com.dcjet.cs.erp.mapper.sap.ErpMqSyncDtoMapper;
import com.dcjet.cs.erp.model.sap.ErpSupplier;
import com.dcjet.cs.erp.model.sap.ErpSyncMsg;
import com.dcjet.cs.mqsync.core.service.BaseMqSyncService;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xdo.common.json.JsonObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class ErpSupplier4MqService extends BaseMqSyncService<ErpSyncMsg> {
    @Resource
    ErpMqSyncDtoMapper erpMqSyncDtoMapper;
    @Resource
    ErpSapSupplierMapper erpSapSupplierMapper;

    @Override
    public void consume(String json) {
        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        ErpSyncMsg<ErpSupplier, Object, Object> message = jsonObjectMapper.fromJson(json, new TypeReference<ErpSyncMsg<ErpSupplier, Object, Object>>() {});
        String syncBatchNo = message.getBasicData().getTempBatchNo();
        List<ErpSupplier> dataList = message.getDataList();
        log.info("ErpSupplier入库开始");
        String updateUser = "syncService";
        Date now = new Date();
        List<ErpSupplier> toInsertList = new ArrayList<>();
        List<ErpSupplier> toUpdateList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(data -> {
                data.setTradeCode(message.getTradeCode());//因基础资料tradeCode key被海关注册编码占用,需重新赋值
                data.setSyncBatchNo(syncBatchNo);
                data.setUpdateTime(now);
                data.setUpdateUser(updateUser);
                ErpSupplier oldData = erpSapSupplierMapper.getByIdentifier(data.getCustomerType(), data.getCustomerCode(), data.getTradeCode());
                if (oldData == null) {
                    data.setInsertTime(now);
                    data.setSid(UUID.randomUUID().toString());
                    data.setStatus(ConstantsStatus.STATUS_0);
                    toInsertList.add(data);
                } else {
                    erpMqSyncDtoMapper.overwrite(data, oldData);
                    //kafka数据状态不为 2 的，状态则为 1 （修改）
                    if (2 != oldData.getModifyMark()) {
                        oldData.setModifyMark(1);
                    }
                    toUpdateList.add(oldData);
                }
            });
            if (toInsertList.size() > 0) {
                BulkSqlOpt.batchInsertForErp(toInsertList, ErpSapSupplierMapper.class);
            }
            if (toUpdateList.size() > 0) {
                BulkSqlOpt.batchUpdateForErp(toUpdateList, ErpSapSupplierMapper.class);
            }
        }
        log.info("ErpSupplier入库结束");
    }
}
