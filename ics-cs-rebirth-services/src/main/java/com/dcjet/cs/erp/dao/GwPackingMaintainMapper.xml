<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.erp.dao.GwPackingMaintainMapper">
    <resultMap id="gwPackingMaintainResultMap" type="com.dcjet.cs.erp.model.GwPackingMaintain">
        <id column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="shipper_no" property="shipperNo" jdbcType="VARCHAR"/>
        <result column="so_no" property="soNo" jdbcType="VARCHAR"/>
        <result column="customer_order_no" property="customerOrderNo" jdbcType="VARCHAR"/>
        <result column="fac_g_no" property="facGNo" jdbcType="VARCHAR"/>
        <result column="customer_g_no" property="customerGNo" jdbcType="VARCHAR"/>
        <result column="g_mark" property="GMark" jdbcType="VARCHAR" />
        <result column="g_name" property="GName" jdbcType="VARCHAR"/>
        <result column="qty" property="qty" jdbcType="NUMERIC"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="carton_no" property="cartonNo" jdbcType="VARCHAR"/>
        <result column="carton_num" property="cartonNum" jdbcType="NUMERIC"/>
        <result column="pallet_no" property="palletNo" jdbcType="VARCHAR"/>
        <result column="pallet_num" property="palletNum" jdbcType="NUMERIC"/>
        <result column="net_wt" property="netWt" jdbcType="NUMERIC"/>
        <result column="gross_wt" property="grossWt" jdbcType="NUMERIC"/>
        <result column="volume" property="volume" jdbcType="NUMERIC"/>
        <result column="carton_model" property="cartonModel" jdbcType="VARCHAR"/>
        <result column="wrap_type" property="wrapType" jdbcType="VARCHAR"/>
        <result column="marks" property="marks" jdbcType="VARCHAR"/>
        <result column="batch_no" property="batchNo" jdbcType="VARCHAR"/>
        <result column="ippc" property="ippc" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="serial_no" property="serialNo" jdbcType="NUMERIC"/>
        <result column="data_source" property="dataSource" jdbcType="VARCHAR"/>
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        SID
        ,HEAD_ID
     ,STATUS
     ,SHIPPER_NO
     ,SO_NO
     ,CUSTOMER_ORDER_NO
     ,FAC_G_NO
     ,CUSTOMER_G_NO
     ,G_NAME
     ,G_MARK
     ,QTY
     ,UNIT
     ,CARTON_NO
     ,CARTON_NUM
     ,PALLET_NO
     ,PALLET_NUM
     ,NET_WT
     ,GROSS_WT
     ,VOLUME
     ,CARTON_MODEL
     ,WRAP_TYPE
     ,MARKS
     ,BATCH_NO
     ,IPPC
     ,REMARK
     ,TRADE_CODE
     ,SERIAL_NO
     ,DATA_SOURCE
     ,INSERT_USER
     ,INSERT_USER_NAME
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_USER_NAME
     ,UPDATE_TIME
    </sql>
    <sql id="condition">
        <if test="sid != null and sid != ''">
            and sid = #{sid}
        </if>
        <if test="headId != null and headId != ''">
            and head_id = #{headId}
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="shipperNo != null and shipperNo != ''">
            and shipper_no like concat(concat('%', #{shipperNo}), '%')
        </if>
        <if test="soNo != null and soNo != ''">
            and so_no like concat(concat('%', #{soNo}), '%')
        </if>
        <if test="customerOrderNo != null and customerOrderNo != ''">
            and customer_order_no like concat(concat('%', #{customerOrderNo}), '%')
        </if>
        <if test="facGNo != null and facGNo != ''">
            and fac_g_no like concat(concat('%', #{facGNo}), '%')
        </if>
        <if test="customerGNo != null and customerGNo != ''">
            and customer_g_no like concat(concat('%', #{customerGNo}), '%')
        </if>
        <if test="GName != null and GName != ''">
            and g_name like concat(concat('%', #{GName}), '%')
        </if>
        <if test="qty != null and qty != ''">
            and qty = #{qty}
        </if>
        <if test="unit != null and unit != ''">
            and unit = #{unit}
        </if>
        <if test="cartonNo != null and cartonNo != ''">
            and carton_no like concat(concat('%', #{cartonNo}), '%')
        </if>
        <if test="cartonNum != null and cartonNum != ''">
            and carton_num = #{cartonNum}
        </if>
        <if test="palletNo != null and palletNo != ''">
            and pallet_no like concat(concat('%', #{palletNo}), '%')
        </if>
        <if test="palletNum != null and palletNum != ''">
            and pallet_num = #{palletNum}
        </if>
        <if test="netWt != null and netWt != ''">
            and net_wt = #{netWt}
        </if>
        <if test="grossWt != null and grossWt != ''">
            and gross_wt = #{grossWt}
        </if>
        <if test="volume != null and volume != ''">
            and volume = #{volume}
        </if>
        <if test="cartonModel != null and cartonModel != ''">
            and carton_model = #{cartonModel}
        </if>
        <if test="wrapType != null and wrapType != ''">
            and wrap_type = #{wrapType}
        </if>
        <if test="marks != null and marks != ''">
            and marks = #{marks}
        </if>
        <if test="batchNo != null and batchNo != ''">
            and batch_no = #{batchNo}
        </if>
        <if test="ippc != null and ippc != ''">
            and ippc = #{ippc}
        </if>
        <if test="remark != null and remark != ''">
            and remark = #{remark}
        </if>
        <if test="tradeCode != null and tradeCode != ''">
            and trade_code = #{tradeCode}
        </if>
        <if test="serialNo != null and serialNo != ''">
            and serial_no = #{serialNo}
        </if>
        <if test="dataSource != null and dataSource != ''">
            and data_source = #{dataSource}
        </if>
        <if test="insertUser != null and insertUser != ''">
            and insert_user = #{insertUser}
        </if>
        <if test="insertUserName != null and insertUserName != ''">
            and insert_user_name = #{insertUserName}
        </if>
        <if test="_databaseId == 'oracle' and insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and insert_time >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and insert_time <= to_date(#{insertTimeTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and insert_time >= to_timestamp(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="_databaseId == 'postgresql' and insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and insert_time <= to_timestamp(#{insertTimeTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp ]]>
        </if>
        <if test="updateUser != null and updateUser != ''">
            and update_user = #{updateUser}
        </if>
        <if test="updateUserName != null and updateUserName != ''">
            and update_user_name = #{updateUserName}
        </if>
        <if test="_databaseId == 'oracle' and updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and update_time >= to_date(#{updateTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and update_time <= to_date(#{updateTimeTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and update_time >= to_timestamp(#{updateTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="_databaseId == 'postgresql' and updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and update_time <= to_timestamp(#{updateTimeTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwPackingMaintainResultMap"
            parameterType="com.dcjet.cs.erp.model.GwPackingMaintain">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_gw_packing_maintain t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        DELETE FROM t_gw_packing_maintain t WHERE t.SID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="getNextSerialNo" parameterType="String" resultType="decimal">
        SELECT F_XDO_NVL(max(t.SERIAL_NO) + 1, F_XDO_TO_NUMBER('1'))
          FROM T_GW_PACKING_MAINTAIN t
        <where>
            AND t.HEAD_ID = #{headId, jdbcType=VARCHAR}
        </where>
    </select>
    <insert id="importTmpData">
        INSERT INTO T_GW_PACKING_MAINTAIN(SID,HEAD_ID,STATUS,SHIPPER_NO,SO_NO,CUSTOMER_ORDER_NO,FAC_G_NO,CUSTOMER_G_NO,G_MARK,G_NAME,QTY,UNIT,CARTON_NO,
            CARTON_NUM,PALLET_NO,PALLET_NUM,NET_WT,GROSS_WT,VOLUME,CARTON_MODEL,WRAP_TYPE,MARKS,BATCH_NO,IPPC,REMARK,TRADE_CODE,SERIAL_NO,DATA_SOURCE,
            INSERT_USER,INSERT_USER_NAME,INSERT_TIME,UPDATE_USER,UPDATE_USER_NAME,UPDATE_TIME)
        SELECT SID,HEAD_ID,STATUS,SHIPPER_NO,SO_NO,CUSTOMER_ORDER_NO,FAC_G_NO,CUSTOMER_G_NO,'E',G_NAME,QTY,UNIT,CARTON_NO,
               CARTON_NUM,PALLET_NO,PALLET_NUM,NET_WT,GROSS_WT,VOLUME,
               CARTON_MODEL,WRAP_TYPE,MARKS,BATCH_NO,IPPC,REMARK,TRADE_CODE,#{nextGNo},'2',
               INSERT_USER,INSERT_USER_NAME,INSERT_TIME,UPDATE_USER,UPDATE_USER_NAME,UPDATE_TIME
         FROM T_GW_PACKING_MAINTAIN_TMP
        WHERE TEMP_OWNER = #{tempOwner} AND HEAD_ID = #{headId} AND TEMP_FLAG = '0'
    </insert>
    <select id="selectTotalByHeadId" resultType="com.dcjet.cs.erp.model.GwPackingMaintain" parameterType="java.lang.String">
        SELECT
        <if test='_databaseId == "postgresql" '>
            SUM(COALESCE(QTY, 0)) AS QTY,
            SUM(COALESCE(NET_WT, 0)) AS NET_WT,
            SUM(COALESCE(GROSS_WT, 0)) AS GROSS_WT
        </if>
        <if test='_databaseId != "postgresql" '>
            SUM(NVL(QTY, 0)) AS QTY,
            SUM(NVL(NET_WT, 0)) AS NET_WT,
            SUM(NVL(GROSS_WT, 0)) AS GROSS_WT
        </if>
        FROM T_GW_PACKING_MAINTAIN WHERE HEAD_ID = #{headId}
    </select>
    <update id="updateStatus" parameterType="map">
        UPDATE T_GW_PACKING_MAINTAIN SET STATUS = #{status}
        WHERE HEAD_ID = #{headId}
        <if test="facGNo != null and facGNo != ''">
            AND FAC_G_NO = #{facGNo}
        </if>
    </update>
    <select id="selectByHeadId" resultMap="gwPackingMaintainResultMap" parameterType="string">
        SELECT * FROM T_GW_PACKING_MAINTAIN WHERE HEAD_ID = #{headId}
    </select>
    <select id="selectByGNo" resultMap="gwPackingMaintainResultMap" parameterType="string">
        SELECT
        <if test='_databaseId == "postgresql" '>
            SUM(QTY) AS QTY,
            SUM(NET_WT) AS NET_WT,
            SUM(GROSS_WT) AS GROSS_WT,
        </if>
        <if test='_databaseId != "postgresql" '>
            SUM(QTY) AS QTY,
            SUM(NET_WT) AS NET_WT,
            SUM(GROSS_WT) AS GROSS_WT,
        </if>
        FAC_G_NO
        FROM T_GW_PACKING_MAINTAIN
        WHERE HEAD_ID = #{HEADID} GROUP BY FAC_G_NO
    </select>
    <select id="selectCountByHeadId" resultMap="gwPackingMaintainResultMap" parameterType="string">
        SELECT SID
        ,HEAD_ID
        ,STATUS
        ,SHIPPER_NO
        ,SO_NO
        ,CUSTOMER_ORDER_NO
        ,FAC_G_NO
        ,CUSTOMER_G_NO
        ,G_NAME
        ,G_MARK
        ,UNIT
        ,CARTON_NO
        ,PALLET_NO
        ,CARTON_MODEL
        ,WRAP_TYPE
        ,BATCH_NO
        ,IPPC
        ,TRADE_CODE
        ,SERIAL_NO
        ,DATA_SOURCE
        <if test='_databaseId == "postgresql" '>
            ,coalesce(QTY, 0) AS QTY
            ,coalesce(CARTON_NUM, 0) AS CARTON_NUM
            ,coalesce(PALLET_NUM, 0) AS PALLET_NUM
            ,coalesce(NET_WT, 0) AS NET_WT
            ,coalesce(GROSS_WT, 0) AS GROSS_WT
            ,coalesce(VOLUME, 0) AS VOLUME
        </if>
        <if test='_databaseId != "postgresql" '>
            ,NVL(QTY, 0) AS QTY
            ,NVL(CARTON_NUM, 0) AS CARTON_NUM
            ,NVL(PALLET_NUM, 0) AS PALLET_NUM
            ,NVL(NET_WT, 0) AS NET_WT
            ,NVL(GROSS_WT, 0) AS GROSS_WT
            ,NVL(VOLUME, 0) AS VOLUME
        </if>
        ,INSERT_USER
        ,INSERT_TIME
        ,UPDATE_USER
        ,UPDATE_TIME
        ,INSERT_USER_NAME
        ,UPDATE_USER_NAME
        ,MARKS
        ,REMARK
        FROM T_GW_PACKING_MAINTAIN WHERE HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode}
    </select>
    <select id="getNetWtAverageByFacGNo" resultMap="gwPackingMaintainResultMap" parameterType="string">
       SELECT FAC_G_NO,
        <if test='_databaseId == "postgresql" '>
              ROUND(SUM(COALESCE(NET_WT, 0))/SUM(COALESCE(QTY, 0)), 8) AS NET_WT
        </if>
        <if test='_databaseId != "postgresql" '>
              ROUND(SUM(NVL(NET_WT, 0))/SUM(NVL(QTY, 0)), 8) AS NET_WT
        </if>
         FROM T_GW_PACKING_MAINTAIN
        WHERE HEAD_ID = #{HEADID}
     GROUP BY FAC_G_NO
    </select>
    <select id="getNetWtAverageByGName" resultMap="gwPackingMaintainResultMap" parameterType="string">
       SELECT G_NAME,
        <if test='_databaseId == "postgresql" '>
              ROUND(SUM(COALESCE(NET_WT, 0))/SUM(COALESCE(QTY, 0)), 8) AS NET_WT
        </if>
        <if test='_databaseId != "postgresql" '>
              ROUND(SUM(NVL(NET_WT, 0))/SUM(NVL(QTY, 0)), 8) AS NET_WT
        </if>
         FROM T_GW_PACKING_MAINTAIN
        WHERE HEAD_ID = #{HEADID} AND G_NAME IS NOT NULL
     GROUP BY G_NAME
    </select>
    <select id="getNetWtAverageByFacGNoBatchNo" resultMap="gwPackingMaintainResultMap" parameterType="string">
       SELECT FAC_G_NO, BATCH_NO,
        <if test='_databaseId == "postgresql" '>
              ROUND(SUM(COALESCE(NET_WT, 0))/SUM(COALESCE(QTY, 0)), 8) AS NET_WT
        </if>
        <if test='_databaseId != "postgresql" '>
              ROUND(SUM(NVL(NET_WT, 0))/SUM(NVL(QTY, 0)), 8) AS NET_WT
        </if>
         FROM T_GW_PACKING_MAINTAIN
        WHERE HEAD_ID = #{HEADID} AND BATCH_NO IS NOT NULL
     GROUP BY FAC_G_NO, BATCH_NO
    </select>

    <update id="updateSummary">
        UPDATE T_DEC_ERP_E_HEAD_N
        SET (NET_WT, gross_wt, pack_num) =
                (select sum(net_wt) as net_wt, sum(gross_wt) as gross_wt, sum(pallet_num) as pack_num
                 from t_gw_packing_maintain
                 where head_id = #{headId}
                   and trade_code = #{tradeCode})
        where sid = #{headId}
          and trade_code = #{tradeCode};
    </update>
</mapper>
