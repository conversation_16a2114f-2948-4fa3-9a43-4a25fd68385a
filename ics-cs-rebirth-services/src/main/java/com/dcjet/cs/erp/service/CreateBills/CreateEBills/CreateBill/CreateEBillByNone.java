package com.dcjet.cs.erp.service.CreateBills.CreateEBills.CreateBill;

import com.dcjet.cs.erp.model.DecEBillList;
import com.dcjet.cs.erp.model.DecErpEListN;
import com.dcjet.cs.erp.service.CreateBills.config.CreateBillsConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生成清单序号
 * 清单归并类型：不归并
 *
 * <AUTHOR>
 * @date: 2020-5-29
 */
@Component
public class CreateEBillByNone implements CreateEBillStrategy {
    @Resource
    private PublicCreateEBill publicCreateEBill;

    @Override
    public String billType() {
        return CreateBillsConfig.BILL_MERGE_TYPE_NONE;
    }

    @Override
    public List<DecEBillList> createBillSeqNo(List<DecErpEListN> decErpEListNS) {
        /** 「清单序号」= 「序号」 */
        decErpEListNS.stream().forEach(e -> e.setBillGNo(e.getSerialNo()));

        return publicCreateEBill.handle(decErpEListNS);
    }
}
