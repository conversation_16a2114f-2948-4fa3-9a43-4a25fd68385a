<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.erp.dao.DecIBillListMapper">
    <resultMap id="decIBillListResultMap" type="com.dcjet.cs.erp.model.DecIBillList">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="SEQ_NO" property="seqNo" jdbcType="VARCHAR"/>
        <result column="EMS_LIST_NO" property="emsListNo" jdbcType="VARCHAR"/>
        <result column="LIST_NO" property="listNo" jdbcType="VARCHAR"/>
        <result column="SERIAL_NO" property="serialNo" jdbcType="VARCHAR"/>
        <result column="G_NO" property="GNo" jdbcType="VARCHAR"/>
        <result column="COP_G_NO" property="copGNo" jdbcType="VARCHAR"/>
        <result column="CODE_T_S" property="codeTS" jdbcType="VARCHAR"/>
        <result column="G_NAME" property="GName" jdbcType="VARCHAR"/>
        <result column="G_MODEL" property="GModel" jdbcType="VARCHAR"/>
        <result column="UNIT" property="unit" jdbcType="VARCHAR"/>
        <result column="UNIT_1" property="unit1" jdbcType="VARCHAR"/>
        <result column="UNIT_2" property="unit2" jdbcType="VARCHAR"/>
        <result column="ORIGIN_COUNTRY" property="originCountry" jdbcType="VARCHAR"/>
        <result column="DEC_PRICE" property="decPrice" jdbcType="VARCHAR"/>
        <result column="DEC_TOTAL" property="decTotal" jdbcType="VARCHAR"/>
        <result column="USD_PRICE" property="usdPrice" jdbcType="VARCHAR"/>
        <result column="CURR" property="curr" jdbcType="VARCHAR"/>
        <result column="QTY_1" property="qty1" jdbcType="VARCHAR"/>
        <result column="QTY_2" property="qty2" jdbcType="VARCHAR"/>
        <result column="FACTOR_WT" property="factorWt" jdbcType="VARCHAR"/>
        <result column="FACTOR_1" property="factor1" jdbcType="VARCHAR"/>
        <result column="FACTOR_2" property="factor2" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="VARCHAR"/>
        <result column="GROSS_WT" property="grossWt" jdbcType="VARCHAR"/>
        <result column="NET_WT" property="netWt" jdbcType="VARCHAR"/>
        <result column="USE_TYPE" property="useType" jdbcType="VARCHAR"/>
        <result column="DUTY_MODE" property="dutyMode" jdbcType="VARCHAR"/>
        <result column="EXG_VERSION" property="exgVersion" jdbcType="VARCHAR"/>
        <result column="ENTRY_G_NO" property="entryGNo" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="COP_EMS_NO" property="copEmsNo" jdbcType="VARCHAR"/>
        <result column="CLASS_MARK" property="classMark" jdbcType="VARCHAR"/>
        <result column="ARRIVAL_DATE" property="arrivalDate" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="VARCHAR"/>
        <result column="HEAD_ID" property="headId" jdbcType="VARCHAR"/>
        <result column="DESTINATION_COUNTRY" property="destinationCountry" jdbcType="VARCHAR"/>
        <result column="MODIFY_MARK" property="modifyMark" jdbcType="VARCHAR"/>
        <result column="FAC_G_NO" property="facGNo" jdbcType="VARCHAR"/>
        <result column="LINKED_NO" property="linkedNo" jdbcType="VARCHAR"/>
        <result column="NOTE_1" property="note1" jdbcType="VARCHAR" />
        <result column="NOTE_2" property="note2" jdbcType="VARCHAR" />
        <result column="NOTE_3" property="note3" jdbcType="VARCHAR" />
        <result column="COP_G_NAME" property="copGName" jdbcType="VARCHAR"/>
        <result column="COP_G_MODEL" property="copGModel" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="DISTRICT_CODE" property="districtCode" jdbcType="VARCHAR"/>
        <result column="DISTRICT_POST_CODE" property="districtPostCode" jdbcType="VARCHAR"/>
        <result column="CIQ_NO" property="ciqNo" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,SEQ_NO
     ,EMS_LIST_NO
     ,LIST_NO
     ,SERIAL_NO
     ,G_NO
     ,COP_G_NO
     ,CODE_T_S
     ,G_NAME
     ,G_MODEL
     ,UNIT
     ,UNIT_1
     ,UNIT_2
     ,ORIGIN_COUNTRY
     ,DEC_PRICE
     ,DEC_TOTAL
     ,USD_PRICE
     ,CURR
     ,QTY_1
     ,QTY_2
     ,FACTOR_WT
     ,FACTOR_1
     ,FACTOR_2
     ,QTY
     ,GROSS_WT
     ,NET_WT
     ,USE_TYPE
     ,DUTY_MODE
     ,EXG_VERSION
     ,ENTRY_G_NO
     ,NOTE
     ,COP_EMS_NO
     ,CLASS_MARK
     ,ARRIVAL_DATE
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,HEAD_ID
     ,DESTINATION_COUNTRY
     ,MODIFY_MARK
     ,FAC_G_NO
     ,NOTE_1
     ,NOTE_2
     ,NOTE_3
     ,COP_G_NAME
     ,COP_G_MODEL
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
     ,TRADE_CODE
     ,DISTRICT_CODE
     ,DISTRICT_POST_CODE
     ,CIQ_NO
    </sql>
    <sql id="condition">
        <if test="sid != null and sid != ''">
            and SID = #{sid}
        </if>
        <if test="seqNo != null and seqNo != ''">
            and SEQ_NO = #{seqNo}
        </if>
        <if test="emsListNo != null and emsListNo != ''">
            and EMS_LIST_NO = #{emsListNo}
        </if>
        <if test="listNo != null and listNo != ''">
            and LIST_NO = #{listNo}
        </if>
        <if test="serialNo != null and serialNo != ''">
            and SERIAL_NO = #{serialNo}
        </if>
        <if test="GNo != null and GNo != ''">
            and G_NO = #{GNo}
        </if>
        <if test="copGNo != null and copGNo != ''">
            and COP_G_NO = #{copGNo}
        </if>
        <if test="codeTS != null and codeTS != ''">
            and CODE_T_S = #{codeTS}
        </if>
        <if test="GName != null and GName != ''">
            and G_NAME = #{GName}
        </if>
        <if test="GModel != null and GModel != ''">
            and G_MODEL = #{GModel}
        </if>
        <if test="unit != null and unit != ''">
            and UNIT = #{unit}
        </if>
        <if test="unit1 != null and unit1 != ''">
            and UNIT_1 = #{unit1}
        </if>
        <if test="unit2 != null and unit2 != ''">
            and UNIT_2 = #{unit2}
        </if>
        <if test="originCountry != null and originCountry != ''">
            and ORIGIN_COUNTRY = #{originCountry}
        </if>
        <if test="decPrice != null and decPrice != ''">
            and DEC_PRICE = #{decPrice}
        </if>
        <if test="decTotal != null and decTotal != ''">
            and DEC_TOTAL = #{decTotal}
        </if>
        <if test="usdPrice != null and usdPrice != ''">
            and USD_PRICE = #{usdPrice}
        </if>
        <if test="curr != null and curr != ''">
            and CURR = #{curr}
        </if>
        <if test="qty1 != null and qty1 != ''">
            and QTY_1 = #{qty1}
        </if>
        <if test="qty2 != null and qty2 != ''">
            and QTY_2 = #{qty2}
        </if>
        <if test="factorWt != null and factorWt != ''">
            and FACTOR_WT = #{factorWt}
        </if>
        <if test="factor1 != null and factor1 != ''">
            and FACTOR_1 = #{factor1}
        </if>
        <if test="factor2 != null and factor2 != ''">
            and FACTOR_2 = #{factor2}
        </if>
        <if test="qty != null and qty != ''">
            and QTY = #{qty}
        </if>
        <if test="grossWt != null and grossWt != ''">
            and GROSS_WT = #{grossWt}
        </if>
        <if test="netWt != null and netWt != ''">
            and NET_WT = #{netWt}
        </if>
        <if test="useType != null and useType != ''">
            and USE_TYPE = #{useType}
        </if>
        <if test="dutyMode != null and dutyMode != ''">
            and DUTY_MODE = #{dutyMode}
        </if>
        <if test="exgVersion != null and exgVersion != ''">
            and EXG_VERSION = #{exgVersion}
        </if>
        <if test="entryGNo != null and entryGNo != ''">
            and ENTRY_G_NO = #{entryGNo}
        </if>
        <if test="note != null and note != ''">
            and NOTE = #{note}
        </if>
        <if test="copEmsNo != null and copEmsNo != ''">
            and COP_EMS_NO = #{copEmsNo}
        </if>
        <if test="classMark != null and classMark != ''">
            and CLASS_MARK = #{classMark}
        </if>
        and HEAD_ID = #{headId}
        <if test="destinationCountry != null and destinationCountry != ''">
            and DESTINATION_COUNTRY = #{destinationCountry}
        </if>
        <if test="modifyMark != null and modifyMark != ''">
            and MODIFY_MARK = #{modifyMark}
        </if>
        <if test="facGNo != null and facGNo != ''">
            and FAC_G_NO = #{facGNo}
        </if>
    </sql>
    <sql id="condition_pg">
        <if test="arrivalDateFrom != null and arrivalDateFrom != ''">
            <![CDATA[ and ARRIVAL_DATE >= to_timestamp(#{arrivalDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="arrivalDateTo != null and arrivalDateTo != ''">
            <![CDATA[ and ARRIVAL_DATE < to_timestamp(#{arrivalDateFrom},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
    </sql>
    <sql id="condition_oracle">
        <if test="arrivalDateFrom != null and arrivalDateFrom != ''">
            <![CDATA[ and ARRIVAL_DATE >= to_date(#{arrivalDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="arrivalDateTo != null and arrivalDateTo != ''">
            <![CDATA[ and ARRIVAL_DATE < to_date(#{arrivalDateTo}, 'yyyy-MM-dd hh24:mi:ss')+1 ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="decIBillListResultMap" parameterType="com.dcjet.cs.erp.model.DecIBillList">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_DEC_I_BILL_LIST t
        <where>
            <include refid="condition"></include>
            <if test='_databaseId == "postgresql" '>
                <include refid="condition_pg"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="condition_oracle"></include>
            </if>
        </where>
        order by SERIAL_NO,t.sid
    </select>
    <!-- 获取表体明细汇总信息-->
    <select id="getListTotal" resultType="string" >
        select '申报数量:'||to_char(sum(f_xdo_nvl( t.QTY, 0.0 )),'fm999999999999999990.09999')||' 申报总价:'||to_char(sum(f_xdo_nvl( t.DEC_TOTAL,
        0.0 )),'fm999999999999999990.09999')||' 净重:'||to_char(sum(f_xdo_nvl( t.net_wt, 0.0)),'fm999999999999999990.09999999')  as total from T_DEC_I_BILL_LIST t
        <where>
            <include refid="condition"></include>
            <if test='_databaseId == "postgresql" '>
                <include refid="condition_pg"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="condition_oracle"></include>
            </if>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_DEC_I_BILL_LIST t
        where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <!-- 查询待发送清单的备案号-->
    <select id="getEmsNoList" resultType="string">
        SELECT
            T.EMS_NO
        FROM T_DEC_ERP_I_LIST_N T
        WHERE T.HEAD_ID=#{headId}
        GROUP BY T.EMS_NO
    </select>
    <select id="getMrpBillList" resultMap="decIBillListResultMap" parameterType="com.dcjet.cs.dto.mrp.MrpMatchStartParam">
        SELECT t.*, h.DECLARE_DATE, h.DCR_CYCLE FROM T_DEC_I_BILL_LIST t LEFT JOIN T_DEC_I_BILL_HEAD h ON t.HEAD_ID = h.SID
        <where>
            <if test="emsNo != null and emsNo != ''">
                and h.EMS_NO = #{emsNo}
            </if>
            <if test="tradeCode != null and tradeCode != ''">
                and h.TRADE_CODE = #{tradeCode}
            </if>
            <if test="dcrTimes != null">
                and h.DCR_CYCLE = #{dcrTimes} || ''
            </if>
            <if test='_databaseId == "postgresql" '>
                <if test="declareDateFrom != null and declareDateFrom != ''">
                    <![CDATA[ and h.DECLARE_DATE >= to_timestamp(#{declareDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
                </if>
                <if test="declareDateTo != null and declareDateTo != ''">
                    <![CDATA[ and h.DECLARE_DATE <to_timestamp(#{declareDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
                </if>
            </if>
            <if test='_databaseId != "postgresql" '>
                <if test="declareDateFrom != null and declareDateFrom != ''">
                    <![CDATA[ and h.DECLARE_DATE >= to_date(#{declareDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
                </if>
                <if test="declareDateTo != null and declareDateTo != ''">
                    <![CDATA[ and h.DECLARE_DATE <to_date(#{declareDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
                </if>
            </if>
            <if test="copGNo != null and copGNo != ''">
                and t.COP_G_NO = #{copGNo}
            </if>
            <choose>
                <when test="originCountryType == 3">
                    and t.ORIGIN_COUNTRY = #{originCountry}
                </when>
            </choose>
            and (h.SEND_API_STATUS = 'J1' or h.SEND_API_STATUS = '4' or h.SEND_API_STATUS = '5')
            and h.TRADE_MODE in ('0657', '0258', '0615', '0214', '0654', '0255')
            AND EXISTS (SELECT 1 FROM T_MRP_RETURN_SUMMARY m WHERE m.HEAD_ID =  #{headId} AND t.COP_G_NO = m.COP_G_NO)
        </where>
        ORDER BY
        <choose>
            <when test="originCountryType == 1">
                CASE ORIGIN_COUNTRY WHEN #{originCountry} THEN 0 ELSE 1 END,
            </when>
            <when test="originCountryType == 2">
                CASE ORIGIN_COUNTRY WHEN #{originCountry} THEN 1 ELSE 0 END,
            </when>
            <when test="originCountryType == 3">
                CASE ORIGIN_COUNTRY WHEN #{originCountry} THEN 0 ELSE 1 END,
            </when>
        </choose>
        <choose>
            <when test="matchMethod == 0">
                h.DECLARE_DATE
            </when>
            <when test="matchMethod == 1">
                h.DECLARE_DATE DESC
            </when>
            <when test="matchMethod == 2">
                ABS(#{decPrice}-t.DEC_PRICE)
            </when>
        </choose>
    </select>
    <select id="getListByHeadId" parameterType="string" resultMap="decIBillListResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_DEC_I_BILL_LIST t
        where t.HEAD_ID = #{headId}
        order by SERIAL_NO
    </select>
    <select id="getListByHeadIds" parameterType="string" resultMap="decIBillListResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_DEC_I_BILL_LIST t
        where t.HEAD_ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by EMS_LIST_NO,SERIAL_NO
    </select>
    <select id="getMrpMatchBillList" resultMap="decIBillListResultMap">
        select l.*
        from t_mrp_manage m
                 join t_mrp_match match on match.head_id = m.sid
            join t_dec_i_bill_list l on l.sid = match.dec_i_bill_list_sid
        where m.sid = #{mrpManageId}
        order by match.fac_g_no, match.remain_qty desc
    </select>

    <select id="selectHeadId" resultType="java.lang.String">
        <if test="bondMark == '0'.toString()">
        select distinct t.fac_g_no
        from (select distinct list.fac_g_no
              from t_dec_i_bill_head head
                       left join t_dec_i_bill_list list on head.sid = list.head_id
              where list.qty_1 = 0
                and head.head_id = #{headId}
                and head.trade_code = #{tradeCode}
              union all
              select distinct fac_g_no
              from t_dec_erp_i_list_n
              where sid in (select re.list_sid
                            from t_dec_i_entry_list list
                                     left join t_dec_erp_i_relation re on re.entry_list_sid = list.sid
                                     left join t_dec_i_entry_head head on list.head_id = head.sid
                            where list.qty_1 = 0
                              and head.erp_head_id = #{headId}
                              and head.trade_code = #{tradeCode})
                and trade_code = #{tradeCode}) t
        </if>
        <if test="bondMark == '1'.toString()">
        select distinct t.fac_g_no
        from (select distinct fac_g_no
              from t_dec_erp_i_list_n
              where sid in (select re.list_sid
                            from t_dec_i_entry_list list
                                     left join t_dec_erp_i_relation re on re.entry_list_sid = list.sid
                                     left join t_dec_i_entry_head head on list.head_id = head.sid
                            where list.qty_1 = 0
                              and head.erp_head_id = #{headId}
                              and head.trade_code = #{tradeCode})
                and trade_code = #{tradeCode}) t
        </if>
    </select>

    <select id="selectList" resultType="com.dcjet.cs.erp.model.DecIBillList">
        SELECT <include refid="Base_Column_List"/> FROM T_DEC_I_BILL_LIST WHERE serial_no=#{serialNo} AND CODE_T_S=#{codeTS} AND g_name = #{GName} AND head_id = #{headId} AND TRADE_CODE = #{tradeCode}
    </select>
</mapper>
