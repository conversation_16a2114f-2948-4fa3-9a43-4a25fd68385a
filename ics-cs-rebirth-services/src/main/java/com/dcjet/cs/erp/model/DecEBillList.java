package com.dcjet.cs.erp.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_DEC_E_BILL_LIST")
public class DecEBillList extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 报关单统一编号
     */
    @Column(name = "SEQ_NO")
    private String seqNo;
    /**
     * 核注清单内部编号
     */
    @Column(name = "EMS_LIST_NO")
    private String emsListNo;
    /**
     * 核注清单编号
     */
    @Column(name = "LIST_NO")
    private String listNo;
    /**
     * 流水号
     */
    @Column(name = "SERIAL_NO")
    private BigDecimal serialNo;
    /**
     * 备案序号
     */
    @Column(name = "G_NO")
    private BigDecimal GNo;
    /**
     * 备案料号
     */
    @Column(name = "COP_G_NO")
    private String copGNo;
    /**
     * 商品编码
     */
    @Column(name = "CODE_T_S")
    private String codeTS;
    /**
     * 商品名称
     */
    @Column(name = "G_NAME")
    private String GName;
    /**
     * 申报规格型号
     */
    @Column(name = "G_MODEL")
    private String GModel;
    /**
     * 计量单位
     */
    @Column(name = "UNIT")
    private String unit;
    /**
     * 法一单位
     */
    @Column(name = "UNIT_1")
    private String unit1;
    /**
     * 法二单位
     */
    @Column(name = "UNIT_2")
    private String unit2;
    /**
     * 原产国
     */
    @Column(name = "ORIGIN_COUNTRY")
    private String originCountry;
    /**
     * 申报单价
     */
    @Column(name = "DEC_PRICE")
    private BigDecimal decPrice;
    /**
     * 申报总价
     */
    @Column(name = "DEC_TOTAL")
    private BigDecimal decTotal;
    /**
     * 美元统计总金额
     */
    @Column(name = "USD_PRICE")
    private BigDecimal usdPrice;
    /**
     * 币制
     */
    @Column(name = "CURR")
    private String curr;
    /**
     * 法一数量
     */
    @Column(name = "QTY_1")
    private BigDecimal qty1;
    /**
     * 法二数量
     */
    @Column(name = "QTY_2")
    private BigDecimal qty2;
    /**
     * 重量比例因子
     */
    @Column(name = "FACTOR_WT")
    private BigDecimal factorWt;
    /**
     * 第一比例因子
     */
    @Column(name = "FACTOR_1")
    private BigDecimal factor1;
    /**
     * 第二比例因子
     */
    @Column(name = "FACTOR_2")
    private BigDecimal factor2;
    /**
     * 申报数量
     */
    @Column(name = "QTY")
    private BigDecimal qty;
    /**
     * 毛重
     */
    @Column(name = "GROSS_WT")
    private BigDecimal grossWt;
    /**
     * 净重
     */
    @Column(name = "NET_WT")
    private BigDecimal netWt;
    /**
     * 用途
     */
    @Column(name = "USE_TYPE")
    private String useType;
    /**
     * 征免方式
     */
    @Column(name = "DUTY_MODE")
    private String dutyMode;
    /**
     * 单耗版本号
     */
    @Column(name = "EXG_VERSION")
    private String exgVersion;
    /**
     * 归并序号
     */
    @Column(name = "ENTRY_G_NO")
    private BigDecimal entryGNo;
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 账册(手册)内部编号
     */
    @Column(name = "COP_EMS_NO")
    private String copEmsNo;
    /**
     * 归类标记
     */
    @Column(name = "CLASS_MARK")
    private String classMark;
    /**
     * 入库时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ARRIVAL_DATE")
    private Date arrivalDate;
    /**
     * 入库时间-开始
     */
    @Transient
    private String arrivalDateFrom;
    /**
     * 入库时间-结束
     */
    @Transient
    private String arrivalDateTo;
    /**
     * 外键ID
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 最终目的国
     */
    @Column(name = "DESTINATION_COUNTRY")
    private String destinationCountry;
    /**
     * 修改标志
     */
    @Column(name = "MODIFY_MARK")
    private String modifyMark;
    /**
     * 企业料号
     */
    @Column(name = "FAC_G_NO")
    private String facGNo;
    /**
     * 关联单号
     */
    @Column(name = "LINKED_NO")
    private String linkedNo;
    /**
     * 备注1
     */
    @Column(name = "NOTE_1")
    private String note1;
    /**
     * 备注2
     */
    @Column(name = "NOTE_2")
    private String note2;
    /**
     * 备注3
     */
    @Column(name = "NOTE_3")
    private String note3;
    /**
     * 中文名称
     */
    @Column(name = "COP_G_NAME")
    private String copGName;
    /**
     * 中文规格型号
     */
    @Column(name = "COP_G_MODEL")
    private String copGModel;
    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 境内目的地/境内货源地
     */
    @Column(name = "DISTRICT_CODE")
    private String districtCode;
    /**
     * 境内目的地/境内货源地 （行政）
     */
    @Column(name = "DISTRICT_POST_CODE")
    private String districtPostCode;

    @Transient
    private String itemNo;

    @Transient
    private String erpListIds;

    @Transient
    private String GMark;

    @Column(name = "CLIENT_CODE")
    private String clientCode;

    @Column(name = "CLIENT_NAME")
    private String clientName;
    /**
     * CIQ编码
     */
    @Column(name = "CIQ_NO")
    private String ciqNo;
    /**
     * 表头内部编号
     */
    @Transient
    private String billEmsListNo;

    @Transient
    private String gNameModel;

    @Transient
    private String decAll;
    @Transient
    private String qtyUnit;
    @Transient
    private String gNoAll;
}
