package com.dcjet.cs.erp.mapper;

import com.dcjet.cs.base.mapper.BasicConverter;
import com.dcjet.cs.dto.erp.DecIBillListDto;
import com.dcjet.cs.dto.erp.DecIBillListParam;
import com.dcjet.cs.dto.erp.DecIEBillListUpdateParam;
import com.dcjet.cs.erp.model.DecIBillList;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-12-14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecIBillListUpdateDtoMapper extends BasicConverter<DecIBillList, DecIBillListDto, DecIBillListParam, DecIEBillListUpdateParam> {
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecIBillList toPo(DecIEBillListUpdateParam param);
}
