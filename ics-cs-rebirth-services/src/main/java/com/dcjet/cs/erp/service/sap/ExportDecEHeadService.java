package com.dcjet.cs.erp.service.sap;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.erp.sap.ErpDecEHeadDto;
import com.dcjet.cs.dto.erp.sap.ErpDecEHeadParam;
import com.dcjet.cs.erp.dao.sap.ErpSapDecEHeadMapper;
import com.dcjet.cs.util.CommonEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 出口提单表头异步导出
 * @author: WJ
 * @createDate: 2020/9/11 17:19
 */
@Component
public class ExportDecEHeadService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private ErpSapDecEHeadMapper erpSapDecEHeadMapper;

    @Resource
    private PCodeHolder pCodeHolder;

    private final String taskName = xdoi18n.XdoI18nUtil.t("出口提单表头异步导出(E_DEC_HEAD)");

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("E_DEC_HEAD");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        ErpDecEHeadParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(exportParam));
        Integer count = erpSapDecEHeadMapper.selectDataCount(exportParam);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        ErpDecEHeadParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(exportParam));
        Page<ErpDecEHeadDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> erpSapDecEHeadMapper.getHeadList(exportParam));

        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(page.getResult()));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    private List<ErpDecEHeadDto> convertForPrint(List<ErpDecEHeadDto> list) {
        for (ErpDecEHeadDto item : list) {
            if (StringUtils.isNotBlank(item.getStatus())) {
                item.setStatus(item.getStatus() + " " + CommonEnum.takeStatusEnum.getValue(item.getStatus()));
            }
            if (StringUtils.isNotBlank(item.getModifyMark())) {
                item.setModifyMark(CommonEnum.modifyMarkEnum.getValue(item.getModifyMark()));
            }
            if (StringUtils.isNotBlank(item.getGMarkConvert())) {
                item.setGMarkConvert(CommonEnum.GMarkEnum.getValue(item.getGMarkConvert()));
            }
            if (StringUtils.isNotBlank(item.getTrafMode())) {
                item.setTrafMode(item.getTrafMode() + " " + pCodeHolder.getValue(PCodeType.TRANSF, item.getTrafMode()));
            }
            if (StringUtils.isNotBlank(item.getTradeCountry())) {
                item.setTradeCountry(item.getTradeCountry() + " " + pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED,
                        item.getTradeCountry()));
            }
            if (StringUtils.isNotBlank(item.getTradeNation())) {
                item.setTradeNation(item.getTradeNation() + " " + pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED,
                        item.getTradeNation()));
            }
            if (StringUtils.isNotBlank(item.getDestPort())) {
                item.setDestPort(item.getDestPort() + " " + pCodeHolder.getValue(PCodeType.PORT_LIN,
                        item.getDestPort()));
            }
            if (StringUtils.isNotBlank(item.getIEPort())) {
                item.setIEPort(item.getIEPort() + " " + pCodeHolder.getCodeValue(PCodeType.CUSTOMS_REL, item.getIEPort()));
            }
            if (StringUtils.isNotBlank(item.getEntryPort())) {
                item.setEntryPort(item.getEntryPort() + " " + pCodeHolder.getCodeValue(PCodeType.CIQ_ENTY_PORT,
                        item.getEntryPort()));
            }
//            if (StringUtils.isNotBlank(item.getDestinationCountry())) {
//                item.setDestinationCountry(item.getDestinationCountry() + " " + pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getDestinationCountry()));
//            }
            if (StringUtils.isNotBlank(item.getDestinationCountryConvert())) {
                item.setDestinationCountryConvert(item.getDestinationCountryConvert() + " " + pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getDestinationCountryConvert()));
            }
            if (StringUtils.isNotBlank(item.getTradeMode())) {
                item.setTradeMode(item.getTradeMode() + " " + pCodeHolder.getValue(PCodeType.TRADE,
                        item.getTradeMode()));
            }
            if (StringUtils.isNotBlank(item.getCutMode())) {
                item.setCutMode(item.getCutMode() + " " + pCodeHolder.getValue(PCodeType.LEVYTYPE,
                        item.getCutMode()));
            }
            if (StringUtils.isNotBlank(item.getMasterCustoms())) {
                item.setMasterCustoms(item.getMasterCustoms() + " " + pCodeHolder.getValue(PCodeType.CUSTOMS_REL,
                        item.getMasterCustoms()));
            }
            if (StringUtils.isNotBlank(item.getTransMode())) {
                item.setTransMode(item.getTransMode() + " " + pCodeHolder.getValue(PCodeType.TRANSAC,
                        item.getTransMode()));
            }
            if (StringUtils.isNotBlank(item.getFeeCurr())) {
                item.setFeeCurr(item.getFeeCurr() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED,
                        item.getFeeCurr()));
            }
            if (StringUtils.isNotBlank(item.getInsurCurr())) {
                item.setInsurCurr(item.getInsurCurr() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED,
                        item.getInsurCurr()));
            }
            if (StringUtils.isNotBlank(item.getOtherCurr())) {
                item.setOtherCurr(item.getOtherCurr() + " " + pCodeHolder.getValue(PCodeType.CURR_OUTDATED,
                        item.getOtherCurr()));
            }
            if (StringUtils.isNotBlank(item.getMergeType())) {
                item.setMergeType(CommonEnum.MERGE_TYPE.getValue(item.getMergeType()));
            }
            if (StringUtils.isNotBlank(item.getFeeMark())) {
                item.setFeeMark(CommonEnum.FeeMarkEnum.getValue(item.getFeeMark()));
            }
            if (StringUtils.isNotBlank(item.getInsurMark())) {
                item.setInsurMark(CommonEnum.FeeMarkEnum.getValue(item.getInsurMark()));
            }
            if (StringUtils.isNotBlank(item.getOtherMark())) {
                item.setOtherMark(CommonEnum.FeeMarkEnum.getValue(item.getOtherMark()));
            }
            if (StringUtils.isNotBlank(item.getDclcusMark())) {
                item.setDclcusMark(CommonEnum.DCLCUS_MARK.getValue(item.getDclcusMark()));
            }
            if (StringUtils.isNotBlank(item.getDclcusType())) {
                item.setDclcusType(CommonEnum.DCLCUS_TYPE.getValue(item.getDclcusType()));
            }
            if (StringUtils.isNotBlank(item.getEntryType())) {
                item.setEntryType(CommonEnum.ENTRY_TYPE.getValue(item.getEntryType()));
            }
            if (StringUtils.isNotBlank(item.getEntryMark())) {
                item.setEntryMark(CommonEnum.ENTRY_MARK.getValue(item.getEntryMark()));
            }
        }
        return list;
    }

    private ErpDecEHeadParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        ErpDecEHeadParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, ErpDecEHeadParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
