package com.dcjet.cs.erp.dao.sap;

import com.dcjet.cs.dto.erp.sap.ErpCavBomParam;
import com.dcjet.cs.erp.model.sap.ErpCavBom;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * ErpBom
 *
 * <AUTHOR>
 * @date: 2019-11-6
 */
public interface ErpSapCavBomMapper extends Mapper<ErpCavBom> {
    List<ErpCavBom> getList(ErpCavBomParam param);

    ErpCavBom getByIdentifier(
            @Param("copExgNo") String copExgNo,
            @Param("bomVersion") String bomVersion,
            @Param("copImgNo") String copImgNo,
            @Param("tradeCode") String tradeCode);
}
