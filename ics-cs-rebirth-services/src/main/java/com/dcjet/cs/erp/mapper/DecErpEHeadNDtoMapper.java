package com.dcjet.cs.erp.mapper;

import com.dcjet.cs.dto.erp.DecErpEHeadNDto;
import com.dcjet.cs.dto.erp.DecErpEHeadNParam;
import com.dcjet.cs.dto.erp.DecErpEInvoiceDto;
import com.dcjet.cs.dto.erp.DecErpHeadUpdateNParam;
import com.dcjet.cs.entry.model.DecEEntryHead;
import com.dcjet.cs.erp.model.DecEBillHead;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpHeadTemplete;
import com.dcjet.cs.erp.model.sap.ErpDecEHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecErpEHeadNDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecErpEHeadNDto toDto(DecErpEHeadN po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecErpEHeadN toPo(DecErpEHeadNParam param);

    /***
     * 转换数据库对象到DecErpEInvoiceDto
     * @param po
     * @return
     */
    DecErpEInvoiceDto toInvoiceDto(DecErpEHeadN po);

    /**
     * 数据库原始数据更新
     *
     * @param decErpEHeadNParam
     * @param decErpEHeadN
     */
    void updatePo(DecErpEHeadNParam decErpEHeadNParam, @MappingTarget DecErpEHeadN decErpEHeadN);

    /**
     * 数据库原始数据更新
     *
     * @param decErpHeadUpdateNParam
     * @param decErpEHeadN
     */
    void updateBack(DecErpHeadUpdateNParam decErpHeadUpdateNParam, @MappingTarget DecErpEHeadN decErpEHeadN);

    default void patchPo(DecErpEHeadNParam decErpEHeadNParam, DecErpEHeadN decErpEHeadN) {
        // TODO 自行实现局部更新
    }

    /**
     * 数据库原始数据更新
     *
     * @param erpDecEHead
     * @param decErpEHeadN
     */
    void updateExtract(ErpDecEHead erpDecEHead, @MappingTarget DecErpEHeadN decErpEHeadN);

    void updatePo(DecErpHeadTemplete decErpHeadTemplete, @MappingTarget DecErpEHeadN decErpIHeadN);

    void updatePo(DecErpEHeadN decErpEHeadN, @MappingTarget DecEBillHead decEBillHead);

    void updatePo(DecErpEHeadN decErpEHeadN, @MappingTarget DecEEntryHead decEEntryHead);

}
