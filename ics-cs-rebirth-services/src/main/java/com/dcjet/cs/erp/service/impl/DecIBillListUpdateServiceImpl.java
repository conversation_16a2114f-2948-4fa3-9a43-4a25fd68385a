package com.dcjet.cs.erp.service.impl;

import com.dcjet.cs.base.service.impl.BasicServiceImpl;
import com.dcjet.cs.common.model.ImportData;
import com.dcjet.cs.common.model.ImportValidation;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto.erp.DecIBillListDto;
import com.dcjet.cs.dto.erp.DecIBillListParam;
import com.dcjet.cs.dto.erp.DecIEBillListUpdateParam;
import com.dcjet.cs.erp.dao.DecIBillListMapper;
import com.dcjet.cs.erp.dao.DecIBillListUpdateMapper;
import com.dcjet.cs.erp.mapper.DecIBillListUpdateDtoMapper;
import com.dcjet.cs.erp.model.DecIBillList;
import com.dcjet.cs.erp.service.DecIBillListService;
import com.dcjet.cs.erp.service.DecIBillListUpdateService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.pcode.service.PCodeHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class DecIBillListUpdateServiceImpl
        extends BasicServiceImpl<DecIBillListUpdateMapper, DecIBillList, DecIBillListDto, DecIBillListParam, DecIEBillListUpdateParam, DecIBillListUpdateDtoMapper>
        implements DecIBillListUpdateService {

    @Resource
    DecIBillListService decIBillListService;
    @Resource
    DecIBillListUpdateDtoMapper decIBillListUpdateDtoMapper;
    @Resource
    DecIBillListUpdateMapper decIBillListUpdateMapper;
    @Resource
    DecIBillListMapper decIBillListMapper;
    @Resource
    PCodeHolder pCodeHolder;

    @Override
    public ImportValidation<DecIEBillListUpdateParam> importValidation(ImportData<DecIEBillListUpdateParam> importData, ExcelImportParam param, UserInfoToken token) {
        List<DecIEBillListUpdateParam> correctList = importData.getCorrectData();
        Map<String, Object> bizParam = importData.getBizParam();
        List<DecIEBillListUpdateParam> corrects = new ArrayList<>(correctList.size());
        List<DecIEBillListUpdateParam> errors = new ArrayList<>(correctList.size());
        for (DecIEBillListUpdateParam val : correctList) {
            DecIBillList decIBillList = decIBillListUpdateDtoMapper.toPo(val);
            decIBillList.setTradeCode(token.getCompany());
            decIBillList.setHeadId(bizParam.get("headId").toString());
            DecIBillList decIBillList1 = decIBillListMapper.selectList(decIBillList);
            if (decIBillList1 != null) {
                val.setSid(decIBillList1.getSid());
                // 数据详细校验规则
                // checkData(val, token, importData.getHeadFieldMap());
            } else {
                val.setErrMsg(xdoi18n.XdoI18nUtil.t("未找到对应数据，无法操作|"));
            }

            if (val.getErrMsg() == null || val.getErrMsg() == "") {
                corrects.add(val);
            } else {
                errors.add(val);
            }
        }

        return new ImportValidation<>(corrects, errors);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importSave(ImportData<DecIEBillListUpdateParam> data, UserInfoToken token) {
        int sum = 0;
        Date date = new Date();
        for (DecIEBillListUpdateParam param : data.getCorrectData()) {
            DecIBillList decIBillList = decIBillListUpdateDtoMapper.toPo(param);
            decIBillList.setUpdateUser(token.getUserNo());
            decIBillList.setUpdateUserName(token.getUserName());
            decIBillList.setUpdateTime(date);
            decIBillListUpdateMapper.updateByPrimaryKeySelective(decIBillList);
            sum++;
        }
        return sum;
    }
}
