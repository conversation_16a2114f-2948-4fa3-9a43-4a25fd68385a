package com.dcjet.cs.erp.service.CreateBills.CreateEBills.CreateBill;

import com.dcjet.cs.erp.mapper.DecErpEListNDtoMapper;
import com.dcjet.cs.erp.model.DecEBillList;
import com.dcjet.cs.erp.model.DecErpEListN;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.DecVariable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GroupByBillGNo
 *
 * <AUTHOR>
 * @date: 2020-5-29
 */
@Component
public class PublicCreateEBill {
    @Resource
    private DecErpEListNDtoMapper decErpEListNDtoMapper;

    /**
     * bill_g_no 分组
     *
     * @param decErpEListNS
     */
    public List<DecEBillList> handle(List<DecErpEListN> decErpEListNS) {

        /** 非空初始化 */
        decErpEListNS.stream().forEach(e -> {
            if (e.getQty1() == null) {
                e.setQty1(BigDecimal.ZERO);
            }
            if (e.getQty2() == null) {
                e.setQty2(BigDecimal.ZERO);
            }
            if (e.getUsdPrice() == null) {
                e.setUsdPrice(BigDecimal.ZERO);
            }
            if (e.getDecTotal() == null) {
                e.setDecTotal(BigDecimal.ZERO);
            }
            if (e.getGrossWt() == null) {
                e.setGrossWt(BigDecimal.ZERO);
            }
            if (e.getNetWt() == null) {
                e.setNetWt(BigDecimal.ZERO);
            }
            if (!CommonEnum.BondMarkEnum.BOND.getCode().equals(e.getBondMark())) {
                e.setGMark("");
            }
        });

        /**「清单序号」分组 */
        Map<BigDecimal, List<DecErpEListN>> mapForBill =
                decErpEListNS.stream().collect(Collectors.groupingBy(DecErpEListN::getBillGNo));

        List<DecEBillList> billList = new ArrayList<>(mapForBill.size());
        for (BigDecimal billGNo : mapForBill.keySet()) {

            DecEBillList bill = new DecEBillList();
            /** updatePo */
            List<DecErpEListN> decErps = mapForBill.get(billGNo);
            decErpEListNDtoMapper.updatePo(decErps.get(0), bill);

            bill.setSid(UUID.randomUUID().toString());
            if (decErps.size() > 1 && decErps.stream().map(e -> e.getCopGModel()).distinct().count() > 1) {
                bill.setCopGModel(decErps.get(0).getGModel());
                bill.setGModel(decErps.get(0).getGModel());
            } else {
                bill.setCopGModel(decErps.get(0).getCopGModel());
                bill.setGModel(decErps.get(0).getCopGModel());
            }
            /** 数值型字段汇总 */
            {
                Double sumQty = decErps.stream().mapToDouble(e -> e.getQty().doubleValue()).sum();
                Double sumQty1 = decErps.stream().mapToDouble(e -> e.getQty1().doubleValue()).sum();
                Double sumQty2 = decErps.stream().mapToDouble(e -> e.getQty2().doubleValue()).sum();
                Double sumDecTotal = decErps.stream().mapToDouble((e -> e.getDecTotal().doubleValue())).sum();
                Double sumUsdPrice = decErps.stream().mapToDouble(e -> e.getUsdPrice().doubleValue()).sum();
                Double sumGrossWt = decErps.stream().mapToDouble(e -> e.getGrossWt().doubleValue()).sum();
                Double sumNetWt = decErps.stream().mapToDouble(e -> e.getNetWt().doubleValue()).sum();

                bill.setQty(new BigDecimal(sumQty));
                bill.setQty1(new BigDecimal(sumQty1));
                bill.setQty2(new BigDecimal(sumQty2));
                bill.setDecTotal(new BigDecimal(sumDecTotal));
                bill.setUsdPrice(new BigDecimal(sumUsdPrice));
                bill.setGrossWt(new BigDecimal(sumGrossWt));
                bill.setNetWt(new BigDecimal(sumNetWt));
            }

            bill.setSerialNo(decErps.get(0).getBillGNo());
            bill.setItemNo(decErps.get(0).getItemNo());
            bill.setErpListIds(decErps.stream().map(e -> e.getSid()).collect(Collectors.joining(",")));
            if (BigDecimal.ZERO.compareTo(bill.getQty()) != 0) {
                bill.setDecPrice(bill.getDecTotal().divide(bill.getQty(), DecVariable.TOTAL_SCALE, BigDecimal.ROUND_HALF_UP));
            }
            bill.setClientCode(decErps.get(0).getSupplierCode());
            bill.setClientName(decErps.get(0).getSupplierName());
            billList.add(bill);
        }

        /** 排序!!! */
        billList.sort(Comparator.comparing(DecEBillList::getSerialNo));
        return billList;
    }
}
