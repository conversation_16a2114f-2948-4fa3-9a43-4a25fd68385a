package com.dcjet.cs.erp.service;

import com.dcjet.cs.agent.DecBillHeadAgentService;
import com.dcjet.cs.cert.service.CertificateCatalogListService;
import com.dcjet.cs.cert.service.CertificateCatalogService;
import com.dcjet.cs.cert.service.impl.InvolveCertIService;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.common.service.FastdFsService;
import com.dcjet.cs.dto.erp.*;
import com.dcjet.cs.dto.mat.MatIeInfoDto;
import com.dcjet.cs.dto.war.WarringPassConfigDto;
import com.dcjet.cs.dto.war.WarringPassExpandConfigDto;
import com.dcjet.cs.dto.ztyth.ZtythEmsHeadDto;
import com.dcjet.cs.dto.ztyth.ZtythEmsHeadNewListDto;
import com.dcjet.cs.entry.dao.DecIEntryHeadMapper;
import com.dcjet.cs.entry.model.DecIEntryHead;
import com.dcjet.cs.entry.model.DecIEntryList;
import com.dcjet.cs.entry.service.DcEntryEdocrealationService;
import com.dcjet.cs.entry.service.DecIEntryContainerService;
import com.dcjet.cs.entry.service.DecIEntryHeadService;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIListNMapper;
import com.dcjet.cs.erp.dao.DecIBillHeadMapper;
import com.dcjet.cs.erp.mapper.DecIBillHeadDtoMapper;
import com.dcjet.cs.erp.model.*;
import com.dcjet.cs.mat.dao.AttachedMapper;
import com.dcjet.cs.mat.dao.MatImgexgOrgMapper;
import com.dcjet.cs.mat.dao.MatNonBondedMapper;
import com.dcjet.cs.mat.model.Attached;
import com.dcjet.cs.mat.model.MatImgexgOrg;
import com.dcjet.cs.mat.model.MatNonBonded;
import com.dcjet.cs.mat.service.MatImgexgOrgService;
import com.dcjet.cs.util.*;
import com.dcjet.cs.util.auditLog.AuditLogUtil;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.dcjet.cs.war.service.WarringPassExpandConfigService;
import com.dcjet.cs.ztyth.service.ZtythEmsImgExgService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.hibernate.validator.internal.util.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.beans.Transient;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Service
public class DecIBillHeadService extends BaseService<DecIBillHead> {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Resource
    private DecIBillHeadMapper decIBillHeadMapper;
    @Resource
    private DecIBillHeadDtoMapper decIBillHeadDtoMapper;
    @Resource
    private DecErpIListNMapper decErpIListNMapper;
    @Resource
    private WarringPassConfigService warringPassConfigService;
    @Resource
    private WarringPassExpandConfigService warringPassExpandConfigService;
    @Override
    public Mapper<DecIBillHead> getMapper() {
        return decIBillHeadMapper;
    }

    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private DecErpIHeadNService decErpIHeadNService;
    @Resource
    private DecIBillListService decIBillListService;
    @Resource
    private ZtythEmsImgExgService ztythEmsImgExgService;
    @Resource
    private CommonService commonService;
    @Resource
    private DecIEntryContainerService decIEntryContainerService;
    @Resource
    private CertificateCatalogService certificateCatalogService;
    @Resource
    private CertificateCatalogListService certificateCatalogListService;
    @Resource
    private InvolveCertIService involveCertIService;
    @Resource
    private MatImgexgOrgMapper matImgexgOrgMapper;
    @Resource
    private MatNonBondedMapper matNonBondedMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private DecIEntryHeadService decIEntryHeadService;
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    @Resource
    private MatImgexgOrgService matImgexgOrgService;
    @Resource
    private AuditLogUtil auditLogUtil;
    @Resource
    private DecBillHeadAgentService agentService;
    private static final SimpleDateFormat df =new  SimpleDateFormat("yyyy-MM-dd");
    @Resource
    private Validator validator;

    @Resource
    private AttachedMapper attachedMapper;
    @Resource
    private DecIEntryHeadMapper decIEntryHeadMapper;
    @Resource
    private DcEntryEdocrealationService dcEntryEdocrealationService;
    @Resource
    private FastdFsService fastdFsService;
    @Value("${dc.export.template:}")
    private String templatePath;

    /**
     * 获取分页信息
     *
     * @param decIBillHeadParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<DecIBillHeadDto>> getListPaged(DecIBillHeadParam decIBillHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        DecIBillHead decIBillHead = decIBillHeadDtoMapper.toPo(decIBillHeadParam);
        decIBillHead.setTradeCode(userInfo.getCompany());

        decIBillHead.setInsertUser(decErpIHeadNService.getAuthority(userInfo));
        // 代理用户参数设置
//        agentService.agentHandler(decIBillHead, userInfo);

        Page<DecIBillHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decIBillHeadMapper.getListAll(decIBillHead));
        List<DecIBillHeadDto> decIBillHeadDtos = page.getResult().stream().map(head -> {
            DecIBillHeadDto dto = decIBillHeadDtoMapper.toDto(head);
            if (StringUtils.isNotEmpty(dto.getTrafName()) && StringUtils.isNotEmpty(dto.getVoyageNo())) {
                dto.setTrafName(dto.getTrafName() + "/" + dto.getVoyageNo());
            } else if (StringUtils.isEmpty(dto.getTrafName()) && StringUtils.isNotEmpty(dto.getVoyageNo())) {
                dto.setTrafName(dto.getVoyageNo());
            }
            String allWrap = (StringUtils.isBlank(dto.getWrapType()) ? "" : dto.getWrapType() + ',') + dto.getWrapType2();
            if (StringUtils.isNotBlank(allWrap)) {
                String newWarpList = "";
                String[] warpList = allWrap.split(",");
                for (String warp : warpList) {
                    String warpName = pCodeHolder.getValue(PCodeType.WRAP, warp);
                    if (StringUtils.isNotBlank(warpName)) {
                        newWarpList = newWarpList + warpName + "/";
                    }
                }
                if (newWarpList.length() > 0) {
                    dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                }
            }
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<DecIBillHeadDto>> paged = ResultObject.createInstance(decIBillHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param decIBillHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecIBillHeadDto insert(DecIBillHeadParam decIBillHeadParam, UserInfoToken userInfo) {
        DecIBillHead decIBillHead = decIBillHeadDtoMapper.toPo(decIBillHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        decIBillHead.setTradeCode(userInfo.getCompany());
        decIBillHead.setSid(sid);
        decIBillHead.setInsertUser(userInfo.getUserNo());
        decIBillHead.setInsertTime(new Date());

        // 新增数据
        int insertStatus = decIBillHeadMapper.insert(decIBillHead);
        auditLogUtil.AuditLog(CommonEnum.operationsEnum.INSERT.getValue(), "进口管理-进口清单表头", userInfo, decIBillHead);
        return insertStatus > 0 ? decIBillHeadDtoMapper.toDto(decIBillHead) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param decIBillHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecIBillHeadDto update(DecIBillHeadParam decIBillHeadParam, UserInfoToken userInfo) {
        DecIBillHead decIBillHead = decIBillHeadMapper.selectByPrimaryKey(decIBillHeadParam.getSid());
        if (decIBillHead != null) {
            decIBillHeadDtoMapper.updatePo(decIBillHeadParam, decIBillHead);
            if (!Strings.isNullOrEmpty(decIBillHead.getFeeMark())) {
                switch (decIBillHead.getFeeMark()) {
                    case "1":
                        if (decIBillHead.getFeeRate() == null) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("运费类型为1|率时，运费不能为空!"));
                        }
                        break;
                    case "2":
                        if (decIBillHead.getFeeRate() == null || decIBillHead.getFeeCurr() == null) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("运费类型为2|单价时，运费和运费币制不能为空!"));
                        }
                        break;
                    case "3":
                        if (decIBillHead.getFeeRate() == null || decIBillHead.getFeeCurr() == null) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("运费类型为3|总价时，运费和运费币制不能为空!"));
                        }
                        break;
                    default:
                        break;
                }
            }
            if (!Strings.isNullOrEmpty(decIBillHead.getInsurMark())) {
                switch (decIBillHead.getInsurMark()) {
                    case "1":
                        if (decIBillHead.getInsurRate() == null) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("保费类型为1|率时，保费不能为空!"));
                        }
                        break;
                    case "2":
                        if (decIBillHead.getInsurRate() == null || decIBillHead.getInsurCurr() == null) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("保费类型为2|单价时，保费和保费币制不能为空!"));
                        }
                        break;
                    case "3":
                        if (decIBillHead.getInsurRate() == null || decIBillHead.getInsurCurr() == null) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("保费类型为3|总价时，保费和保费币制不能为空!"));
                        }
                        break;
                    default:
                        break;
                }
            }
            if (!Strings.isNullOrEmpty(decIBillHead.getOtherMark())) {
                switch (decIBillHead.getOtherMark()) {
                    case "1":
                        if (decIBillHead.getOtherRate() == null) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("杂费类型为1|率时，杂费费不能为空!"));
                        }
                        break;
                    case "2":
                        if (decIBillHead.getOtherRate() == null || decIBillHead.getOtherCurr() == null) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("杂费类型为2|单价时，杂费和杂费币制不能为空!"));
                        }
                        break;
                    case "3":
                        if (decIBillHead.getOtherRate() == null || decIBillHead.getOtherCurr() == null) {
                            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("杂费类型为3|总价时，杂费和杂费币制不能为空!"));
                        }
                        break;
                    default:
                        break;
                }
            }
            if (StringUtils.isNotEmpty(decIBillHead.getEntryNo()) && decIBillHead.getEntryNo().length() != 18) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("报关单号必须为18位，请重新填写!"));
            }
            if (decIBillHeadMapper.selectEntryNoCount(decIBillHead) > 0) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("本企业报关单号重复，请重新填写!"));
            }
            decIBillHead.setUpdateUser(userInfo.getUserNo());
            decIBillHead.setUpdateUserName(userInfo.getUserName());
            decIBillHead.setUpdateTime(new Date());

            decIBillHeadMapper.updateEEntryNoBySid(decIBillHead, decIBillHead.getSid());

            // 更新数据
            int update = decIBillHeadMapper.updateByPrimaryKey(decIBillHead);
            auditLogUtil.AuditLog(CommonEnum.operationsEnum.UPDATE.getValue(), "进口管理-进口清单表头", userInfo, decIBillHead);
            return update > 0 ? decIBillHeadDtoMapper.toDto(decIBillHead) : null;
        } else {
            return null;
        }
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
        decIBillHeadMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecIBillHeadDto> selectAll(DecIBillHeadParam exportParam, UserInfoToken userInfo) {
        DecIBillHead decIBillHead = decIBillHeadDtoMapper.toPo(exportParam);
        decIBillHead.setTradeCode(userInfo.getCompany());
        decIBillHead.setInsertUser(decErpIHeadNService.getAuthority(userInfo));
        // 代理用户参数设置
//        agentService.agentHandler(decIBillHead, userInfo);

        List<DecIBillHeadDto> decIBillHeadDtos = new ArrayList<>();
        List<DecIBillHead> decIBillHeads = decIBillHeadMapper.getListAll(decIBillHead);
        if (CollectionUtils.isNotEmpty(decIBillHeads)) {
            decIBillHeadDtos = decIBillHeads.stream().map(head -> {
                DecIBillHeadDto dto = decIBillHeadDtoMapper.toDto(head);
                if (StringUtils.isNotEmpty(dto.getTrafName()) && StringUtils.isNotEmpty(dto.getVoyageNo())) {
                    dto.setTrafName(dto.getTrafName() + "/" + dto.getVoyageNo());
                } else if (StringUtils.isEmpty(dto.getTrafName()) && StringUtils.isNotEmpty(dto.getVoyageNo())) {
                    dto.setTrafName(dto.getVoyageNo());
                }
                String allWrap = (StringUtils.isBlank(dto.getWrapType()) ? "" : dto.getWrapType() + ',') + dto.getWrapType2();
                if (StringUtils.isNotBlank(allWrap)) {
                    String newWarpList = "";
                    String[] warpList = allWrap.split(",");
                    for (String warp : warpList) {
                        String warpName = pCodeHolder.getValue(PCodeType.WRAP, warp);
                        if (StringUtils.isNotBlank(warpName)) {
                            newWarpList = newWarpList + warpName + "/";
                        }
                    }
                    if (newWarpList.length() > 0) {
                        dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                    }
                }
                if (StringUtils.isNotBlank(dto.getBondMark())) {
                    /*
                    非保税状态回写(报关单状态)
                     */
                    if (CommonEnum.BondMarkEnum.NOBOND.getCode().equals(dto.getBondMark())) {
                        if (StringUtils.isBlank(dto.getEntryStatusName())) {
                            dto.setAllStatus(dto.getEntSendApiStatus());
                            dto.setAllStatusName(CommonEnum.BillEnum.getValue(dto.getEntSendApiStatus()));
                        } else {
                            dto.setAllStatus(dto.getEntryStatus());
                            dto.setAllStatusName(dto.getEntryStatusName());
                        }
                    }
                    /*
                    保税状态回写(清单状态)
                     */
                    if (CommonEnum.BondMarkEnum.BOND.getCode().equals(dto.getBondMark())) {
                        dto.setAllStatus(dto.getSendApiStatus());
                        dto.setAllStatusName(CommonEnum.BillEnum.getValue(dto.getSendApiStatus()));
                    }
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return decIBillHeadDtos;
    }

    public List<DecIBillHeadDto> selectForPrint(DecIBillHeadParam exportParam, UserInfoToken userInfo) {
        DecIBillHead decIBillHead = decIBillHeadDtoMapper.toPo(exportParam);
        decIBillHead.setTradeCode(userInfo.getCompany());
        List<DecIBillHeadDto> decIBillHeadDtos = new ArrayList<>();
        List<DecIBillHead> decIBillHeads = decIBillHeadMapper.getList(decIBillHead);
        if (CollectionUtils.isNotEmpty(decIBillHeads)) {
            List<DecIBillHead> listSum = decIBillHeadMapper.getListSum(decIBillHead);
            decIBillHeadDtos = decIBillHeads.stream().map(head -> {
                DecIBillHeadDto dto = decIBillHeadDtoMapper.toDto(head);
                if (listSum.size()>0 && listSum.get(0)!=null) {
                    dto.setTotalAll(listSum.get(0).getTotalAll());
                }
                if (StringUtils.isNotEmpty(dto.getTrafName()) && StringUtils.isNotEmpty(dto.getVoyageNo())) {
                    dto.setTrafName(dto.getTrafName() + "/" + dto.getVoyageNo());
                } else if (StringUtils.isEmpty(dto.getTrafName()) && StringUtils.isNotEmpty(dto.getVoyageNo())) {
                    dto.setTrafName(dto.getVoyageNo());
                }
                String allWrap = (StringUtils.isBlank(dto.getWrapType()) ? "" : dto.getWrapType() + ',') + dto.getWrapType2();
                if (StringUtils.isNotBlank(allWrap)) {
                    String newWarpList = "";
                    String[] warpList = allWrap.split(",");
                    for (String warp : warpList) {
                        String warpName = pCodeHolder.getValue(PCodeType.WRAP, warp);
                        if (StringUtils.isNotBlank(warpName)) {
                            newWarpList = newWarpList + warpName + "/";
                        }
                    }
                    if (newWarpList.length() > 0) {
                        dto.setWrapTypeName(newWarpList.substring(0, newWarpList.length() - 1));
                    }
                }
                if (StringUtils.isNotBlank(dto.getBondMark())) {
                    /*
                    非保税状态回写(报关单状态)
                     */
                    if (CommonEnum.BondMarkEnum.NOBOND.getCode().equals(dto.getBondMark())) {
                        if (StringUtils.isBlank(dto.getEntryStatusName())) {
                            dto.setAllStatus(dto.getEntSendApiStatus());
                            dto.setAllStatusName(CommonEnum.BillEnum.getValue(dto.getEntSendApiStatus()));
                        } else {
                            dto.setAllStatus(dto.getEntryStatus());
                            dto.setAllStatusName(dto.getEntryStatusName());
                        }
                    }
                    /*
                    保税状态回写(清单状态)
                     */
                    if (CommonEnum.BondMarkEnum.BOND.getCode().equals(dto.getBondMark())) {
                        dto.setAllStatus(dto.getSendApiStatus());
                        dto.setAllStatusName(CommonEnum.BillEnum.getValue(dto.getSendApiStatus()));
                    }
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return decIBillHeadDtos;
    }

    /**
     * 功能描述: grid分页查询
     *
     * @auther: 沈振宇
     * @date: 2018-11-07
     * @param: param 查询参数
     * @param: pageNum 页码
     * @param: pageSize 每页记录数
     * @param: count 查询数据总数
     * @param: sortName 排序列表(单排)
     * @param: sortOrder 排序规则（asc/desc）
     * @param: multiSort 是否多排
     * @return: com.dcits.base.model.PagedObject<java.util.List < java.util.Map < java.lang.String, java.lang.Object>>>
     */
    public List<Map<String, Object>> selectListByHeadId(String headId, UserInfoToken userInfo) {

        Map<String, Object> param = new HashMap<>(2);
        param.put("headId", headId);
        param.put("tradeCode", userInfo.getCompany());

        return decIBillHeadMapper.selectListByHeadId(param);
    }

    public List<Map<String, Object>> selectBillListByHeadId(String headId, UserInfoToken userInfo) {

        Map<String, Object> param = new HashMap<>(2);
        param.put("headId", headId);
        param.put("tradeCode", userInfo.getCompany());

        return decIBillHeadMapper.selectBillListByHeadId(param);
    }

    /**
     * 生成清单
     *
     * @param para erpHeadId 传入参数 提单表头oid isSucc 输入参数 是否成功 errMsg 输入参数 错误信息
     * <AUTHOR>
     */
    public Map<String, String> createBills(Map<String, Object> para) {
        return decIBillHeadMapper.createBills(para);
    }

    /**
     * 生成清单
     *
     * @param para erpHeadId 传入参数 提单表头oid isSucc 输入参数 是否成功 errMsg 输入参数 错误信息
     * <AUTHOR>
     */
    public void createBillsJg(Map<String, Object> para) {
        decIBillHeadMapper.createBillsJg(para);
    }

    /**
     * 生成清单
     *
     * @param para erpHeadId 传入参数 提单表头oid isSucc 输入参数 是否成功 errMsg 输入参数 错误信息
     * <AUTHOR>
     */
    public Map<String, String> createBillsJgMerge(Map<String, Object> para) {
        Map<String, String> result = decIBillHeadMapper.createBillsJgMerge(para);
        return result;
    }

    public Map<String, String> createBillsNew(Map<String, Object> para) {

        Map<String, String> result = decIBillHeadMapper.createBillsNew(para);
        return result;
    }

    /**
     * 功能描述: 清单生成校验
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/6/26
     * @param: headId 提单表头sid
     * @param: tradeCode 企业编码
     * @return:
     */
    public ResultObject<List<DecErpIListNDto>> createBillsCheck(DecErpIHeadN decErpIHeadN, UserInfoToken userInfo) throws Exception {
        logger.info(String.format("单据号%s开始进口清单生成校验",decErpIHeadN.getEmsListNo()));
        if (!Arrays.asList(DecVariable.DEL_STATUS_IE).contains(decErpIHeadN.getApprStatus())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有 “暂存”、“清单生成”、“内审退回”、“清单撤回” 状态数据可重新生成!"));
        }
        ResultObject result = ResultObject.createInstance(true);
        String headId = decErpIHeadN.getSid();
        logger.info(String.format("进口清单生成%s校验表头必填项",decErpIHeadN.getEmsListNo()));

        List<DecErpIListN> checkResult = new ArrayList<>();

        Set<ConstraintViolation<DecErpIHeadN>> constraintViolations = validator.validate(decErpIHeadN);
        if (constraintViolations != null && constraintViolations.size() > 0) {
            for (ConstraintViolation<DecErpIHeadN> cv : constraintViolations) {
                //这里循环获取错误信息，可以自定义格式
                checkResult.add(new DecErpIListN() {
                    {
                        setDataMark("head");
                        setWarningMark(DecVariable.STATUS_2);
                        setWarningMsg(cv.getMessage());
                    }
                });
            }
        }
//        if (decErpIHeadN.getEmsListNo().contains(",")){
//            checkResult.add(new DecErpIListN(){
//                {
//                    setDataMark("head");
//                    setWarningMark(DecVariable.STATUS_2);
//                    setWarningMsg("单据内部编号不允许存在特殊字符");
//                }
//            });
//        }
        logger.info(String.format("进口清单生成%s特殊关联校验",decErpIHeadN.getEmsListNo()));
        if (CommonEnum.DCLCUS_MARK.MARK_1.getCode().equals(decErpIHeadN.getDclcusMark()) && StringUtils.isBlank(decErpIHeadN.getDeclareCode())) {
            checkResult.add(new DecErpIListN() {
                {
                    setDataMark("head");
                    setWarningMark(DecVariable.STATUS_2);
                    setWarningMsg(xdoi18n.XdoI18nUtil.t("报关行海关十位代码不能为空"));
                }
            });
        }
        if (CommonEnum.DCLCUS_MARK.MARK_1.getCode().equals(decErpIHeadN.getDclcusMark()) && StringUtils.isBlank(decErpIHeadN.getDeclareCreditCode())) {
            checkResult.add(new DecErpIListN() {
                {
                    setDataMark("head");
                    setWarningMark(DecVariable.STATUS_2);
                    setWarningMsg(xdoi18n.XdoI18nUtil.t("报关行社会信用代码不能为空"));
                }
            });
        }
        if (CommonEnum.DCLCUS_MARK.MARK_1.getCode().equals(decErpIHeadN.getDclcusMark()) && StringUtils.isBlank(decErpIHeadN.getDeclareName())) {
            checkResult.add(new DecErpIListN() {
                {
                    setDataMark("head");
                    setWarningMark(DecVariable.STATUS_2);
                    setWarningMsg(xdoi18n.XdoI18nUtil.t("报关行企业名称不能为空"));
                }
            });
        }
        if (!CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark()) && StringUtils.isBlank(decErpIHeadN.getAgentCode())) {
            checkResult.add(new DecErpIListN() {
                {
                    setDataMark("head");
                    setWarningMark(DecVariable.STATUS_2);
                    setWarningMsg(xdoi18n.XdoI18nUtil.t("清单申报单位海关十位代码不能为空"));
                }
            });
        }
        if (!CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark()) && StringUtils.isBlank(decErpIHeadN.getAgentCreditCode())) {
            checkResult.add(new DecErpIListN() {
                {
                    setDataMark("head");
                    setWarningMark(DecVariable.STATUS_2);
                    setWarningMsg(xdoi18n.XdoI18nUtil.t("清单申报单位社会信用代码不能为空"));
                }
            });
        }
        if (!CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark()) && StringUtils.isBlank(decErpIHeadN.getAgentName())) {
            checkResult.add(new DecErpIListN() {
                {
                    setDataMark("head");
                    setWarningMark(DecVariable.STATUS_2);
                    setWarningMsg(xdoi18n.XdoI18nUtil.t("清单申报单位名称不能为空"));
                }
            });
        }
        logger.info(String.format("进口清单生成%s运保杂校验开始",decErpIHeadN.getEmsListNo()));
        if (!Strings.isNullOrEmpty(decErpIHeadN.getFeeMark())) {
            switch (decErpIHeadN.getFeeMark()) {
                case "1":
                    if (decErpIHeadN.getFeeRate() == null) {
                        checkResult.add(new DecErpIListN() {
                            {
                                setDataMark("head");
                                setWarningMark(DecVariable.STATUS_2);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("运费类型为1|率时，运费不能为空"));
                            }
                        });
                    }
                    break;
                case "2":
                    if (decErpIHeadN.getFeeRate() == null || decErpIHeadN.getFeeCurr() == null) {
                        checkResult.add(new DecErpIListN() {
                            {
                                setDataMark("head");
                                setWarningMark(DecVariable.STATUS_2);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("运费类型为2|单价时，运费和运费币制不能为空"));
                            }
                        });
                    }
                    break;
                case "3":
                    if (decErpIHeadN.getFeeRate() == null || decErpIHeadN.getFeeCurr() == null) {
                        checkResult.add(new DecErpIListN() {
                            {
                                setDataMark("head");
                                setWarningMark(DecVariable.STATUS_2);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("运费类型为3|总价时，运费和运费币制不能为空"));
                            }
                        });
                    }
                    break;
                default:
                    break;
            }
        }
        if (!Strings.isNullOrEmpty(decErpIHeadN.getInsurMark())) {
            switch (decErpIHeadN.getInsurMark()) {
                case "1":
                    if (decErpIHeadN.getInsurRate() == null) {
                        checkResult.add(new DecErpIListN() {
                            {
                                setDataMark("head");
                                setWarningMark(DecVariable.STATUS_2);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("保费类型为1|率时，保费不能为空"));
                            }
                        });
                    }
                    break;
                case "2":
                    if (decErpIHeadN.getInsurRate() == null || decErpIHeadN.getInsurCurr() == null) {
                        checkResult.add(new DecErpIListN() {
                            {
                                setDataMark("head");
                                setWarningMark(DecVariable.STATUS_2);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("保费类型为2|单价时，保费和保费币制不能为空"));
                            }
                        });
                    }
                    break;
                case "3":
                    if (decErpIHeadN.getInsurRate() == null || decErpIHeadN.getInsurCurr() == null) {
                        checkResult.add(new DecErpIListN() {
                            {
                                setDataMark("head");
                                setWarningMark(DecVariable.STATUS_2);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("保费类型为3|总价时，保费和保费币制不能为空"));
                            }
                        });
                    }
                    break;
                default:
                    break;
            }
        }
        if (!Strings.isNullOrEmpty(decErpIHeadN.getOtherMark())) {
            switch (decErpIHeadN.getOtherMark()) {
                case "1":
                    if (decErpIHeadN.getOtherRate() == null) {
                        checkResult.add(new DecErpIListN() {
                            {
                                setDataMark("head");
                                setWarningMark(DecVariable.STATUS_2);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("杂费类型为1|率时，杂费费不能为空"));
                            }
                        });
                    }
                    break;
                case "2":
                    if (decErpIHeadN.getOtherRate() == null || decErpIHeadN.getOtherCurr() == null) {
                        checkResult.add(new DecErpIListN() {
                            {
                                setDataMark("head");
                                setWarningMark(DecVariable.STATUS_2);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("杂费类型为2|单价时，杂费和杂费币制不能为空"));
                            }
                        });
                    }
                    break;
                case "3":
                    if (decErpIHeadN.getOtherRate() == null || decErpIHeadN.getOtherCurr() == null) {
                        checkResult.add(new DecErpIListN() {
                            {
                                setDataMark("head");
                                setWarningMark(DecVariable.STATUS_2);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("杂费类型为3|总价时，杂费和杂费币制不能为空"));
                            }
                        });
                    }
                    break;
                default:
                    break;
            }
        }
        /*
        报关单状态为X时（二步申报）时候，非保税校验涉税、涉证、涉检栏位必填
         */
        if (StringUtils.isNotEmpty(decErpIHeadN.getEntryType()) && "X".equals(decErpIHeadN.getEntryType().toUpperCase())) {
            logger.info(String.format("进口清单生成%s报关单状态为X时特殊校验开始",decErpIHeadN.getEmsListNo()));
            if (StringUtils.isEmpty(decErpIHeadN.getBondMark()) && StringUtils.isNotEmpty(headId)) {
                Example example = new Example(DecErpIListN.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("headId", headId);
                criteria.andEqualTo("tradeCode", userInfo.getCompany());
                criteria.andEqualTo("bondMark", CommonEnum.BondMarkEnum.NOBOND.getCode());
                int nobondCount = decErpIListNMapper.selectCountByExample(example);
                if (nobondCount > 0 && (StringUtils.isEmpty(decErpIHeadN.getCardMark()) || StringUtils.isEmpty(decErpIHeadN.getCheckMark()) || StringUtils.isEmpty(decErpIHeadN.getTaxMark()))) {
                    checkResult.add(new DecErpIListN() {
                        {
                            setDataMark("head");
                            setWarningMark(DecVariable.STATUS_2);
                            setWarningMsg(xdoi18n.XdoI18nUtil.t("存在两步申报报关单数据，请先至表头维护两步申报模式"));
                        }
                    });
                }
            }
            /*
            表头有保完税状态时，为非保税小提单，校验表头栏位
             */
            else if (StringUtils.isNotEmpty(decErpIHeadN.getBondMark()) && CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark())) {
                if (StringUtils.isEmpty(decErpIHeadN.getCardMark()) || StringUtils.isEmpty(decErpIHeadN.getCheckMark()) || StringUtils.isEmpty(decErpIHeadN.getTaxMark())) {
                    checkResult.add(new DecErpIListN() {
                        {
                            setDataMark("head");
                            setWarningMark(DecVariable.STATUS_2);
                            setWarningMsg(xdoi18n.XdoI18nUtil.t("存在两步申报报关单数据，请先至表头维护两步申报模式"));
                        }
                    });
                }
            }
        }
        /**
         * 根据运输方式和监管方式做关联备案号校验（备案号不为空）
         * 当运输方式为(1-监管仓库，7-保税区，8-保税仓库，W-物流中心，X-物流园区，Y-保税港区，Z-出口加工区)时
         * 当监管方式为：0255-来料深加工、0654-进料深加工、0657-进料余料结转、0258-来料余料结转时
         * 关联备案号为必填
         * 日期:2020-02-07
         */
        if (StringUtils.isNotEmpty(decErpIHeadN.getEmsNo()) && StringUtils.isNotEmpty(decErpIHeadN.getTrafMode())) {
            if ("178WXYZ".contains(decErpIHeadN.getTrafMode()) && StringUtils.isBlank(decErpIHeadN.getRelEmsNo())) {
                String msg = String.format("[%s]%s",decErpIHeadN.getTrafMode() + "-" + pCodeHolder.getValue(PCodeType.TRANSF, decErpIHeadN.getTrafMode()),xdoi18n.XdoI18nUtil.t("表头运输方式时,关联备案号不能为空"));
                checkResult.add(new DecErpIListN() {
                    {
                        setDataMark("head");
                        setWarningMark(DecVariable.STATUS_2);
                        setWarningMsg(msg);
                    }
                });
            }
        }
        if (StringUtils.isNotEmpty(decErpIHeadN.getEmsNo()) && StringUtils.isNotEmpty(decErpIHeadN.getTradeMode())) {
            if ("0255065406570258".contains(decErpIHeadN.getTradeMode()) && StringUtils.isBlank(decErpIHeadN.getRelEmsNo())) {
                String msg = String.format("[%s]%s",decErpIHeadN.getTradeMode() + "-" + pCodeHolder.getValue(PCodeType.TRADE, decErpIHeadN.getTradeMode()),xdoi18n.XdoI18nUtil.t("表头监管方式时,关联备案号不能为空"));
                checkResult.add(new DecErpIListN() {
                    {
                        setDataMark("head");
                        setWarningMark(DecVariable.STATUS_2);
                        setWarningMsg(msg);
                    }
                });
            }
        }
         /*
        通关业务配置校验
         */
        logger.info(String.format("进口清单生成%s通关业务配置校验开始",decErpIHeadN.getEmsListNo()));
        List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(userInfo);
        if (warringPassConfigDtos != null) {
            if (warringPassConfigDtos.size() > 0) {
                WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
                logger.info(String.format("进口清单生成%s通关业务配置校验开始-表头",decErpIHeadN.getEmsListNo()));
                if (StringUtils.isEmpty(decErpIHeadN.getDecType()) || DecVariable.DEC_TYPE_0.equals(decErpIHeadN.getDecType()) || CommonEnum.BondMarkEnum.BOND.getCode().equals(decErpIHeadN.getBondMark())) {
                    checkResult = check(checkResult, warringPassConfigDto, "head", "wrapType", decErpIHeadN.getWrapType(), xdoi18n.XdoI18nUtil.t("表头包装种类不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "packNum", decErpIHeadN.getPackNum(), xdoi18n.XdoI18nUtil.t("表头件数不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "netWt", decErpIHeadN.getNetWt(), xdoi18n.XdoI18nUtil.t("表头总净重不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "grossWt", decErpIHeadN.getGrossWt(), xdoi18n.XdoI18nUtil.t("表头总毛重不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "transMode", decErpIHeadN.getTransMode(), xdoi18n.XdoI18nUtil.t("表头成交方式不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "trafName", decErpIHeadN.getTrafName(), xdoi18n.XdoI18nUtil.t("表头运输工具及航次不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "tradeNation", decErpIHeadN.getTradeNation(), xdoi18n.XdoI18nUtil.t("表头贸易国(地区)不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "tradeTerms", decErpIHeadN.getTradeTerms(), xdoi18n.XdoI18nUtil.t("表头贸易条款不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "invoiceNo", decErpIHeadN.getInvoiceNo(), xdoi18n.XdoI18nUtil.t("表头发票号不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "contrNo", decErpIHeadN.getContrNo(), xdoi18n.XdoI18nUtil.t("表头合同协议号不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "forwardCode", decErpIHeadN.getForwardCode(), xdoi18n.XdoI18nUtil.t("表头货运代理不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "iedate", decErpIHeadN.getIedate(), xdoi18n.XdoI18nUtil.t("表头进口日期不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "feeMark", decErpIHeadN.getFeeMark(), xdoi18n.XdoI18nUtil.t("表头运费不能为空"), decErpIHeadN.getTransMode(), "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "insurMark", decErpIHeadN.getInsurMark(), xdoi18n.XdoI18nUtil.t("表头保费不能为空"), decErpIHeadN.getTransMode(), "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "districtCode", decErpIHeadN.getDistrictCode(), xdoi18n.XdoI18nUtil.t("表头境内目的地不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "overseasShipper", decErpIHeadN.getOverseasShipper(), xdoi18n.XdoI18nUtil.t("表头境外发货人不能为空"), null, "iBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "masterCustoms", decErpIHeadN.getMasterCustoms(), xdoi18n.XdoI18nUtil.t("表头申报地海关(主管海关)不能为空"), null, "iBond");
                    check(checkResult, warringPassConfigDto, "head", "shipFrom", decErpIHeadN.getShipFrom(), xdoi18n.XdoI18nUtil.t("表头Ship From不能为空"), null, "iBond");
                    check(checkResult, warringPassConfigDto, "head", "hawb", decErpIHeadN.getHawb(), xdoi18n.XdoI18nUtil.t("表头提运单号不能为空"), null, "iBond");
                    checkDefaultTrue(checkResult, warringPassConfigDto, "head", "tradeCountry", decErpIHeadN.getTradeCountry(), xdoi18n.XdoI18nUtil.t("表头启运国不能为空"), null, "iBond");
                    checkDefaultTrue(checkResult, warringPassConfigDto, "head", "trafMode", decErpIHeadN.getTrafMode(), xdoi18n.XdoI18nUtil.t("表头运输方式不能为空"), null, "iBond");
                    checkDefaultTrue(checkResult, warringPassConfigDto, "head", "ieport", decErpIHeadN.getIEPort(), xdoi18n.XdoI18nUtil.t("表头进境关别不能为空"), null, "iBond");
                } else {
                    checkResult = check(checkResult, warringPassConfigDto, "head", "wrapType", decErpIHeadN.getWrapType(), xdoi18n.XdoI18nUtil.t("表头包装种类不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "packNum", decErpIHeadN.getPackNum(), xdoi18n.XdoI18nUtil.t("表头件数不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "netWt", decErpIHeadN.getNetWt(), xdoi18n.XdoI18nUtil.t("表头总净重不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "grossWt", decErpIHeadN.getGrossWt(), xdoi18n.XdoI18nUtil.t("表头总毛重不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "transMode", decErpIHeadN.getTransMode(), xdoi18n.XdoI18nUtil.t("表头成交方式不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "trafName", decErpIHeadN.getTrafName(), xdoi18n.XdoI18nUtil.t("表头运输工具及航次不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "tradeNation", decErpIHeadN.getTradeNation(), xdoi18n.XdoI18nUtil.t("表头贸易国(地区)不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "tradeTerms", decErpIHeadN.getTradeTerms(), xdoi18n.XdoI18nUtil.t("表头贸易条款不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "invoiceNo", decErpIHeadN.getInvoiceNo(), xdoi18n.XdoI18nUtil.t("表头发票号不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "contrNo", decErpIHeadN.getContrNo(), xdoi18n.XdoI18nUtil.t("表头合同协议号不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "forwardCode", decErpIHeadN.getForwardCode(), xdoi18n.XdoI18nUtil.t("表头货运代理不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "iedate", decErpIHeadN.getIedate(), xdoi18n.XdoI18nUtil.t("表头进口日期不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "feeMark", decErpIHeadN.getFeeMark(), xdoi18n.XdoI18nUtil.t("表头运费不能为空"), decErpIHeadN.getTransMode(), "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "insurMark", decErpIHeadN.getInsurMark(), xdoi18n.XdoI18nUtil.t("表头保费不能为空"), decErpIHeadN.getTransMode(), "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "districtCode", decErpIHeadN.getDistrictCode(), xdoi18n.XdoI18nUtil.t("表头境内目的地不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "overseasShipper", decErpIHeadN.getOverseasShipper(), xdoi18n.XdoI18nUtil.t("表头境外发货人代码不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "overseasShipperName", decErpIHeadN.getOverseasShipperName(), xdoi18n.XdoI18nUtil.t("表头境外发货人名称不能为空"), null, "iNoBond");
                    //进口非保税
                    checkResult = check(checkResult, warringPassConfigDto, "head", "hawb", decErpIHeadN.getHawb(), xdoi18n.XdoI18nUtil.t("表头提运单号不能为空"), null, "iNoBond");
                    checkResult = check(checkResult, warringPassConfigDto, "head", "masterCustoms", decErpIHeadN.getMasterCustoms(), xdoi18n.XdoI18nUtil.t("表头申报地海关(主管海关)不能为空"), null, "iNoBond");
                    check(checkResult, warringPassConfigDto, "head", "shipFrom", decErpIHeadN.getShipFrom(), xdoi18n.XdoI18nUtil.t("表头Ship From不能为空"), null, "iNoBond");
                    checkDefaultTrue(checkResult, warringPassConfigDto, "head", "tradeCountry", decErpIHeadN.getTradeCountry(), xdoi18n.XdoI18nUtil.t("表头启运国不能为空"), null, "iNoBond");
                    checkDefaultTrue(checkResult, warringPassConfigDto, "head", "trafMode", decErpIHeadN.getTrafMode(), xdoi18n.XdoI18nUtil.t("表头运输方式不能为空"), null, "iNoBond");
                    checkDefaultTrue(checkResult, warringPassConfigDto, "head", "ieport", decErpIHeadN.getIEPort(), xdoi18n.XdoI18nUtil.t("表头进境关别不能为空"), null, "iNoBond");

                }
                if (StringUtils.isNotBlank(warringPassConfigDto.getChecksum()) && CommonVariable.CONFIG_1.equals(warringPassConfigDto.getChecksum())) {
                    Triple<String, BigDecimal, BigDecimal> res = decErpIHeadNService.statErpList(headId);
                    if (decErpIHeadN != null) {
                        if (decErpIHeadN.getSumQty() == null || decErpIHeadN.getSumQty().floatValue() != res.getMiddle().floatValue()) {
                            checkResult.add(new DecErpIListN() {
                                {
                                    setDataMark("head");
                                    setWarningMark(DecVariable.STATUS_2);
                                    setWarningMsg(xdoi18n.XdoI18nUtil.t("表头总数量不一致"));
                                }
                            });
                        }
                        if (decErpIHeadN.getSumDecTotal() == null || decErpIHeadN.getSumDecTotal().floatValue() != res.getRight().floatValue()) {
                            checkResult.add(new DecErpIListN() {
                                {
                                    setDataMark("head");
                                    setWarningMark(DecVariable.STATUS_2);
                                    setWarningMsg(xdoi18n.XdoI18nUtil.t("表头总价不一致"));
                                }
                            });
                        }
                    }
                }

                //校验杂费
                if (StringUtils.equals(warringPassConfigDto.getOtherRateCheck(), CommonVariable.CONFIG_1)) {
                    if (StringUtils.equals(decErpIHeadN.getTrafMode(), DecVariable.TRAF_MODE_2)) {
                        if (StringUtils.equals(decErpIHeadN.getTransMode(), DecVariable.TRAF_MODE_1) || StringUtils.equals(decErpIHeadN.getTransMode(), DecVariable.TRAF_MODE_2)) {
                            if (decErpIHeadN.getOtherRate() == null) {
                                checkResult.add(new DecErpIListN() {
                                    {
                                        setDataMark("head");
                                        setWarningMark(DecVariable.STATUS_1);
                                        setWarningMsg(xdoi18n.XdoI18nUtil.t("成交方式为CIF、C&F，运输方式为水路运输，杂费为空"));
                                    }
                                });
                            }
                        }
                    }
                }

                logger.info(String.format("进口清单生成%s通关业务配置校验开始-表体",decErpIHeadN.getEmsListNo()));
                /*
                表体校验
                初始化warningMark，warningMsg
                 */
                Example example = new Example(DecErpIListN.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("headId", headId);
                DecErpIListN setModel = new DecErpIListN();
                setModel.setWarningMark(DecVariable.STATUS_0);
                setModel.setWarningMsg("");
                decErpIListNMapper.updateByExampleSelective(setModel, example);

                //表体非空校验
                decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "notnull", CommonVariable.CONFIG_2, null, null);
                //非保税法二校验
                if (StringUtils.isNotBlank(decErpIHeadN.getDecType()) && StringUtils.isNotBlank(decErpIHeadN.getBondMark()) && DecVariable.DEC_TYPE_1.equals(decErpIHeadN.getDecType()) && CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark())) {
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListReFieldINoBond()) && warringPassConfigDto.getListReFieldINoBond().contains("qty2")) {
                        decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "qty2", CommonVariable.CONFIG_2, null, null);
                    }
                } else {
                    //保税、大提单法二校验
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListRequiredField()) && warringPassConfigDto.getListRequiredField().contains("qty2")) {
                        decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "qty2", CommonVariable.CONFIG_2, null, null);
                    }
                }
                //最终目的地校验
                if (StringUtils.isNotBlank(decErpIHeadN.getDecType()) && StringUtils.isNotBlank(decErpIHeadN.getBondMark()) && DecVariable.DEC_TYPE_1.equals(decErpIHeadN.getDecType()) && CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark())) {
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListReFieldINoBond()) && warringPassConfigDto.getListReFieldINoBond().contains("districtCode")) {
                        decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "districtCode", CommonVariable.CONFIG_2, null, null);
                    }
                } else {
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListRequiredField()) && warringPassConfigDto.getListRequiredField().contains("districtCode")) {
                        decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "districtCode", CommonVariable.CONFIG_2, null, null);
                    }
                }
                //最终目的地校验
                if (StringUtils.isNotBlank(decErpIHeadN.getDecType()) && StringUtils.isNotBlank(decErpIHeadN.getBondMark()) && DecVariable.DEC_TYPE_1.equals(decErpIHeadN.getDecType()) && CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark())) {
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListReFieldINoBond()) && warringPassConfigDto.getListReFieldINoBond().contains("netWt")) {
                        decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "noNetWt", CommonVariable.CONFIG_2, null, null);
                    }
                } else {
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListRequiredField()) && warringPassConfigDto.getListRequiredField().contains("netWt")) {
                        decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "noNetWt", CommonVariable.CONFIG_2, null, null);
                    }
                }
                logger.info(String.format("进口清单生成%s通关业务配置校验开始-表体阶段1",decErpIHeadN.getEmsListNo()));

                //根据配置需求 进行相应的校验
                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckCurr()) && CommonVariable.CONFIG_12.contains(warringPassConfigDto.getCheckCurr())) {
                    //校验币值
                    checkCurr(headId, userInfo, warringPassConfigDto.getCheckCurr());
                    //校验币值
//                    decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "curr", warringPassConfigDto.getCheckCurr(), null, null);
                }
                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckTotalPrice()) && CommonVariable.CONFIG_12.contains(warringPassConfigDto.getCheckTotalPrice())) {
                    //校验数量*单价是否等于总价
                    decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "decPrice", CommonVariable.CONFIG_1, null, null);
                }
                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckTradeMode()) && CommonVariable.CONFIG_12.contains(warringPassConfigDto.getCheckTradeMode())) {
                    //校验监管方式
                    decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "tradeMode", warringPassConfigDto.getCheckTradeMode(), null, null);
                }
                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckPrice()) && CommonVariable.CONFIG_12.contains(warringPassConfigDto.getCheckPrice())) {
                    //校验单价%
                    if (warringPassConfigDto.getPriceLimite() == null) {
                        warringPassConfigDto.setPriceLimite(BigDecimal.ZERO);
                    }
//                    decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "price", warringPassConfigDto.getCheckPrice(), warringPassConfigDto.getPriceLimite(), null);
                    checkDecPrice(checkResult,headId,warringPassConfigDto.getCheckPrice(),warringPassConfigDto.getPriceLimite(),userInfo);
                }
                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckNetWt()) && CommonVariable.CONFIG_12.contains(warringPassConfigDto.getCheckNetWt())) {
                    //校验净重%
                    if (warringPassConfigDto.getNetWtLimit() == null) {
                        warringPassConfigDto.setNetWtLimit(BigDecimal.ZERO);
                    }
                    if (warringPassConfigDto.getNetWtDigit() == null) {
                        warringPassConfigDto.setNetWtDigit(5);
                    }
                    decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "netWt", warringPassConfigDto.getCheckNetWt(), warringPassConfigDto.getNetWtLimit(), warringPassConfigDto.getNetWtDigit());
                }

                logger.info(String.format("进口清单生成%s通关业务配置校验开始-表体阶段2",decErpIHeadN.getEmsListNo()));
                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckGrossWt()) && CommonVariable.CONFIG_12.contains(warringPassConfigDto.getCheckGrossWt())) {
                    //校验总毛重
                    if (warringPassConfigDto.getGrossWtLimit() == null) {
                        warringPassConfigDto.setGrossWtLimit(BigDecimal.ZERO);
                    }
                    checkResult = checkGrossWtOfHead(checkResult, headId, warringPassConfigDto.getGrossWtLimit(), warringPassConfigDto.getCheckGrossWt());
                }
                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckNetWtTotal()) && CommonVariable.CONFIG_12.contains(warringPassConfigDto.getCheckNetWtTotal())) {
                    //校验总净重
                    if (warringPassConfigDto.getNetWtAllowErrVal() == null) {
                        warringPassConfigDto.setNetWtAllowErrVal(BigDecimal.ZERO);
                    }
                    checkResult = checkNetWtOfHead(checkResult, headId, warringPassConfigDto.getNetWtAllowErrVal(), warringPassConfigDto.getCheckNetWtTotal());

                }

                //表体净重0判断
                List<WarringPassExpandConfigDto> warringPassExpandConfigs = warringPassExpandConfigService.selectAll(warringPassConfigDto.getSid(),userInfo);
                if (warringPassExpandConfigs != null) {
                    if (warringPassExpandConfigs.size() > 0) {
                        WarringPassExpandConfigDto warringPassExpandConfigDto = warringPassExpandConfigs.get(0);
                        if (StringUtils.isNotBlank(warringPassExpandConfigDto.getBodyNetWtPrompt()) && CommonVariable.CONFIG_12.contains(warringPassExpandConfigDto.getBodyNetWtPrompt())) {
                            decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "netWtPrompt", warringPassExpandConfigDto.getBodyNetWtPrompt(), null, null);
                        }
                        if (StringUtils.isNotBlank(warringPassExpandConfigDto.getCodeBillCheck()) && CommonVariable.CONFIG_1.contains(warringPassExpandConfigDto.getCodeBillCheck())) {
                            decIBillHeadMapper.createCheckList(headId,decErpIHeadN.getBondMark(), warringPassExpandConfigDto.getCodeBillCheck(),userInfo.getCompany());
                        }
                    }
                }
                //涉证校验提醒
                if (StringUtils.isNotEmpty(warringPassConfigDto.getInvolveCertCheck()) && !CommonVariable.CONFIG_1.equals(warringPassConfigDto.getInvolveCertCheck())) {
                    involveCertIService.checkInvolveCert(checkResult, decErpIHeadN, warringPassConfigDto.getInvolveCertCheck(), userInfo);
                }
                if (CommonEnum.MERGE_TYPE.MERGE_TYPE_1.getCode().equals(decErpIHeadN.getMergeType())) {
                    decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "entryNo", null, null, null);
                }
                if (CommonEnum.BILL_TYPE.BILL_TYPE_4.getCode().equals(decErpIHeadN.getBillType())) {
                    decIBillHeadMapper.createBillsCheck(headId, userInfo.getCompany(), "billNo", null, null, null);
                }
                logger.info(String.format("进口清单生成%s通关业务配置校验开始-表体阶段3",decErpIHeadN.getEmsListNo()));

                //校验表头原产国
                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckCountryI()) && CommonVariable.CONFIG_12.contains(warringPassConfigDto.getCheckCountryI())) {
                    checkResult = checkList(checkResult, headId, warringPassConfigDto.getCheckCountryI(), userInfo);
                }
                if (StringUtils.isNotBlank(decErpIHeadN.getDecType()) && DecVariable.DEC_TYPE_1.equals(decErpIHeadN.getDecType()) && CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark())) {
                    //是否校验RCEP税率
                    if (StringUtils.isNotBlank(warringPassConfigDto.getCheckRcep()) && CommonVariable.CONFIG_1.contains(warringPassConfigDto.getCheckRcep())) {
                        //获取RCEP税率
                        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                        GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.PRARM_RCEP);
                        String url;
                        if (null != gwstdHttpConfig) {
                            url = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();

                            List<RcepParam> rcepParams = decErpIListNMapper.getRcepList(headId, userInfo.getCompany());
                            List<RcepParam> rcepParamList = rcepParams.stream().map(head -> {
                                if (StringUtils.isNotEmpty(head.getOriginCountryCn())) {
                                    head.setOriginCountryCn(pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, head.getOriginCountryCn()));
                                }
                                return head;
                            }).collect(Collectors.toList());
                            String postData = jsonObjectMapper.toJson(rcepParamList);
                            String str = "";
                            try {
                                str = RequestUtil.sendPost(url, userInfo.getAccessToken(), postData);
                            } catch (Exception e) {

                            }
                            ResultObject<List<RcepDto>> resultObject = jsonObjectMapper.fromJson(str, new TypeReference<ResultObject<List<RcepDto>>>() {
                            });
                            if (resultObject != null && resultObject.getData().size() > 0) {
                                List<RcepDto> rcepDtoList2 = resultObject.getData();
                                List<String> rcepDtoList = rcepDtoList2.stream().map(RcepDto::getCodeTs).collect(Collectors.toList());
                                List<DecErpIListN> list = decErpIListNMapper.getList(new DecErpIListN() {{
                                    setHeadId(headId);
                                }});
                                for (DecErpIListN decErpIListN : list) {
                                    if (rcepDtoList.contains(decErpIListN.getCodeTS())) {
                                        decErpIListN.setDataMark("list");
                                        decErpIListN.setWarningMark(DecVariable.STATUS_1);
                                        decErpIListN.setWarningMsg(xdoi18n.XdoI18nUtil.t("该商品编码涉及RCEP税率"));
                                    }
                                }
                                bulkSqlOpt.batchUpdate(list, DecErpIListNMapper.class);
                            }
                        }
                    }
                }
                //清单生成时校验单价数量总价
                checkPrice(checkResult, headId, userInfo);

                //校验预录入单表体数据和清单表体数据是否相等
                if ((StringUtils.isNotBlank(warringPassConfigDto.getCheckIDecPrice())&&!StringUtils.equals(warringPassConfigDto.getCheckIDecPrice(), ConstantsStatus.STATUS_0)) ||
                        (StringUtils.isNotBlank(warringPassConfigDto.getCheckISingleWeight())&&!StringUtils.equals(warringPassConfigDto.getCheckISingleWeight(), ConstantsStatus.STATUS_0)) ||
                        (StringUtils.isNotBlank(warringPassConfigDto.getCheckIOriginCountry())&&!StringUtils.equals(warringPassConfigDto.getCheckIOriginCountry(), ConstantsStatus.STATUS_0))) {
                    checkBill(headId, warringPassConfigDto, userInfo);
                }

                logger.info(String.format("进口清单生成%s通关业务配置校验开始-表体阶段结束",decErpIHeadN.getEmsListNo()));


                //随附单据校验
                if (StringUtils.isNotBlank(warringPassConfigDto.getEntryDocumentsAttached()) && StringUtils.equals(warringPassConfigDto.getEntryDocumentsAttached(),ConstantsStatus.STATUS_1)) {
                    checkResult = checkAttached(decErpIHeadN,checkResult, userInfo);
                }

            }
        }

        int warningCount = decErpIListNMapper.getBodyCount(new DecErpIListN() {{
            setHeadId(headId);
            setWarningMark("1,2");
        }});
        if (warningCount > 0 || checkResult.size() > 0) {
            result.setSuccess(false);
            //获取表体
            List<DecErpIListN> checkListResult = decErpIListNMapper.getList(new DecErpIListN() {{
                setHeadId(headId);
                setWarningMark("1,2");
            }});
            checkListResult.stream().forEach(e -> {
                e.setDataMark("list");
                if (StringUtils.isNotBlank(e.getWarningMsg()) && e.getWarningMsg().trim().startsWith("/")) {
                    String warringMsg = e.getWarningMsg().trim().substring(1);
                    warringMsg = warringMsg.replaceAll("备案料号不能为空","备案料号不能为空").
                            replaceAll("商品编码不能为空",xdoi18n.XdoI18nUtil.t("商品编码不能为空")).
                            replaceAll("商品名称不能为空",xdoi18n.XdoI18nUtil.t("商品名称不能为空")).
                            replaceAll("申报规格型号不能为空",xdoi18n.XdoI18nUtil.t("申报规格型号不能为空")).
                            replaceAll("申报单位不能为空",xdoi18n.XdoI18nUtil.t("申报单位不能为空")).
                            replaceAll("法一单位不能为空",xdoi18n.XdoI18nUtil.t("法一单位不能为空")).
                            replaceAll("原产国不能为空",xdoi18n.XdoI18nUtil.t("原产国不能为空")).
                            replaceAll("申报单价不能为空",xdoi18n.XdoI18nUtil.t("申报单价不能为空")).
                            replaceAll("申报总价不能为空",xdoi18n.XdoI18nUtil.t("申报总价不能为空")).
                            replaceAll("申报币制不能为空",xdoi18n.XdoI18nUtil.t("申报币制不能为空")).
                            replaceAll("申报数量不能为空",xdoi18n.XdoI18nUtil.t("申报数量不能为空")).
                            replaceAll("法一数量不能为空",xdoi18n.XdoI18nUtil.t("法一数量不能为空")).
                            replaceAll("征免方式不能为空",xdoi18n.XdoI18nUtil.t("征免方式不能为空")).
                            replaceAll("单耗版本号不能为空",xdoi18n.XdoI18nUtil.t("单耗版本号不能为空")).
                            replaceAll("币制与物料中心不一致",xdoi18n.XdoI18nUtil.t("币制与物料中心不一致")).
                            replaceAll("表体单价*数量不等于总价",xdoi18n.XdoI18nUtil.t("表体单价*数量不等于总价")).
                            replaceAll("监管方式与表头不一致",xdoi18n.XdoI18nUtil.t("监管方式与表头不一致")).
                            replaceAll("净重误差超过",xdoi18n.XdoI18nUtil.t("净重误差超过")).
                            replaceAll("单价误差超过",xdoi18n.XdoI18nUtil.t("单价误差超过")).
                            replaceAll("报关归并为人工归并时，表体归并序号不能为空",xdoi18n.XdoI18nUtil.t("报关归并为人工归并时，表体归并序号不能为空")).
                            replaceAll("清单归并为人工归并时，表体清单归并序号不能为空",xdoi18n.XdoI18nUtil.t("清单归并为人工归并时，表体清单归并序号不能为空")).
                            replaceAll("法二数量不能为空",xdoi18n.XdoI18nUtil.t("法二数量不能为空")).
                            replaceAll("境内目的地不能为空",xdoi18n.XdoI18nUtil.t("境内目的地不能为空")).
                            replaceAll("净重不能为空或0",xdoi18n.XdoI18nUtil.t("净重不能为空或0"));
                    e.setWarningMsg(warringMsg);
                }
            });
            if (CollectionUtils.isNotEmpty(checkListResult)) {
                checkResult.addAll(checkListResult);
            }
            result.setData(checkResult);
        }
        if (CollectionUtils.isNotEmpty(checkResult)) {
            //限制性校验
            long waCount = checkResult.stream()
                    .filter(x -> x.getWarningMark().equals(DecVariable.STATUS_2)).count();
            if (waCount > 0) {
                result.setSuccess(false);
                result.setErrorField(DecVariable.STATUS_1);
            }
        }
        logger.info(String.format("单据号%s结束进口清单生成校验",decErpIHeadN.getEmsListNo()));
        return result;
    }

    private List<DecErpIListN> checkAttached(DecErpIHeadN decErpIHeadN, List<DecErpIListN> checkResult, UserInfoToken userInfo) throws Exception {
        Example example = new Example(Attached.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("businessSid", decErpIHeadN.getSid());
        criteria.andEqualTo("businessType", "IM");
        List<Attached> attacheds = attachedMapper.selectByExample(example);
//        List<Attached> attacheds = attachedMapper.getCheckAll(decErpIHeadN.getSid(),"IM");
        List<Attached> attachedsNew = attacheds.stream().filter(x -> !"other".equals(x.getAcmpType())).collect(Collectors.toList());

        //校验附件
        checkResult = dcEntryEdocrealationService.checkIAttache(attachedsNew,checkResult,userInfo);

        return checkResult;
    }

    /**
     * 校验单价
     *
     * @param checkResult
     * @param headId
     * @param checkPrice
     * @param priceLimite
     * @param userInfo
     */
    private void checkDecPrice(List<DecErpIListN> checkResult, String headId, String checkPrice, BigDecimal priceLimite, UserInfoToken userInfo) {

        List<DecErpIListN> decErpIListNS = decErpIListNMapper.getListAll(headId, userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(decErpIListNS)) {
            for (DecErpIListN decErpIListN : decErpIListNS) {

                //获取物料中心币值汇率
                BigDecimal matUsd = new BigDecimal(0);
                //获取表体币值汇率
                BigDecimal listUsd = new BigDecimal(0);
                //物料中心汇率计算
                BigDecimal matCurrUsd = new BigDecimal(0);
                //表体汇率计算
                BigDecimal listCurrUsd = new BigDecimal(0);
                //比例
                BigDecimal usd = new BigDecimal(0);
                //获取物料中心数据
                Map<String,String> param = new HashedMap();
                param.put("facGNo",decErpIListN.getFacGNo());
                param.put("gMark",decErpIListN.getGMark());
                param.put("tradeCode",userInfo.getCompany());
                param.put("bondMark",decErpIListN.getBondMark());
                param.put("emsNo",decErpIListN.getEmsNo());
                MatIeInfoDto matIeInfoDto = matImgexgOrgMapper.getListFacGNo(param);
                if (matIeInfoDto!=null) {
                    if (StringUtils.isNotBlank(matIeInfoDto.getCurr())) {
                        matUsd = matImgexgOrgMapper.getMatRmb(matIeInfoDto.getCurr());
                    }
                    if (StringUtils.isNotBlank(decErpIListN.getCurr())) {
                        listUsd = matImgexgOrgMapper.getMatRmb(decErpIListN.getCurr());
                    }

                    //判断物料中心币制和表体币制是否相同
                    if (matIeInfoDto.getDecPrice() != null &&BigDecimal.ZERO.compareTo(matIeInfoDto.getDecPrice()) != 0&& decErpIListN.getDecPrice() != null) {
                        if (StringUtils.isNotBlank(matIeInfoDto.getCurr())) {
                            //物料中心汇率计算
                            matCurrUsd = matIeInfoDto.getDecPrice().multiply(matUsd);
                            //表体汇率计算
                            listCurrUsd = decErpIListN.getDecPrice().multiply(listUsd);
                        } else if (StringUtils.isBlank(matIeInfoDto.getCurr())) {
                            matCurrUsd = matIeInfoDto.getDecPrice();
                            listCurrUsd = decErpIListN.getDecPrice();
                        }

                        //计算物料中心和表体单价汇率比例
                        BigDecimal percentage = new BigDecimal(0);
                        usd = matCurrUsd.subtract(listCurrUsd);
                        if (matCurrUsd.compareTo(BigDecimal.ZERO) == 0 && listCurrUsd.compareTo(BigDecimal.ZERO) == 0) {
                            return;
                        }else {
                            percentage = new BigDecimal((usd.abs().doubleValue() / matCurrUsd.doubleValue()) * 100).setScale(8, BigDecimal.ROUND_HALF_UP);
                            //校验汇率转换后是否相等
                            if (percentage.compareTo(priceLimite) == 1) {
                                checkResult.add(new DecErpIListN() {{
                                    setFacGNo(decErpIListN.getFacGNo());
                                    setEmsNo(decErpIListN.getEmsNo());
                                    setGMark(decErpIListN.getGMark());
                                    setDataMark("list");
                                    setWarningMark(checkPrice);
                                    setWarningMsg(xdoi18n.XdoI18nUtil.t("/单价误差超过") + priceLimite + "%！|");
                                }});
                            }
                        }
                    }
                }else {
                    checkResult.add(new DecErpIListN() {{
                        setFacGNo(decErpIListN.getFacGNo());
                        setEmsNo(decErpIListN.getEmsNo());
                        setGMark(decErpIListN.getGMark());
                        setDataMark("list");
                        setWarningMark(checkPrice);
                        setWarningMsg(xdoi18n.XdoI18nUtil.t("企业料号内审未通过!|"));
                    }});
                }
            }
        }
    }

    /**
     * 校验预录入单表体数据和清单表体数据是否相等
     * @param headId
     * @param warringPassConfigDto
     * @param userInfo
     */
    private void checkBill(String headId, WarringPassConfigDto warringPassConfigDto, UserInfoToken userInfo) {

            List<DecErpIListN> listNS = decErpIListNMapper.selectListAll(headId);
        BigDecimal priceIPercentage = null;
        BigDecimal singleIPercentage = null;
        if (warringPassConfigDto.getPriceIPercentage() == null) {
            priceIPercentage = new BigDecimal(0);
        } else {
            priceIPercentage = warringPassConfigDto.getPriceIPercentage();
        }
        if (warringPassConfigDto.getSingleIPercentage() == null) {
            singleIPercentage = new BigDecimal(0);
        }
        else {
            singleIPercentage = warringPassConfigDto.getSingleIPercentage();
        }

        if (warringPassConfigDto.getPriceIPercentage()==null){
            priceIPercentage = new BigDecimal(0);
        }
        if (warringPassConfigDto.getSingleIPercentage()==null){
            singleIPercentage = new BigDecimal(0);
        }

        //查询企业物料对应保税清单最新一条数据
        List<DecIBillList> iBillListBond = decIBillHeadMapper.selectListAll(headId, "0", userInfo.getCompany());
        //查询企业物料对应非保税清单最新一条数据
        List<DecIBillList> iBillListNoBond = decIBillHeadMapper.selectListAll(headId, "1", userInfo.getCompany());

            String status = "12";
            if (listNS.size() > 0) {
                for (DecErpIListN listN : listNS) {
                    DecIBillList iBillList=null;
                    if("0".equals(listN.getBondMark())&&iBillListBond!=null&&iBillListBond.size()>0) {
                        iBillList = iBillListBond.stream().filter(x -> x.getFacGNo().equals(listN.getFacGNo())).findFirst().orElse(null);
                    }
                    if("1".equals(listN.getBondMark())&&iBillListNoBond!=null&&iBillListNoBond.size()>0) {
                        iBillList = iBillListNoBond.stream().filter(x -> x.getFacGNo().equals(listN.getFacGNo())).findFirst().orElse(null);
                    }
                    if (iBillList != null) {
                        //单价
                        BigDecimal decPrice = new BigDecimal(0);
                        //单重
                        BigDecimal single = null;
                        //预录入单表体单重
                        BigDecimal listSingle = new BigDecimal(0);
                        //清单表体单重
                        BigDecimal billSingle = new BigDecimal(0);
                        if (StringUtils.equals(listN.getFacGNo(), iBillList.getFacGNo())) {
                            if (listN.getQty().compareTo(BigDecimal.ZERO) == 1 && listN.getDecPrice().compareTo(BigDecimal.ZERO) == 1) {
                                //预录入单表体单价减清单表体单价，计算差距
                                decPrice = listN.getDecPrice().subtract(iBillList.getDecPrice());
                                decPrice = decPrice.abs().divide(iBillList.getDecPrice(), 5, BigDecimal.ROUND_HALF_UP);
                                //计算预录入单变体单重
                                if (listN.getNetWt() != null) {
                                    listSingle = listN.getNetWt().divide(listN.getQty(), 10, BigDecimal.ROUND_HALF_UP);
                                }
                                if (iBillList.getNetWt() != null) {
                                    billSingle = iBillList.getNetWt().divide(iBillList.getQty(), 10, BigDecimal.ROUND_HALF_UP);
                                }
                                //计算单重差距
//                            single = listSingle.subtract(billSingle);
                                if (listSingle.compareTo(BigDecimal.ZERO) == 1 && billSingle.compareTo(BigDecimal.ZERO) == 1) {
                                    if (listSingle.compareTo(billSingle) == 0) {
                                        single = new BigDecimal(0);
                                    } else {
                                        listSingle = listSingle.subtract(billSingle);
                                        single = listSingle.abs().divide(billSingle.abs(), 10, BigDecimal.ROUND_HALF_UP);
                                    }
                                }
                                //单价校验
                                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckIDecPrice())) {
                                    if (status.contains(warringPassConfigDto.getCheckIDecPrice())) {
                                        if (decPrice.multiply(new BigDecimal(100)).compareTo(priceIPercentage) == 1) {
                                            if (!Objects.equals(listN.getWarningMark(), "2")) {
                                                listN.setWarningMark(warringPassConfigDto.getCheckIDecPrice());
                                            }
                                            listN.setWarningMsg(listN.getWarningMsg() + xdoi18n.XdoI18nUtil.t(" 企业料号：") + listN.getFacGNo() + xdoi18n.XdoI18nUtil.t("与单据内部编号：") + iBillList.getBillEmsListNo() + xdoi18n.XdoI18nUtil.t("单价差额超出范围！|"));
                                        }
                                    }
                                }
                                //单重校验
                                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckISingleWeight())) {
                                    if (status.contains(warringPassConfigDto.getCheckISingleWeight())) {
                                        if (single != null && single.multiply(new BigDecimal(100)).compareTo(singleIPercentage) == 1) {
                                            if (!Objects.equals(listN.getWarningMark(), "2")) {
                                                listN.setWarningMark(warringPassConfigDto.getCheckISingleWeight());
                                            }
                                            listN.setWarningMsg(listN.getWarningMsg() + xdoi18n.XdoI18nUtil.t(" 企业料号：") + listN.getFacGNo() + xdoi18n.XdoI18nUtil.t("与单据内部编号：") + iBillList.getBillEmsListNo() + xdoi18n.XdoI18nUtil.t("单重差额超出范围！|"));
                                        }
                                    }
                                }
                                //原产国校验
                                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckIOriginCountry())) {
                                    if (status.contains(warringPassConfigDto.getCheckIOriginCountry())) {
                                        if (!StringUtils.equals(listN.getOriginCountry(), iBillList.getOriginCountry())) {
                                            if (!Objects.equals(listN.getWarningMark(), "2")) {
                                                listN.setWarningMark(warringPassConfigDto.getCheckIOriginCountry());
                                            }
                                            listN.setWarningMsg(listN.getWarningMsg() + xdoi18n.XdoI18nUtil.t(" 企业料号：") + listN.getFacGNo() + xdoi18n.XdoI18nUtil.t("与单据内部编号：") + iBillList.getBillEmsListNo() + xdoi18n.XdoI18nUtil.t("原产国不一致！|"));
                                        }
                                    }
                                }

                            }
                            decErpIListNMapper.updateByPrimaryKeySelective(listN);
                        }
                    }
                }
            }

    }

    /**
     * 校验币制
     * @param headId
     * @param userInfo
     * @param checkCurr
     */
    private void checkCurr(String headId, UserInfoToken userInfo, String checkCurr) {
        List<DecErpIListN> decErpIListNS = decErpIListNMapper.getListAll(headId, userInfo.getCompany());
        for (DecErpIListN listN : decErpIListNS) {
            //批量获取企业料件信息
            Map<String, MatIeInfoDto> mapMatIEInfo = matImgexgOrgService.getCurrList(Collections.singletonList(listN.getFacGNo()), userInfo.getCompany(), listN.getEmsNo(), listN.getGMark(), listN.getBondMark(), listN.getTradeMode());
            if (mapMatIEInfo.size() > 0) {
                MatIeInfoDto matModel = mapMatIEInfo.get(listN.getFacGNo());
                if (StringUtils.equals(listN.getFacGNo(), matModel.getFacGNo())
                        && !StringUtils.equals(listN.getWarningMark(), ConstantsStatus.STATUS_2)) {
                    if (StringUtils.isNotBlank(matModel.getCurr()) && !StringUtils.equals(listN.getCurr(), matModel.getCurr())
                            && !StringUtils.equals(listN.getStatus(), ConstantsStatus.STATUS_1)) {
                        listN.setWarningMark(checkCurr);
                        listN.setWarningMsg(xdoi18n.XdoI18nUtil.t("/币制与物料中心不一致"));
                        decErpIListNMapper.updateByPrimaryKeySelective(listN);
                    }
                }
            }
        }
    }

    /**
     * 清单生成时校验单价数量总价
     *
     * @param checkResult
     * @param headId
     * @param userInfo
     */
    private void checkPrice(List<DecErpIListN> checkResult, String headId, UserInfoToken userInfo) {
        List<DecErpIListN> decErpIListNS = decErpIListNMapper.getListAll(headId, userInfo.getCompany());
        BigDecimal number = BigDecimal.ZERO;
        for (DecErpIListN listN : decErpIListNS) {
            if (listN.getDecTotal() != null && listN.getQty() != null && listN.getDecPrice() != null && listN.getDecPrice().compareTo(number) == 1 && listN.getQty().compareTo(number) == 1) {
                BigDecimal result = new BigDecimal(0);
                result = listN.getDecTotal().divide(listN.getQty(), 5, BigDecimal.ROUND_HALF_UP);
                result = result.subtract(listN.getDecPrice()).abs();
                if (result.compareTo(new BigDecimal(1)) == 1) {
                    listN.setDataMark("list");
                    listN.setWarningMark(ConstantsStatus.STATUS_1);
                    listN.setWarningMsg(xdoi18n.XdoI18nUtil.t("企业料号") + listN.getFacGNo() + xdoi18n.XdoI18nUtil.t("申报总价除以申报数量，申报单价差额超过1！|"));
                }
            } else {
                if (listN.getDecPrice().compareTo(number) < 1) {
                    checkResult.add(new DecErpIListN() {{
                        setDataMark("list");
                        setWarningMark(ConstantsStatus.STATUS_2);
                        setWarningMsg(xdoi18n.XdoI18nUtil.t("企业料号") + listN.getFacGNo() + xdoi18n.XdoI18nUtil.t("申报单价不能为[0]！|"));
                    }});
                }
                if (listN.getQty().compareTo(number) < 1) {
                    checkResult.add(new DecErpIListN() {{
                        setDataMark("list");
                        setWarningMark(ConstantsStatus.STATUS_2);
                        setWarningMsg(xdoi18n.XdoI18nUtil.t("企业料号") + listN.getFacGNo() + xdoi18n.XdoI18nUtil.t("申报数量不能为[0]！|"));
                    }});
                }
            }
        }
    }

    /**
     * 功能描述：校验原产国与物料中心产销国是否相同
     *
     * @param checkResult
     * @param headId
     * @param status
     * @param userInfo
     * @return
     */
    private List<DecErpIListN> checkList(List<DecErpIListN> checkResult, String headId, String status, UserInfoToken userInfo) {
        DecErpIListN decErpIListN = new DecErpIListN();
        decErpIListN.setHeadId(headId);
        decErpIListN.setTradeCode(userInfo.getCompany());
        List<DecErpIListN> decErpIListNS = decErpIListNMapper.getList(decErpIListN);
        for (DecErpIListN listN : decErpIListNS) {
            if ("1".equals(listN.getBondMark())) {
                if (!Strings.isNullOrEmpty(listN.getOriginCountry())) {
                    MatNonBonded matNonBonded = matNonBondedMapper.getNonCompany(listN.getFacGNo(), listN.getGMark(), userInfo.getCompany());
                    if (matNonBonded != null) {
                        if (StringUtils.isBlank(matNonBonded.getCountry())) {
                            checkResult.add(new DecErpIListN() {{
                                setDataMark("list");
                                setWarningMark(status);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("企业料号") + listN.getFacGNo() + xdoi18n.XdoI18nUtil.t(":产销国为空"));
                            }});
                        } else if (!listN.getOriginCountry().equals(matNonBonded.getCountry())) {
                            checkResult.add(new DecErpIListN() {{
                                setDataMark("list");
                                setWarningMark(status);
                                setWarningMsg(listN.getFacGNo() + xdoi18n.XdoI18nUtil.t("原产国与物料中心产销国不一样"));
                            }});
                        }
                    }
                }
            } else {
                if (!Strings.isNullOrEmpty(listN.getOriginCountry())) {
                    MatImgexgOrg matImgexgOrg = matImgexgOrgMapper.getOriginCountry(listN.getCopGNo(), listN.getGMark(), listN.getEmsNo(), userInfo.getCompany());
                    if (matImgexgOrg != null) {
                        if (StringUtils.isBlank(matImgexgOrg.getCountry())) {
                            checkResult.add(new DecErpIListN() {{
                                setDataMark("list");
                                setWarningMark(status);
                                setWarningMsg(xdoi18n.XdoI18nUtil.t("企业料号") + listN.getFacGNo() + xdoi18n.XdoI18nUtil.t(":产销国为空"));
                            }});
                        } else if (!listN.getOriginCountry().equals(matImgexgOrg.getCountry())) {
                            checkResult.add(new DecErpIListN() {{
                                setDataMark("list");
                                setWarningMark(status);
                                setWarningMsg(listN.getFacGNo() + xdoi18n.XdoI18nUtil.t("原产国与物料中心产销国不一样"));
                            }});
                        }
                    }
                }
            }
        }
        return checkResult;
    }

    public ResultObject createBillNew(DecErpIHeadN decErpIHeadN, UserInfoToken userInfo, String type) throws Exception {
        ResultObject result = ResultObject.createInstance();

        boolean isNewBill = false;
        boolean ischeckContainer = false;
        boolean isCheckQty = false;
        String checkEmlQty = null;
        boolean entryDocumentsAttached = false;
        String productActivation = ConstantsStatus.STATUS_0;
        List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(userInfo);
        if (warringPassConfigDtos != null) {
            if (warringPassConfigDtos.size() > 0) {
                WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
                if (StringUtils.isNotBlank(warringPassConfigDto.getBillType()) && CommonVariable.CONFIG_1.equals(warringPassConfigDto.getBillType())) {
                    isNewBill = true;
                }
                if (StringUtils.isNotBlank(warringPassConfigDto.getCheckContainer()) && CommonVariable.CONFIG_1.equals(warringPassConfigDto.getCheckContainer())) {
                    ischeckContainer = true;
                }
                if (StringUtils.isEmpty(warringPassConfigDto.getCheckEmlQty())) {
                    //校验手册余量
                    isCheckQty = true;
                    checkEmlQty = CommonVariable.CONFIG_2;
                } else if (StringUtils.isNotBlank(warringPassConfigDto.getCheckEmlQty()) && CommonVariable.CONFIG_12.contains(warringPassConfigDto.getCheckEmlQty())) {
                    //校验手册余量
                    isCheckQty = true;
                    checkEmlQty = warringPassConfigDto.getCheckEmlQty();
                }
                if (StringUtils.isNotBlank(warringPassConfigDto.getEntryDocumentsAttached())&& StringUtils.equals(warringPassConfigDto.getEntryDocumentsAttached(),ConstantsStatus.STATUS_1)){
                    entryDocumentsAttached = true;
                }
                if (StringUtils.equals(warringPassConfigDto.getProductActivation(),CommonVariable.CONFIG_1)){
                    productActivation = warringPassConfigDto.getProductActivation();
                }

            }
        }
        //获取提单中存在的手账册号
        List<String> listEmsNos = decIBillListService.getEmsNoList(decErpIHeadN.getSid()).parallelStream().filter(x -> !StringHelper.isNullOrEmptyString(x)).sorted().collect(Collectors.toList());
        //单据标记 00: 一般贸易 11：金二手册 12：H200手册 21：金二账册 22：H2000账册
        String billFlag = "00";
        if (CollectionUtils.isNotEmpty(listEmsNos)) {
            //获取第一个手账册号的单据类型
            List<ZtythEmsHeadDto> ztythEmsHeadDto = ztythEmsImgExgService.getEmsNoListSelf(userInfo.getCompany(), userInfo).parallelStream()
                    .filter(x -> x.getEmsNo().equals(listEmsNos.get(0))).collect(Collectors.toList());
            if (ztythEmsHeadDto.size() > 0) {
                billFlag = ztythEmsHeadDto.get(0).getBillFlag();
            } else {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未找到备案号发送类型，请联系管理员配置"));
            }
        }
        //调用存储过程参数
        Map<String, Object> param = new HashMap(2);
        param.put("erpHeadId", decErpIHeadN.getSid());
        param.put("type", type);
        Map<String, String> error = new HashMap(1);
        boolean blnIsSucc = true;
        String strErrMsg;

        if (isCheckQty) {
            error = decIBillHeadMapper.checkMatQty(param);
            /*
            PG数据库调用
             */
            if (error != null) {
                blnIsSucc = DecVariable.STATUS_1.equals(String.valueOf(error.get("p_is_succ")));
                strErrMsg = error.get("p_err_msg");
            }
            /*
            Oracle数据库调用
             */
            else {
                blnIsSucc = DecVariable.STATUS_1.equals(String.valueOf(param.get("isSucc")));
                strErrMsg = (String) param.get("errMsg");
            }
            if (!blnIsSucc) {
                result.setSuccess(false);
                result.setMessage(strErrMsg);
                result.setData(checkEmlQty);
                return result;
            }
        }
        //H2000手、账册
        if (CommonEnum.EMS_TYPE_ENUM.EMS_TYPE_12.getCode().equals(billFlag) || CommonEnum.EMS_TYPE_ENUM.EMS_TYPE_22.getCode().equals(billFlag)) {
            logger.info(String.format("生成H2000清单：内部编号（%s）", decErpIHeadN.getEmsListNo()));
            error = decIBillHeadMapper.createBills(param);
        } else {
            if (isNewBill) {
                param.put("productActivation",productActivation);
                logger.info(String.format("生成金二清单(合并New)：内部编号（%s）", decErpIHeadN.getEmsListNo()));
                error = decIBillHeadMapper.createBillsNew(param);
            } else {
                logger.info(String.format("生成金二清单(合并)：内部编号（%s）", decErpIHeadN.getEmsListNo()));
                error = decIBillHeadMapper.createBillsJgMerge(param);
            }
        }
        logger.info(String.format("生成金二清单(合并)：返回错误信息（%s）", error));
            /*
            PG数据库调用
             */
        if (error != null) {
            blnIsSucc = DecVariable.STATUS_1.equals(String.valueOf(error.get("p_is_succ")));
            strErrMsg = error.get("p_err_msg");
        }
            /*
            Oracle数据库调用
             */
        else {
            blnIsSucc = DecVariable.STATUS_1.equals(String.valueOf(param.get("isSucc")));
            strErrMsg = (String) param.get("errMsg");
        }
        if (!blnIsSucc) {
            throw new ErrorException(400, commonService.sqlErrorMsg(strErrMsg));
        }

        logger.info(String.format("生成金二清单(合并)：开始获取集装箱信息"));
            /*
            zzd 2020-05-11 改成配置项进行管理 如果生成清单和报关单成功 则获取集装箱信息 根据企业十位代码 过滤  JJL 2020-04-03 ADD
             */
        if (blnIsSucc) {
            if (ischeckContainer) {
                logger.info(String.format("生成金二清单(合并)：开始校验集装箱信息"));
                decIEntryContainerService.CheckInsertContainer(decErpIHeadN.getSid(), decErpIHeadN.getEmsListNo(), CommonVariable.IE_MARK_I);
            }
        }

        //插入附件
        if (entryDocumentsAttached) {
            insertAttached(decErpIHeadN, userInfo);
        }
        //根据表头sid复制随附单证
        decIBillHeadMapper.copy(decErpIHeadN.getSid());

        return result;
    }


    public void insertAttached(DecErpIHeadN decErpIHeadN, UserInfoToken userInfo) throws Exception {
//        Example example = new Example(Attached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("businessSid", decErpIHeadN.getSid());
//        criteria.andEqualTo("businessType", "IM");
//        List<Attached> attacheds = attachedMapper.selectByExample(example);
        List<Attached> attacheds = attachedMapper.getCheckAll(decErpIHeadN.getSid(),"IM");
        List<Attached> attachedsNew = attacheds.stream().filter(x -> !"other".equals(x.getAcmpType())).collect(Collectors.toList());
        DecIEntryHead decIEntryHead = new DecIEntryHead();
        decIEntryHead.setTradeCode(decErpIHeadN.getTradeCode());
        decIEntryHead.setErpHeadId(decErpIHeadN.getSid());
        decIEntryHead.setIEMark("I");
        List<DecIEntryHead> boschEntryHeadList = decIEntryHeadMapper.getList(decIEntryHead);
        for (DecIEntryHead item : boschEntryHeadList) {
            dcEntryEdocrealationService.copyAttachedToEdoc(attachedsNew, item.getSid(), decIEntryHead.getIEMark(), null, userInfo);
        }
    }

    /**
     * 功能描述
     *
     * @param warringPassConfigDto 1
     * @param type                 2
     * @param value                3
     * @return com.dcjet.cs.erp.model.DecErpEListN
     * <AUTHOR>
     * @date 2020/3/5
     * @version 1.0
     */
    private List<DecErpIListN> check(List<DecErpIListN> checkList, WarringPassConfigDto warringPassConfigDto, String dataMark, String type, Object value, String errMessage, String transMode, String IEType) {
        boolean isPass = false;
        if ("iBond".equals(IEType)) {
            if (warringPassConfigDto.getRequiredField() != null
                    && Arrays.asList(warringPassConfigDto.getRequiredField().split(",")).contains(type)
                    && value == null) {
                isPass = true;
            }
        }
        if ("iNoBond".equals(IEType)) {
            if (warringPassConfigDto.getReFieldINoBond() != null
                    && Arrays.asList(warringPassConfigDto.getReFieldINoBond().split(",")).contains(type)
                    && value == null) {
                isPass = true;
            }
        }
        if (isPass) {
            if ("feeMark".equals(type)) {
                if (DecVariable.TRANS_MODE_3.equals(transMode) || DecVariable.TRANS_MODE_4.equals(transMode) || DecVariable.TRANS_MODE_7.equals(transMode)) {
                    checkList.add(new DecErpIListN() {
                        {
                            setDataMark(dataMark);
                            setWarningMark(DecVariable.STATUS_2);
                            setWarningMsg(errMessage);
                        }
                    });
                }
            } else if ("insurMark".equals(type)) {
                if (DecVariable.TRANS_MODE_2.equals(transMode) || DecVariable.TRANS_MODE_3.equals(transMode) || DecVariable.TRANS_MODE_7.equals(transMode)) {
                    checkList.add(new DecErpIListN() {
                        {
                            setDataMark(dataMark);
                            setWarningMark(DecVariable.STATUS_2);
                            setWarningMsg(errMessage);
                        }
                    });
                }
            } else {
                checkList.add(new DecErpIListN() {
                    {
                        setDataMark(dataMark);
                        setWarningMark(DecVariable.STATUS_2);
                        setWarningMsg(errMessage);
                    }
                });
            }
        }
        return checkList;
    }

    private void checkDefaultTrue(List<DecErpIListN> checkList, WarringPassConfigDto warringPassConfigDto, String dataMark, String type, Object value, String errMessage, String transMode, String IEType) {
        boolean isPass = false;
        if ("iBond".equals(IEType)) {
            if (value == null) {
                isPass = true;
            }
        }
        if ("iNoBond".equals(IEType)) {
            if (warringPassConfigDto.getReFieldINoBond() != null && warringPassConfigDto.getReFieldINoBond().contains(type) && value == null) {
                isPass = true;
            }
        }
        if (isPass) {
            checkList.add(new DecErpIListN() {
                {
                    setDataMark(dataMark);
                    setWarningMark(DecVariable.STATUS_2);
                    setWarningMsg(errMessage);
                }
            });
        }
    }

    /**
     * @param headId
     * @param netWtAllowErrVal
     * @param checkNetWtTotal
     * @return
     */
    public List<DecErpIListN> checkNetWtOfHead(List<DecErpIListN> checkList, String headId, BigDecimal netWtAllowErrVal, String checkNetWtTotal) {
        DecErpIHeadN headModel = decErpIHeadNMapper.selectByPrimaryKey(headId);
        if (headModel == null) {
            return null;
        }
        Map<String, BigDecimal> mapResult = decErpIListNMapper.getSumInfo(headId);
        if (mapResult != null) {
            BigDecimal sumNetWt = BigDecimal.ZERO;
            if (mapResult.containsKey("SUMNETWT") && mapResult.get("SUMNETWT") != null) {
                sumNetWt = mapResult.get("SUMNETWT");
            }
            if (headModel.getNetWt() == null) {
                headModel.setNetWt(BigDecimal.ZERO);
            }
            BigDecimal errVal = headModel.getNetWt().subtract(sumNetWt).abs().setScale(5, BigDecimal.ROUND_HALF_UP);//.divide(sumNetWt, 5, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue();
            if (errVal.compareTo(netWtAllowErrVal) > 0) {
                checkList.add(new DecErpIListN() {
                    {
                        setDataMark("head");
                        setWarningMark(checkNetWtTotal);
                        setWarningMsg(xdoi18n.XdoI18nUtil.t("表头总净重-表体总净重超过预设值") + xdoi18n.XdoI18nUtil.t(",误差") + errVal);
                    }
                });
            }

        }
        return checkList;
    }

    /**
     * @param headId
     * @param grossWtLimit
     * @param checkGrossWt
     * @return
     */
    public List<DecErpIListN> checkGrossWtOfHead(List<DecErpIListN> checkList, String headId, BigDecimal grossWtLimit, String checkGrossWt) {
        DecErpIHeadN headModel = decErpIHeadNMapper.selectByPrimaryKey(headId);
        if (headModel == null) {
            return null;
        }
        Map<String, BigDecimal> mapResult = decErpIListNMapper.getSumInfo(headId);
        if (mapResult != null) {
            BigDecimal sumNetWt = BigDecimal.ZERO;
            if (mapResult.containsKey("SUMGROSSWT") && mapResult.get("SUMGROSSWT") != null) {
                sumNetWt = mapResult.get("SUMGROSSWT");
            }
            if (headModel.getGrossWt() == null) {
                headModel.setGrossWt(BigDecimal.ZERO);
            }
            BigDecimal errVal = headModel.getGrossWt().subtract(sumNetWt).abs().setScale(5, BigDecimal.ROUND_HALF_UP);//.divide(sumNetWt, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue();
            if (errVal.compareTo(grossWtLimit) > 0) {
                checkList.add(new DecErpIListN() {
                    {
                        setDataMark("head");
                        setWarningMark(checkGrossWt);
                        setWarningMsg(xdoi18n.XdoI18nUtil.t("表头总毛重-表体总毛重超过预设值") + xdoi18n.XdoI18nUtil.t(",误差") + errVal);
                    }
                });
            }
        }
        return checkList;
    }

    /**
     * 功能描述 批量打印清单
     *
     * @param sids      1
     * @param printType 2
     * @param tname     3
     * @param userInfo  4
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2021-06-08
     * @version 1.0
     */
    public Map<String, String> exportForIBillList(List<String> sids, String printType, String tname, UserInfoToken userInfo) throws Exception {
        List<String> files = new ArrayList<>();
        Map<String, String> fileNameMap = new HashMap<>();
        for (String sid : sids) {
            String fileName = UUID.randomUUID().toString() + ".pdf";

            DecIBillHeadParam decIBillHeadParam = new DecIBillHeadParam();
            decIBillHeadParam.setSid(sid);
            List<DecIBillHeadDto> decIBillHeadDtos = convertForPrint(selectForPrint(decIBillHeadParam, userInfo), userInfo);

            DecIBillListParam decIBillListParam = new DecIBillListParam();
            decIBillListParam.setHeadId(sid);
            List<DecIBillListDto> decIBillListDtos = convertForListPrint(decIBillListService.selectAll(decIBillListParam, userInfo));

            String exportFileName = "";
            if (decIBillHeadDtos.size() > 0) {
                exportFileName = exportService.exportForBillPage(decIBillHeadDtos, decIBillListDtos, fileName, tname);
                files.add(exportFileName);
                fileNameMap.put(exportFileName, xdoi18n.XdoI18nUtil.t("清单草单") + decIBillHeadDtos.get(0).getEmsListNo());
            }
        }

        Map<String, String> mapFile = new HashMap<>(2);
        String returnFile = "";
        String returnFileName = "";
        if ("0".equals(printType)) {
            if (files.size() > 1) {
                returnFile = exportService.combinPdf(files, java.util.UUID.randomUUID().toString() + ".pdf");
                returnFileName = xdoi18n.XdoI18nUtil.t("清单草单打印.pdf");
            } else if (files.size() == 1) {
                returnFile = files.get(0);
                returnFileName = fileNameMap.get(files.get(0));
            } else {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未找到待打印的清单草单"));
            }
        } else {
            returnFile = exportService.toZip(files, fileNameMap);
            returnFileName = xdoi18n.XdoI18nUtil.t("清单草单打印.zip");
        }
        mapFile.put("returnFile", returnFile);
        mapFile.put("returnFileName", returnFileName);
        return mapFile;
    }

    /**
     * 清单表头海关参数转换
     *
     * @param list
     * @return
     * <AUTHOR>
     * @date 2019-03-13
     */
    public List<DecIBillHeadDto> convertForPrint(List<DecIBillHeadDto> list, UserInfoToken userInfo) {
        Date now = new Date();
        String billFlag = null;
        for (DecIBillHeadDto item : list) {
            if (StringUtils.isNotBlank(item.getGMark())) {
                item.setGMark(CommonEnum.GMarkEnum.getValue(item.getGMark()));
            }
            item.setBillListType(CommonEnum.billListType.getValue(item.getBillListType()));
            if (StringUtils.isNotBlank(item.getDclcusMark())) {
                item.setDclcusMark(CommonEnum.DCLCUS_MARK.getValue(item.getDclcusMark()));
            }
            if (StringUtils.isNotBlank(item.getDclcusType())) {
                item.setDclcusType(CommonEnum.DCLCUS_TYPE.getValue(item.getDclcusType()));
            }
            if (StringUtils.isNotBlank(item.getEntryType())) {
                item.setEntryType(item.getEntryType() + " " + CommonEnum.ENTRY_TYPE.getValue(item.getEntryType()));
            }
            if (StringUtils.isNotBlank(item.getEmsNo())) {
                item.setCompetentCustoms(commonService.convertPCode(item.getEmsNo().substring(1, 5), PCodeType.CUSTOMS_REL));
            }
            item.setListIochkptStatus("");//暂时为空
            item.setRotateType("");
            item.setPrintDate(df.format(now));
            item.setInputDateStr(item.getInputDate() == null ? "" : df.format(item.getInputDate()));
            item.setDeclareDateStr(item.getDeclareDate() == null ? "" : df.format(item.getDeclareDate()));
            item.setTradeMode(commonService.convertPCode(item.getTradeMode(), PCodeType.TRADE));
            item.setTrafMode(commonService.convertPCode(item.getTrafMode(), PCodeType.TRANSF));
            item.setTransMode(commonService.convertPCode(item.getTransMode(), PCodeType.TRANSAC));
            item.setMasterCustoms(commonService.convertPCode(item.getMasterCustoms(), PCodeType.CUSTOMS_REL));
            item.setTradeCountry(commonService.convertPCode(item.getTradeCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setTradeNation(commonService.convertPCode(item.getTradeNation(), PCodeType.COUNTRY_OUTDATED));
            item.setIEPort(commonService.convertPCode(item.getIEPort(), PCodeType.CUSTOMS_REL));
            if (StringUtils.isNotBlank(item.getWrapType2())) {
                item.setWrapType((StringUtils.isBlank(item.getWrapType()) ? "" : item.getWrapType() + ',') + item.getWrapType2());
            }
            if (StringUtils.isNotBlank(item.getWrapType())) {
                String[] wrapList = item.getWrapType().split(",");
                String newWrap = "";
                for (String wrap : wrapList) {
                    newWrap = newWrap + pCodeHolder.getValue(PCodeType.WRAP, wrap) + "/";
                }
                item.setWrapType(StringUtils.isBlank(newWrap) ? "" : newWrap.substring(0, newWrap.length() - 1));
            }
            if (item.getFeeRate() != null) {
                item.setFeeAll((item.getFeeCurr() == null ? CommonVariable.DEFAULT_STR000 : item.getFeeCurr()) + "/" + (item.getFeeRate() == null ? "" : item.getFeeRate() + "/") + (item.getFeeMark() == null ? "" : item.getFeeMark()));
            }
            if (item.getInsurRate() != null) {
                item.setInsurAll((item.getInsurCurr() == null ? CommonVariable.DEFAULT_STR000 : item.getInsurCurr()) + "/" + (item.getInsurRate() == null ? "" : item.getInsurRate() + "/") + (item.getInsurMark() == null ? "" : item.getInsurMark()));
            }
            if (item.getOtherRate() != null) {
                item.setOtherAll((item.getOtherCurr() == null ? CommonVariable.DEFAULT_STR000 : item.getOtherCurr()) + "/" + (item.getOtherRate() == null ? "" : item.getOtherRate() + "/") + (item.getOtherMark() == null ? "" : item.getOtherMark()));
            }
            //-1 未发送 0 发送失败  1发送成功 2发送中 J1 通过 J3 退单 JDL 删单
            if (item.getSendApiStatus() != null) {
                item.setSendApiStatus(item.getSendApiStatus() + " " + CommonEnum.BillEnum.getValue(item.getSendApiStatus()));
            }
            item.setInsurCurr(commonService.convertPCode(item.getInsurCurr(), PCodeType.CURR_OUTDATED));
            item.setOtherCurr(commonService.convertPCode(item.getOtherCurr(), PCodeType.CURR_OUTDATED));
            item.setFeeCurr(commonService.convertPCode(item.getFeeCurr(), PCodeType.CURR_OUTDATED));
            item.setCurrAll(commonService.convertPCode(item.getCurrAll(), PCodeType.CURR_OUTDATED));
            if (StringUtils.isNotBlank(item.getFeeMark())) {
                item.setFeeMark(CommonEnum.FeeMarkEnum.getValue(item.getFeeMark()));
            }
            if (StringUtils.isNotBlank(item.getOtherMark())) {
                item.setOtherMark(CommonEnum.FeeMarkEnum.getValue(item.getOtherMark()));
            }
            if (StringUtils.isNotBlank(item.getInsurMark())) {
                item.setInsurMark(CommonEnum.FeeMarkEnum.getValue(item.getInsurMark()));
            }
            //获取第一个手账册号的单据类型
            List<ZtythEmsHeadDto> ztythEmsHeadDto = ztythEmsImgExgService.getEmsNoListSelf(userInfo.getCompany(), userInfo).parallelStream()
                    .filter(x -> x.getEmsNo().equals(item.getEmsNo())).collect(Collectors.toList());
            if (ztythEmsHeadDto.size() > 0) {
                // 11：金二手册 12：H200手册 21：金二账册 22：H2000账册
                billFlag = ztythEmsHeadDto.get(0).getBillFlag();
            }
            item.setReceiveCode(item.getTradeCode());
            item.setReceiveCreditCode(item.getTradeCreditCode());
            item.setReceiveName(item.getTradeName());
            if ("0".equals(item.getBondMark())) {
                item.setTradeCode(null);
                item.setTradeCreditCode(null);
                item.setTradeName(null);
                item.setReceiveCode(null);
                item.setReceiveCreditCode(null);
                item.setReceiveName(null);

                if ("11".equals(billFlag) && StringUtils.isNotEmpty(item.getEmsNo())) {
                    ZtythEmsHeadNewListDto headResult = ztythEmsImgExgService.getEmlNewData(userInfo, item.getEmsNo());
                    if (headResult != null) {
                    /*
                    经营单位信息：tradeCode、tradeCreditCode、tradeName
                    加工单位信息：receiveCode、receiveCreditCode、receiveName
                     */
                        item.setTradeCode(headResult.getTradeCode());
                        item.setTradeCreditCode(headResult.getTradeCreditCode());
                        item.setTradeName(headResult.getTradeName());
                        item.setReceiveCode(headResult.getReceiveCode());
                        item.setReceiveCreditCode(headResult.getReceiveCreditCode());
                        item.setReceiveName(headResult.getReceiveName());
                    }
                }
                if ("21".equals(billFlag) && StringUtils.isNotEmpty(item.getEmsNo())) {
                    //获取金二账册
                    List<ZtythEmsHeadNewListDto> listHeadsDto = ztythEmsImgExgService.getEmsNewData(userInfo);
                    if (listHeadsDto != null) {
                        ZtythEmsHeadNewListDto ztythEmsHeadNewListDto = listHeadsDto.parallelStream().filter(x -> StringUtils.isNotEmpty(x.getEmsNo())).filter(x -> x.getEmsNo().equals(item.getEmsNo())).findFirst().orElse(null);
                        if (ztythEmsHeadNewListDto != null) {
                            item.setTradeCode(ztythEmsHeadNewListDto.getTradeCode());
                            item.setTradeCreditCode(ztythEmsHeadNewListDto.getTradeCreditCode());
                            item.setTradeName(ztythEmsHeadNewListDto.getTradeName());
                            item.setReceiveCode(ztythEmsHeadNewListDto.getReceiveCode());
                            item.setReceiveCreditCode(ztythEmsHeadNewListDto.getReceiveCreditCode());
                            item.setReceiveName(ztythEmsHeadNewListDto.getReceiveName());
                        }
                    }
                }
            }
        }
        return list;
    }

    /**
     * 清单表体海关参数转换
     *
     * @param list
     * @return
     * <AUTHOR>
     * @date 2019-03-13
     */
    public List<DecIBillListDto> convertForListPrint(List<DecIBillListDto> list) {
        for (DecIBillListDto item : list) {
            item.setUnit(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit()));
            item.setUnit1(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit1()));
            if (item.getUnit2() != null) {
                item.setUnit2(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit2()));
            }
            if (StringUtils.isNotEmpty(item.getCiqNo())) {
                item.setCodeTS(item.getCodeTS() + "\r\n" + item.getCiqNo());
            }
            item.setNote1((item.getQty1() == null ? "" : item.getQty1()) + " " + (item.getUnit1() == null ? "" : item.getUnit1()) + "\r\n" + (item.getQty2() == null ? "" : item.getQty2()) + " " + (item.getUnit2() == null ? "" : item.getUnit2()) + "\r\n" + item.getQty() + " " + item.getUnit());
            item.setNote2(item.getDecPrice() + "\r\n" + item.getDecTotal() + "\r\n" + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurr()));

            item.setDutyMode(commonService.convertPCode(item.getDutyMode(), PCodeType.LEVYMODE));
            item.setOriginCountry(commonService.convertPCode(item.getOriginCountry(), PCodeType.COUNTRY_OUTDATED));
            item.setDestinationCountry(commonService.convertPCode(item.getDestinationCountry(), PCodeType.COUNTRY_OUTDATED));
        }
        return list;
    }

    /**
     * 功能描述 批量打印报关单
     *
     * @param sids      1
     * @param printType 2
     * @param tname     3
     * @param userInfo  4
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2021-06-08
     * @version 1.0
     */
    public Map<String, String> exportForIEntryList(List<String> sids, String printType, String tname, UserInfoToken userInfo) throws Exception {
        List<String> files = new ArrayList<>();
        Map<String, String> fileNameMap = new HashMap<>();
        StringBuilder entryTypeString = new StringBuilder();
        for (String sid : sids) {
            String fileName = UUID.randomUUID().toString() + ".pdf";
            List<DecIEntryHead> decIEntryHeads = convertEntryForPrint(decIEntryHeadService.selectPrintHead(null, sid, userInfo), userInfo);
            List<DecIEntryList> decIEntryLists = convertEntryForListPrint(decIEntryHeadService.selectListAll(sid, userInfo));
            DecIEntryHead decIEntryHead = decIEntryHeads.get(0);
            DecErpIHeadN decErpIHeadN = decErpIHeadNMapper.selectByPrimaryKey(decIEntryHead.getErpHeadId());

            if (StringUtils.isNotBlank(decIEntryHead.getBondMark()) && "1".equals(decIEntryHead.getBondMark()) && StringUtils.isNotBlank(decIEntryHead.getEntryType()) && "X".equals(decIEntryHead.getEntryType())) {
                entryTypeString.append("两步申报模式：");
                //是否涉证 0-否，1-是
                if (StringUtils.isNotBlank(decIEntryHead.getCardMark()) && "1".equals(decIEntryHead.getCardMark())) {
                    entryTypeString.append("涉证、");
                } else {
                    entryTypeString.append("非证、");
                }
                //是否涉检 0-否，1-是
                if (StringUtils.isNotBlank(decIEntryHead.getCheckMark()) && "1".equals(decIEntryHead.getCheckMark())) {
                    entryTypeString.append("涉检、");
                } else {
                    entryTypeString.append("非检、");
                }
                //是否涉税 0-否，1-是
                if (StringUtils.isNotBlank(decIEntryHead.getTaxMark()) && "1".equals(decIEntryHead.getTaxMark())) {
                    entryTypeString.append("涉税");
                } else {
                    entryTypeString.append("非税");
                }
            }

            String exportFileName = "";
            if (decIEntryHeads.size() > 0) {
                exportFileName = exportService.exportForEntryPage(decIEntryHeads, decIEntryLists, fileName, tname, entryTypeString.toString());
                files.add(exportFileName);
                fileNameMap.put(exportFileName, xdoi18n.XdoI18nUtil.t("报关单草单") + decIEntryHead.getEmsListNo());
            }
        }

        Map<String, String> mapFile = new HashMap<>(2);
        String returnFile = "";
        String returnFileName = "";
        if ("0".equals(printType)) {
            if (files.size() > 1) {
                returnFile = exportService.combinPdf(files, java.util.UUID.randomUUID().toString() + ".pdf");
                returnFileName = xdoi18n.XdoI18nUtil.t("报关单草单打印")+".pdf";
            } else if (files.size() == 1) {
                returnFile = files.get(0);
                returnFileName = fileNameMap.get(files.get(0));
            } else {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未找到待打印的报关单草单"));
            }
        } else {
            returnFile = exportService.toZip(files, fileNameMap);
            returnFileName = xdoi18n.XdoI18nUtil.t("报关单草单打印")+".pdf";
        }
        mapFile.put("returnFile", returnFile);
        mapFile.put("returnFileName", returnFileName);
        return mapFile;
    }

    /**
     * 清单表头海关参数转换
     *
     * @param list
     * @return
     * <AUTHOR>
     * @date 2019-03-13
     */
    public List<DecIEntryHead> convertEntryForPrint(List<DecIEntryHead> list, UserInfoToken userInfoToken) {
        for (DecIEntryHead item : list) {

            item.setTradeModeName(pCodeHolder.getValue(PCodeType.TRADE, item.getTradeMode()));
            item.setTrafModeName(pCodeHolder.getValue(PCodeType.TRANSF, item.getTrafMode()));
            item.setTransModeName(pCodeHolder.getValue(PCodeType.TRANSAC, item.getTransMode()));
            item.setTradeCountryName(pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getTradeCountry()));
            item.setTradeCountry(convert(item.getTradeCountry(),"1"));
            item.setTradeNationName(pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getTradeNation()));
            item.setTradeNation(convert(item.getTradeNation(),"1"));
            item.setIEPortName(pCodeHolder.getValue(PCodeType.CUSTOMS_REL, item.getIEPort()));
            item.setDestPortName(pCodeHolder.getValue(PCodeType.PORT_LIN, item.getDestPort()));
            item.setDespPortName(pCodeHolder.getValue(PCodeType.PORT_LIN, item.getDespPort()));
            item.setEntryPortName(pCodeHolder.getValue(PCodeType.CIQ_ENTY_PORT, item.getEntryPort()));
            item.setCutModeName(pCodeHolder.getValue(PCodeType.LEVYTYPE, item.getCutMode()));
            if (StringUtils.isNotEmpty(item.getTrafName()) && StringUtils.isNotEmpty(item.getVoyageNo())) {
                item.setTrafName(item.getTrafName() + "/" + item.getVoyageNo());
            } else if (StringUtils.isEmpty(item.getTrafName()) && StringUtils.isNotEmpty(item.getVoyageNo())) {
                item.setTrafName(item.getVoyageNo());
            }
            if (StringUtils.isNotBlank(item.getWrapType()) && StringUtils.isNotBlank(item.getWrapType2())) {
                String[] wrapList = item.getWrapType2().split(",");
                String newWrap = "";
                for (String wrap : wrapList) {
                    newWrap = newWrap + pCodeHolder.getValue(PCodeType.WRAP, wrap) + "/";
                }
                item.setWrapTypeName(pCodeHolder.getValue(PCodeType.WRAP, item.getWrapType()) + "/" + (StringUtils.isBlank(newWrap) ? "" : newWrap.substring(0, newWrap.length() - 1)));
                item.setWrapType("(" + item.getWrapType() + "/" + item.getWrapType2().replace(",", "/") + ")");
            } else if (StringUtils.isBlank(item.getWrapType2())) {
                item.setWrapTypeName(pCodeHolder.getValue(PCodeType.WRAP, item.getWrapType()));
                item.setWrapType(item.getWrapType() == null ? "" : "(" + item.getWrapType() + ")");
            } else if (StringUtils.isBlank(item.getWrapType())) {
                String[] wrapList = item.getWrapType2().split(",");
                String newWrap = "";
                for (String wrap : wrapList) {
                    newWrap = newWrap + pCodeHolder.getValue(PCodeType.WRAP, wrap) + "/";
                }
                item.setWrapTypeName(StringUtils.isBlank(newWrap) ? "" : newWrap.substring(0, newWrap.length() - 1));
                item.setWrapType(item.getWrapType2() == null ? "" : "(" + item.getWrapType2().replace(",", "/") + ")");
            }

            item.setIEPort(item.getIEPort() == null ? "" : "(" + item.getIEPort() + ")");
            item.setTrafMode(item.getTrafMode() == null ? "" : "(" + item.getTrafMode() + ")");
            item.setTradeMode(item.getTradeMode() == null ? "" : "(" + item.getTradeMode() + ")");
            item.setCutMode(item.getCutMode() == null ? "" : "(" + item.getCutMode() + ")");
            item.setDespPort(item.getDespPort() == null ? "" : "(" + item.getDespPort() + ")");
            item.setDestPort(item.getDestPort() == null ? "" : "(" + item.getDestPort() + ")");
            item.setTradeNation(item.getTradeNation() == null ? "" : "(" + item.getTradeNation() + ")");
            item.setTradeCountry(item.getTradeCountry() == null ? "" : "(" + item.getTradeCountry() + ")");
            item.setEntryPort(item.getEntryPort() == null ? "" : "(" + item.getEntryPort() + ")");
            item.setTransMode(item.getTransMode() == null ? "" : "(" + item.getTransMode() + ")");
            item.setTotalAll(item.getTotalAll() + item.getNetWtAll());
            item.setTradeCode("(" + item.getTradeCode() + ")(" + pCodeHolder.getCompany("COMPANY", "CREDIT_CODE", item.getTradeCode()) + ")");
            item.setDeclareCode("(" + item.getDeclareCreditCode() + ")");
            item.setOwnerCode((item.getOwnerCode() == null ? "" : "(" + item.getOwnerCode() + ")") + "(" + item.getOwnerCreditCode() + ")");
            if (item.getFeeRate() != null) {
                item.setFeeAll((item.getFeeCurr() == null ? CommonVariable.DEFAULT_STR000 : convert(item.getFeeCurr(),"0")) + "/" + (item.getFeeRate() == null ? "" : item.getFeeRate() + "/") + (item.getFeeMark() == null ? "" : item.getFeeMark()));
            }
            if (item.getInsurRate() != null) {
                item.setInsurAll((item.getInsurCurr() == null ? CommonVariable.DEFAULT_STR000 : convert(item.getInsurCurr(),"0")) + "/" + (item.getInsurRate() == null ? "" : item.getInsurRate() + "/") + (item.getInsurMark() == null ? "" : item.getInsurMark()));
            }
            if (item.getOtherRate() != null) {
                item.setOtherAll((item.getOtherCurr() == null ? CommonVariable.DEFAULT_STR000 : convert(item.getOtherCurr(),"0")) + "/" + (item.getOtherRate() == null ? "" : item.getOtherRate() + "/") + (item.getOtherMark() == null ? "" : item.getOtherMark()));
            }
            if (item.getMasterCustoms() != null) {
                item.setMasterCustomsName(item.getMasterCustoms() + "(" + pCodeHolder.getValue(PCodeType.CUSTOMS_REL, item.getMasterCustoms()) + ")");
            }
            item.setDeclareName("(" + item.getDeclareCreditCode() + ")" + item.getDeclareName());
            if (StringUtils.isNotBlank(item.getPromiseItems())) {
                if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_1)) {
                    item.setItem1(CommonVariable.YES_CN);
                } else if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_9)) {
                    item.setItem1(CommonVariable.NO_CN);
                }
                if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_2)) {
                    item.setItem2(CommonVariable.YES_CN);
                } else if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_8)) {
                    item.setItem2(CommonVariable.NO_CN);
                }
                if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_3)) {
                    item.setItem3(CommonVariable.YES_CN);
                } else if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_7)) {
                    item.setItem3(CommonVariable.NO_CN);
                }
//                if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_4)) {
//                    item.setItem4(CommonVariable.YES_CN);
//                } else if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_6)) {
//                    item.setItem4(CommonVariable.NO_CN);
//                }
                if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_A)) {
                    item.setItem5(CommonVariable.YES_CN);
                } else if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_Z)) {
                    item.setItem5(CommonVariable.NO_CN);
                }
                if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_B)) {
                    item.setItem6(CommonVariable.YES_CN);
                } else if (item.getPromiseItems().contains(DecVariable.PROMISE_ITEM_Y)) {
                    item.setItem6(CommonVariable.NO_CN);
                }
            }
        }
        return list;
    }

    /**
     * 币制转换格式
     * @param code
     * @return
     */
    public String convert(String code,String status) {

        String finalResult = null;
        Optional<List<Map<String, String>>> pcode =null;
        if (StringUtils.equals(status,ConstantsStatus.STATUS_0)) {
            pcode = pCodeHolder.getTypeList(PCodeType.CURR_ALL);
            List<Map<String, String>> currList = pcode.get();
            if (CollectionUtils.isNotEmpty(currList)) {
                for (Map<String, String> currMap : currList) {
                    if (StringUtils.equals(code, currMap.get("CODE_NUM"))) {
                        finalResult = currMap.get("CODE");
                    }
                }
            }
        }
        if (StringUtils.equals(status,ConstantsStatus.STATUS_1)){
            pcode = pCodeHolder.getTypeList(PCodeType.COUNTRY_ALL);
            List<Map<String, String>> countryList = pcode.get();
            if (CollectionUtils.isNotEmpty(countryList)) {
                for (Map<String, String> countryMap : countryList) {
                    if (StringUtils.equals(code, countryMap.get("CODE_NUM"))) {
                        finalResult = countryMap.get("CODE");
                    }
                }
            }
        }
        return finalResult;
    }

    /**
     * 清单表体海关参数转换
     *
     * @param list
     * @return
     * <AUTHOR>
     * @date 2019-03-13
     */
    public List<DecIEntryList> convertEntryForListPrint(List<DecIEntryList> list) {
        for (DecIEntryList item : list) {
            item.setUnit(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit()));
            item.setUnit1(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit1()));
            item.setUnit2(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit2()));
            if (StringUtils.isNotEmpty(item.getCiqCode())) {
                item.setCodeTS(item.getCodeTS() + "\r\n" + item.getCiqCode());
            }
//            item.setGNoAll(item.getSerialNo() + (item.getGNo() == null ? "" : "\r\n(" + item.getGNo() + ")"));
            item.setGNoAll(item.getSerialNo().toString());
            item.setGNameModel(item.getGName() + "\r\n" + item.getGModel());

            item.setQtyUnit((item.getQty1() == null ? "" : item.getQty1()) + " " + (item.getUnit1() == null ? "" : item.getUnit1()) + "\r\n" + (item.getQty2() == null ? "" : item.getQty2()) + " " + (item.getUnit2() == null ? "" : item.getUnit2()) + "\r\n" + item.getQty() + " " + item.getUnit());
            item.setDecAll(item.getDecPrice() + "\r\n" + item.getDecTotal() + "\r\n" + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurr()));
            if (StringUtils.isNotBlank(item.getDestinationCountry())) {
                item.setDestinationCountry(pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getDestinationCountry()) + "\r\n" + "(" + convert(item.getDestinationCountry(),"1") + ")");
            }
            item.setOriginCountry(pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getOriginCountry()) + "\r\n" + "(" + convert(item.getOriginCountry(),"1") + ")");
            if (StringUtils.isNotBlank(item.getDistrictCode()) && StringUtils.isNotBlank(item.getDistrictPostCode())) {
                item.setDistrictCode("(" + item.getDistrictCode() + "/" + item.getDistrictPostCode() + ")" + pCodeHolder.getValue(PCodeType.AREA, item.getDistrictCode()) + "/" + pCodeHolder.getValue(PCodeType.POST_AREA, item.getDistrictPostCode()));
            } else if (StringUtils.isNotBlank(item.getDistrictCode())) {
                item.setDistrictCode("(" + item.getDistrictCode() + ")" + pCodeHolder.getValue(PCodeType.AREA, item.getDistrictCode()));
            } else if (StringUtils.isNotBlank(item.getDistrictPostCode())) {
                item.setDistrictCode("(" + item.getDistrictPostCode() + ")" + pCodeHolder.getValue(PCodeType.POST_AREA, item.getDistrictPostCode()));
            }
            item.setDutyMode(pCodeHolder.getValue(PCodeType.LEVYMODE, item.getDutyMode()) + "\r\n" + "(" + item.getDutyMode() + ")");
        }
        return list;
    }

    public ResultObject billSync(String emsNo, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(Constants.BILL_SYNC);
        if (gwstdHttpConfig == null || StringUtils.isEmpty(gwstdHttpConfig.getBaseUrl()) || StringUtils.isEmpty(gwstdHttpConfig.getServiceUrl())) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("未配置进口清单同步请求接口，请联系管理员！"));
        }
        try {
            // 获取token
            //String strToken = commonService.getToken(userInfo.getCompany(), "**********获取的token为：");
            String strToken = userInfo.getAccessToken();
            JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
            String postUrl = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();
            String taskId = UUID.randomUUID().toString();
            logger.info("此次请求清单同步批次号:", taskId);
            String paramJson = "{\"emsNo\":\"" + emsNo + "\",\"taskID\":\"" + taskId + "\",\"emsListNo\":[],\"statuFilter\":[\"1\"]}";
            String resJson = RequestUtil.sendPost(postUrl, strToken, paramJson, "application/json", CommonVariable.UTF8);
            ResultObject<SendBillSyncResult> result =
                    jsonObjectMapper.fromJson(resJson, new TypeReference<ResultObject<SendBillSyncResult>>() {
                    });
            if (null != result && Constants.HTTP_UNAUTHORIZED == result.getCode()) {
                logger.info(String.format("**********Token已过期,错误返回：%s**********", resJson));
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("Token已过期"));
            }
            if (result.getCode() != 200) {
                logger.info(String.format("**********调用接口错误,错误返回：%s**********", result.getMessage()));
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("调用接口错误,错误返回") + result.getMessage());
            }
            if (result.getData().getErrorDataCount() != null && result.getData().getErrorDataCount() != 0) {
                logger.info(String.format("**********请求清单推送接口失败：错误数据%s**********", result.getData().getErrData()));
                throw new ErrorException(400, String.format(xdoi18n.XdoI18nUtil.t("**********请求清单推送接口失败：错误数据%s**********"), result.getData().getErrData()));
            }
        } catch (Exception e) {
            logger.error("发送进口清单同步请求失败：" + e.getMessage());
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("发送进口清单同步请求失败!"));
        }
        return resultObject;
    }


    public Map<String, String> exportForEEntry(List<String> sids, String printType, UserInfoToken userInfo, String type) throws Exception {
        List<String> files = new ArrayList<>();
        Map<String, String> fileNameMap = new HashMap<>();
        if (sids.size()>30){
            throw new Exception("勾选数据(最多选30行)！");
        }
        for (int i = 0; i < sids.size(); i++) {
            if (StringUtils.isBlank(sids.get(i))) {
                throw new Exception("报关单附件存在未同步数据，无法打印！");
            }
            int attachedType = decErpIHeadNMapper.selectAttachedTypeE(sids.get(i), userInfo.getCompany());
            if (attachedType == 0) {
                throw new Exception("报关单附件存在未同步数据，无法打印！");
            }
        }
        for (String sid : sids) {
            List<Attached> model = decErpIHeadNMapper.selectAttachedE(sid, userInfo.getCompany());
            for (int i = 0; i < model.size(); i++) {
                byte[] bytes = fastdFsService.getByteFromFastDFS(model.get(i).getFileName());
                String fileName = model.get(i).getSid() + ".pdf";
                String feil = templatePath + fileName;
                try {
                    Path path = Paths.get(feil);
                    Files.write(path, bytes);
                    String exportFileName = "";
                    exportFileName = feil;
                    files.add(exportFileName);
                    if (StringUtils.equals(ConstantsStatus.STATUS_0, printType)) {
                        fileNameMap.put(exportFileName, "报关单");
                    } else {
                        String name = model.get(i).getOriginFileName();
                        String target = ".";
                        int index = name.indexOf(target);
                        String result = name.substring(0, index);
                        fileNameMap.put(exportFileName, result);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        Map<String, String> mapFile = new HashMap<>(2);
        String returnFile = "";
        String returnFileName = "";
        if ("0".equals(printType)) {
            if (files.size() > 1) {
                returnFile = exportService.combinPdf(files, java.util.UUID.randomUUID().toString() + ".pdf");
                returnFileName = "报关单.pdf";

            } else if (files.size() == 1) {
                returnFile = files.get(0);
                returnFileName = fileNameMap.get(files.get(0));
            } else {
                if (type != null) {
                    throw new ErrorException(400, "未找到待打印的报关单");
                } else {
                    throw new ErrorException(400, "未找到待打印的报关单草单");
                }
            }
        } else {
            //报关单集合_8位时间流水.zip
            SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
            // 获取当前时间
            Date currentDate = new Date();
            // 格式化当前时间
            String formattedDate = sdf.format(currentDate);
            returnFile = exportService.toZip(files, fileNameMap);
            returnFileName = "报关单集合_" + formattedDate + ".zip";
        }
        mapFile.put("returnFile", returnFile);
        mapFile.put("returnFileName", returnFileName);
        return mapFile;
    }


    /**
     * 清单表体海关参数转换
     *
     * @param list
     * @return
     * <AUTHOR>
     * @date 2019-03-13
     */
    public List<DecIBillList> convertForChecklist(List<DecIBillList> list) {
        for (DecIBillList item : list) {
            item.setUnit(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit()));
            item.setUnit1(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit1()));
            item.setUnit2(pCodeHolder.getValue(PCodeType.UNIT, item.getUnit2()));

            if (item.getGNo() != null) {
                item.setGNoAll(item.getSerialNo().toString() + "  (" + item.getGNo() + ")");
            } else {
                item.setGNoAll(item.getSerialNo().toString());
            }
            item.setGNameModel(item.getGName() + "\r\n" + item.getGModel());

            item.setQtyUnit((item.getQty1() == null ? "" : item.getQty1()) + " " + (item.getUnit1() == null ? "" : item.getUnit1()) + "\r\n" + (item.getQty2() == null ? "" : item.getQty2()) + " " + (item.getUnit2() == null ? "" : item.getUnit2()) + "\r\n" + item.getQty() + " " + item.getUnit());
            item.setDecAll(item.getDecPrice() + "\r\n" + item.getDecTotal() + "\r\n" + pCodeHolder.getValue(PCodeType.CURR_OUTDATED, item.getCurr()));
            if (StringUtils.isNotBlank(item.getDestinationCountry())) {
                item.setDestinationCountry(pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getDestinationCountry()) + "\r\n" + "(" + convert(item.getDestinationCountry(), "1") + ")");
            }
            item.setOriginCountry(pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getOriginCountry()) + "\r\n" + "(" + convert(item.getOriginCountry(), "1") + ")");
            if (StringUtils.isNotBlank(item.getDistrictCode()) && StringUtils.isNotBlank(item.getDistrictPostCode())) {
                item.setDistrictCode("(" + item.getDistrictCode() + "/" + item.getDistrictPostCode() + ")" + pCodeHolder.getValue(PCodeType.AREA, item.getDistrictCode()) + "/" + pCodeHolder.getValue(PCodeType.POST_AREA, item.getDistrictPostCode()));
            } else if (StringUtils.isNotBlank(item.getDistrictCode())) {
                item.setDistrictCode("(" + item.getDistrictCode() + ")" + pCodeHolder.getValue(PCodeType.AREA, item.getDistrictCode()));
            } else if (StringUtils.isNotBlank(item.getDistrictPostCode())) {
                item.setDistrictCode("(" + item.getDistrictPostCode() + ")" + pCodeHolder.getValue(PCodeType.POST_AREA, item.getDistrictPostCode()));
            }
            item.setDutyMode(pCodeHolder.getValue(PCodeType.LEVYMODE, item.getDutyMode()) + "\r\n" + "(" + item.getDutyMode() + ")");
        }
        return list;
    }

}
