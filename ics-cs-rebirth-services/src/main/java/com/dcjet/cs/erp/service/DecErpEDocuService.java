package com.dcjet.cs.erp.service;

import com.dcjet.cs.dto.erp.DecErpEDocuDto;
import com.dcjet.cs.dto.erp.DecErpEDocuParam;
import com.dcjet.cs.erp.dao.DecErpEDocuMapper;
import com.dcjet.cs.erp.mapper.DecErpEDocuDtoMapper;
import com.dcjet.cs.erp.model.DecErpEDocu;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2020-10-20
 */
@Service
public class DecErpEDocuService extends BaseService<DecErpEDocu> {
    @Resource
    private DecErpEDocuMapper decErpEDocuMapper;
    @Resource
    private DecErpEDocuDtoMapper decErpEDocuDtoMapper;

    @Override
    public Mapper<DecErpEDocu> getMapper() {
        return decErpEDocuMapper;
    }

    /**
     * 获取分页信息
     *
     * @param decErpEDocuParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<DecErpEDocuDto>> getListPaged(DecErpEDocuParam decErpEDocuParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        DecErpEDocu decErpEDocu = decErpEDocuDtoMapper.toPo(decErpEDocuParam);
        decErpEDocu.setTradeCode(userInfo.getCompany());
        Page<DecErpEDocu> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decErpEDocuMapper.getList(decErpEDocu));
        List<DecErpEDocuDto> decErpEDocuDtos = page.getResult().stream().map(head -> {
            DecErpEDocuDto dto = decErpEDocuDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<DecErpEDocuDto>> paged = ResultObject.createInstance(decErpEDocuDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param decErpEDocuParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecErpEDocuDto insert(DecErpEDocuParam decErpEDocuParam, UserInfoToken userInfo) {
        DecErpEDocu decErpEDocu = decErpEDocuDtoMapper.toPo(decErpEDocuParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        decErpEDocu.setSid(sid);
        decErpEDocu.setInsertUser(userInfo.getUserNo());
        decErpEDocu.setInsertUserName(userInfo.getUserName());
        decErpEDocu.setTradeCode(userInfo.getCompany());
        decErpEDocu.setInsertTime(new Date());
        if(decErpEDocuMapper.itExist(decErpEDocu) > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("随附单证代码、随附单证编号已存在"));
        }
        // 新增数据
        int insertStatus = decErpEDocuMapper.insert(decErpEDocu);
        return insertStatus > 0 ? decErpEDocuDtoMapper.toDto(decErpEDocu) : null;
    }

    /**
     * 功能描述:修改
     *
     * @param decErpEDocuParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecErpEDocuDto update(DecErpEDocuParam decErpEDocuParam, UserInfoToken userInfo) {
        DecErpEDocu decErpEDocu = decErpEDocuMapper.selectByPrimaryKey(decErpEDocuParam.getSid());
        decErpEDocuDtoMapper.updatePo(decErpEDocuParam, decErpEDocu);
        decErpEDocu.setUpdateUser(userInfo.getUserNo());
        decErpEDocu.setUpdateUserName(userInfo.getUserName());
        decErpEDocu.setUpdateTime(new Date());
        if(decErpEDocuMapper.itExist(decErpEDocu) > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("随附单证代码、随附单证编号已存在"));
        }
        // 更新数据
        int update = decErpEDocuMapper.updateByPrimaryKey(decErpEDocu);
        return update > 0 ? decErpEDocuDtoMapper.toDto(decErpEDocu) : null;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
        decErpEDocuMapper.deleteBySids(sids);
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecErpEDocuDto> selectAll(DecErpEDocuParam exportParam, UserInfoToken userInfo) {
        DecErpEDocu decErpEDocu = decErpEDocuDtoMapper.toPo(exportParam);
        decErpEDocu.setTradeCode(userInfo.getCompany());
        List<DecErpEDocuDto> decErpEDocuDtos = new ArrayList<>();
        List<DecErpEDocu> decErpEDocus = decErpEDocuMapper.getList(decErpEDocu);
        if (CollectionUtils.isNotEmpty(decErpEDocus)) {
            decErpEDocuDtos = decErpEDocus.stream().map(head -> {
                DecErpEDocuDto dto = decErpEDocuDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decErpEDocuDtos;
    }
}
