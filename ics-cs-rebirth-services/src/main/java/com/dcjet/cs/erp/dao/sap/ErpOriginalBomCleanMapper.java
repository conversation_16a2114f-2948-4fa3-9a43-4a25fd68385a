package com.dcjet.cs.erp.dao.sap;

import com.dcjet.cs.erp.model.sap.ErpOriginalBomClean;
import com.dcjet.cs.mrp.model.MrpBom;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * ErpOriginalBomClean
 *
 * <AUTHOR>
 * @date: 2022-5-23
 */
public interface ErpOriginalBomCleanMapper extends Mapper<ErpOriginalBomClean> {
    /**
     * 查询获取数据
     *
     * @param erpOriginalBomClean
     * @return
     */
    List<ErpOriginalBomClean> getList(ErpOriginalBomClean erpOriginalBomClean);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 更新净耗
     *
     * @param erpOriginalBomClean
     */
    void updateDecCm(ErpOriginalBomClean erpOriginalBomClean);

    List<ErpOriginalBomClean> selectGetList(@Param("tradeCode") String tradeCode);

    /**
     * 更新损耗
     *
     * @param head
     */
    void updateDecDm(ErpOriginalBomClean head);

    /**
     * 查询所有数据
     *
     * @param company
     * @return
     */
    List<ErpOriginalBomClean> getListAll(String company);

    /**
     * 根据条件删除内销维护数据
     *
     * @param head
     */
    void deleteBom(ErpOriginalBomClean head);

    /**
     * 批量插入
     *
     * @param map
     */
    void insertSelectList(Map<String, Object> map);

    /**
     * 更新状态
     *
     * @param tradeCode
     */
    void updateStatus(@Param("tradeCode") String tradeCode);

    List<ErpOriginalBomClean> getErrorList(ErpOriginalBomClean erpOriginalBomClean);

    /**
     * 根据物料群组更新备案理论总耗用
     *
     * @param tradeCode
     */
    void updateConsumptionTotal(@Param("tradeCode") String tradeCode);

    /**
     * 根据状态查询备案理论总耗用数据
     *
     * @param status
     * @param tradeCode
     * @return
     */
    List<ErpOriginalBomClean> selectTotal(@Param("status") String status, @Param("tradeCode") String tradeCode);

    void deleteBOmClean();

    /**
     * @param tradeCode
     */
    void updateCheck(@Param("tradeCode") String tradeCode);

    List<MrpBom> selectList(@Param("emsNo") String emsNo, @Param("tradeCode") String tradeCode);

    ErpOriginalBomClean cleaningStatus(@Param("tradeCode") String tradeCode);
}
