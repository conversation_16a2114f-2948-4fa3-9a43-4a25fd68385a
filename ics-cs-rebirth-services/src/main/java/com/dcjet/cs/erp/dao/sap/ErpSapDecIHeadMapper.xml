<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.erp.dao.sap.ErpSapDecIHeadMapper">
    <resultMap id="erpDecIHeadResultMap" type="com.dcjet.cs.erp.model.sap.ErpDecIHead">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="LINKED_NO" property="linkedNo" jdbcType="VARCHAR"/>
        <result column="FORWARD_CODE" property="forwardCode" jdbcType="VARCHAR"/>
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="CONTR_NO" property="contrNo" jdbcType="VARCHAR"/>
        <result column="WRAP_TYPE" property="wrapType" jdbcType="VARCHAR"/>
        <result column="PACK_NUM" property="packNum" jdbcType="NUMERIC"/>
        <result column="NET_WT" property="netWt" jdbcType="NUMERIC"/>
        <result column="GROSS_WT" property="grossWt" jdbcType="NUMERIC"/>
        <result column="VOLUME" property="volume" jdbcType="NUMERIC"/>
        <result column="TRAF_MODE" property="trafMode" jdbcType="VARCHAR"/>
        <result column="DISTRICT_CODE" property="districtCode" jdbcType="VARCHAR"/>
        <result column="DISTRICT_POST_CODE" property="districtPostCode" jdbcType="VARCHAR"/>
        <result column="TRADE_NATION" property="tradeNation" jdbcType="VARCHAR"/>
        <result column="TRADE_COUNTRY" property="tradeCountry" jdbcType="VARCHAR"/>
        <result column="DESP_PORT" property="despPort" jdbcType="VARCHAR"/>
        <result column="IE_PORT" property="IEPort" jdbcType="VARCHAR"/>
        <result column="ENTRY_PORT" property="entryPort" jdbcType="VARCHAR"/>
        <result column="DEST_PORT" property="destPort" jdbcType="VARCHAR"/>
        <result column="DESTINATION_COUNTRY" property="destinationCountry" jdbcType="VARCHAR"/>
        <result column="DECLARE_CODE" property="declareCode" jdbcType="VARCHAR"/>
        <result column="DECLARE_NAME" property="declareName" jdbcType="VARCHAR"/>
        <result column="TRADE_CREDIT_CODE" property="tradeCreditCode" jdbcType="VARCHAR"/>
        <result column="EMS_NO" property="emsNo" jdbcType="VARCHAR"/>
        <result column="TRADE_MODE" property="tradeMode" jdbcType="VARCHAR"/>
        <result column="CUT_MODE" property="cutMode" jdbcType="VARCHAR"/>
        <result column="LICENSE_NO" property="licenseNo" jdbcType="VARCHAR"/>
        <result column="MERGE_TYPE" property="mergeType" jdbcType="VARCHAR"/>
        <result column="VOYAGE_DATE" property="voyageDate" jdbcType="DATE"/>
        <result column="MAWB" property="mawb" jdbcType="VARCHAR"/>
        <result column="HAWB" property="hawb" jdbcType="VARCHAR"/>
        <result column="TRAF_NAME" property="trafName" jdbcType="VARCHAR"/>
        <result column="VOYAGE_NO" property="voyageNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_CODE" property="receiveCode" jdbcType="VARCHAR"/>
        <result column="RECEIVE_NAME" property="receiveName" jdbcType="VARCHAR"/>
        <result column="MASTER_CUSTOMS" property="masterCustoms" jdbcType="VARCHAR"/>
        <result column="IE_DATE" property="ieDate" jdbcType="DATE"/>
        <result column="TRANS_MODE" property="transMode" jdbcType="VARCHAR"/>
        <result column="FEE_RATE" property="feeRate" jdbcType="NUMERIC"/>
        <result column="INSUR_RATE" property="insurRate" jdbcType="NUMERIC"/>
        <result column="OTHER_RATE" property="otherRate" jdbcType="NUMERIC"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="IE_FLAG" property="ieFlag" jdbcType="VARCHAR"/>
        <result column="G_MARK" property="GMark" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="TEMP_OWNER" property="tempOwner" jdbcType="VARCHAR"/>
        <result column="MODIFY_MARK" property="modifyMark" jdbcType="NUMERIC"/>
        <result column="LAST_MODIFY_DATE" property="lastModifyDate" jdbcType="TIMESTAMP"/>
        <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <!-- add by JJL 2020-03-14 -->
        <result column="CONTAINER_MD" property="containerMd" jdbcType="VARCHAR" />
        <result column="CONTAINER_TYPE" property="containerType" jdbcType="VARCHAR" />
        <result column="CONTAINER_LCL" property="containerLcl" jdbcType="VARCHAR" />
        <result column="CONTAINER_G_NO" property="containerGNo" jdbcType="VARCHAR" />
        <result column="GOODS_STROE_PLACE" property="goodsStroePlace" jdbcType="VARCHAR" />
        <result column="OVERSEAS_SHIPPER" property="overseasShipper" jdbcType="VARCHAR" />
        <result column="OVERSEAS_SHIPPER_NAME" property="overseasShipperName" jdbcType="VARCHAR" />
        <result column="DCLCUS_MARK" property="dclcusMark" jdbcType="VARCHAR" />
        <result column="DCLCUS_TYPE" property="dclcusType" jdbcType="VARCHAR" />
        <result column="ENTRY_TYPE" property="entryType" jdbcType="VARCHAR" />
        <result column="REL_EMS_NO" property="relEmsNo" jdbcType="VARCHAR" />
        <result column="FEE_MARK" property="feeMark" jdbcType="VARCHAR" />
        <result column="FEE_CURR" property="feeCurr" jdbcType="VARCHAR" />
        <result column="INSUR_MARK" property="insurMark" jdbcType="VARCHAR" />
        <result column="INSUR_CURR" property="insurCurr" jdbcType="VARCHAR" />
        <result column="OTHER_MARK" property="otherMark" jdbcType="VARCHAR" />
        <result column="OTHER_CURR" property="otherCurr" jdbcType="VARCHAR" />
        <result column="ENTRY_MARK" property="entryMark" jdbcType="VARCHAR" />
        <result column="CONTAINER_WT" property="containerWt" jdbcType="NUMERIC" />
        <result column="EMS_LIST_NO" property="emsListNo" jdbcType="VARCHAR" />
        <!-- add by jlzhang 新接口字段 -->
        <result column="DESTINATION_COUNTRY_CONVERT" property="destinationCountryConvert" jdbcType="VARCHAR" />
        <result column="G_MARK_CONVERT" property="GMarkConvert" jdbcType="VARCHAR" />
        <result column="PUSH_STATUS" property="pushStatus" jdbcType="VARCHAR" />
        <result column="OVERSEAS_SHIPPER_AEO" property="overseasShipperAeo" jdbcType="VARCHAR" />
        <result column="OVERSEAS_SHIPPER_ENAME" property="overseasShipperEname" jdbcType="VARCHAR" />
        <result column="SERIAL_NO" property="serialNo" jdbcType="NUMERIC" />
        <result column="ROYALITY_NO" property="royalityNo" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,LINKED_NO
     ,FORWARD_CODE
     ,INVOICE_NO
     ,CONTR_NO
     ,WRAP_TYPE
     ,PACK_NUM
     ,NET_WT
     ,GROSS_WT
     ,VOLUME
     ,TRAF_MODE
     ,DISTRICT_CODE
     ,DISTRICT_POST_CODE
     ,TRADE_NATION
     ,TRADE_COUNTRY
     ,DESP_PORT
     ,IE_PORT
     ,ENTRY_PORT
     ,DEST_PORT
     ,DESTINATION_COUNTRY
     ,DECLARE_CODE
     ,DECLARE_NAME
     ,TRADE_CREDIT_CODE
     ,EMS_NO
     ,TRADE_MODE
     ,CUT_MODE
     ,LICENSE_NO
     ,MERGE_TYPE
     ,VOYAGE_DATE
     ,MAWB
     ,HAWB
     ,TRAF_NAME
     ,VOYAGE_NO
     ,RECEIVE_CODE
     ,RECEIVE_NAME
     ,MASTER_CUSTOMS
     ,IE_DATE
     ,TRANS_MODE
     ,FEE_RATE
     ,INSUR_RATE
     ,OTHER_RATE
     ,NOTE
     ,IE_FLAG
     ,G_MARK
     ,TRADE_CODE
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,TEMP_OWNER
     ,MODIFY_MARK
     ,LAST_MODIFY_DATE
     ,DATA_SOURCE
     ,STATUS
     ,CONTAINER_MD
     ,CONTAINER_TYPE
     ,CONTAINER_LCL
     ,CONTAINER_G_NO
     ,GOODS_STROE_PLACE
     ,OVERSEAS_SHIPPER
     ,OVERSEAS_SHIPPER_NAME
     ,DCLCUS_MARK
     ,DCLCUS_TYPE
     ,ENTRY_TYPE
     ,REL_EMS_NO
     ,FEE_MARK
     ,FEE_CURR
     ,INSUR_MARK
     ,INSUR_CURR
     ,OTHER_MARK
     ,OTHER_CURR
     ,ENTRY_MARK
     ,CONTAINER_WT
     ,EMS_LIST_NO
     ,DESTINATION_COUNTRY_CONVERT
     ,G_MARK_CONVERT
     ,PUSH_STATUS
     ,OVERSEAS_SHIPPER_AEO
     ,OVERSEAS_SHIPPER_ENAME
     ,SERIAL_NO
     ,SYNC_BATCH_NO
    </sql>
    <sql id="mainCondition">
        <if test="linkedNo != null and linkedNo != '' ">
            and t.LINKED_NO like concat(concat('%',#{linkedNo}),'%')
        </if>
        <if test="status != null and status != ''">
            and t.STATUS = #{status}
        </if>
        <if test="modifyMark != null and modifyMark != ''">
            and t.MODIFY_MARK = #{modifyMark}
        </if>
        <if test="invoiceNo != null and invoiceNo != '' ">
            and t.INVOICE_NO like concat(concat('%',#{invoiceNo}),'%')
        </if>
        <if test="contrNo != null and contrNo != '' ">
            and t.CONTR_NO like concat(concat('%',#{contrNo}),'%')
        </if>
        <if test="hawb != null and hawb != '' ">
            and t.HAWB like concat(concat('%',#{hawb}),'%')
        </if>
        <if test="trafMode != null and trafMode != ''">
            and t.TRAF_MODE = #{trafMode}
        </if>
        <if test="tradeMode != null and tradeMode != ''">
            and t.TRADE_MODE = #{tradeMode}
        </if>
        <if test='_databaseId == "postgresql" '>
            <if test="ieDateFrom != null and ieDateFrom != ''">
                <![CDATA[ and t.IE_DATE >= to_timestamp(#{ieDateFrom},'yyyy-MM-dd') ]]>
            </if>
            <if test="ieDateTo != null and ieDateTo != ''">
                <![CDATA[ and t.IE_DATE < to_timestamp(#{ieDateTo},'yyyy-MM-dd') + INTERVAL '1 day']]>
            </if>
            <if test="insertTimeFrom != null and insertTimeFrom != ''">
                <![CDATA[ and t.INSERT_TIME >= to_timestamp(#{insertTimeFrom},'yyyy-MM-dd') ]]>
            </if>
            <if test="insertTimeTo != null and insertTimeTo != ''">
                <![CDATA[ and t.INSERT_TIME < to_timestamp(#{insertTimeTo},'yyyy-MM-dd') + INTERVAL '1 day']]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="ieDateFrom != null and ieDateFrom != ''">
                <![CDATA[ and t.IE_DATE >= to_date(#{ieDateFrom},'yyyy-MM-dd') ]]>
            </if>
            <if test="ieDateTo != null and ieDateTo != ''">
                <![CDATA[ and t.IE_DATE < to_date(#{ieDateTo},'yyyy-MM-dd') + 1 ]]>
            </if>
            <if test="insertTimeFrom != null and insertTimeFrom != ''">
                <![CDATA[ and t.INSERT_TIME >= to_date(#{insertTimeFrom},'yyyy-MM-dd') ]]>
            </if>
            <if test="insertTimeTo != null and insertTimeTo != ''">
                <![CDATA[ and t.INSERT_TIME < to_date(#{insertTimeTo},'yyyy-MM-dd') + 1 ]]>
            </if>
        </if>
            and t.TRADE_CODE = #{tradeCode}
        <if test="bondMark != null and bondMark != ''">
            and t.BOND_MARK = #{bondMark}
        </if>
    </sql>

    <!-- 列表查询 and 条件 begin-->
    <select id="getHeadList" parameterType="com.dcjet.cs.dto.erp.sap.ErpDecIHeadParam"
            resultType="com.dcjet.cs.dto.erp.sap.ErpDecIHeadDto">
        SELECT t.* , s.COMPANY_NAME as forwardName
        FROM T_ERP_DEC_I_HEAD t
        left join T_BI_CLIENT_INFORMATION s
        on t.TRADE_CODE = #{tradeCode} and s.TRADE_CODE = #{tradeCode}
        and t.FORWARD_CODE = s.CUSTOMER_CODE and s.CUSTOMER_TYPE = #{cliType}
        <where>
            <include refid="mainCondition"></include>
        </where>
        order by t.INSERT_TIME DESC,t.LINKED_NO,t.sid
    </select>

    <sql id="headCondition">
        <if test="linkedNo != null and linkedNo != '' ">
            and h.LINKED_NO like concat(concat('%',#{linkedNo}),'%')
        </if>
        <if test="status != null and status != ''">
            and h.STATUS = #{status}
        </if>
        <if test="modifyMark != null and modifyMark != ''">
            and h.MODIFY_MARK = #{modifyMark}
        </if>
        <if test="contrNo != null and contrNo != '' ">
            and h.CONTR_NO like concat(concat('%',#{contrNo}),'%')
        </if>
        <if test="mawb != null and mawb != '' ">
            and h.MAWB like concat(concat('%',#{mawb}),'%')
        </if>
        <if test="invoiceNo != null and invoiceNo != '' ">
            and h.INVOICE_NO like concat(concat('%',#{invoiceNo}),'%')
        </if>
        <if test="trafMode != null and trafMode != ''">
            and h.TRAF_MODE = #{trafMode}
        </if>
        <if test="tradeMode != null and tradeMode != ''">
            and h.TRADE_MODE = #{tradeMode}
        </if>
        <if test="tempOwner != null and tempOwner != ''">
            and h.TEMP_OWNER = #{tempOwner}
        </if>
        <if test="GMarkConvert != null and GMarkConvert != ''">
            and h.G_MARK_CONVERT = #{GMarkConvert}
        </if>
        and h.TRADE_CODE = #{tradeCode}
    </sql>

    <sql id="listCondition">
        <if test="bondMarkConvert != null and bondMarkConvert != ''">
            and l.BOND_MARK_CONVERT = #{bondMarkConvert}
        </if>

        <if test="facGNo != null and facGNo != '' ">
            and l.FAC_G_NO like concat(concat('%',#{facGNo}),'%')
        </if>
        <if test="emsNo != null and emsNo != '' ">
            and l.EMS_NO like concat(concat('%',#{emsNo}),'%')
        </if>
        <if test="purchaseNo != null and purchaseNo != '' ">
            and l.PURCHASE_NO like concat(concat('%',#{purchaseNo}),'%')
        </if>
        <if test='_databaseId == "postgresql" '>
            <if test="poDateFrom != null and poDateFrom != ''">
                <![CDATA[ and l.PO_DATE >= to_timestamp(#{poDateFrom},'yyyy-MM-dd hh24:mi:ss') ]]>
            </if>
            <if test="poDateTo != null and poDateTo != ''">
                <![CDATA[ and l.PO_DATE < to_timestamp(#{poDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
            </if>
            <if test="insertTimeFrom != null and insertTimeFrom != ''">
                <![CDATA[ and l.INSERT_TIME >= to_timestamp(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="insertTimeTo != null and insertTimeTo != ''">
                <![CDATA[ and l.INSERT_TIME < to_timestamp(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="poDateFrom != null and poDateFrom != ''">
                <![CDATA[ and l.PO_DATE >= to_date(#{poDateFrom},'yyyy-MM-dd hh24:mi:ss') ]]>
            </if>
            <if test="poDateTo != null and poDateTo != ''">
                <![CDATA[ and l.PO_DATE < to_date(#{poDateTo},'yyyy-MM-dd hh24:mi:ss') + 1 ]]>
            </if>
            <if test="insertTimeFrom != null and insertTimeFrom != ''">
                <![CDATA[ and l.INSERT_TIME >= to_date(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="insertTimeTo != null and insertTimeTo != ''">
                <![CDATA[ and l.INSERT_TIME < to_date(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss') + 1 ]]>
            </if>
        </if>
        <if test="inWay != null and inWay != ''">
            and l.IN_WAY = #{inWay}
        </if>
        <if test="inWayConvert != null and inWayConvert != ''">
            and l.IN_WAY_CONVERT = #{inWayConvert}
        </if>
        <if test="decPrice == 1">
            and (l.DEC_PRICE = 0 OR l.DEC_PRICE IS NULL)
        </if>
        <if test="netWt == 1">
            and (l.NET_WT = 0 OR l.NET_WT IS NULL)
        </if>
        and l.TRADE_CODE = #{tradeCode}
    </sql>

    <sql id="headResultColumn">
       h.MASTER_CUSTOMS,h.IE_DATE,h.TRANS_MODE,h.CONTAINER_TYPE,h.CONTAINER_MD,h.CONTAINER_LCL,h.CONTAINER_G_NO,
       h.GOODS_STROE_PLACE,h.OVERSEAS_SHIPPER,h.OVERSEAS_SHIPPER_NAME,h.DCLCUS_MARK,h.DCLCUS_TYPE,h.ENTRY_TYPE,
       h.REL_EMS_NO,h.FEE_MARK,h.FEE_CURR,h.INSUR_MARK,h.INSUR_CURR,h.OTHER_MARK,h.OTHER_CURR,h.FEE_RATE,h.INSUR_RATE,
       h.ENTRY_MARK,h.CONTAINER_WT,h.EMS_LIST_NO,h.DESTINATION_COUNTRY_CONVERT,h.G_MARK_CONVERT,h.OVERSEAS_SHIPPER_AEO,
       h.OVERSEAS_SHIPPER_ENAME,h.LINKED_NO,h.FORWARD_CODE,h.INVOICE_NO,h.CONTR_NO,h.WRAP_TYPE,h.PACK_NUM,h.NET_WT,
       h.GROSS_WT,h.VOLUME,h.TRAF_MODE,h.DISTRICT_CODE,h.DISTRICT_POST_CODE,h.TRADE_NATION,h.TRADE_COUNTRY,h.DESP_PORT,
       h.IE_PORT,h.ENTRY_PORT,h.DEST_PORT,h.DESTINATION_COUNTRY,h.DECLARE_CODE,h.DECLARE_NAME,h.TRADE_CREDIT_CODE,h.EMS_NO,
       h.TRADE_MODE,h.CUT_MODE,h.LICENSE_NO,h.MERGE_TYPE,h.VOYAGE_DATE,h.MAWB,h.TRAF_NAME,h.VOYAGE_NO,h.RECEIVE_CODE,
       h.RECEIVE_NAME,h.TEMP_OWNER,h.MODIFY_MARK,h.STATUS,h.OTHER_RATE,h.NOTE as headNote,h.IE_FLAG,h.G_MARK,
       h.C_WEIGHT,h.TRADE_TERMS,h.INVITE_DATE,h.EXTRACT_TIME
     </sql>

    <sql id="listResultColumn">
        l.SID,l.LINE_NO,l.FAC_G_NO,l.QTY,l.QTY_1,l.QTY_2,l.UNIT_ERP,l.DEC_PRICE,l.DEC_TOTAL,
        l.ORIGIN_COUNTRY,l.EXG_VERSION,l.ARRIVAL_DATE,l.NOTE,l.INSERT_TIME,l.UPDATE_TIME,
        l.COP_G_NO,l.PURCHASE_NO,l.PURCHASE_LINE_NO,l.ORDER_DATE,l.PURCHASE_NUM,l.IN_WAY,
        l.LAST_MODIFY_DATE,l.DATA_SOURCE,l.COP_G_NAME_EN,l.DUTY_MODE,l.ENTRY_G_NO,
        l.ORIGIN_COUNTRY_CONVERT,l.PO_DATE,l.REMARK1,l.REMARK2,l.REMARK3,l.COST_CENTER,
        l.ORIGINAL_G_NO,l.FACTORY,l.UNIT_CONVERT,l.SUPPLIER_CODE,l.IN_WAY_CONVERT,
        l.COUNTRY_CONVERT,l.BOND_MARK,l.BOND_MARK_CONVERT,
        l.DESTINATION_COUNTRY as listDestinationCountry ,l.CURR,l.NET_WT as listNetWt,l.GROSS_WT as listGrossWt,l.INVOICE_NO as listInvoiceNo,l.INVOICE_DATE as listInvoiceDate,
        l.DISTRICT_CODE as listDistrictCode,l.DISTRICT_POST_CODE as listDistrictPostCode,l.G_MARK as listGmark,l.G_MARK_CONVERT as listGmarkConvert,
        l.ROYALITY_NO,l.IN_OUT_REL_NO,l.PACK_NUM AS LIST_PACK_NUM,l.unit,l.g_model as gmodel
    </sql>

    <select id="getWholeList" resultType="com.dcjet.cs.dto.erp.sap.ErpDecIDto"
            parameterType="com.dcjet.cs.dto.erp.sap.ErpDecIParam">
        select
        <include refid="headResultColumn"></include>
        ,
        <include refid="listResultColumn"></include>
        ,
        s.COMPANY_NAME as forwardName,s2.COMPANY_NAME as supplierName
        from T_ERP_DEC_I_HEAD h
        left join T_BI_CLIENT_INFORMATION s
        on h.TRADE_CODE = #{tradeCode} and s.TRADE_CODE = #{tradeCode}
        and s.CUSTOMER_TYPE = #{cliType} and h.FORWARD_CODE = s.CUSTOMER_CODE
        left join T_ERP_DEC_I_LIST l on l.TRADE_CODE = #{tradeCode} and l.LINKED_NO = h.LINKED_NO
        left join T_BI_CLIENT_INFORMATION s2
        on  s2.TRADE_CODE = #{tradeCode}  and s2.CUSTOMER_TYPE = #{cliType2} and l.SUPPLIER_CODE = s2.CUSTOMER_CODE
        <where>
            <choose>
                <when test='supplierName == "N"'>
                    and s2.COMPANY_NAME IS NULL
                </when>
                <when test="supplierName != null and supplierName != '' ">
                    and (l.SUPPLIER_CODE like concat(concat('%',#{supplierName}),'%') OR
                    s2.COMPANY_NAME like concat(concat('%',#{supplierName}),'%'))
                </when>
            </choose>
            <include refid="headCondition"></include>
            <include refid="listCondition"></include>
        </where>
        order by l.UPDATE_TIME DESC,h.LINKED_NO,l.LINE_NO, l.sid
    </select>

    <!-- 查询提取表头数据  获取数据  JJL ADD 2020-04-02 -->
    <select id="getLeadList" resultMap="erpDecIHeadResultMap" parameterType="com.dcjet.cs.erp.model.sap.ErpDecIHead">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_ERP_DEC_I_HEAD h
        <where>
            and h.TRADE_CODE=#{tradeCode}
            <if test="linkedNo != null and linkedNo !=''">
                and h.LINKED_NO=#{linkedNo}
            </if>

        </where>
    </select>

    <select id="getByIdentifier" resultType="com.dcjet.cs.erp.model.sap.ErpDecIHead">
        select *
        from T_ERP_DEC_I_HEAD
        where  LINKED_NO = #{linkedNo} and TRADE_CODE = #{tradeCode}
   </select>
    <select id="selectDataCount" resultType="java.lang.Integer" parameterType="com.dcjet.cs.dto.erp.sap.ErpDecIHeadParam">
        SELECT count(1)
        FROM T_ERP_DEC_I_HEAD t
        left join T_BI_CLIENT_INFORMATION s
        on t.TRADE_CODE = #{tradeCode} and s.TRADE_CODE = #{tradeCode}
        and t.FORWARD_CODE = s.CUSTOMER_CODE and s.CUSTOMER_TYPE = #{cliType}
        <where>
            <include refid="mainCondition"></include>
        </where>
    </select>
    <select id="selectDataCountAll" resultType="java.lang.Integer"
            parameterType="com.dcjet.cs.dto.erp.sap.ErpDecIParam">
        select
            count(1)
        from T_ERP_DEC_I_HEAD h
        left join T_BI_CLIENT_INFORMATION s
        on h.TRADE_CODE = #{tradeCode} and s.TRADE_CODE = #{tradeCode}
        and s.CUSTOMER_TYPE = #{cliType} and h.FORWARD_CODE = s.CUSTOMER_CODE
        left join T_ERP_DEC_I_LIST l on l.TRADE_CODE = #{tradeCode} and l.LINKED_NO = h.LINKED_NO
        left join T_BI_CLIENT_INFORMATION s2
        on  s2.TRADE_CODE = #{tradeCode}  and s2.CUSTOMER_TYPE = #{cliType2} and l.SUPPLIER_CODE = s2.CUSTOMER_CODE
        <where>
            <choose>
                <when test='supplierName == "N"'>
                    and s2.COMPANY_NAME IS NULL
                </when>
                <when test="supplierName != null and supplierName != '' ">
                    and (l.SUPPLIER_CODE like concat(concat('%',#{supplierName}),'%') OR
                    s2.COMPANY_NAME like concat(concat('%',#{supplierName}),'%'))
                </when>
            </choose>
            <include refid="headCondition"></include>
            <include refid="listCondition"></include>
        </where>
    </select>

    <select id="selectinsert">
        insert into t_tmp_erp_dec_i_head(
        sid,
        linked_no,
        forward_code,
        invoice_no,
        contr_no,
        wrap_type,
        pack_num,
        net_wt,
        gross_wt,
        volume,
        traf_mode,
        district_code,
        district_post_code,
        trade_nation,
        trade_country,
        desp_port,
        ie_port,
        entry_port,
        dest_port,
        destination_country,
        declare_code,
        declare_name,
        trade_credit_code,
        ems_no,
        trade_mode,
        cut_mode,
        license_no,
        merge_type,
        voyage_date,
        mawb,
        hawb,
        traf_name,
        voyage_no,
        receive_code,
        receive_name,
        master_customs,
        ie_date,
        trans_mode,
        fee_rate,
        insur_rate,
        other_rate,
        note,
        ie_flag,
        g_mark,
        trade_code,
        insert_user,
        insert_time,
        update_user,
        update_time,
        modify_mark,
        last_modify_date,
        data_source,
        status,
        container_md,
        container_type,
        container_lcl,
        container_g_no,
        goods_stroe_place,
        overseas_shipper,
        overseas_shipper_name,
        dclcus_mark,
        dclcus_type,
        entry_type,
        rel_ems_no,
        fee_mark,
        fee_curr,
        insur_mark,
        insur_curr,
        other_mark,
        other_curr,
        entry_mark,
        container_wt,
        ems_list_no,
        destination_country_convert,
        g_mark_convert,
        push_status,
        overseas_shipper_aeo,
        overseas_shipper_ename,
        serial_no,
        sync_batch_no,
        invite_date,
        trade_terms,
        c_weight,
        extract_time,
        temp_owner,
        temp_mark,
        temp_remark
        )
        select sys_guid(),
        linked_no,
        forward_code,
        invoice_no,
        contr_no,
        wrap_type,
        pack_num,
        net_wt,
        gross_wt,
        volume,
        traf_mode,
        district_code,
        district_post_code,
        trade_nation,
        trade_country,
        desp_port,
        ie_port,
        entry_port,
        dest_port,
        destination_country,
        declare_code,
        declare_name,
        trade_credit_code,
        ems_no,
        trade_mode,
        cut_mode,
        license_no,
        merge_type,
        voyage_date,
        mawb,
        hawb,
        traf_name,
        voyage_no,
        receive_code,
        receive_name,
        master_customs,
        ie_date,
        trans_mode,
        fee_rate,
        insur_rate,
        other_rate,
        note,
        ie_flag,
        g_mark,
        trade_code,
        insert_user,
        insert_time,
        update_user,
        update_time,
        modify_mark,
        last_modify_date,
        data_source,
        status,
        container_md,
        container_type,
        container_lcl,
        container_g_no,
        goods_stroe_place,
        overseas_shipper,
        overseas_shipper_name,
        dclcus_mark,
        dclcus_type,
        entry_type,
        rel_ems_no,
        fee_mark,
        fee_curr,
        insur_mark,
        insur_curr,
        other_mark,
        other_curr,
        entry_mark,
        container_wt,
        ems_list_no,
        destination_country_convert,
        g_mark_convert,
        push_status,
        overseas_shipper_aeo,
        overseas_shipper_ename,
        serial_no,
        sync_batch_no,
        invite_date,
        trade_terms,
        c_weight,
        extract_time,
        #{sid},
        null,
        null
        from t_erp_dec_i_head where linked_no in
        <foreach collection="linkedNos" item="linkedNo" open="(" close=")" separator=",">
            #{linkedNo}
        </foreach>
        and status ='0'
        and trade_code = #{tradeCode};

        insert into t_tmp_erp_dec_i_list(
        sid,
        linked_no,
        line_no,
        bond_mark,
        fac_g_no,
        qty,
        unit_erp,
        dec_price,
        dec_total,
        curr,
        net_wt,
        gross_wt,
        origin_country,
        exg_version,
        arrival_date,
        supplier_code,
        note,
        trade_code,
        insert_user,
        insert_time,
        update_user,
        update_time,
        status,
        g_mark,
        qty_1,
        qty_2,
        serial_no,
        cop_g_no,
        invoice_no,
        purchase_no,
        purchase_line_no,
        order_date,
        purchase_num,
        in_way,
        modify_mark,
        last_modify_date,
        data_source,
        cost_center,
        pack_num,
        original_g_no,
        cop_g_name_en,
        factory,
        remark1,
        remark2,
        remark3,
        is_clear,
        unit_convert,
        curr_convert,
        lock_mark,
        supplier_name,
        in_way_convert,
        country_convert,
        g_mark_convert,
        bond_mark_convert,
        head_id,
        destination_country_convert,
        origin_country_convert,
        push_status,
        po_date,
        duty_mode,
        district_code,
        district_post_code,
        destination_country,
        entry_g_no,
        sync_batch_no,
        royality_no,
        in_out_rel_no,
        extract_time,
        g_model,
        unit,
        invoice_date,
        temp_owner,
        temp_flag,
        temp_remark)
        select sys_guid(),
        linked_no,
        line_no,
        bond_mark,
        fac_g_no,
        qty,
        unit_erp,
        dec_price,
        dec_total,
        curr,
        net_wt,
        gross_wt,
        origin_country,
        exg_version,
        arrival_date,
        supplier_code,
        note,
        trade_code,
        insert_user,
        insert_time,
        update_user,
        update_time,
        status,
        g_mark,
        qty_1,
        qty_2,
        serial_no,
        cop_g_no,
        invoice_no,
        purchase_no,
        purchase_line_no,
        order_date,
        purchase_num,
        in_way,
        modify_mark,
        last_modify_date,
        data_source,
        cost_center,
        pack_num,
        original_g_no,
        cop_g_name_en,
        factory,
        remark1,
        remark2,
        remark3,
        is_clear,
        unit_convert,
        curr_convert,
        lock_mark,
        supplier_name,
        in_way_convert,
        country_convert,
        g_mark_convert,
        bond_mark_convert,
        head_id,
        destination_country_convert,
        origin_country_convert,
        push_status,
        po_date,
        duty_mode,
        district_code,
        district_post_code,
        destination_country,
        entry_g_no,
        sync_batch_no,
        royality_no,
        in_out_rel_no,
        extract_time,
        g_model,
        unit,
        invoice_date,
        #{sid},
        null,
        null
        from t_erp_dec_i_list where linked_no in
        <foreach collection="linkedNos" item="linkedNo" open="(" close=")" separator=",">
            #{linkedNo}
        </foreach>
        and status ='0'
        and trade_code = #{tradeCode};
    </select>

    <select id="selectLinkedNo" resultType="java.lang.String">
        select distinct linked_no from t_tmp_erp_dec_i_head where temp_owner = #{sid} and trade_code = #{tradeCode}
    </select>
    <select id="selectHeadLinkedNo" resultType="java.lang.String">
        select distinct ems_list_no as linked_no
        from t_dec_erp_e_head_n
        where ems_list_no in
        <foreach collection="linkedNos" item="linkedNo" open="(" close=")" separator=",">
            #{linkedNo}
        </foreach>
        and trade_code = #{tradeCode}
    </select>
    <select id="getList" resultType="com.dcjet.cs.erp.model.sap.ErpDecIHead">
        select * from t_tmp_erp_dec_i_head where temp_owner = #{sid} and trade_code = #{tradeCode}
    </select>

    <delete id="seletctDelete">
        delete from t_tmp_erp_dec_i_head where temp_owner = #{sid} and trade_code = #{tradeCode};
        delete from t_tmp_erp_dec_i_list where temp_owner = #{sid} and trade_code = #{tradeCode};
    </delete>

</mapper>
