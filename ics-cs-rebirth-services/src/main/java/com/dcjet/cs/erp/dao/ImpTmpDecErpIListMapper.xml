<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.erp.dao.ImpTmpDecErpIListMapper">
    <resultMap id="impTmpDecErpIListResultMap" type="com.dcjet.cs.erp.model.ImpTmpDecErpIList">
        <result column="SID" jdbcType="VARCHAR" property="sid"/>
        <result column="TEMP_OWNER" jdbcType="VARCHAR" property="tempOwner"/>
        <result column="TEMP_MARK" jdbcType="DECIMAL" property="tempMark"/>
        <result column="TEMP_REMARK" jdbcType="VARCHAR" property="tempRemark"/>
        <result column="HEAD_ID" jdbcType="VARCHAR" property="headId"/>
        <result column="EMS_LIST_NO" jdbcType="VARCHAR" property="emsListNo"/>
        <result column="SERIAL_NO" jdbcType="DECIMAL" property="serialNo"/>
        <result column="G_NO" jdbcType="DECIMAL" property="GNo"/>
        <result column="COP_G_NO" jdbcType="VARCHAR" property="copGNo"/>
        <result column="CODE_T_S" jdbcType="VARCHAR" property="codeTS"/>
        <result column="G_NAME" jdbcType="VARCHAR" property="GName"/>
        <result column="G_MODEL" jdbcType="VARCHAR" property="GModel"/>
        <result column="UNIT" jdbcType="VARCHAR" property="unit"/>
        <result column="UNIT_1" jdbcType="VARCHAR" property="unit1"/>
        <result column="UNIT_2" jdbcType="VARCHAR" property="unit2"/>
        <result column="ORIGIN_COUNTRY" jdbcType="VARCHAR" property="originCountry"/>
        <result column="DESTINATION_COUNTRY" jdbcType="VARCHAR" property="destinationCountry"/>
        <result column="DEC_PRICE" jdbcType="VARCHAR" property="decPrice"/>
        <result column="DEC_TOTAL" jdbcType="VARCHAR" property="decTotal"/>
        <result column="USD_PRICE" jdbcType="DECIMAL" property="usdDecTotal"/>
        <result column="CURR" jdbcType="VARCHAR" property="curr"/>
        <result column="QTY_1" jdbcType="VARCHAR" property="qty1"/>
        <result column="QTY_2" jdbcType="VARCHAR" property="qty2"/>
        <result column="FACTOR_WT" jdbcType="DECIMAL" property="factorWt"/>
        <result column="FACTOR_1" jdbcType="DECIMAL" property="factor1"/>
        <result column="FACTOR_2" jdbcType="DECIMAL" property="factor2"/>
        <result column="QTY" jdbcType="VARCHAR" property="qty"/>
        <result column="GROSS_WT" jdbcType="VARCHAR" property="grossWt"/>
        <result column="NET_WT" jdbcType="VARCHAR" property="netWt"/>
        <result column="USE_TYPE" jdbcType="VARCHAR" property="useType"/>
        <result column="DUTY_MODE" jdbcType="VARCHAR" property="dutyMode"/>
        <result column="EXG_VERSION" jdbcType="VARCHAR" property="exgVersion"/>
        <result column="ENTRY_G_NO" jdbcType="DECIMAL" property="entryGNo"/>
        <result column="NOTE" jdbcType="VARCHAR" property="note"/>
        <result column="COP_EMS_NO" jdbcType="VARCHAR" property="copEmsNo"/>
        <result column="CLASS_MARK" jdbcType="VARCHAR" property="classMark"/>
        <result column="ARRIVAL_DATE" jdbcType="DATE" property="addTime"/>
        <result column="INSERT_USER" jdbcType="VARCHAR" property="insertUser"/>
        <result column="INSERT_TIME" jdbcType="DATE" property="insertTime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_TIME" jdbcType="DATE" property="updateTime"/>
        <result column="BOND_MARK" jdbcType="VARCHAR" property="bondMark"/>
        <result column="G_MARK" jdbcType="VARCHAR" property="GMark"/>
        <result column="EMS_NO" jdbcType="VARCHAR" property="emsNo"/>
        <result column="COP_G_NAME" jdbcType="VARCHAR" property="copGName"/>
        <result column="COP_G_MODEL" jdbcType="VARCHAR" property="copGModel"/>
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo"/>
        <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="VOLUME" jdbcType="VARCHAR" property="volume"/>
        <result column="SUPPLIER_CODE" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName"/>
        <result column="TRADE_MODE" jdbcType="VARCHAR" property="tradeMode"/>
        <result column="COST_CENTER" property="costCenter" jdbcType="VARCHAR" />
        <result column="FAC_G_NO" jdbcType="VARCHAR" property="facGNo" />
        <result column="LINKED_NO" jdbcType="VARCHAR" property="linkedNo" />
        <result column="LINE_NO" jdbcType="VARCHAR" property="lineNo" />
        <result column="NOTE_1" property="note1" jdbcType="VARCHAR" />
        <result column="NOTE_2" property="note2" jdbcType="VARCHAR" />
        <result column="NOTE_3" property="note3" jdbcType="VARCHAR" />
        <result column="DISTRICT_CODE" property="districtCode" jdbcType="VARCHAR" />
        <result column="DISTRICT_POST_CODE" property="districtPostCode" jdbcType="VARCHAR" />
        <result column="TEMP_FLAG" jdbcType="DECIMAL" property="tempFlag"/>
        <result column="TEMP_INDEX" jdbcType="DECIMAL" property="tempIndex"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="ORDER_LINE_NO" property="orderLineNo" jdbcType="VARCHAR"/>
        <result column="CONFIRM_ROYALTIES" property="confirmRoyalties" jdbcType="VARCHAR"/>
        <result column="CONFIRM_ROYALTIES_NO" property="confirmRoyaltiesNo" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="checkTmpByParam" parameterMap="checkTmpMap" statementType="CALLABLE" resultType="java.util.Map">
      CALL P_IMP_DEC_ERP_I_LIST(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpMap">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="INOUT" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="checkTmpByParam" parameterMap="checkTmpMap2" statementType="CALLABLE" resultType="java.util.Map" databaseId="postgresql">
      CALL P_IMP_DEC_ERP_I_LIST(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpMap2">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="selectALL" resultMap="impTmpDecErpIListResultMap" parameterType="map">
        select t.SID,
            t.TEMP_OWNER,
            t.TEMP_MARK,
            t.TEMP_REMARK,
            t.HEAD_ID,
            t.EMS_LIST_NO,
            t.SERIAL_NO,
            t.G_NO,
            t.COP_G_NO,
            t.CODE_T_S,
            t.G_NAME,
            t.G_MODEL,
            t.UNIT,
            t.UNIT_1,
            t.UNIT_2,
            t.ORIGIN_COUNTRY,
            t.DESTINATION_COUNTRY,
            t.DESTINATION_COUNTRY as COUNTRY,
            t.DEC_PRICE,
            t.DEC_TOTAL,
            t.USD_PRICE,
            t.CURR,
            t.QTY_1,
            t.QTY_2,
            t.FACTOR_WT,
            t.FACTOR_1,
            t.FACTOR_2,
            t.QTY,
            t.GROSS_WT,
            t.NET_WT,
            t.USE_TYPE,
            t.DUTY_MODE,
            t.EXG_VERSION,
            t.ENTRY_G_NO,
            t.NOTE,
            t.COP_EMS_NO,
            t.CLASS_MARK,
            t.ARRIVAL_DATE,
            t.INSERT_USER,
            t.INSERT_TIME,
            t.UPDATE_USER,
            t.UPDATE_TIME,
            t.BOND_MARK,
            t.G_MARK,
            t.EMS_NO,
            t.COP_G_NAME,
            t.COP_G_MODEL,
            t.ORDER_NO,
            t.INVOICE_NO,
            t.VOLUME,
            t.SUPPLIER_CODE,
            t.SUPPLIER_NAME,
            t.TRADE_MODE,
            t.FAC_G_NO,
            t.LINKED_NO,
            t.UNIT_ERP,
            t.NOTE_1,
            t.NOTE_2,
            t.NOTE_3,
            t.DISTRICT_CODE,
            t.DISTRICT_POST_CODE,
            t.TEMP_FLAG,
            t.TEMP_INDEX,
            t.COST_CENTER,
            t.LINE_NO,
            t.INSERT_USER_NAME,
            t.UPDATE_USER_NAME,
            t.TRADE_CODE,
            t.ORDER_LINE_NO,
            t.CONFIRM_ROYALTIES,
            t.CONFIRM_ROYALTIES_NO
        from T_IMP_TMP_DEC_ERP_I_LIST t
        <where>
            and t.TEMP_OWNER = #{tempOwner,jdbcType=VARCHAR}
        </where>
    </select>

    <select id="selectByFlag" resultMap="impTmpDecErpIListResultMap" parameterType="map">
        select t.* from T_IMP_TMP_DEC_ERP_I_LIST t
        <where>
            and t.TEMP_OWNER = #{TEMP_OWNER,jdbcType=VARCHAR}
            AND t.TEMP_FLAG IN
            <foreach item="item" index="index" collection="TEMP_FLAG" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        order by t.TEMP_INDEX ASC
    </select>

    <select id="checkTmpByParamGR" parameterMap="checkTmpMapGR" statementType="CALLABLE" resultType="java.util.Map">
      CALL P_IMP_DEC_ERP_I_LIST_GR(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpMapGR">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="INOUT" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="checkTmpByParamGR" parameterMap="checkTmpMapGR2" statementType="CALLABLE" resultType="java.util.Map" databaseId="postgresql">
      CALL P_IMP_DEC_ERP_I_LIST_GR(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpMapGR2">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="checkTmpByParamAMK" parameterMap="checkTmpMapAMK" statementType="CALLABLE" resultType="java.util.Map">
      CALL P_IMP_DEC_ERP_I_LIST_AMK(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpMapAMK">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="INOUT" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="checkTmpByParamAMK" parameterMap="checkTmpMapAMK2" statementType="CALLABLE" resultType="java.util.Map" databaseId="postgresql">
      CALL P_IMP_DEC_ERP_I_LIST_AMK(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpMapAMK2">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="selectList" resultMap="impTmpDecErpIListResultMap" parameterType="string">
        SELECT serialNo FROM T_IMP_TMP_DEC_ERP_I_LIST WHERE temp_owner = #{tempOwner}
    </select>
    <select id="selectError" resultMap="impTmpDecErpIListResultMap">
        select t.SID,
        t.TEMP_OWNER,
        t.TEMP_MARK,
        t.TEMP_REMARK,
        t.HEAD_ID,
        t.EMS_LIST_NO,
        t.SERIAL_NO,
        t.G_NO,
        t.COP_G_NO,
        t.CODE_T_S,
        t.G_NAME,
        t.G_MODEL,
        t.UNIT,
        t.UNIT_1,
        t.UNIT_2,
        t.ORIGIN_COUNTRY,
        t.DESTINATION_COUNTRY,
        t.DESTINATION_COUNTRY as COUNTRY,
        t.DEC_PRICE,
        t.DEC_TOTAL,
        t.USD_PRICE,
        t.CURR,
        t.QTY_1,
        t.QTY_2,
        t.FACTOR_WT,
        t.FACTOR_1,
        t.FACTOR_2,
        t.QTY,
        t.GROSS_WT,
        t.NET_WT,
        t.USE_TYPE,
        t.DUTY_MODE,
        t.EXG_VERSION,
        t.ENTRY_G_NO,
        t.NOTE,
        t.COP_EMS_NO,
        t.CLASS_MARK,
        t.ARRIVAL_DATE,
        t.INSERT_USER,
        t.INSERT_TIME,
        t.UPDATE_USER,
        t.UPDATE_TIME,
        t.BOND_MARK,
        t.G_MARK,
        t.EMS_NO,
        t.COP_G_NAME,
        t.COP_G_MODEL,
        t.ORDER_NO,
        t.INVOICE_NO,
        t.VOLUME,
        t.SUPPLIER_CODE,
        t.SUPPLIER_NAME,
        t.TRADE_MODE,
        t.FAC_G_NO,
        t.LINKED_NO,
        t.UNIT_ERP,
        t.NOTE_1,
        t.NOTE_2,
        t.NOTE_3,
        t.DISTRICT_CODE,
        t.DISTRICT_POST_CODE,
        t.TEMP_FLAG,
        t.TEMP_INDEX,
        t.COST_CENTER,
        t.LINE_NO
        ,t.INSERT_USER_NAME
        ,t.UPDATE_USER_NAME
        ,TRADE_CODE
        from T_IMP_TMP_DEC_ERP_I_LIST t
        where t.TRADE_MODE = #{tradeCode} and t.TEMP_OWNER = #{tempOwner} and TEMP_FLAG='1'
    </select>

    <delete id="deletelist" parameterType="java.util.List">
        delete from T_DEC_ERP_I_LIST_N t where t.FAC_G_NO in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteHistoryDate">
        delete from t_imp_tmp_dec_erp_i_list
        <where>
            <if test='_databaseId == "postgresql" '>
                <![CDATA[ insert_time < current_date::timestamp + '-2 month' ]]>
            </if>
            <if test='_databaseId != "postgresql" '>
                <![CDATA[ insert_time < ADD_MONTHS(trunc(sysdate), -2) ]]>
            </if>
        </where>
    </delete>


    <select id="checkTmpByParamCDLX" parameterMap="checkTmpMapCDLX" statementType="CALLABLE" resultType="java.util.Map">
        CALL P_IMP_DEC_ERP_I_LIST_CDLX(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpMapCDLX">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="INOUT" jdbcType="VARCHAR"/>
    </parameterMap>

    <select id="checkTmpByParamCDLX" parameterMap="checkTmpMapCDLX2" statementType="CALLABLE" resultType="java.util.Map" databaseId="postgresql">
        CALL P_IMP_DEC_ERP_I_LIST_CDLX(?,?,?,?)
    </select>
    <parameterMap type="java.util.Map" id="checkTmpMapCDLX2">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_FLAG" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>

</mapper>
