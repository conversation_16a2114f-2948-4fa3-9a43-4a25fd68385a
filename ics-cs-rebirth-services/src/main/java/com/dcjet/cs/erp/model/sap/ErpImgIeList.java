package com.dcjet.cs.erp.model.sap;

import com.dcjet.cs.dto.erp.sap.constants.ErpSapConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 料件出入库
 */
@Setter
@Getter
@Table(name = "T_ERP_IMG_IE_LIST")
public class ErpImgIeList implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 同步批次号
     */
    @Column(name = "SYNC_BATCH_NO")
    @JsonIgnore
    private String syncBatchNo;
    /**
     * 唯一键
     **/
    @Id
    @Column(name = "SID")
    private String sid;

    /**
     * 出入库单据号
     **/
    @Column(name = "BILL_NO")
    private String billNo;

    /**
     * 单据序号
     **/
    @Column(name = "LINE_NO")
    @JsonProperty("billSerialNo")
    private Long lineNo;

    /**
     * 库位别
     **/
    @Column(name = "WAREHOUSE_NO")
    private String warehouseNo;

    /**
     * 移动类型
     **/
    @Column(name = "IE_TYPE")
    private String ieType;

    /**
     * 清单关联编号
     **/
    @Column(name = "LINKED_NO")
    private String linkedNo;

    /**
     * 采购订单号
     **/
    @Column(name = "PO_NO")
    @JsonProperty("purchaseNo")
    private String poNo;

    /**
     * 发票号
     **/
    @Column(name = "INVOICE_NO")
    private String invoiceNo;

    /**
     * 交易日期
     **/
    @Column(name = "DELIVERY_DATE")
    private Date deliveryDate;

    /**
     * 企业料号
     **/
    @Column(name = "FAC_G_NO")
    private String facGNo;

    /**
     * 数量
     **/
    @Column(name = "QTY")
    private BigDecimal qty;

    /**
     * ERP交易单位
     **/
    @Column(name = "UNIT")
    @JsonProperty("unitErp")
    private String unit;
    /**
     * 单价
     **/
    @Column(name = "DEC_PRICE")
    private BigDecimal decPrice;

    /**
     * 总价
     **/
    @Column(name = "DEC_TOTAL")
    private BigDecimal decTotal;

    /**
     * 币制
     **/
    @Column(name = "CURR")
    private String curr;

    /**
     * 保完税标志
     **/
    @Column(name = "BOND_MARK")
    private String bondMark;

    /**
     * 供应商代码
     **/
    @Column(name = "SUPPLIER_CODE")
    private String supplierCode;

    /**
     * 供应商联系人
     **/
    @Column(name = "SUPPLIER_LINK_MAN")
    private String supplierLinkMan;

    /**
     * 进口方式
     **/
    @Column(name = "IN_WAY")
    private String inWay;

    /**
     * 成本中心
     **/
    @Column(name = "COST_CENTER")
    private String costCenter;

    /**
     * 结转状态
     **/
    @Column(name = "EXP_STATUS")
    private String expStatus;

    /**
     * 业务员代号
     **/
    @Column(name = "BUSINESS_MAN")
    private String businessMan;

    /**
     * 人员
     **/
    @Column(name = "PERSON")
    private String person;

    /**
     * 备注
     **/
    @Column(name = "NOTE")
    private String note;

    /**
     * ERP创建时间
     **/
    @Column(name = "LAST_MODIFY_DATE")
    @JsonProperty("lastUpdateTime")
    private Date lastModifyDate;

    /**
     * 传输批次号
     **/
    @Column(name = "TEMP_OWNER")
    private String tempOwner;

    /**
     * 转换后保完税标志
     **/
    @Column(name = "BOND_MARK_CONVERT")
    private String bondMarkConvert;

    /**
     * 转换后进口方式
     **/
    @Column(name = "IN_WAY_CONVERT")
    private String inWayConvert;

    /**
     * 转换后币制
     **/
    @Column(name = "CURR_CONVERT")
    private String currConvert;

    /**
     * 企业代码
     **/
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 创建人
     **/
    @Column(name = "INSERT_USER")
    private String insertUser;

    /**
     * 创建时间
     **/
    @Column(name = "INSERT_TIME")
    private Date insertTime;

    /**
     * 更新人
     **/
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 更新时间
     **/
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 流水号
     **/
    @Column(name = "SERIAL_NO")
    private BigDecimal serialNo;

    /**
     * 数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）
     **/
    @Column(name = "MODIFY_MARK")
    private Integer modifyMark;

    /**
     * 推送状态，1-已推送 0-未推送	字符型	1
     */
    @Column(name = "PUSH_STATUS")
    private String pushStatus;

    /***
     * 数据状态 0 未提取，1 提取过
     */
    @Column(name = "STATUS")
    private String status;

    /***
     * 清洗标志
     */
    @Column(name = "IS_CLEAR")
    private String isClear="1";

    /**
     * 1 ERP
     **/
    @Column(name = "DATA_SOURCE")
    private String dataSource = "1";

    /**
     * 企业自定义物料类型
     **/
    @Column(name = "G_MARK")
    private String GMark;

    /**
     * 转换后物料类型
     **/
    @Column(name = "G_MARK_CONVERT")
    private String GMarkConvert;

    /**
     * 仓库编号
     **/
    @Column(name = "WAREHOUSE")
    private String warehouse;

    /**
     * 工厂代码
     **/
    @Column(name = "FACTORY")
    private String factory;

    @Transient
    private String pickUp;

    /**
     * 采购单价
     **/
    @Column(name = "PO_PRICE")
    private BigDecimal poPrice;

    /**
     * 原始物料
     **/
    @Column(name = "ORIGINAL_G_NO")
    private String originalGNo;

    /**
     * 英文名称
     **/
    @Column(name = "COP_G_NAME_EN")
    private String copGNameEn;

    /**
     * 提取单据号
     **/
    @Column(name = "BUSINESS_NO")
    private String businessNo;

    /**
     * 提单表体ID
     **/
    @Column(name = "LIST_ID")
    private String listId;

    /**
     * 分送清单编号
     **/
    @Column(name = "FS_LIST_NO")
    private String fsListNo;

    /**
     * 申请表编号
     **/
    @Column(name = "APPLY_NO")
    private String applyNo;

    /**
     * 转换后的交易单位
     **/
    @Column(name = "UNIT_CONVERT")
    private String unitConvert;

    /**
     * 供应商中文名称
     **/
    @Column(name = "SUPPLIER_NAME")
    private String supplierName;
    /**
     * 提取时间
     */
    @Column(name ="EXTRACT_TIME")
    private Date extractTime;

    /**
     * ERP最后变更日期-开始
     */
    @Transient
    private String lastModifyDateFrom;
    /**
     * ERP最后变更日期-结束
     */
    @Transient
    private String lastModifyDateTo;
    @Transient
    private String deliveryDateFrom;
    @Transient
    private String deliveryDateTo;
    @Transient
    private String insertTimeFrom;
    @Transient
    private String insertTimeTo;
    @Transient
    private String updateTimeFrom;
    @Transient
    private String updateTimeTo;
    @Transient
    private String cliType = ErpSapConstants.SUPPLIER_TYPE;
}
