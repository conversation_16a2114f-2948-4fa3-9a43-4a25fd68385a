package com.dcjet.cs.erp.service.CreateBills.CreateIBills.CreateBill;

import com.dcjet.cs.erp.model.DecErpIListN;
import com.dcjet.cs.erp.model.DecIBillList;
import com.dcjet.cs.erp.service.CreateBills.config.CreateBillsConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生成清单序号
 * 清单归并类型：不归并
 *
 * <AUTHOR>
 * @date: 2020-5-29
 */
@Component
public class CreateIBillByNone implements CreateIBillStrategy {
    @Resource
    private PublicCreateIBill publicCreateIBill;

    @Override
    public String billType() {
        return CreateBillsConfig.BILL_MERGE_TYPE_NONE;
    }

    @Override
    public List<DecIBillList> createBillSeqNo(List<DecErpIListN> decErpIListNS) {
        /** 「清单序号」= 「序号」 */
        decErpIListNS.stream().forEach(e -> e.setBillGNo(e.getSerialNo()));

        return publicCreateIBill.handle(decErpIListNS);
    }
}
