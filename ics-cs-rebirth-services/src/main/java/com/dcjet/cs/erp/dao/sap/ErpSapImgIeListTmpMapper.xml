<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.erp.dao.sap.ErpSapImgIeListTmpMapper">
    <resultMap id="erpSapImgIeListTmpMap" type="com.dcjet.cs.erp.model.sap.ErpImgIeListTmp">
        <result column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="BILL_NO" property="billNo" jdbcType="VARCHAR"/>
        <result column="LINE_NO" property="lineNo" jdbcType="DATE"/>
        <result column="WAREHOUSE_NO" property="warehouseNo" jdbcType="VARCHAR"/>
        <result column="LINKED_NO" property="linkedNo" jdbcType="VARCHAR"/>
        <result column="IE_TYPE" property="ieType" jdbcType="VARCHAR"/>
        <result column="PO_NO" property="poNo" jdbcType="VARCHAR"/>
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="DELIVERY_DATE" property="deliveryDate" jdbcType="VARCHAR"/>
        <result column="FAC_G_NO" property="facGNo" jdbcType="VARCHAR"/>
        <result column="BOND_MARK" property="bondMark" jdbcType="VARCHAR"/>
        <result column="G_MARK" property="GMark" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="NUMERIC"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="VARCHAR"/>
        <result column="IS_CLEAR" property="isClear" jdbcType="VARCHAR"/>
        <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR"/>
        <result column="TEMP_OWNER" property="tempOwner" jdbcType="VARCHAR"/>
        <result column="TEMP_MARK" property="tempOwner" jdbcType="VARCHAR"/>
        <result column="TEMP_REMARK" property="tempRemark" jdbcType="VARCHAR"/>
        <result column="TEMP_FLAG" property="tempFlag" jdbcType="VARCHAR"/>
        <result column="TEMP_INDEX" property="tempIndex" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="selectByFlag" resultMap="erpSapImgIeListTmpMap">
        select t.* from T_ERP_IMG_IE_LIST_TMP t
        <where>
            and t.TRADE_CODE = #{tradeCode}
            and t.TEMP_OWNER = #{tempOwner}
            AND t.TEMP_FLAG = #{tempFlag}
        </where>
        order by t.TEMP_INDEX ASC
    </select>
    <insert id="insertBatch">
        begin
            delete from T_ERP_IMG_IE_LIST where trade_code = #{tradeCode} and
            sid in (
                select t1.sid from T_ERP_IMG_IE_LIST t1
                inner join T_ERP_IMG_IE_LIST_TMP t2
                on t2.bill_no = t1.bill_no and t2.line_no = t1.line_no and t2.trade_code = t1.trade_code
                where t1.trade_code = #{tradeCode} and t2.temp_owner = #{tempOwner}
            );
            insert into T_ERP_IMG_IE_LIST
            (
                SID,
                BILL_NO,
                LINE_NO,
                WAREHOUSE_NO,
                LINKED_NO,
                IE_TYPE,
                PO_NO,
                INVOICE_NO,
                DELIVERY_DATE,
                FAC_G_NO,
                BOND_MARK,
                G_MARK,
                QTY,
                TRADE_CODE,
                INSERT_USER,
                INSERT_TIME,
                STATUS,
                IS_CLEAR,
                MODIFY_MARK,
                DATA_SOURCE,
                TEMP_OWNER
            )
            select
                SID,
                BILL_NO,
                LINE_NO,
                WAREHOUSE_NO,
                LINKED_NO,
                IE_TYPE,
                PO_NO,
                INVOICE_NO,
                DELIVERY_DATE_CONVERT,
                FAC_G_NO,
                BOND_MARK,
                G_MARK,
                QTY,
                TRADE_CODE,
                INSERT_USER,
                INSERT_TIME,
                STATUS,
                IS_CLEAR,
                MODIFY_MARK,
                DATA_SOURCE,
                TEMP_OWNER
            from T_ERP_IMG_IE_LIST_TMP where TRADE_CODE = #{tradeCode} and TEMP_OWNER = #{tempOwner};
            delete from T_ERP_IMG_IE_LIST_TMP where TRADE_CODE = #{tradeCode} and TEMP_OWNER = #{tempOwner};
        end;
    </insert>
    <insert id="insertBatch" databaseId="postgresql">
        delete from T_ERP_IMG_IE_LIST where trade_code = #{tradeCode} and
        sid in(
            select t1.sid from T_ERP_IMG_IE_LIST t1
            inner join T_ERP_IMG_IE_LIST_TMP t2
            on t2.bill_no = t1.bill_no and t2.line_no::numeric = t1.line_no and t2.trade_code = t1.trade_code
            where t1.trade_code = #{tradeCode} and t2.temp_owner = #{tempOwner}
        );
        insert into T_ERP_IMG_IE_LIST
        (
            SID,
            BILL_NO,
            LINE_NO,
            WAREHOUSE_NO,
            LINKED_NO,
            IE_TYPE,
            PO_NO,
            INVOICE_NO,
            DELIVERY_DATE,
            FAC_G_NO,
            BOND_MARK,
            G_MARK,
            QTY,
            TRADE_CODE,
            INSERT_USER,
            INSERT_TIME,
            STATUS,
            IS_CLEAR,
            MODIFY_MARK,
            DATA_SOURCE,
            TEMP_OWNER
        )
        select
            SID,
            BILL_NO,
            LINE_NO::numeric,
            WAREHOUSE_NO,
            LINKED_NO,
            IE_TYPE,
            PO_NO,
            INVOICE_NO,
            DELIVERY_DATE_CONVERT,
            FAC_G_NO,
            BOND_MARK,
            G_MARK,
            QTY::numeric,
            TRADE_CODE,
            INSERT_USER,
            INSERT_TIME,
            STATUS,
            IS_CLEAR,
            MODIFY_MARK,
            DATA_SOURCE,
            TEMP_OWNER
        from T_ERP_IMG_IE_LIST_TMP where TRADE_CODE = #{tradeCode} and TEMP_OWNER = #{tempOwner};
        delete from T_ERP_IMG_IE_LIST_TMP where TRADE_CODE = #{tradeCode} and TEMP_OWNER = #{tempOwner};
    </insert>
</mapper>