<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.erp.dao.DecErpEListNMapper">
    <resultMap id="decErpEListNResultMap" type="com.dcjet.cs.erp.model.DecErpEListN">
        <result column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="HEAD_ID" property="headId" jdbcType="VARCHAR"/>
        <result column="EMS_LIST_NO" property="emsListNo" jdbcType="VARCHAR"/>
        <result column="SERIAL_NO" property="serialNo" jdbcType="VARCHAR"/>
        <result column="G_NO" property="GNo" jdbcType="VARCHAR"/>
        <result column="COP_G_NO" property="copGNo" jdbcType="VARCHAR"/>
        <result column="CODE_T_S" property="codeTS" jdbcType="VARCHAR"/>
        <result column="G_NAME" property="GName" jdbcType="VARCHAR"/>
        <result column="G_MODEL" property="GModel" jdbcType="VARCHAR"/>
        <result column="UNIT" property="unit" jdbcType="VARCHAR"/>
        <result column="UNIT_1" property="unit1" jdbcType="VARCHAR"/>
        <result column="UNIT_2" property="unit2" jdbcType="VARCHAR"/>
        <result column="ORIGIN_COUNTRY" property="originCountry" jdbcType="VARCHAR"/>
        <result column="DESTINATION_COUNTRY" property="destinationCountry" jdbcType="VARCHAR"/>
        <result column="DEC_PRICE" property="decPrice" jdbcType="VARCHAR"/>
        <result column="DEC_TOTAL" property="decTotal" jdbcType="VARCHAR"/>
        <result column="USD_PRICE" property="usdPrice" jdbcType="VARCHAR"/>
        <result column="CURR" property="curr" jdbcType="VARCHAR"/>
        <result column="QTY_1" property="qty1" jdbcType="VARCHAR"/>
        <result column="QTY_2" property="qty2" jdbcType="VARCHAR"/>
        <result column="FACTOR_WT" property="factorWt" jdbcType="VARCHAR"/>
        <result column="FACTOR_1" property="factor1" jdbcType="VARCHAR"/>
        <result column="FACTOR_2" property="factor2" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="VARCHAR"/>
        <result column="GROSS_WT" property="grossWt" jdbcType="VARCHAR"/>
        <result column="NET_WT" property="netWt" jdbcType="VARCHAR"/>
        <result column="USE_TYPE" property="useType" jdbcType="VARCHAR"/>
        <result column="DUTY_MODE" property="dutyMode" jdbcType="VARCHAR"/>
        <result column="EXG_VERSION" property="exgVersion" jdbcType="VARCHAR"/>
        <result column="ENTRY_G_NO" property="entryGNo" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="COP_EMS_NO" property="copEmsNo" jdbcType="VARCHAR"/>
        <result column="CLASS_MARK" property="classMark" jdbcType="VARCHAR"/>
        <result column="ARRIVAL_DATE" property="arrivalDate" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="VARCHAR"/>
        <result column="BOND_MARK" property="bondMark" jdbcType="VARCHAR"/>
        <result column="G_MARK" property="GMark" jdbcType="VARCHAR"/>
        <result column="EMS_NO" property="emsNo" jdbcType="VARCHAR"/>
        <result column="COP_G_NAME" property="copGName" jdbcType="VARCHAR"/>
        <result column="COP_G_MODEL" property="copGModel" jdbcType="VARCHAR"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="VOLUME" property="volume" jdbcType="VARCHAR"/>
        <result column="SUPPLIER_CODE" property="supplierCode" jdbcType="VARCHAR"/>
        <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR"/>
        <result column="TRADE_MODE" property="tradeMode" jdbcType="VARCHAR"/>
        <result column="FAC_G_NO" property="facGNo" jdbcType="VARCHAR"/>
        <result column="LINKED_NO" property="linkedNo" jdbcType="VARCHAR"/>
        <result column="CUSTOMER_ORDER_NO" property="customerOrderNo" jdbcType="VARCHAR"/>
        <result column="CUSTOMER_G_NO" property="customerGNo" jdbcType="VARCHAR"/>
        <result column="WARNING_MARK" property="warningMark" jdbcType="VARCHAR"/>
        <result column="WARNING_MSG" property="warningMsg" jdbcType="VARCHAR"/>
        <result column="NOTE_1" property="note1" jdbcType="VARCHAR"/>
        <result column="NOTE_2" property="note2" jdbcType="VARCHAR"/>
        <result column="NOTE_3" property="note3" jdbcType="VARCHAR"/>
        <result column="DISTRICT_CODE" property="districtCode" jdbcType="VARCHAR"/>
        <result column="DISTRICT_POST_CODE" property="districtPostCode" jdbcType="VARCHAR"/>
        <result column="COST_CENTER" jdbcType="VARCHAR" property="costCenter"/>
        <result column="LINE_NO" jdbcType="VARCHAR" property="lineNo"/>
        <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="BUYER" property="buyer" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="BILL_G_NO" property="billGNo" jdbcType="VARCHAR"/>
        <result column="IN_OUT_NO" property="inOutNo" jdbcType="VARCHAR"/>
        <result column="CIQ_NO" property="ciqNo" jdbcType="VARCHAR"/>
        <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR"/>
        <result column="FS_LIST_NO" property="fsListNo" jdbcType="VARCHAR"/>
        <result column="SINGLE_WEIGHT" property="singleWeight" jdbcType="VARCHAR"/>
        <result column="CONFIRM_ROYALTIES" property="confirmRoyalties" jdbcType="VARCHAR"/>
        <result column="CONFIRM_ROYALTIES_NO" property="confirmRoyaltiesNo" jdbcType="VARCHAR"/>
        <result column="CUT_MODE" property="cutMode" jdbcType="VARCHAR"/>
        <result column="LABOR_COST" property="laborCost" jdbcType="VARCHAR" />
        <result column="MAT_COST" property="matCost" jdbcType="VARCHAR" />
        <result column="CLIENT_TOTAL" property="clientTotal" jdbcType="VARCHAR" />
        <result column="PAY_TOTAL" property="payTotal" jdbcType="VARCHAR" />
        <result column="CONTAINER_MD" property="containerMd" jdbcType="VARCHAR" />
        <result column="CLIENT_PRICE" property="clientPrice" jdbcType="VARCHAR" />
        <result column="ORDER_LINE_NO" property="orderLineNo" jdbcType="VARCHAR"/>
        <result column="PACK_NUM" property="packNum" jdbcType="NUMERIC"/>
        <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"/>
        <result column="ORDER_QTY" property="orderQty" jdbcType="NUMERIC"/>
        <result column="ORDER_PRICE" property="orderPrice" jdbcType="NUMERIC"/>
        <result column="LIST_CARTON_NUM" property="listCartonNum" jdbcType="NUMERIC"/>
        <result column="KEY_PRODUCT_MARK" property="keyProductMark" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        t.SID
        ,t.TRADE_CODE
        ,t.HEAD_ID
        ,t.EMS_LIST_NO
        ,t.SERIAL_NO
        ,t.G_NO
        ,t.COP_G_NO
        ,t.CODE_T_S
        ,t.G_NAME
        ,t.G_MODEL
        ,t.UNIT
        ,t.UNIT_1
        ,t.UNIT_2
        ,t.ORIGIN_COUNTRY
        ,t.DESTINATION_COUNTRY
        ,t.DEC_PRICE
        ,t.DEC_TOTAL
        ,t.USD_PRICE
        ,t.CURR
        ,t.QTY_1
        ,t.QTY_2
        ,t.FACTOR_WT
        ,t.FACTOR_1
        ,t.FACTOR_2
        ,t.QTY
        ,t.GROSS_WT
        ,t.NET_WT
        ,t.USE_TYPE
        ,t.DUTY_MODE
        ,t.EXG_VERSION
        ,t.ENTRY_G_NO
        ,t.NOTE
        ,t.COP_EMS_NO
        ,t.CLASS_MARK
        ,t.ARRIVAL_DATE
        ,t.INSERT_USER
        ,t.INSERT_TIME
        ,t.UPDATE_USER
        ,t.UPDATE_TIME
        ,t.BOND_MARK
        ,t.G_MARK
        ,t.EMS_NO
        ,t.COP_G_NAME
        ,t.COP_G_MODEL
        ,t.ORDER_NO
        ,t.INVOICE_NO
        ,t.VOLUME
        ,t.SUPPLIER_CODE
        ,t.SUPPLIER_NAME
        ,t.TRADE_MODE
        ,t.FAC_G_NO
        ,t.LINKED_NO
        ,t.CUSTOMER_ORDER_NO
        ,t.CUSTOMER_G_NO
        ,t.WARNING_MARK
        ,t.WARNING_MSG
        ,t.NOTE_1
        ,t.NOTE_2
        ,t.NOTE_3
        ,t.DISTRICT_CODE
        ,t.DISTRICT_POST_CODE
        ,t.COST_CENTER
        ,t.LINE_NO
        ,t.ITEM_NO
        ,t.STATUS
        ,t.BUYER
        ,t.INSERT_USER_NAME
        ,t.UPDATE_USER_NAME
        ,t.BILL_G_NO
        ,t.IN_OUT_NO
        ,t.CIQ_NO
        ,t.DATA_SOURCE
        ,t.FS_LIST_NO
        ,t.SINGLE_WEIGHT
        ,t.CONFIRM_ROYALTIES
        ,t.CONFIRM_ROYALTIES_NO
        ,t.CUT_MODE
        ,t.CONTAINER_MD
        ,t.LABOR_COST
        ,t.MAT_COST
        ,t.CLIENT_TOTAL
        ,t.PAY_TOTAL
        ,t.CLIENT_PRICE
        ,t.ORDER_LINE_NO
        ,t.PACK_NUM
        ,t.BATCH_NO
        ,t.ORDER_QTY
        ,t.ORDER_PRICE
        ,t.LIST_CARTON_NUM
        ,t.KEY_PRODUCT_MARK
    </sql>
    <sql id="condition">
        <if test="sid != null and sid != ''">
            and t.SID = #{sid}
        </if>
        <if test="dataSource != null and dataSource != ''">
            and t.DATA_SOURCE = #{dataSource}
        </if>
        <if test="headId != null and headId != ''">
            and t.HEAD_ID = #{headId}
        </if>
        <if test="headIds != null">
            and t.HEAD_ID in
            <foreach collection="headIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="emsListNo != null and emsListNo != ''">
            and EMS_LIST_NO = #{emsListNo}
        </if>
        <if test="serialNo != null and serialNo != ''">
            and t.SERIAL_NO = #{serialNo}
        </if>
        <if test="GNo != null and GNo != ''">
            and t.G_NO = #{GNo}
        </if>
        <if test="copGNo != null and copGNo != ''">
            and t.COP_G_NO like concat(concat('%',#{copGNo}),'%')
        </if>
        <if test="customerOrderNo != null and customerOrderNo != ''">
            and t.CUSTOMER_ORDER_NO like concat(concat('%',#{customerOrderNo}),'%')
        </if>
        <if test="codeTS != null and codeTS != ''">
            and t.CODE_T_S = #{codeTS}
        </if>
        <if test="GName != null and GName != ''">
            and t.G_NAME like concat(concat('%',#{GName}),'%')
        </if>
        <if test="GModel != null and GModel != ''">
            and t.G_MODEL like concat(concat('%',#{GModel}),'%')
        </if>
        <if test="unit != null and unit != ''">
            and t.UNIT = #{unit}
        </if>
        <if test="unit1 != null and unit1 != ''">
            and t.UNIT_1 = #{unit1}
        </if>
        <if test="unit2 != null and unit2 != ''">
            and t.UNIT_2 = #{unit2}
        </if>
        <if test="originCountry != null and originCountry != ''">
            and t.ORIGIN_COUNTRY = #{originCountry}
        </if>
        <if test="destinationCountry != null and destinationCountry != ''">
            and t.DESTINATION_COUNTRY = #{destinationCountry}
        </if>
        <if test="decPrice != null and decPrice != ''">
            and t.DEC_PRICE = #{decPrice}
        </if>
        <if test="decTotal != null and decTotal != ''">
            and t.DEC_TOTAL = #{decTotal}
        </if>
        <if test="usdPrice != null and usdPrice != ''">
            and t.USD_PRICE = #{usdPrice}
        </if>
        <if test="curr != null and curr != ''">
            and t.CURR = #{curr}
        </if>
        <if test="qty1 != null and qty1 != ''">
            and t.QTY_1 = #{qty1}
        </if>
        <if test="qty2 != null and qty2 != ''">
            and t.QTY_2 = #{qty2}
        </if>
        <if test="factorWt != null and factorWt != ''">
            and t.FACTOR_WT = #{factorWt}
        </if>
        <if test="factor1 != null and factor1 != ''">
            and t.FACTOR_1 = #{factor1}
        </if>
        <if test="factor2 != null and factor2 != ''">
            and t.FACTOR_2 = #{factor2}
        </if>
        <if test="qty != null and qty != ''">
            and t.QTY = #{qty}
        </if>
        <if test="grossWt != null and grossWt != ''">
            and t.GROSS_WT = #{grossWt}
        </if>
        <if test="netWt != null and netWt != ''">
            and t.NET_WT = #{netWt}
        </if>
        <if test="useType != null and useType != ''">
            and t.USE_TYPE = #{useType}
        </if>
        <if test="dutyMode != null and dutyMode != ''">
            and t.DUTY_MODE = #{dutyMode}
        </if>
        <if test="exgVersion != null and exgVersion != ''">
            and t.EXG_VERSION = #{exgVersion}
        </if>
        <if test="entryGNo != null and entryGNo != ''">
            and t.ENTRY_G_NO = #{entryGNo}
        </if>
        <if test="note != null and note != ''">
            and t.NOTE = #{note}
        </if>
        <if test="copEmsNo != null and copEmsNo != ''">
            and t.COP_EMS_NO = #{copEmsNo}
        </if>
        <if test="classMark != null and classMark != ''">
            and t.CLASS_MARK = #{classMark}
        </if>

        <if test="bondMark != null and bondMark != ''">
            and t.BOND_MARK = #{bondMark}
        </if>
        <if test="GMark != null and GMark != ''">
            and t.G_MARK = #{GMark}
        </if>
        <if test="emsNo != null and emsNo != ''">
            and t.EMS_NO = #{emsNo}
        </if>
        <if test="copGName != null and copGName != ''">
            and t.COP_G_NAME like concat(concat('%',#{copGName}),'%')
        </if>
        <if test="copGModel != null and copGModel != ''">
            and t.COP_G_MODEL like concat(concat('%',#{copGModel}),'%')
        </if>
        <if test="orderNo != null and orderNo != ''">
            and t.ORDER_NO like concat(concat('%',#{orderNo}),'%')
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
            and t.INVOICE_NO like concat(concat('%',#{invoiceNo}),'%')
        </if>
        <if test="volume != null and volume != ''">
            and t.VOLUME = #{volume}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            and t.SUPPLIER_CODE = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            and t.SUPPLIER_NAME = #{supplierName}
        </if>
        <if test="tradeMode != null and tradeMode != ''">
            and t.TRADE_MODE = #{tradeMode}
        </if>
        <if test="facGNo != null and facGNo != ''">
            and t.FAC_G_NO like concat(concat('%', #{facGNo}),'%')
        </if>
        <if test="warningMark != null and warningMark != ''">
            and t.WARNING_MARK in
            <foreach item="item" index="index" collection="warningMark.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="linkedNo != null and linkedNo != ''">
            and LINKED_NO like concat(concat('%', #{linkedNo}),'%')
        </if>

        <if test="buyer != null and buyer != ''">
            and t.BUYER  like concat(concat('%',#{buyer}),'%')
        </if>
        <if test="billGNo != null and billGNo != ''">
            and t.BILL_G_NO = #{billGNo}
        </if>
        <if test="inOutNo != null and inOutNo != ''">
            and t.IN_OUT_NO like concat(concat('%',#{inOutNo}),'%')
        </if>
        <if test="itemNo != null and itemNo != ''">
            and t.ITEM_NO like concat(concat('%',#{itemNo}),'%')
        </if>
        <if test="ciqNo != null and ciqNo != ''">
            and t.CIQ_NO = #{ciqNo}
        </if>
    </sql>
    <sql id="condition_pg">
        <if test="arrivalDateFrom != null and arrivalDateFrom != ''">
            <![CDATA[ and t.ARRIVAL_DATE >= to_timestamp(#{arrivalDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="arrivalDateTo != null and arrivalDateTo != ''">
            <![CDATA[ and t.ARRIVAL_DATE < to_timestamp(#{arrivalDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
    </sql>
    <sql id="condition_oracle">
        <if test="arrivalDateFrom != null and arrivalDateFrom != ''">
            <![CDATA[ and t.ARRIVAL_DATE >= to_date(#{arrivalDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="arrivalDateTo != null and arrivalDateTo != ''">
            <![CDATA[ and t.ARRIVAL_DATE < to_date(#{arrivalDateTo}, 'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="decErpEListNResultMap" parameterType="com.dcjet.cs.erp.model.DecErpEListN">
        SELECT
        <include refid="Base_Column_List"/>
        ,v.CREDENTIALS
        ,v.INSPMONITORCOND
        FROM
        T_DEC_ERP_E_LIST_N t
        left join t_mat_non_bonded v on t.fac_g_no = v.cop_g_no and t.g_mark = v.g_mark and t.code_t_s = v.code_t_s and t.trade_code = v.trade_code
        <where>
            <include refid="condition"></include>
            <if test='_databaseId == "postgresql" '>
                <include refid="condition_pg"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="condition_oracle"></include>
            </if>
        </where>
        ORDER BY t.EMS_LIST_NO, t.SERIAL_NO, t.invoice_no, t.INSERT_TIME, t.SID
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        DELETE FROM T_DEC_ERP_E_LIST_N t WHERE t.status != '1' AND t.SID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 发票模块存在新增的发票号则更新 原提单中表头、表体为空的发票号-->
    <update id="updateInvoiceNo">
		BEGIN
		  UPDATE T_DEC_ERP_E_HEAD_N T SET T.INVOICE_NO = #{invoiceNo} WHERE T.SID = #{headId} AND T.INVOICE_NO IS NULL;
		  UPDATE T_DEC_ERP_E_LIST_N T SET T.INVOICE_NO = #{invoiceNo} WHERE T.HEAD_ID = #{headId} AND T.INVOICE_NO IS NULL;
		END;
	</update>
    <!-- 复制时插入表体数据-->
    <insert id="insertListByHeadId" parameterType="map">
        INSERT INTO T_DEC_ERP_E_LIST_N (
        SID,
        HEAD_ID,
        EMS_LIST_NO,
        SERIAL_NO,
        G_NO,
        COP_G_NO,
        CODE_T_S,
        G_NAME,
        UNIT_1,
        UNIT_2,
        G_MODEL,
        UNIT,
        ORIGIN_COUNTRY,
        DESTINATION_COUNTRY,
        DEC_PRICE,
        DEC_TOTAL,
        USD_PRICE,
        CURR,
        QTY_1,
        QTY_2,
        FACTOR_WT,
        FACTOR_1,
        FACTOR_2,
        QTY,
        GROSS_WT,
        NET_WT,
        USE_TYPE,
        DUTY_MODE,
        EXG_VERSION,
        ENTRY_G_NO,
        NOTE,
        COP_EMS_NO,
        CLASS_MARK,
        ARRIVAL_DATE,
        INSERT_USER,
        INSERT_TIME,
        BOND_MARK,
        G_MARK,
        EMS_NO,
        COP_G_NAME,
        COP_G_MODEL,
        ORDER_NO,
        INVOICE_NO,
        VOLUME,
        SUPPLIER_CODE,
        SUPPLIER_NAME,
        TRADE_MODE,
        FAC_G_NO,
        LINKED_NO,
        NOTE_1,
        NOTE_2,
        NOTE_3,
        WARNING_MARK,
        WARNING_MSG,
        DISTRICT_CODE,
        DISTRICT_POST_CODE,
        COST_CENTER,
        LINE_NO,
        ITEM_NO,
        STATUS,
        BILL_G_NO,
        TRADE_CODE,
        DATA_SOURCE,
        FS_LIST_NO,
        SINGLE_WEIGHT,
        CONFIRM_ROYALTIES,
        CONFIRM_ROYALTIES_NO,
        BUYER,
        INSERT_USER_NAME,
        UPDATE_USER_NAME,
        IN_OUT_NO,
        CIQ_NO,
        APPLY_NO,
        CUSTOMER_ORDER_NO,
        CUSTOMER_G_NO,
        CUT_MODE,
        LABOR_COST,
        MAT_COST,
        CLIENT_TOTAL,
        PAY_TOTAL,
        CLIENT_PRICE,
        PACK_NUM,
        BATCH_NO,
        KEY_PRODUCT_MARK
        )
        select
        sys_guid(),
        #{sid,jdbcType=VARCHAR},
        #{emsListNo,jdbcType=VARCHAR},
        ROW_NUMBER() over (ORDER BY N.INSERT_TIME, N.SERIAL_NO, N.sid),
        N.G_NO,
        N.COP_G_NO,
        <if test="status=='0'.toString()">
            <choose>
                <when test="bondMark != null and bondMark != ''">
                    mat.CODE_T_S,
                    mat.G_NAME,
                    mat.UNIT_1,
                    mat.UNIT_2,
                </when>
                <otherwise>
                    N.CODE_T_S,
                    N.G_NAME,
                    N.UNIT_1,
                    N.UNIT_2,
                </otherwise>
            </choose>
        </if>
        <if test="status=='1'.toString()">
            N.CODE_T_S,
            N.G_NAME,
            N.UNIT_1,
            N.UNIT_2,
        </if>
        N.G_MODEL,
        N.UNIT,
        N.ORIGIN_COUNTRY,
        N.DESTINATION_COUNTRY,
        N.DEC_PRICE,
        N.DEC_TOTAL,
        N.USD_PRICE,
        N.CURR,
        N.QTY_1,
        N.QTY_2,
        N.FACTOR_WT,
        N.FACTOR_1,
        N.FACTOR_2,
        N.QTY,
        N.GROSS_WT,
        N.NET_WT,
        N.USE_TYPE,
        N.DUTY_MODE,
        N.EXG_VERSION,
        N.ENTRY_G_NO,
        N.NOTE,
        N.COP_EMS_NO,
        N.CLASS_MARK,
        N.ARRIVAL_DATE,
        #{insertUser,jdbcType=VARCHAR},
        #{insertTime},
        N.BOND_MARK,
        N.G_MARK,
        N.EMS_NO,
        N.COP_G_NAME,
        N.COP_G_MODEL,
        N.ORDER_NO,
        N.INVOICE_NO,
        N.VOLUME,
        N.SUPPLIER_CODE,
        N.SUPPLIER_NAME,
        N.TRADE_MODE,
        N.FAC_G_NO,
        N.LINKED_NO,
        N.NOTE_1,
        N.NOTE_2,
        N.NOTE_3,
        N.WARNING_MARK,
        N.WARNING_MSG,
        N.DISTRICT_CODE,
        N.DISTRICT_POST_CODE,
        N.COST_CENTER,
        N.LINE_NO,
        N.ITEM_NO,
        '0',
        N.BILL_G_NO,
        N.TRADE_CODE,
        '1',
        N.FS_LIST_NO,
        N.SINGLE_WEIGHT,
        N.CONFIRM_ROYALTIES,
        N.CONFIRM_ROYALTIES_NO,
        N.BUYER,
        N.INSERT_USER_NAME,
        N.UPDATE_USER_NAME,
        N.IN_OUT_NO,
        N.CIQ_NO,
        N.APPLY_NO,
        N.CUSTOMER_ORDER_NO,
        N.CUSTOMER_G_NO,
        N.CUT_MODE,
        N.LABOR_COST,
        N.MAT_COST,
        N.CLIENT_TOTAL,
        N.PAY_TOTAL,
        N.CLIENT_PRICE,
        N.PACK_NUM,
        N.BATCH_NO,
        N.KEY_PRODUCT_MARK
        from T_DEC_ERP_E_LIST_N N
        <if test="status=='0'.toString()">
            <if test="bondMark=='0'.toString()">
                left join v_mat_imgexg mat
                on mat.fac_g_no = N.fac_g_no
                and mat.g_mark = n.g_mark
                and mat.trade_code = N.trade_code
                and mat.status = '0'
                and mat.ems_no = N.ems_no
                and mat.cop_g_no = N.cop_g_no
            </if>
            <if test="bondMark=='1'.toString()">
                left join v_mat_non_bonded mat
                on mat.g_mark = n.g_mark
                and mat.trade_code = N.trade_code
                and mat.cop_g_no = N.cop_g_no
                and mat.status = '0'
                and mat.appr_status = '8'
            </if>
        </if>
        <where>
            and N.HEAD_ID = #{headId,jdbcType=VARCHAR}
        </where>
        order by N.INSERT_TIME, N.SERIAL_NO, N.SID
    </insert>
    <!-- 获取表体明细汇总信息-->
    <select id="getListTotal" resultType="string" parameterType="com.dcjet.cs.erp.model.DecErpIListN">
        select '申报数量:'||to_char(sum(f_xdo_nvl( t.QTY, 0.0 )),'fm999999999999999990.09999')||' 申报总价:'||to_char(sum(f_xdo_nvl( t.DEC_TOTAL,
        0.0 )),'fm999999999999999990.09999')||' 法一数量:'||to_char(sum(f_xdo_nvl( t.QTY_1, 0.0 )),'fm999999999999999990.09999')||'
        法二数量:'||to_char(sum(f_xdo_nvl( t.QTY_2, 0.0 )),'fm999999999999999990.09999')||' 净重:'||to_char(sum(f_xdo_nvl( t.net_wt, 0.0
        )),'fm999999999999999990.09999999')||' 毛重:'||to_char(sum(f_xdo_nvl( t.gross_wt, 0.0 )),'fm999999999999999990.09999999')  as total from T_DEC_ERP_E_LIST_N t
        <where>
            <include refid="condition"></include>
            <if test='_databaseId == "postgresql" '>
                <include refid="condition_pg"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="condition_oracle"></include>
            </if>
        </where>
    </select>
    <!-- 根据表头id获取表体数据量 -->
    <select id="getBodyCount" resultType="integer">
        SELECT COUNT(1) FROM T_DEC_ERP_E_LIST_N T
        <where>
            <include refid="condition"></include>
            <if test='_databaseId == "postgresql" '>
                <include refid="condition_pg"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="condition_oracle"></include>
            </if>
        </where>
    </select>
    <!-- 获取未提取过的ERP信息-->
    <select id="getLinkedNo" resultType="string">
        select distinct LINKED_NO as linkedNo from T_ERP_DEC_E_LIST T
        <where>
            and STATUS='0'
            and TRADE_CODE = #{tradeCode}
        </where>
    </select>
    <!-- 提取ERP数据(数据提取)-->
    <select id="checkLinkedNo" parameterMap="checkLinkedNoMap" statementType="CALLABLE" resultType="java.util.Map">
      CALL P_CHECK_ERP_DEC_E(?,?,?,?,?)
     </select>
    <parameterMap type="java.util.Map" id="checkLinkedNoMap">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_HEAD_ID" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_LOCK_MARK" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="INOUT" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="INOUT" jdbcType="VARCHAR"/>
    </parameterMap>

    <!-- 提取ERP数据(数据提取)-->
    <select id="checkLinkedNo" parameterMap="checkLinkedNoMap2" statementType="CALLABLE" resultType="java.util.Map" databaseId="postgresql">
      CALL P_CHECK_ERP_DEC_E(?,?,?,?,?)
     </select>
    <parameterMap type="java.util.Map" id="checkLinkedNoMap2">
        <parameter property="P_TEMP_OWNER" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_HEAD_ID" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_LOCK_MARK" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_CODE" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="P_RET_STR" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>
    <!-- 获取最大明细序号-->
    <select id="getMaxSerialNo" resultType="integer">
        select f_xdo_nvl(cast(Max(T.SERIAL_NO) as integer ) ,0) from T_DEC_ERP_E_LIST_N T
        <where>
            <if test="headId != null and headId != ''">
                and t.HEAD_ID = #{headId}
            </if>
        </where>
    </select>
    <update id="updateInoviceNo">
        UPDATE T_DEC_ERP_E_HEAD_N
        set
        GROSS_WT=(select round(sum( tmp.GROSS_WT ), 5)
        FROM T_DEC_ERP_E_LIST_N tmp
        WHERE tmp.HEAD_ID=#{sid}),
        NET_WT=(select round(sum( tmp.NET_WT ), 5)
        FROM T_DEC_ERP_E_LIST_N tmp
        WHERE tmp.HEAD_ID=#{sid})
        where SID=#{sid}
    </update>
    <update id="updateInoviceNo" databaseId="postgresql">
        UPDATE T_DEC_ERP_E_HEAD_N
        set
        GROSS_WT=(select COALESCE(round(sum( tmp.GROSS_WT ), 5),0)
        FROM T_DEC_ERP_E_LIST_N tmp
        WHERE tmp.HEAD_ID=#{sid}),
        NET_WT=(select COALESCE(round(sum( tmp.NET_WT ), 5),0)
        FROM T_DEC_ERP_E_LIST_N tmp
        WHERE tmp.HEAD_ID=#{sid})
        where SID=#{sid}
    </update>

    <select id="getNetWtSummary" resultType="com.dcjet.cs.erp.model.DecErpEListN">
        SELECT nvl( sum( t.QTY ), 0 ) AS qty,
        nvl(round( sum( t.NET_WT ), #{autoNetWtDigit}), 0 ) AS netWt,
        nvl(round( sum( t.GROSS_WT ), #{autoGrossWtDigit}), 0 ) AS grossWt FROM T_DEC_ERP_E_LIST_N t where t.HEAD_ID = #{headId}
    </select>
    <select id="getNetWtSummary" resultType="com.dcjet.cs.erp.model.DecErpEListN" databaseId="postgresql">
        SELECT f_xdo_nvl(cast(sum( t.QTY ) as numeric), 0.0 ) AS qty,
        f_xdo_nvl(round(cast( sum( t.NET_WT ) as numeric), #{autoNetWtDigit}::int), 0.0 ) AS netWt,
        f_xdo_nvl(round(cast( sum( t.GROSS_WT ) as numeric), #{autoGrossWtDigit}::int),0.0) AS grossWt FROM T_DEC_ERP_E_LIST_N t where t.HEAD_ID = #{headId}
    </select>

    <select id="selectConfirmRoyalties" resultType="java.lang.Integer">
        select count(1) from T_DEC_ERP_E_List_N
        <where>
            HEAD_ID=#{headId}
            and confirm_Royalties='1'
        </where>
    </select>
    <select id="selectByGNo" resultType="com.dcjet.cs.erp.model.DecErpEListN">
        SELECT
        <if test='_databaseId == "postgresql" '>
            SUM(QTY) AS QTY,
            SUM(NET_WT) AS NET_WT,
            SUM(GROSS_WT) AS GROSS_WT,
        </if>
        <if test='_databaseId != "postgresql" '>
            SUM(QTY) AS QTY,
            SUM(NET_WT) AS NET_WT,
            SUM(GROSS_WT) AS GROSS_WT,
        </if>
            FAC_G_NO
        FROM T_DEC_ERP_E_LIST_N WHERE HEAD_ID = #{HEADID} GROUP BY FAC_G_NO
    </select>

    <select id="selectList" resultType="com.dcjet.cs.erp.model.DecErpEListN">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM
        T_DEC_ERP_E_LIST_N t
        <where>
            and t.SERIAL_NO = #{serialNo}
            and t.HEAD_ID = #{headId}
            and t.FAC_G_NO = #{facGNo}
        </where>
    </select>
    <select id="getCount" resultType="integer">
        SELECT count(*)FROM	t_bi_client_information t WHERE t.CUSTOMER_TYPE = 'CLI' AND t.TRADE_CODE = #{tradeCode} AND t.CUSTOMER_CODE = #{customerCode}
    </select>

    <select id="getDataList" resultType="com.dcjet.cs.erp.model.DecErpEListN"
            parameterType="com.dcjet.cs.erp.model.DecErpEListN">
        SELECT
            <include refid="Base_Column_List"/>
            <if test='_databaseId == "postgresql" '>
                ,COALESCE(p.custom_param_code,t.BOND_MARK) as BOND_CUSTOM_CODE
            </if>
            <if test='_databaseId != "postgresql" '>
                ,NVL(p.custom_param_code,t.BOND_MARK) as BOND_CUSTOM_CODE
            </if>
        FROM T_DEC_ERP_E_LIST_N t
        LEFT JOIN T_DEC_E_BILL_HEAD B ON t.BILL_HEAD_ID = B.SID
        INNER JOIN T_DEC_E_ENTRY_HEAD a ON a.bill_head_id = B.sid and a.entry_status = '1'
        LEFT JOIN t_bi_customer_params P ON t.trade_code = p.trade_code and p.params_type = 'BONDMARK' and
        t.bond_mark = p.params_code

        WHERE t.TRADE_CODE = #{tradeCode}
        <if test="facGNo != null and facGNo != ''">
            and t.FAC_G_NO like concat(concat('%', #{facGNo}),'%')
        </if>
        <if test="GMark != null and GMark != ''">
            and t.G_MARK = #{GMark}
        </if>
        <if test="tradeMode != null and tradeMode != ''">
            and t.TRADE_MODE = #{tradeMode}
        </if>
        <if test='_databaseId == "postgresql" '>
            <if test="declareDateFrom != null and declareDateFrom != ''">
                <![CDATA[ and B.DECLARE_DATE >= to_timestamp(#{declareDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="declareDateTo != null and declareDateTo != ''">
                <![CDATA[ and B.DECLARE_DATE < to_timestamp(#{declareDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
            </if>
        </if>
        <if test='_databaseId != "postgresql" '>
            <if test="declareDateFrom != null and declareDateFrom != ''">
                <![CDATA[ and B.DECLARE_DATE > to_date(#{declareDateFrom},'yyyy-MM-dd hh24:mi:ss') ]]>
            </if>
            <if test="declareDateTo != null and declareDateTo != ''">
                <![CDATA[ and B.DECLARE_DATE <= to_date(#{declareDateTo},'yyyy-MM-dd hh24:mi:ss') +1 ]]>
            </if>
        </if>

        <if test="entryNo != null and entryNo!=''">
            and B.ENTRY_NO like concat(concat('%',#{entryNo}),'%')
        </if>
        <if test="listNo != null and listNo != ''">
            and B.LIST_NO = #{listNo}
        </if>
        <if test="emsNo != null and emsNo != ''">
            and B.EMS_NO = #{emsNo}
        </if>
        order by t.sid
    </select>

    <select id="selectBySids" resultType="com.dcjet.cs.erp.model.DecErpEListN" parameterType="java.util.List">
        SELECT
        <include refid="Base_Column_List"/>
        <if test='_databaseId == "postgresql" '>
            ,COALESCE(p.custom_param_code,t.BOND_MARK) as BOND_CUSTOM_CODE
        </if>
        <if test='_databaseId != "postgresql" '>
            ,NVL(p.custom_param_code,t.BOND_MARK) as BOND_CUSTOM_CODE
        </if>
        ,B.ENTRY_NO
        FROM T_DEC_ERP_E_LIST_N t
        LEFT JOIN T_DEC_E_BILL_HEAD B ON t.BILL_HEAD_ID = B.SID
        INNER JOIN T_DEC_E_ENTRY_HEAD a ON a.bill_head_id = B.sid and a.entry_status = '1'
        LEFT JOIN t_bi_customer_params P ON t.trade_code = p.trade_code and p.params_type = 'BONDMARK' and
        t.bond_mark = p.params_code
        WHERE t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by t.LINKED_NO, t.LINE_NO,t.sid
    </select>

    <update id="updateBySid" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update T_DEC_ERP_E_LIST_N
            <set>
                ENTRY_NO = #{item.entryNo}
            </set>
            where SID = #{item.sid}
        </foreach>
    </update>

    <delete id="deleteOld" parameterType="java.lang.String">
        <if test='_databaseId != "postgresql" '>
            begin
        </if>
        DELETE
        FROM T_DEC_E_ENTRY_LIST T
        WHERE EXISTS(SELECT 1
                     FROM T_DEC_E_ENTRY_HEAD H
                     WHERE H.SID = T.HEAD_ID
                       AND H.ERP_HEAD_ID = #{headId}
                       and H.STATUS = '0');
        DELETE FROM T_DEC_E_ENTRY_HEAD H WHERE H.ERP_HEAD_ID = #{headId} and H.STATUS = '0';
        --删除清单数据
        DELETE
        FROM T_DEC_E_BILL_LIST T
        WHERE EXISTS(SELECT 1
                     FROM T_DEC_E_BILL_HEAD H
                     WHERE H.SID = T.HEAD_ID
                       AND H.HEAD_ID = #{headId}
                       and H.STATUS = '0');
        DELETE FROM T_DEC_E_BILL_HEAD H WHERE H.HEAD_ID = #{headId} and H.STATUS = '0';
        --删除报关单追踪数据
        DELETE FROM T_DEC_E_CUSTOMS_TRACK H WHERE H.ERP_HEAD_ID = #{headId} and H.STATUS = '0';
        <if test='_databaseId != "postgresql" '>
            END;
        </if>
    </delete>
    <select id="getListByHeadId" parameterType="string" resultType="string">
		SELECT distinct t.FS_LIST_NO
		  FROM T_DEC_ERP_E_LIST_N t
		 WHERE t.HEAD_ID = #{headId}
	</select>
    <select id="selectTotalByHeadId" resultType="com.dcjet.cs.erp.model.DecErpEListN" parameterType="java.lang.String">
        SELECT
        <if test='_databaseId == "postgresql" '>
            SUM(COALESCE(QTY, 0)) AS QTY,
            SUM(COALESCE(NET_WT, 0)) AS NET_WT,
            SUM(COALESCE(GROSS_WT, 0)) AS GROSS_WT
        </if>
        <if test='_databaseId != "postgresql" '>
            SUM(NVL(QTY, 0)) AS QTY,
            SUM(NVL(NET_WT, 0)) AS NET_WT,
            SUM(NVL(GROSS_WT, 0)) AS GROSS_WT
        </if>
        FROM T_DEC_ERP_E_LIST_N
       WHERE HEAD_ID = #{headId}
    </select>
    <select id="getCurrListByInvoiceNo" resultType="java.lang.String">
        SELECT CURR FROM T_DEC_ERP_E_LIST_N
         WHERE INVOICE_NO = #{invoiceNo} AND TRADE_CODE = #{tradeCode}
      GROUP BY CURR
    </select>

    <select id="getVolume" resultType="com.dcjet.cs.erp.model.DecErpEListN" parameterType="java.lang.String">
        SELECT nvl(SUM(VOLUME), 0) AS VOLUME FROM T_DEC_ERP_E_LIST_N WHERE HEAD_ID = #{headId}
    </select>
    <select id="getVolume" resultType="com.dcjet.cs.erp.model.DecErpEListN" parameterType="java.lang.String" databaseId="postgresql">
        SELECT coalesce(SUM(VOLUME), 0) AS VOLUME FROM T_DEC_ERP_E_LIST_N WHERE HEAD_ID = #{headId}
    </select>
    <update id="batchUpdateBillHeadId" parameterType="java.util.List">
        update T_DEC_ERP_E_LIST_N
           set BILL_HEAD_ID =
        <foreach collection="list" item="item" index="index"
                 separator=" " open="case SID" close="end">
            when #{item.sid} then #{item.billHeadId}
        </foreach>
        , ENTRY_G_NO =
        <foreach collection="list" item="item" index="index"
                 separator=" " open="case SID" close="end">
            when #{item.sid} then #{item.entryGNo}
        </foreach>
        where sid in
        <foreach collection="list" index="index" item="item"
                 separator="," open="(" close=")">
            #{item.sid}
        </foreach>
    </update>
    <select id="selectInoviceNo" resultType="map" parameterType="map"  databaseId="postgresql">
        SELECT count(distinct INVOICE_NO)::text AS "INVOICE_NO_COUNT",
               count(distinct order_no)::text AS "CONTR_NO_COUNT",
            (select first_value (INVOICE_NO) over (order by serial_no) FROM T_DEC_ERP_E_LIST_N where HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode} and INVOICE_NO is not null LIMIT 1) AS "FIRST_INVOICE_NO",
            (select SUBSTR(first_value (order_no) over (order by serial_no), 1, 32) FROM T_DEC_ERP_E_LIST_N where HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode} and order_no is not null LIMIT 1) AS "FIRST_CONTR_NO",
            (SELECT SUBSTR(string_agg(INVOICE_NO, #{invoiceNoSplit}), 1, 100) FROM (select INVOICE_NO from (select distinct on (INVOICE_NO) * from T_DEC_ERP_E_LIST_N WHERE HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode} and INVOICE_NO is not null) M order by serial_no) A) AS "ALL_INVOICE_NO",
            (SELECT SUBSTR(string_agg(ORDER_NO, #{contrNoSplit}), 1, 32) FROM (select ORDER_NO from (select distinct on (ORDER_NO) * from T_DEC_ERP_E_LIST_N WHERE HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode} and order_no is not null) M order by serial_no) A) AS "ALL_CONTR_NO"
        FROM T_DEC_ERP_E_LIST_N where HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode}
    </select>
    <select id="selectInoviceNo" resultType="map" parameterType="map">
        SELECT
            (select count(distinct INVOICE_NO) || '' FROM T_DEC_ERP_E_LIST_N WHERE HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode}) AS "INVOICE_NO_COUNT",
            (select count(distinct order_no) || '' FROM T_DEC_ERP_E_LIST_N WHERE HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode}) AS "CONTR_NO_COUNT",
            (select FIRST_INVOICE_NO from (select first_value (INVOICE_NO) over (order by serial_no) FIRST_INVOICE_NO FROM T_DEC_ERP_E_LIST_N where HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode} and INVOICE_NO is not null) where ROWNUM = 1) AS "FIRST_INVOICE_NO",
            (select FIRST_CONTR_NO from (select SUBSTR(first_value (order_no) over (order by serial_no), 1, 32) FIRST_CONTR_NO FROM T_DEC_ERP_E_LIST_N where HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode} and order_no is not null) where ROWNUM = 1) AS "FIRST_CONTR_NO",
            (SELECT SUBSTR(LISTAGG(INVOICE_NO, #{invoiceNoSplit}) WITHIN GROUP (ORDER BY null), 1, 100) FROM (select distinct INVOICE_NO from T_DEC_ERP_E_LIST_N WHERE HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode} ORDER BY serial_no)) AS "ALL_INVOICE_NO",
            (SELECT SUBSTR(LISTAGG(order_no, #{contrNoSplit}) WITHIN GROUP (ORDER BY null), 1, 32) FROM (select distinct order_no from T_DEC_ERP_E_LIST_N WHERE HEAD_ID = #{headId} AND TRADE_CODE = #{tradeCode} ORDER BY serial_no)) AS "ALL_CONTR_NO"
        FROM dual
    </select>

    <select id="getAutoSumInfo" resultType="com.dcjet.cs.dto.erp.DecErpEListESumInfo">
        select
               sum(mat_cost) mat_total,
               sum(client_total) client_total,
               sum(LABOR_COST) dec_total_process
         from t_dec_erp_e_list_n
        where head_id = #{headId}
    </select>
    <select id="selectPackNum" resultType="java.math.BigDecimal">
        SELECT SUM(pack_num) pack_num FROM t_dec_erp_e_list_n WHERE head_id = #{headId}
    </select>

    <update id="updatePackNum">
        UPDATE t_dec_erp_E_head_n
           SET pack_num = #{packNum}
         WHERE sid = #{headId}
    </update>
    <update id="updateDistrictCodeBysid">
        update t_dec_erp_e_list_n a
        set district_code = #{districtCode}
        where a.head_id = #{sid} and not exists(
            select 1 from t_dec_erp_e_list_n b where (b.district_code is not null or b.district_code != '') and b.head_id = a.head_id
        )
    </update>
    <update id="updateDistrictPostCodeBysid">
        update t_dec_erp_e_list_n a
        set district_post_code = #{districtPostCode}
        where a.head_id = #{sid} and not exists(
            select 1 from t_dec_erp_e_list_n b where (b.district_post_code is not null or b.district_post_code != '') and b.head_id = a.head_id
        )
    </update>

    <select id="getListAll" resultType="com.dcjet.cs.erp.model.DecErpEListN">
        select * from t_dec_erp_e_list_n where head_id = #{headId} and trade_code = #{tradeCode}
    </select>
    <select id="getlistBillFlag" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_gwstd_ems_head
        WHERE bill_flag = 'H200'
          AND cop_ems_no = #{emsListNo}
          AND ems_no = #{emsNo}
          AND trade_code = #{tradeCode}
    </select>

    <select id="selectListAll" resultType="com.dcjet.cs.erp.model.DecErpEListN">
        SELECT T.*
        FROM t_dec_erp_e_list_n T
        WHERE T.head_id = #{headId}
          and T.BOND_MARK = '0'
    </select>

    <select id="getPacKing" resultType="java.lang.Integer">
        select count(*)
        from t_gw_packing_maintain
        where status !='1' and HEAD_ID = #{headId}
          AND trade_code = #{tradeCode}
    </select>

    <select id="getcurr" resultType="java.lang.String">
        select distinct curr
        from t_dec_erp_e_list_n
        where head_id = #{headId}
          AND trade_code = #{tradeCode}
    </select>
    <select id="getCurrListAll" resultType="com.dcjet.cs.erp.model.DecErpEListN">
        select distinct curr, sum(dec_total) as dec_total
        from t_dec_erp_e_list_n
        where head_id = #{headId}
          and trade_code = #{tradeCode}
        group by curr
    </select>
    <select id="selectSumAll" resultType="com.dcjet.cs.erp.model.DecErpEListN">
        select sum(l.QTY)       QTY_ALL,
               sum(l.DEC_TOTAL) TOTAL_ALL,
               min(l.G_NAME)    G_NAME_ALL
        FROM T_DEC_ERP_E_LIST_N l
        where L.head_id = #{headId}
    </select>

    <select id="selectListHeadIdAll" resultType="com.dcjet.cs.erp.model.DecErpEListN">
        SELECT T.*
        FROM t_dec_erp_e_list_n T
        WHERE T.head_id = #{headId}
          and T.trade_code = #{tradeCode};
    </select>

    <delete id="deleteByHeadId">
        delete from t_dec_erp_e_list_n where head_id = #{headId} and trade_code = #{tradeCode};
    </delete>

</mapper>
