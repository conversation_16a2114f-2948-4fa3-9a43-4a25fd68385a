package com.dcjet.cs.erp.service.mqsync;

import com.dcjet.cs.erp.dao.sap.ErpSapExgIeListMapper;
import com.dcjet.cs.erp.mapper.sap.ErpMqSyncDtoMapper;
import com.dcjet.cs.erp.model.sap.ErpExgIeList;
import com.dcjet.cs.erp.model.sap.ErpSyncMsg;
import com.dcjet.cs.mqsync.core.service.BaseMqSyncService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xdo.common.json.JsonObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class ErpExgIeList4MqService extends BaseMqSyncService<ErpSyncMsg> {
    @Resource
    ErpSapExgIeListMapper erpSapExgIeListMapper;
    @Resource
    ErpMqSyncDtoMapper erpMqSyncDtoMapper;

    @Override
    public void consume(String json) {
        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        ErpSyncMsg<ErpExgIeList, Object, Object> message = jsonObjectMapper.fromJson(json, new TypeReference<ErpSyncMsg<ErpExgIeList, Object, Object>>() {});
        String syncBatchNo = message.getBasicData().getTempBatchNo();
        List<ErpExgIeList> dataList = message.getDataList();
        log.info("ErpExgIeList入库开始");
        String updateUser = "syncService";
        Date now = new Date();
        List<ErpExgIeList> toInsertList = new ArrayList<>();
        List<ErpExgIeList> toUpdateList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(data -> {
                data.setSyncBatchNo(syncBatchNo);
                data.setUpdateTime(now);
                data.setUpdateUser(updateUser);
                fixConvertFields(data);
                data.setGMark(CommonEnum.GMarkEnum.EXG.getCode());//成品出入库赋默认值：E
                ErpExgIeList oldData = erpSapExgIeListMapper.getByIdentifier(data.getBillNo(), data.getLineNo(), data.getTradeCode());
                if (oldData == null) {
                    data.setInsertTime(now);
                    data.setSid(UUID.randomUUID().toString());
                    data.setStatus(ConstantsStatus.STATUS_0);
                    toInsertList.add(data);
                } else {
                    erpMqSyncDtoMapper.overwrite(data, oldData);
                    //kafka数据状态不为 2 的，状态则为 1 （修改）
                    if (2 != oldData.getModifyMark()) {
                        oldData.setModifyMark(1);
                    }
                    toUpdateList.add(oldData);
                }
            });
            if (toInsertList.size() > 0) {
                BulkSqlOpt.batchInsertForErp(toInsertList, ErpSapExgIeListMapper.class);
            }
            if (toUpdateList.size() > 0) {
                BulkSqlOpt.batchUpdateBySidAndTradeCodeForErp(toUpdateList, ErpSapExgIeListMapper.class);
            }
        }
        log.info("ErpExgIeList入库结束");
    }

    private void fixConvertFields(ErpExgIeList me) {
        if (StringUtils.isEmpty(me.getBondMarkConvert())) {
            me.setBondMarkConvert(me.getBondMark());
        }
        if (StringUtils.isEmpty(me.getOutWayConvert())) {
            me.setOutWayConvert(me.getOutWay());
        }
        if (StringUtils.isEmpty(me.getCurrConvert())) {
            me.setCurrConvert(me.getCurr());
        }
    }
}
