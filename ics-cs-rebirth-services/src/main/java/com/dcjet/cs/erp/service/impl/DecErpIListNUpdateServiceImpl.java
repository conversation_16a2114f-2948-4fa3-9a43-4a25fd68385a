package com.dcjet.cs.erp.service.impl;

import com.dcjet.cs.base.service.impl.BasicServiceImpl;
import com.dcjet.cs.bi.service.GwstdRoyaltyFeeService;
import com.dcjet.cs.common.model.ImportData;
import com.dcjet.cs.common.model.ImportValidation;
import com.dcjet.cs.dto.bi.GwstdRoyaltyFeeParam;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.dto.erp.DecErpIListNDto;
import com.dcjet.cs.dto.erp.DecErpIListNParam;
import com.dcjet.cs.dto.erp.DecErpIListNUpdateParam;
import com.dcjet.cs.dto.mat.MatIeInfoDto;
import com.dcjet.cs.dto.war.WarringPassConfigDto;
import com.dcjet.cs.dto.war.WarringPassExpandConfigDto;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIListNMapper;
import com.dcjet.cs.erp.dao.DecErpIListNUpdateMapper;
import com.dcjet.cs.erp.mapper.DecErpIListNUpdateDtoMapper;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.erp.model.DecErpIListN;
import com.dcjet.cs.erp.service.DecCommonService;
import com.dcjet.cs.erp.service.DecErpIHeadNService;
import com.dcjet.cs.erp.service.DecErpIListNImportService;
import com.dcjet.cs.erp.service.DecErpIListNService;
import com.dcjet.cs.mat.dao.MatImgexgOrgMapper;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.dcjet.cs.war.service.WarringPassConfigService;
import com.dcjet.cs.war.service.WarringPassExpandConfigService;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
public class DecErpIListNUpdateServiceImpl
        extends BasicServiceImpl<DecErpIListNUpdateMapper, DecErpIListN, DecErpIListNDto, DecErpIListNParam, DecErpIListNUpdateParam, DecErpIListNUpdateDtoMapper>
        implements DecErpIListNImportService {
    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private DecErpIListNMapper decErpIListNMapper;
    @Resource
    private DecErpIListNUpdateDtoMapper decErpIListNUpdateDtoMapper;
    @Resource
    private WarringPassConfigService warringPassConfigService;
    @Resource
    private DecErpIListNService decErpIListNService;
    @Resource
    private MatImgexgOrgMapper matImgexgOrgMapper;
    @Resource
    private DecCommonService decCommonService;
    @Resource
    private DecErpIHeadNService decErpIHeadNService;
    @Resource
    private GwstdRoyaltyFeeService gwstdRoyaltyFeeService;
    @Resource
    private WarringPassExpandConfigService warringPassExpandConfigService;

    @Override
    public ImportValidation<DecErpIListNUpdateParam> importValidation(ImportData<DecErpIListNUpdateParam> importData, ExcelImportParam param, UserInfoToken token) {
        List<DecErpIListNUpdateParam> correctList = importData.getCorrectData();
        Map<String, Object> bizParam = importData.getBizParam();
        List<DecErpIListNUpdateParam> corrects = new ArrayList<>(correctList.size());
        String headId = Optional.ofNullable(bizParam.get("headId")).orElseThrow(() -> new ArgumentException(xdoi18n.XdoI18nUtil.t("headId不能为空"))).toString();
        List<DecErpIListNUpdateParam> errors = new ArrayList<>(correctList.size());
        for (DecErpIListNUpdateParam val : correctList) {
            DecErpIListN decErpIListN = decErpIListNUpdateDtoMapper.toPo(val);
            decErpIListN.setHeadId(headId);
            decErpIListN.setSerialNo(val.getSerialNo());
            DecErpIListN decErpIListNS = decErpIListNMapper.selectList(decErpIListN);
            if (decErpIListNS != null) {
                DecErpIHeadN decErpIHeadN = decErpIHeadNMapper.selectByPrimaryKey(headId);
                if (!Arrays.asList(DecVariable.DEL_STATUS_IE).contains(decErpIHeadN.getApprStatus())) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("只有 “暂存”、“清单生成”、“内审退回”、“清单撤回” 状态数据可表体导入!"));
                }
                val.setHeadId(headId);
                val.setSid(decErpIListNS.getSid());
                checkData(val, token, importData.getHeadFieldMap());
            } else {
                val.setErrMsg(xdoi18n.XdoI18nUtil.t("未找到对应数据，无法操作"));
            }
            if (val.getErrMsg() == null || val.getErrMsg() == "") {
                corrects.add(val);
            } else {
                errors.add(val);
            }
        }
        return new ImportValidation<>(corrects, errors);
    }

    /**
     * 数据校验
     *
     * @param val
     * @param token
     * @param headFieldMap
     * @return
     */
    private DecErpIListNUpdateParam checkData(DecErpIListNUpdateParam val, UserInfoToken token, Map<String, String> headFieldMap) {
        DecErpIListN decErpIListNs = decErpIListNMapper.selectByPrimaryKey(val.getSid());
        DecErpIHeadN decErpIHeadN = decErpIHeadNMapper.selectByPrimaryKey(val.getHeadId());

        String tMode = CommonEnum.tModeEnum.getValue(decErpIListNs.getTradeMode());
        Map<String, Object> matParam = new HashMap<>(6);
        matParam.put("tradeCode", token.getCompany());
        matParam.put("GMark", decErpIListNs.getGMark());
        matParam.put("facGNo", decErpIListNs.getFacGNo());
        matParam.put("emsNo", decErpIListNs.getEmsNo());
        matParam.put("bondedFlag", decErpIListNs.getBondMark());
        matParam.put("tradeMode", tMode);

        // 批量获取企业料件信息
        List<MatIeInfoDto> mapMatIEInfo = matImgexgOrgMapper.getIeInfoSimple(matParam);
        List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(token);
        if (warringPassConfigDtos != null) {
            if (warringPassConfigDtos.size() > 0) {
                WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
                if (val.getNetWt() != null) {
                    val.setNetWt(decErpIListNService.digitFormat("netWt", val.getNetWt(), warringPassConfigDtos));
                }
                if (val.getQty() != null) {
                    val.setQty(decErpIListNService.digitFormat("qty", val.getQty(), warringPassConfigDtos));
                }
                if(StringUtils.isNotBlank(warringPassConfigDto.getListReFieldINoBond())) {
                    List<String> listReFieldINoBond = Arrays.asList(warringPassConfigDto.getListReFieldINoBond().split(","));
                    if(listReFieldINoBond.contains("orderNo") && StringUtils.isEmpty(val.getOrderNo())) {
                        val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("采购订单号不能为空/"));
                    }
                }
                // 分批获取物料信息
                if (mapMatIEInfo.size() > 0) {
                    // 计算净重
                    if (mapMatIEInfo.get(0).getNetWt() != null && val.getNetWt() == null) {
                        val.setNetWt(val.getQty().multiply(mapMatIEInfo.get(0).getNetWt()));
                    }
                    if (val.getQty1() == null) {
                        val.setQty1(decCommonService.calculateLegalUnit(val.getQty(), decErpIListNs.getUnit(), mapMatIEInfo.get(0).getUnit1(), mapMatIEInfo.get(0).getFactor1(), val.getNetWt(), token.getCompany()));
                    }
                    if (StringUtils.isEmpty(decErpIListNs.getUnit2())) {
                        if (val.getQty2() != null) {
                            val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("法二单位为空法二数量不容许修改/"));
                        }
                    } else {
                        if (val.getQty2() == null) {
                            val.setQty2(decCommonService.calculateLegalUnit(val.getQty(), decErpIListNs.getUnit(), mapMatIEInfo.get(0).getUnit2(), mapMatIEInfo.get(0).getFactor2(), val.getNetWt(), token.getCompany()));
                        }
                    }
                    // 非保税法二校验
                    if (StringUtils.isNotBlank(decErpIHeadN.getDecType()) && StringUtils.isNotBlank(decErpIHeadN.getBondMark()) && DecVariable.DEC_TYPE_1.equals(decErpIHeadN.getDecType()) && CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark())) {
                        if (StringUtils.isNotBlank(warringPassConfigDto.getListReFieldINoBond()) && warringPassConfigDto.getListReFieldINoBond().contains("qty2")) {
                            if (StringUtils.isNotBlank(mapMatIEInfo.get(0).getUnit2()) && val.getQty2() == null) {
                                val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("法二数量不能为空/"));
                            }
                        }
                    } else {
                        // 保税、大提单法二校验
                        if (StringUtils.isNotBlank(warringPassConfigDto.getListRequiredField()) && warringPassConfigDto.getListRequiredField().contains("qty2")) {
                            if (StringUtils.isNotBlank(mapMatIEInfo.get(0).getUnit2()) && val.getQty2() == null) {
                                val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("法二数量不能为空/"));
                            }
                        }
                    }
                    val.setCiqNo(mapMatIEInfo.get(0).getCiqNo());
                    if ("1".equals(warringPassConfigDto.getMatDataI())) {
                        if (StringUtils.isEmpty(val.getCurr())) {
                            val.setCurr(mapMatIEInfo.get(0).getCurr());
                        }
                        if (StringUtils.isEmpty(val.getOriginCountry())) {
                            val.setOriginCountry(mapMatIEInfo.get(0).getCountry());
                        }
                        if (val.getDecPrice() == null) {
                            val.setDecPrice(mapMatIEInfo.get(0).getDecPrice());
                        }
                    }

                    //成都龙信定制功能-修改商品名称
                    if (StringUtils.equals(warringPassConfigDto.getImportType(),CommonVariable.CONFIG_5)){
                        if (StringUtils.isBlank(val.getGName())){
                            val.setGName(mapMatIEInfo.get(0).getGName());
                        }
                    }else {
                        val.setGName(mapMatIEInfo.get(0).getGName());
                    }

                    //重点标识
                    if (StringUtils.equals(warringPassConfigDto.getProductActivation(), ConstantsStatus.STATUS_1)){
                        val.setKeyProductMark(mapMatIEInfo.get(0).getKeyProductMark());
                    }

                }
                // 非保税境内目的地
                if (StringUtils.isNotBlank(decErpIHeadN.getDecType()) && StringUtils.isNotBlank(decErpIHeadN.getBondMark()) && DecVariable.DEC_TYPE_1.equals(decErpIHeadN.getDecType()) && CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark())) {
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListReFieldINoBond()) && warringPassConfigDto.getListReFieldINoBond().contains("districtCode")) {
                        if (val.getDistrictCode() == null) {
                            val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("境内目的地不能为空/"));
                        }
                    }
                } else {
                    // 保税、大提单境内目的地
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListRequiredField()) && warringPassConfigDto.getListRequiredField().contains("districtCode")) {
                        if (val.getDistrictCode() == null) {
                            val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("境内目的地不能为空/"));
                        }
                    }
                }
                // 非保税净重
                if (StringUtils.isNotBlank(decErpIHeadN.getDecType()) && StringUtils.isNotBlank(decErpIHeadN.getBondMark()) && DecVariable.DEC_TYPE_1.equals(decErpIHeadN.getDecType()) && CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark())) {
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListReFieldINoBond()) && warringPassConfigDto.getListReFieldINoBond().contains("netWt")) {
                        if (val.getNetWt() == null) {
                            val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("净重不能为空/"));
                        }
                    }
                } else {
                    // 保税、大提单净重
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListRequiredField()) && warringPassConfigDto.getListRequiredField().contains("netWt")) {
                        if (val.getNetWt() == null) {
                            val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("净重不能为空/"));
                        }
                    }
                }
                // 非保税毛重
                if (StringUtils.isNotBlank(decErpIHeadN.getDecType()) && StringUtils.isNotBlank(decErpIHeadN.getBondMark()) && DecVariable.DEC_TYPE_1.equals(decErpIHeadN.getDecType()) && CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark())) {
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListReFieldINoBond()) && warringPassConfigDto.getListReFieldINoBond().contains("grossWt")) {
                        if (val.getGrossWt() == null) {
                            val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("毛重不能为空/"));
                        }
                    }
                } else {
                    // 保税、大提单毛重
                    if (StringUtils.isNotBlank(warringPassConfigDto.getListRequiredField()) && warringPassConfigDto.getListRequiredField().contains("grossWt")) {
                        if (val.getGrossWt() == null) {
                            val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("毛重不能为空/"));
                        }
                    }
                }
            }
        }
        // 判断栏位是否存在
        if ("GModel".equals(headFieldMap.get("申报规格型号"))) {
            if (val.getGModel() == null) {
                val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("申报规格型号不能为空/"));
            }
        }
        if (val.getDecTotal() == null) {
            if ("qty".equals(headFieldMap.get("申报数量")) && "decPrice".equals(headFieldMap.get("申报单价"))) {
                if (val.getQty() == null || val.getDecPrice() == null) {
                    val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("申报数量或申报单价不能为空/"));
                } else {
                    val.setDecTotal(val.getQty().multiply(val.getDecPrice()));
                }
            } else if ("qty".equals(headFieldMap.get("申报数量")) && !"decPrice".equals(headFieldMap.get("申报单价"))) {
                if (val.getQty() == null) {
                    val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("申报数量不能为空/"));
                } else {
                    val.setDecTotal(val.getQty().multiply(decErpIListNs.getDecPrice()));
                }
            } else if (!"qty".equals(headFieldMap.get("申报数量")) && "decPrice".equals(headFieldMap.get("申报单价"))) {
                if (val.getDecPrice() == null) {
                    val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("申报单价不能为空/"));
                } else {
                    val.setDecTotal(decErpIListNs.getQty().multiply(val.getDecPrice()));
                }
            }
        } else {
            if (val.getQty() == null) {
                val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("申报数量不能为空/"));
            }
            if (val.getDecPrice() == null) {
                val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("申报单价不能为空/"));
            }
        }
        // 判断客服是否存在
        if (val.getSupplierCode() != null) {
            if (decErpIListNMapper.getCount(token.getCompany(), val.getSupplierCode()) == 0) {
                val.setErrMsg(val.getErrMsg() + val.getSupplierCode() + "||" + xdoi18n.XdoI18nUtil.t("客户不存在/"));
            }
        }
        // 判断单耗版本号是否可以修改
        if (CommonEnum.BondMarkEnum.BOND.getCode().equals(decErpIListNs.getBondMark())) {
            if (decErpIListNs.getEmsNo() != null) {
                if (!decErpIListNs.getEmsNo().startsWith(CommonVariable.EMS_C) && !decErpIListNs.getEmsNo().startsWith(CommonVariable.EMS_B)) {
                    String gmark;
                    if (decErpIHeadN.getTradeMode() != null && (Constants.TRADE_MODE_4400.equals(decErpIHeadN.getTradeMode()) || Constants.TRADE_MODE_4600.equals(decErpIHeadN.getTradeMode()))) {
                        gmark = CommonEnum.GMarkEnum.EXG.getCode();
                    } else {
                        gmark = decErpIListNs.getGMark() == null ? decErpIHeadN.getGMark() : decErpIListNs.getGMark();
                    }
                    if (CommonEnum.GMarkEnum.EXG.getCode().equals(gmark)) {
                        if (StringUtils.isEmpty(val.getExgVersion())) {
                            val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("单耗版本号不能为空/"));
                        }
                    } else {
                        if (StringUtils.isNotEmpty(val.getExgVersion())) {
                            val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("料件不容许修改单耗版本号/"));
                        }
                    }
                }
            }
        } else {
            if (StringUtils.isNotEmpty(val.getExgVersion())) {
                val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("非保税数据单耗版本号不容许修改"));
            }
        }
        // 判断报关单商品序号是否可以修改
        if (CommonEnum.MERGE_TYPE.MERGE_TYPE_1.getCode().equals(decErpIHeadN.getMergeType())) {
            if (val.getEntryGNo() == null) {
                val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("报关单商品序号不能为空/"));
            }
        } else {
            if (val.getEntryGNo() != null) {
                val.setErrMsg(val.getErrMsg() + String.format("[%s]%s",decErpIHeadN.getMergeType(),xdoi18n.XdoI18nUtil.t("表头报关单归并类型，报关单商品序号不能修改")));
            }
        }
        // 校验清单归并序号是否可以修改
        if (!CommonEnum.BondMarkEnum.NOBOND.getCode().equals(decErpIHeadN.getBondMark())) {
            if (CommonEnum.BILL_TYPE.BILL_TYPE_4.getCode().equals(decErpIHeadN.getBillType())) {
                if (val.getBillGNo() == null) {
                    val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("清单归并序号不能为空/"));
                }
            } else {
                if (val.getBillGNo() != null) {
                    val.setErrMsg(val.getErrMsg() + String.format("[%s]%s",decErpIHeadN.getBillType(),xdoi18n.XdoI18nUtil.t("表头清单归并类型，清单归并序号不能修改")));
                }
            }
        }
        boolean checkTradeMode = true;
        if (StringUtils.isNotBlank(decErpIListNs.getTradeMode())) {
            if (StringUtils.isNotBlank(decErpIListNs.getEmsNo())) {
                String firstEmsNoChar = decErpIListNs.getEmsNo().substring(0, 1);
                if (!"H".equals(firstEmsNoChar.toUpperCase())) {
                    // H类型账册监管方式不校验
                    checkTradeMode = CommonEnum.tradeModeEnumI.checkCode(decErpIListNs.getTradeMode(), firstEmsNoChar);
                }
            } else {
                // 非保税校验强制用A类型
                checkTradeMode = CommonEnum.tradeModeEnumI.checkCode(decErpIListNs.getTradeMode(), "A");
            }
        }
        if (!checkTradeMode) {
            val.setErrMsg(val.getErrMsg() + xdoi18n.XdoI18nUtil.t("监管方式错误/"));
        }
        // 法二法定单位为空清除法二数量
        if (Strings.isNullOrEmpty(decErpIListNs.getUnit2())) {
            val.setQty2(null);
        }
        if (StringUtils.isEmpty(val.getDutyMode())) {
            val.setDutyMode(CommonEnum.DUTY_MODE_ENUM.getValue(CommonVariable.IE_MARK_I, val.getTradeMode()));
        }
        if (val.getQty1() != null) {
            val.setQty1(decErpIListNService.digitFormat("qty1", val.getQty1(), warringPassConfigDtos));
        }
        if (val.getQty2() != null) {
            val.setQty2(decErpIListNService.digitFormat("qty2", val.getQty2(), warringPassConfigDtos));
        }
        if (val.getVolume() != null) {
            val.setVolume(decErpIListNService.digitFormat("volume", val.getVolume(), warringPassConfigDtos));
        }
        if (val.getDecTotal() != null) {
            val.setDecTotal(decErpIListNService.digitFormat("decTotal", val.getDecTotal(), warringPassConfigDtos));
        }
        if (val.getDecPrice() != null) {
            val.setDecPrice(decErpIListNService.digitFormat("decPrice", val.getDecPrice(), warringPassConfigDtos));
        }
        if (val.getGrossWt() != null) {
            val.setGrossWt(decErpIListNService.digitFormat("grossWt", val.getGrossWt(), warringPassConfigDtos));
        }
        // 特许权关联单号设置
        List<WarringPassExpandConfigDto> warringPassExpandConfigDtos = warringPassExpandConfigService.selectAll(token);
        if (warringPassExpandConfigDtos.size() > 0) {
            WarringPassExpandConfigDto warringPassExpandConfigDto = warringPassExpandConfigDtos.get(0);
            String concessionsNoAutoTake = warringPassExpandConfigDto.getConcessionsNoAutoTake();
            if (StringUtils.isBlank(val.getConfirmRoyaltiesNo())) {
                if ("1".equals(concessionsNoAutoTake)) {
                    if (StringUtils.isNotBlank(val.getFacGNo())) {
                        val.setConfirmRoyaltiesNo(val.getFacGNo());
                    }
                } else if ("2".equals(concessionsNoAutoTake)) {
                    if (StringUtils.isNotBlank(val.getSupplierCode())) {
                        val.setConfirmRoyaltiesNo(val.getSupplierCode());
                    }
                } else if ("3".equals(concessionsNoAutoTake)) {
                    if (StringUtils.isNotBlank(val.getOrderNo())) {
                        val.setConfirmRoyaltiesNo(val.getOrderNo());
                    }
                }
            }
            if (StringUtils.isNotBlank(val.getConfirmRoyaltiesNo())) {
                GwstdRoyaltyFeeParam gwstdRoyaltyFeeParam = new GwstdRoyaltyFeeParam();
                gwstdRoyaltyFeeParam.setIemark("I");
                gwstdRoyaltyFeeParam.setRoyalityNo(val.getConfirmRoyaltiesNo());
                Integer listCount = gwstdRoyaltyFeeService.checkRoyalityNo(gwstdRoyaltyFeeParam, token);
                if (listCount > 0) {
                    val.setConfirmRoyalties(ConstantsStatus.STATUS_1);
                } else {
                    val.setConfirmRoyalties(null);
                }
            }
        }
        return val;
    }

    @Override
    public int importSave(ImportData<DecErpIListNUpdateParam> data, UserInfoToken token) {
        if (data.getBizParam() == null) {
            throw new ArgumentException();
        }
        Date date = new Date();
        int sum = 0;

        // 获取表头headId
        String headId = Optional.ofNullable(data.getBizParam().get("headId")).orElseThrow(() -> new ArgumentException(xdoi18n.XdoI18nUtil.t("headId不能为空"))).toString();
        deleteOther(headId);
        for (DecErpIListNUpdateParam param : data.getCorrectData()) {
            DecErpIListN decErpIListN = decErpIListNUpdateDtoMapper.toPo(param);
            decErpIListN.setUpdateUser(token.getUserNo());
            decErpIListN.setUpdateUserName(token.getUserName());
            decErpIListN.setUpdateTime(date);
            mapper.updateByPrimaryKeySelective(decErpIListN);
            sum++;
        }
        // 更新表头更新时间
        DecErpIHeadN decErpIHeadN = new DecErpIHeadN();

        decErpIHeadN.setSid(headId);
        decErpIHeadN = decErpIHeadNMapper.selectByPrimaryKey(decErpIHeadN);

        String invoiceNoType = null;
        String contrNoType = null;
        String invoiceNoSplit = null;
        String contrNoSplit = null;
        List<WarringPassConfigDto> warringPassConfigDtos = warringPassConfigService.selectAll(token);
        if (warringPassConfigDtos.size() > 0) {
            WarringPassConfigDto warringPassConfigDto = warringPassConfigDtos.get(0);
            invoiceNoType = warringPassConfigDto.getInvoiceNoType();
            contrNoType = warringPassConfigDto.getContrNoType();
            invoiceNoSplit = warringPassConfigDto.getInvoiceNoSplit();
            contrNoSplit = warringPassConfigDto.getContrNoSplit();
        }
        Map<String, String> inoviceNoList = decErpIListNMapper.selectInoviceNo(token.getCompany(), decErpIHeadN.getSid(), invoiceNoSplit, contrNoSplit);

        decErpIHeadN.setInvoiceNo(decErpIListNService.getInvoiceNoString(inoviceNoList, invoiceNoType));
        decErpIHeadN.setContrNo(decErpIListNService.getContrNoString(inoviceNoList, contrNoType));
        /**
         * 表体汇总净重、毛重
         */
        decErpIHeadN = decErpIListNService.getListSum(decErpIHeadN, headId, token);
        decErpIHeadNService.auditUpdateInfo(decErpIHeadN, token);

        if (decErpIListNMapper.selectConfirmRoyalties(headId) > 0) {
            String promiseItems = decErpIHeadN.getPromiseItems();
            if (StringUtils.isBlank(promiseItems)) {
                decErpIHeadN.setPromiseItems("3");
            } else if (promiseItems.contains("7")) {
                decErpIHeadN.setPromiseItems(promiseItems.replace("7", "3"));
            } else if (!promiseItems.contains("3")) {
                decErpIHeadN.setPromiseItems(promiseItems + ",3");
            }
        }

        //更新表体汇总数据到表头
        decErpIHeadN = decErpIListNService.updateSumAll(decErpIHeadN, headId);

        // 更新表体件数汇总到表头
        int update2 = decErpIHeadNMapper.updateByPrimaryKeySelective(decErpIHeadN);
        return sum + update2;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteOther(String headId) {
        DecErpIHeadN decErpEHeadN = decErpIHeadNMapper.selectByPrimaryKey(headId);
        if (decErpEHeadN != null) {
            if (CommonVariable.APPR_STATUS_9.equals(decErpEHeadN.getApprStatus()) || CommonVariable.APPR_STATUS_B.equals(decErpEHeadN.getApprStatus()) || CommonVariable.APPR_STATUS_8.equals(decErpEHeadN.getApprStatus())) {
                decErpEHeadN.setApprStatus(CommonVariable.APPR_STATUS_0);
                decErpIHeadNMapper.updateByPrimaryKeySelective(decErpEHeadN);

                Map<String, Object> pa = new HashMap<String, Object>(4);
                pa.put("P_SID", decErpEHeadN.getSid());
                pa.put("P_FLAG", DecVariable.STATUS_1);
                pa.put("P_RET_CODE", "");
                pa.put("P_RET_STR", "");
                decErpIHeadNMapper.deleteDecI(pa);
            }
        }
    }
}
