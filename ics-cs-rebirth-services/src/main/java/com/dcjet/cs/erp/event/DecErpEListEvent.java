package com.dcjet.cs.erp.event;

import com.dcjet.cs.base.event.BasicEvent;
import com.dcjet.cs.base.event.CRUDEnum;
import com.dcjet.cs.erp.model.DecErpEListN;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class DecErpEListEvent extends BasicEvent {

    private List<DecErpEListN> decErpEListNs;

    public DecErpEListEvent(CRUDEnum crud, UserInfoToken token, List<DecErpEListN> decErpEListNs) {
        super(crud, token);
        this.decErpEListNs = decErpEListNs;
    }

    @Override
    public void validation() {
        super.validation();
        if (CollectionUtils.isEmpty(decErpEListNs)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("预录入单信息不能为空"));
        }
    }
}
