package com.dcjet.cs.erp.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-24
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_DEC_ERP_QTY_CALC_RECORD")
public class DecErpQtyCalcRecord extends BasicModel implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	@Id
	@Column(name = "SID")
	private String sid;
	/**
	 * 提单表头sid
	 */
	@Column(name = "ERP_HEAD_ID")
	private String erpHeadId;
	/**
	 * 备案号
	 */
	@Column(name = "EMS_NO")
	private String emsNo;
	/**
	 * 备案序号
	 */
	@Column(name = "G_NO")
	private BigDecimal GNo;
	/**
	 * 成品料件标志 I-料件 E-成品
	 */
	@Column(name = "G_MARK")
	private String GMark;
	/**
	 * 计算值
	 */
	@Column(name = "CALC_VALUE")
	private BigDecimal calcValue;
	/**
	 * 进出口标志 I-进口 E-出口
	 */
	@Column(name = "I_E_MARK")
	private String IEMark;
	/**
	 * 是否异常 1：是 0：否
	 */
	@Column(name = "IS_ABNORMAL")
	private String isAbnormal;
	/**
	 * 企业代码
	 */
	@Column(name = "TRADE_CODE")
	private String tradeCode;
	/**
	 * 超出值
	 */
	@Column(name = "OVER_VALUE")
	private BigDecimal overValue;
}
