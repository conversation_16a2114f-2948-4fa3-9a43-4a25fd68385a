package com.dcjet.cs.erp.service.sap;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.erp.sap.ErpDecEPlanHeadDto;
import com.dcjet.cs.dto.erp.sap.ErpDecEPlanHeadParam;
import com.dcjet.cs.erp.dao.sap.ErpSapDecEPlanHeadMapper;
import com.dcjet.cs.util.CommonEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.export.async.core.Exportable;
import com.xdo.export.async.domain.TaskInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 出口计划表头异步导出
 * @author: WJ
 * @createDate: 2021/9/1 13:50
 */
@Component
public class ExportEPlanHeadService implements Exportable {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    private final String taskName = xdoi18n.XdoI18nUtil.t("出口计划表头异步导出(E_PLAN_HEAD)");

    @Resource
    private ErpSapDecEPlanHeadMapper erpSapDecEPlanHeadMapper;

    @Resource
    private CommonService commonService;

    @Override
    public List<String> getTaskCode() {
        List<String> list = new ArrayList<>();
        // 返回所需处理的taskCode
        list.add("E_PLAN_HEAD");
        return list;
    }

    @Override
    public Integer getExportRecordCount(TaskInfo taskInfo) {
        // 组装参数
        ErpDecEPlanHeadParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());
        logger.info("================={}, 计数查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(exportParam));
        Integer count = erpSapDecEPlanHeadMapper.selectDataCount(exportParam);
        logger.info("================={}, 导出条数：{}=================", taskName, count);
        return count;
    }

    @Override
    public int getExportPagedSize(TaskInfo taskInfo, int totalCount) {
        return 5000;
    }

    @Override
    public List<Object> getExportPagedList(TaskInfo taskInfo, PageParam pageParam) {
        logger.info("================={}, 企业编号：{}, 第{}页, 任务sid:{}=================", taskName,
                taskInfo.getUserInfoToken().getCompany(), pageParam.getPage(), taskInfo.getSid());
        // 组装参数
        ErpDecEPlanHeadParam exportParam = convertParam(taskInfo);

        UserInfoToken userInfo = taskInfo.getUserInfoToken();
        exportParam.setTradeCode(userInfo.getCompany());

        logger.info("================={}, 获取数据查询条件：{}=================", taskName, JsonObjectMapper.getInstance().toJson(exportParam));
        Page<ErpDecEPlanHeadDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> erpSapDecEPlanHeadMapper.getList(exportParam));

        List<Object> list = new ArrayList<>();
        list.addAll(convertForPrint(page.getResult()));
        logger.info("================={}, 导出size：{}=================", taskName, list.size());
        return list;
    }

    private List<ErpDecEPlanHeadDto> convertForPrint(List<ErpDecEPlanHeadDto> list) {
        for (ErpDecEPlanHeadDto item : list) {
            if (StringUtils.isNotBlank(item.getStatus())) {
                item.setStatus(item.getStatus() + " " + CommonEnum.takeStatusEnum.getValue(item.getStatus()));
            }
            if (StringUtils.isNotBlank(item.getModifyMark())) {
                item.setModifyMark(CommonEnum.modifyMarkEnum.getValue(item.getModifyMark()));
            }
            item.setTrafMode(commonService.convertPCode(item.getTrafMode(), PCodeType.TRANSF));
            item.setDestPort(commonService.convertPCode(item.getDestPort(), PCodeType.PORT_LIN));
            item.setDespPort(commonService.convertPCode(item.getDespPort(), PCodeType.PORT_LIN));
        }
        return list;
    }

    private ErpDecEPlanHeadParam convertParam(TaskInfo taskInfo) {
        ObjectMapper mapper = new ObjectMapper();
        // 获取业务参数
        String json = taskInfo.getExportParamJson();
        ErpDecEPlanHeadParam exportParam = null;
        try {
            exportParam = mapper.readValue(json, ErpDecEPlanHeadParam.class);
        } catch (IOException e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("获取业务参数失败"));
        }
        return exportParam;
    }
}
