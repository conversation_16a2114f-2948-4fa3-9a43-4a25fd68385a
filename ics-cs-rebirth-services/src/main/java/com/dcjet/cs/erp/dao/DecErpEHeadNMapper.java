package com.dcjet.cs.erp.dao;

import com.dcjet.cs.Utils.chPojo.GNameRoBackParam;
import com.dcjet.cs.Utils.chPojo.SeqNoRoBackParam;
import com.dcjet.cs.dto.cost.CostEstimateEDto;
import com.dcjet.cs.dto.cost.CostEstimateEParam;
import com.dcjet.cs.dto.insure.PreHead;
import com.dcjet.cs.erp.model.DecErpEHeadN;
import com.dcjet.cs.erp.model.DecErpIHeadN;
import com.dcjet.cs.mat.model.Attached;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * DecErpEHeadN
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
public interface DecErpEHeadNMapper extends Mapper<DecErpEHeadN> {
    /**
     * 查询获取数据
     *
     * @param decErpEHeadN
     * @return
     */
    List<DecErpEHeadN> getListAll(DecErpEHeadN decErpEHeadN);

    List<DecErpEHeadN> getList(DecErpEHeadN decErpEHeadN);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    List<DecErpEHeadN> selectBySids(List<String> sids);

    /**
     * 商品归类查询
     *
     * @param decErpEHeadN 传入参数
     * @return list数据
     * <AUTHOR>
     */
    List<DecErpEHeadN> getListAuditPaged(DecErpEHeadN decErpEHeadN);

    int getListAuditCount(@Param("tradeCode") String tradeCode, @Param("userNo") String userNo);

    /**
     * 获取提单企业内部编号
     *
     * @param param 参数
     * @return
     * @author: 沈振宇
     * @date: 2019-2-23
     */
    Integer isEmsListNo(Map<String, Object> param);

    /**
     * @param param 参数
     * @return
     * @author: 沈振宇
     * @date: 2019-2-23
     */
    List<Map<String, Object>> isMerge(Map<String, Object> param);

    /***
     * 调用存储过程
     * @param param
     * @return
     */
    List<Map<String, Object>> deleteDecE(Map<String, Object> param);

    /**
     * @param sids 参数
     * @return
     * @author: 沈振宇
     * @date: 2019-2-23
     */
    List<Map<String, String>> selectEntryNoBySids(List<String> sids);

    /**
     * 根据关联回填记录表查询出口待回填数据
     *
     * @return
     * @author: jlzhang
     * @date: 2019-11-29
     */
    List<SeqNoRoBackParam> selectXCodeSendMark(String tradeCode);

    /**
     * 根据关联回填记录表查询出口报关品名待回填数据
     *
     * @return
     * @author: jlzhang
     * @date: 2019-
     */
    List<GNameRoBackParam> selectMakTxSendMark(String tradeCode);

    /**
     * 修改物流追踪
     *
     * @param decErpEHeadN
     * @return 返回执行成功行数，0为执行失败
     */
    int updateLogisticsTrack(DecErpEHeadN decErpEHeadN);

    /**
     * 功能描述 小提单时，修改表体监管方式
     *
     * @param tradeMode 1
     * @param sid       2
     * @return int
     * <AUTHOR>
     * @date 2020/5/8
     * @version 1.0
     */
    int updateListTradeMode(@Param("tradeMode") String tradeMode, @Param("sid") String sid);

    /**
     * 查询关联编号在提单表中是否已存在，排除清单置无效的
     */
    int countHeadByLinkedNo(@Param("linkedNo") String linkedNo, @Param("tradeCode") String tradeCode);

    /**
     * 功能描述 小提单时，修改ERP状态
     * JJL ADD
     *
     * @param tradeCode linkedNo 2
     * @return int
     * <AUTHOR> @date 2020/6/12
     * @version 1.0
     */
    int updateERPstatus(@Param("tradeCode") String tradeCode, @Param("linkedNo") String linkedNo, @Param("status") String status);

    int updateERPListStatus(@Param("tradeCode") String tradeCode, @Param("linkedNo") String linkedNo, @Param("status") String status);

    @Select("select count(*) from t_dec_e_bill_head where head_id = #{headId} and bond_mark = '0' and send_api_status != 'DL' and send_api_status != 'JDL'")
    Integer checkForForceChangeStatus(@Param("headId") String headId);

    String selectApplyNoBySid(String headId);

    int deletePackByHeadId(List<String> sids);

    void updateBill(DecErpEHeadN decErpEHeadN);

    void updateEntry(DecErpEHeadN decErpEHeadN);

    /**
     * 获取清单生成的出口提单数据
     */
    List<CostEstimateEDto> getCostList(CostEstimateEParam costEstimateEParam);

    /**
     * 获取出口费用明细列表
     */
    List<CostEstimateEDto> getPriceList(CostEstimateEParam costEstimateEParam);

    void checkRemainQtyForEml(Map<String, String> param);

    /**
     * 根据提单内部编号获取报关单号列表
     *
     * @return
     */
    List<String> getEntryNoListBySid(String sid);

    List<DecErpEHeadN> selectOldDataBySids(@Param("sids") List<String> sids);

    /**
     * 更新status提取状态
     */
    int updateStatus(@Param("sids") List<String> sid, @Param("status") String status);

    /**
     * 根据headIds汇总净重，毛重，体积
     *
     * @param sids
     * @return
     */
    DecErpEHeadN sumWtByHeadIds(List<String> sids);

    /**
     * 更新复运出境管理状态
     *
     * @param emsListNos
     * @param tradeCode
     */
    void updateHeadStatus(@Param("emsListNos") List<String> emsListNos, @Param("tradeCode") String tradeCode);

    /**
     * 插入到发票箱单
     *
     * @param decErpEHeadN
     */
    void insertInvoicBox(DecErpEHeadN decErpEHeadN);

    List<Map<String, Object>> getLinkedNo(@Param("tradeCode") String tradeCode, @Param("linkedNo") String linkedNo);

    void updateDecHead(@Param("sid") String sid, @Param("apprStatus") String apprStatus, @Param("status") String status, @Param("tradeCode") String tradeCode);

    /**
     * 功能描述:获取表头信息
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/5/13
     * @param: * @param null
     * @return:
     */
    Map<String, Object> getHeadDataBySid(@Param("sid") String sid);

    /**
     * 功能描述:获取表体信息
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/5/13
     * @param: erpHeadId 提单表头id
     * @param: groupField 归并条件
     * @return:
     */
    List<Map<String, Object>> getBodyDataListByErpHeadSid(@Param("erpHeadId") String erpHeadId, @Param("groupField") String groupField, @Param("exchangeRate") BigDecimal exchangeRate, @Param("tradeCode") String tradeCode);

    List<String> getInsertUser(@Param("tradeCode") String tradeCode);

    int selectAttachedTypeE(@Param("sid") String sid, @Param("tradeCode") String tradeCode);

    List<Attached> selectAttachedE(@Param("sid") String sid, @Param("tradeCode") String tradeCode);

    @Select("select e_mail from t_dec_erp_E_head_n head left join T_BI_CLIENT_INFORMATION bi on head.DECLARE_CODE_CUSTOMS = bi.customer_Code where head.sid = #{sid} and head.trade_code = #{tradeCode} limit 1")
    String getEmailBySid(@Param("sid") String sid, @Param("tradeCode") String tradeCode);

    @Select("select ems_list_no, 'E' as i_e_mark, mawb, hawb, traf_mode, traf_name, district_code as district_code_original, dest_port, desp_port, insure_id , insure_company as  insurance_company, insur_rate as rate  from T_DEC_ERP_E_HEAD_N where sid = #{sid} and trade_code = #{tradeCode} limit 1")
    PreHead getInsurePreHeadBySid(@Param("sid") String sid, @Param("tradeCode") String tradeCode);

    @Update("update T_DEC_ERP_E_HEAD_N set insure_id = #{insureId} , insure_time = now() where sid = #{sid}")
    void updateInsureInfoBySid(@Param("sid") String sid, @Param("insureId") String insureId);

    /**
     * 出口随附单据-其他-信息
     * @param tradeCode
     * @return
     */
    @Select("select erp_head.sid,erp_head.ems_list_no,erp_head.entry_no from t_dec_erp_e_head_n erp_head left join t_attached att on erp_head.sid = att.head_id where erp_head.trade_code = #{tradeCode} and acmp_type = 'other' and business_type = 'EM'")
    List<DecErpEHeadN> getListByTradeCode(@Param("tradeCode") String tradeCode);
}
