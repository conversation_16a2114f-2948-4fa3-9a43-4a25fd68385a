package com.dcjet.cs.erp.mapper.sap;

import com.dcjet.cs.erp.model.sap.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ErpMqSyncDtoMapper {
    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpDecIHead source, @MappingTarget ErpDecIHead target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpDecIList source, @MappingTarget ErpDecIList target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpDecEHead source, @MappingTarget ErpDecEHead target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpDecEList source, @MappingTarget ErpDecEList target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpMatImgexg source, @MappingTarget ErpMatImgexg target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpImgIeList source, @MappingTarget ErpImgIeList target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpExgIeList source, @MappingTarget ErpExgIeList target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpImgStock source, @MappingTarget ErpImgStock target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpExgStock source, @MappingTarget ErpExgStock target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpSemiExgStock source, @MappingTarget ErpSemiExgStock target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpCavBom source, @MappingTarget ErpCavBom target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpMrpBom source, @MappingTarget ErpMrpBom target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpSupplier source, @MappingTarget ErpSupplier target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpTaxCip source, @MappingTarget ErpTaxCip target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpEPlanHead source, @MappingTarget ErpEPlanHead target);

    @Mapping(target = "sid", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "insertTime", ignore = true)
    void overwrite(ErpEPlanList source, @MappingTarget ErpEPlanList target);
}
