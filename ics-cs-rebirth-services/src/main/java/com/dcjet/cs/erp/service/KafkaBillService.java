package com.dcjet.cs.erp.service;

import com.dcjet.cs.erp.dao.*;
import com.dcjet.cs.erp.model.*;
import com.dcjet.cs.mqsync.core.service.BaseMqSyncService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 *
 */
@Service
@Slf4j
public class KafkaBillService extends BaseMqSyncService<BillSyncMessage> {

    @Resource
    private DecEBillHeadMapper decEBillHeadMapper;
    @Resource
    private DecIBillHeadMapper decIBillHeadMapper;
    @Resource
    private BillSyncHeadTmpMapper billSyncHeadTmpMapper;
    @Resource
    private BillSyncListTmpMapper billSyncListTmpMapper;
    @Resource
    private EntrySyncListMapper entrySyncListMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void consume(String data) {
        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        ResultObject<BillSyncMessage> result = jsonObjectMapper.fromJson(data, new TypeReference<ResultObject<BillSyncMessage>>() {});
        BillSyncMessage message = result.getData();
        log.info(String.format("开始处理KAFKA接口获取清单信息,企业编码：【%s】,手/账册号：【%s】,任务Id：【%s】", message.getList().getBillHead().getTradeCode(), message.getEmsNo(), message.getTaskID()));
        BillSyncBody billSyncBody = message.getList();
        String head_id = UUID.randomUUID().toString();
        try {
            BillSyncHeadTmp billSyncHeadTmp = billSyncBody.getBillHead();
            log.info(String.format("开始处理KAFKA接口获取清单信息,清单内部编号：【%s】", billSyncHeadTmp.getEmsListNo()));
            //只处理清单状态为1（海关终审通过）的数据
            if (!ConstantsStatus.STATUS_1.equals(billSyncHeadTmp.getStatus())) {
                log.info(String.format("清单内部编码【%s】对应清单状态为：【%s】，不予处理", billSyncHeadTmp.getEmsListNo(), billSyncHeadTmp.getStatus()));
                return;
            }
            //无清单表体数据不处理
            List<BillSyncListTmp> billSyncListTmps = billSyncBody.getBillList();
            if (CollectionUtils.isEmpty(billSyncListTmps)) {
                log.info(String.format("清单内部编码【%s】下无对应表体数据,不做处理", billSyncHeadTmp.getEmsListNo()));
                return;
            }
            //校验清单是否已存在
            if (CommonEnum.I_E_MARK_ENUM.E_MARK.getCode().equals(billSyncBody.getBillHead().getIEMark())) {
                DecEBillHead decEBillHead = new DecEBillHead();
                decEBillHead.setTradeCode(billSyncHeadTmp.getTradeCode());
                decEBillHead.setEmsListNo(billSyncHeadTmp.getEmsListNo());
                List<DecEBillHead> decEBillHeads = decEBillHeadMapper.select(decEBillHead);
                if (CollectionUtils.isNotEmpty(decEBillHeads)) {
                    log.info(String.format("清单内部编码【%s】在数据库中已存在,不做处理", billSyncHeadTmp.getEmsListNo()));
                    return;
                }
            } else {
                DecIBillHead decIBillHead = new DecIBillHead();
                decIBillHead.setTradeCode(billSyncHeadTmp.getTradeCode());
                decIBillHead.setEmsListNo(billSyncHeadTmp.getEmsListNo());
                List<DecIBillHead> decIBillHeads = decIBillHeadMapper.select(decIBillHead);
                if (CollectionUtils.isNotEmpty(decIBillHeads)) {
                    log.info(String.format("清单内部编码【%s】在数据库中已存在,不做处理", billSyncHeadTmp.getEmsListNo()));
                    return;
                }
            }
            //表头入临时表
            billSyncHeadTmp.setSid(head_id);
            billSyncHeadTmpMapper.insert(billSyncHeadTmp);
            //表体入临时表
            for (BillSyncListTmp billSyncListTmp : billSyncListTmps) {
                billSyncListTmp.setSid(UUID.randomUUID().toString());
                billSyncListTmp.setHeadId(head_id);
                billSyncListTmpMapper.insert(billSyncListTmp);
            }
            //报关单表体数据入临时表
            List<EntrySyncListTmp> entrySyncListTmps = billSyncBody.getEntryList();
            if (CollectionUtils.isNotEmpty(entrySyncListTmps)) {
                for (EntrySyncListTmp entrySyncListTmp : entrySyncListTmps) {
                    entrySyncListTmp.setSid(UUID.randomUUID().toString());
                    entrySyncListTmp.setBillHeadId(head_id);
                    entrySyncListMapper.insert(entrySyncListTmp);
                }
            }
            //调用存储过程生成清单及提单
            billSyncHeadTmpMapper.callCreateBillAndErp(head_id);
        } catch (Exception e) {
            log.error(String.format("KAFKA接口处理清单信息错误；【%s】", e.getMessage()));
        }
        log.info(String.format("KAFKA接口获取清单信息,企业编码：【%s】,手/账册号：【%s】处理结束", message.getList().getBillHead().getTradeCode(), message.getEmsNo()));
    }

}

