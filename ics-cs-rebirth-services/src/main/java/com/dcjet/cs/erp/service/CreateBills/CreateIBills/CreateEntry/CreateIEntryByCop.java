package com.dcjet.cs.erp.service.CreateBills.CreateIBills.CreateEntry;

import com.dcjet.cs.erp.model.DecIBillList;
import com.dcjet.cs.erp.service.CreateBills.config.CreateBillsConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * CreateEntryByCop
 *
 * <AUTHOR>
 * @date: 2020-5-29
 */
@Component
public class CreateIEntryByCop implements CreateIEntryStrategy {

    @Resource
    private PublicCreateIEntry publicCreateIEntry;

    @Override
    public String entryType() {
        return CreateBillsConfig.ENTRY_MERGE_TYPE_COP;
    }

    @Override
    public PublicCreateIEntry getHandler() {
        return this.publicCreateIEntry;
    }

    /**
     * 获取group key
     *
     * @param m
     * @return
     */
    @Override
    public String fetchGroupKey(DecIBillList m) {
        return String.format("%s||%s||%s||%s||%s||%s||%s||%s||%s||%s||%s||%s||%s",
                m.getCopGNo(), m.getCodeTS(),
                m.getGName(), m.getUnit(),
                m.getCurr(), m.getOriginCountry(),
                m.getDestinationCountry(),
                m.getUnit1(), m.getUnit2(),
                m.getGMark(), m.getDutyMode(),
                m.getDistrictCode(), m.getDistrictPostCode());
    }
}
