package com.dcjet.cs.erp.model.sap;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-5-22
 */
@Setter
@Getter
@Table(name = "t_erp_original_bom")
public class ErpOriginalBom implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 同步批次号
	 */
	@Column(name = "SYNC_BATCH_NO")
	@JsonIgnore
	private String syncBatchNo;
	/**
	 * 主键,GUID
	 */
	@Id
	@Column(name = "sid")
	private String sid;
	/**
	 * 主键关务料号
	 */
	@Column(name = "cop_exg_no")
	private String copExgNo;
	/**
	 * 关务版本
	 */
	@Column(name = "exg_version")
	private String exgVersion;
	/**
	 * 母阶计量单位
	 */
	@Column(name = "exg_unit")
	private String exgUnit;
	/**
	 * 关务元件料号
	 */
	@Column(name = "cop_img_no")
	private String copImgNo;
	/**
	 * 关务元件料号说明
	 */
	@Column(name = "img_note")
	private String imgNote;
	/**
	 * 基础计量单位
	 */
	@Column(name = "unit")
	private String unit;
	/**
	 * 物料群组
	 */
	@Column(name = "mat_group")
	private String matGroup;
	/**
	 * 物料群组说明
	 */
	@Column(name = "mat_group_name")
	private String matGroupName;
	/**
	 * 备案理论总耗用(多阶)
	 */
	@Column(name = "consumption_total")
	private BigDecimal consumptionTotal;
	/**
	 * 原料关务料号
	 */
	@Column(name = "fac_g_no")
	private String facGNo;
	/**
	 * 计量比例因子
	 */
	@Column(name = "factor")
	private BigDecimal factor;
	/**
	 * 元件计量单位
	 */
	@Column(name = "img_unit")
	private String imgUnit;
	/**
	 * 海关商品名称
	 */
	@Column(name = "g_name")
	private String GName;
	/**
	 * 更新日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "last_update_time")
	private Date lastUpdateTime;
	/**
	 * 创建人
	 */
	@Column(name = "insert_user")
	private String insertUser;
	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "insert_time")
	private Date insertTime;
	/**
	 * 更新人
	 */
	@Column(name = "update_user")
	private String updateUser;
	/**
	 * 更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "update_time")
	private Date updateTime;
	/**
	 * 用户/导入批次号
	 */
	@Column(name = "temp_owner")
	private String tempOwner;
	/**
	 * 数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）
	 */
	@Column(name = "modify_mark")
	private Integer modifyMark;
	/**
	 * 数据来源 0 手工录入 1 ERP接口 2 导入
	 */
	@Column(name = "data_source")
	private String dataSource;
	/**
	 * 数据状态 0 未提取; 1 提取过
	 */
	@Column(name = "STATUS")
	private String status;
	/**
	 * 清洗标志
	 */
	@Column(name = "IS_CLEAR")
	private String isClear = "1";
	/**
	 * 企业代码
	 */
	@Column(name = "TRADE_CODE")
	private String tradeCode;

	/**
	 * 外部物料群组
	 */
	@Column(name = "mat_group_out")
	private String matGroupOut;
	/**
	 * 外部物料群组说明
	 */
	@Column(name = "mat_group_name_out")
	private String matGroupNameOut;
	/**
	 * 成品物料群组
	 */
	@Column(name = "exg_group")
	private String exgGroup;
}
