package com.dcjet.cs.erp.model;

import lombok.Getter;
import lombok.Setter;
import xdo.interceptor.decimal.RemoveTailingZero;

import java.io.Serializable;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter
@Getter
@RemoveTailingZero
public class RcepDto implements Serializable {
    private static final long serialVersionUID = 1L;
	private  String codeTs;
	private  String taxRate;
	private  String beginDate;
	private  String endDate;
	private  String tradeCountryCn;
	private  String lowRate;
	private  String impTempRate;
	private  String impAgreementRate;
	private  String description;
	private  String originCountryCn;
	private  String destinationCountryCn;
	private  String baseTaxRate;
	private  String rcepTaxRate;
	private  String rcepExpdate;
	private  String gname;
}
