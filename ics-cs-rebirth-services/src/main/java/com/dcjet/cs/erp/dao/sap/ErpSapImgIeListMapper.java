package com.dcjet.cs.erp.dao.sap;

import com.dcjet.cs.dto.deep.ListSumInfo;
import com.dcjet.cs.dto.erp.sap.ErpImgIeListDto;
import com.dcjet.cs.dto.erp.sap.ErpImgIeListParam;
import com.dcjet.cs.erp.model.sap.ErpImgIeList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * generated by Generate 神码
 * ErpImgIeList
 *
 * <AUTHOR>
 * @date: 2019-8-14
 */
public interface ErpSapImgIeListMapper extends Mapper<ErpImgIeList> {
    List<ErpImgIeListDto> getList(ErpImgIeListParam erpImgIeListParam);

    /**
     * 获取汇总数据
     *
     * @param matreialErp
     * @return
     */
    ListSumInfo getListTotalSjg(ErpImgIeList matreialErp);

    ErpImgIeList getByIdentifier(@Param("billNo") String billNo,
                                 @Param("lineNo") Long lineNo,
                                 @Param("tradeCode") String tradeCode);

    /***
     * 根据ID更新数据的ID
     *
     * @param tradeCode
     * @param status
     * @param idList
     */
    void updateStatusByIdList(@Param("tradeCode") String tradeCode, @Param("status") String status, @Param("idList") List<String> idList);

    Integer selectDataCount(ErpImgIeListParam exportParam);

    int deleteBySids(@Param("list") List<String> list, @Param("tradeCode") String tradeCode);
}
