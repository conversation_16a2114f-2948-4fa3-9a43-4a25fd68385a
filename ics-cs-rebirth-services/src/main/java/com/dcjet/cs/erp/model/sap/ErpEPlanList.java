package com.dcjet.cs.erp.model.sap;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: WJ
 * @createDate: 2021/8/31 15:14
 */
@Setter
@Getter
@Table(name = "t_erp_e_plan_list")
public class ErpEPlanList implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 同步批次号
     */
    @Column(name = "SYNC_BATCH_NO")
    @JsonIgnore
    private String syncBatchNo;

    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 数据状态 0 未提取，1 提取过
     */
    @Column(name = "STATUS")
    private String status;

    @Column(name = "HEAD_ID")
    private String headId;

    @Column(name = "TRADE_CODE")
    private String tradeCode;

    @Column(name = "INSERT_USER")
    private String insertUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;

    @Column(name = "UPDATE_USER")
    private String updateUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Column(name = "EXTRACT_TIME")
    private Date extractTime;

    /**
     * 出货ID
     */
    @Column(name = "LINKED_NO")
    @JsonProperty("billNo")
    private String linkedNo;
    /**
     * 序号
     */
    @Column(name = "LINE_NO")
    @JsonProperty("billSerialNo")
    private Long lineNo;
    /**
     * 企业料号
     */
    @Column(name = "FAC_G_NO")
    private  String facGNo;
    /**
     * 保完税标志
     */
    @Column(name = "BOND_MARK")
    private  String bondMark;
    /**
     * 物料类型
     */
    @Column(name = "G_MARK")
    @JsonProperty("gMark")
    private  String gMark;
    /**
     * 转换后保完税标志
     **/
    @Column(name = "BOND_MARK_CONVERT")
    private String bondMarkConvert;

    /**
     * 转换后物料类型标志
     **/
    @Column(name = "G_MARK_CONVERT")
    private String GMarkConvert;
    /**
     * 商品名称
     */
    @Column(name = "COP_G_NAME")
    private  String copGName;
    /**
     * 规格型号
     */
    @Column(name = "G_MODEL")
    @JsonProperty("gModel")
    private  String gModel;
    /**
     * 交易数量
     */
    @Column(name = "QTY")
    private  BigDecimal qty;
    /**
     * ERP计量单位
     */
    @Column(name = "UNIT_ERP")
    private  String unitErp;
    /**
     * 材料费
     */
    @Column(name = "DEC_PRICE_MATERIALS")
    private  BigDecimal decPriceMaterials;
    /**
     * 加工费
     */
    @Column(name = "DEC_PRICE_PROCESS")
    private  BigDecimal decPriceProcess;
    /**
     * 币制
     */
    @Column(name = "CURR")
    private  String curr;
    /**
     * 箱号
     */
    @Column(name = "CARTON_NO")
    private  String cartonNo;
    /**
     * 销售订单号码
     */
    @Column(name = "SO_NO")
    private  String soNo;
    /**
     * 车间订单
     */
    @Column(name = "SHOP_ORDER")
    private  String shopOrder;
    /**
     * 客户PO
     */
    @Column(name = "CUSTOMER_PO_NO")
    private  String customerPoNo;
    /**
     * 净重
     */
    @Column(name = "NET_WT")
    private  BigDecimal netWt;
    /**
     * 毛重
     */
    @Column(name = "GROSS_WT")
    private  BigDecimal grossWt;
    /**
     * 体积
     */
    @Column(name = "VOLUME")
    private  BigDecimal volume;
    /**
     * 备注1
     */
    @Column(name = "REMARK1")
    private  String remark1;
    /**
     * 备注2
     */
    @Column(name = "REMARK2")
    private  String remark2;
    /**
     * ERP创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Column(name = "LAST_UPDATE_TIME")
    private  Date lastUpdateTime;
    /**
     * 传输批次号
     */
    @Column(name = "TEMP_OWNER")
    private  String tempOwner;

    /**
     * 数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）
     */
    @Column(name = "MODIFY_MARK")
    private Integer modifyMark;
}
