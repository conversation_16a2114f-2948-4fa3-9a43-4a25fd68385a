package com.dcjet.cs.invoice.mapper;

import com.dcjet.cs.dto.invoice.DecInvoiceBoxHeadIDto;
import com.dcjet.cs.dto.invoice.DecInvoiceBoxHeadIParam;
import com.dcjet.cs.invoice.model.DecInvoiceBoxHeadI;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-6-12
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecInvoiceBoxHeadIDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecInvoiceBoxHeadIDto toDto(DecInvoiceBoxHeadI po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecInvoiceBoxHeadI toPo(DecInvoiceBoxHeadIParam param);
    /**
     * 数据库原始数据更新
     * @param decInvoiceBoxHeadIParam
     * @param decInvoiceBoxHeadI
     */
    void updatePo(DecInvoiceBoxHeadIParam decInvoiceBoxHeadIParam, @MappingTarget DecInvoiceBoxHeadI decInvoiceBoxHeadI);
    default void patchPo(DecInvoiceBoxHeadIParam decInvoiceBoxHeadIParam, DecInvoiceBoxHeadI decInvoiceBoxHeadI) {
        // TODO 自行实现局部更新
    }
}
