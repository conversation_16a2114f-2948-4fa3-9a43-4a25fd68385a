package com.dcjet.cs.invoice.mapper;
import com.dcjet.cs.dto.invoice.DecErpInvoiceHeadEDto;
import com.dcjet.cs.dto.invoice.DecErpInvoiceHeadEParam;
import com.dcjet.cs.invoice.model.DecErpInvoiceHeadE;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-5-8
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DecErpInvoiceHeadEDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    DecErpInvoiceHeadEDto toDto(DecErpInvoiceHeadE po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    DecErpInvoiceHeadE toPo(DecErpInvoiceHeadEParam param);
    /**
     * 数据库原始数据更新
     * @param decErpInvoiceHeadEParam
     * @param decErpInvoiceHeadE
     */
    void updatePo(DecErpInvoiceHeadEParam decErpInvoiceHeadEParam, @MappingTarget DecErpInvoiceHeadE decErpInvoiceHeadE);
    default void patchPo(DecErpInvoiceHeadEParam decErpInvoiceHeadEParam, DecErpInvoiceHeadE decErpInvoiceHeadE) {
        // TODO 自行实现局部更新
    }
}
