package com.dcjet.cs.invoice.dao;

import com.dcjet.cs.invoice.model.DecInvoiceBoxListE;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate dcits
* DecInvoiceBoxListE
* <AUTHOR>
* @date: 2019-6-12
*/
public interface DecInvoiceBoxListEMapper extends Mapper<DecInvoiceBoxListE> {
    /**
     * 根据参数查询
     *
     * @param decInvoiceBoxListE
     * @return
     */
    List<DecInvoiceBoxListE> getList(DecInvoiceBoxListE decInvoiceBoxListE);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 根据表头headId批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    void deleteByHeadIds(List<String> sids);
    /**
     * 根据表头headId查询是否存在表体数据
     * @param sids
     * @return
     */
    int getListNumByHeadIds(List<String> sids);
    /**
     * 根据表头sid查询表体数据
     *
     * @param sids 表头sid列表
     * @return
     */
    List<DecInvoiceBoxListE> getListByHeadIds(List<String> sids);

}
