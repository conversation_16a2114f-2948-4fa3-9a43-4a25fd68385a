package com.dcjet.cs.invoice.service;

import com.dcjet.cs.dto.invoice.DecErpInvoiceHeadIDto;
import com.dcjet.cs.dto.invoice.DecErpInvoiceHeadIParam;
import com.dcjet.cs.erp.dao.DecErpEListNMapper;
import com.dcjet.cs.invoice.dao.DecErpInvoiceHeadIMapper;
import com.dcjet.cs.invoice.mapper.DecErpInvoiceHeadIDtoMapper;
import com.dcjet.cs.invoice.model.DecErpInvoiceHeadI;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.validator.internal.util.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-5-8
 */
@Service
public class DecErpInvoiceHeadIService extends BaseService<DecErpInvoiceHeadI> {
    private static final Logger LOGGER = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Resource
    private DecErpEListNMapper decErpEListNMapper;
    @Resource
    private DecErpInvoiceHeadIMapper decErpInvoiceHeadIMapper;
    @Resource
    private DecErpInvoiceHeadIDtoMapper decErpInvoiceHeadIDtoMapper;
    @Override
    public Mapper<DecErpInvoiceHeadI> getMapper() {
        return decErpInvoiceHeadIMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param decErpInvoiceHeadIParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<DecErpInvoiceHeadIDto>> getListPaged(DecErpInvoiceHeadIParam decErpInvoiceHeadIParam, PageParam pageParam) {
        // 启用分页查询
        DecErpInvoiceHeadI decErpInvoiceHeadI = decErpInvoiceHeadIDtoMapper.toPo(decErpInvoiceHeadIParam);
        Page<DecErpInvoiceHeadI> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decErpInvoiceHeadIMapper.getList(decErpInvoiceHeadI));
        List<DecErpInvoiceHeadIDto> decErpInvoiceHeadIDtos = page.getResult().stream().map(head -> {
            DecErpInvoiceHeadIDto dto = decErpInvoiceHeadIDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<DecErpInvoiceHeadIDto>> paged = ResultObject.createInstance(decErpInvoiceHeadIDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param decErpInvoiceHeadIParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecErpInvoiceHeadIDto insert(DecErpInvoiceHeadIParam decErpInvoiceHeadIParam, UserInfoToken userInfo) {
        DecErpInvoiceHeadI decErpInvoiceHeadI = decErpInvoiceHeadIDtoMapper.toPo(decErpInvoiceHeadIParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        decErpInvoiceHeadI.setSid(sid);
        decErpInvoiceHeadI.setInsertUser(userInfo.getUserNo());
        decErpInvoiceHeadI.setInsertUserName(userInfo.getUserName());
        decErpInvoiceHeadI.setInsertTime(new Date());
        // 新增数据
        int insertStatus = decErpInvoiceHeadIMapper.insert(decErpInvoiceHeadI);
        return  insertStatus > 0 ? decErpInvoiceHeadIDtoMapper.toDto(decErpInvoiceHeadI) : null;
    }
    /**
     * 功能描述:批量新增
     *
     * @param decErpInvoiceHeadIParams
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String inserts(List<DecErpInvoiceHeadIParam> decErpInvoiceHeadIParams, UserInfoToken userInfo) {
        try{
            //获取新增的发票号（提单表体中且表头发票为空的才需要新增）
           String newAddInvoiceNo= decErpInvoiceHeadIParams.stream().filter(x->x.isInvoiceNoIsNew()).map(x->x.getInvoiceNo()).findFirst().get();
          //提单表头id
           String headId=decErpInvoiceHeadIParams.get(0).getErpHeadId();
           //存在新增的发票号则更新 原提单中表头、表体为空的发票号
           if(!StringHelper.isNullOrEmptyString(newAddInvoiceNo)){
               decErpEListNMapper.updateInvoiceNo(headId,newAddInvoiceNo);
           }
           //删除该提单已生成的发票号
            decErpInvoiceHeadIMapper.deleteByErpHeadId(headId);
           //循环插入新生成的发票号
            for (DecErpInvoiceHeadIParam decErpInvoiceHeadIParam:decErpInvoiceHeadIParams ) {
                DecErpInvoiceHeadI  decErpInvoiceHeadI=decErpInvoiceHeadIDtoMapper.toPo(decErpInvoiceHeadIParam);
                /**
                 * 规范固定字段
                 */
                String sid = UUID.randomUUID().toString();
                decErpInvoiceHeadI.setSid(sid);
                decErpInvoiceHeadI.setInsertUser(userInfo.getUserNo());
                decErpInvoiceHeadI.setInsertUserName(userInfo.getUserName());
                decErpInvoiceHeadI.setInsertTime(new Date());
                // 新增数据
                decErpInvoiceHeadIMapper.insert(decErpInvoiceHeadI);
            }
            return "";
        }catch (Exception ex){
            LOGGER.error("批量新增失败：" + ex.getMessage());
            return ex.getMessage();
        }
    }
    /**
     * 功能描述:修改
     *
     * @param decErpInvoiceHeadIParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DecErpInvoiceHeadIDto update(DecErpInvoiceHeadIParam decErpInvoiceHeadIParam, UserInfoToken userInfo) {
        DecErpInvoiceHeadI decErpInvoiceHeadI = decErpInvoiceHeadIMapper.selectByPrimaryKey(decErpInvoiceHeadIParam.getSid());
        decErpInvoiceHeadIDtoMapper.updatePo(decErpInvoiceHeadIParam, decErpInvoiceHeadI);
        decErpInvoiceHeadI.setUpdateUser(userInfo.getUserNo());
        decErpInvoiceHeadI.setUpdateUserName(userInfo.getUserName());
        decErpInvoiceHeadI.setUpdateTime(new Date());
        // 更新数据
        int update = decErpInvoiceHeadIMapper.updateByPrimaryKey(decErpInvoiceHeadI);
        return update > 0 ? decErpInvoiceHeadIDtoMapper.toDto(decErpInvoiceHeadI) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
		decErpInvoiceHeadIMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<DecErpInvoiceHeadIDto> selectAll(DecErpInvoiceHeadIParam exportParam, UserInfoToken userInfo) {
        DecErpInvoiceHeadI decErpInvoiceHeadI = decErpInvoiceHeadIDtoMapper.toPo(exportParam);
        // decErpInvoiceHeadI.setTradeCode(userInfo.getCompany());
        List<DecErpInvoiceHeadIDto> decErpInvoiceHeadIDtos = new ArrayList<>();
        List<DecErpInvoiceHeadI> decErpInvoiceHeadIs = decErpInvoiceHeadIMapper.getList(decErpInvoiceHeadI);
        if (CollectionUtils.isNotEmpty(decErpInvoiceHeadIs)) {
            decErpInvoiceHeadIDtos = decErpInvoiceHeadIs.stream().map(head -> {
                DecErpInvoiceHeadIDto dto = decErpInvoiceHeadIDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decErpInvoiceHeadIDtos;
    }
}
