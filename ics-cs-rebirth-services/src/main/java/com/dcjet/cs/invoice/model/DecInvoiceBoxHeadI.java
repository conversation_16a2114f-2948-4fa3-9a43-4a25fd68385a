package com.dcjet.cs.invoice.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-6-12
 */
@Setter
@Getter
@RemoveTailingZero
@Table(name = "T_DEC_INVOICE_BOX_HEAD_I")
public class DecInvoiceBoxHeadI extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 关联编号（单据内部编号）
     */
	@Column(name = "EMS_LIST_NO")
	private  String emsListNo;
	/**
     * 发票号码
     */
	@Column(name = "INVOICE_NO")
	private  String invoiceNo;
	/**
     * 合同协议号
     */
	@Column(name = "CONTR_NO")
	private  String contrNo;
	/**
     * 客户代码
     */
	@Column(name = "SUPPLIER_CODE")
	private  String supplierCode;
	/**
     * 运输方式
     */
	@Column(name = "TRAF_MODE")
	private  String trafMode;
	/**
     * 货代编码
     */
	@Column(name = "FORWARD_CODE")
	private  String forwardCode;
	/**
     * 目的地
     */
	@Column(name = "DESTINATION")
	private  String destination;
	/**
     * 总毛重
     */
	@Column(name = "GROSS_WT")
	private  BigDecimal grossWt;
	/**
     * 总净重
     */
	@Column(name = "NET_WT")
	private  BigDecimal netWt;
	/**
     * 件数
     */
	@Column(name = "PACK_NUM")
	private  Integer packNum;
	/**
     * 包装种类
     */
	@Column(name = "WRAP_TYPE")
	private  String wrapType;
	/**
     * 出货日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "SHIPMENT_DATE")
	private  Date shipmentDate;
	/**
     * 出货日期-开始
     */
	@Transient
	private String shipmentDateFrom;
	/**
     * 出货日期-结束
     */
	@Transient
    private String shipmentDateTo;
	/**
     * 模板id
     */
	@Column(name = "TEMPLATE_HEAD_ID")
	private  String templateHeadId;
	/**
     * 状态 1：锁定 0：未锁定
     */
	@Column(name = "STATUS")
	private  String status;
	/**
     * 企业代码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 提单表头id
     */
	@Column(name = "ERP_HEAD_ID")
	private  String erpHeadId;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 模板编号
     */
	@Column(name = "TEMPLATE_CODE")
	private  String templateCode;
	/**
     * 客户名称
     */
	@Column(name = "SUPPLIER_NAME")
	private  String supplierName;
	/**
     * 货代名称
     */
	@Column(name = "FORWARD_NAME")
	private  String forwardName;
	/**
	 * 收货人代码
	 */
	@Column(name = "CONSIGNOR_CODE")
	private  String consignorCode;
	/**
	 * 收货人名称
	 */
	@Column(name = "CONSIGNOR_NAME")
	private  String consignorName;
	/**
	 * 收货人地址
	 */
	@Column(name = "CONSIGNOR_ADDR")
	private  String consignorAddr;
	/**
	 * 收货人电话
	 */
	@Column(name = "CONSIGNOR_TEL")
	private  String consignorTel;
	/**
	 * 启运港
	 */
	@Column(name = "DESP_PORT")
	private  String despPort;
	/**
	 * 目的港
	 */
	@Column(name = "DEST_PORT")
	private  String destPort;
	/**
	 * SHIP_FROM
	 */
	@Column(name = "SHIP_FROM")
	private  String shipFrom;
	/**
	 * 发票日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INVOICE_DATE")
	private  Date invoiceDate;
	/**
	 * 发票日期-开始
	 */
	@Transient
	private String invoiceDateFrom;
	/**
	 * 发票日期-结束
	 */
	@Transient
	private String invoiceDateTo;
	/**
	 * 币制
	 */
	@Column(name = "CURR")
	private  String curr;
	/**
	 * 货代
	 */
	@Column(name = "FORWARDER")
	private  String forwarder;
	/**
	 * 运输方式
	 */
	@Column(name = "TRAF_MODE_NAME")
	private  String trafModeName;
	/**
	 * 付款方式
	 */
	@Column(name = "PAYMENT_MODE")
	private  String paymentMode;
	/**
	 * 成交方式
	 */
	@Column(name = "TRANS_MODE_NAME")
	private  String transModeName;
	/**
	 * 银行信息
	 */
	@Column(name = "BANK_INFO")
	private  String bankInfo;

	/**
	 * 唛头
	 */
	@Column(name = "MARKS")
	private  String marks;

	/**
	 * SHIP_FROM_NAME
	 */
	@Column(name = "SHIP_FROM_NAME")
	private  String shipFromName;
	/**
	 * 提运单号
	 */
	@Column(name = "HAWB")
	private  String hawb;
	/**
	 * 航班日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "VOYAGE_DATE")
	private  Date voyageDate;
	/**
	 * 体积
	 */
	@Column(name = "VOLUME")
	private  BigDecimal volume;
	/**
	 * 启运国
	 */
	@Column(name = "TRADE_COUNTRY")
	private  String tradeCountry;
	/**
	 * 启运港(发票)
	 */
	@Column(name = "DESP_PORT_EN")
	private  String despPortEn;
	/***
	 * 汇率
	 */
	@Column(name = "EXCHANGE_RATE")
	private BigDecimal exchangeRate;
}
