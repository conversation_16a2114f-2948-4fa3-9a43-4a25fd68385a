<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.ztyth.dao.ZtythEmsImgExgMapper">
    <resultMap id="ztythHeadResultMap" type="com.dcjet.cs.ztyth.model.ZtythEmsHead">
        <result column="EMS_NO" property="emsNo" jdbcType="VARCHAR" />
        <result column="COP_EMS_NO" property="copEmsNo" jdbcType="VARCHAR" />
        <result column="BILL_FLAG" property="billFlag" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 列表查询 and 条件 begin-->
    <select id="getEmsNoList" resultMap="ztythHeadResultMap">
        SELECT
        EMS_NO,
        COP_EMS_NO,
        BILL_FLAG
        FROM
        T_JGT_EMS_HEAD
        <where>
            and TRADE_CODE = #{compCode}
        </where>
    </select>
    <select id="getAuditLogList" resultType="com.dcjet.cs.util.auditLog.GwAuditLogConfig">
        SELECT
        *
        FROM
        T_GW_AUDIT_LOG_CONFIG
    </select>
</mapper>
