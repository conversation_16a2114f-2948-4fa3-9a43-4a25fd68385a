package com.dcjet.cs.notice.service.bizData.decE;

import com.dcjet.cs.notice.service.bizData.BizData;
import com.dcjet.cs.notice.service.bizData.DecEBizData;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component(value = "DEC_E_SENDED")
public class DecESendedBizData implements BizData {

    @Resource
    private DecEBizData decEBizData;

    @Override
    public String replaceContent(String template, String tradeCode, String bizDataId) {
        return decEBizData.replaceContent(template, tradeCode, bizDataId);
    }
}
