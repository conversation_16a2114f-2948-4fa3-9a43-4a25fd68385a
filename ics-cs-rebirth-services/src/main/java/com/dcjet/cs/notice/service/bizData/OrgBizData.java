package com.dcjet.cs.notice.service.bizData;

import com.dcjet.cs.mat.dao.MatImgexgMapper;
import com.dcjet.cs.mat.dao.MatImgexgOrgMapper;
import com.dcjet.cs.mat.model.MatImgexg;
import com.dcjet.cs.mat.model.MatImgexgOrg;
import com.dcjet.cs.notice.service.bizData.BizData;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class OrgBizData implements BizData {
    @Resource
    private MatImgexgOrgMapper matImgexgOrgMapper;
    @Resource
    private PCodeHolder pCodeHolder;

    @Override
    public String replaceContent(String template, String tradeCode, String bizDataId) {
        if(StringUtils.isNotBlank(bizDataId)) {
            MatImgexgOrg matImgexg = matImgexgOrgMapper.selectByPrimaryKey(bizDataId);
            if (matImgexg == null) {
                throw new RuntimeException(String.format("sid:[%s],%s",bizDataId,xdoi18n.XdoI18nUtil.t("备案物料信息不存在")));
            }
        }

        Map<String, String> valuesMap = new HashMap<String, String>();
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        SimpleDateFormat sdfYMD = new SimpleDateFormat("YYYYMMdd");
        SimpleDateFormat sdfHM = new SimpleDateFormat("HH:mm");
        Date currDate = new Date();
        valuesMap.put("YYYYMMDD", sdfYMD.format(currDate));
        valuesMap.put("HHMM", sdfHM.format(currDate));
//        valuesMap.put("declareName", entry.getAgentName());
//        valuesMap.put("emsListNo", entry.getEmsListNo());
//        valuesMap.put("tradeName", entry.getTradeName());
//        valuesMap.put("OverseasShipperName", entry.getOverseasTradeName());
//        valuesMap.put("tradeCountry", pCodeHolder.getName(PCodeType.COUNTRY_OUTDATED, entry.getTradeCountry()).orElse(""));
//        valuesMap.put("trafMode", pCodeHolder.getName(PCodeType.TRANSF, entry.getTrafMode()).orElse(""));
//        valuesMap.put("entryPort", pCodeHolder.getName(PCodeType.CIQ_ENTY_PORT, entry.getDespPortCode()).orElse(""));
//        valuesMap.put("billNo", entry.getBillNo());
//        valuesMap.put("packNum",entry.getPackNo()==null?"":entry.getPackNo().toString());
//        valuesMap.put("grossWt",entry.getGrossWt()==null?"":entry.getGrossWt().toString());

        return sub.replace(template);
    }
}
