package com.dcjet.cs.notice.service.bizData.entryI;

import com.dcjet.cs.notice.service.bizData.BizData;
import com.dcjet.cs.notice.service.bizData.EntryIBizData;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 报关单复核退回
 */
@Component(value = "ENTRY_I_INSPECTION")
public class EntryIInspectionBizData implements BizData {

    @Resource
    private EntryIBizData entryIBizData;


    @Override
    public String replaceContent(String template, String tradeCode, String bizDataId) {
        return entryIBizData.replaceContent(template, tradeCode, bizDataId);
    }
}
