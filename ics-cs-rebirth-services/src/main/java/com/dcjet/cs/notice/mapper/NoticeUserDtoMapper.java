package com.dcjet.cs.notice.mapper;

import com.dcjet.cs.dto.notice.NoticeUserDto;
import com.dcjet.cs.dto.notice.NoticeUserParam;
import com.dcjet.cs.notice.model.NoticeUser;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-5-19
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NoticeUserDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    NoticeUserDto toDto(NoticeUser po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    NoticeUser toPo(NoticeUserParam param);
    /**
     * 数据库原始数据更新
     * @param noticeUserParam
     * @param noticeUser
     */
    void updatePo(NoticeUserParam noticeUserParam, @MappingTarget NoticeUser noticeUser);
    default void patchPo(NoticeUserParam noticeUserParam, NoticeUser noticeUser) {
        // TODO 自行实现局部更新
    }
}
