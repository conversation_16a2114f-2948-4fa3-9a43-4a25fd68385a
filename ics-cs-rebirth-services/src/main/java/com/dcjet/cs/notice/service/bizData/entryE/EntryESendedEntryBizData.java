package com.dcjet.cs.notice.service.bizData.entryE;

import com.dcjet.cs.notice.service.bizData.BizData;
import com.dcjet.cs.notice.service.bizData.EntryBizData;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 出口报关单草单已发送
 */
@Component(value = "ENTRY_E_SENDED")
public class EntryESendedEntryBizData implements BizData {

    @Resource
    private EntryBizData entryBizData;


    @Override
    public String replaceContent(String template, String tradeCode, String bizDataId) {
        return entryBizData.replaceContent(template, tradeCode, bizDataId);
    }
}