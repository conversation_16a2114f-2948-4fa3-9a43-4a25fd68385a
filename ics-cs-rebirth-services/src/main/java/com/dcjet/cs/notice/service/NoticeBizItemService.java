package com.dcjet.cs.notice.service;

import com.dcjet.cs.notice.dao.NoticeBizItemMapper;
import com.dcjet.cs.notice.model.NoticeBizItem;
import com.xdo.common.base.service.BaseService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-5-19
 */
@Service
public class NoticeBizItemService extends BaseService<NoticeBizItem> {
    @Resource
    private NoticeBizItemMapper noticeBizItemMapper;
    @Override
    public Mapper<NoticeBizItem> getMapper() {
        return noticeBizItemMapper;
    }
}
