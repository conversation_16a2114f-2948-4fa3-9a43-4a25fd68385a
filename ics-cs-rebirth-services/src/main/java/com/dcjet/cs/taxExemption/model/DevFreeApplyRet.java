package com.dcjet.cs.taxExemption.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-5-26
 */
@Setter
@Getter
@Table(name = "T_DEV_FREE_APPLY_RET")
public class DevFreeApplyRet implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 数据中心统一编号
     */
    @Column(name = "ETPS_PREENT_NO")
    private String etpsPreentNo;
    /**
     * 处理结果
     */
    @Column(name = "MANAGE_RESULT")
    private String manageResult;
    /**
     * 回执类型
     */
    @Column(name = "MSG_TYPE")
    private String msgType;
    /**
     * 报文类型
     */
    @Column(name = "MESSAGE_SUB_TYPE")
    private String messageSubType;
    /**
     * 报文编号
     */
    @Column(name = "MESSAGE_ID")
    private String messageId;
    /**
     * 项目统一编号
     */
    @Column(name = "PROJECT_ID")
    private String projectId;
    /**
     * 征免税客户端统一编号
     */
    @Column(name = "CLIENT_SEQ_NO")
    private String clientSeqNo;
    /**
     * 备注信息
     */
    @Column(name = "CHECKINFO")
    private String checkinfo;
    /**
     *
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    @Column(name = "note_time")
    private String noteTime;

    /**
     * 征免税证明海关编号
     */
    @Column(name = "PRJZ_NO")
    private String prjZNo;

    @Transient
    private String tradeCode;
}
