package com.dcjet.cs.plane.service;

import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.plane.dao.GwImpTmpEApplyOtmMapper;
import com.dcjet.cs.plane.model.GwImpTmpEApplyOTM;
import com.google.common.base.Strings;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.dataimport.base.ImportHandlerInterface;
import com.xdo.dataimport.model.EnumCollection;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import com.xdo.pcode.service.PCodeHolder;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 业务系统校验数据、持久化数据的业务类（采用临时表校验，只要用到临时表就算临时表校验）
 * 临时表校验需要在校验方法checkData中返回TempOwnerId，并在持久化数据方法persistData中接收
 * 导入服务在发现业务采用了临时表校验（即checkData方法返回的结果包含TempOwnerId）时，
 * 在调用persistData方法时便不会把正确数据文件重新解析传递给业务方法，减少不必要的解析时间
 */
@Service
public class GwDecEApplyOTMImportService extends BaseService<GwImpTmpEApplyOTM> implements ImportHandlerInterface {
    @Resource
    private BulkInsertConfig bulkInsertConfig;
    public IBulkInsert getBulkInsertInstance() throws Exception{
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }

    @Resource
    private GwImpTmpEApplyOtmMapper mapper;

    @Resource
    private PCodeHolder pCodeHolder;

    @Override
    public Mapper<GwImpTmpEApplyOTM> getMapper() {
        return mapper;
    }

    // 0：正确数据 1：错误数据 2：警告数据 对应于EnumCollection.EntityListType的枚举的ordinal
    public final int CORRECT_FLAG = EnumCollection.EntityListType.CORRECT_LIST.ordinal();
    public final int WRONG_FLAG = EnumCollection.EntityListType.WRONG_LIST.ordinal();
    public final int WARN_FLAG = EnumCollection.EntityListType.WARN_LIST.ordinal();

    /**
     * 返回当前业务类对应的任务类型，导入服务执行器将根据此方法的返回值获取相应的业务类实例
     * @return 当前业务类对应的任务类型
     */
    @Override
    public String getTaskCode() {
        String strTaskCode = "APPLY_E_OTM";
        return strTaskCode;
    }

    /**
     * 业务校验方法（临时表校验）
     * @param mapBasicParam 任务基础参数(当前任务的sid、taskCode、tradeCode、importType、insertUserOid、insertUserName)
     * @param mapBusinessParam 业务自定义参数(比如insertUser、插入表体时用到的表头id等都可以在这里传入,需在前端导入时确定需要用到的参数并传递)
     * @param mapObjectList 实体列表map，可根据xmlContent的sheet的id获取某类实体列表
     * @return 业务校验结果，head根据xmlContent的sheet的field获取，无需设置；当采用临时表校验方式时tempOwnerId必须设置，否则插入正确数据时无法进行操作
     */
    @Override
    public List<ExcelImportDto> checkData(Map<String,Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList) throws Exception {
        List<ExcelImportDto> excelImportDtoList=new ArrayList<>();
        if(mapObjectList.size() <= 0){
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }
        List<Object> objectList=mapObjectList.entrySet().iterator().next().getValue();
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        //从自定义参数中获取需要的数据
        if(!mapBusinessParam.containsKey("insertUser")){
            XdoImportLogger.log("部分自定义参数未成功获取");
            return null;
        }
        String insertUser = mapBusinessParam.get("insertUser").toString();
        String insertUserName = mapBasicParam.get("insertUserName").toString();

        //遍历当前实体列表进行校验
        String guid = UUID.randomUUID().toString();
        int intObjectListSize = objectList.size();
        XdoImportLogger.log("共获取实体：" + intObjectListSize);
        List<GwImpTmpEApplyOTM> impDecEApplyList = Collections.synchronizedList(new ArrayList<>(intObjectListSize));
        AtomicReference<Integer> index= new AtomicReference<>(1);

        objectList.parallelStream().forEachOrdered(pa -> {
            GwImpTmpEApplyOTM el = (GwImpTmpEApplyOTM)pa;
            if(Strings.isNullOrEmpty(el.getTempRemark())){
                el.setTempRemark("");
                el.setTempFlag(0);
            }
            if(el.getCcetdDate()!=null&&!isLegalDate(el.getCcetdDate().length(),el.getCcetdDate(),"yyyy-MM-dd"))
            {
                el.setTempRemark(el.getTempRemark()+"订舱订舱开船日不正确|");
                el.setTempFlag(1);
            }
            String bondMarkErp=mapper.selectBondMarkByErp(el);
            List<String> bondMarkMat=mapper.selectBondMarkByMat(el);
            if(bondMarkMat==null||bondMarkMat.size()<=0)
            {
                el.setTempRemark(el.getTempRemark()+"料号未备案通过|");
                el.setTempFlag(WARN_FLAG);
            }
            if(bondMarkMat!=null&&bondMarkMat.size()==1)
            {
                if(bondMarkErp!=null&&bondMarkMat.get(0)!=null&&!bondMarkErp.equals(bondMarkMat.get(0)))
                {
                    el.setTempRemark(el.getTempRemark()+"保完税标记不正确|");
                    el.setTempFlag(WARN_FLAG);
                }
            }

            el.setSid(UUID.randomUUID().toString());
            el.setTempOwner(guid);
            el.setTradeCode(tradeCode);
            el.setInsertUser(insertUser);
            el.setInsertUserName(insertUserName);
            el.setInsertTime(new Date());

            impDecEApplyList.add(el);
            index.getAndSet(index.get() + 1);
        });

        XdoImportLogger.log("共获取插入实体："+impDecEApplyList.size()+";执行快速入库操作");
        //初始化快速入库实例（在config文件夹下新建OracleBulkConfig用于读取配置文件中的数据库连接配置）
        //使用快速入库时，拼接的sql语句会根据当前实体进行，如果某个字段存在于实体定义中，但传入的值为空，那么这个字段在数据表存在的默认值将无效
        // 所以请显示赋值，或者，如果要使用数据表的默认值请将此字段从实体中去除
        IBulkInsert iBulkInsert = getBulkInsertInstance();
        int intEffectCount = 0;
        try{
            intEffectCount = iBulkInsert.fastImport(impDecEApplyList);
        }
        catch (BulkInsertException ex){
            intEffectCount = ex.getCommitRowCount();
            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
            XdoImportLogger.log(ex);
            return null;
        }
        catch (Exception ex){
            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
            XdoImportLogger.log(ex);
            return null;
        }
        //调用存储过程校验，对字段的基本校验(数据类型、非空、长度、精度等)尽量放在校验实体的注解上
        //如果有特殊情况必须在存储过程里做，不要用 !='' 来判断字段是否非空，因为oracle里''会被当作null处理，而判断null不能用不等于号，因此不会得到预想的结果

        mapper.checkListOtm(guid);

        XdoImportLogger.log("执行存储过程校验");

        ExcelImportDto<GwImpTmpEApplyOTM> excelImportDto = new ExcelImportDto<>();
        excelImportDto.setTempOwnerId(guid);
        //获取正确、错误、警告数据列表(警告数据根据各自业务需求设置,可不加)
        Map<String, Object> param = new HashMap<>(2);
        param.put("TEMP_OWNER", guid);
        List flags = new ArrayList();
        //正确数据包含警告数据，因此要把警告数据添加到正确数据列表中，否则警告数据无法导入
        //flags.add(WARN_FLAG);
        //param.put("TEMP_FLAG", flags);
        //excelImportDto.setWarnList(selectDataList(param));
        flags.add(CORRECT_FLAG);
        param.put("TEMP_FLAG", flags);
        List<GwImpTmpEApplyOTM> correctList = selectDataList(param);
        excelImportDto.setCorrectNumber(correctList.size());
        excelImportDto.setCorrectList(correctList);
        flags.clear();
        flags.add(WARN_FLAG);
        param.put("TEMP_FLAG", flags);
        List<GwImpTmpEApplyOTM> warnList = selectDataList(param);
        excelImportDto.setWarnNumber(warnList.size());
        excelImportDto.setWarnList(warnList);

        flags.clear();
        flags.add(WRONG_FLAG);
        param.put("TEMP_FLAG", flags);
        List<GwImpTmpEApplyOTM> wrongList = selectDataList(param);
        excelImportDto.setWrongNumber(wrongList.size());
        if(wrongList.size()>0 && correctList.size()>0 && warnList.size()>0){
            wrongList.addAll(correctList);
            wrongList = wrongList.stream().sorted(Comparator.comparing(GwImpTmpEApplyOTM::getTempIndex)).collect(Collectors.toList());
        }
        excelImportDto.setWrongList(wrongList);
        XdoImportLogger.log("correct："+excelImportDto.getCorrectList().size()
                +";wrong："+excelImportDto.getWrongList().size()
                +";warn："+excelImportDto.getWarnList().size());
        excelImportDtoList.add(excelImportDto);

        return excelImportDtoList;
    }

    /**
     * 根据时间 和时间格式 校验是否正确
     * @param length 校验的长度
     * @param sDate 校验的日期
     * @param format 校验的格式
     * @return
     */
    public static boolean isLegalDate(int length, String sDate,String format) {
        int legalLen = length;
        if ((sDate == null) || (sDate.length() != legalLen)) {
            return false;
        }
        DateFormat formatter = new SimpleDateFormat(format);
        try {
            Date date = formatter.parse(sDate);
            return sDate.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 业务持久化数据方法
     * @param mapBasicParam 任务基础参数(当前任务的sid、taskCode、tradeCode、importType、insertUserOid、insertUserName)
     * @param mapBusinessParam 业务自定义参数(比如insertUser、插入表体时用到的表头id等都可以在这里传入,需在前端导入时确定需要用到的参数并传递)
     * @param mapObjectList 正确数据序列化的实体列表集合，根据导入配置文件(xmlContent)的每个sheet配置的id获取相应实体列表（采用内存校验时传递）
     * @param tempOwnerIdList 临时表处理批次Id，单sheet时取第一条（采用临时表校验时传递）
     * @return 业务持久化数据结果，导入服务根据返回值设置任务状态
     */
    @Override
    public ResultObject persistData(Map<String,Object> mapBasicParam, Map<String, Object> mapBusinessParam, Map<String, List<Object>> mapObjectList, List<String> tempOwnerIdList)  throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, "新增成功");
        //从自定义参数中获取需要的数据
        String tradeCode = mapBasicParam.get("tradeCode").toString();
        String insertUser = mapBusinessParam.get("insertUser").toString();
        String insertUserName = mapBasicParam.get("insertUserName").toString();
        //按配置顺序（同时也是业务校验时添加到excelImportDtoList的顺序）获取临时表批次Id
        if(tempOwnerIdList==null || tempOwnerIdList.size()<=0){
            throw new ErrorException(400, "临时表处理批次Id为空，无法执行持久化数据方法");
        }
        String strTempOwnerId = tempOwnerIdList.get(0);
        mapper.extractionOtm(strTempOwnerId,tradeCode,insertUser,new Date(),insertUserName);
        return resultObject;
    }

    /**
     * 描述：获取导入的各种类型数据
     *
     * @param param
     * @return
     * <AUTHOR>
     * @date 2019-03-16
     */
    public List<GwImpTmpEApplyOTM> selectDataList(Map<String, Object> param) {
        return mapper.selectByFlag(param);
    }

}