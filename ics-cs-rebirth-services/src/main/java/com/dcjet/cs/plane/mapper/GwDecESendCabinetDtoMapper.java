package com.dcjet.cs.plane.mapper;

import com.dcjet.cs.dto.plane.*;
import com.dcjet.cs.plane.model.GwDecESendCabinet;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-1-25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwDecESendCabinetDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwDecESendCabinetDto toDto(GwDecESendCabinet po);

    GwDecESendCabinetSelectDto toSelectDto(GwDecESendCabinet po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwDecESendCabinet toPo(GwDecESendCabinetParam param);

    GwDecESendCabinet toSelectPo(GwDecESendCabinetSelectParam param);
    /**
     * 数据库原始数据更新
     * @param decESendCabinetParam
     * @param decESendCabinet
     */
    void updatePo(GwDecESendCabinetParam decESendCabinetParam, @MappingTarget GwDecESendCabinet decESendCabinet);
    void updateSelectPo(GwDecESendCabinetSelectParam decESendCabinetSelectParam, @MappingTarget GwDecESendCabinet decESendCabinet);
    default void patchPo(GwDecESendCabinetParam decESendCabinetParam, GwDecESendCabinet decESendCabinet) {
        // TODO 自行实现局部更新
    }
}
