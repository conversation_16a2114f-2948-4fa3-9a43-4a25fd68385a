<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.plane.dao.GwDecESendHeadMapper">
    <resultMap id="decESendHeadResultMap" type="com.dcjet.cs.plane.model.GwDecESendHead">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="booking_list_no" property="bookingListNo" jdbcType="VARCHAR" />
		<result column="forward_code" property="forwardCode" jdbcType="VARCHAR" />
		<result column="forward_name" property="forwardName" jdbcType="VARCHAR" />
		<result column="booking_id" property="bookingId" jdbcType="VARCHAR" />
		<result column="hawb" property="hawb" jdbcType="VARCHAR" />
		<result column="etd" property="etd" jdbcType="TIMESTAMP" />
		<result column="ctn" property="ctn" jdbcType="VARCHAR" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="traf_mode" property="trafMode" jdbcType="VARCHAR" />
		<result column="traf_name" property="trafName" jdbcType="VARCHAR" />
		<result column="cietd_date" property="cietdDate" jdbcType="TIMESTAMP" />
		<result column="ccetd_date" property="ccetdDate" jdbcType="TIMESTAMP" />
		<result column="destination_country" property="destinationCountry" jdbcType="VARCHAR" />
		<result column="dest_port" property="destPort" jdbcType="VARCHAR" />
		<result column="client_code" property="clientCode" jdbcType="VARCHAR" />
		<result column="delivery_no" property="deliveryNo" jdbcType="VARCHAR" />
		<result column="trade_terms" property="tradeTerms" jdbcType="VARCHAR" />
		<result column="trade_area" property="tradeArea" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
        <result column="client_name" property="clientName" jdbcType="VARCHAR" />
        <result column="delivery_name" property="deliveryName" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="forward_no" property="forwardNo" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     t.sid
     ,t.trade_code
     ,t.insert_user
     ,t.insert_time
     ,t.update_user
     ,t.update_time
     ,t.insert_user_name
     ,t.update_user_name
     ,t.status
     ,t.booking_list_no
     ,t.forward_code
     ,t.forward_name
     ,t.booking_id
     ,t.hawb
     ,t.etd
     ,t.ctn
     ,t.note
     ,t.traf_mode
     ,t.traf_name
     ,t.cietd_date
     ,t.ccetd_date
     ,t.destination_country
     ,t.dest_port
     ,t.client_code
     ,t.delivery_no
     ,t.trade_terms
     ,t.trade_area
     ,t.head_id
     ,t.forward_no
     ,t.remark
    </sql>
    <sql id="condition">
        and t.trade_code = #{tradeCode}
    <if test="sid != null and sid != ''">
        and t.sid = #{sid}
    </if>
    <if test="insertUserName != null and insertUserName != ''">
		and t.insert_user_name = #{insertUserName}
	</if>
    <if test="updateUserName != null and updateUserName != ''">
		and t.update_user_name = #{updateUserName}
	</if>
    <if test="status != null and status != ''">
		and t.status = #{status}
	</if>
    <if test="bookingListNo != null and bookingListNo != ''">
		and t.booking_list_no like concat(concat('%',#{bookingListNo}),'%')
	</if>
    <if test="forwardCode != null and forwardCode != ''">
		and t.forward_code = #{forwardCode}
	</if>
    <if test="forwardName != null and forwardName != ''">
		and t.forward_name = #{forwardName}
	</if>
    <if test="bookingId != null and bookingId != ''">
		and t.booking_id like concat(concat('%',#{bookingId}),'%')
	</if>
    <if test="hawb != null and hawb != ''">
		and t.hawb like concat(concat('%',#{hawb}),'%')
	</if>
        <if test="_databaseId == 'oracle' and etdFrom != null and etdFrom != ''">
            <![CDATA[ and t.etd >= to_date(#{etdFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and etdTo != null and etdTo != ''">
            <![CDATA[ and t.etd <= to_date(#{etdTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and etdFrom != null and etdFrom != ''">
            <![CDATA[ and t.etd >= to_timestamp(#{etdFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="_databaseId == 'postgresql' and etdTo != null and etdTo != ''">
            <![CDATA[ and t.etd <= to_timestamp(#{etdTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp ]]>
        </if>
    <if test="ctn != null and ctn != ''">
		and t.ctn = #{ctn}
	</if>
    <if test="note != null and note != ''">
		and t.note like concat(concat('%',#{note}),'%')
	</if>
    <if test="trafMode != null and trafMode != ''">
		and t.traf_mode = #{trafMode}
	</if>
    <if test="trafName != null and trafName != ''">
		and t.traf_name = #{trafName}
	</if>
        <if test="_databaseId == 'oracle' and cietdDateFrom != null and cietdDateFrom != ''">
            <![CDATA[ and t.cietd_date >= to_date(#{cietdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and cietdDateTo != null and cietdDateTo != ''">
            <![CDATA[ and t.cietd_date <= to_date(#{cietdDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and cietdDateFrom != null and cietdDateFrom != ''">
            <![CDATA[ and t.cietd_date >= to_date(#{cietdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
        </if>
        <if test="_databaseId == 'postgresql' and cietdDateTo != null and cietdDateTo != ''">
            <![CDATA[ and t.cietd_date <= to_date(#{cietdDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
        </if>
        <if test="_databaseId == 'oracle' and ccetdDateFrom != null and ccetdDateFrom != ''">
            <![CDATA[ and t.ccetd_date >= to_date(#{ccetdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and ccetdDateTo != null and ccetdDateTo != ''">
            <![CDATA[ and t.ccetd_date <= to_date(#{ccetdDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and ccetdDateFrom != null and ccetdDateFrom != ''">
            <![CDATA[ and t.ccetd_date >= to_date(#{ccetdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
        </if>
        <if test="_databaseId == 'postgresql' and ccetdDateTo != null and ccetdDateTo != ''">
            <![CDATA[ and t.ccetd_date <= to_date(#{ccetdDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
        </if>
    <if test="destinationCountry != null and destinationCountry != ''">
		and t.destination_country = #{destinationCountry}
	</if>
    <if test="destPort != null and destPort != ''">
		and t.dest_port = #{destPort}
	</if>
    <if test="clientCode != null and clientCode != ''">
		and t.client_code = #{clientCode}
	</if>
    <if test="deliveryNo != null and deliveryNo != ''">
		and t.delivery_no like concat(concat('%',#{deliveryNo}),'%')
	</if>
    <if test="tradeTerms != null and tradeTerms != ''">
		and t.trade_terms = #{tradeTerms}
	</if>
    <if test="tradeArea != null and tradeArea != ''">
		and t.trade_area = #{tradeArea}
	</if>
    <if test="headId != null and headId != ''">
		and t.head_id = #{headId}
	</if>
        <if test="forwardNo != null and forwardNo != ''">
            and t.forward_no like concat(concat('%',#{forwardNo}),'%')
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="decESendHeadResultMap" parameterType="com.dcjet.cs.plane.model.GwDecESendHead">
        SELECT
        <include refid="Base_Column_List" />
        ,p.company_name as client_Name
        ,p.address
        ,c.company_name as delivery_Name
        FROM
        t_gw_dec_e_send_head t
        left join t_bi_client_information p on t.trade_code=p.trade_code and t.client_Code=p.customer_code and p.customer_type='CLI'
        left join t_bi_client_information c on t.trade_code=c.trade_code and t.delivery_No=c.customer_code and c.customer_type='CLI'
        <where>
            <include refid="condition"></include>
        </where>
        order by t.status,t.insert_time desc,t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        <if test='_databaseId != "postgresql" '>
            begin
        </if>
        update t_gw_dec_e_plan_head set status='0' where sid in
        (
        select head_id from t_gw_dec_e_send_head where sid in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        );
        delete from t_gw_dec_e_send_list t where t.head_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        delete from t_gw_dec_e_send_cabinet t where t.head_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        delete from t_gw_dec_e_send_head t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        delete from T_COST_LIST_E t where t.head_id in (select sid from T_COST_HEAD_E where bill_head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>);
        delete from T_COST_HEAD_E t where t.bill_head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        <if test='_databaseId != "postgresql" '>
            end;
        </if>
    </delete>
    <delete id="deleteDetailBySids" parameterType="java.util.List">
        <if test='_databaseId != "postgresql" '>
            begin
        </if>
        delete from t_gw_dec_e_send_list t where t.head_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        delete from t_gw_dec_e_send_cabinet t where t.head_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        update  t_gw_dec_e_send_head t set status='0' where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        <if test='_databaseId != "postgresql" '>
            end;
        </if>
    </delete>

    <select id="getListBySids" resultMap="decESendHeadResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_gw_dec_e_send_head t where  sid in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>
    <select id="getCountByHeadIds" resultType="java.lang.Integer">
        SELECT
         count(1)
        FROM
        t_gw_dec_e_send_head t where status in ('1','2','3','4') and  head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>
<!--    <select id="getSendListBySids" resultType="com.dcjet.cs.dto.plane.DecESendApiParam">-->
<!--        SELECT-->
<!--        t.send_no as gwSeq,-->
<!--        t.linked_no as vbeln,-->
<!--        t.line_no as posnr,-->
<!--        t.plan_line_no||COALESCE(lpad(t.split_line_no::VARCHAR,2,'0'),'') as etenr,-->
<!--        t.fac_g_no as matnr,-->
<!--        round(t.qty,3) as qty,-->
<!--        #{sendDate} as ddate,-->
<!--        COALESCE(head.hawb,'')||t.branch_no as branchno-->
<!--        FROM-->
<!--        t_gw_dec_e_send_list t-->
<!--        left join t_gw_dec_e_send_head head on t.head_id=head.sid where  t.status in ('0','4') and t.head_id in-->
<!--        <foreach collection="sids"  item="item" open="(" separator="," close=")"  >-->
<!--            #{item}-->
<!--        </foreach>-->
<!--    </select>-->

    <select id="getHeadList" parameterType="com.dcjet.cs.plane.model.GwDecESendHeadList"
            resultType="com.dcjet.cs.plane.model.GwDecESendHeadList">
        SELECT
        t.sid
        ,t.trade_code
        ,t.insert_user
        ,t.insert_time
        ,t.update_user
        ,t.update_time
        ,t.insert_user_name
        ,t.update_user_name
        ,t.status
        ,t.booking_list_no
        ,t.forward_code
        ,t.forward_name
        ,t.booking_id
        ,t.hawb
        ,t.etd
        ,t.ctn
        ,t.traf_mode
        ,t.traf_name
        ,t.cietd_date
        ,t.ccetd_date
        ,t.destination_country
        ,t.dest_port
        ,t.client_code
        ,t.delivery_no
        ,t.trade_terms
        ,t.trade_area
        ,t.forward_no
        ,t.note as remark
        ,p.company_name as client_Name
        ,p.address
        ,c.company_name as delivery_Name
        ,l.linked_no
        ,l.line_no
        ,l.plan_line_no
        ,l.bond_mark
        ,l.g_mark
        ,l.fac_g_no
        ,l.qty
        ,l.unit_erp
        ,l.dec_price
        ,l.dec_total
        ,l.curr
        ,l.net_wt
        ,l.gross_wt
        ,l.so_no
        ,l.so_line_no
        ,l.so_date
        ,l.invoice_no
        ,l.delivery_po_no
        ,l.delivery_po_line_no
        ,l.sold_po_no
        ,l.sold_po_line_no
        ,l.last_modify_date
        ,l.district_code
        ,l.market_order_no
        ,l.container_num
        ,l.pack_num
        ,l.volume
        ,l.refused_reason
        ,l.customer_g_no
        ,l.head_id
        ,l.list_sid
        ,l.bill_no
        ,COALESCE(t.hawb,'')||l.branch_no as branch_no
        ,l.cabinet_date
        ,l.status as list_Status
        ,l.cabinet_sid
        ,l.cabinet_type
        ,l.container_no
        ,l.extract_time
        ,l.g_name
        ,l.duty_mode
        ,l.send_no
        ,l.note
        ,l.shipment_id
        from
        t_gw_dec_e_send_head t
        left join t_gw_dec_e_send_list l on t.sid=l.head_id
        left join t_bi_client_information p on t.trade_code=p.trade_code and t.client_code=p.customer_code and p.customer_type='CLI'
        left join t_bi_client_information c on t.trade_code=c.trade_code and t.delivery_no=c.customer_code and c.customer_type='CLI'
        <where>
            <include refid="headCondition"></include>
        </where>
        order by t.etd desc NULLS LAST,t.booking_list_no desc,l.branch_no,l.container_no,t.sid
    </select>
    <sql id="headCondition">
        and t.trade_code = #{tradeCode}
        <choose>
            <when test="shipmentId != null and shipmentId.indexOf(',') !=-1">
                and l.shipment_id in
                <foreach item="item" index="index" collection="shipmentId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <when test="shipmentId != null and shipmentId != ''">
                and l.shipment_id like concat(concat('%',#{shipmentId}),'%')
            </when>
        </choose>
        <if test="listStatus != null and listStatus != ''">
            and l.status = #{listStatus}
        </if>
        <if test="status != null and status != ''">
            and t.status = #{status}
        </if>
        <if test="bookingListNo != null and bookingListNo != ''">
            and t.booking_list_no like concat(concat('%',#{bookingListNo}),'%')
        </if>
        <if test="bookingId != null and bookingId != ''">
            and t.booking_id like concat(concat('%',#{bookingId}),'%')
        </if>
        <if test="hawb != null and hawb != ''">
            and t.hawb like concat(concat('%',#{hawb}),'%')
        </if>
        <if test="note != null and note != ''">
            and t.note like concat(concat('%',#{note}),'%')
        </if>
        <if test="_databaseId == 'oracle' and cietdDateFrom != null and cietdDateFrom != ''">
            <![CDATA[ and t.cietd_date >= to_date(#{cietdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and cietdDateTo != null and cietdDateTo != ''">
            <![CDATA[ and t.cietd_date <= to_date(#{cietdDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and cietdDateFrom != null and cietdDateFrom != ''">
            <![CDATA[ and t.cietd_date >= to_date(#{cietdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
        </if>
        <if test="_databaseId == 'postgresql' and cietdDateTo != null and cietdDateTo != ''">
            <![CDATA[ and t.cietd_date <= to_date(#{cietdDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
        </if>
        <if test="_databaseId == 'oracle' and ccetdDateFrom != null and ccetdDateFrom != ''">
            <![CDATA[ and t.ccetd_date >= to_date(#{ccetdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and ccetdDateTo != null and ccetdDateTo != ''">
            <![CDATA[ and t.ccetd_date <= to_date(#{ccetdDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and ccetdDateFrom != null and ccetdDateFrom != ''">
            <![CDATA[ and t.ccetd_date >= to_date(#{ccetdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
        </if>
        <if test="_databaseId == 'postgresql' and ccetdDateTo != null and ccetdDateTo != ''">
            <![CDATA[ and t.ccetd_date <= to_date(#{ccetdDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
        </if>
        <if test="clientCode != null and clientCode != ''">
            and t.client_code = #{clientCode}
        </if>
        <if test="deliveryNo != null and deliveryNo != ''">
            and t.delivery_no like concat(concat('%',#{deliveryNo}),'%')
        </if>
        <if test="forwardNo != null and forwardNo != ''">
            and t.forward_no like concat(concat('%',#{forwardNo}),'%')
        </if>
        <if test="linkedNo != null and linkedNo != ''">
            and l.linked_no like concat(concat('%',#{linkedNo}),'%')
        </if>
        <if test="lineNo != null and lineNo != ''">
            and l.line_no = #{lineNo}
        </if>
        <if test="facGNo != null and facGNo != ''">
            and l.fac_g_no like concat(concat('%',#{facGNo}),'%')
        </if>
        <if test="GName != null and GName != ''">
            and l.g_name like concat(concat('%',#{GName}),'%')
        </if>
        <if test="soNo != null and soNo != ''">
            and l.so_no like concat(concat('%',#{soNo}),'%')
        </if>
        <if test="soLineNo != null">
            and l.so_line_no = #{soLineNo}
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
            and l.invoice_no like concat(concat('%',#{invoiceNo}),'%')
        </if>
        <if test="cabinetType != null and cabinetType != ''">
            and l.cabinet_type = #{cabinetType}
        </if>
        <if test="containerNo != null and containerNo != ''">
            and l.container_no = #{containerNo}
        </if>
        <if test="_databaseId == 'oracle' and cabinetDateFrom != null and cabinetDateFrom != ''">
            <![CDATA[ and l.cabinet_date >= to_date(#{cabinetDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and cabinetDateTo != null and cabinetDateTo != ''">
            <![CDATA[ and l.cabinet_date <= to_date(#{cabinetDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and cabinetDateFrom != null and cabinetDateFrom != ''">
            <![CDATA[ and l.cabinet_date >= to_timestamp(#{cabinetDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="_databaseId == 'postgresql' and cabinetDateTo != null and cabinetDateTo != ''">
            <![CDATA[ and l.cabinet_date <= to_timestamp(#{cabinetDateTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp ]]>
        </if>
        <if test="branchNo != null and branchNo != ''">
            and COALESCE(t.hawb,'')||l.branch_no like concat(concat('%',#{branchNo}),'%')
        </if>
        <if test="billNo != null and billNo != ''">
            and l.bill_no like concat(concat('%',#{billNo}),'%')
        </if>
        <if test="sendNo != null and sendNo != ''">
            and l.send_no like concat(concat('%',#{sendNo}),'%')
        </if>
    </sql>

<!--    <select id="getCoutByHawb" resultType="java.lang.Integer">-->
<!--    select count(1) from (-->
<!--        select distinct trade_code-->
<!--    ,head_id-->
<!--    ,cabinet_type-->
<!--    ,container_no-->
<!--    ,cabinet_date from t_gw_dec_e_scheduling_list where head_id-->
<!--    in (select sid from t_gw_dec_e_scheduling_head  where trade_code=#{tradeCode} and hawb=#{hawb})) A;-->
<!--    </select>-->
<!--    <select id="getSendCoutByHawb" resultType="java.lang.Integer">-->
<!--    select count(1) from (-->
<!--    select distinct trade_code-->
<!--    ,head_id-->
<!--    ,cabinet_type-->
<!--    ,container_no-->
<!--    ,cabinet_date from t_gw_dec_e_send_list where head_id-->
<!--    in (select sid from t_gw_dec_e_send_head  where trade_code=#{tradeCode} and hawb=#{hawb})) A;-->
<!--    </select>-->


<!--    <select id="getPrintList" resultType="com.dcjet.cs.plane.model.DecEPrintList">-->
<!--        select dense_rank ( ) over (-->
<!--            order by-->
<!--           A.cabinet_type,-->
<!--           A.container_no,-->
<!--					 A.cabinet_date-->
<!--            ) bill_gno,A.* from (SELECT-->
<!--            l.delivery_Po_No,-->
<!--            l.so_No,-->
<!--            l.so_Line_No,-->
<!--            l.fac_g_no,-->
<!--            l.g_name,-->
<!--            l.qty,-->
<!--            l.pack_num,-->
<!--            l.container_Num,-->
<!--            l.cabinet_type,-->
<!--            COALESCE(head.hawb,'')||l.branch_no as branch_no,-->
<!--            erplist.gross_wt,-->
<!--            erplist.volume,-->
<!--            slist.cabinet_date,-->
<!--            l.bill_no,-->
<!--            l.market_Order_No,-->
<!--			l.container_no-->
<!--        FROM-->
<!--            t_gw_dec_e_scheduling_list slist-->
<!--            LEFT JOIN t_gw_dec_e_scheduling_head head ON head.sid = slist.head_id-->
<!--            LEFT JOIN t_gw_dec_e_send_list l ON head.bill_no=l.bill_no and slist.container_no=l.container_no-->
<!--            LEFT JOIN t_dec_erp_e_list_n erplist ON l.sid = erplist.send_list_sid-->
<!--            where l.head_id in (select SID from t_gw_dec_e_send_head  where trade_code=#{tradeCode} and hawb=#{hawb})-->
<!--            union all-->
<!--            SELECT-->
<!--            null,-->
<!--            null,-->
<!--            null,-->
<!--            null,-->
<!--            '合计',-->
<!--            sum(l.qty),-->
<!--            sum(l.pack_num),-->
<!--            null,-->
<!--        l.cabinet_type,-->
<!--            null,-->
<!--            sum(erplist.gross_wt),-->
<!--            sum(erplist.volume),-->
<!--        slist.cabinet_date,-->
<!--            null,-->
<!--            null,-->
<!--			l.container_no-->
<!--        FROM-->
<!--                t_gw_dec_e_scheduling_list slist-->
<!--                LEFT JOIN t_gw_dec_e_scheduling_head head ON head.sid = slist.head_id-->
<!--            LEFT JOIN t_gw_dec_e_send_list l ON head.bill_no=l.bill_no and slist.container_no=l.container_no-->
<!--            LEFT JOIN t_dec_erp_e_list_n erplist ON l.sid = erplist.send_list_sid-->
<!--                where l.head_id in (select SID from t_gw_dec_e_send_head  where trade_code=#{tradeCode} and hawb=#{hawb})-->
<!--            group by l.cabinet_type,-->
<!--                    l.container_no,-->
<!--                             slist.cabinet_date-->

<!--            union all-->
<!--                SELECT-->
<!--                '箱/封号',-->
<!--                NULL,-->
<!--                NULL,-->
<!--                NULL,-->
<!--                NULL,-->
<!--                NULL,-->
<!--                NULL,-->
<!--                NULL,-->
<!--        l.cabinet_type,-->
<!--                NULL,-->
<!--                NULL,-->
<!--                NULL,-->
<!--        slist.cabinet_date,-->
<!--                NULL,-->
<!--            NULL,-->
<!--			l.container_no-->
<!--            FROM-->
<!--                t_gw_dec_e_scheduling_list slist-->
<!--                LEFT JOIN t_gw_dec_e_scheduling_head head ON head.sid = slist.head_id-->
<!--            LEFT JOIN t_gw_dec_e_send_list l ON head.bill_no=l.bill_no and slist.container_no=l.container_no-->
<!--            LEFT JOIN t_dec_erp_e_list_n erplist ON l.sid = erplist.send_list_sid-->
<!--                where l.head_id in (select SID from t_gw_dec_e_send_head  where trade_code=#{tradeCode} and hawb=#{hawb})-->
<!--            GROUP BY-->
<!--                l.cabinet_type,-->
<!--                l.container_no,-->
<!--                slist.cabinet_date) A-->
<!--         order by bill_gno,branch_no NULLS LAST,g_name NULLS LAST;-->
<!--    </select>-->

<!--    <update id="updatePrintList">-->
<!--        update t_gw_dec_e_scheduling_head set print_Count=F_XDO_NVL(print_Count,0)+1-->
<!--         where trade_code=#{tradeCode} and hawb=#{hawb}-->
<!--    </update>-->
<!--    <select id="selectHawbCount" resultType="java.lang.Integer">-->
<!--        SELECT count(distinct hawb)-->
<!--        FROM-->
<!--        t_gw_dec_e_scheduling_head t where t.sid in-->
<!--        <foreach collection="sids"  item="item" open="(" separator="," close=")"  >-->
<!--            #{item}-->
<!--        </foreach>-->
<!--    </select>-->
</mapper>
