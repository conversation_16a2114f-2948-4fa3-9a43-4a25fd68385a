package com.dcjet.cs.plane.mapper;

import com.dcjet.cs.dto.plane.GwDecESendHeadDto;
import com.dcjet.cs.dto.plane.GwDecESendHeadListDto;
import com.dcjet.cs.dto.plane.GwDecESendHeadListParam;
import com.dcjet.cs.dto.plane.GwDecESendHeadParam;
import com.dcjet.cs.plane.model.GwDecESendHead;
import com.dcjet.cs.plane.model.GwDecESendHeadList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-1-26
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwDecESendHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwDecESendHeadDto toDto(GwDecESendHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwDecESendHead toPo(GwDecESendHeadParam param);
    /**
     * 数据库原始数据更新
     * @param decESendHeadParam
     * @param decESendHead
     */
    void updatePo(GwDecESendHeadParam decESendHeadParam, @MappingTarget GwDecESendHead decESendHead);
    default void patchPo(GwDecESendHeadParam decESendHeadParam, GwDecESendHead decESendHead) {
        // TODO 自行实现局部更新
    }
    GwDecESendHeadList toHPo(GwDecESendHeadListParam param);

    GwDecESendHeadListDto toHDto(GwDecESendHeadList param);
}
