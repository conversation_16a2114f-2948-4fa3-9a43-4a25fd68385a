<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.plane.dao.GwDecESendCabinetMapper">
    <resultMap id="decESendCabinetResultMap" type="com.dcjet.cs.plane.model.GwDecESendCabinet">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
        <result column="cabinet_type" property="cabinetType" jdbcType="VARCHAR" />
        <result column="container_no" property="containerNo" jdbcType="VARCHAR" />
        <result column="cabinet_date" property="cabinetDate" jdbcType="TIMESTAMP" />
        <result column="note" property="note" jdbcType="VARCHAR" />
        <result column="branch_no" property="branchNo" jdbcType="VARCHAR" />
        <result column="shipment_id" property="shipmentId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
	</resultMap>

    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_gw_dec_e_send_cabinet t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getCabinetList" resultMap="decESendCabinetResultMap" parameterType="com.dcjet.cs.plane.model.GwDecESendCabinet">
        SELECT
        sid
        ,cabinet_type
        ,container_no
        ,cabinet_date
        ,branch_no
        ,note
        ,shipment_id
        ,status
        FROM
        t_gw_dec_e_send_cabinet t
        <where>
            and trade_code = #{tradeCode}
            <if test="headId != null and headId != ''">
                and head_id = #{headId}
            </if>
            <if test="shipmentId != null and shipmentId != ''">
                and shipment_id = #{shipmentId}
            </if>
        </where>
        order by shipment_id,container_no
    </select>


</mapper>
