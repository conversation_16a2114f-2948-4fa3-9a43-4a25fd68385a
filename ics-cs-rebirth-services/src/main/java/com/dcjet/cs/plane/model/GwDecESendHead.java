package com.dcjet.cs.plane.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-1-26
 */
@Setter
@Getter
@Table(name = "t_gw_dec_e_send_head")
public class GwDecESendHead implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键,GUID
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * 企业代码
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 建表员
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 建表日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 变更人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 变更日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 制单人姓名
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     * 变更人姓名
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 状态，0：暂存 1 已拼柜分票 2 SAP已发送
     */
    @Column(name = "status")
    private String status;
    /**
     * 订舱流水号
     */
    @Column(name = "booking_list_no")
    private String bookingListNo;
    /**
     * 货代编码
     */
    @Column(name = "forward_code")
    private String forwardCode;
    /**
     * 货代名称
     */
    @Column(name = "forward_name")
    private String forwardName;
    /**
     * BookingID
     */
    @Column(name = "booking_id")
    private String bookingId;
    /**
     * 分提运单(货代提单号)
     */
    @Column(name = "hawb")
    private String hawb;
    /**
     * 放舱ETD
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "etd")
    private Date etd;
    /**
     * 放舱ETD-开始
     */
    @Transient
    private String etdFrom;
    /**
     * 放舱ETD-结束
     */
    @Transient
    private String etdTo;
    /**
     * 柜型柜量·
     */
    @Column(name = "ctn")
    private String ctn;
    /**
     * 备注
     */
    @Column(name = "note")
    private String note;
    /**
     * 运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递
     */
    @Column(name = "traf_mode")
    private String trafMode;
    /**
     * 运输方式名称
     */
    @Column(name = "traf_name")
    private String trafName;
    /**
     * 订单计划开船日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "cietd_date")
    private Date cietdDate;
    /**
     * 订单计划开船日-开始
     */
    @Transient
    private String cietdDateFrom;
    /**
     * 订单计划开船日-结束
     */
    @Transient
    private String cietdDateTo;
    /**
     * 订单订舱开船日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ccetd_date")
    private Date ccetdDate;
    /**
     * 订单订舱开船日-开始
     */
    @Transient
    private String ccetdDateFrom;
    /**
     * 订单订舱开船日-结束
     */
    @Transient
    private String ccetdDateTo;
    /**
     * 最终目的国
     */
    @Column(name = "destination_country")
    private String destinationCountry;
    /**
     * 目的港
     */
    @Column(name = "dest_port")
    private String destPort;
    /**
     * 客户代码(售达方代码)
     */
    @Column(name = "client_code")
    private String clientCode;
    /**
     * 送达方编码
     */
    @Column(name = "delivery_no")
    private String deliveryNo;
    /**
     * 贸易条款
     */
    @Column(name = "trade_terms")
    private String tradeTerms;
    /**
     * 贸易条款(地区)
     */
    @Column(name = "trade_area")
    private String tradeArea;

    /**
     * 申请订舱表头SID
     */
    @Column(name = "head_id")
    private String headId;
    @Transient
    private String clientName;
    @Transient
    private String address;
    @Transient
    private String deliveryName;
    /**
     * SO号(销售订单号)
     */
    @Transient
    private String soNo;
    /**
     * SO行号(销售订单行号)
     */
    @Transient
    private Integer soLineNo;
    /**
     * 货代单号
     */
    @Column(name = "forward_no")
    private String forwardNo;

    /**
     * 跟单备注
     */
    @Column(name = "remark")
    private String remark;
}
