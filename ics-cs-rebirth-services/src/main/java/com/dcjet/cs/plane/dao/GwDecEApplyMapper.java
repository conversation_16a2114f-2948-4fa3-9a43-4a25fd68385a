package com.dcjet.cs.plane.dao;

import com.dcjet.cs.erp.model.sap.ErpDecEList;
import com.dcjet.cs.plane.model.GwDecEApply;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
* generated by Generate 神码
* DecEApply
* <AUTHOR>
* @date: 2021-1-25
*/
public interface GwDecEApplyMapper extends Mapper<GwDecEApply> {
    /**
     * 查询获取数据
     * @param decEApply
     * @return
     */
    List<GwDecEApply> getList(GwDecEApply decEApply);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    List<GwDecEApply> getListBySids(List<String> sids);
    /**
     * 功能描述 根据查询条件提取ERP数据
     * <AUTHOR>
     * @date 2021-01-25
     * @version 1.0
     * @param erpDecEList 1
     * @return int
    */
    int extraction(ErpDecEList erpDecEList);
    /**
     * 功能描述 根据勾选SID列表提取
     * <AUTHOR>
     * @date 2021-01-28
     * @version 1.0
     * @param sids 1
     * @param lockMark 2
     * @param insertUser 3
     * @param insertUserName 4
     * @param insertTime 5
     * @param tradeCode 6
     * @return int
    */
    int extractionBySid(@Param("sids") List<String> sids, @Param("lockMark") String lockMark, @Param("insertUser") String insertUser, @Param("insertUserName") String insertUserName, @Param("insertTime") Date insertTime, @Param("tradeCode") String tradeCode);

    /**
     * 功能描述
     * <AUTHOR>
     * @date 2021-01-26
     * @version 1.0
     * @param sids 1
     * @param insertUser 2
     * @param insertUserName 3
     * @param insertTime 4
     * @param tradeCode 5
     * @param headId 6
     * @param bookingListNo 7
     * @return int
    */
    int applyBookList(@Param("sids") List<String> sids, @Param("insertUser") String insertUser, @Param("insertUserName") String insertUserName, @Param("insertTime") Date insertTime,
                      @Param("tradeCode") String tradeCode, @Param("headId") String headId, @Param("bookingListNo") String bookingListNo, @Param("trafMode") String trafMode, @Param("uuid") String uuid, @Param("bookingId") String bookingId);

    /**
     * 功能描述
     * <AUTHOR>
     * @date 2021-01-25
     * @version 1.0
     * @param sids 1
     * @return java.util.List<com.dcjet.cs.plane.model.DecEApply>
    */
    List<GwDecEApply> getApplyBook(List<String> sids);

    /**
     * 功能描述
     * <AUTHOR>
     * @date 2021-01-26
     * @version 1.0
     * @param sids 1
     * @return java.math.BigDecimal
    */
    BigDecimal getVolume(List<String> sids);

    /**
     * 功能描述
     * <AUTHOR>
     * @date 2021-01-26
     * @version 1.0
     * @param sids 1
     * @return int
    */
    int getVolumeNullCount(List<String> sids);


}
