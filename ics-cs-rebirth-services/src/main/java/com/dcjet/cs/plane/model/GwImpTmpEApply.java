package com.dcjet.cs.plane.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-1-25
 */
@Setter
@Getter
@Table(name = "t_gw_imp_dec_e_apply")
public class GwImpTmpEApply extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 状态，0：暂存
     */
    @Column(name = "status")
    private String status;
    /**
     * 运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递
     */
    @Column(name = "traf_mode")
    private String trafMode;
    /**
     * 运输方式名称
     */
    @Column(name = "traf_name")
    private String trafName;
    /**
     * 最终目的国
     */
    @Column(name = "destination_country")
    private String destinationCountry;
    /**
     * 订单订舱开船日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "ccetd_date")
    private Date ccetdDate;

    /**
     * 目的港
     */
    @Column(name = "dest_port")
    private String destPort;
    /**
     * 售达方
     */
    @Column(name = "client_code")
    private String clientCode;
    /**
     * 送达方
     */
    @Column(name = "delivery_no")
    private String deliveryNo;
    /**
     * 国际贸易条件
     */
    @Column(name = "trade_terms")
    private String tradeTerms;
    /**
     * 国际贸易条件（部分2）
     */
    @Column(name = "trade_area")
    private String tradeArea;
    /**
     * 单据号
     */
    @Column(name = "linked_no")
    private String linkedNo;
    /**
     * 序号
     */
    @Column(name = "line_no")
    private Integer lineNo;
    /**
     * 计划行编码
     */
    @Column(name = "plan_line_no")
    private Integer planLineNo;
    /**
     * 保完税标记
     */
    @Column(name = "bond_mark")
    private String bondMark;
    /**
     * 物料类型 I 料件  E 成品
     */
    @Column(name = "g_mark")
    private String GMark;
    /**
     * 企业料号
     */
    @Column(name = "fac_g_no")
    private String facGNo;
    /**
     * 商品名称
     */
    @Column(name = "g_name")
    private String GName;
    /**
     * 数量
     */
    @Column(name = "qty")
    private BigDecimal qty;
    /**
     * 交易单位
     */
    @Column(name = "unit_erp")
    private String unitErp;
    /**
     * 单价
     */
    @Column(name = "dec_price")
    private BigDecimal decPrice;
    /**
     * 总价
     */
    @Column(name = "dec_total")
    private BigDecimal decTotal;
    /**
     * 币制
     */
    @Column(name = "curr")
    private String curr;
    /**
     * 净重
     */
    @Column(name = "net_wt")
    private BigDecimal netWt;
    /**
     * 毛重
     */
    @Column(name = "gross_wt")
    private BigDecimal grossWt;
    /**
     * SO号(销售订单号)
     */
    @Column(name = "so_no")
    private String soNo;
    /**
     * SO行号(销售订单行号)
     */
    @Column(name = "so_line_no")
    private Integer soLineNo;
    /**
     * SO日期(销售订单日期)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "so_date")
    private Date soDate;

    /**
     * 发票号
     */
    @Column(name = "invoice_no")
    private String invoiceNo;
    /**
     * 送达方PO号
     */
    @Column(name = "delivery_po_no")
    private String deliveryPoNo;
    /**
     * 送达方PO行号
     */
    @Column(name = "delivery_po_line_no")
    private Integer deliveryPoLineNo;
    /**
     * 售达方PO号
     */
    @Column(name = "sold_po_no")
    private String soldPoNo;
    /**
     * 售达方PO行号
     */
    @Column(name = "sold_po_line_no")
    private Integer soldPoLineNo;
    /**
     * ERP创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "last_modify_date")
    private Date lastModifyDate;

    /**
     * 境内货源地
     */
    @Column(name = "district_code")
    private String districtCode;
    /**
     * 卖场订单DI PO
     */
    @Column(name = "market_order_no")
    private String marketOrderNo;
    /**
     * 台/箱
     */
    @Column(name = "container_num")
    private Integer containerNum;
    /**
     * 件数
     */
    @Column(name = "pack_num")
    private BigDecimal packNum;
    /**
     * 体积
     */
    @Column(name = "volume")
    private BigDecimal volume;
    /**
     * 拒绝原因
     */
    @Column(name = "refused_reason")
    private String refusedReason;
    /**
     * 订单计划开船日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "cietd_date")
    private Date cietdDate;

    /**
     * 客户料号
     */
    @Column(name = "customer_g_no")
    private String customerGNo;
    @Column(name = "list_sid")
    private String listSid;


    /**
     * 征免方式
     */
    @Column(name = "DUTY_MODE")
    private String dutyMode;
    /**
     * 用户
     */
    @Column(name = "TEMP_OWNER")
    private String tempOwner;
    /**
     * 标识
     */
    @Column(name = "TEMP_FLAG")
    private Integer tempFlag;
    /**
     * 提示
     */
    @Column(name = "TEMP_REMARK")
    private String tempRemark;
    /**
     * 序号
     */
    @Column(name = "TEMP_INDEX")
    private int tempIndex;
}
