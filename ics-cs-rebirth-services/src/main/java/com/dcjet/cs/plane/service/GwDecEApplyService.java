package com.dcjet.cs.plane.service;

import com.dcjet.cs.dto.erp.sap.ErpDecEListDto;
import com.dcjet.cs.dto.erp.sap.ErpDecEListParam;
import com.dcjet.cs.dto.plane.GwDecEApplyDto;
import com.dcjet.cs.dto.plane.GwDecEApplyParam;
import com.dcjet.cs.dto.plane.GwDecEPlanCabinetParam;
import com.dcjet.cs.dto.plane.GwImpDecEApply;
import com.dcjet.cs.erp.dao.sap.ErpSapDecEListMapper;
import com.dcjet.cs.erp.mapper.sap.ErpDecEListDtoMapper;
import com.dcjet.cs.erp.model.sap.ErpDecEList;
import com.dcjet.cs.plane.dao.*;
import com.dcjet.cs.plane.mapper.GwDecEApplyDtoMapper;
import com.dcjet.cs.plane.mapper.GwDecEPlanCabinetDtoMapper;
import com.dcjet.cs.plane.mapper.GwDecEPlanListDtoMapper;
import com.dcjet.cs.plane.model.*;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-1-25
 */
@Service
public class GwDecEApplyService extends BaseService<GwDecEApply> {
    @Resource
    private GwDecEApplyMapper decEApplyMapper;
    @Resource
    private GwDecEApplyDtoMapper decEApplyDtoMapper;
    @Override
    public Mapper<GwDecEApply> getMapper() {
        return decEApplyMapper;
    }
    @Resource
    private ErpDecEListDtoMapper erpDecEListDtoMapper;
    @Resource
    private ErpSapDecEListMapper erpDecEListMapper;
    @Resource
    protected BulkSqlOpt bulkSqlOpt;
    @Resource
    protected GwDecEPlanHeadMapper gwDecEPlanHeadMapper;
    @Resource
    protected GwDecEPlanListMapper gwDecEPlanListMapper;
    @Resource
    protected GwDecEPlanCabinetDtoMapper decEPlanCabinetDtoMapper;
    @Resource
    private GwDecEPlanCabinetMapper decEPlanCabinetMapper;
    @Resource
    private GwDecEPlanListDtoMapper decEPlanListDtoMapper;
    @Resource
    private GwDecESendHeadMapper decESendHeadMapper;
    @Resource
    private GwDecESendCabinetMapper decESendCabinetMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param decEApplyParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwDecEApplyDto>> getListPaged(GwDecEApplyParam decEApplyParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        GwDecEApply decEApply = decEApplyDtoMapper.toPo(decEApplyParam);
        decEApply.setTradeCode(userInfo.getCompany());
        decEApply.setStatus(ConstantsStatus.STATUS_0);
        Page<GwDecEApply> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decEApplyMapper.getList(decEApply));
        List<GwDecEApplyDto> decEApplyDtos = page.getResult().stream().map(head -> {
            GwDecEApplyDto dto = decEApplyDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwDecEApplyDto>> paged = ResultObject.createInstance(decEApplyDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param decEApplyParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwDecEApplyDto insert(GwDecEApplyParam decEApplyParam, UserInfoToken userInfo) {
        GwDecEApply decEApply = decEApplyDtoMapper.toPo(decEApplyParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        decEApply.setSid(sid);
        decEApply.setStatus(ConstantsStatus.STATUS_0);
        decEApply.setInsertUser(userInfo.getUserNo());
        decEApply.setInsertUserName(userInfo.getUserName());
        decEApply.setTradeCode(userInfo.getCompany());
        decEApply.setInsertTime(new Date());
        // 新增数据
        int insertStatus = decEApplyMapper.insert(decEApply);
        return  insertStatus > 0 ? decEApplyDtoMapper.toDto(decEApply) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param decEApplyParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwDecEApplyDto update(GwDecEApplyParam decEApplyParam, UserInfoToken userInfo) {
        GwDecEApply decEApply = decEApplyMapper.selectByPrimaryKey(decEApplyParam.getSid());
        decEApplyDtoMapper.updatePo(decEApplyParam, decEApply);
        decEApply.setUpdateUser(userInfo.getUserNo());
        decEApply.setUpdateUserName(userInfo.getUserName());
        decEApply.setUpdateTime(new Date());
        // 更新数据
        int update = decEApplyMapper.updateByPrimaryKey(decEApply);
        return update > 0 ? decEApplyDtoMapper.toDto(decEApply) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		decEApplyMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwDecEApplyDto> selectAll(GwDecEApplyParam exportParam, UserInfoToken userInfo) {
        GwDecEApply decEApply = decEApplyDtoMapper.toPo(exportParam);
        decEApply.setTradeCode(userInfo.getCompany());
        decEApply.setStatus(ConstantsStatus.STATUS_0);
        List<GwDecEApplyDto> decEApplyDtos = new ArrayList<>();
        List<GwDecEApply> decEApplys = decEApplyMapper.getList(decEApply);
        if (CollectionUtils.isNotEmpty(decEApplys)) {
            decEApplyDtos = decEApplys.stream().map(head -> {
                GwDecEApplyDto dto = decEApplyDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decEApplyDtos;
    }

    /**
     * 功能描述 提单表体数据提取功能
     * <AUTHOR>
     * @date 2020/2/21
     * @version 1.0
     * @param erpDecEListParam 1
     * @param userInfo 2
     * @return com.xdo.domain.ResultObject
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject extraction(ErpDecEListParam erpDecEListParam, UserInfoToken userInfo) {

        if(StringUtils.isNotBlank(erpDecEListParam.getLinkedNo2())) {
            erpDecEListParam.getLinkedNo2().replace("，", ",");
            erpDecEListParam.setLinkedNo(erpDecEListParam.getLinkedNo2().replace("，", ","));
            erpDecEListParam.setLinkedNo(erpDecEListParam.getLinkedNo2().replaceAll(" ", ","));
            erpDecEListParam.setLinkedNo(erpDecEListParam.getLinkedNo2().replaceAll("[',']+", ","));
        }

        String sid = UUID.randomUUID().toString();
        erpDecEListParam.setTradeCode(userInfo.getCompany());
//        erpDecEListParam.setGMarkConvert("E");
        erpDecEListParam.setStatus("0");
        List<ErpDecEListDto> decELists=erpDecEListMapper.getList(erpDecEListParam);
        if(decELists.size()==0)
        {
            throw new ErrorException(400, "本次查询没有可提取数据!");
        }
        List<ErpDecEListDto> elist=decELists.stream().filter(e -> "1".equals(e.getStatus())).collect(Collectors.toList());
        if(elist.size()>0)
        {
            throw new ErrorException(400, "本次提取数据有已经被提取过，无法重复提取!");
        }
        List<GwImpDecEApply> impDecEApplyList=new ArrayList<>();
        for(ErpDecEListDto erpDecEListDto:decELists)
        {
            /*
            ImpTmpDecErpEList校验表体必填项
            */
            ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
            Validator validator = validatorFactory.getValidator();
            GwImpDecEApply impDecEApply=decEApplyDtoMapper.toParam(erpDecEListDto);
            Set<ConstraintViolation<GwImpDecEApply>> constraintViolations = validator.validate(impDecEApply);
            if (constraintViolations != null && constraintViolations.size() > 0) {
                for (ConstraintViolation<GwImpDecEApply> cv : constraintViolations) {
                    impDecEApply.setTempRemark(cv.getMessage());
                    impDecEApply.setTempMark(1);
                }
            }
            impDecEApplyList.add(impDecEApply);
            if(StringUtils.isNotEmpty(impDecEApply.getTempRemark())) {
                throw new ErrorException(400, impDecEApply.getTempRemark());
            }
        }
        List<GwImpDecEApply> errlist=impDecEApplyList.stream().filter(e -> StringUtils.isNotBlank(e.getTempRemark())).collect(Collectors.toList());
        if(errlist.size()>0)
        {
            return ResultObject.createInstance(true, "提取失败", errlist);
        }
        else {
            erpDecEListParam.setSid(sid);//更新lockMark
            ErpDecEList erpDecEList = erpDecEListDtoMapper.toPo(erpDecEListParam);
            erpDecEList.setInsertUser(userInfo.getUserNo());
            erpDecEList.setInsertUserName(userInfo.getUserName());
            erpDecEList.setTradeCode(userInfo.getCompany());
            erpDecEList.setInsertTime(new Date());
            decEApplyMapper.extraction(erpDecEList);
            return ResultObject.createInstance(true, "提取成功");
        }
    }

    /**
     * 功能描述 提单表体数据提取功能(勾选)
     * <AUTHOR>
     * @date 2020/2/21
     * @version 1.0
     * @param sids 1
     * @param userInfo 3
     * @return com.xdo.domain.ResultObject
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject extractionBySid(List<String> sids, UserInfoToken userInfo) {
        List<ErpDecEList> decELists=erpDecEListMapper.selectBySids(sids,userInfo.getCompany());
        if(decELists.size()==0)
        {
            throw new ErrorException(400, "本次提取没有可提取数据!");
        }
        List<ErpDecEList> elist=decELists.stream().filter(e -> "1".equals(e.getStatus())).collect(Collectors.toList());
        if(elist.size()>0)
        {
            throw new ErrorException(400, "本次提取数据有已经被提取过，无法重复提取!");
        }
        List<GwImpDecEApply> impDecEApplyList=new ArrayList<>();
        for(ErpDecEList erpDecEList:decELists)
        {
            /*
            ImpTmpDecErpEList校验表体必填项
            */
            ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
            Validator validator = validatorFactory.getValidator();
            GwImpDecEApply impDecEApply=decEApplyDtoMapper.toParamList(erpDecEList);
            Set<ConstraintViolation<GwImpDecEApply>> constraintViolations = validator.validate(impDecEApply);
            if (constraintViolations != null && constraintViolations.size() > 0) {
                for (ConstraintViolation<GwImpDecEApply> cv : constraintViolations) {
                    impDecEApply.setTempRemark(cv.getMessage());
                    impDecEApply.setTempMark(1);
                }
            }
            if(erpDecEList.getQty()==null&&erpDecEList.getDecTotal()==null)
            {
                impDecEApply.setTempRemark(impDecEApply.getTempMark()+"数量和总价不能同时为空！");
                impDecEApply.setTempMark(1);
            }
            impDecEApplyList.add(impDecEApply);
            if(StringUtils.isNotEmpty(impDecEApply.getTempRemark())) {
                throw new ErrorException(400, impDecEApply.getTempRemark());
            }
        }
        List<GwImpDecEApply> errlist=impDecEApplyList.stream().filter(e -> StringUtils.isNotBlank(e.getTempRemark())).collect(Collectors.toList());
        if(errlist.size()>0)
        {
            return ResultObject.createInstance(true, "提取失败", errlist);
        }
        else {
            String lockMark = UUID.randomUUID().toString();
            decEApplyMapper.extractionBySid(sids, lockMark, userInfo.getUserNo(), userInfo.getUserName(), new Date(), userInfo.getCompany());
            return ResultObject.createInstance(true, "提取成功");
        }
    }

    /**
     * 功能描述 申请订舱功能（勾选）
     * <AUTHOR>
     * @date 2021-01-25
     * @version 1.0
     * @param sids 1
     * @param decEPlanCabinetParamList 2
     * @param userInfo 3
     * @return com.xdo.domain.ResultObject
    */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject applyBookBySid(String type, List<String> sids, List<GwDecEPlanCabinetParam> decEPlanCabinetParamList, UserInfoToken userInfo) {

        List<GwDecEPlanCabinetParam> decEPlanCabinets=decEPlanCabinetParamList.stream().sorted(Comparator.comparing(GwDecEPlanCabinetParam::getShipmentId,Comparator.nullsFirst(null))).collect(Collectors.toList());

        ResultObject resultObject= ResultObject.createInstance(true, "申请订舱成功");
        List<GwDecEApply> decEApplyList=decEApplyMapper.getListBySids(sids);
        List<GwDecEApply> canNotDatas = decEApplyList.stream()
                .filter(x -> !ConstantsStatus.STATUS_0.equals(x.getStatus()))
                .collect(Collectors.toList());
        if (canNotDatas.size() > 0) {
            resultObject.setSuccess(false);
            resultObject.setMessage("只有【暂存】状态数据可以申请订舱！");
            return resultObject;
        }

        //判断数据唯一
        GwDecEApply decEApply=applyBookCheck(sids);
        if(decEApply==null)
        {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不唯一，无法进行申请订舱操作！");
            return resultObject;
        }
        else
        {
            String first="S";
            String bookingListNo=decEPlanCabinets.get(0).getBookingListNo();
            if(StringUtils.isNotBlank(bookingListNo)) {
                List<String> list = gwDecEPlanHeadMapper.getBookingListId(bookingListNo);
                if (list.size() == 0) {
                    resultObject.setSuccess(false);
                    resultObject.setMessage("订舱流水号：" + bookingListNo + "在单证订舱中不存在！");
                    return resultObject;
                }
            }
            List<GwDecEPlanCabinet> shipmentList=gwDecEPlanListMapper.getShipment(bookingListNo);
            //确定生成单据编号首字母
            for(GwDecEPlanCabinetParam decEPlanCabinet:decEPlanCabinets)
            {
                if(Arrays.asList("A","B","C","D","H").contains(decEPlanCabinet.getCabinetType()))
                {
                    first="Z";
                }
                if(decEPlanCabinet.getCabinetType()==null)
                {
                    resultObject.setSuccess(false);
                    resultObject.setMessage("柜型不能为空！");
                    return resultObject;
                }
                if(decEPlanCabinet.getCabinetQty()==null)
                {
                    resultObject.setSuccess(false);
                    resultObject.setMessage("柜量数量必须大于0！");
                    return resultObject;
                }
                if(decEPlanCabinet.getCabinetQty()!=null&&decEPlanCabinet.getCabinetQty().compareTo(BigDecimal.ZERO)==0)
                {
                    resultObject.setSuccess(false);
                    resultObject.setMessage("柜量数量必须大于0！");
                    return resultObject;
                }
                if(decEPlanCabinet.getCabinetType()!=null&&decEPlanCabinet.getCabinetQty()!=null&&"EFG".contains(decEPlanCabinet.getCabinetType())&&decEPlanCabinet.getCabinetQty().compareTo(BigDecimal.ONE)>0)
                {
                    resultObject.setSuccess(false);
                    resultObject.setMessage("柜型为【散货、空运、快递】时，数量只能为1！");
                    return resultObject;
                }

                if(StringUtils.isNotBlank(bookingListNo)) {
                    List<GwDecEPlanCabinet> nowShipment = shipmentList.stream().filter(x -> StringUtils.isNotBlank(decEPlanCabinet.getShipmentId())&&x.getShipmentId().equals(decEPlanCabinet.getShipmentId())).collect(Collectors.toList());
                    if (nowShipment.size() > 0) {
                        List<GwDecEPlanCabinet> shipment = nowShipment.stream().filter(x -> x.getCabinetType().equals(decEPlanCabinet.getCabinetType())).collect(Collectors.toList());
                        if (shipment.size() == 0) {
                            resultObject.setSuccess(false);
                            resultObject.setMessage("订舱流水号下shipmentID :" + decEPlanCabinet.getShipmentId() + "柜型不一致！");
                            return resultObject;
                        }
                    }
                }
            }

            //提交校验
            if(ConstantsStatus.STATUS_0.equals(type)) {
                String errMessage = volumeCheck(sids, decEPlanCabinets);

                if (StringUtils.isNotBlank(errMessage)) {
                    resultObject.setSuccess(false);
                    resultObject.setErrorField(ConstantsStatus.STATUS_0);
                    resultObject.setMessage(errMessage);
                    return resultObject;
                }
            }

            //生成单据编号
            String dateFmt = "yyyyMMdd";
            String uuid=UUID.randomUUID().toString();

            SimpleDateFormat sdf = new SimpleDateFormat(dateFmt);
            dateFmt = sdf.format(new Date());
            first=first+dateFmt;
            String  maxEmsListNo= gwDecEPlanHeadMapper.getMaxOrderNo(userInfo.getCompany(), first);
            String maxListNo=String.format("%03d", 1);
            long maxNo;
            if(StringUtils.isNotBlank(maxEmsListNo)) {
                maxNo=Long.parseUnsignedLong(maxEmsListNo.substring(9));
                maxListNo=String.format("%03d", (maxNo + 1));
            }
            Map<String,Integer> cabinetTypes=new HashMap<>();
            List<GwDecEPlanCabinet> decEPlanCabinetList=new ArrayList<>();
            String headId = UUID.randomUUID().toString();
            String bookingId = "";
            String trafMode=null;
            StringBuilder ctn = new StringBuilder();
            StringBuilder remark = new StringBuilder();
            if(StringUtils.isNotBlank(bookingListNo))
            {
                GwDecEPlanHead decEPlanHead = new GwDecEPlanHead();
                decEPlanHead.setTradeCode(userInfo.getCompany());
                decEPlanHead.setBookingListNo(bookingListNo);
                List<GwDecEPlanHead> decEPlanHeads = gwDecEPlanHeadMapper.getList(decEPlanHead);
                if(decEPlanHeads.size()>0) {
                    headId = decEPlanHeads.get(0).getSid();
                    bookingId=decEPlanHeads.get(0).getBookingId();
                }
            }
            else{
                bookingListNo = first+maxListNo;
            }
            GwDecEPlanCabinet decEPlanCabinet=new GwDecEPlanCabinet();
            decEPlanCabinet.setHeadId(headId);
            decEPlanCabinet.setBookingListNo(bookingListNo);
            decEPlanCabinet.setTradeCode(userInfo.getCompany());
            List<GwDecEPlanCabinet> decEPlanCabinetList1=decEPlanCabinetMapper.getList(decEPlanCabinet);

            for(GwDecEPlanCabinetParam decEPlanCabinetParam:decEPlanCabinets)
            {
                trafMode=decEPlanCabinetParam.getTrafMode();
                List<GwDecEPlanCabinet> useList=decEPlanCabinetList1.stream().filter(x->StringUtils.isNotBlank(decEPlanCabinetParam.getShipmentId())&&decEPlanCabinetParam.getShipmentId().equals(x.getShipmentId())).collect(Collectors.toList());
                if(useList.size()>0)
                {
                    continue;
                }

                decEPlanCabinet=decEPlanCabinetDtoMapper.toPo(decEPlanCabinetParam);
                decEPlanCabinet.setHeadId(headId);
                decEPlanCabinet.setBookingListNo(bookingListNo);
                decEPlanCabinet.setTradeCode(userInfo.getCompany());
                decEPlanCabinet.setSid(UUID.randomUUID().toString());
                decEPlanCabinet.setStatus(ConstantsStatus.STATUS_0);
                decEPlanCabinet.setInsertUser(userInfo.getUserNo());
                decEPlanCabinet.setInsertUserName(userInfo.getUserName());
                decEPlanCabinet.setInsertTime(new Date());
                decEPlanCabinetList.add(decEPlanCabinet);
                decEPlanCabinetList1.add(decEPlanCabinet);
            }
            //批量插入柜型柜量表
            bulkSqlOpt.batchInsert(decEPlanCabinetList, GwDecEPlanCabinetMapper.class);

            if(decEPlanCabinetList1.size()>0)
            {
                for(GwDecEPlanCabinet decEPlanCabinetC:decEPlanCabinetList1) {
                    ctn.append(decEPlanCabinetC.getCabinetQty()).append("*").append(CommonEnum.CONTAINER_TYPE_CUSTOM_ENUM.getValue(decEPlanCabinetC.getCabinetType())).append("+");
                    if (StringUtils.isNotBlank(decEPlanCabinetC.getNote())) {
                        remark.append(decEPlanCabinetC.getNote()).append(";");
                    }
                    if ("2".equals(decEApplyList.get(0).getDataSource())) {
                        if (cabinetTypes.containsKey(decEPlanCabinetC.getCabinetType())) {
                            int a = decEPlanCabinetC.getCabinetQty() + cabinetTypes.get(decEPlanCabinetC.getCabinetType()).intValue();
                            cabinetTypes.put(decEPlanCabinetC.getCabinetType(), a);
                        } else {
                            cabinetTypes.put(decEPlanCabinetC.getCabinetType(), decEPlanCabinetC.getCabinetQty());
                        }
                    }
                }
            }

            //插入申请订舱明细表，修改申报表状态
            decEApplyMapper.applyBookList(sids,userInfo.getUserNo(),userInfo.getUserName(),new Date(),userInfo.getCompany(),headId,bookingListNo,trafMode,uuid,bookingId);
            if(cabinetTypes.size()>0) {
                ctn=new StringBuilder();
                Set set = cabinetTypes.keySet();
                for (Object o : set) {
                    ctn.append(cabinetTypes.get(o)).append("*").append(CommonEnum.CONTAINER_TYPE_CUSTOM_ENUM.getValue(o.toString())).append("+");
                }
            }
            if(ctn.length()>0)
            {
                ctn = new StringBuilder(ctn.substring(0, ctn.length() - 1));
            }
            if(StringUtils.isBlank(decEPlanCabinets.get(0).getBookingListNo())) {
                //插入申请订舱表头表
                GwDecEPlanHead decEPlanHead = decEApplyDtoMapper.toHead(decEApply);
                decEPlanHead.setSid(headId);
                decEPlanHead.setBookingListNo(bookingListNo);
                decEPlanHead.setStatus(ConstantsStatus.STATUS_0);
                decEPlanHead.setInsertUser(userInfo.getUserNo());
                decEPlanHead.setInsertUserName(userInfo.getUserName());
                decEPlanHead.setInsertTime(new Date());
                decEPlanHead.setTradeCode(userInfo.getCompany());
                decEPlanHead.setCietdDate(decEApply.getCietdDate());
                decEPlanHead.setCcetdDate(decEApply.getCcetdDate());
                decEPlanHead.setCtn(ctn.toString());
                decEPlanHead.setRemark(remark.toString());
                decEPlanHead.setTrafMode(trafMode);
                decEPlanHead.setTrafName(CommonEnum.TRAF_MODE_CUSTOM_ENUM.getValue(trafMode));
                gwDecEPlanHeadMapper.insertSelective(decEPlanHead);
            }
            else
            {
                if(StringUtils.isNotBlank(ctn.toString())) {
                    //插入申请订舱表头表
                    GwDecEPlanHead decEPlanHeadE= new GwDecEPlanHead();
                    decEPlanHeadE.setSid(headId);
                    decEPlanHeadE.setCtn(ctn.toString());
                    gwDecEPlanHeadMapper.updateByPrimaryKeySelective(decEPlanHeadE);
                }

                GwDecESendHead decESendHead=new GwDecESendHead();
                decESendHead.setTradeCode(userInfo.getCompany());
                decESendHead.setBookingListNo(bookingListNo);
                List<GwDecESendHead> decESendHeadList=decESendHeadMapper.select(decESendHead);
                if(decESendHeadList.size()>0) {

                    List<GwDecESendList> decESendLists = new ArrayList<>();
                    List<GwDecESendCabinet> decESendCabinetList = new ArrayList<>();
                    GwDecEPlanList decEPlanListP = new GwDecEPlanList();
                    decEPlanListP.setTradeCode(userInfo.getCompany());
                    decEPlanListP.setHeadId(headId);
                    decEPlanListP.setStatus("0");
                    decEPlanListP.setLockMark(uuid);
                    List<GwDecEPlanList> decEPlanListList = gwDecEPlanListMapper.getList(decEPlanListP);
                    for (GwDecEPlanList decEPlanList : decEPlanListList) {
                        GwDecESendList decESendList = decEPlanListDtoMapper.toSendPo(decEPlanList);
                        decESendList.setStatus(ConstantsStatus.STATUS_4);
                        decESendList.setHeadId(decESendHeadList.get(0).getSid());
                        decESendList.setListSid(decEPlanList.getSid());
                        decESendList.setBookingListNo(bookingListNo);
                        decESendList.setSid(UUID.randomUUID().toString());
                        decESendList.setInsertUser(userInfo.getUserNo());
                        decESendList.setInsertUserName(userInfo.getUserName());
                        decESendList.setTradeCode(userInfo.getCompany());
                        decESendList.setInsertTime(new Date());
                        decESendLists.add(decESendList);
                    }
                    int cabinetNo = 1;

                    for (GwDecEPlanCabinet decEPlanCabinet1 : decEPlanCabinetList) {
                        GwDecESendCabinet decESendCabinetP = new GwDecESendCabinet();
                        decESendCabinetP.setTradeCode(userInfo.getCompany());
                        decESendCabinetP.setHeadId(decESendHeadList.get(0).getSid());
                        List<GwDecESendCabinet> decCabinetsList = decESendCabinetMapper.getCabinetList(decESendCabinetP);
                        List<GwDecESendCabinet> existSList =decCabinetsList.stream().filter(x->x.getShipmentId().equals(decEPlanCabinet1.getShipmentId())).collect(Collectors.toList());
                        if(existSList.size()>0)
                        {
                            continue;
                        }

                        for (int i = 0; i < decEPlanCabinet1.getCabinetQty(); ) {
                            while(true) {
                                int finalCabinetNo = cabinetNo;
                                List<GwDecESendCabinet> existList =decCabinetsList.stream().filter(x->x.getContainerNo().equals(finalCabinetNo + "#")).collect(Collectors.toList());
                                if(existList.size()>0)
                                {
                                    cabinetNo++;
                                }
                                else{
                                    break;
                                }
                            }
                            GwDecESendCabinet decESendCabinet = new GwDecESendCabinet();
                            decESendCabinet.setSid(UUID.randomUUID().toString());
                            decESendCabinet.setHeadId(decESendHeadList.get(0).getSid());
                            decESendCabinet.setStatus(ConstantsStatus.STATUS_0);
                            decESendCabinet.setBookingListNo(bookingListNo);
                            decESendCabinet.setContainerNo(cabinetNo + "#");
                            decESendCabinet.setCabinetType(decEPlanCabinet1.getCabinetType());
                            decESendCabinet.setInsertUser(userInfo.getUserNo());
                            decESendCabinet.setInsertUserName(userInfo.getUserName());
                            decESendCabinet.setTradeCode(userInfo.getCompany());
                            decESendCabinet.setInsertTime(new Date());
                            decESendCabinet.setShipmentId(decEPlanCabinet1.getShipmentId());
                            decESendCabinet.setPlanId(decEPlanCabinet1.getSid());
                            decESendCabinetList.add(decESendCabinet);
                            i++;
                            cabinetNo++;
                        }
                    }
                    bulkSqlOpt.batchInsert(decESendLists, GwDecESendListMapper.class);
                    bulkSqlOpt.batchInsert(decESendCabinetList, GwDecESendCabinetMapper.class);
                    GwDecESendHead decESendHeadP=new GwDecESendHead();
                    decESendHeadP.setSid(decESendHeadList.get(0).getSid());
                    decESendHeadP.setStatus(ConstantsStatus.STATUS_4);
                    if(StringUtils.isNotBlank(ctn.toString())) {
                        decESendHeadP.setCtn(ctn.toString());
                    }
                    decESendHeadMapper.updateByPrimaryKeySelective(decESendHeadP);
                }
            }
        }
        return  resultObject;
    }

    /**
     * 功能描述 申请订舱提交校验
     * <AUTHOR>
     * @date 2021-01-26
     * @version 1.0
     * @param sids 1
     * @param decEPlanCabinets 2
     * @return java.lang.String
    */
    private String volumeCheck(List<String> sids,List<GwDecEPlanCabinetParam> decEPlanCabinets)
    {
        String errMessage="";
        int nullCount=decEApplyMapper.getVolumeNullCount(sids);
        if(nullCount>0)
        {
            errMessage="当前数据存在体积为空的数据！";
        }
        BigDecimal volumeSum=decEApplyMapper.getVolume(sids);
        BigDecimal minVolume=BigDecimal.ZERO;
        BigDecimal maxVolume=BigDecimal.ZERO;
        for(GwDecEPlanCabinetParam decEPlanCabinet:decEPlanCabinets)
        {
            if(decEPlanCabinet.getCabinetType()!=null&&decEPlanCabinet.getCabinetQty()!=null) {
                switch (decEPlanCabinet.getCabinetType()) {
                    case "A":
                        minVolume = minVolume.add(new BigDecimal("20").multiply(decEPlanCabinet.getCabinetQty()));
                        maxVolume = maxVolume.add(new BigDecimal("32").multiply(decEPlanCabinet.getCabinetQty()));
                        break;
                    case "B":
                        minVolume = minVolume.add(new BigDecimal("48").multiply(decEPlanCabinet.getCabinetQty()));
                        maxVolume = maxVolume.add(new BigDecimal("62").multiply(decEPlanCabinet.getCabinetQty()));
                        break;
                    case "C":
                        minVolume = minVolume.add(new BigDecimal("62").multiply(decEPlanCabinet.getCabinetQty()));
                        maxVolume = maxVolume.add(new BigDecimal("72").multiply(decEPlanCabinet.getCabinetQty()));
                        break;
                    case "D":
                    case "H":
                        minVolume = minVolume.add(new BigDecimal("72").multiply(decEPlanCabinet.getCabinetQty()));
                        maxVolume = maxVolume.add(new BigDecimal("90").multiply(decEPlanCabinet.getCabinetQty()));
                        break;
                    default:
                        break;
                }
            }
        }

        //体积总量比最小体积小
        if(volumeSum.compareTo(BigDecimal.ZERO)>0&&volumeSum.compareTo(minVolume) < 0)
        {
            errMessage=errMessage+"订柜体积小于最小值:"+minVolume+"，预定柜量过多！";
        }
        //体积总量比最大体积大
        if(volumeSum.compareTo(BigDecimal.ZERO)>0&&volumeSum.compareTo(maxVolume) > 0)
        {
            errMessage=errMessage+"订柜体积超出最大值:"+maxVolume+"，预定柜量不足！";
        }
        return errMessage;
    }

    /**
     * 功能描述 申请订舱选择数据唯一校验
     * <AUTHOR>
     * @date 2021-01-26
     * @version 1.0
     * @param sids 1
     * @return com.dcjet.cs.plane.model.DecEApply
    */
    private GwDecEApply applyBookCheck(List<String> sids)
    {
        List<GwDecEApply> decEApplies=decEApplyMapper.getApplyBook(sids);
        if(decEApplies.size() == 1)
        {
            return decEApplies.get(0);
        }
        else
        {
            return null;
        }
    }
}
