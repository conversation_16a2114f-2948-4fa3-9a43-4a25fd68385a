package com.dcjet.cs.plane.service;

import com.dcjet.cs.dto.plane.GwDecEPlanCabinetDto;
import com.dcjet.cs.dto.plane.GwDecEPlanCabinetParam;
import com.dcjet.cs.plane.dao.GwDecEPlanCabinetMapper;
import com.dcjet.cs.plane.mapper.GwDecEPlanCabinetDtoMapper;
import com.dcjet.cs.plane.model.GwDecEPlanCabinet;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2021-1-25
 */
@Service
public class GwDecEPlanCabinetService extends BaseService<GwDecEPlanCabinet> {
    @Resource
    private GwDecEPlanCabinetMapper decEPlanCabinetMapper;
    @Resource
    private GwDecEPlanCabinetDtoMapper decEPlanCabinetDtoMapper;
    @Override
    public Mapper<GwDecEPlanCabinet> getMapper() {
        return decEPlanCabinetMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param decEPlanCabinetParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwDecEPlanCabinetDto>> getListPaged(GwDecEPlanCabinetParam decEPlanCabinetParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        GwDecEPlanCabinet decEPlanCabinet = decEPlanCabinetDtoMapper.toPo(decEPlanCabinetParam);
        decEPlanCabinet.setTradeCode(userInfo.getCompany());
        Page<GwDecEPlanCabinet> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> decEPlanCabinetMapper.getList(decEPlanCabinet));
        List<GwDecEPlanCabinetDto> decEPlanCabinetDtos = page.getResult().stream().map(head -> {
            GwDecEPlanCabinetDto dto = decEPlanCabinetDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<GwDecEPlanCabinetDto>> paged = ResultObject.createInstance(decEPlanCabinetDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param decEPlanCabinetParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwDecEPlanCabinetDto insert(GwDecEPlanCabinetParam decEPlanCabinetParam, UserInfoToken userInfo) {
        GwDecEPlanCabinet decEPlanCabinet = decEPlanCabinetDtoMapper.toPo(decEPlanCabinetParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        decEPlanCabinet.setSid(sid);
        decEPlanCabinet.setInsertUser(userInfo.getUserNo());
        decEPlanCabinet.setInsertUserName(userInfo.getUserName());
        decEPlanCabinet.setTradeCode(userInfo.getCompany());
        decEPlanCabinet.setInsertTime(new Date());
        // 新增数据
        int insertStatus = decEPlanCabinetMapper.insert(decEPlanCabinet);
        return  insertStatus > 0 ? decEPlanCabinetDtoMapper.toDto(decEPlanCabinet) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param decEPlanCabinetParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwDecEPlanCabinetDto update(GwDecEPlanCabinetParam decEPlanCabinetParam, UserInfoToken userInfo) {
        GwDecEPlanCabinet decEPlanCabinet = decEPlanCabinetMapper.selectByPrimaryKey(decEPlanCabinetParam.getSid());
        decEPlanCabinetDtoMapper.updatePo(decEPlanCabinetParam, decEPlanCabinet);
        decEPlanCabinet.setUpdateUser(userInfo.getUserNo());
        decEPlanCabinet.setUpdateUserName(userInfo.getUserName());
        decEPlanCabinet.setUpdateTime(new Date());
        // 更新数据
        int update = decEPlanCabinetMapper.updateByPrimaryKey(decEPlanCabinet);
        return update > 0 ? decEPlanCabinetDtoMapper.toDto(decEPlanCabinet) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		decEPlanCabinetMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwDecEPlanCabinetDto> selectAll(GwDecEPlanCabinetParam exportParam, UserInfoToken userInfo) {
        GwDecEPlanCabinet decEPlanCabinet = decEPlanCabinetDtoMapper.toPo(exportParam);
        decEPlanCabinet.setTradeCode(userInfo.getCompany());
        List<GwDecEPlanCabinetDto> decEPlanCabinetDtos = new ArrayList<>();
        List<GwDecEPlanCabinet> decEPlanCabinets = decEPlanCabinetMapper.getList(decEPlanCabinet);
        if (CollectionUtils.isNotEmpty(decEPlanCabinets)) {
            decEPlanCabinetDtos = decEPlanCabinets.stream().map(head -> {
                GwDecEPlanCabinetDto dto = decEPlanCabinetDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return decEPlanCabinetDtos;
    }
}
