<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.plane.dao.GwDecEPlanHeadMapper">
    <resultMap id="decEPlanHeadResultMap" type="com.dcjet.cs.plane.model.GwDecEPlanHead">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="booking_list_no" property="bookingListNo" jdbcType="VARCHAR" />
		<result column="forward_code" property="forwardCode" jdbcType="VARCHAR" />
		<result column="forward_name" property="forwardName" jdbcType="VARCHAR" />
		<result column="booking_id" property="bookingId" jdbcType="VARCHAR" />
		<result column="hawb" property="hawb" jdbcType="VARCHAR" />
		<result column="etd" property="etd" jdbcType="DATE" />
		<result column="ctn" property="ctn" jdbcType="VARCHAR" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="traf_mode" property="trafMode" jdbcType="VARCHAR" />
		<result column="traf_name" property="trafName" jdbcType="VARCHAR" />
		<result column="cietd_date" property="cietdDate" jdbcType="DATE" />
		<result column="ccetd_date" property="ccetdDate" jdbcType="DATE" />
		<result column="destination_country" property="destinationCountry" jdbcType="VARCHAR" />
		<result column="dest_port" property="destPort" jdbcType="VARCHAR" />
		<result column="client_code" property="clientCode" jdbcType="VARCHAR" />
		<result column="delivery_no" property="deliveryNo" jdbcType="VARCHAR" />
		<result column="trade_terms" property="tradeTerms" jdbcType="VARCHAR" />
		<result column="trade_area" property="tradeArea" jdbcType="VARCHAR" />
        <result column="client_name" property="clientName" jdbcType="VARCHAR" />
        <result column="delivery_name" property="deliveryName" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="forward_no" property="forwardNo" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     t.sid
     ,t.trade_code
     ,t.insert_user
     ,t.insert_time
     ,t.update_user
     ,t.update_time
     ,t.insert_user_name
     ,t.update_user_name
     ,t.status
     ,t.booking_list_no
     ,t.forward_code
     ,t.forward_name
     ,t.booking_id
     ,t.hawb
     ,t.etd
     ,t.ctn
     ,t.note
     ,t.traf_mode
     ,t.traf_name
     ,t.cietd_date
     ,t.ccetd_date
     ,t.destination_country
     ,t.dest_port
     ,t.client_code
     ,t.delivery_no
     ,t.trade_terms
     ,t.trade_area
     ,t.forward_no
     ,t.remark
    </sql>
    <sql id="condition">
        and t.trade_code = #{tradeCode}
    <if test="sid != null and sid != ''">
        and t.sid = #{sid}
    </if>
    <if test="insertUser != null and insertUser != ''">
        and (t.insert_user like concat(concat('%',#{insertUser}),'%') or t.insert_user_name like concat(concat('%',#{insertUser}),'%'))
    </if>
    <if test="insertUserName != null and insertUserName != ''">
		and t.insert_user_name = #{insertUserName}
	</if>
    <if test="updateUser != null and updateUser != ''">
		and (t.update_user like concat(concat('%',#{updateUser}),'%') or t.update_user_name like concat(concat('%',#{updateUser}),'%'))
	</if>
    <if test="status != null and status != ''">
		and t.status = #{status}
	</if>
    <if test="bookingListNo != null and bookingListNo != ''">
		and t.booking_list_no like concat(concat('%',#{bookingListNo}),'%')
	</if>
    <if test="forwardCode != null and forwardCode != ''">
		and t.forward_code = #{forwardCode}
	</if>
    <if test="forwardName != null and forwardName != ''">
		and t.forward_name = #{forwardName}
	</if>
    <if test="bookingId != null and bookingId != ''">
		and t.booking_id like concat(concat('%',#{bookingId}),'%')
	</if>
    <if test="hawb != null and hawb != ''">
		and t.hawb like concat(concat('%',#{hawb}),'%')
	</if>
        <if test="_databaseId == 'oracle' and etdFrom != null and etdFrom != ''">
            <![CDATA[ and t.etd >= to_date(#{etdFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and etdTo != null and etdTo != ''">
            <![CDATA[ and t.etd <= to_date(#{etdTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and etdFrom != null and etdFrom != ''">
            <![CDATA[ and t.etd >= to_timestamp(#{etdFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="_databaseId == 'postgresql' and etdTo != null and etdTo != ''">
            <![CDATA[ and t.etd <= to_timestamp(#{etdTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp ]]>
        </if>
    <if test="ctn != null and ctn != ''">
		and t.ctn = #{ctn}
	</if>
    <if test="note != null and note != ''">
		and t.note like concat(concat('%',#{note}),'%')
	</if>
    <if test="trafMode != null and trafMode != ''">
		and t.traf_mode = #{trafMode}
	</if>
    <if test="trafName != null and trafName != ''">
		and t.traf_name = #{trafName}
	</if>
        <if test="_databaseId == 'oracle' and cietdDateFrom != null and cietdDateFrom != ''">
            <![CDATA[ and t.cietd_date >= to_date(#{cietdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and cietdDateTo != null and cietdDateTo != ''">
            <![CDATA[ and t.cietd_date <= to_date(#{cietdDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and cietdDateFrom != null and cietdDateFrom != ''">
            <![CDATA[ and t.cietd_date >= to_date(#{cietdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
        </if>
        <if test="_databaseId == 'postgresql' and cietdDateTo != null and cietdDateTo != ''">
            <![CDATA[ and t.cietd_date <= to_date(#{cietdDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
        </if>
        <if test="_databaseId == 'oracle' and ccetdDateFrom != null and ccetdDateFrom != ''">
            <![CDATA[ and t.ccetd_date >= to_date(#{ccetdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and ccetdDateTo != null and ccetdDateTo != ''">
            <![CDATA[ and t.ccetd_date <= to_date(#{ccetdDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and ccetdDateFrom != null and ccetdDateFrom != ''">
            <![CDATA[ and t.ccetd_date >= to_date(#{ccetdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
        </if>
        <if test="_databaseId == 'postgresql' and ccetdDateTo != null and ccetdDateTo != ''">
            <![CDATA[ and t.ccetd_date <= to_date(#{ccetdDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
        </if>
    <if test="destinationCountry != null and destinationCountry != ''">
		and t.destination_country = #{destinationCountry}
	</if>
    <if test="destPort != null and destPort != ''">
		and t.dest_port = #{destPort}
	</if>
    <if test="clientCode != null and clientCode != ''">
		and t.client_code = #{clientCode}
	</if>
    <if test="deliveryNo != null and deliveryNo != ''">
		and t.delivery_no like concat(concat('%',#{deliveryNo}),'%')
	</if>
    <if test="tradeTerms != null and tradeTerms != ''">
		and t.trade_terms = #{tradeTerms}
	</if>
    <if test="tradeArea != null and tradeArea != ''">
		and t.trade_area = #{tradeArea}
	</if>
    <if test="forwardNo != null and forwardNo != ''">
        and t.forward_no like concat(concat('%',#{forwardNo}),'%')
    </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="decEPlanHeadResultMap" parameterType="com.dcjet.cs.plane.model.GwDecEPlanHead">
        SELECT
        <include refid="Base_Column_List" />
        ,p.company_name as client_Name
        ,p.address
        ,c.company_name as delivery_Name
        FROM
        t_gw_dec_e_plan_head t
        left join t_bi_client_information p on t.trade_code=p.trade_code and t.client_Code=p.customer_code and p.customer_type='CLI'
        left join t_bi_client_information c on t.trade_code=c.trade_code and t.delivery_No=c.customer_code and c.customer_type='CLI'
        <where>
            <include refid="condition"></include>
        </where>
        order by t.status,t.ccetd_date,t.booking_list_no desc,t.sid
    </select>
    <resultMap id="decEPlanHeadListResultMap" type="com.dcjet.cs.plane.model.GwDecEPlanHeadList">
        <id column="sid" property="sid" jdbcType="VARCHAR" />
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="booking_list_no" property="bookingListNo" jdbcType="VARCHAR" />
        <result column="forward_code" property="forwardCode" jdbcType="VARCHAR" />
        <result column="forward_name" property="forwardName" jdbcType="VARCHAR" />
        <result column="booking_id" property="bookingId" jdbcType="VARCHAR" />
        <result column="hawb" property="hawb" jdbcType="VARCHAR" />
        <result column="etd" property="etd" jdbcType="DATE" />
        <result column="ctn" property="ctn" jdbcType="VARCHAR" />
        <result column="note" property="note" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="traf_mode" property="trafMode" jdbcType="VARCHAR" />
        <result column="traf_name" property="trafName" jdbcType="VARCHAR" />
        <result column="cietd_date" property="cietdDate" jdbcType="DATE" />
        <result column="ccetd_date" property="ccetdDate" jdbcType="DATE" />
        <result column="destination_country" property="destinationCountry" jdbcType="VARCHAR" />
        <result column="dest_port" property="destPort" jdbcType="VARCHAR" />
        <result column="client_code" property="clientCode" jdbcType="VARCHAR" />
        <result column="delivery_no" property="deliveryNo" jdbcType="VARCHAR" />
        <result column="trade_terms" property="tradeTerms" jdbcType="VARCHAR" />
        <result column="trade_area" property="tradeArea" jdbcType="VARCHAR" />
        <result column="client_name" property="clientName" jdbcType="VARCHAR" />
        <result column="delivery_name" property="deliveryName" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="linked_no" property="linkedNo" jdbcType="VARCHAR" />
        <result column="line_no" property="lineNo" jdbcType="NUMERIC" />
        <result column="plan_line_no" property="planLineNo" jdbcType="NUMERIC" />
        <result column="bond_mark" property="bondMark" jdbcType="VARCHAR" />
        <result column="g_mark" property="GMark" jdbcType="VARCHAR" />
        <result column="fac_g_no" property="facGNo" jdbcType="VARCHAR" />
        <result column="g_name" property="GName" jdbcType="VARCHAR" />
        <result column="qty" property="qty" jdbcType="NUMERIC" />
        <result column="unit_erp" property="unitErp" jdbcType="VARCHAR" />
        <result column="dec_price" property="decPrice" jdbcType="NUMERIC" />
        <result column="dec_total" property="decTotal" jdbcType="NUMERIC" />
        <result column="curr" property="curr" jdbcType="VARCHAR" />
        <result column="net_wt" property="netWt" jdbcType="NUMERIC" />
        <result column="gross_wt" property="grossWt" jdbcType="NUMERIC" />
        <result column="so_no" property="soNo" jdbcType="VARCHAR" />
        <result column="so_line_no" property="soLineNo" jdbcType="NUMERIC" />
        <result column="so_date" property="soDate" jdbcType="VARCHAR" />
        <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR" />
        <result column="delivery_po_no" property="deliveryPoNo" jdbcType="VARCHAR" />
        <result column="delivery_po_line_no" property="deliveryPoLineNo" jdbcType="NUMERIC" />
        <result column="sold_po_no" property="soldPoNo" jdbcType="VARCHAR" />
        <result column="sold_po_line_no" property="soldPoLineNo" jdbcType="NUMERIC" />
        <result column="last_modify_date" property="lastModifyDate" jdbcType="VARCHAR" />
        <result column="district_code" property="districtCode" jdbcType="VARCHAR" />
        <result column="market_order_no" property="marketOrderNo" jdbcType="VARCHAR" />
        <result column="container_num" property="containerNum" jdbcType="NUMERIC" />
        <result column="pack_num" property="packNum" jdbcType="NUMERIC" />
        <result column="volume" property="volume" jdbcType="NUMERIC" />
        <result column="refused_reason" property="refusedReason" jdbcType="VARCHAR" />
        <result column="customer_g_no" property="customerGNo" jdbcType="VARCHAR" />
        <result column="head_id" property="headId" jdbcType="VARCHAR" />
        <result column="list_sid" property="listSid" jdbcType="VARCHAR" />
        <result column="forward_no" property="forwardNo" jdbcType="VARCHAR" />
        <result column="list_cietd_date" property="listCietdDate" jdbcType="DATE" />
        <result column="list_ccetd_date" property="listCcetdDate" jdbcType="DATE" />
    </resultMap>
    <select id="getHeadList" resultMap="decEPlanHeadListResultMap" parameterType="com.dcjet.cs.plane.model.GwDecEPlanHead">
        SELECT
        t.sid
        ,t.trade_code
        ,t.insert_user
        ,t.insert_time
        ,t.update_user
        ,t.update_time
        ,t.insert_user_name
        ,t.update_user_name
        ,t.status
        ,t.booking_list_no
        ,t.forward_code
        ,t.forward_name
        ,t.booking_id
        ,t.hawb
        ,t.etd
        ,t.ctn
        ,t.note
        ,t.traf_mode
        ,t.traf_name
        ,t.cietd_date
        ,t.ccetd_date
        ,t.destination_country
        ,t.dest_port
        ,t.client_code
        ,t.delivery_no
        ,t.trade_terms
        ,t.trade_area
        ,t.forward_no
        ,t.remark
        ,p.company_name as client_Name
        ,p.address
        ,c.company_name as delivery_Name
        ,l.linked_no
        ,l.line_no
        ,l.plan_line_no
        ,l.bond_mark
        ,l.g_mark
        ,l.fac_g_no
        ,l.qty
        ,l.unit_erp
        ,l.dec_price
        ,l.dec_total
        ,l.curr
        ,l.net_wt
        ,l.gross_wt
        ,l.so_no
        ,l.so_line_no
        ,l.so_date
        ,l.invoice_no
        ,l.delivery_po_no
        ,l.delivery_po_line_no
        ,l.sold_po_no
        ,l.sold_po_line_no
        ,l.last_modify_date
        ,l.district_code
        ,l.market_order_no
        ,l.container_num
        ,l.pack_num
        ,l.volume
        ,l.refused_reason
        ,l.customer_g_no
        ,l.head_id
        ,l.list_sid
        ,l.g_name
        ,l.shipment_id
        ,l.cietd_date list_cietd_date
        ,l.ccetd_date list_ccetd_date
        FROM
        t_gw_dec_e_plan_head t
        left join t_gw_dec_e_plan_list l on t.sid=l.head_id
        left join t_bi_client_information p on t.trade_code=p.trade_code and t.client_Code=p.customer_code and p.customer_type='CLI'
        left join t_bi_client_information c on t.trade_code=c.trade_code and t.delivery_No=c.customer_code and c.customer_type='CLI'
        <where>
            and t.trade_code = #{tradeCode}
            <choose>
                <when test="shipmentId != null and shipmentId.indexOf(',') !=-1">
                    and l.shipment_id in
                    <foreach item="item" index="index" collection="shipmentId.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="shipmentId != null and shipmentId != ''">
                    and l.shipment_id like concat(concat('%',#{shipmentId}),'%')
                </when>
            </choose>
            <if test="bookingId != null and bookingId != ''">
                and t.booking_id like concat(concat('%',#{bookingId}),'%')
            </if>
            <if test="bookingListNo != null and bookingListNo != ''">
                and t.booking_list_no like concat(concat('%',#{bookingListNo}),'%')
            </if>
            <if test="linkedNo != null and linkedNo != ''">
                and l.linked_no like concat(concat('%',#{linkedNo}),'%')
            </if>
            <if test="lineNo != null and lineNo != ''">
                and l.line_no = #{lineNo}
            </if>
            <if test="GName != null and GName != ''">
                and l.g_name like concat(concat('%',#{GName}),'%')
            </if>
            <if test="_databaseId == 'oracle' and cietdDateFrom != null and cietdDateFrom != ''">
                <![CDATA[ and t.cietd_date >= to_date(#{cietdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="_databaseId == 'oracle' and cietdDateTo != null and cietdDateTo != ''">
                <![CDATA[ and t.cietd_date <= to_date(#{cietdDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="_databaseId == 'postgresql' and cietdDateFrom != null and cietdDateFrom != ''">
                <![CDATA[ and t.cietd_date >= to_date(#{cietdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
            </if>
            <if test="_databaseId == 'postgresql' and cietdDateTo != null and cietdDateTo != ''">
                <![CDATA[ and t.cietd_date <= to_date(#{cietdDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
            </if>
            <if test="_databaseId == 'oracle' and ccetdDateFrom != null and ccetdDateFrom != ''">
                <![CDATA[ and t.ccetd_date >= to_date(#{ccetdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="_databaseId == 'oracle' and ccetdDateTo != null and ccetdDateTo != ''">
                <![CDATA[ and t.ccetd_date <= to_date(#{ccetdDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
            </if>
            <if test="_databaseId == 'postgresql' and ccetdDateFrom != null and ccetdDateFrom != ''">
                <![CDATA[ and t.ccetd_date >= to_date(#{ccetdDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
            </if>
            <if test="_databaseId == 'postgresql' and ccetdDateTo != null and ccetdDateTo != ''">
                <![CDATA[ and t.ccetd_date <= to_date(#{ccetdDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
            </if>
            <if test="insertUser != null and insertUser != ''">
                and (t.insert_user like concat(concat('%',#{insertUser}),'%') or t.insert_user_name like concat(concat('%',#{insertUser}),'%'))
            </if>
            <if test="updateUser != null and updateUser != ''">
                and (t.update_user like concat(concat('%',#{updateUser}),'%') or t.update_user_name like concat(concat('%',#{updateUser}),'%'))
            </if>
            <choose>
                <when test="bondMark == 'N'.toString()">
                    and (l.bond_mark IS NULL or l.bond_mark='')
                </when>
                <when test="bondMark != null and bondMark != '' ">
                    and l.bond_mark = #{bondMark}
                </when>
            </choose>
            <choose>
                <when test="GMark == 'N'.toString()">
                    and (l.g_mark IS NULL or l.g_mark='')
                </when>
                <when test="GMark != null and GMark != '' ">
                    and l.g_mark = #{GMark}
                </when>
            </choose>
            <if test="facGNo != null and facGNo != ''">
                and l.fac_g_no like concat(concat('%',#{facGNo}),'%')
            </if>
            <if test="clientCode != null and clientCode != ''">
                and t.client_code = #{clientCode}
            </if>
            <if test="deliveryNo != null and deliveryNo != ''">
                and t.delivery_no like concat(concat('%',#{deliveryNo}),'%')
            </if>
            <if test="soNo != null and soNo != ''">
                and l.so_no like concat(concat('%',#{soNo}),'%')
            </if>
            <if test="soLineNo != null">
                and l.so_line_no = #{soLineNo}
            </if>
            <if test="forwardNo != null and forwardNo != ''">
                and t.forward_no like concat(concat('%',#{forwardNo}),'%')
            </if>
            <if test="hawb != null and hawb != ''">
                and t.hawb like concat(concat('%',#{hawb}),'%')
            </if>
            <if test="status != null and status != ''">
                and t.status = #{status}
            </if>
        </where>
        order by t.booking_list_no desc,l.insert_time desc,l.sid
    </select>
    <select id="getListBySids" resultMap="decEPlanHeadResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_gw_dec_e_plan_head t where t.sid in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        <if test='_databaseId != "postgresql" '>
            begin
        </if>
        update t_gw_dec_e_apply set status='0' where sid in
        (
            select list_sid from t_gw_dec_e_plan_list where head_ID in
            <foreach collection="list"  item="item" open="(" separator="," close=")"  >
                #{item}
            </foreach>
        );
        delete from t_gw_dec_e_plan_list t where t.head_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        delete from t_gw_dec_e_plan_cabinet t where t.head_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        delete from t_gw_dec_e_plan_head t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        <if test='_databaseId != "postgresql" '>
            end;
        </if>
    </delete>

    <update id="setStatusBySids" parameterType="java.util.List">
        update t_gw_dec_e_plan_head set status=#{status} where sid in
        <foreach collection="sids"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
        update t_gw_dec_e_plan_list set status='1' where head_id in
        <foreach collection="sids"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </update>

    <!-- 列表查询 and 条件 begin-->
    <select id="getMaxOrderNo"  resultType="java.lang.String">
        select max(booking_list_no) bookingListNo from t_gw_dec_e_plan_head where TRADE_CODE = #{tradeCode} and booking_list_no like #{prefix} || '%'
    </select>
    <select id="getIDCount"  resultType="java.lang.Integer">
        select count(1) from t_gw_dec_e_plan_head where TRADE_CODE = #{tradeCode} and booking_ID = #{bookingId} and sid!=#{sid}
    </select>

<!--    <update id="changeHawb">-->
<!--        <if test='_databaseId != "postgresql" '>-->
<!--            begin-->
<!--        </if>-->
<!--        update t_gw_dec_e_send_head set hawb=#{hawb} where head_id in (select sid from t_gw_dec_e_plan_head where TRADE_CODE = #{tradeCode} and Booking_ID=#{bookingId});-->
<!--        update t_dec_erp_e_head_n set hawb=#{hawb} where TRADE_CODE = #{tradeCode} and Booking_ID=#{bookingId};-->
<!--        update t_gw_dec_e_scheduling_head set hawb=#{hawb} where head_id in (select sid  from t_dec_erp_e_head_n where TRADE_CODE = #{tradeCode} and Booking_ID=#{bookingId});-->
<!--        update t_dec_e_bill_head set hawb=#{hawb} where head_id in (select sid  from t_dec_erp_e_head_n where TRADE_CODE = #{tradeCode} and Booking_ID=#{bookingId});-->
<!--        update t_dec_e_entry_head set hawb=#{hawb} where erp_head_id in (select sid  from t_dec_erp_e_head_n where TRADE_CODE = #{tradeCode} and Booking_ID=#{bookingId});-->
<!--        <if test='_databaseId != "postgresql" '>-->
<!--            end;-->
<!--        </if>-->
<!--    </update>-->
<!--    <update id="changeForward">-->
<!--        <if test='_databaseId != "postgresql" '>-->
<!--            begin-->
<!--        </if>-->
<!--        update t_gw_dec_e_send_head set forward_no=#{forwardNo} where head_id in (select sid from t_gw_dec_e_plan_head where TRADE_CODE = #{tradeCode} and Booking_ID=#{bookingId});-->
<!--        update t_dec_erp_e_head_n set forward_no=#{forwardNo} where TRADE_CODE = #{tradeCode} and Booking_ID=#{bookingId};-->
<!--        <if test='_databaseId != "postgresql" '>-->
<!--            end;-->
<!--        </if>-->
<!--    </update>-->

    <select id="getBookingListId" resultType="java.lang.String">
        select booking_list_no from t_gw_dec_e_plan_head where booking_list_no=#{bookingListId}
    </select>
</mapper>
