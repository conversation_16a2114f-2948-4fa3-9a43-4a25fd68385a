package com.dcjet.cs.plane.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-1-26
 */
@Setter
@Getter
@Table(name = "t_gw_dec_e_send_list")
public class GwDecESendList implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键,GUID
     */
    @Id
    @Column(name = "sid")
    private String sid;
    /**
     * 企业代码
     */
    @Column(name = "trade_code")
    private String tradeCode;
    /**
     * 建表员
     */
    @Column(name = "insert_user")
    private String insertUser;
    /**
     * 建表日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "insert_time")
    private Date insertTime;
    /**
     * 变更人
     */
    @Column(name = "update_user")
    private String updateUser;
    /**
     * 变更日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 制单人姓名
     */
    @Column(name = "insert_user_name")
    private String insertUserName;
    /**
     * 变更人姓名
     */
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 状态，0：暂存
     */
    @Column(name = "status")
    private String status;
    /**
     * 单据号
     */
    @Column(name = "linked_no")
    private String linkedNo;
    /**
     * 序号
     */
    @Column(name = "line_no")
    private Integer lineNo;
    /**
     * 计划行编码
     */
    @Column(name = "plan_line_no")
    private Integer planLineNo;
    /**
     * 保完税标记
     */
    @Column(name = "bond_mark")
    private String bondMark;
    /**
     * 物料类型 I 料件  E 成品
     */
    @Column(name = "g_mark")
    private String GMark;
    /**
     * 企业料号
     */
    @Column(name = "fac_g_no")
    private String facGNo;
    /**
     * 商品名称
     */
    @Column(name = "g_name")
    private String GName;
    /**
     * 数量
     */
    @Column(name = "qty")
    private BigDecimal qty;
    /**
     * 交易单位
     */
    @Column(name = "unit_erp")
    private String unitErp;
    /**
     * 单价
     */
    @Column(name = "dec_price")
    private BigDecimal decPrice;
    /**
     * 总价
     */
    @Column(name = "dec_total")
    private BigDecimal decTotal;
    /**
     * 币制
     */
    @Column(name = "curr")
    private String curr;
    /**
     * 净重
     */
    @Column(name = "net_wt")
    private BigDecimal netWt;
    /**
     * 毛重
     */
    @Column(name = "gross_wt")
    private BigDecimal grossWt;
    /**
     * SO号(销售订单号)
     */
    @Column(name = "so_no")
    private String soNo;
    /**
     * SO行号(销售订单行号)
     */
    @Column(name = "so_line_no")
    private Integer soLineNo;
    /**
     * SO日期(销售订单日期)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "so_date")
    private Date soDate;
    /**
     * SO日期(销售订单日期)-开始
     */
    @Transient
    private String soDateFrom;
    /**
     * SO日期(销售订单日期)-结束
     */
    @Transient
    private String soDateTo;
    /**
     * 发票号
     */
    @Column(name = "invoice_no")
    private String invoiceNo;
    /**
     * 送达方PO号
     */
    @Column(name = "delivery_po_no")
    private String deliveryPoNo;
    /**
     * 送达方PO行号
     */
    @Column(name = "delivery_po_line_no")
    private Integer deliveryPoLineNo;
    /**
     * 售达方PO号
     */
    @Column(name = "sold_po_no")
    private String soldPoNo;
    /**
     * 售达方PO行号
     */
    @Column(name = "sold_po_line_no")
    private Integer soldPoLineNo;
    /**
     * ERP创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "last_modify_date")
    private Date lastModifyDate;
    /**
     * ERP创建时间-开始
     */
    @Transient
    private String lastModifyDateFrom;
    /**
     * ERP创建时间-结束
     */
    @Transient
    private String lastModifyDateTo;
    /**
     * 境内货源地
     */
    @Column(name = "district_code")
    private String districtCode;
    /**
     * 卖场订单DI PO
     */
    @Column(name = "market_order_no")
    private String marketOrderNo;
    /**
     * 台/箱
     */
    @Column(name = "container_num")
    private Integer containerNum;
    /**
     * 件数
     */
    @Column(name = "pack_num")
    private BigDecimal packNum;
    /**
     * 体积
     */
    @Column(name = "volume")
    private BigDecimal volume;
    /**
     * 拒绝原因
     */
    @Column(name = "refused_reason")
    private String refusedReason;
    /**
     * 客户代码(售达方代码)
     */
    @Column(name = "client_code")
    private String clientCode;
    /**
     * 送达方编码
     */
    @Column(name = "delivery_no")
    private String deliveryNo;
    /**
     * 订单计划开船日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "cietd_date")
    private Date cietdDate;
    /**
     * 订单计划开船日-开始
     */
    @Transient
    private String cietdDateFrom;
    /**
     * 订单计划开船日-结束
     */
    @Transient
    private String cietdDateTo;
    /**
     * 贸易条款
     */
    @Column(name = "trade_terms")
    private String tradeTerms;
    /**
     * 贸易条款(地区)
     */
    @Column(name = "trade_area")
    private String tradeArea;
    /**
     * 客户料号
     */
    @Column(name = "customer_g_no")
    private String customerGNo;
    /**
     * 表头ID
     */
    @Column(name = "head_id")
    private String headId;
    /**
     * 订舱流水号
     */
    @Column(name = "booking_list_no")
    private String bookingListNo;
    /**
     * plan表SID
     */
    @Column(name = "list_sid")
    private String listSid;
    /**
     * cabinet表SID
     */
    @Column(name = "cabinet_sid")
    private String cabinetSid;

    /**
     * 柜型
     */
    @Column(name = "cabinet_type")
    private String cabinetType;
    /**
     * 柜号
     */
    @Column(name = "container_no")
    private String containerNo;
    /**
     * 拖柜日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "cabinet_date")
    private Date cabinetDate;

    /**
     * 拖柜日期-开始
     */
    @Transient
    private String cabinetDateFrom;
    /**
     * 拖柜日期-结束
     */
    @Transient
    private String cabinetDateTo;
    /**
     * 拖柜备注
     */
    @Column(name = "note")
    private String note;
    /**
     * 分票号
     */
    @Column(name = "branch_no")
    private String branchNo;
    /**
     * 发货单号
     */
    @Column(name = "bill_no")
    private String billNo;
    /**
     * 提取日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "extract_time")
    private Date extractTime;
    /**
     * 征免方式
     */
    @Column(name = "DUTY_MODE")
    private String dutyMode;

    /**
     * 最终目的国
     */
    @Transient
    private String destinationCountry;
    /**
     * 售达方name
     */
    @Transient
    private String clientName;
    /**
     * 送达方name
     */
    @Transient
    private String deliveryName;

    /**
     * 发送SAP流水单号
     */
    @Column(name = "SEND_NO")
    private String sendNo;

    @Transient
    private Integer rowsid;
    /**
     * 拆单行号
     */
    @Column(name = "split_line_no")
    private Integer splitLineNo;

    @Column(name = "shipment_id")
    private String shipmentId;

    @Column(name = "data_Source")
    private String dataSource;
}
