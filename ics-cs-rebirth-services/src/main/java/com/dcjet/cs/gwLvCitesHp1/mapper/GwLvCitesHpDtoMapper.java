package com.dcjet.cs.gwLvCitesHp1.mapper;
import com.dcjet.cs.dto.gwLvCitesHp1.*;
import com.dcjet.cs.gwLvCitesHp1.model.GwLvCitesHp;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2022-4-13
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwLvCitesHpDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwLvCitesHpDto toDto(GwLvCitesHp po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwLvCitesHp toPo(GwLvCitesHpParam param);
    /**
     * 数据库原始数据更新
     * @param gwLvCitesHpParam
     * @param gwLvCitesHp
     */
    void updatePo(GwLvCitesHpParam gwLvCitesHpParam, @MappingTarget GwLvCitesHp gwLvCitesHp);

    void updatePo(GwLvCitesHpDto dto, @MappingTarget GwLvCitesHp gwLvCitesHp);

    default void patchPo(GwLvCitesHpParam gwLvCitesHpParam, GwLvCitesHp gwLvCitesHp) {
        // TODO 自行实现局部更新
    }
}
