package com.dcjet.cs.gwLvCitesHp1.dao;
import com.dcjet.cs.gwLvCitesHp1.model.GwLvCitesHp;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* GwLvCitesHp
* <AUTHOR>
* @date: 2022-4-13
*/
public interface GwLvCitesHpMapper extends Mapper<GwLvCitesHp> {
    /**
     * 查询获取数据
     * @param gwLvCitesHp
     * @return
     */
    List<GwLvCitesHp> getList(GwLvCitesHp gwLvCitesHp);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int stopHpByExpired(List<String> hpCodes);
}
