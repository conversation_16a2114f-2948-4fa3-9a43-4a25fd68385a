package com.dcjet.cs.gwLvCitesProofMain.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2022-6-20
 */
@Setter
@Getter
@Table(name = "t_gw_lv_cites_proof_main")
public class GwLvCitesProofMain implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * SID
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 插入时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 插入人
     */
	@Column(name = "insert_user")
	private  String insertUser;

	@Transient
	private String trMark;

	/**
     * 修改日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 修改人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 单据状态：0-暂存  1-证明办结  2-关联SID  3-报关完成
     */
	@Column(name = "status")
	private  String status;
	/**
     * 品牌
     */
	@Column(name = "brand_code")
	private  String brandCode;
	/**
     * 进口证明流水号
     */
	@Column(name = "proof_no")
	private  String proofNo;
	/**
     * 允许进口证明号
     */
	@Column(name = "import_proof_no")
	private  String importProofNo;
	/**
     * 证明办结日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "proof_punish_date")
	private  Date proofPunishDate;
	/**
     * Shipment ID
     */
	@Column(name = "shipment_id")
	private  String shipmentId;
	/**
     * 关联日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "linke_date")
	private  Date linkeDate;
	/**
     * 报关日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "cn_approval_date")
	private  Date cnApprovalDate;
	/**
     * 报关单号
     */
	@Column(name = "entry_no")
	private  String entryNo;

	@Transient
	private List<String> brandCodes;

	@Transient
	private String batchFileNo;

	@Transient
	private String outSideNo;

	@Transient
	private String isQuota;

	@Transient
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date outSideProofValid;

	@Transient
	private String brandCodeNameAll;

	@Transient
	private String batchFileMianSid;

	@Transient
	private String transportNo;

	@Transient
	private String batchFileSerial;
}
