package com.dcjet.cs.gwLvCitesProofMain.service;

import com.alibaba.druid.util.StringUtils;

public class EnumLvDictionary {
    /**
     * 操作方式
     */
    public enum OperateType {
        ADD,IMPORT_EDIT,IMPORT_ADD,DELETE,EDIT,EXPORT
    }

    public enum CTF_LAGE{
        blank,Yes,No,NA;

        public static CTF_LAGE valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            return valueOf(ordinal).name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }


    public enum ZERO_ONE{
        否,是;

        public static ZERO_ONE valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            ZERO_ONE obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }

    public enum DIFF_FLAG{
        无,有;

        public static DIFF_FLAG valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            DIFF_FLAG obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }


    public enum DATA_ORIGIN{
        手工录入,系统导入;

        public static DATA_ORIGIN valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            DATA_ORIGIN obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }


    // 处理状态  0-暂存 1-发送内审 2-发送主数据 3-内审退回 4-内审通过 5-修改 6-待归类
    public enum STATUS{
        暂存,发送内审,发送主数据,内审退回,内审通过,修改,待归类;

        public static STATUS valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            STATUS obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }

    public enum STATUS_TITY{
        暂存,发送主数据,主数据退回,内审通过,待归类;

        public static STATUS_TITY valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            STATUS_TITY obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }

    //状态  0暂存 1-发送内审 3-内审退回   4-内审通过
    public enum STATUS_MAIN{
        暂存,发送内审,NULL,内审退回,内审通过;

        public static STATUS_MAIN valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            STATUS_MAIN obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }


    public enum STATUS2{
        暂存,生成办证清单,外方证过期;

        public static STATUS2 valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            STATUS2 obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }

    public enum TR_MARK{
        TR,LM;

        public static TR_MARK valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            TR_MARK obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }

    /**
     * 是否有效(ROS) 0-失效，1-有效
     */
    public enum IS_EXPIRED{
        失效,有效;

        public static IS_EXPIRED valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            IS_EXPIRED obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }


    /**
     * 批件类别 0-普通类，1-特殊类
     */
    public enum BATCH_TYPE{
        普通类,特殊类;

        public static BATCH_TYPE valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            BATCH_TYPE obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }

    //CITES办证管理 单据状态：0-暂存，1-许可证办结，2-已使用 3-报关已完成
    public enum STATUS_PROOF{
        暂存,许可证办结,已使用,报关已完成;

        public static STATUS_PROOF valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            STATUS_PROOF obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }


    public enum YES_NO{
        NO,YES;

        public static YES_NO valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            YES_NO obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }

    public enum Package_Type{
        NULL,一类,二类,三类,其他;

        public static Package_Type valueOf(int ordinal) {
            if(ordinal < 0 || ordinal >= values().length)
                return null;

            return values()[ordinal];
        }

        public static String getName(int ordinal) {
            if(!isInclude(ordinal)) return "";

            Package_Type obj = valueOf(ordinal);
            return obj.ordinal() + "-" + obj.name();
        }

        public static String getName(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return "";

            return getName(Integer.parseInt(ordinal));
        }

        public static boolean isInclude(int ordinal) {
            return valueOf(ordinal) != null;
        }

        /**
         * true:存在
         * @param ordinal
         * @return
         */
        public static boolean isInclude(String ordinal) {
            if(StringUtils.isEmpty(ordinal)) return false;

            int _ordinal = Integer.parseInt(ordinal);
            return valueOf(_ordinal) != null;
        }
    }
}
