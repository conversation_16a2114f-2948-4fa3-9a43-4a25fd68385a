package com.dcjet.cs.common.service;

import com.dcjet.cs.Utils.chPojo.*;
import com.dcjet.cs.Utils.entryReturn.EntroNoParam;
import com.dcjet.cs.Utils.entryReturn.RestParam;
import com.dcjet.cs.common.dao.GwstdHttpConfigMapper;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.erp.dao.DecErpEHeadNMapper;
import com.dcjet.cs.erp.dao.DecErpIHeadNMapper;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.OkHttpUtils;
import com.dcjet.cs.util.variable.CommonVariable;
import com.google.common.base.Strings;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报关信息回填服务类(企业定制)
 *
 * @author: zhangjunlin
 * @date: 2019/11/8
 */
@Service
public class RobackService {

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource
    private BulkInsertConfig bulkInsertConfig;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private GwstdHttpConfigMapper gwstdHttpConfigMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private DecErpIHeadNMapper decErpIHeadNMapper;
    @Resource
    private DecErpEHeadNMapper decErpEHeadNMapper;

    public IBulkInsert getBulkInsertInstance() throws Exception {
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }

    /**
     * 调用企业Http接口，回填报关单号
     */
    public ResultObject invokeEntryNoHttpInterface(List<RestParam> restParams, UserInfoToken userInfo) {
        logger.debug("企业[" + userInfo.getCompany() + "]接口调用进来了");
        logger.info("企业[" + userInfo.getCompany() + "]接口调用进来了");
        ResultObject resultObject = ResultObject.createInstance(true);
        try {
            //根据企业代码获取企业配置的接口信息
            GwstdHttpConfig gwstdHttpConfig = commonService.getHttpConfigInfo(userInfo.getCompany(), Constants.ENTRY_NO_BACK);
            if (gwstdHttpConfig == null) {
                resultObject.setSuccess(false);
                resultObject.setCode(-1);
                resultObject.setMessage(xdoi18n.XdoI18nUtil.t("企业[") + userInfo.getCompany() + xdoi18n.XdoI18nUtil.t("]接口调用失败，请检查Erp接口参数配置！"));
            } else {
                for (RestParam restParam : restParams) {
                    if (gwstdHttpConfig.getExtendFiled1() != null) {
                        restParam.setTradeCode(gwstdHttpConfig.getExtendFiled1());
                    } else {
                        restParam.setTradeCode(userInfo.getCompany());
                    }
                }
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                if (Strings.isNullOrEmpty(gwstdHttpConfig.getBaseUrl()) || Strings.isNullOrEmpty(gwstdHttpConfig.getServiceUrl())) {
                    resultObject.setSuccess(false);
                    resultObject.setMessage(xdoi18n.XdoI18nUtil.t("未配置企业Http接口地址！"));
                    return resultObject;
                }
                String url = gwstdHttpConfig.getBaseUrl() + gwstdHttpConfig.getServiceUrl();
                logger.info("企业[" + userInfo.getCompany() + "]企业Http接口:" + url);
                String resJson = OkHttpUtils.httpPostJson(url, jsonObjectMapper.toJson(restParams));
                logger.info("企业[" + userInfo.getCompany() + "]接口调用成功：" + resJson);
                logger.info("企业[" + userInfo.getCompany() + "]接口调用结束");
            }
        } catch (Exception ex) {
            resultObject.setSuccess(false);
            resultObject.setMessage(xdoi18n.XdoI18nUtil.t("接口调用失败：") + ex.getMessage());
            logger.info("企业[" + userInfo.getCompany() + "]接口调用异常：" + ex.getMessage());
        }
        return resultObject;
    }

    private List<GwstdHttpConfig> getGwstdHttpConfigs(String type) {
        GwstdHttpConfig gwstdHttpConfig = new GwstdHttpConfig();
        gwstdHttpConfig.setType(type);
        return gwstdHttpConfigMapper.select(gwstdHttpConfig);
    }

    /**
     * 后台批量回填进出口报关单号/贸易方式
     */
    public void seqNoRoBackBatch() {
        //根据企业代码获取企业配置的接口信息
        List<GwstdHttpConfig> gwstdHttpConfigList = getGwstdHttpConfigs(Constants.INVOICE_BACK);// 指定类型为向Erp回填报关单号
        if (gwstdHttpConfigList.size() == 0) {
            logger.info("未查询到企业Erp接口参数配置");
            return;
        }
        for (GwstdHttpConfig erp : gwstdHttpConfigList) {
            try {
                logger.info("开始处理企业：[" + erp.getTradeCode() + "]的回填报关单数据");
                UserInfoToken userInfoToken = new UserInfoToken();
                List<SeqNoRoBackParam> decIparams = decErpIHeadNMapper.selectXCodeSendMark(erp.getTradeCode());
                List<SeqNoRoBackParam> decEparams = decErpEHeadNMapper.selectXCodeSendMark(erp.getTradeCode());
                decIparams.addAll(decEparams);
                if (CollectionUtils.isEmpty(decIparams)) {
                    logger.info("企业[" + erp.getTradeCode() + "]未匹配到需要回填的报关单号数据!");
                } else {
                    userInfoToken.setCompany(erp.getTradeCode());
                    if (Strings.isNullOrEmpty(erp.getBaseUrl()) || Strings.isNullOrEmpty(erp.getServiceUrl())) {
                        logger.info("企业[" + erp.getTradeCode() + "]未配置企业Http接口!");
                        continue;
                    }
                    String url = erp.getBaseUrl() + erp.getServiceUrl();
                    logger.info("企业[" + erp.getTradeCode() + "]企业Http接口:" + url);
                    ResultObject resultObject = seqNoRoBackAll(decIparams, url, userInfoToken);
                    if (resultObject.isSuccess()) {
                        logger.info("企业[" + erp.getTradeCode() + "]报关单号回填成功!");
                    } else {
                        logger.info("企业[" + erp.getTradeCode() + "]报关单号回填失败,异常为：" + resultObject.getMessage());
                    }
                }
                logger.info("企业：[" + erp.getTradeCode() + "]的回填数据结束，共处理条数：" + decIparams.size());
            } catch (Exception e) {
                logger.info("企业[" + erp.getTradeCode() + "]报关单号回填失败:" + e.getMessage());
            }
        }
    }

    public ResultObject seqNoRoBackAll(List<SeqNoRoBackParam> params, String url, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("回填成功"));
        List<RoBackRecord> records = new ArrayList<>();
        ResultObject resultObject = ResultObject.createInstance(true);
        try {
            JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
            for (SeqNoRoBackParam param : params) {
                RestSeqNoRoBackParam restSeqNoRoBackParam = new RestSeqNoRoBackParam();
                restSeqNoRoBackParam.setTRADE(param.getTRADE());
                restSeqNoRoBackParam.setXCODE(param.getXCODE());
                restSeqNoRoBackParam.setINVCODE(param.getINVCODE());
                restSeqNoRoBackParam.setXTYPE(param.getXTYPE());

                String json = jsonObjectMapper.toJson(restSeqNoRoBackParam);
                String resJson = OkHttpUtils.httpPostJson(url, json);
                ChHttpInvoiceResult httpResult = jsonObjectMapper.fromJson(resJson, ChHttpInvoiceResult.class);
                RoBackRecord roBackRecord = new RoBackRecord();
                roBackRecord.setSid(UUID.randomUUID().toString());
                roBackRecord.setHeadId(param.getSid());
                roBackRecord.setBackFillType(Constants.XCODE_FILL);
                if (param.getXTYPE().equals(ConstantsStatus.STATUS_0)) {
                    roBackRecord.setIeType(CommonVariable.IE_MARK_I);
                } else {
                    roBackRecord.setIeType(CommonVariable.IE_MARK_E);
                }
                roBackRecord.setTradeCode(userInfo.getCompany());
                roBackRecord.setInsertTime(new Date());
                roBackRecord.setInsertUser("XXL定时任务");
                if (httpResult.getCode() == 200 && "OK".equals(httpResult.getContent().getMsg())) {
                    roBackRecord.setSendMark(ConstantsStatus.STATUS_1);
                    records.add(roBackRecord);
                } else {
                    roBackRecord.setSendMark(ConstantsStatus.STATUS_0);
                    logger.info("核注清单" + param.getSid() + "回填报关单号错误!错误信息：" + httpResult.getContent().getMsg());
                }
            }
            insertBatchRoBackRec(records);
        } catch (Exception ex) {
            resultObject.setSuccess(false);
            logger.error("接口调用失败：" + ex.getMessage());
        }
        return result;
    }

    /**
     * 后台批量回填出口报关品名
     */
    public void gNameRoBackBatch() {
        //根据企业代码获取企业配置的接口信息
        List<GwstdHttpConfig> gwstdHttpConfigList = getGwstdHttpConfigs(Constants.G_NAME_BACK); //指定类型为向Erp回填报关单号
        if (gwstdHttpConfigList.size() == 0) {
            logger.info("未查询到企业Erp接口参数配置！");
            return;
        }
        for (GwstdHttpConfig erp : gwstdHttpConfigList) {
            try {
                logger.info("开始处理企业：[" + erp.getTradeCode() + "]的回填报关品名数据");
                UserInfoToken userInfoToken = new UserInfoToken();
                List<GNameRoBackParam> gNameRoBackParams = decErpEHeadNMapper.selectMakTxSendMark(erp.getTradeCode());
                if (CollectionUtils.isEmpty(gNameRoBackParams)) {
                    logger.info("企业[" + erp.getTradeCode() + "]未匹配到需要回填的报关品名数据!");
                } else {
                    userInfoToken.setCompany(erp.getTradeCode());
                    if (Strings.isNullOrEmpty(erp.getBaseUrl()) || Strings.isNullOrEmpty(erp.getServiceUrl())) {
                        logger.info("企业[" + erp.getTradeCode() + "]未配置企业Http接口!");
                        continue;
                    }
                    String url = erp.getBaseUrl() + erp.getServiceUrl();
                    logger.info("企业[" + erp.getTradeCode() + "]企业Http接口:" + url);
                    ResultObject resultObject = gNameRoBackAll(gNameRoBackParams, url, erp.getExtendFiled1(), userInfoToken);
                    if (resultObject.isSuccess()) {
                        logger.info("企业[" + erp.getTradeCode() + "]报关品名回填成功!");
                    } else {
                        logger.info("企业[" + erp.getTradeCode() + "]报关品名回填失败,异常为：" + resultObject.getMessage());
                    }
                }
                logger.info("企业：[" + erp.getTradeCode() + "]的回填数据结束，共处理条数：" + gNameRoBackParams.size());
            } catch (Exception e) {
                logger.error("企业[" + erp.getTradeCode() + "]报关品名回填失败:" + e.getMessage());
            }
        }
    }

    public ResultObject gNameRoBackAll(List<GNameRoBackParam> params, String url, String weaks, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("回填成功"));
        List<RoBackRecord> records = new ArrayList<>();
        List<Map<String, String>> restGNameRoBackParams = new ArrayList<>();
        ResultObject resultObject = ResultObject.createInstance(true);
        try {
            for (GNameRoBackParam param : params) {
                Map<String, String> model = new HashMap<>();
                model.put("HSCODE", param.getHSCODE());
                model.put("MAKTX", param.getMAKTX());
                model.put("MATNR", param.getMATNR());
                String unitConvert = pCodeHolder.getValue(PCodeType.UNIT, param.getMEINSC());
                if (StringUtils.isEmpty(unitConvert)) {
                    model.put("MEINS_C", param.getMEINSC());
                } else {
                    model.put("MEINS_C", unitConvert);
                }
                model.put("MSG", param.getMSG());
                model.put("WEAKS", weaks);
                restGNameRoBackParams.add(model);
            }
            JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();

            Map<String, Object> map = new HashMap<>();
            map.put("TAB_LIST", restGNameRoBackParams);
            String json = jsonObjectMapper.toJson(map);
            String resJson = OkHttpUtils.httpPostJson(url, json);
            ChHttpGNameResult httpResult = jsonObjectMapper.fromJson(resJson, ChHttpGNameResult.class);
            if (httpResult.getCode() == 200) {
                List<RoBackResult> roBackResults = httpResult.getContent().getTabList();
                Map<String, List<RoBackResult>> maps = roBackResults.stream().collect(Collectors.groupingBy(s -> "copGNo:[" + s.getMATNR() + "]"));
                Map<String, List<GNameRoBackParam>> recordMap = params.stream().collect(Collectors.groupingBy(s -> "copGNo:[" + s.getMATNR() + "]"));
                for (Map.Entry<String, List<RoBackResult>> entry : maps.entrySet()) {
                    String mapKey = entry.getKey();
                    RoBackResult value = entry.getValue().get(0);
                    RoBackRecord roBackRecord = new RoBackRecord();
                    roBackRecord.setSid(UUID.randomUUID().toString());
                    roBackRecord.setHeadId(recordMap.get(mapKey).get(0).getSid());
                    roBackRecord.setBackFillType(Constants.MAKTX_FILL);
                    roBackRecord.setIeType(CommonVariable.IE_MARK_E);
                    if (value.getMSG().indexOf("sucess") > 0) {
                        roBackRecord.setSendMark(ConstantsStatus.STATUS_1);
                    } else {
                        roBackRecord.setSendMark(ConstantsStatus.STATUS_0);
                        logger.info("核注清单表体：" + roBackRecord.getHeadId() + "回填报关品名失败");
                    }
                    roBackRecord.setTradeCode(userInfo.getCompany());
                    roBackRecord.setInsertTime(new Date());
                    roBackRecord.setInsertUser("XXL_JOB");
                    records.add(roBackRecord);
                }
                insertBatchRoBackRec(records);
                resultObject.setSuccess(true);
                resultObject.setMessage("接口调用成功");
            }
        } catch (Exception ex) {
            resultObject.setSuccess(false);
            resultObject.setMessage("接口调用失败：" + ex.getMessage());
        }
        return result;
    }

    public void insertBatchRoBackRec(List<RoBackRecord> list) {
        try {
            IBulkInsert iBulkInsert = getBulkInsertInstance();
            int intEffectCount = iBulkInsert.fastImport(list);
            logger.info("==========================本次入库数量" + intEffectCount + "条数据================================");
        } catch (BulkInsertException ex) {
            ex.getCommitRowCount();
        } catch (Exception ex) {
            logger.info("erp数据回填记录批量插入异常 ex：" + ex.getMessage() + "\r\n" + Arrays.toString(ex.getStackTrace()));
        }
    }

    //region 回填报关单号-对接企业ERP

    /**
     * 回填报关单号
     *
     * @param sids
     * @param userInfo
     * @return
     */
    public ResultObject roback(List<String> sids, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("回填成功"));
        List<RestParam> restParams = new ArrayList<>();
        List<EntroNoParam> entryNoBySids = decErpIHeadNMapper.selectEntryNoBySids(sids);
        List<String> newSids = new ArrayList<>();
        if (entryNoBySids.size() > 0) {
            for (EntroNoParam param : entryNoBySids) {
                String[] entryList = CommonService.distinctData(param.getLinkedNo().split(","));
                for (int i = 0; i < entryList.length; i++) {
                    RestParam restParam = new RestParam();
                    restParam.setBillNo(entryList[i]);
                    restParam.setEntryNo(param.getEntryNo());
                    restParam.setTradeCode(userInfo.getCompany());
                    restParams.add(restParam);
                }
                newSids.add(param.getSid());
            }
            ResultObject resultObject = this.invokeEntryNoHttpInterface(restParams, userInfo);
            if (resultObject.isSuccess()) {
                //更新回填标志
                decErpIHeadNMapper.updateSendMark(newSids);
            } else {
                result = resultObject;
            }
        } else {
            result.setSuccess(false);
            result.setMessage(xdoi18n.XdoI18nUtil.t("无回填的报关单号信息"));
        }
        return result;
    }

}
