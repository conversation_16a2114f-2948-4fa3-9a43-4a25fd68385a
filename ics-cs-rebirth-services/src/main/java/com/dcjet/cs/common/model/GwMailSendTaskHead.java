package com.dcjet.cs.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-11-2
 */
@Setter
@Getter
@Table(name = "t_gw_mail_send_task_head")
public class GwMailSendTaskHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 唯一键
     */
	@Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 发送者(发送邮箱地址或邮件接口的sendid)
     */
	@Column(name = "sender")
	private  String sender;
	/**
     * 收件人
     */
	@Column(name = "recipient")
	private  String recipient;
	/**
     * 抄送人
     */
	@Column(name = "cc")
	private  String cc;
	/**
     * 主题
     */
	@Column(name = "subject")
	private  String subject;
	/**
     * 正文部分
     */
	@Column(name = "body")
	private  String body;
	/**
     * 业务类型(预警.RISK_WARRING)
     */
	@Column(name = "bussiness_type")
	private  String bussinessType;
	/**
     * 数据类型(证书有效期.CERT_VALID)
     */
	@Column(name = "daty_type")
	private  String datyType;
	/**
     * 发送状态(0.待发送 1.发送中 2.发送成功 3.发送失败)
     */
	@Column(name = "state")
	private  String state;
	/**
     * 是否定制业务(0.标准 1.定制)
     */
	@Column(name = "is_customized")
	private  String isCustomized;
	/**
     * 发送失败原因
     */
	@Column(name = "err_mess")
	private  String errMess;
	/**
     * 批次锁定标记
     */
	@Column(name = "lock_mark")
	private  String lockMark;
	/**
     * 企业代码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 创建人姓名
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 更新人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 更新人姓名
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 扩展信息,以json的方式记录
     */
	@Column(name = "extend_field")
	private  String extendField;
	/**
	 * 是否是html
	 */
	@Column(name = "is_html")
	private String isHtml;

	public GwMailSendTaskHead initData(UserInfoToken token) {
		this.sid = UUID.randomUUID().toString();
		this.tradeCode = token.getCompany();
		this.insertTime = new Date();
		this.insertUser = token.getUserNo();
		this.insertUserName = token.getUserName();
		return this;
	}
}
