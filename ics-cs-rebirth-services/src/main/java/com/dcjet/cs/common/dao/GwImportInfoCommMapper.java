package com.dcjet.cs.common.dao;

import com.dcjet.cs.common.model.GwImportInfoComm;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* GwImportError
* <AUTHOR>
* @date: 2021-9-28
*/
public interface GwImportInfoCommMapper extends Mapper<GwImportInfoComm> {
    /**
     * 查询获取数据
     * @param gwImportError
     * @return
     */
    List<GwImportInfoComm> getList(GwImportInfoComm gwImportError);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
