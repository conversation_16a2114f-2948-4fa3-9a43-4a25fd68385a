<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.returned.dao.GwReturnListMapper">
    <resultMap id="gwReturnListResultMap" type="com.dcjet.cs.returned.model.GwReturnList">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="fac_g_no" property="facGNo" jdbcType="VARCHAR" />
		<result column="cop_g_name" property="copGName" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="qty" property="qty" jdbcType="NUMERIC" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="price" property="price" jdbcType="NUMERIC" />
		<result column="destination_country" property="destinationCountry" jdbcType="VARCHAR" />
		<result column="origin_country" property="originCountry" jdbcType="VARCHAR" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="return_order_no" property="returnOrderNo" jdbcType="VARCHAR" />
		<result column="data_source" property="dataSource" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="bill_list_id" property="billListId" jdbcType="VARCHAR" />
		<result column="linked_no" property="linkedNo" jdbcType="VARCHAR" />
		<result column="line_no" property="lineNo" jdbcType="NUMERIC" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,fac_g_no
     ,cop_g_name
     ,status
     ,COALESCE(qty, 0) qty
     ,unit
     ,COALESCE(price, 0) price
     ,destination_country
     ,origin_country
     ,curr
     ,return_order_no
     ,data_source
     ,trade_code
     ,insert_user
     ,insert_time
     ,insert_user_name
     ,update_user
     ,update_time
     ,update_user_name
     ,head_id
     ,bill_list_id
     ,linked_no
     ,line_no
    </sql>
    <sql id="condition">
    <if test="facGNo != null and facGNo != ''">
		and fac_g_no like concat('%', concat(#{facGNo}, '%'))
	</if>
    <if test="copGName != null and copGName != ''">
		and cop_g_name like concat('%', concat(#{copGName}, '%'))
	</if>
    <if test="status != null and status != ''">
		and status = #{status}
	</if>
    <if test="qty != null and qty != ''">
		and qty = #{qty}
	</if>
    <if test="unit != null and unit != ''">
		and unit = #{unit}
	</if>
    <if test="price != null and price != ''">
		and price = #{price}
	</if>
    <if test="destinationCountry != null and destinationCountry != ''">
		and destination_country = #{destinationCountry}
	</if>
    <if test="curr != null and curr != ''">
		and curr = #{curr}
	</if>
    <if test="returnOrderNo != null and returnOrderNo != ''">
		and return_order_no = #{returnOrderNo}
	</if>
    <if test="dataSource != null and dataSource != ''">
		and data_source = #{dataSource}
	</if>
		and trade_code = #{tradeCode}
    <if test="insertUserName != null and insertUserName != ''">
		and insert_user_name = #{insertUserName}
	</if>
    <if test="updateUserName != null and updateUserName != ''">
		and update_user_name = #{updateUserName}
	</if>
    <if test="headId != null and headId != ''">
		and head_id = #{headId}
	</if>
    <if test="billListId != null and billListId != ''">
		and bill_list_id = #{billListId}
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultType="com.dcjet.cs.returned.model.GwReturnList" parameterType="com.dcjet.cs.returned.model.GwReturnList">
        SELECT
        <include refid="Base_Column_List" />, COALESCE(price, 0) * COALESCE(qty, 0) as totalPrice
        FROM
        t_gw_return_list t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.insert_time desc, t.linked_no, t.line_no, t.sid
    </select>

    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_gw_return_list t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="selectByHeadId" parameterType="java.util.List" resultType="com.dcjet.cs.returned.model.GwReturnList">
        select * from t_gw_return_list
        where head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>

    <delete id="deleteByHeadId" parameterType="java.util.List">
        delete from t_gw_return_list  where head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="selectMatchBill" resultType="com.dcjet.cs.dto.returned.GwReturnListMatchDto">
        SELECT x.entry_no, x.list_no, x.dec_list_id, x.d_date, x.unit, x.destination_country, x.curr, x.fac_g_no,
			   x.sum_qty, COALESCE(e.match_qty, 0) as match_qty, x.trade_mode, x.dec_price,
			   CASE WHEN e.fac_g_no IS NULL OR e.fac_g_no = '' THEN 2 ELSE 1 END sx, COALESCE(f.qty, 0) as qty,
               x.sum_qty-COALESCE(e.match_qty, 0) as kpp_qty, x.sum_qty-COALESCE(e.match_qty, 0) as sy_qty,
               COALESCE(f.qty, 0) as lastQty, sys_guid() as sid
		FROM (
			SELECT g.entry_no, c.list_no, to_date(g.d_date, 'yyyy-MM-dd') AS d_date, b.unit, b.dec_price, a.trade_mode,
			       b.destination_country, b.curr, b.fac_g_no, COALESCE(b.qty, 0) AS sum_qty, b.sid as dec_list_id
            <choose>
                <when test='iEMark == "I"'>
                    FROM t_dec_erp_e_head_n a
                    INNER JOIN t_dec_erp_e_list_n b     ON b.head_id = a.sid
                    INNER JOIN t_dec_e_entry_head g     ON g.erp_head_id = a.sid
                    INNER JOIN t_dec_e_bill_head c      ON c.head_id = a.sid
                    WHERE a.trade_mode = '0615'
                </when >
                <when test='iEMark == "E"'>
                    FROM t_dec_erp_i_head_n a
                    INNER JOIN t_dec_erp_i_list_n b     ON b.head_id = a.sid
                    INNER JOIN t_dec_i_entry_head g     ON g.erp_head_id = a.sid
                    INNER JOIN t_dec_i_bill_head c      ON c.head_id = a.sid
                    WHERE a.trade_mode = '4600'
                </when >
                <otherwise>
                </otherwise>
            </choose>
			AND b.fac_g_no = #{facGNo} AND a.trade_code = #{tradeCode} AND a.bond_mark = '0'
		) x
        LEFT JOIN (
            SELECT SUM(COALESCE(qty, 0)) AS match_qty, dec_list_id, fac_g_no
            FROM t_gw_return_list_match
            WHERE fac_g_no = #{facGNo} AND trade_code = #{tradeCode}
            GROUP BY dec_list_id, fac_g_no
        ) e ON e.dec_list_id = x.dec_list_id
        LEFT JOIN t_gw_return_list_match f on f.dec_list_id = x.dec_list_id and f.list_id = #{sid}
        WHERE x.sum_qty > COALESCE(e.match_qty, 0) OR ( x.sum_qty = COALESCE(e.match_qty, 0) AND f.qty IS NOT NULL )
        ORDER BY sx, x.d_date
    </select>
    <select id="selectNonMatchBill" resultType="com.dcjet.cs.dto.returned.GwReturnListMatchDto"
            parameterType="com.dcjet.cs.dto.returned.GwReturnListParam">
        SELECT x.entry_no, x.list_no, x.d_date, x.unit, x.destination_country, x.curr, x.fac_g_no,
               x.sum_qty, COALESCE(e.match_qty, 0) as match_qty, x.trade_mode, x.dec_price,
               CASE WHEN e.fac_g_no IS NULL OR e.fac_g_no = '' THEN 2 ELSE 1 END sx, COALESCE(f.qty, 0) as qty,
               x.sum_qty-COALESCE(e.match_qty, 0) as kpp_qty, x.sum_qty-COALESCE(e.match_qty, 0) as sy_qty,
               COALESCE(f.qty, 0) as lastQty, sys_guid() as sid
        FROM (
            SELECT a.entry_no, a.list_no, a.entry_declare_date AS d_date, b.unit, b.dec_price, a.trade_mode,
                   b.destination_country, b.curr, d.fac_g_no, SUM(COALESCE(b.qty, 0)) AS sum_qty
            <choose>
                <when test='iEMark == "I"'>
                    FROM t_dec_e_bill_head a
                    INNER JOIN t_dec_e_bill_list b     ON a.sid = b.head_id
                    INNER JOIN t_dec_erp_e_relation c  ON b.sid = c.bill_list_sid
                    INNER JOIN t_dec_erp_e_list_n d    ON c.list_sid = d.sid
                    INNER JOIN t_dec_e_entry_head g    ON g.sid = c.entry_sid
                </when >
                <when test='iEMark == "E"'>
                    FROM t_dec_i_bill_head a
                    INNER JOIN t_dec_i_bill_list b     ON a.sid = b.head_id
                    INNER JOIN t_dec_erp_i_relation c  ON b.sid = c.bill_list_sid
                    INNER JOIN t_dec_erp_i_list_n d    ON c.list_sid = d.sid
                    INNER JOIN t_dec_i_entry_head g    ON g.sid = c.entry_sid
                </when >
                <otherwise>
                </otherwise>
            </choose>
            WHERE g.ENTRY_STATUS in ('R', '1', 'P', 'B', 'K')
            <choose>
                <when test='bondMark == "0"'>
                    AND a.trade_mode = '0615' AND a.bond_mark = '0'
                    <if test="emsNo != null and emsNo != ''">
                        AND a.ems_no = #{emsNo}
                    </if>
                </when >
                <when test='bondMark == "1"'>
                    AND g.trade_mode = '0110' AND g.bond_mark = '1'
                </when >
                <when test='bondMark == "2"'>
                    AND ((g.trade_mode = '0110' AND g.bond_mark = '1')
                    or (a.trade_mode = '0615' AND a.bond_mark = '0'
                        <if test="emsNo != null and emsNo != ''">
                            AND a.ems_no = #{emsNo}
                        </if>))
                </when>
                <otherwise>
                </otherwise>
            </choose>
            AND d.fac_g_no = #{facGNo} AND a.trade_code = #{tradeCode}
            <if test="dDateFrom != null and dDateFrom != ''">
                <![CDATA[ AND a.entry_declare_date >= to_date(#{dDateFrom}, 'yyyy-MM-dd')]]>
            </if>
            <if test="dDateTo != null and dDateTo != ''">
                <![CDATA[ AND a.entry_declare_date < to_date(#{dDateTo}, 'yyyy-MM-dd') + 1]]>
            </if>
            GROUP BY a.entry_no, a.sid, a.entry_declare_date, b.unit, a.trade_mode, b.destination_country,
                     b.curr, d.fac_g_no, b.dec_price
        ) x
        LEFT JOIN (
            SELECT SUM(COALESCE(qty, 0)) AS match_qty, entry_no, bill_head_id, d_date, unit, destination_country,
             curr, fac_g_no
            FROM t_gw_return_list_match
            WHERE fac_g_no = #{facGNo} AND trade_code = #{tradeCode}
            GROUP BY entry_no, bill_head_id, d_date, unit, destination_country, curr,fac_g_no
        ) e ON e.entry_no = x.entry_no AND e.bill_head_id = x.sid AND x.fac_g_no = e.fac_g_no AND to_char(e.d_date,
        'yyyy-MM-dd') = to_char(x.d_date, 'yyyy-MM-dd')
        AND e.unit = x.unit AND x.curr = e.curr AND e.destination_country = x.destination_country
        LEFT JOIN t_gw_return_list_match f on f.entry_no = x.entry_no and f.bill_head_id = x.sid
            and x.fac_g_no = f.fac_g_no
            AND to_char(f.d_date, 'yyyy-MM-dd') = to_char(x.d_date, 'yyyy-MM-dd') AND f.unit = x.unit AND x.curr = f.curr
            AND f.destination_country = x.destination_country and f.list_id = #{sid}
        WHERE x.sum_qty > COALESCE(e.match_qty, 0) OR ( x.sum_qty = COALESCE(e.match_qty, 0) AND f.qty IS NOT NULL )
        ORDER BY sx, x.d_date
    </select>
    <select id="selectRepairMatchBill" resultType="com.dcjet.cs.dto.returned.GwReturnListMatchDto">
        SELECT x.entry_no, x.dec_list_id, x.d_date, x.unit, x.destination_country, x.curr, x.fac_g_no,
               x.sum_qty, COALESCE(e.match_qty, 0) as match_qty, x.trade_mode, x.dec_price,
               CASE WHEN e.fac_g_no IS NULL OR e.fac_g_no = '' THEN 2 ELSE 1 END sx, COALESCE(f.qty, 0) as qty,
               x.sum_qty-COALESCE(e.match_qty, 0) as kpp_qty, x.sum_qty-COALESCE(e.match_qty, 0) as sy_qty,
               COALESCE(f.qty, 0) as lastQty, sys_guid() as sid
        FROM (
            SELECT g.entry_no, to_date(g.d_date, 'yyyy-MM-dd') AS d_date, b.unit, b.dec_price, a.trade_mode,
                   b.destination_country, b.curr, b.fac_g_no, COALESCE(b.qty, 0) AS sum_qty, b.sid as dec_list_id
            <choose>
                <when test='iEMark == "I"'>
                    FROM t_dec_erp_e_head_n a
                    INNER JOIN t_dec_erp_e_list_n b     ON b.head_id = a.sid
                    INNER JOIN t_dec_e_entry_head g     ON g.erp_head_id = a.sid
                    WHERE a.trade_mode in ('0615', '0110')
                </when >
                <when test='iEMark == "E"'>
                    FROM t_dec_erp_i_head_n a
                    INNER JOIN t_dec_erp_i_list_n b     ON b.head_id = a.sid
                    INNER JOIN t_dec_i_entry_head g     ON g.erp_head_id = a.sid
                    WHERE a.trade_mode = '1300'
                </when >
                <otherwise>
                </otherwise>
            </choose>
            AND g.ENTRY_STATUS in ('R', '1', 'P', 'B', 'K')
            AND b.fac_g_no = #{facGNo} AND a.trade_code = #{tradeCode}
        ) x
        LEFT JOIN (
            SELECT SUM(COALESCE(qty, 0)) AS match_qty, dec_list_id, fac_g_no
            FROM t_gw_return_list_match
            WHERE fac_g_no = #{facGNo} AND trade_code = #{tradeCode}
            GROUP BY dec_list_id, fac_g_no
        ) e ON e.dec_list_id = x.dec_list_id
        LEFT JOIN t_gw_return_list_match f on f.dec_list_id = x.dec_list_id and f.list_id = #{sid}
        WHERE x.sum_qty > COALESCE(e.match_qty, 0) OR ( x.sum_qty = COALESCE(e.match_qty, 0) AND f.qty IS NOT NULL )
        ORDER BY sx, x.d_date
    </select>
    <select id="getEmsNo" resultType="java.lang.String">
        SELECT DISTINCT EMS_NO FROM T_MAT_IMGEXG_ORG_PASS
        WHERE EMS_NO is not null and TRADE_CODE = #{tradeCode}
        ORDER BY EMS_NO
    </select>

    <select id="selectNonMatch" resultType="com.dcjet.cs.dto.returned.GwReturnListMatchDto"
            parameterType="com.dcjet.cs.dto.returned.GwReturnListParam">
        SELECT x.entry_no, x.dec_list_id, x.d_date, x.unit, x.destination_country, x.curr, x.fac_g_no,
            x.sum_qty, COALESCE(e.match_qty, 0) as match_qty, x.trade_mode, x.dec_price, x.list_no,
            CASE WHEN e.fac_g_no IS NULL OR e.fac_g_no = '' THEN 2 ELSE 1 END sx, COALESCE(f.qty, 0) as qty,
            x.sum_qty-COALESCE(e.match_qty, 0) as kpp_qty, x.sum_qty-COALESCE(e.match_qty, 0) as sy_qty,
            COALESCE(f.qty, 0) as lastQty, sys_guid() as sid
        FROM (
            SELECT g.entry_no, to_date(g.d_date, 'yyyy-MM-dd') as d_date, b.unit, b.dec_price, a.trade_mode, h.list_no,
                   b.destination_country, b.curr, b.fac_g_no, COALESCE(b.qty, 0) AS sum_qty, b.sid as dec_list_id
            <choose>
                <when test='iEMark == "I"'>
                    FROM t_dec_erp_e_head_n a
                    INNER JOIN t_dec_erp_e_list_n b     ON b.head_id = a.sid
                    INNER JOIN t_dec_e_entry_head g     ON g.erp_head_id = a.sid
                    INNER JOIN t_dec_e_bill_head h      ON h.head_id = a.sid
                </when >
                <when test='iEMark == "E"'>
                    FROM t_dec_erp_i_head_n a
                    INNER JOIN t_dec_erp_i_list_n b     ON b.head_id = a.sid
                    INNER JOIN t_dec_i_entry_head g     ON g.erp_head_id = a.sid
                    INNER JOIN t_dec_i_bill_head h      ON h.head_id = a.sid
                </when >
                <otherwise>
                </otherwise>
            </choose>
            WHERE g.ENTRY_STATUS in ('R', '1', 'P', 'B', 'K')
            <choose>
                <when test='bondMark == "0"'>
                    AND g.trade_mode = '0615' AND g.bond_mark = '0'
                    <if test="emsNo != null and emsNo != ''">
                        AND g.ems_no = #{emsNo}
                    </if>
                </when>
                <when test='bondMark == "1"'>
                    AND g.trade_mode = '0110' AND g.bond_mark = '1'
                </when>
                <when test='bondMark == "2" or bondMark == null or bondMark == ""'>
                    AND ((g.trade_mode = '0110' AND g.bond_mark = '1')
                    or (g.trade_mode = '0615' AND g.bond_mark = '0'
                    <if test="emsNo != null and emsNo != ''">
                        AND g.ems_no = #{emsNo}
                    </if>))
                </when>
                <otherwise>
                </otherwise>
            </choose>
            AND b.fac_g_no = #{facGNo} AND a.trade_code = #{tradeCode}
            <if test='_databaseId != "postgresql" '>
                <if test="dDateFrom != null and dDateFrom != ''">
                    <![CDATA[ AND g.d_date >= SUBSTR(#{dDateFrom}, 0, 10) ]]>
                </if>
                <if test="dDateTo != null and dDateTo != ''">
                    <![CDATA[ AND g.d_date <= SUBSTR(#{dDateTo}, 0, 10) ]]>
                </if>
            </if>
            <if test='_databaseId == "postgresql" '>
                <if test="dDateFrom != null and dDateFrom != ''">
                    <![CDATA[ AND g.d_date >= SUBSTR(#{dDateFrom}, 1, 10) ]]>
                </if>
                <if test="dDateTo != null and dDateTo != ''">
                    <![CDATA[ AND g.d_date <= SUBSTR(#{dDateTo}, 1, 10) ]]>
                </if>
            </if>
        ) x
        LEFT JOIN (
            SELECT SUM(COALESCE(qty, 0)) AS match_qty, dec_list_id, fac_g_no
            FROM t_gw_return_list_match
            WHERE fac_g_no = #{facGNo} AND trade_code = #{tradeCode}
            GROUP BY dec_list_id, fac_g_no
        ) e ON e.dec_list_id = x.dec_list_id

        LEFT JOIN t_gw_return_list_match f ON f.dec_list_id = x.dec_list_id and f.list_id = #{sid}
        WHERE x.sum_qty > COALESCE(e.match_qty, 0) OR ( x.sum_qty = COALESCE(e.match_qty, 0) AND f.qty IS NOT NULL )
        ORDER BY sx, x.d_date
    </select>

    <select id="selectImgShippingMatch" resultType="com.dcjet.cs.dto.returned.GwReturnListMatchDto"
            parameterType="com.dcjet.cs.dto.returned.GwReturnListParam">
        SELECT x.entry_no, x.dec_list_id, x.d_date, x.unit, x.origin_country, x.curr, x.fac_g_no,
            x.sum_qty, COALESCE(e.match_qty, 0) as match_qty, x.trade_mode, x.dec_price, x.list_no,
            CASE WHEN f.fac_g_no IS NULL OR f.fac_g_no = '' THEN 2 ELSE 1 END sx,
            x.sum_qty - COALESCE(e.match_qty, 0) + COALESCE(f.qty, 0) as kpp_qty,
            x.sum_qty - COALESCE(e.match_qty, 0) as sy_qty,
            COALESCE(f.qty, 0) as qty,
            COALESCE(f.qty, 0) as lastQty, sys_guid() as sid, x.ems_list_no, x.g_name, x.invoice_no,
            x.document_no
        FROM (
            SELECT
                a.invoice_no, b.sid as dec_list_id, b.fac_g_no, d.document_no,
                g.entry_no, to_date(g.d_date, 'yyyy-MM-dd') as d_date, g.trade_mode,
                h.list_no, h.ems_list_no, b.unit, b.dec_price, b.g_name, b.origin_country, b.curr,
                COALESCE(b.qty, 0) AS sum_qty
            FROM t_dec_erp_i_head_n a
            INNER JOIN t_dec_erp_i_list_n b     ON b.head_id = a.sid
            INNER JOIN t_dec_i_entry_head g     ON g.erp_head_id = a.sid
            INNER JOIN t_dec_i_bill_head h      ON h.head_id = a.sid and h.sid = b.bill_head_id
            left JOIN T_CERTIFICATE_DEDUCT c    ON c.dec_erp_list_id = b.sid and c.valid_mark != '0'
            left JOIN T_CERTIFICATE d           ON d.sid = c.certificate_id
            WHERE g.ENTRY_STATUS = 'R' AND g.bond_mark = '0' AND b.g_mark = 'I'
            AND b.fac_g_no = #{facGNo} AND a.trade_code = #{tradeCode}
            AND h.send_api_status in ('J1', '4', '5')
            <if test="tradeMode != null and tradeMode != ''">
                AND g.trade_mode = #{tradeMode}
            </if>
            <if test="emsNo != null and emsNo != ''">
                AND g.ems_no = #{emsNo}
            </if>
            <if test="GNo != null and GNo != ''">
                AND b.g_no = #{GNo}
            </if>
            <if test="gName != null and gName != ''">
                AND b.g_name like concat('%', concat(#{gName}, '%'))
            </if>
            <if test='_databaseId != "postgresql" '>
                <if test="dDateFrom != null and dDateFrom != ''">
                    <![CDATA[ AND g.d_date >= SUBSTR(#{dDateFrom}, 0, 10) ]]>
                </if>
                <if test="dDateTo != null and dDateTo != ''">
                    <![CDATA[ AND g.d_date <= SUBSTR(#{dDateTo}, 0, 10) ]]>
                </if>
            </if>
            <if test='_databaseId == "postgresql" '>
                <if test="dDateFrom != null and dDateFrom != ''">
                    <![CDATA[ AND g.d_date >= SUBSTR(#{dDateFrom}, 1, 10) ]]>
                </if>
                <if test="dDateTo != null and dDateTo != ''">
                    <![CDATA[ AND g.d_date <= SUBSTR(#{dDateTo}, 1, 10) ]]>
                </if>
            </if>
        ) x
        LEFT JOIN (
            SELECT SUM(COALESCE(qty, 0)) AS match_qty, dec_list_id, fac_g_no
            FROM t_gw_return_list_match
            WHERE fac_g_no = #{facGNo} AND trade_code = #{tradeCode}
            GROUP BY dec_list_id, fac_g_no
        ) e ON e.dec_list_id = x.dec_list_id

        LEFT JOIN t_gw_return_list_match f ON f.dec_list_id = x.dec_list_id and f.list_id = #{sid}
        WHERE x.sum_qty > COALESCE(e.match_qty, 0) OR ( x.sum_qty = COALESCE(e.match_qty, 0) AND f.qty IS NOT NULL )
        ORDER BY sx, x.d_date
    </select>
    <select id="selectImgAutoMatch" resultType="com.dcjet.cs.dto.returned.GwReturnListMatchDto">
        SELECT x.entry_no, x.dec_list_id, x.d_date, x.unit, x.origin_country, x.curr, x.fac_g_no,
            x.sum_qty, COALESCE(e.match_qty, 0) as match_qty, x.trade_mode, x.dec_price, x.list_no,
            x.sum_qty - COALESCE(e.match_qty, 0) as kpp_qty,
            sys_guid() as sid, x.ems_list_no, x.g_name, x.invoice_no, x.document_no
        FROM (
            SELECT
                a.invoice_no, b.sid as dec_list_id, b.fac_g_no, d.document_no,
                g.entry_no, to_date(g.d_date, 'yyyy-MM-dd') as d_date, g.trade_mode,
                h.list_no, h.ems_list_no, b.unit, b.dec_price, b.g_name, b.origin_country, b.curr,
                COALESCE(b.qty, 0) AS sum_qty
            FROM t_dec_erp_i_head_n a
            INNER JOIN t_dec_erp_i_list_n b     ON b.head_id = a.sid
            INNER JOIN t_dec_i_entry_head g     ON g.erp_head_id = a.sid
            INNER JOIN t_dec_i_bill_head h      ON h.head_id = a.sid and h.sid = b.bill_head_id
            left JOIN T_CERTIFICATE_DEDUCT c    ON c.dec_erp_list_id = b.sid and c.valid_mark != '0'
            left JOIN T_CERTIFICATE d           ON d.sid = c.certificate_id
            WHERE g.ENTRY_STATUS = 'R' AND g.bond_mark = '0' AND b.g_mark = 'I'
            AND b.fac_g_no = #{facGNo} AND a.trade_code = #{tradeCode}
            AND h.send_api_status in ('J1', '4', '5')
            AND g.trade_mode = #{tradeMode} AND g.ems_no = #{emsNo}

        ) x
        LEFT JOIN (
            SELECT SUM(COALESCE(qty, 0)) AS match_qty, dec_list_id, fac_g_no
            FROM t_gw_return_list_match
            WHERE fac_g_no = #{facGNo} AND trade_code = #{tradeCode}
            GROUP BY dec_list_id, fac_g_no
        ) e ON e.dec_list_id = x.dec_list_id

        WHERE x.sum_qty > COALESCE(e.match_qty, 0)
        ORDER BY x.d_date desc
    </select>
</mapper>
