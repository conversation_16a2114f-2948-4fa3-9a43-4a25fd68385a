package com.dcjet.cs.returned.mapper;

import com.dcjet.cs.dto.returned.GwReturnHeadDto;
import com.dcjet.cs.dto.returned.GwReturnHeadParam;
import com.dcjet.cs.returned.model.GwReturnHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-2-5
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GwReturnHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    GwReturnHeadDto toDto(GwReturnHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    GwReturnHead toPo(GwReturnHeadParam param);
    /**
     * 数据库原始数据更新
     * @param gwReturnHeadParam
     * @param gwReturnHead
     */
    void updatePo(GwReturnHeadParam gwReturnHeadParam, @MappingTarget GwReturnHead gwReturnHead);
    default void patchPo(GwReturnHeadParam gwReturnHeadParam, GwReturnHead gwReturnHead) {
        // TODO 自行实现局部更新
    }
}
