package com.dcjet.cs.returned.service.imgshipping;

import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.dto.returned.GwReturnListDto;
import com.dcjet.cs.dto.returned.GwReturnListMatchDto;
import com.dcjet.cs.dto.returned.GwReturnListParam;
import com.dcjet.cs.returned.dao.GwReturnHeadMapper;
import com.dcjet.cs.returned.dao.GwReturnListMapper;
import com.dcjet.cs.returned.dao.GwReturnListMatchMapper;
import com.dcjet.cs.returned.mapper.GwReturnListDtoMapper;
import com.dcjet.cs.returned.model.GwReturnHead;
import com.dcjet.cs.returned.model.GwReturnList;
import com.dcjet.cs.returned.model.GwReturnListMatch;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: WJ
 * @createDate: 2021/11/1 15:53
 */
@Service
public class ReturnListImgService {

    @Resource
    private GwReturnListMapper gwReturnListMapper;
    @Resource
    private GwReturnListDtoMapper gwReturnListDtoMapper;
    @Resource
    private GwReturnHeadMapper gwReturnHeadMapper;
    @Resource
    private GwReturnListMatchMapper gwReturnListMatchMapper;
    @Resource
    private BulkInsertConfig bulkInsertConfig;
    public IBulkInsert getBulkInsertInstance() throws Exception{
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }
    @Resource
    private BulkSqlOpt bulkSqlOpt;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param gwReturnListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<GwReturnListDto>> getListPaged(GwReturnListParam gwReturnListParam, PageParam pageParam) {
        // 启用分页查询
        GwReturnList gwReturnList = gwReturnListDtoMapper.toPo(gwReturnListParam);
        Page<GwReturnList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwReturnListMapper.getList(gwReturnList));
        List<GwReturnListDto> gwReturnListDtos = page.getResult().stream().map(head -> {
            GwReturnListDto dto = gwReturnListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<GwReturnListDto>> paged = ResultObject.createInstance(gwReturnListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param gwReturnListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwReturnListDto insert(GwReturnListParam gwReturnListParam, UserInfoToken userInfo) {
        GwReturnList gwReturnList = gwReturnListDtoMapper.toPo(gwReturnListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        gwReturnList.setSid(sid);
        gwReturnList.setTradeCode(userInfo.getCompany());
        gwReturnList.setInsertUserName(userInfo.getUserName());
        gwReturnList.setInsertUser(userInfo.getUserNo());
        gwReturnList.setInsertTime(new Date());
        gwReturnList.setStatus(ConstantsStatus.STATUS_0);
        gwReturnList.setDataSource(ConstantsStatus.STATUS_0);

        // 更新表头状态
        updateHeadStatus(gwReturnList.getHeadId(), ConstantsStatus.STATUS_0);
        // 新增数据
        int insertStatus = gwReturnListMapper.insert(gwReturnList);
        return  insertStatus > 0 ? gwReturnListDtoMapper.toDto(gwReturnList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param gwReturnListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GwReturnListDto update(GwReturnListParam gwReturnListParam, UserInfoToken userInfo) {
        GwReturnList gwReturnList = gwReturnListMapper.selectByPrimaryKey(gwReturnListParam.getSid());

        // 判断表头状态
        checkHeadStatus(gwReturnList.getHeadId(), xdoi18n.XdoI18nUtil.t("已生成报关不允许修改"));

        // 更新表头状态
        updateHeadStatus(gwReturnList.getHeadId(), ConstantsStatus.STATUS_0);
        // 删除对应match信息
        gwReturnListMatchMapper.deleteByListId(gwReturnListParam.getSid());

        gwReturnListDtoMapper.updatePo(gwReturnListParam, gwReturnList);
        gwReturnList.setUpdateUser(userInfo.getUserNo());
        gwReturnList.setUpdateTime(new Date());
        gwReturnList.setStatus(ConstantsStatus.STATUS_0);
        // 更新数据
        int update = gwReturnListMapper.updateByPrimaryKey(gwReturnList);
        return update > 0 ? gwReturnListDtoMapper.toDto(gwReturnList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> sids) {
        Example example = new Example(GwReturnList.class);
        example.createCriteria().andIn("sid", sids);
        List<GwReturnList> gwReturnLists = gwReturnListMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(gwReturnLists)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("勾选的数据不存在"));
        }

        // 表头sid
        String sid = gwReturnLists.get(0).getHeadId();
        // 判断表头状态
        checkHeadStatus(sid, xdoi18n.XdoI18nUtil.t("已生成报关不允许删除"));

        // 删除表体
        gwReturnListMapper.deleteBySids(sids);
        // 删除已匹配表体
        gwReturnListMatchMapper.deleteByListIds(sids);
        // 更新表头状态
        GwReturnList gwReturnList = new GwReturnList(){{
            setHeadId(sid);
        }};
        gwReturnLists.clear();
        gwReturnLists = gwReturnListMapper.select(gwReturnList);
        if (CollectionUtils.isEmpty(gwReturnLists) ||
                CollectionUtils.isNotEmpty(gwReturnLists.stream().filter(x -> ConstantsStatus.STATUS_0.equals(x.getStatus())).collect(Collectors.toList()))) {
            updateHeadStatus(sid, ConstantsStatus.STATUS_0);
        } else {
            updateHeadStatus(sid, ConstantsStatus.STATUS_1);
        }
    }

    /**
     * 更新表头状态
     * @param sid
     */
    private void updateHeadStatus(String sid, String status) {
        GwReturnHead head = new GwReturnHead(){{
            setStatus(status);
            setSid(sid);
        }};
        gwReturnHeadMapper.updateByPrimaryKeySelective(head);
    }

    /**
     * 判断表头状态
     * @param sid
     * @param msg
     */
    private void checkHeadStatus(String sid, String msg) {
        GwReturnHead head = gwReturnHeadMapper.selectByPrimaryKey(sid);
        if (null != head && ConstantsStatus.STATUS_2.equals(head.getStatus())) {
            throw new ErrorException(400, msg);
        }
    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<GwReturnListDto> selectAll(GwReturnListParam exportParam, UserInfoToken userInfo) {
        GwReturnList gwReturnList = gwReturnListDtoMapper.toPo(exportParam);
        gwReturnList.setTradeCode(userInfo.getCompany());
        List<GwReturnListDto> gwReturnListDtos = new ArrayList<>();
        List<GwReturnList> gwReturnLists = gwReturnListMapper.getList(gwReturnList);
        if (CollectionUtils.isNotEmpty(gwReturnLists)) {
            gwReturnListDtos = gwReturnLists.stream().map(head -> {
                GwReturnListDto dto = gwReturnListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return gwReturnListDtos;
    }

    /**
     * 匹配报关数据
     * @param gwReturnListParam
     * @param userInfo
     * @return
     */
    public ResultObject matchCustoms(GwReturnListParam gwReturnListParam, PageParam pageParam, UserInfoToken userInfo) {
        gwReturnListParam.setTradeCode(userInfo.getCompany());
        Page<GwReturnListMatchDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> gwReturnListMapper.selectImgShippingMatch(gwReturnListParam));
        return ResultObject.createInstance(page.getResult(), (int) page.getTotal(), page.getPageNum());
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject saveMatchCustoms(String sid, List<GwReturnListMatchDto> matchDtoList, UserInfoToken userInfo) {
        if (CollectionUtils.isEmpty(matchDtoList)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("请勾选要匹配的数据"));
        }
        // 可匹配数量+上次匹配数量-本次匹配数量=剩余数量
        List<GwReturnListMatchDto> minusList =
                matchDtoList.stream().filter(x -> x.getKppQty().add(x.getLastQty()).subtract(x.getQty()).compareTo(BigDecimal.ZERO) == -1).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(minusList)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("剩余数量为负值"));
        }

        GwReturnList gwReturnList = gwReturnListMapper.selectByPrimaryKey(sid);
        BigDecimal qty = matchDtoList.stream().map(GwReturnListMatchDto::getQty).reduce(BigDecimal.ZERO,
                BigDecimal::add);

        // 比较匹配数量、待退运数量
        if (gwReturnList.getQty().compareTo(qty) == -1) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("匹配数量大于待退运数量"));
        }
        if (gwReturnList.getQty().compareTo(qty) == 0) {
            gwReturnList.setStatus(ConstantsStatus.STATUS_1);
        } else {
            gwReturnList.setStatus(ConstantsStatus.STATUS_0);
        }
        gwReturnListMapper.updateByPrimaryKey(gwReturnList);

        Date date = new Date();
        matchDtoList.stream().forEach(x -> {
            x.setSid(UUID.randomUUID().toString());
            x.setHeadId(gwReturnList.getHeadId());
            x.setListId(sid);
            x.setTradeCode(userInfo.getCompany());
            x.setInsertTime(date);
            x.setInsertUser(userInfo.getUserNo());
            x.setInsertUserName(userInfo.getUserName());
        });
        // 匹配表先删后插
        gwReturnListMatchMapper.deleteByListId(sid);
        bulkSqlOpt.batchInsert(matchDtoList.stream().filter(
                e -> null != e.getQty() && e.getQty().compareTo(BigDecimal.ZERO) == 1).collect(Collectors.toList()), GwReturnListMatchMapper.class);

        // 判断当前表头下表体状态是否都为已匹配
        GwReturnList list = new GwReturnList(){{
            setHeadId(gwReturnList.getHeadId());
            setStatus(ConstantsStatus.STATUS_0);
        }};
        GwReturnHead head = new GwReturnHead(){{
            setSid(gwReturnList.getHeadId());
        }};
        if (CollectionUtils.isEmpty(gwReturnListMapper.select(list))) {
            // 更新表头状态为已匹配完成
            head.setStatus(ConstantsStatus.STATUS_1);
        } else {
            // 更新表头状态为暂存
            head.setStatus(ConstantsStatus.STATUS_0);
        }
        gwReturnHeadMapper.updateByPrimaryKeySelective(head);
        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("保存成功"));
    }

    /**
     * 自动匹配
     * @param headId
     * @param userInfo
     * @return
     */
    public ResultObject autoMatch(String headId, UserInfoToken userInfo) {
        List<String> sids = new ArrayList<String>(){{
            add(headId);
        }};
        // 删除已匹配数据
        gwReturnListMatchMapper.deleteByHeadIds(sids);

        // 待匹配表体
        List<GwReturnList> list = gwReturnListMapper.selectByHeadId(sids);

        // 表头
        GwReturnHead head = gwReturnHeadMapper.selectByPrimaryKey(headId);
        // 默认匹配完成
        boolean matchFinish = true;
        // 表头备案号、监管方式
        String emsNo = head.getEmsNo();
        String tradeMode = null;
        if (CommonVariable.TRADE_MODE_0265.equals(head.getTradeMode())) {
            tradeMode = CommonVariable.TRADE_MODE_0214;
        }
        if (CommonVariable.TRADE_MODE_0664.equals(head.getTradeMode())) {
            tradeMode = CommonVariable.TRADE_MODE_0615;
        }
        // 开始匹配
        for (GwReturnList returnList : list) {
            returnList.setEmsNo(emsNo);
            returnList.setTradeMode(tradeMode);
            // 当前退运表体对应的匹配到的数据
            List<GwReturnListMatchDto> matchDtos = gwReturnListMapper.selectImgAutoMatch(returnList);
            if (CollectionUtils.isEmpty(matchDtos)) {
                matchFinish = false;
                returnList.setStatus(ConstantsStatus.STATUS_0);
            } else {
                // 待匹配数量
                BigDecimal qty = returnList.getQty();
                // 匹配到的数据
                List<GwReturnListMatch> matchList = new ArrayList<>();
                // 已匹配数量
                BigDecimal matchedQty = BigDecimal.ZERO;
                for (GwReturnListMatchDto dto : matchDtos) {
                    // 待匹配数量 > 可匹配数量 + 已匹配数量
                    if (qty.compareTo(dto.getKppQty().add(matchedQty)) == 1) {
                        matchedQty = matchedQty.add(dto.getKppQty());
                        dto.setQty(dto.getKppQty());
                        bulidMatchDto(dto, userInfo, head.getSid(), returnList.getSid());
                        matchList.add(gwReturnListDtoMapper.toModel(dto));
                        // 表体状态置为未匹配
                        returnList.setStatus(ConstantsStatus.STATUS_0);
                    } else if (qty.compareTo(dto.getKppQty().add(matchedQty)) == 0) {
                        // 待匹配数量 = 可匹配数量 + 已匹配数量
                        matchedQty = matchedQty.add(dto.getKppQty());
                        dto.setQty(dto.getKppQty());
                        bulidMatchDto(dto, userInfo, head.getSid(), returnList.getSid());
                        matchList.add(gwReturnListDtoMapper.toModel(dto));
                        // 表体状态置为已匹配
                        returnList.setStatus(ConstantsStatus.STATUS_1);
                        break;
                    } else {
                        // 待匹配数量<可匹配数量+已匹配数量
                        // 凑足待匹配数量
                        dto.setQty(qty.subtract(matchedQty));
                        bulidMatchDto(dto, userInfo, head.getSid(), returnList.getSid());
                        matchList.add(gwReturnListDtoMapper.toModel(dto));
                        // 表体状态置为已匹配
                        returnList.setStatus(ConstantsStatus.STATUS_1);
                        break;
                    }
                }
                // 匹配数据遍历结束时状态为未匹配则置为false
                if (ConstantsStatus.STATUS_0.equals(returnList.getStatus())) {
                    matchFinish = false;
                }
                // 插入匹配明细
                bulkSqlOpt.batchInsert(matchList, GwReturnListMatchMapper.class);
            }
        }


        // 更新表体状态
        bulkSqlOpt.batchUpdate(list, GwReturnListMapper.class);
        // 更新表头状态
        head.setStatus(matchFinish ? ConstantsStatus.STATUS_1 : ConstantsStatus.STATUS_0);
        gwReturnHeadMapper.updateByPrimaryKey(head);

        return ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("匹配成功"));
    }

    private void bulidMatchDto(GwReturnListMatchDto dto, UserInfoToken userInfo, String sid, String listSid) {
        dto.setSid(UUID.randomUUID().toString());
        dto.setHeadId(sid);
        dto.setListId(listSid);
        dto.setTradeCode(userInfo.getCompany());
        dto.setInsertTime(new Date());
        dto.setInsertUser(userInfo.getUserNo());
        dto.setInsertUserName(userInfo.getUserName());
    }

}
