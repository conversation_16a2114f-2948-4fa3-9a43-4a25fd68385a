package com.dcjet.cs.returned.service.imgshipping;

import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.returned.GwReturnListMatchDto;
import com.dcjet.cs.dto.returned.GwReturnListMatchParam;
import com.dcjet.cs.returned.dao.GwReturnListMatchMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: WJ
 * @createDate: 2021/11/1 17:13
 */
@Service
public class ReturnMatchImgService {

    @Resource
    private GwReturnListMatchMapper matchMapper;
    @Resource
    private CommonService commonService;


    public ResultObject<List<GwReturnListMatchDto>> getMatchList(GwReturnListMatchParam param, PageParam pageParam) {

        Page<GwReturnListMatchDto> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> matchMapper.getMatchList(param));
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            converCode(page.getResult());
        }

        return ResultObject.createInstance(page.getResult(), (int) page.getTotal(), page.getPageNum());
    }




    private void converCode(List<GwReturnListMatchDto> list) {
        for (GwReturnListMatchDto item : list) {
            item.setUnit(commonService.convertPCode(item.getUnit(), PCodeType.UNIT));
            item.setUnit1(commonService.convertPCode(item.getUnit1(), PCodeType.UNIT));
            item.setUnit2(commonService.convertPCode(item.getUnit2(), PCodeType.UNIT));
            item.setCurr(commonService.convertPCode(item.getCurr(), PCodeType.CURR_OUTDATED));
            item.setOriginCountry(commonService.convertPCode(item.getOriginCountry(), PCodeType.COUNTRY_OUTDATED));
        }
    }

    public List<GwReturnListMatchDto> selectAll(GwReturnListMatchParam param, UserInfoToken userInfo) {
        param.setTradeCode(userInfo.getCompany());
        List<GwReturnListMatchDto> list = matchMapper.getMatchList(param);
        if (CollectionUtils.isNotEmpty(list)) {
            converCode(list);
        }
        return list;
    }
}
