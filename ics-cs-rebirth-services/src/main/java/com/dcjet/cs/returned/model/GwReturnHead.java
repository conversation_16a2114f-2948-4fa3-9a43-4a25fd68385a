package com.dcjet.cs.returned.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-2-5
 */
@Setter
@Getter
@Table(name = "t_gw_return_head")
public class GwReturnHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 唯一键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 单据内部编号(最终的提单内部编号)
     */
	@Column(name = "ems_list_no")
	private  String emsListNo;
	/**
     * 手账册号
     */
	@Column(name = "ems_no")
	private  String emsNo;
	/**
     * 进出口标志
     */
	@Column(name = "i_e_mark")
	@JsonProperty("ieMark")
	private  String ieMark;
	/**
     * 启运港
     */
	@Column(name = "desp_port")
	private  String despPort;
	/**
     * 目的港
     */
	@Column(name = "dest_port")
	private  String destPort;
	/**
     * 报关单申报单位(生成到提单时需注意)
     */
	@Column(name = "agent_code")
	private  String agentCode;
	/**
     * 状态（0.暂存 1.已匹配完成 2.已生成报关）
     */
	@Column(name = "status")
	private  String status;
	/**
     * 监管方式
     */
	@Column(name = "trade_mode")
	private  String tradeMode;
	/**
     * 备注1
     */
	@Column(name = "note_1")
	private  String note1;
	/**
     * 备注2
     */
	@Column(name = "note_2")
	private  String note2;
	/**
     * 提单表头模板ID
     */
	@Column(name = "template_head_id")
	private  String templateHeadId;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 制单人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 制单时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 制单人姓名
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 更新人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 更新人姓名
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 退运类型（1.进料成品退换 2.非保成品退运 3.修理物品 4.非保成品退运(退运货物) 5.料件复出）
     */
	@Column(name = "type")
	private  String type;
	/**
     * 退运表体涉及的报关单号列表
     */
	@Column(name = "entry_no")
	private  String entryNo;
	/**
     * 担保类型(担保方式)
     */
	@Column(name = "assure_type")
	private  String assureType;
	/**
     * 担保金额
     */
	@Column(name = "assure_amount")
	private  BigDecimal assureAmount;
	/**
     * 成品料件标志(I.料件 E.成品)
     */
	@Column(name = "g_mark")
	private String GMark;
	/**
     * 出口发货单号
     */
	@Column(name = "bill_no")
	private  String billNo;
//	/**
//     * 报关单申报单位
//     */
//	@Column(name = "declare_code")
//	private  String declareCode;
	/**
     * 报关单申报单位
     */
	@Column(name = "agent_name")
	private  String agentName;
	/**
     * 提单表头sid
     */
	@Column(name = "dec_head_id")
	private  String decHeadId;

	/**
	 * 制单日期-开始
	 */
	@Transient
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private String insertTimeFrom;
	/**
	 * 制单日期-结束
	 */
	@Transient
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private String insertTimeTo;
}
