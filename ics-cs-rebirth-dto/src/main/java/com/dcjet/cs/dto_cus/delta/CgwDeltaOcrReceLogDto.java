package com.dcjet.cs.dto_cus.delta;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2021-12-30
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class CgwDeltaOcrReceLogDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * kafka返回的json数据
      */
    @ApiModelProperty("kafka返回的json数据")
	private  String receiveData;
	/**
      * 业务类型(BILL.提单)
      */
    @ApiModelProperty("业务类型(BILL.提单)")
	private  String bussinessType;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建人名称
      */
    @ApiModelProperty("创建人名称")
	private  String insertUserName;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 修改人
      */
    @ApiModelProperty("修改人")
	private  String updateUser;
	/**
      * 修改人名称
      */
    @ApiModelProperty("修改人名称")
	private  String updateUserName;
	/**
      * 修改时间
      */
    @ApiModelProperty("修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
}
