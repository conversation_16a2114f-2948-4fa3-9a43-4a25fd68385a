package com.dcjet.cs.dto_cus.delta;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2021-11-25
 */
@ApiModel(value = "CgwDeltaOcrBillDto")
@Setter @Getter
public class CgwDeltaOcrBillDto implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final Map<String, String> compareResultMap = new HashMap(){{
		put("0", "0 未比对"); put("1", "1 一致"); put("2", "2 不一致");
	}};

	public static final Map<String, String> statusMap = new HashMap(){{
		put("1", "1 提单已识别"); put("2", "2 托书已获取"); put("3", "3 托书获取失败"); put("4", "4 比对成功"); put("5", "5 比对失败");
	}};

	public static final Map<String, String> mailSendFlagMap = new HashMap(){{
		put("0", "0 未通知"); put("1", "1 已通知");
	}};

	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * EN号码
      */
    @ApiModelProperty("EN号码")
	private  String enNo;
	/**
      * SHIP TO
      */
    @ApiModelProperty("SHIP TO")
	private  String shipTo;
	/**
      * 收货人
      */
    @ApiModelProperty("收货人")
	private  String consignee;
	/**
      * 通知人
      */
    @ApiModelProperty("通知人")
	private  String notifyParty;
	/**
      * 启运港
      */
    @ApiModelProperty("启运港")
	private  String despPort;
	/**
      * 标记唛码
      */
    @ApiModelProperty("标记唛码")
	private  String markNo;
	/**
      * 总毛重
      */
    @ApiModelProperty("总毛重")
	private  BigDecimal grossWt;
	/**
      * 总体积
      */
    @ApiModelProperty("总体积")
	private  BigDecimal volume;
	/**
      * 外包装数量
      */
    @ApiModelProperty("外包装数量")
	private  String outerPackaging;
	/**
      * 主要品名
      */
    @ApiModelProperty("主要品名")
	@JsonProperty("gName")
	private  String gName;
	/**
      * 接收记录表头表中的SID
      */
    @ApiModelProperty("接收记录表头表中的SID")
	private  String headId;
	/**
      * 数据状态(1.提单已识别 2.托书已获取 3.托书获取失败 4.比对成功 5.比对失败)
      */
    @ApiModelProperty("数据状态(1.提单已识别 2.托书已获取 3.托书获取失败 4.比对成功 5.比对失败)")
	private  String status;
	/**
      * 比对完成时间
      */
    @ApiModelProperty("比对完成时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date compareTime;
	/**
      * 比对结果(0.未比对 1.一致 2.不一致)
      */
    @ApiModelProperty("比对结果(0.未比对 1.一致 2.不一致)")
	private  String compareResult;
	/**
      * 托书获取时间
      */
    @ApiModelProperty("托书获取时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date bookingGetTime;
	/**
      * 邮件通知状态(0.未通知 1.已通知)
      */
    @ApiModelProperty("邮件通知状态(0.未通知 1.已通知)")
	private  String mailSendFlag;
	/**
      * 邮件发送时间
      */
    @ApiModelProperty("邮件发送时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date mailSendTime;
	/**
      * 提单识别时间
      */
    @ApiModelProperty("提单识别时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date scanTime;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建人名称
      */
    @ApiModelProperty("创建人名称")
	private  String insertUserName;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 修改人
      */
    @ApiModelProperty("修改人")
	private  String updateUser;
	/**
      * 修改人名称
      */
    @ApiModelProperty("修改人名称")
	private  String updateUserName;
	/**
      * 修改时间
      */
    @ApiModelProperty("修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
}
