package com.dcjet.cs.dto_cus.daikin;

import com.xdo.domain.ExcelExportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date: 2021-9-15
 */
@Getter
@Setter
@ApiModel(description = "订单管理报表导出-传入参数")
public class OrderReportExportParam extends ExcelExportParam {
    /**
     * 订单管理报表查询参数
     */
    @ApiModelProperty("订单管理报表查询参数")
    private OrderListParam exportColumns;
}
