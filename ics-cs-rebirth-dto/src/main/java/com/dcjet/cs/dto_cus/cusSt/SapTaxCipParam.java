package com.dcjet.cs.dto_cus.cusSt;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2020-7-22
 */
@Setter @Getter
@ApiModel(value = "客户对账单接口正式表传入参数")
public class SapTaxCipParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 日期
     */
	@ApiModelProperty("日期")
	@JsonProperty("mDate")
	private  Date mDate;
	/**
    * 日期-开始
    */
	@ApiModelProperty("日期-开始")
	private String mDateFrom;
	/**
    * 日期-结束
    */
	@ApiModelProperty("日期-结束")
    private String mDateTo;
	/**
     * 发运单号
     */
	@XdoSize(max = 30, message = "{发运单号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发运单号")
	private  String billNo;
	/**
     * 客户代码
     */
	@XdoSize(max = 30, message = "{客户代码长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户代码")
	private  String custId;
	/**
     * 客户采购订单
     */
	@XdoSize(max = 35, message = "{客户采购订单长度不能超过35位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户采购订单")
	private  String po;
	/**
     * 客户料号
     */
	@XdoSize(max = 35, message = "{客户物料长度不能超过35位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户物料")
	@JsonProperty("cCopGNo")
	private  String cCopGNo;
	/**
     * 客户项号
     */
	@Digits(integer = 22, fraction = 0, message = "{客户项号必须为数字,整数位最大22位,小数最大0位!}")
	@ApiModelProperty("客户项号")
	@JsonProperty("cGNo")
	private  Integer cGNo;
	/**
     * 物料描述
     */
	@XdoSize(max = 200, message = "{物料描述长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("物料描述")
	@JsonProperty("gMark")
	private  String gMark;
	/**
     * 数量（片）
     */
	@Digits(integer = 13, fraction = 5, message = "{数量必须为数字,整数位最大13位,小数最大5位!}")
	@ApiModelProperty("数量")
	private  BigDecimal qty1;
	/**
     * 单位
     */
	@XdoSize(max = 30, message = "{单位长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("单位")
	private  String unit;
	/**
     * 净重
     */
	@Digits(integer = 13, fraction = 5, message = "{净重必须为数字,整数位最大13位,小数最大5位!}")
	@ApiModelProperty("净重")
	private  BigDecimal net;
	/**
     * 毛重
     */
	@Digits(integer = 13, fraction = 5, message = "{毛重必须为数字,整数位最大13位,小数最大5位!}")
	@ApiModelProperty("毛重")
	private  BigDecimal gross;
	/**
     * 金额
     */
	@Digits(integer = 13, fraction = 5, message = "{金额必须为数字,整数位最大13位,小数最大5位!}")
	@ApiModelProperty("金额")
	private  BigDecimal decTotal;
	/**
     * 币别
     */
	@XdoSize(max = 30, message = "{币别长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("币别")
	private  String decCurr;
	/**
     * 企业物料代码
     */
	@XdoSize(max = 30, message = "{物料代码长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("物料代码")
	private  String sapCopGNo;
	/**
     * 海关物料代码
     */
	@XdoSize(max = 30, message = "{海关物料代码长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("海关物料代码")
	private  String copGNo;
	/**
     * 数量（折合平方米）
     */
	@Digits(integer = 13, fraction = 5, message = "{数量（折合平方米）必须为数字,整数位最大13位,小数最大5位!}")
	@ApiModelProperty("数量（折合平方米）")
	private  BigDecimal qty2;
	/**
     * BOM版本号
     */
	@XdoSize(max = 30, message = "{BOM版本号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("BOM版本号")
	private  String versionNo;
	/**
     * 企业代码
     */
	@XdoSize(max = 50, message = "{企业代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
     * MQ同步数据批次号
     */
	@XdoSize(max = 50, message = "{MQ同步数据批次号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("MQ同步数据批次号")
	private  String syncBatchNo;
	/**
     * 传输批次号
     */
	@XdoSize(max = 255, message = "{传输批次号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("传输批次号")
	private  String tempOwner;
	/**
     * 数据标识 0 正常 1 错误
     */
	@Digits(integer = 4, fraction = 0, message = "{数据标识 0 正常 1 错误必须为数字,整数位最大4位,小数最大0位!}")
	@ApiModelProperty("数据标识 0 正常 1 错误")
	private  Integer tempFlag;
	/**
     * 错误说明
     */
	@XdoSize(max = 1000, message = "{错误说明长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("错误说明")
	private  String tempRemark;
}
