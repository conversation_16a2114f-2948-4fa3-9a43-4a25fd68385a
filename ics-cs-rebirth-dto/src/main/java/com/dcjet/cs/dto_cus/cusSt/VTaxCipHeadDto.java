package com.dcjet.cs.dto_cus.cusSt;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 * @date: 2020-7-27
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class VTaxCipHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String clientType;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String clientCode;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String shortname;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tel;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String fax;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String nameCh;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String nameEn;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String addressCh;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String addressEn;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tradeCode;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String emsNo;
	/**
	 *
	 */
	@ApiModelProperty("")
	private  String CLIENT_TYPE;
	/**
	 *
	 */
	@ApiModelProperty("")
	private  String CLIENT_CODE;
	/**
	 *
	 */
	@ApiModelProperty("")
	private  String NAME_CH;
	/**
	 *
	 */
	@ApiModelProperty("")
	private  String NAME_EN;
	/**
	 *
	 */
	@ApiModelProperty("")
	private  String ADDRESS_CH;
	/**
	 *
	 */
	@ApiModelProperty("")
	private  String ADDRESS_EN;
	/**
	 *
	 */
	@ApiModelProperty("")
	private  String TRADE_CODE;
	/**
	 *
	 */
	@ApiModelProperty("")
	private  String EMS_NO;
}
