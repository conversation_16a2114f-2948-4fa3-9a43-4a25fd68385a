package com.dcjet.cs.dto_cus.panasonic;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description:
 * @author: WJ
 * @createDate: 2020/7/10 15:11
 */
@Setter
@Getter
public class DecInvoiceShipmentListDto {

    /**
     * 表体备注栏位斜杠前数值(例子:“9/基板内制”,取9)
     */
    @ApiModelProperty("NO")
    private String no;

    /**
     * 英文品名
     */
    @ApiModelProperty("description")
    private String copGNameEn;

    /**
     * 企业料号
     */
    @ApiModelProperty("料号")
    private String facGNo;

    /**
     * 发票号码
     */
    @ApiModelProperty("INV NO.")
    private String invoiceNo;

    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String trafMode;

    /**
     * 采购订单号
     */
    @ApiModelProperty("PO NO.")
    private String orderNo;

    /**
     * 备注斜杠后数值(例子:“9/基板内制”,取基板内制)
     */
    @ApiModelProperty("机种")
    private String model;

    /**
     * 保税为“免”,非保税为 “课”
     */
    @ApiModelProperty("课/免")
    private String bondMark;

    /**
     * 保税取”项号”,非保税取”HSCODE”
     */
    @ApiModelProperty("序号")
    private String serialNo;

    /**
     * 商品名称
     */
    @ApiModelProperty("中文名称")
    private String GName;

    /**
     * 申报规格型号
     */
    @ApiModelProperty("TYPE.")
    private String GModel;

    /**
     * 申报单价
     */
    @ApiModelProperty("Unit Price(JPY)")
    private BigDecimal decPrice;

    /**
     * 申报数量
     */
    @ApiModelProperty("QTY")
    private BigDecimal qty;

    /**
     * 计量单位
     */
    @ApiModelProperty("UNIT")
    private String unit;

    /**
     * 申报总价
     */
    @ApiModelProperty("Total Price(JPY)")
    private BigDecimal decTotal;

    /**
     * 净重
     */
    @ApiModelProperty("Net Weight(KG)")
    private BigDecimal netWt;

    /**
     * 不取值,固定为空
     */
    @ApiModelProperty("CASE_NO")
    private String caseNo;

    /**
     * 原产国
     */
    @ApiModelProperty("Original")
    private String originCountry;

    /**
     * 企业料件信息单个净重
     */
    @ApiModelProperty("单重(KG)")
    private BigDecimal singleWt;;

    /**
    * 企业料件监管条件&备注 (监管条件有值取值,无取备注信息)
    * */
    @ApiModelProperty("A/L")
    private String regulatoryConditions;

    /**
     * 法一单位
     */
    @ApiModelProperty("法定第一单位")
    private String unit1;

    /**
     * 法一数量
     */
    @ApiModelProperty("法定第一数量")
    private BigDecimal qty1;

    /**
     * 表头特殊关系确认(勾选”是”，未勾选”否”)
     */
    @ApiModelProperty("特殊关系确认")
    private String confirmSpecial;

    /**
     * 表头价格影响确认(勾选”是”，未勾选”否”)
     */
    @ApiModelProperty("价格影响确认")
    private String confirmPrice;

    /**
    * 表头支付特许权使用确认 (勾选”是”，未勾选”否”)
    */
    @ApiModelProperty("支付特许权使用确认")
    private String confirmRoyalties;

    /**
    * 表头监管方式
    */
    @ApiModelProperty("监管方式")
    private String tradeMode;

}
