package com.dcjet.cs.dto_cus.technimark;

import com.xdo.domain.ExcelExportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date: 2021-11-18
 */
@Getter
@Setter
@ApiModel(description = "泰马克出口单证统计报表导出传入参数")
public class TechnimarkDzReportExportParam extends ExcelExportParam {
    /**
     * 导出传入参数
     */
    @ApiModelProperty("查询参数")
    private TechnimarkDzReportParam exportColumns;
}
