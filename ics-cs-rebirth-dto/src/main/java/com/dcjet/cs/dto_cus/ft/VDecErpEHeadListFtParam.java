package com.dcjet.cs.dto_cus.ft;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter
@Getter
@ApiModel(value = "出口报表(丰田)表头表体传入参数")
public class VDecErpEHeadListFtParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 单据内部编号
     */
    @XdoSize(max = 50, message = "{单据内部编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("单据内部编号")
    private String erpEmsListNo;
    /**
     * 企业代码
     */
    @XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 经营企业名称
     */
    @XdoSize(max = 70, message = "{经营企业名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("经营企业名称")
    private String tradeName;
    /**
     * 经营企业社会信用代码
     */
    @XdoSize(max = 18, message = "{经营企业社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("经营企业社会信用代码")
    private String tradeCreditCode;
    /**
     * 申报单位编码
     */
    @XdoSize(max = 10, message = "{申报单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位编码")
    private String declareCode;
    /**
     * 申报单位名称
     */
    @XdoSize(max = 70, message = "{申报单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位名称")
    private String declareName;
    /**
     * 申报单位社会信用代码
     */
    @XdoSize(max = 18, message = "{申报单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位社会信用代码")
    private String declareCreditCode;
    /**
     * 录入单位编号
     */
    @XdoSize(max = 10, message = "{录入单位编号长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("录入单位编号")
    private String inputCode;
    /**
     * 录入单位名称
     */
    @XdoSize(max = 70, message = "{录入单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("录入单位名称")
    private String inputName;
    /**
     * 录入单位社会信用代码
     */
    @XdoSize(max = 18, message = "{录入单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("录入单位社会信用代码")
    private String inputCreditCode;
    /**
     * 供应商编码
     */
    @XdoSize(max = 20, message = "{供应商编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @XdoSize(max = 100, message = "{供应商名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 货代编码
     */
    @XdoSize(max = 50, message = "{货代编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货代编码")
    private String forwardCode;
    /**
     * 货代名称
     */
    @XdoSize(max = 100, message = "{货代名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货代名称")
    private String forwardName;
    /**
     * 出境关别
     */
    @XdoSize(max = 4, message = "{出境关别长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("出境关别")
    private String IEPort;
    /**
     * 申报地海关
     */
    @XdoSize(max = 4, message = "{主管海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报地海关")
    private String masterCustoms;
    /**
     * 监管方式
     */
    @XdoSize(max = 6, message = "{监管方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("监管方式")
    private String tradeMode;
    /**
     * 运输方式
     */
    @XdoSize(max = 6, message = "{运输方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 料件成品标记
     */
    @XdoSize(max = 4, message = "{料件成品标记长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("料件成品标记")
    private String GMark;
    /**
     * 贸易国别
     */
    @XdoSize(max = 6, message = "{贸易国别长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("贸易国别")
    private String tradeNation;
    /**
     * 归并类型0-自动归并 1-人工归并
     */
    @XdoSize(max = 1, message = "{归并类型0-自动归并 1-人工归并长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("归并类型0-自动归并 1-人工归并")
    private String mergeType;
    /**
     * 备注
     */
    @XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String note;
    /**
     * 录入日期
     */
    @ApiModelProperty("录入日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inputDate;
    /**
     * 录入日期-开始
     */
    @ApiModelProperty("录入日期-开始")
    private String inputDateFrom;
    /**
     * 录入日期-结束
     */
    @ApiModelProperty("录入日期-结束")
    private String inputDateTo;
    /**
     * 成交方式
     */
    @XdoSize(max = 6, message = "{成交方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("成交方式")
    private String transMode;
    /**
     * 运抵国
     */
    @XdoSize(max = 3, message = "{运抵国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运抵国")
    private String tradeCountry;
    /**
     * 启运港
     */
    @XdoSize(max = 6, message = "{启运港长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("启运港")
    private String despPort;
    /**
     * 价格说明
     */
    @XdoSize(max = 20, message = "{价格说明长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("价格说明")
    private String promiseItems;
    /**
     * 有效标志 0 有效，1 无效
     */
    @XdoSize(max = 1, message = "{有效标志 0 有效，1 无效长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("有效标志 0 有效，1 无效")
    private String validMark;
    /**
     * 备案号
     */
    @XdoSize(max = 20, message = "{备案号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备案号")
    private String emsNo;
    /**
     * 主提运单
     */
    @XdoSize(max = 50, message = "{主提运单长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("主提运单")
    private String mawb;
    /**
     * 提运单号
     */
    @XdoSize(max = 50, message = "{提运单长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("提运单")
    private String hawb;
    /**
     * 合同协议号
     */
    @XdoSize(max = 50, message = "{合同协议号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("合同协议号")
    private String contrNo;
    /**
     * 许可证号
     */
    @XdoSize(max = 50, message = "{许可证号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("许可证号")
    private String licenseNo;
    /**
     * 发票号码
     */
    @XdoSize(max = 100, message = "{发票号码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发票号码")
    private String invoiceNo;
    /**
     * 件数
     */
    @Digits(integer = 9, fraction = 0, message = "{件数必须为数字,整数位最大9位,小数最大0位!}")
    @ApiModelProperty("件数")
    private BigDecimal packNum;
    /**
     * 体积
     */
    @Digits(integer = 11, fraction = 5, message = "{体积必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("体积")
    private BigDecimal volume;
    /**
     * 总净重
     */
    @Digits(integer = 11, fraction = 5, message = "{总净重必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("总净重")
    private BigDecimal netWt;
    /**
     * 指运港
     */
    @XdoSize(max = 40, message = "{指运港长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("指运港")
    private String destPort;
    /**
     * 包装种类
     */
    @XdoSize(max = 50, message = "{包装种类长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("包装种类")
    private String wrapType;
    /**
     * 运输工具名称
     */
    @XdoSize(max = 100, message = "{运输工具名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运输工具名称")
    private String trafName;
    /**
     * 航次号
     */
    @XdoSize(max = 100, message = "{航次号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("航次号")
    private String voyageNo;
    /**
     * 杂费币制
     */
    @XdoSize(max = 3, message = "{杂费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("杂费币制")
    private String otherCurr;
    /**
     * 杂费类型
     */
    @XdoSize(max = 1, message = "{杂费类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("杂费类型")
    private String otherMark;
    /**
     * 杂费
     */
    @Digits(integer = 11, fraction = 5, message = "{杂费必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("杂费")
    private BigDecimal otherRate;
    /**
     * 运费币制
     */
    @XdoSize(max = 3, message = "{运费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运费币制")
    private String feeCurr;
    /**
     * 运费类型
     */
    @XdoSize(max = 1, message = "{运费类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运费类型")
    private String feeMark;
    /**
     * 运费
     */
    @Digits(integer = 11, fraction = 5, message = "{运费必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("运费")
    private BigDecimal feeRate;
    /**
     * 保费币制
     */
    @XdoSize(max = 3, message = "{保费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("保费币制")
    private String insurCurr;
    /**
     * 保费类型
     */
    @XdoSize(max = 1, message = "{保费类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("保费类型")
    private String insurMark;
    /**
     * 保费
     */
    @Digits(integer = 11, fraction = 5, message = "{保费必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("保费")
    private BigDecimal insurRate;
    /**
     * 总毛重
     */
    @Digits(integer = 11, fraction = 5, message = "{总毛重必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("总毛重")
    private BigDecimal grossWt;
    /**
     * 出口日期
     */
    @ApiModelProperty("出口日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date iedate;
    /**
     * 出口日期-开始
     */
    @ApiModelProperty("出口日期-开始")
    private String iEDateFrom;
    /**
     * 出口日期-结束
     */
    @ApiModelProperty("出口日期-结束")
    private String iEDateTo;
    /**
     * 境内货源地
     */
    @XdoSize(max = 5, message = "{境内货源地长度不能超过5位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内货源地")
    private String districtCode;
    /**
     * 境内发货人编码
     */
    @XdoSize(max = 20, message = "{境内发货人编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内发货人编码")
    private String receiveCode;
    /**
     * 境内发货人名称
     */
    @XdoSize(max = 255, message = "{境内发货人名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内发货人名称")
    private String receiveName;
    /**
     * 航班日期
     */
    @ApiModelProperty("航班日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date voyageDate;
    /**
     * 航班日期-开始
     */
    @ApiModelProperty("航班日期-开始")
    private String voyageDateFrom;
    /**
     * 航班日期-结束
     */
    @ApiModelProperty("航班日期-结束")
    private String voyageDateTo;
    /**
     * 出货日期
     */
    @ApiModelProperty("出货日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date shipDate;
    /**
     * 出货日期-开始
     */
    @ApiModelProperty("出货日期-开始")
    private String shipDateFrom;
    /**
     * 出货日期-结束
     */
    @ApiModelProperty("出货日期-结束")
    private String shipDateTo;
    /**
     * 境外发货人编码
     */
    @XdoSize(max = 255, message = "{境外发货人编码长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外发货人编码")
    private String overseasShipper;
    /**
     * 境外发货人名称
     */
    @XdoSize(max = 255, message = "{境外发货人名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外发货人名称")
    private String overseasShipperName;
    /**
     * 征免性质
     */
    @XdoSize(max = 3, message = "{征免性质长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("征免性质")
    private String cutMode;
    /**
     * 货物存放地点
     */
    @XdoSize(max = 255, message = "{货物存放地点长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货物存放地点")
    private String warehouse;
    /**
     * 最终目的国
     */
    @XdoSize(max = 3, message = "{最终目的国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("最终目的国")
    private String destinationCountry;
    /**
     * 入境口岸/出境口岸
     */
    @XdoSize(max = 6, message = "{入境口岸/出境口岸长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("入境口岸/出境口岸")
    private String entryPort;

    /**
     * 创建时间-开始
     */
    @ApiModelProperty("创建时间-开始")
    private String insertTimeFrom;
    /**
     * 创建时间-结束
     */
    @ApiModelProperty("创建时间-结束")
    private String insertTimeTo;
    /**
     * 发送时间-开始
     */
    @ApiModelProperty("发送时间-开始")
    private String declareDateFrom;
    /**
     * 发送时间-结束
     */
    @ApiModelProperty("发送时间-结束")
    private String declareDateTo;
    /**
     * 发送人
     */
    @XdoSize(max = 50, message = "{发送人长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发送人")
    private String sendUser;


    /**
     * 发送人
     */
    @ApiModelProperty("发送人")
    private String sendUserName;
    /**
     * 内审员
     */
    @ApiModelProperty("内审员")
    private String apprUserName;

    /**
     * 审核时间-开始
     */
    @ApiModelProperty("审核时间-开始")
    private String apprDateFrom;
    /**
     * 审核时间-结束
     */
    @ApiModelProperty("审核时间-结束")
    private String apprDateTo;
    /**
     * 内审员
     */
    @XdoSize(max = 50, message = "{内审员长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("内审员")
    private String apprUser;
    /**
     * 审核状态
     */
    @XdoSize(max = 3, message = "{审核状态长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审核状态")
    private String apprStatus;

    /**
     * 随附单据是否上传
     */
    @ApiModelProperty("随附单据是否上传 0 无 1 有")
    private String attach;

    /**
     * 保完税标识 0 保税 1 非保税
     */
    @ApiModelProperty("保完税标识 0 保税 1 非保税")
    private String bondMark;

    /**
     * 数据来源 0大提单 1 小提单
     */
    @ApiModelProperty("数据来源 0大提单 1 小提单")
    private String dataSource;

    /**
     * 报关标志
     */
    @ApiModelProperty("报关标志")
    private String dclcusMark;
    /**
     * 报关类型
     */
    @ApiModelProperty("报关类型")
    private String dclcusType;
    /**
     * 报关单类型
     */
    @ApiModelProperty("报关单类型")
    private String entryType;
    /**
     * 申请表编号/申报表编号
     */
    @ApiModelProperty("申请表编号/申报表编号")
    private String applyNo;
    /**
     * 关联核注清单编号
     */
    @ApiModelProperty("关联核注清单编号")
    private String relListNo;
    /**
     * 关联账册（手册）编号
     */
    @ApiModelProperty("关联账册（手册）编号")
    private String relEmsNo;
    /**
     * 境内目的地(行政区域)
     */
    @ApiModelProperty("境内目的地(行政区域)")
    private String districtPostCode;
    /**
     * shipTo代码
     */
    @ApiModelProperty("shipTo代码")
    private String shipTo;
    /**
     * 申报单位编码
     */
    @XdoSize(max = 50, message = "{申报单位编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位编码(基础信息)")
    private String declareCodeCustoms;
    /**
     * 申报单位名称
     */
    @XdoSize(max = 100, message = "{申报单位名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位名称(基础信息)")
    private String declareNameCustoms;

    /**
     * 表头SID
     */
    @XdoSize(max = 40, message = "{表头SID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("表头SID")
    private String headId;
    /**
     * 流水号
     */
    @Digits(integer = 11, fraction = 0, message = "{流水号必须为数字,整数位最大11位,小数最大0位!}")
    @ApiModelProperty("流水号")
    private BigDecimal serialNo;
    /**
     * 备案序号
     */
    @Digits(integer = 11, fraction = 0, message = "{备案序号必须为数字,整数位最大11位,小数最大0位!}")
    @ApiModelProperty("备案序号")
    private BigDecimal GNo;
    /**
     * 备案料号
     */
    @XdoSize(max = 100, message = "{备案料号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @NotBlank(message = "{备案料号不能为空}")
    @ApiModelProperty("备案料号")
    private String copGNo;
    /**
     * 商品编码
     */
    @XdoSize(max = 10, message = "{商品编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @NotBlank(message = "{商品编码不能为空}")
    @ApiModelProperty("商品编码")
    private String codeTS;
    /**
     * 商品名称
     */
    @XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @NotBlank(message = "{商品名称不能为空}")
    @ApiModelProperty("商品名称")
    private String GName;
    /**
     * 申报规格型号
     */
    @XdoSize(max = 255, message = "{申报规格型号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @NotBlank(message = "{商品规格型号不能为空}")
    @ApiModelProperty("申报规格型号")
    private String GModel;
    /**
     * 计量单位
     */
    @XdoSize(max = 6, message = "{计量单位长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @NotBlank(message = "{计量单位不能为空}")
    @ApiModelProperty("计量单位")
    private String unit;
    /**
     * 法一单位
     */
    @XdoSize(max = 3, message = "{法一单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("法一单位")
    private String unit1;
    /**
     * 法二单位
     */
    @XdoSize(max = 3, message = "{法二单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("法二单位")
    private String unit2;
    /**
     * 原产国
     */
    @XdoSize(max = 3, message = "{原产国长度不能超过3位字节长度(一个汉字2位字节长度)!}")

    @ApiModelProperty("原产国")
    private String originCountry;
    /**
     * 申报单价
     */
    @Digits(integer = 12, fraction = 5, message = "{申报单价必须为数字,整数位最大12位,小数最大5位!}")
    @NotNull(message = "{申报单价不能为空}")
    @ApiModelProperty("申报单价")
    private BigDecimal decPrice;
    /**
     * 申报总价
     */
    @Digits(integer = 12, fraction = 5, message = "{申报总价必须为数字,整数位最大12位,小数最大5位!}")
    @NotNull(message = "{申报总价不能为空}")
    @ApiModelProperty("申报总价")
    private BigDecimal decTotal;
    /**
     * 美元统计总金额
     */
    @Digits(integer = 11, fraction = 5, message = "{美元统计总金额必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("美元统计总金额")
    private BigDecimal usdPrice;
    /**
     * 币制
     */
    @XdoSize(max = 3, message = "{币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @NotBlank(message = "{币制不能为空}")
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 法一数量
     */
    @Digits(integer = 11, fraction = 5, message = "{法一数量必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("法一数量")
    private BigDecimal qty1;
    /**
     * 法二数量
     */
    @Digits(integer = 11, fraction = 5, message = "{法二数量必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("法二数量")
    private BigDecimal qty2;
    /**
     * 重量比例因子
     */
    @Digits(integer = 11, fraction = 5, message = "{重量比例因子必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("重量比例因子")
    private BigDecimal factorWt;
    /**
     * 第一比例因子
     */
    @Digits(integer = 11, fraction = 5, message = "{第一比例因子必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("第一比例因子")
    private BigDecimal factor1;
    /**
     * 第二比例因子
     */
    @Digits(integer = 11, fraction = 5, message = "{第二比例因子必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("第二比例因子")
    private BigDecimal factor2;
    /**
     * 申报数量
     */
    @Digits(integer = 11, fraction = 5, message = "{申报数量必须为数字,整数位最大11位,小数最大5位!}")
    @NotNull(message = "{申报数量不能为空}")
    @ApiModelProperty("申报数量")
    private BigDecimal qty;
    /**
     * 用途
     */
    @XdoSize(max = 4, message = "{用途长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("用途")
    private String useType;
    /**
     * 征免方式
     */
    @XdoSize(max = 6, message = "{征免方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("征免方式")
    private String dutyMode;
    /**
     * 单耗版本号
     */
    @XdoSize(max = 8, message = "{单耗版本号长度不能超过8位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("单耗版本号")
    private String exgVersion;
    /**
     * 归并序号
     */
    @Digits(integer = 11, fraction = 0, message = "{归并序号必须为数字,整数位最大11位,小数最大0位!}")
    @ApiModelProperty("归并序号")
    private BigDecimal entryGNo;
    /**
     * 账册企业内部编号
     */
    @XdoSize(max = 30, message = "{账册企业内部编号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("账册企业内部编号")
    private String copEmsNo;
    /**
     * 归类标记
     */
    @XdoSize(max = 4, message = "{归类标记长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("归类标记")
    private String classMark;
    /**
     * 入库时间
     */
    @ApiModelProperty("入库时间")
    private Date arrivalDate;
    /**
     * 入库时间-开始
     */
    @ApiModelProperty("入库时间-开始")
    private String arrivalDateFrom;
    /**
     * 入库时间-结束
     */
    @ApiModelProperty("入库时间-结束")
    private String arrivalDateTo;
    /**
     * 中文名称
     */
    @XdoSize(max = 255, message = "{中文名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("中文名称")
    private String copGName;
    /**
     * 中文规格型号
     */
    @XdoSize(max = 255, message = "{中文规格型号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("中文规格型号")
    private String copGModel;
    /**
     * 订单号码
     */
    @XdoSize(max = 30, message = "{订单号码长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("订单号码")
    private String orderNo;
    /**
     * 企业料号
     */
    @XdoSize(max = 100, message = "{企业料号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @NotNull(message = "{企业料号不能为空}")
    @ApiModelProperty("企业料号")
    private String facGNo;
    /**
     * 关联单号
     */
    @XdoSize(max = 50, message = "{关联单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("关联单号")
    private String linkedNo;

    /**
     * 企业订单号
     */
    @XdoSize(max = 50, message = "{企业订单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业订单号")
    private String customerOrderNo;

    /**
     * 企业料号
     */
    @XdoSize(max = 50, message = "{企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业料号")
    private String customerGNo;
    /**
     * 警告标志 0：非警告  1：警告
     */
    @ApiModelProperty("警告标志 0：非警告  1：警告")
    private String warningMark;

    /**
     * 备注1
     */
    @XdoSize(max = 250, message = "{Remark1长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注1")
    private String note1;
    /**
     * 备注2
     */
    @XdoSize(max = 250, message = "{Remark2长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注2")
    private String note2;
    /**
     * 备注3
     */
    @XdoSize(max = 250, message = "{Remark3长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注3")
    private String note3;

    /**
     * 报关单号
     */
    @ApiModelProperty("报关单号")
    private String entryNo;
    /**
     * 报关单申报日期
     */
    @ApiModelProperty("报关单申报日期")
    private Date entryDeclareDate;

    /**
     * 报关单申报日期-开始
     */
    @ApiModelProperty("报关单申报日期-开始")
    private String entryDeclareDateFrom;
    /**
     * 报关单申报日期-结束
     */
    @ApiModelProperty("报关单申报日期-结束")
    private String entryDeclareDateTo;


    /**
     * 表体物料类型
     */
    @XdoSize(max = 2, message = "{表体物料类型长度不能超过2位字节长度!}")
    @ApiModelProperty("表体物料类型")
    private String listGMark;
    /**
     * 表体保完税标记  0 保税 1 非保税
     */
    @ApiModelProperty("表体保完税标记  0 保税 1 非保税")
    private String listBondMark;
    /**
     * 表体单据内部编号
     */
    @XdoSize(max = 50, message = "{表体单据内部编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("表体单据内部编号")
    private String listEmsListNo;

    /**
     * 清单申报日期-开始
     */
    @ApiModelProperty("清单申报日期-开始")
    private String billDeclareDateFrom;
    /**
     * 清单申报日期-结束
     */
    @ApiModelProperty("清单申报日期-结束")
    private String billDeclareDateTo;
    /**
     * 提单制单员
     */
    @ApiModelProperty("提单制单员")
    private String insertUser;


    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private String listInvoiceNo;
    /**
     * 表体监管方式(贸易方式)
     */
    @ApiModelProperty("表体监管方式(贸易方式)")
    private String listTradeMode;
    /**
     * 表体备案号
     */
    @ApiModelProperty("表体备案号")
    private String listEmsNo;


    /**
     * 到港日期
     */
    @ApiModelProperty("到港日期")
    private Date arrivalPortDate;
    /**
     * 到港日期-开始
     */
    @ApiModelProperty("到港日期-开始")
    private String arrivalPortDateFrom;
    /**
     * 到港日期-结束
     */
    @ApiModelProperty("到港日期-结束")
    private String arrivalPortDateTo;

    /**
     * 报关单统一编号
     */
    @ApiModelProperty("报关单统一编号")
    private String seqNo;
    /**
     * 成本中心
     */
    @XdoSize(max = 50, message = "{成本中心长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("成本中心")
    private String costCenter;
    /**
     * 关联单序号
     */
    @ApiModelProperty("关联单序号")
    private String lineNo;

    /**
     * 模拟项号
     */
    @ApiModelProperty("模拟项号")
    private String itemNo;

    /**
     * 递送日期
     */
    @NotNull(message = "{递送日期不能为空！}")
    @ApiModelProperty("递送日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDate;
    /**
     * 递送日期开始
     */
    @ApiModelProperty("递送日期-开始")
    private String deliveryDateFrom;
    /**
     * 递送日期-结束
     */
    @ApiModelProperty("递送日期-结束")
    private String deliveryDateTo;
    /**
     * 财务单据号
     */
    @ApiModelProperty("财务单据号")
    private String financeNo;


    /**
     * 总数量
     */
    @ApiModelProperty("总数量")
    private BigDecimal qtyAll;
    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal totalAll;
    /**
     * 体积
     */
    @ApiModelProperty("体积")
    private BigDecimal volumeTotal;
    /**
     * shipto名称
     */
    @ApiModelProperty("shipto名称")
    private String shipToName;

    /**
     * 总税款
     */
    @Digits(integer = 11, fraction = 5, message = "{总税款必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("总税款")
    private BigDecimal taxTotal;
    /**
     * 其他进口环节税
     */
    @Digits(integer = 11, fraction = 5, message = "{其他进口环节税必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("其他进口环节税")
    private BigDecimal otherTax;
    /**
     * 增值税
     */
    @Digits(integer = 11, fraction = 5, message = "{增值税必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("增值税")
    private BigDecimal taxPrice;
    /**
     * 关税
     */
    @Digits(integer = 11, fraction = 5, message = "{关税必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("关税")
    private BigDecimal dutyPrice;
    /**
     * 入/出库单号
     */
    @ApiModelProperty("入/出库单号")
    private String inOutNo;
    /**
     * 内部备注
     */
    @ApiModelProperty("内部备注")
    @XdoSize(max = 100, message = "{内部备注长度不能超过100个字符(一个汉字2位字节长度)}")
    private String remark;
    /**
     * 订舱日期
     */
    @ApiModelProperty("订舱日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date bookingDate;
    /**
     * 表头币制
     */
    @ApiModelProperty("表头币制")
    private String headCurr;
    /**
     * 计费重量
     */
    @ApiModelProperty("计费重量")
    private BigDecimal cweight;
    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal sumDecTotal;
    /**
     * 特殊关系确认
     */
    @ApiModelProperty("特殊关系确认")
    private String confirmSpecial;
    /**
     * 价格影响确认
     */
    @ApiModelProperty("价格影响确认")
    private String confirmPrice;
    /**
     * 支持特许权使用费确认
     */
    @ApiModelProperty("支持特许权使用费确认")
    private String confirmRoyalties;
    /**
     * 自报自缴
     */
    @ApiModelProperty("自报自缴")
    private String dutySelf;
    /**
     * 报关状态
     */
    @ApiModelProperty("报关状态")
    private String entryStatus;
    /**
     * 报关状态名称
     */
    @ApiModelProperty("报关状态名称")
    private String entryStatusName;
    /**
     * 实收总价
     */
    @ApiModelProperty("实收总价")
    private BigDecimal payTotal;
    /**
     * 实收总价币制
     */
    @ApiModelProperty("实收总价币制")
    private String payTotalCurr;
}
