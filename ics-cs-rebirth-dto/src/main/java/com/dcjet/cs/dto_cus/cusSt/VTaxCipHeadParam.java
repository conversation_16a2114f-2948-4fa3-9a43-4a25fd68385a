package com.dcjet.cs.dto_cus.cusSt;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date: 2020-7-27
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class VTaxCipHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     *
     */
	@XdoSize(max = 3, message = "{长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String clientType;
	/**
     *
     */
	@XdoSize(max = 50, message = "{长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String clientCode;
	/**
     *
     */
	@XdoSize(max = 50, message = "{长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String shortname;
	/**
     *
     */
	@XdoSize(max = 50, message = "{长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String tel;
	/**
     *
     */
	@XdoSize(max = 50, message = "{长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String fax;
	/**
     *
     */
	@XdoSize(max = 200, message = "{长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String nameCh;
	/**
     *
     */
	@XdoSize(max = 250, message = "{长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String nameEn;
	/**
     *
     */
	@XdoSize(max = 250, message = "{长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String addressCh;
	/**
     *
     */
	@XdoSize(max = 250, message = "{长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String addressEn;
	/**
     *
     */
	@XdoSize(max = 20, message = "{长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String tradeCode;
	/**
     *
     */
	@XdoSize(max = 12, message = "{长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String emsNo;
}
