package com.dcjet.cs.dto_cus.preerp;

import com.dcjet.cs.dto.base.BasicImportParam;
import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2021-3-8
 */
@Setter @Getter
@ApiModel(value = "预报单补充导入参数")
public class PreDecErpHeadSupplementImportParam extends BasicImportParam implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
	 * 预报单编号
	 */
	@NotEmpty(message="{预报单编号不能为空！}")
	@XdoSize(max = 20, message = "{预报单编号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("预报单编号")
	@ExcelColumn(name = "预报单编号", required = true)
	private String preEmsListNo;

	/**
     * 包装信息
     */
	@NotEmpty(message="{包装信息不能为空！}")
	@XdoSize(max = 10, message = "{包装信息长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("包装信息")
	@ExcelColumn(name = "包装信息", required = true, note = "海关参数形式 例如00", tag = "WRAP", converter = "pcodeExcelConverter", IOC = true)
	private  String wrapType;


	/**
	 * ATA日期
	 */
	@NotNull(message="{ATA日期不能为空！}")
	@ApiModelProperty("ATA日期")
	@ExcelColumn(name = "ATA日期", required = true, format = "yyyyMMdd", note = "格式如: 20210808")
	private Date ataDate;
}
