package com.dcjet.cs.dto_cus.preerp;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2021-3-8
 */
@Setter @Getter
@ApiModel(value = "预报单查询参数")
public class PreDecErpHeadSearchParam implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty("货运代理")
	private String forwardCode;

	private Date preCreateTime;

	private String preCreateTimeFrom;

	private String preCreateTimeTo;

	/**
	 * 报关行
	 */
	@ApiModelProperty("报关行")
	private String customerCode;

	/**
     * 预报单编号
     */
	@XdoSize(max = 20, message = "{预报单编号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("预报单编号")
	private  String preEmsListNo;

	/**
	 * 关务人员
	 */
	@ApiModelProperty("关务人员")
	private  String gwUser;

	/**
     * SA
     */
	@XdoSize(max = 100, message = "{SA长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("SA")
	private  String sa;
	/**
     * 境外发货人
     */
	@XdoSize(max = 40, message = "{境外发货人长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境外发货人")
	private  String overseasShipper;
	/**
     * 运输方式
     */
	@XdoSize(max = 10, message = "{运输方式长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运输方式")
	private  String trafMode;
	/**
     * 主运单号
     */
	@XdoSize(max = 100, message = "{主运单号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("主运单号")
	private  String mawb;
	/**
     * 分运单号
     */
	@XdoSize(max = 100, message = "{分运单号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("分运单号")
	private  String hawb;
	/**
     * 包装信息
     */
	@XdoSize(max = 10, message = "{包装信息长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("包装信息")
	private  String wrapType;

	/**
     * 提货日期
     */
	@ApiModelProperty("提货日期")
	private  Date deliveryDate;

	/**
     * ETD日期
     */
	@ApiModelProperty("ETD日期")
	private  Date etdDate;

	/**
     * ETA日期
     */
	@ApiModelProperty("ETA日期")
	private  Date etaDate;
	/**
    * ETA日期-开始
    */
	@ApiModelProperty("ETA日期-开始")
	private String etaDateFrom;
	/**
    * ETA日期-结束
    */
	@ApiModelProperty("ETA日期-结束")
    private String etaDateTo;
	/**
     * ATA日期
     */
	@ApiModelProperty("ATA日期")
	private  Date ataDate;

	/**
     * 起运国
     */
	@XdoSize(max = 10, message = "{起运国长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("起运国")
	private  String tradeCountry;
	/**
     * 运费
     */
	@Digits(integer = 14, fraction = 5, message = "{运费必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("运费")
	private  BigDecimal feeRate;
	/**
     * 运费币制
     */
	@XdoSize(max = 10, message = "{运费币制长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运费币制")
	private  String feeCurr;
	/**
     * 杂费币制
     */
	@XdoSize(max = 10, message = "{杂费币制长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("杂费币制")
	private  String otherCurr;
	/**
     * 杂费
     */
	@Digits(integer = 14, fraction = 5, message = "{杂费必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("杂费")
	private  BigDecimal otherRate;
	/**
     * 起运港
     */
	@XdoSize(max = 10, message = "{起运港长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("起运港")
	private  String despPort;
	/**
     * 经停港
     */
	@XdoSize(max = 10, message = "{经停港长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("经停港")
	private  String destPort;
	/**
     * 入境口岸
     */
	@XdoSize(max = 10, message = "{入境口岸长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("入境口岸")
	private  String entryPort;
	/**
     * 受托标记
     */
	@XdoSize(max = 10, message = "{受托标记长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("状态")
	private  String status;


	/**
	 * 受托标记 0 未受托, 1 已受托 默认未受托
	 */
	@ApiModelProperty("受托标记")
	private  String entrustStatus;

	/**
	 * 提货日期-开始
	 */
	@ApiModelProperty("提货日期-开始")
	private Date deliveryDateFrom;
	/**
	 * 提货日期-结束
	 */
	@ApiModelProperty("提货日期-结束")
	private Date deliveryDateTo;

	/**
	 * ETD日期-开始
	 */
	@ApiModelProperty("ETD日期-开始")
	private Date etdDateFrom;
	/**
	 * ETD日期-结束
	 */
	@ApiModelProperty("ETD日期-结束")
	private Date etdDateTo;

	/**
     * 受托时间
     */
	@ApiModelProperty("受托时间")
	private Date entrustDate;

	/**
	 * ATA日期-开始
	 */
	@ApiModelProperty("ATA日期-开始")
	private Date ataDateFrom;
	/**
	 * ATA日期-结束
	 */
	@ApiModelProperty("ATA日期-结束")
	private Date ataDateTo;

	/**
    * 受托时间-开始
    */
	@ApiModelProperty("受托时间-开始")
	private Date entrustDateFrom;
	/**
    * 受托时间-结束
    */
	@ApiModelProperty("受托时间-结束")
    private Date entrustDateTo;

	/**
	 * 预报日期-开始
	 */
	@ApiModelProperty("预报日期-开始")
	private String insertTimeFrom;
	/**
	 * 预报日期-结束
	 */
	@ApiModelProperty("预报日期-结束")
	private String insertTimeTo;


	/***
	 * 提交分单时间
	 */
	@ApiModelProperty("预提交分单时-结束")
	private Date submitDateFrom;

	/***
	 * 提交分单时间
	 */
	@ApiModelProperty("提交分单时间-结束")
	private Date submitDateTo;

}
