package com.dcjet.cs.dto_cus.optorun;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
/**
 *
 * <AUTHOR>
 * @date: 2021-8-31
 */
@Setter @Getter
@ApiModel(value = "BalanceResultParam")
public class BalanceResultParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 备案号
     */
	@NotEmpty(message="{备案号不能为空！}")
	@XdoSize(max = 512, message = "{备案号长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案号")
	private  String emsNo;
	/**
     * 手/账册内部编号
     */
	@NotEmpty(message="{手/账册内部编号不能为空！}")
	@XdoSize(max = 512, message = "{手/账册内部编号长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("手/账册内部编号")
	private  String copEmsNo;
	/**
     * 企业料号
     */
	@NotEmpty(message="{企业料号不能为空！}")
	@XdoSize(max = 100, message = "{企业料号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业料号")
	private  String facGNo;
	/**
     * 备案料号
     */
	@XdoSize(max = 50, message = "{备案料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案料号")
	private  String copGNo;
	/**
     * 备案序号
     */
	@Digits(integer = 11, fraction = 0, message = "{备案序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("备案序号")
	private  long serialNo;
	/**
     * 商品名称
     */
	@XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 商品编码
     */
	@XdoSize(max = 32, message = "{商品编码长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("商品编码")
	private  String codeTS;
	/**
     * 余料转入
     */
	@NotNull(message="{余料转入不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{余料转入必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("余料转入")
	private  BigDecimal ylImpQty;
	/**
     * 实际进口数量
     */
	@NotNull(message="{实际进口数量不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{实际进口数量必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("实际进口数量")
	private  BigDecimal actualImpQty;
	/**
     * 深加工结转转入
     */
	@NotNull(message="{深加工结转转入不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{深加工结转转入必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("深加工结转转入")
	private  BigDecimal sjgImpQty;
	/**
     * 料件退换进口
     */
	@NotNull(message="{料件退换进口不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{料件退换进口必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("料件退换进口")
	private  BigDecimal ljthImpQty;
	/**
     * 成品退换进口耗用
     */
	@NotNull(message="{成品退换进口耗用不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{成品退换进口耗用必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("成品退换进口耗用")
	private  BigDecimal exgImpCon;
	/**
     * 料件内销
     */
	@NotNull(message="{料件内销不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{料件内销必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("料件内销")
	private  BigDecimal imgSaleQty;
	/**
     * 料件复出
     */
	@NotNull(message="{料件复出不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{料件复出必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("料件复出")
	private  BigDecimal imgReturnQty;
	/**
     * 料件销毁
     */
	@NotNull(message="{料件销毁不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{料件销毁必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("料件销毁")
	private  BigDecimal imgDestroyQty;
	/**
     * 成品直接出口耗用
     */
	@NotNull(message="{成品直接出口耗用不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{成品直接出口耗用必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("成品直接出口耗用")
	private  BigDecimal expDirectCon;
	/**
     * 成品退换出口耗用
     */
	@NotNull(message="{成品退换出口耗用不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{成品退换出口耗用必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("成品退换出口耗用")
	private  BigDecimal expReturnCon;
	/**
     * 损耗耗用
     */
	@NotNull(message="{损耗耗用不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{损耗耗用必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("损耗耗用")
	private  BigDecimal dmCon;
	/**
     * 余料转出
     */
	@NotNull(message="{余料转出不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{余料转出必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("余料转出")
	private  BigDecimal ylExpQty;
	/**
     * 料件退换进口
     */
	@NotNull(message="{料件退换进口不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{料件退换进口必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("料件退换进口")
	private  BigDecimal ljthExpQty;
	/**
     * 理论结余
     */
	@NotNull(message="{理论结余不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{理论结余必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("理论结余")
	private  BigDecimal theorySurplus;
	/**
     * 已备案未出口
     */
	@NotNull(message="{已备案未出口不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{已备案未出口必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("已备案未出口")
	private  BigDecimal isRecord;
	/**
     * 未备案未出口
     */
	@NotNull(message="{未备案未出口不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{未备案未出口必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("未备案未出口")
	private  BigDecimal noRecord;
	/**
     * 最后剩余
     */
	@NotNull(message="{最后剩余不能为空！}")
	@Digits(integer = 8, fraction = 5, message = "{最后剩余必须为数字,整数位最大8位,小数最大5位!}")
	@ApiModelProperty("最后剩余")
	private  BigDecimal lastSurplus;
	/**
     * 创建人名称
     */
	@NotEmpty(message="{创建人名称不能为空！}")
	@XdoSize(max = 100, message = "{创建人名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("创建人名称")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
     * 企业代码
     */
	@XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业代码")
	private  String tradeCode;
}
