package com.dcjet.cs.dto_cus.sz;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter
@Getter
@ApiModel(value = "报表进口表头表体传入参数")
public class OutboundRepairCostsParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 序号
     */
    @ApiModelProperty("单据内部编号")
    private Long serialNo;
    /**
     * 单据内部编号
     */
    @ApiModelProperty("单据内部编号")
    private String emsListNo;
    /**
     * 客户
     */
    @ApiModelProperty("客户")
    private String overseasShipperName;
    /**
     * 进/出
     */
    @ApiModelProperty("进/出")
    private String IEMark;
    /**
     * 申报日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("申报日期")
    private Date declareDate;
    /**
     * 报关单申报日期-开始
     */
    @ApiModelProperty("报关单申报日期-开始")
    private String declareDateFrom;
    /**
     * 报关单申报日期-结束
     */
    @ApiModelProperty("报关单申报日期-结束")
    private String declareDateTo;
    /**
     * 合同号
     */
    @ApiModelProperty("合同号")
    private String contrNo;
    /**
     * S/N
     */
    @ApiModelProperty("S/N")
    private String remark1;
    /**
     * HS编码
     */
    @ApiModelProperty("HS编码")
    private String codeTS;
    /**
     * 品名
     */
    @ApiModelProperty("品名")
    private String GName;
    /**
     * 型号
     */
    @ApiModelProperty("型号")
    private String facGNo;
    /**
     * 包装件数
     */
    @ApiModelProperty("包装件数")
    private BigDecimal packNum;
    /**
     * 包装类型
     */
    @ApiModelProperty("包装类型")
    private String wrapType;
    /**
     * 净重kg
     */
    @ApiModelProperty("净重kg")
    private BigDecimal netWt;
    /**
     * 毛重kg
     */
    @ApiModelProperty("毛重kg")
    private BigDecimal grossWt;
    /**
     * 包装尺寸
     */
    @ApiModelProperty("包装尺寸")
    private String packing;
    /**
     * 产品数量
     */
    @ApiModelProperty("产品数量")
    private BigDecimal qty;
    /**
     * 单价USD
     */
    @ApiModelProperty("单价USD")
    private BigDecimal decPrice;
    /**
     * 总价USD
     */
    @ApiModelProperty("总价USD")
    private BigDecimal decTotal;
    /**
     * 贸易方式
     */
    @ApiModelProperty("贸易方式")
    private String tradeMode;
    /**
     * 物流
     */
    @ApiModelProperty("物流")
    private String forwardCode;
    /**
     * 提运单号
     */
    @ApiModelProperty("提运单号")
    private String hawb;
    /**
     * 运费RMB
     */
    @ApiModelProperty("运费RMB")
    private BigDecimal feeRate;
    /**
     * 原报关单号
     */
    @ApiModelProperty("原报关单号")
    private String primaryEntryNo;
    /**
     * 报关单号
     */
    @ApiModelProperty("报关单号")
    private String entryNo;
    /**
     * 报关行简码
     */
    @ApiModelProperty("报关行简码")
    private String declareCodeCustoms;
    /**
     * 报关行名称
     */
    @ApiModelProperty("报关行名称")
    private String declareName;
    /**
     * 进口日期
     */
    @ApiModelProperty("进口日期")
    private Date iedate;
    /**
     * 进出口日期-开始
     */
    @ApiModelProperty("进出口日期-开始")
    private String iedateFrom;
    /**
     * 进出口日期-结束
     */
    @ApiModelProperty("进出口日期-结束")
    private String iedateTo;
    /**
     * 消保
     */
    @ApiModelProperty("消保")
    private String note;
    /**
     * 是否查验
     */
    @ApiModelProperty("是否查验")
    private String inspection;
    /**
     * 报关费
     */
    @ApiModelProperty("报关费")
    private BigDecimal logisticsFee;
    /**
     * 仓储费
     */
    @ApiModelProperty("仓储费")
    private BigDecimal customsFee;
    /**
     * 备注2
     */
    @ApiModelProperty("备注2")
    private String notePass;
    /**
     * 报关维修费
     */
    @ApiModelProperty("报关维修费")
    private BigDecimal repairDecTotal;
    /**
     * 废料总价
     */
    @ApiModelProperty("废料总价")
    private BigDecimal wasteDecTotal;
    /**
     * 总维修费
     */
    @ApiModelProperty("总维修费")
    private BigDecimal totalRepairFee;
    /**
     * 废品零件
     */
    @ApiModelProperty("废品零件")
    private String scrapParts;
    /**
     * 企业代码
     */
    @ApiModelProperty("企业代码")
    private String tradeCode;
}
