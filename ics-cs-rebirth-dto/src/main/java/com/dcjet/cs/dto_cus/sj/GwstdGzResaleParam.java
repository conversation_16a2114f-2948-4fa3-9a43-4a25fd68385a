package com.dcjet.cs.dto_cus.sj;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;

/**
 * <AUTHOR>
 * @date: 2022-9-1
 */
@Setter
@Getter
@ApiModel(value = "转卖登记传入参数")
public class GwstdGzResaleParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 进出口标记
     */
    @XdoSize(max = 1, message = "{进出口标记长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("进出口标记")
    private String iemark;
    /**
     * 进出口日期
     */
    @ApiModelProperty("进出口日期")
    private Date ieDate;
    /**
     * 企业料件料号
     */
    @XdoSize(max = 50, message = "{企业料件料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业料件料号")
    private String facGNo;
    /**
     * 商品名称
     */
    @XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品名称")
    private String gName;
    /**
     * 汇总数量
     */
    @Digits(integer = 14, fraction = 5, message = "{汇总数量必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("汇总数量")
    private BigDecimal qty;
    /**
     * 计量单位
     */
    @XdoSize(max = 3, message = "{计量单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("计量单位")
    private String unit;
    /**
     * 备注
     */
    @XdoSize(max = 512, message = "{备注长度不能超过512位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String note;
    /**
     * 创建人名称
     */
    @XdoSize(max = 30, message = "{创建人名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("创建人名称")
    private String insertUserName;
    /**
     * 修改人名称
     */
    @XdoSize(max = 30, message = "{修改人名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改人名称")
    private String updateUserName;

    /**
     * 提单录入日期-开始
     */
    @ApiModelProperty("提单录入日期-开始")
    private String insertTimeFrom;
    /**
     * 提单录入日期-结束
     */
    @ApiModelProperty("提单录入日期-结束")
    private String insertTimeTo;

}
