package com.dcjet.cs.dto_cus.preerp;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date: 2021-3-8
 */
@ApiModel(value = "集装箱信息")
@Setter
@Getter
public class PreDecErpContainerDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 表头主键
     */
    @ApiModelProperty("表头主键")
    private String headId;

    /**
     * 序号
     */
    @ApiModelProperty("序号")
    private Integer serialNo;

    /**
     * 集装箱型号
     */
    @ApiModelProperty("集装箱型号")
    private String containerModel;
    /**
     * 集装箱箱号
     */
    @ApiModelProperty("集装箱箱号")
    private String containerMd;

    /**
     * 拼箱标识
     */
    @ApiModelProperty("拼箱标识")
    private  String containerLcl;

    /**
     * 集装箱自重
     */
    @ApiModelProperty("集装箱自重")
    private BigDecimal containerWt;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 预报单号
     */
    @ApiModelProperty("预报单号")
    private String preEmsListNo;
}
