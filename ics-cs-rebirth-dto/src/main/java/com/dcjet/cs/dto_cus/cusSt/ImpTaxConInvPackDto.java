package com.dcjet.cs.dto_cus.cusSt;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2020-7-24
 */
@ApiModel(value = "导入生益对账单返回信息")
@Setter @Getter
public class ImpTaxConInvPackDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 日期
      */
    @ApiModelProperty("日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@JsonProperty("mDate")
	private  Date mDate;
	/**
      * 发运单号
      */
    @ApiModelProperty("发运单号")
	private  String billNo;
	/**
      * 客户采购料号
      */
    @ApiModelProperty("客户采购料号")
	private  String po;
	/**
      * 卡板数
      */
    @ApiModelProperty("卡板数")
	@JsonProperty("kQty")
	private  BigDecimal kQty;
	/**
      * 客户物料
      */
    @ApiModelProperty("客户物料")
	@JsonProperty("cCopGNo")
	private  String cCopGNo;
	/**
      * 客户项号
      */
    @ApiModelProperty("客户项号")
	@JsonProperty("cGNo")
	private  Integer cGNo;
	/**
      * 物料描述
      */
    @ApiModelProperty("物料描述")
	@JsonProperty("gMark")
	private  String gMark;
	/**
      * 数量（片）
      */
    @ApiModelProperty("数量（片）")
	private  BigDecimal qty1;
	/**
      * 单位
      */
    @ApiModelProperty("单位")
	private  String unit;
	/**
      * 净重
      */
    @ApiModelProperty("净重")
	private  BigDecimal net;
	/**
      * 毛重
      */
    @ApiModelProperty("毛重")
	private  BigDecimal gross;
	/**
      * 金额
      */
    @ApiModelProperty("金额")
	private  BigDecimal decTotal;
	/**
      * 币别
      */
    @ApiModelProperty("币别")
	private  String decCurr;
	/**
      * 企业物料代码
      */
    @ApiModelProperty("企业物料代码")
	private  String sapCopGNo;
	/**
      * 海关物料代码
      */
    @ApiModelProperty("海关物料代码")
	private  String copGNo;
	/**
      * 数量（折合平方米）
      */
    @ApiModelProperty("数量（折合平方米）")
	private  BigDecimal qty2;
	/**
      * 客户代码
      */
    @ApiModelProperty("客户代码")
	private  String custId;
	/**
      * 客户名称
      */
    @ApiModelProperty("客户名称")
	private  String custName;
	/**
      * 合同备案号
      */
    @ApiModelProperty("合同备案号")
	private  String contractNo;
	/**
      * 发票号码
      */
    @ApiModelProperty("发票号码")
	private  String invoiceNo;
	/**
      * 箱单号码
      */
    @ApiModelProperty("箱单号码")
	private  String packingNo;
	/**
      * 中文品名
      */
    @ApiModelProperty("中文品名")
	@JsonProperty("gNameCh")
	private  String gNameCh;
	/**
      * 单价（每张）
      */
    @ApiModelProperty("单价（每张）")
	private  BigDecimal decP1;
	/**
      * 单价（每平方米）
      */
    @ApiModelProperty("单价（每平方米）")
	private  BigDecimal decP2;
	/**
      * 英文品名
      */
    @ApiModelProperty("英文品名")
	@JsonProperty("gNameEn")
	private  String gNameEn;
	/**
      * 归并序号
      */
    @ApiModelProperty("归并序号")
	private  Integer mergeId;
	/**
      * 表体序号
      */
    @ApiModelProperty("表体序号")
	private  Integer seq;
	/**
      * 是否是新美亚客户 1:是 0：否
      */
    @ApiModelProperty("是否是新美亚客户 1:是 0：否")
	private  String isXmy;
	/**
      * 客户物料-备份
      */
    @ApiModelProperty("客户物料-备份")
	@JsonProperty("cCopGNoB")
	private  String cCopGNoB;
	/**
      * 客户项号-备份
      */
    @ApiModelProperty("客户项号-备份")
	@JsonProperty("cGNoB")
	private  Integer cGNoB;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private  String sid;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 备案号
      */
    @ApiModelProperty("备案号")
	private  String emsNo;
	/**
      * 标识
      */
    @ApiModelProperty("标识")
	private  Integer tempFlag;
	/**
      * 导入数据序号
      */
    @ApiModelProperty("导入数据序号")
	private  Integer tempIndex;
	/**
      * 用户
      */
    @ApiModelProperty("用户")
	private  String tempOwner;
	/**
      * 标识
      */
    @ApiModelProperty("标识")
	private  Integer tempMark;
	/**
      * 提示
      */
    @ApiModelProperty("提示")
	private  String tempRemark;
}
