package com.dcjet.cs.dto.report;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
/**
 *
 * <AUTHOR>
 * @date: 2020-3-13
 */
@Setter @Getter
@ApiModel(value = "信越受注残接口传入参数")
public class XyszcReportParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 企业成品料号
     */
	@NotEmpty(message="{企业成品料号不能为空！}")
	@XdoSize(max = 50, message = "{企业成品料号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业成品料号")
	private  String facGNo;
	/**
     * 订单总量
     */
	@NotNull(message="{订单总量不能为空！}")
	@Digits(integer = 16, fraction = 5, message = "{订单总量必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("订单总量")
	private  BigDecimal orderTotal;
	/**
     * 已到货量
     */
	@NotNull(message="{已到货量不能为空！}")
	@Digits(integer = 16, fraction = 5, message = "{已到货量必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("订单已到货量")
	private  BigDecimal quantityArrived;
	/**
     * 未到货量
     */
	@Digits(integer = 16, fraction = 5, message = "{未到货量必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("未到货量")
	private  BigDecimal quantityNoArrived;
	/**
     * erp计量单位
     */
	@XdoSize(max = 20, message = "{ERP计量单位长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("erp计量单位")
	private  String erpUnit;
	/**
     * 申报计量单位
     */
	@XdoSize(max = 20, message = "{申报计量单位长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报计量单位")
	private  String unit;
	/**
     * erp比例因子
     */
	@Digits(integer = 22, fraction = 0, message = "{erp比例因子必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("erp比例因子")
	private  BigDecimal erpScaleFactor;
	/**
     * 转换后未到货量
     */
	@Digits(integer = 22, fraction = 0, message = "转换后{未到货量必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("转换后未到货量")
	private  BigDecimal noArrivedConvert;
	/**
     * 备案成品料号
     */
	@XdoSize(max = 50, message = "{备案成品料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案成品料号")
	private  String copGNo;
	/**
     * 备案成品名称
     */
	@XdoSize(max = 30, message = "{备案成品名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案成品名称")
	private  String copGName;
	/**
     * 备案成品序号
     */
	@Digits(integer = 22, fraction = 0, message = "{备案成品序号必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("备案成品序号")
	private  BigDecimal copLineNo;
	/**
     * 备案数量
     */
	@Digits(integer = 22, fraction = 0, message = "{备案数量必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("备案数量")
	private  BigDecimal copAmount;
	/**
     * 已出口量
     */
	@Digits(integer = 22, fraction = 0, message = "{已出口量必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("已出口量")
	private  BigDecimal exportVolume;
	/**
     * 手册余量
     */
	@Digits(integer = 22, fraction = 0, message = "{手册余量必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("手册余量")
	private  BigDecimal handAllowance;
	/**
     * 手册余量与未到货差异
     */
	@Digits(integer = 22, fraction = 0, message = "{手册余量与未到货差异必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("手册余量与未到货差异")
	private  BigDecimal handAllowanceDiff;
	/**
     * 差异百分比
     */
	@Digits(integer = 22, fraction = 0, message = "{差异百分比必须为数字,整数位最大16位,小数最大5位!}")
	@ApiModelProperty("差异百分比")
	private  BigDecimal diffPrecent;
	/**
	 * 数据来源
	 */
	@ApiModelProperty("数据来源")
	private String dateSource;
	/**
	 * 企业编码
	 */
	@ApiModelProperty("企业编码")
	private String tradeCode;
}
