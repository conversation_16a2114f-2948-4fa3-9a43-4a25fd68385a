package com.dcjet.cs.dto.ocrrecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2022-4-27
 */
@ApiModel(value = "返回信息")
@Setter
@Getter
public class GwLvOcrDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * OCR识别发票表头sid
     */
    @ApiModelProperty("OCR识别发票表头sid")
    private String headId;
    /**
     * 字段英文名称
     */
    @ApiModelProperty("字段英文名称")
    private String fieldName;
    /**
     * OCR提单识别的值
     */
    @ApiModelProperty("OCR提单识别的值")
    private String ocrValue;
    /**
     * AX中的值
     */
    @ApiModelProperty("AX中的值")
    private String axValue;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String insertUserName;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;
    /**
     * 修改人名称
     */
    @ApiModelProperty("修改人名称")
    private String updateUserName;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
