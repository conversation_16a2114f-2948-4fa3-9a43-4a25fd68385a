package com.dcjet.cs.dto.mat;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-7-9
 */
@Setter
@Getter
@ApiModel(value = "待归类传入参数")
public class MatImgexgClassifyParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 保完税标志
     */
    @NotEmpty(message = "{保完税标志不能为空！}")
    @XdoSize(max = 30, message = "{保完税标志长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("保完税标志")
    private String bondMark;
    /**
     * 备案料号
     */
    @XdoSize(max = 32, message = "{备案料号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备案料号")
    private String copGNo;
    /**
     * 企业料号
     */
    @NotEmpty(message = "{企业料号不能为空！}")
    @XdoSize(max = 100, message = "{企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业料号")
    private String facGNo;
    /**
     * 中文名称
     */
    @XdoSize(max = 100, message = "{中文名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("中文名称")
    private String copGName;
    /**
     * 中文规格型号
     */
    @XdoSize(max = 500, message = "{中文规格型号长度不能超过500位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("中文规格型号")
    private String copGModel;
    /**
     * 英文名称
     */
    @XdoSize(max = 255, message = "{英文名称(英文)长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("英文名称")
    private String copGNameEn;
    /**
     * 英文规格型号
     */
    @XdoSize(max = 500, message = "{英文规格型号长度不能超过500位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("英文规格型号")
    private String copGModelEn;
    /**
     * 单位
     */
    @XdoSize(max = 10, message = "{单位长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("单位")
    private String unit;
    /**
     * 单价
     */
    @Digits(integer = 10, fraction = 8, message = "{单价必须为数字,整数位最大10位,小数最大8位!}")
    @ApiModelProperty("单价")
    private BigDecimal decPrice;
    /**
     * 币值
     */
    @XdoSize(max = 10, message = "{币值长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("币值")
    private String curr;
    /**
     * 物料类型
     */
    @NotEmpty(message = "{物料类型不能为空！}")
    @XdoSize(max = 4, message = "{物料类型长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("物料类型")
    private String GMark;
    /**
     * 净重
     */
    @Digits(integer = 10, fraction = 10, message = "{净重必须为数字,整数位最大10位,小数最大10位!}")
    @ApiModelProperty("净重")
    private BigDecimal netWt;
    /**
     * 企业代码
     */
    @XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 状态 0正常 1 过滤
     */
    @XdoSize(max = 1, message = "{状态 0正常 1 过滤长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("状态 0正常 1 过滤")
    private String status;
    /**
     * 备注
     */
    @XdoSize(max = 500, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String remark;
    /**
     * ERP计量单位
     */
    @XdoSize(max = 20, message = "{ERP计量单位长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("ERP计量单位")
    private String unitErp;
    /**
     * 数据来源 0 手工录入 1ERP接口 2导入
     */
    @XdoSize(max = 1, message = "{数据来源 0 手工录入 1ERP接口 2导入长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("数据来源 0 手工录入 1ERP接口 2导入")
    private String dataSource;

    /**
     * 流水号
     */
    @Digits(integer = 11, fraction = 0, message = "{流水号必须为数字,整数位最大11位,小数最大0位!}")
    @ApiModelProperty("流水号")
    private BigDecimal serialNo;
    /**
     * 供应商/客户代码
     */
    @XdoSize(max = 100, message = "{供应商/客户代码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("供应商/客户代码")
    private String supplierCode;
    /**
     * 法一单位比例
     */
    @Digits(integer = 11, fraction = 5, message = "{法一单位比例必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("法一单位比例")
    private BigDecimal factor1;
    /**
     * 法二单位比例
     */
    @Digits(integer = 11, fraction = 5, message = "{法二单位比例必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("法二单位比例")
    private BigDecimal factor2;
    /**
     * ERP比例因子
     */
    @Digits(integer = 11, fraction = 8, message = "{ERP比例因子比例必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("ERP比例因子")
    private BigDecimal factorErp;
    /**
     * 贸易类型(A-不限、B-来料加工、C-进料加工)
     */
    @ApiModelProperty("贸易类型(A-不限、B-来料加工、C-进料加工)")
    private String tradeMode;
    /**
     * 净重数量
     */
    @Digits(integer = 11, fraction = 5, message = "{净重数量比例必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("净重数量")
    private BigDecimal qtyWt;


    /**
     * 创建时间-开始
     */
    @ApiModelProperty("创建时间-开始")
    private String insertTimeFrom;
    /**
     * 创建时间-结束
     */
    @ApiModelProperty("创建时间-结束")
    private String insertTimeTo;
    /**
     * 备案号
     */
    @XdoSize(max = 512, message = "{备案号长度不能超过512位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备案号")
    private String emsNo;
    /**
     * 备案序号
     */
    @Digits(integer = 11, fraction = 0, message = "{备案序号必须为数字,整数位最大11位,小数最大0位!}")
    @ApiModelProperty("备案序号")
    private BigDecimal GNo;
    /**
     * 商品编码
     */
    @XdoSize(max = 32, message = "{商品编码不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品编码")
    private String codeTS;
    /**
     * 产销国
     */
    @ApiModelProperty("产销国")
    private String country;
    /**
     * 手/账册内部编号
     */
    @ApiModelProperty("手/账册内部编号")
    private String copEmsNo;
    /**
     * 备案数量
     */
    @Digits(integer = 11, fraction = 5, message = "{备案数量必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("备案数量")
    private BigDecimal qty;
    /**
     * 备案总价
     */
    @Digits(integer = 11, fraction = 5, message = "{备案总价必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("备案总价")
    private BigDecimal decTotal;
    /**
     * 备案有效期开始时间
     */
    @ApiModelProperty("备案有效期开始时间")
    private Date recordDateStart;
    /**
     * 备案有效期结束时间
     */
    @ApiModelProperty("备案有效期结束时间")
    private Date recordDateEnd;

    /**
     * 备案有效期开始时间
     */
    @ApiModelProperty("备案有效期开始时间")
    private String recordDateStartFrom;
    /**
     * 备案有效期开始时间
     */
    @ApiModelProperty("备案有效期开始时间")
    private String recordDateStartTo;
    /**
     * 备案有效期结束时间
     */
    @ApiModelProperty("备案有效期开始时间")
    private String recordDateEndFrom;
    /**
     * 备案有效期结束时间
     */
    @ApiModelProperty("备案有效期开始时间")
    private String recordDateEndTo;
    /**
     * 商品名称
     */
    @XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品名称")
    private String GName;
    /**
     * 商品规格型号
     */
    @XdoSize(max = 255, message = "{商品规格型号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品规格型号")
    private String GModel;
    /**
     * 原始物料
     */
    @ApiModelProperty("原始物料")
    @XdoSize(max = 50, message = "{原始物料长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String originalGNo;

    @ApiModelProperty("备案状态")
    private String orgStatus;

    /**
     * 处理标志 0新增 1已处理 2 已匹配
     */
    @ApiModelProperty("处理标志")
    private Integer handleMark;

    //繁体转简体（转换栏位：中文品名，中文规格型号，备注）
    /**
     * 中文品名(转换后)
     */
    @ApiModelProperty("中文品名(转换后)")
    @XdoSize(max = 100, message = "{中文品名(转换后)长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    private String copGNameConvert;
    /**
     * 中文规格(转换后)
     */
    @ApiModelProperty("中文规格(转换后)")
    @XdoSize(max = 500, message = "{中文规格(转换后)长度不能超过500位字节长度(一个汉字2位字节长度)!}")
    private String copGModelConvert;
    /**
     * 备注(转换后)
     */
    @ApiModelProperty("备注(转换后)")
    @XdoSize(max = 500, message = "{备注(转换后)长度不能超过500位字节长度(一个汉字2位字节长度)!}")
    private String remarkConvert;
    /**
     * 翻译状态
     */
    @ApiModelProperty("翻译状态")
    private String translateStatus;
    /**
     * 翻译时间
     */
    @ApiModelProperty("翻译时间")
    private Date translateTime;
    /**
     * 翻译错误
     */
    @ApiModelProperty("翻译错误")
    private String tempRemark;

    /**
     * 单据标记 11：金二手册 12：H200手册 21：金二账册 22：H2000账册
     */
    @ApiModelProperty("单据标记 11：金二手册 12：H200手册 21：金二账册 22：H2000账册")
    private String billFlag;
    /**
     * 转换后净重
     */
    @ApiModelProperty("转换后净重")
    @Digits(integer = 11, fraction = 8, message = "{转换后净重必须为数字,整数位最大11位,小数最大8位!}")
    private BigDecimal netWtConvert;
    /**
     * 法定计量单位
     */
    @ApiModelProperty("法定计量单位")
    private String unit1;
    /**
     * 法定第二计量单位
     */
    @ApiModelProperty("法定第二计量单位")
    private String unit2;
    /**
     * 工厂代码
     */
    @XdoSize(max = 50, message = "{工厂代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("工厂代码")
    private String factoryCode;
    /**
     * 客户料号
     */
    @XdoSize(max = 100, message = "{客户料号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户料号")
    private String customerGNo;

    /**
     * 更新时间-开始
     */
    @ApiModelProperty("更新时间-开始")
    private String updateTimeFrom;
    /**
     * 更新时间-结束
     */
    @ApiModelProperty("更新时间-结束")
    private String updateTimeTo;
}
