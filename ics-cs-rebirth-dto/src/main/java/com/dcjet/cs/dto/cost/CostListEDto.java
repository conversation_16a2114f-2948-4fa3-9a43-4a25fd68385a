package com.dcjet.cs.dto.cost;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 出口费用表体
 * <p>
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-02-27
 */
@ApiModel(value = "出口费用表体 DTO")
@Setter
@Getter
public class CostListEDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 费用科目代码
     */
    @ApiModelProperty(value = "费用科目代码")
    private String costCourseCode;

    /**
     * 币制
     */
    @ApiModelProperty(value = "币制")
    private String curr;

    /**
     * 费用
     */
    @ApiModelProperty(value = "费用")
    private String decTotal;

    /**
     * 明细表头SID
     */
    @ApiModelProperty(value = "明细表头SID")
    private String headId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 人民币费用
     */
    @ApiModelProperty(value = "人民币费用")
    private BigDecimal rmbDecTotal;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Long serialNo;

    /***
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal noRateAmount;
    /**
     * 税金
     */
    @ApiModelProperty(value = "税金")
    private BigDecimal rate;
}