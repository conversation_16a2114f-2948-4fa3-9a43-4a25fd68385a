package com.dcjet.cs.dto.erp;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2019-4-22
 */
@ApiModel(value = "进出口模板返回信息")
@Setter @Getter
public class DecErpHeadTempleteDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private  String sid;
	/**
      * 提单企业内部编号
      */
    @ApiModelProperty("提单企业内部编号")
	private  String emsListNo;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 经营企业名称
      */
    @ApiModelProperty("经营企业名称")
	private  String tradeName;
	/**
      * 经营企业社会信用代码
      */
    @ApiModelProperty("经营企业社会信用代码")
	private  String tradeCreditCode;
	/**
      * 申报企业编码
      */
    @ApiModelProperty("申报企业编码")
	private  String declareCode;
	/**
      * 申报企业名称
      */
    @ApiModelProperty("申报企业名称")
	private  String declareName;
	/**
      * 申报企业社会信用代码
      */
    @ApiModelProperty("申报企业社会信用代码")
	private  String declareCreditCode;
	/**
      * 录入企业编号
      */
    @ApiModelProperty("录入企业编号")
	private  String inputCode;
	/**
      * 录入单位名称
      */
    @ApiModelProperty("录入单位名称")
	private  String inputName;
	/**
      * 录入企业社会信用代码
      */
    @ApiModelProperty("录入企业社会信用代码")
	private  String inputCreditCode;
	/**
      * 供应商编码
      */
    @ApiModelProperty("供应商编码")
	private  String supplierCode;
	/**
      * 供应商
      */
    @ApiModelProperty("供应商")
	private  String supplierName;
	/**
      * 货代编码
      */
    @ApiModelProperty("货代编码")
	private  String forwardCode;
	/**
      * 货代
      */
    @ApiModelProperty("货代")
	private  String forwardName;
	/**
      * 出境关别(出口口岸)代码
      */
    @ApiModelProperty("出境关别(出口口岸)代码")
	private  String IEPort;
	/**
      *申报地海关
      */
    @ApiModelProperty("申报地海关")
	private  String masterCustoms;
	/**
      * 监管方式(贸易方式)代码
      */
    @ApiModelProperty("监管方式(贸易方式)代码")
	private  String tradeMode;
	/**
      * 运输方式代码
      */
    @ApiModelProperty("运输方式代码")
	private  String trafMode;

	/**
      * 贸易国别
      */
    @ApiModelProperty("贸易国别")
	private  String tradeNation;
	/**
      * 归并类型0-自动归并 1-人工归并
      */
    @ApiModelProperty("归并类型0-自动归并 1-人工归并")
	private  String mergeType;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 录入日期
      */
    @ApiModelProperty("录入日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date inputDate;
	/**
      * 成交方式
      */
    @ApiModelProperty("成交方式")
	private  String transMode;
	/**
      * 启运、运抵国别代码
      */
    @ApiModelProperty("启运、运抵国别代码")
	private  String tradeCountry;
	/**
      * 启运港
      */
    @ApiModelProperty("启运港")
	private  String despPort;
	/**
      * 价格说明
      */
    @ApiModelProperty("价格说明")
	private  String promiseItems;
	/**
      * 有效标志 0 有效，1 无效
      */
    @ApiModelProperty("有效标志 0 有效，1 无效")
	private  String validMark;
	/**
      * 备案号
      */
    @ApiModelProperty("备案号")
	private  String emsNo;
	/**
      * 主提运单
      */
    @ApiModelProperty("主提运单")
	private  String mawb;
	/**
      * 提运单号
      */
    @ApiModelProperty("提运单号")
	private  String hawb;
	/**
      * 合同协议号
      */
    @ApiModelProperty("合同协议号")
	private  String contrNo;
	/**
      * 许可证号
      */
    @ApiModelProperty("许可证号")
	private  String licenseNo;
	/**
      * 发票号码
      */
    @ApiModelProperty("发票号码")
	private  String invoiceNo;
	/**
      * 件数
      */
    @ApiModelProperty("件数")
	private BigDecimal packNum;
	/**
      * 体积
      */
    @ApiModelProperty("体积")
	private  BigDecimal volume;
	/**
      * 总净重
      */
    @ApiModelProperty("总净重")
	private  BigDecimal netWt;
	/**
      * 指运港/装货港
      */
    @ApiModelProperty("指运港/装货港")
	private  String destPort;
	/**
      * 包装种类
      */
    @ApiModelProperty("包装种类")
	private  String wrapType;
	/**
      * 杂费币制
      */
    @ApiModelProperty("杂费币制")
	private  String otherCurr;
	/**
      * 杂费类型
      */
    @ApiModelProperty("杂费类型")
	private  String otherMark;
	/**
      * 杂费
      */
    @ApiModelProperty("杂费")
	private  BigDecimal otherRate;
	/**
      * 运费币制
      */
    @ApiModelProperty("运费币制")
	private  String feeCurr;
	/**
      * 运费类型
      */
    @ApiModelProperty("运费类型")
	private  String feeMark;
	/**
      * 运费
      */
    @ApiModelProperty("运费")
	private  BigDecimal feeRate;
	/**
      * 保费币制
      */
    @ApiModelProperty("保费币制")
	private  String insurCurr;
	/**
      * 保费类型
      */
    @ApiModelProperty("保费类型")
	private  String insurMark;
	/**
      * 保费
      */
    @ApiModelProperty("保费")
	private  BigDecimal insurRate;
	/**
      * 毛重
      */
    @ApiModelProperty("毛重")
	private  BigDecimal grossWt;

	/**
      * 境内货源地/目的地
      */
    @ApiModelProperty("境内货源地/目的地")
	private  String districtCode;
	/**
      * 收/发货人CODE
      */
    /*@ApiModelProperty("收/发货人CODE")
	private  String receiveCode;*/
	/**
      * 收/发货人NAME
      */
    /*@ApiModelProperty("收/发货人NAME")
	private  String receiveName;*/
	/**
      * 航班日期
      */
    @ApiModelProperty("航班日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date voyageDate;
	/**
      * 出货日期
      */
    @ApiModelProperty("出货日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date shipDate;
	/**
      * 境外发货人code
      */
    @ApiModelProperty("境外发货人code")
	private  String overseasShipper;
	/**
      * 境外发货人name
      */
    @ApiModelProperty("境外发货人name")
	private  String overseasShipperName;
	/**
      * 征免性质
      */
    @ApiModelProperty("征免性质")
	private  String cutMode;
	/**
      * 货物存放地点
      */
    @ApiModelProperty("货物存放地点")
	private  String warehouse;
	/**
      * 最终目的国
      */
    @ApiModelProperty("最终目的国")
	private  String destinationCountry;
	/**
      * 入境口岸/出境口岸
      */
    @ApiModelProperty("入境口岸/出境口岸")
	private  String entryPort;

	/**
      * 审核状态
      */
    @ApiModelProperty("审核状态")
	private  String status;
	/**
      * 进出口标志I-进口 E-出口
      */
    @ApiModelProperty("进出口标志I-进口 E-出口")
	private  String IEMark;
	/**
      * 模板名称
      */
    @ApiModelProperty("模板名称")
	private  String tempName;
	/**
	 * 保完税标识 0 保税 1 非保税
	 */
	@ApiModelProperty("保完税标识 0 保税 1 非保税")
	private  String bondMark;
	/**
	 * shipTo代码
	 */
	@ApiModelProperty("shipTo代码")
	private  String shipTo;
	/**
	 * 申报单位编码
	 */
	@ApiModelProperty("申报单位编码(基础信息)")
	private  String declareCodeCustoms;
	/**
	 * 申报单位名称
	 */
	@ApiModelProperty("申报单位名称(基础信息)")
	private  String declareNameCustoms;
	/**
	 * 清单归并类型
	 */
	@ApiModelProperty("清单归类类型")
	private  String billType;
	/**
	 * 关联账册（手册）编号
	 */
	@ApiModelProperty("关联账册（手册）编号")
	private  String relEmsNo;
	/**
	 * 集装箱类型
	 */
	@ApiModelProperty("集装箱类型")
	private  String containerType;
	/**
	 * 集装箱数量
	 */
	@ApiModelProperty("集装箱数量")
	private  String containerNum;
	/**
	 * 包装种类2
	 */
	@ApiModelProperty("包装种类2")
	private  String wrapType2;

	/**
	 * 贸易条款
	 */
	@ApiModelProperty("贸易条款")
	private  String tradeTerms;
	/**
	 * 料件成品标记
	 */
	@ApiModelProperty("料件成品标记")
	private String GMark;
	/**
	 * 报关标志
	 */
	@ApiModelProperty("报关标志")
	private  String dclcusMark;
	/**
	 * 报关类型
	 */
	@ApiModelProperty("报关类型")
	private  String dclcusType;
	/**
	 * 报关单类型
	 */
	@ApiModelProperty("报关单类型")
	private  String entryType;

	/**
	 * 境内货源地/境内目的地(行政区域)
	 */
	@ApiModelProperty("境内货源地/境内目的地(行政区域)")
	private  String districtPostCode;
	/**
	 * shipFrom代码
	 */
	@ApiModelProperty("shipFrom代码")
	private String shipFrom;
	/**
	 * 清单类型
	 */
	@ApiModelProperty("清单类型")
	private  String billListType;

	/**
	 * 进口: 计划进口日期  出口:计划出口日期(邀请日期)
	 */
	@ApiModelProperty("计划进口日期/计划出口日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date inviteDate;

	/**
	 * 计费重量
	 */
	@ApiModelProperty("计费重量")
	private BigDecimal cweight;
	/**
	 * 清单申报单位
	 */
	@ApiModelProperty("清单申报单位")
	private  String agentCode;
	/**
	 * 清单申报单位名称
	 */
	@ApiModelProperty("清单申报单位名称")
	private  String agentName;
	/**
	 * 清单申报单位社会统一信用代码
	 */
	@ApiModelProperty("清单申报单位社会信用代码")
	private  String agentCreditCode;
	/**
	 * 清单申报单位编码
	 */
	@ApiModelProperty("清单申报单位编码(基础信息)")
	private  String agentCodeCustoms;
	/**
	 * 清单申报单位名称
	 */
	@ApiModelProperty("清单申报单位名称(基础信息)")
	private  String agentNameCustoms;
	/**
	 * 单据类型 0大提单 1 小提单
	 */
	@ApiModelProperty("单据类型")
	private String decType;
	private String shipFromName;
	private String shipToName;

}
