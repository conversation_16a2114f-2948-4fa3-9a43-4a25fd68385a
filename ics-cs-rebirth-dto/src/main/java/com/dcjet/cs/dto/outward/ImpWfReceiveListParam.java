package com.dcjet.cs.dto.outward;

import com.xdo.validation.annotation.XdoEnum;
import com.xdo.validation.annotation.XdoSize;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2020-04-13
 */
@Setter
@Getter
public class ImpWfReceiveListParam implements Serializable {
    private static final long serialVersionUID = 1L;

	@NotEmpty(message="{物料类型不能为空！}")
	@XdoEnum(message = "{物料类型必须为: I、E、3、4}", values = {"I", "E", "3", "4}"})
	@XdoSize(max = 10, message = "{物料类型长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	private String GMark;

	@NotEmpty(message="{企业料号不能为空！}")
	@XdoSize(max = 50, message = "{企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private String facGNo;

	@XdoSize(max = 10, message = "{商品编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	private String codeTS;

	@XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	private String GName;

	@XdoSize(max = 3, message = "{计量单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	private String unit;

	@NotEmpty(message="{收货数量不能为空！}")
	@XdoSize(max = 20, message = "{收货数量必须为数字,整数位最大13位,小数最大5位!}")
	private String qty;

	@XdoSize(max = 20, message = "{收货金额必须为数字,整数位最大13位,小数最大5位!}")
	private String amount;

	@XdoSize(max = 3, message = "{币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	private String curr;

	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	private String remark;
}
