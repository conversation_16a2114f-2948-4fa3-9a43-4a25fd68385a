package com.dcjet.cs.dto.receipt;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
/**
 *
 * <AUTHOR>
 * @date: 2019-5-21
 */
@Setter @Getter
@ApiModel(value = "发票表头传入参数")
public class ReceiptTemplateHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 单证属性
     */
	@NotEmpty(message="{单证属性不能为空！}")
	@XdoSize(max = 1, message = "{单证属性长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("单证属性")
	private  String documentType;
	/**
     * 适用客户代码
     */
	@XdoSize(max = 12, message = "{适用客户代码长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("适用客户代码")
	private  String customerCode;
	/**
     * 模板编号
     */
	@NotEmpty(message="{模板编号不能为空！}")
	@XdoSize(max = 20, message = "{模板编号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("模板编号")
	private  String templateCode;
	/**
     * 运输方式
     */
	@ApiModelProperty("运输方式")
	private  String trafMode;
	/**
     * 联络人
     */
	@XdoSize(max = 50, message = "{联络人长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("联络人")
	private  String linkmanName;
	/**
     * 目的地
     */
	@XdoSize(max = 20, message = "{目的地长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("目的地")
	private  String destination;
	/**
     * 备注
     */
	@XdoSize(max = 100, message = "{备注长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 状态
     */
	@NotEmpty(message="{状态不能为空！}")
	@XdoSize(max = 1, message = "{状态长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("状态")
	private  String status;
	/**
     * 模板类型
     */
	@XdoSize(max = 1, message = "{模板类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("模板类型")
	private  String templateType;
	/**
     * 录入时间
     */
	@ApiModelProperty("录入时间")
	private  Date insertTime;

	@NotNull(message="{进出口标记不能为空}")
	@ApiModelProperty("进出口标记")
	private String IEMark;
	/**
	 * 运输方式显示
	 */
	@XdoSize(max = 30, message = "{运输方式显示长度不能超过30位字节长度(一个汉字2个字节长度)!}")
	@ApiModelProperty("运输方式显示")
	private  String trafModeName;
	/**
	 * 币制显示
	 */
	@XdoSize(max = 20, message = "{币制显示长度不能超过20位字节长度(一个汉字2个字节长度)!}")
	@ApiModelProperty("币制显示")
	private  String currName;
	/**
	 * 目的港
	 */
	@XdoSize(max = 40, message = "{币制显示长度不能超过40位字节长度(一个汉字2个字节长度)!}")
	@ApiModelProperty("目的港")
	private  String destPort;
}
