package com.dcjet.cs.dto.bi;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2020-5-22
 */
@Setter @Getter
@ApiModel(value = "企业参数库-特许权使用费传入参数")
public class GwstdRoyaltyFeeParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 进出口类型，I进口 E出口
     */
	@NotEmpty(message="{进出口类型不能为空！}")
	@XdoSize(max = 4, message = "{进出口类型长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("进出口标记，I进口 E出口")
	private  String iemark;
	/**
     * 特许权关联号
     */
	@NotEmpty(message="{特许权关联号不能为空！}")
	@XdoSize(max = 100, message = "{特许权关联号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("特许权关联号")
	private  String royalityNo;
	/**
     * 特许权使用费：0-否，1-是
     */
	@XdoSize(max = 10, message = "{特许权使用费：0-否，1-是长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("特许权使用费：0-否，1-是")
	private  String royalityFee;
	/**
     * 备注
     */
	@XdoSize(max = 500, message = "{备注长度不能超过500位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 制单人姓名
     */
	@XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("制单人姓名")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
	 * 所属企业编码
	 */
	@XdoSize(max = 10, message = "{所属企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("所属企业编码")
	private  String tradeCode;
}
