package com.dcjet.cs.dto.gwLvCitesBatchFileMain;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 *
 * <AUTHOR>
 * @date: 2022-4-24
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class GwLvCitesBatchFileMainDto implements Serializable {
    private static final long serialVersionUID = 1L;

	
	private String brandCodeNameAll;
	/**
	 * 外方CITES编号
	 */
	
	private  String outSideNo;

	private String isExpired;

	private  Date greelightCreateDate;

	private  Date qtotaQreelightCreateDate;

	/**
	 * 外方证书到期日期
	 */
	
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date outSideProofValid;

	private String customsType;

	/**
	 * 是否quota
	 */
	
	private  String isQuota;
	/**
	 * Transport NO
	 */
	
	private  String transportNo;

	/**
	 * 生成清单日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createBillDate;


	
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date batchFilePunishDate;

	
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date batchFileValidDate;

	/**
	 * week
	 */
	
	private  String week;

	
	private String batchFileNo;
	/**
	 * sid
	 */
	private  String sid;
	/**
	 * 插入日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
	 * 插入人
	 */
	private  String insertUser;
	/**
	 * 更新日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
	 * 更新人
	 */
	private  String updateUser;
	/**
	 * 企业编码
	 */
	private  String tradeCode;
	/**
	 * 状态
	 */
	
	private  String status;
	/**
	 * 操作日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date operateDate;

	/**
	 * 品牌
	 */
	private  String brandCode;
	/**
	 * TR标志
	 */
	private  String trMark;
	/**
	 * 办证流水号
	 */
	private  String batchFileSerial;
	
	
	private String isOpen;

	
	private Boolean isSearchLikeMatch;

	private String statusAll;
}
