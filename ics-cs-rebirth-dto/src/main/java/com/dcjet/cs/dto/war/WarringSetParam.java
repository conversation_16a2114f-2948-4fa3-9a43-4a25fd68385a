package com.dcjet.cs.dto.war;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @date: 2019-5-16
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class WarringSetParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 预警名称
     */
	@Size(max = 255, message = "{预警名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("预警名称")
	private  String warningName;
	/**
     * 预警类型 T_BI_CUSTOMER_PARAMS   WARRING_TYPE
     */
	@Size(max = 50, message = "{预警类型 T_BI_CUSTOMER_PARAMS   WARRING_TYPE长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("预警类型 T_BI_CUSTOMER_PARAMS   WARRING_TYPE")
	private  String warringType;
	/**
     * 预警天数
     */
	@Digits(integer = 11, fraction = 0, message = "{预警天数必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("预警天数")
	private  Integer warringDays;
	/**
     * 预警百分比
     */
	@Digits(integer = 4, fraction = 2, message = "{预警百分比必须为数字,整数位最大4位,小数最大2位!}")
	@ApiModelProperty("预警百分比")
	private  BigDecimal warringPercent;
	/**
     * 预警最小值
     */
	@Digits(integer = 12, fraction = 4, message = "{预警最小值必须为数字,整数位最大12位,小数最大4位!}")
	@ApiModelProperty("预警最小值")
	private  BigDecimal warringMin;
	/**
     * 预警最大值
     */
	@Digits(integer = 12, fraction = 4, message = "{预警最大值必须为数字,整数位最大12位,小数最大4位!}")
	@ApiModelProperty("预警最大值")
	private  BigDecimal warringMax;
	/**
     * 启动标志 1：启动 0：禁用 默认 1  暂时不用
     */
	@ApiModelProperty("启动标志 1：启动 0：禁用 默认 1  暂时不用")
	private  String enableMark;
	/**
	 * 业务主键
	 */
	@ApiModelProperty("业务字段")
	private String businessId;
}
