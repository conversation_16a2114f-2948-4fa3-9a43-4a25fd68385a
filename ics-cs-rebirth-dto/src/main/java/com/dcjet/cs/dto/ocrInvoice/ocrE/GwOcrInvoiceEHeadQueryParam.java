package com.dcjet.cs.dto.ocrInvoice.ocrE;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date: 2023-2-13
 */
@Data
@ApiModel("GwOcrInvoiceEHeadQueryParam")
public class GwOcrInvoiceEHeadQueryParam {

    @NotEmpty(message = "{sid不可为空}")
    @ApiModelProperty("主键sid")
    private String sid;
    /**
     * 生成状态(1已识别 2已生成)
     */
    @XdoSize(max = 1, message = "{生成状态(1已识别 2已生成)长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("生成状态(1已识别 2已生成)")
    private String createStatus;
    /**
     * 修改状态（1未修改；2已修改）
     */
    @XdoSize(max = 1, message = "{修改状态（1未修改；2已修改）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改状态（1未修改；2已修改）")
    private String modifyStatus;

    /**
     * 数据类型(INVOICE.发票 PACKING.箱单)
     */
    @XdoSize(max = 20, message = "{数据类型(INVOICE.发票 PACKING.箱单)长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("文件类型(INVOICE.发票 PACKING.箱单)")
    private String dataType;

    /**
     * 发票号码
     */
    @XdoSize(max = 100, message = "{发票号码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发票号码")
    private String invoiceNo;

    /**
     * 装箱单号码
     */
    @XdoSize(max = 50, message = "{装箱单号码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("装箱单号码")
    private String packingNo;

    /**
     * 发货人
     */
    @XdoSize(max = 100, message = "{发货人长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发货人")
    private String shipper;

    /**
     * 境外发货人代码
     */
    @XdoSize(max = 30, message = "{境外发货人代码长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外发货人代码")
    private String shipperCode;

    /**
     * 发票日期-开始
     */
    @ApiModelProperty("发票日期-开始")
    private String invoiceDateFrom;
    /**
     * 发票日期-结束
     */
    @ApiModelProperty("发票日期-结束")
    private String invoiceDateTo;

    @ApiModelProperty("单据内部编号")
    private String emsListNo;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("是否需要Loc")
    private Boolean needLoc;

    @ApiModelProperty("tradeCode")
    private String tradeCode;

    @ApiModelProperty("consignee")
    private String consignee;

}
