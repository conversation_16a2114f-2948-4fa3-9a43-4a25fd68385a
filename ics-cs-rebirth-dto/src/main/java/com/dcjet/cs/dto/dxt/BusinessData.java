package com.dcjet.cs.dto.dxt;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@Setter @Getter
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "BusinessData")
@XmlType(propOrder = {
        "InvtHead",
        "InvtListDetail",
        "InvtInOutStoreList"
})
public class BusinessData implements Serializable {
    private static final long serialVersionUID = 1L;

    private com.dcjet.cs.dto.dxt.InvtHead InvtHead;
    private com.dcjet.cs.dto.dxt.InvtListDetail InvtListDetail;
    private com.dcjet.cs.dto.dxt.InvtInOutStoreList InvtInOutStoreList;
}
