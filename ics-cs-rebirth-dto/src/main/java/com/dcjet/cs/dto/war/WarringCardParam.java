package com.dcjet.cs.dto.war;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class WarringCardParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 序号
     */
	@Digits(integer = 11, fraction = 0, message = "{序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("序号")
	private BigDecimal serialNo;
	/**
     * 单据号
     */
	@NotNull(message="{证件编号不能为空！}")
	@XdoSize(max = 30, message = "{证件编号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("证件编号")
	private  String documentNo;
	/**
     * 证件名称
     */
	@NotNull(message="{证件名称不能为空！}")
	@XdoSize(max = 100, message = "{证件名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("证件名称")
	private  String documentName;
	/**
     * 有效期起
     */
	@ApiModelProperty("有效期起")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date dateStart;
	/**
    * 有效期起-开始
    */
	@ApiModelProperty("有效期起-开始")
	private String dateStartFrom;
	/**
    * 有效期起-结束
    */
	@ApiModelProperty("有效期起-结束")
    private String dateStartTo;
	/**
     * 有效期止
     */
	@ApiModelProperty("有效期止")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date dateEnd;
	/**
    * 有效期止-开始
    */
	@ApiModelProperty("有效期止-开始")
	private String dateEndFrom;
	/**
    * 有效期止-结束
    */
	@ApiModelProperty("有效期止-结束")
    private String dateEndTo;
	/**
     * 备注
     */
	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 发证机关
     */
	@XdoSize(max = 30, message = "{发证机关长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发证机关")
	private  String authority;
	/**
     * 持卡人
     */
	@XdoSize(max = 30, message = "{持卡人长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("持卡人")
	private  String cardHolder;
	/**
     * 负责人
     */
	@XdoSize(max = 30, message = "{负责人长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("负责人")
	private  String chargePerson;
	/**
	 * 数据状态
	 */
	@ApiModelProperty("数据状态")
	private String dataStatus;

	/**
	 * 提单录入日期-开始
	 */
	private String insertTimeFrom;
	/**
	 * 提单录入日期-结束
	 */
	private String insertTimeTo;
	/**
	 * 预警天数
	 */
	@ApiModelProperty("预警天数")
	private  Integer warringDays;
}
