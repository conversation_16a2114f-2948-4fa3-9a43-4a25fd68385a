package com.dcjet.cs.dto.war;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 * 
 * <AUTHOR>
 * @date: 2019-5-17
 */
@ApiModel(value = "WarringEmailConfigDto返回信息")
@Setter @Getter
public class WarringEmailConfigDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
      * 通知人名称
      */
    @ApiModelProperty("通知人名称")
	private  String userName;
	/**
      * 通知人职位
      */
    @ApiModelProperty("通知人职位")
	private  String userPosition;
	/**
      * 通知人邮箱
      */
    @ApiModelProperty("通知人邮箱")
	private  String userEmail;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 启动标志 1：启动 0：禁用 默认 1  暂时不用
      */
    @ApiModelProperty("启动标志 1：启动 0：禁用 默认 1  暂时不用")
	private  String enableMark;
	/**
      * 预警类型 多个逗号分隔
      */
    @ApiModelProperty("预警类型 多个逗号分隔")
	private  String warringTypes;
	/**
      * 邮件预警限制标志  1：限制 0：不限制 默认1
      */
    @ApiModelProperty("邮件预警限制标志  1：限制 0：不限制 默认1")
	private  String warringLimitMark;
	/**
      * 邮件预警限制次数 当限制标志为1时 启动 
      */
    @ApiModelProperty("邮件预警限制次数 当限制标志为1时 启动 ")
	private  Integer warringLimitCount;

	/**
	 * 主页预警提示 0 否 1 是
	 */
	@ApiModelProperty("主页预警提示 0 否 1 是")
	private  String homepageMark;
	/**
	 * 预警类型中文
	 */
	@ApiModelProperty("预警类型中文")
	private  String warringTypeCn;
	/**
	 * 预警类型
	 */
	@ApiModelProperty("预警类型")
	private  String warringType;
	/**
	 * 表体id
	 */
	@ApiModelProperty("表体id")
	private  String bodyId;

	/**
	 * 预警类型Id
	 */
	@ApiModelProperty("预警类型Id")
	private  String warringId;
}
