package com.dcjet.cs.dto.erp.sap;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-11-6
 */
@Setter
@Getter
@ApiModel(description= "ERP核销BOM接口传入参数")
public class ErpCavBomParam {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 成品料号
     */
    @ApiModelProperty("成品料号")
    private String copExgNo;
    /**
     * 项次
     */
    @ApiModelProperty("项次")
    private String itemNo;
    /**
     * 成品版本号
     */
    @ApiModelProperty("成品版本号")
    private String exgVersion;
    /**
     * 料件料号
     */
    @ApiModelProperty("料件料号")
    private String copImgNo;
    /**
     * 净耗
     */
    @ApiModelProperty("净耗")
    private BigDecimal decCm;
    /**
     * 单耗
     */
    @ApiModelProperty("单耗")
    private BigDecimal decAllConmuse;
    /**
     * 主件底数
     */
    @ApiModelProperty("主件底数")
    private BigDecimal baseNum;
    /**
     * 料件单位
     */
    @ApiModelProperty("料件单位")
    private String imgUnit;
    /**
     * 成品单位
     */
    @ApiModelProperty("成品单位")
    private String exgUnit;
    /**
     * 有形损耗率
     */
    @ApiModelProperty("有形损耗率")
    private BigDecimal decDmVisiable;
    /**
     * 有效起始日期
     */
    @NotNull(message = "{有效起始日期不能为空！}")
    @ApiModelProperty("有效起始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validBeginDate;
    /**
     * 有效起始日期-开始
     */
    @ApiModelProperty("有效起始日期")
    private String validBeginDateFrom;
    /**
     * 有效结束日期
     */
    @NotNull(message = "{有效结束日期不能为空！}")
    @ApiModelProperty("有效结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validEndDate;
    /**
     * 有效结束日期-结束
     */
    @ApiModelProperty("有效结束日期")
    private String validEndDateTo;
    /**
     * 料件保完税标志
     */
    @ApiModelProperty("料件保完税标志")
    private String imgBondMark;
    /**
     * 成品保完税标志
     */
    @ApiModelProperty("成品保完税标志")
    private String exgBondMark;
    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    private String factory;
    /**
     * 备注1
     */
    @ApiModelProperty("备注1")
    private String remark1;
    /**
     * 备注2
     */
    @ApiModelProperty("备注2")
    private String remark2;

    /**
     * 用户/导入批次号
     */
    @ApiModelProperty("传输批次号")
    private String tempOwner;

    /**
     * ERP最后变更日期
     */
    @ApiModelProperty("ERP最后变更日期")
    private Date lastUpdateTime;
    /**
     * ERP最后变更日期-开始
     */
    @ApiModelProperty("ERP最后变更日期-开始")
    private String lastUpdateTimeFrom;
    /**
     * ERP最后变更日期-结束
     */
    @ApiModelProperty("ERP最后变更日期-结束")
    private String lastUpdateTimeTo;
    /**
     * 无形损耗率
     */
    @ApiModelProperty("无形损耗率")
    private BigDecimal decDmInvisiable;
    /**
     * 保税料件比例
     */
    @ApiModelProperty("保税料件比例")
    private BigDecimal bondMtpckPrpr;

    private String tradeCode;
}
