package com.dcjet.cs.dto.gwstd;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2020-11-27
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class GwstdHscodeParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 商品编码
     */
	@NotEmpty(message="{商品编码不能为空！}")
	@XdoSize(max = 10, message = "{商品编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("商品编码")
	private  String codeTS;
	/**
     * 商品名称
     */
	@XdoSize(max = 500, message = "{商品名称长度不能超过500位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 法一单位
     */
	@XdoSize(max = 3, message = "{法一单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("法一单位")
	private  String unit1;
	/**
     * 法二单位
     */
	@XdoSize(max = 3, message = "{法二单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("法二单位")
	private  String unit2;

	/** 物料类型 */
	private  String gMark;
	/** 备案料号 */
	private String copGNo;
	/** 序号 */
	private Long serialNo;
	/** 备案号 */
	private String emsNo;
}
