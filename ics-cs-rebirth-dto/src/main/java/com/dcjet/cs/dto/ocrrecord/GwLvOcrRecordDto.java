package com.dcjet.cs.dto.ocrrecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2022-4-25
 */
@ApiModel(value = "返回信息")
@Setter
@Getter
public class GwLvOcrRecordDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * shipment ID
     */
    @ApiModelProperty("shipment ID")
    private String shipmentId;
    private String shipmentIdConvert;

    /**
     * 品牌
     */
    @ApiModelProperty("品牌")
    private String brandCode;
    private String brandName;
    private String brandAbbr;
    /**
     * 品牌ocr识别标志
     */
    private String brandOcr;
    /**
     * 业务类型(INV_PACK)
     */
    @ApiModelProperty("业务类型(INV_PACK)")
    private String bussinessType;
    /**
     * 执行状态(0 OCR识别失败、1 OCR识别成功、2 OCR识别中)
     */
    @ApiModelProperty("执行状态(0 OCR识别失败、1 OCR识别成功、2 OCR识别中)")
    private String status;

    /**
     * 回填写入业务表的SID
     */
    @ApiModelProperty("回填写入业务表的SID")
    private String taskId;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String insertUserName;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;
    /**
     * 修改人名称
     */
    @ApiModelProperty("修改人名称")
    private String updateUserName;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("发票号码")
    private String invoiceNo;
    @ApiModelProperty("解析失败的原因")
    private String errorMessage;
    @ApiModelProperty("调用OCR上传接口成功后返回的信息(JSON存储)")
    private String uploadRes;
    @ApiModelProperty("原始路径")
    private String originalPath;
    @ApiModelProperty("备份路径")
    private String bakPath;

    private String parseErrorMsg;
}
