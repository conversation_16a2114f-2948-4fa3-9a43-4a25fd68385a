package com.dcjet.cs.dto.gwstdentry;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 *
 * <AUTHOR>
 * @date: 2020-3-10
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class GwstdEntryHeadLogParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 申报单位社会信用代码
     */
	@ApiModelProperty("申报单位社会信用代码")
	private  String agentCodeScc;
	/**
     * 申报单位（10位海关代码）
     */
	@NotNull(message="{申报单位（10位海关代码）不能为空！}")
	@ApiModelProperty("申报单位（10位海关代码）")
	private  String agentCode;
	/**
     * 申报单位名称
     */
	@NotNull(message="{申报单位名称不能为空！}")
	@ApiModelProperty("申报单位名称")
	private  String agentName;
	/**
     * 随附单证
     */
	@ApiModelProperty("随附单证")
	private  String certMark;
	/**
     * 备案清单类型
     */
	@ApiModelProperty("备案清单类型")
	private  String billType;
	/**
     * 提运单号码
     */
	@ApiModelProperty("提运单号码")
	private  String billNo;
	/**
     * 保税仓库或者监管仓库编号
     */
	@ApiModelProperty("保税仓库或者监管仓库编号")
	private  String bondedNo;
	/**
     * 境内收发货人编码
     */
	@ApiModelProperty("境内收发货人编码")
	private  String tradeCode;
	/**
     * 境内收发货人社会信用代码
     */
	@ApiModelProperty("境内收发货人社会信用代码")
	private  String tradeCodeScc;
	/**
     * 境内收发货人名称
     */
	@ApiModelProperty("境内收发货人名称")
	private  String tradeName;
	/**
     * 境外收发货人编码
     */
	@ApiModelProperty("境外收发货人编码")
	private  String overseasTradeCode;
	/**
     * 境外收发货人-企业名称
     */
	@ApiModelProperty("境外收发货人-企业名称")
	private  String overseasTradeName;
	/**
     * 集装箱号
     */
	@ApiModelProperty("集装箱号")
	private  String containerNo;
	/**
     * 合同协议号
     */
	@ApiModelProperty("合同协议号")
	private  String contrNo;
	/**
     * 关联理由【关联报检号的关联理由】
     */
	@ApiModelProperty("关联理由【关联报检号的关联理由】")
	private  String correlationReasonFlag;
	/**
     * 报关单申报状态编码
     */
	@ApiModelProperty("报关单申报状态编码")
	private  String chkStatus;
	/**
     * 码头/货场代码（为物流监控备用）
     */
	@ApiModelProperty("码头/货场代码（为物流监控备用）")
	private  String customsField;
	/**
     * 征免性质编码
     */
	@ApiModelProperty("征免性质编码")
	private  String cutMode;
	/**
     * 发货日期
     */
	@ApiModelProperty("发货日期")
	private  Date despDate;
	/**
    * 发货日期-开始
    */
	@ApiModelProperty("发货日期-开始")
	private String despDateFrom;
	/**
    * 发货日期-结束
    */
	@ApiModelProperty("发货日期-结束")
    private String despDateTo;
	/**
     *  启运港(进口)/离境口岸(出口)
     */
	@XdoSize(max = 6, message = "{ 启运港(进口)/离境口岸(出口)长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty(" 启运港(进口)/离境口岸(出口)")
	private  String despPortCode;
	/**
     * 指运港/经停港(名称）
     */
	@ApiModelProperty("指运港/经停港(名称）")
	private  String distinatePort;
	/**
     * 申报日期
     */
	@NotNull(message="{申报日期不能为空！}")
	@ApiModelProperty("申报日期")
	@JsonProperty("dDate")
	private  Date dDate;
	/**
    * 申报日期-开始
    */
	@ApiModelProperty("申报日期-开始")
	private String dDateFrom;
	/**
    * 申报日期-结束
    */
	@ApiModelProperty("申报日期-结束")
    private String dDateTo;
	/**
     * 海关编号
     */
	@NotEmpty(message="{海关编号不能为空！}")
	@XdoSize(max = 18, message = "{海关编号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("海关编号")
	private  String entryId;
	/**
     * 报关单类型
     */
	@NotEmpty(message="{报关单类型不能为空！}")
	@XdoSize(max = 1, message = "{报关单类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("报关单类型")
	private  String entryType;
	/**
     * 运费币制
     */
	@XdoSize(max = 3, message = "{运费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运费币制")
	private  String feeCurr;
	/**
     * 运费标记
     */
	@XdoSize(max = 1, message = "{运费标记长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运费标记")
	private  String feeMark;
	/**
     * 运费／率
     */
	@Digits(integer = 14, fraction = 5, message = "{运费／率必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("运费／率")
	private  BigDecimal feeRate;
	/**
     * 货物存放地点
     */
	@XdoSize(max = 200, message = "{货物存放地点长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货物存放地点")
	private  String goodsplace;
	/**
     * 毛重
     */
	@Digits(integer = 14, fraction = 5, message = "{毛重必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("毛重")
	private  BigDecimal grossWt;
	/**
     * 口岸检验检疫机关企业编码
     */
	@XdoSize(max = 200, message = "{口岸检验检疫机关企业编码长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("口岸检验检疫机关企业编码")
	private  String insporgCode;
	/**
     * 保险费标记
     */
	@XdoSize(max = 1, message = "{保险费标记长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("保险费标记")
	private  String insurMark;
	/**
     * 保险费／率
     */
	@Digits(integer = 14, fraction = 5, message = "{保险费／率必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("保险费／率")
	private  BigDecimal insurRate;
	/**
     * 进出口标志
     */
	@NotEmpty(message="{进出口标志不能为空！}")
	@XdoSize(max = 1, message = "{进出口标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("进出口标志")
	@JsonProperty("iEMark")
	private  String iEMark;
	/**
     * 进出口岸代码（进境关别（出境关别））
     */
	@NotEmpty(message="{进出口岸代码（进境关别（出境关别））不能为空！}")
	@XdoSize(max = 10, message = "{进出口岸代码（进境关别（出境关别））长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("进出口岸代码（进境关别（出境关别））")
	@JsonProperty("iEPort")
	private  String iEPort;
	/**
     * 进出口日期
     */
	@ApiModelProperty("进出口日期")
	@JsonProperty("iEDate")
	private  Date iEDate;
	/**
    * 进出口日期-开始
    */
	@ApiModelProperty("进出口日期-开始")
	private String iEDateFrom;
	/**
    * 进出口日期-结束
    */
	@ApiModelProperty("进出口日期-结束")
    private String iEDateTo;
	/**
     * 手册号码（备案号）
     */
	@XdoSize(max = 12, message = "{手册号码（备案号）长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("手册号码（备案号）")
	private  String manualNo;
	/**
     * 标记唛码
     */
	@XdoSize(max = 500, message = "{标记唛码长度不能超过500位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("标记唛码")
	private  String markNo;
	/**
     * 申报地海关
     */
	@XdoSize(max = 4, message = "{申报地海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报地海关")
	private  String masterCustoms;
	/**
     * 净重
     */
	@Digits(integer = 14, fraction = 5, message = "{净重必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("净重")
	private  BigDecimal netWt;
	/**
     * 备注
     */
	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 检验检疫受理机关编码
     */
	@XdoSize(max = 200, message = "{检验检疫受理机关编码长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("检验检疫受理机关编码")
	private  String orgCode;
	/**
     * 杂费标记
     */
	@XdoSize(max = 1, message = "{杂费标记长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("杂费标记")
	private  String otherMark;
	/**
     * 杂费币制
     */
	@XdoSize(max = 3, message = "{杂费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("杂费币制")
	private  String otherCurr;
	/**
     * 杂费／率
     */
	@Digits(integer = 14, fraction = 5, message = "{杂费／率必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("杂费／率")
	private  BigDecimal otherRate;
	/**
     * 消费使用单位(10位检验检疫编码)--进口/生产销售单位-10位检验检疫编码--出口
     */
	@XdoSize(max = 200, message = "{消费使用单位(10位检验检疫编码)--进口/生产销售单位-10位检验检疫编码--出口长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("消费使用单位(10位检验检疫编码)--进口/生产销售单位-10位检验检疫编码--出口")
	private  String ownerciqCode;
	/**
     * 货主单位/消费使用单位（18位社会信用编码）--进口 /生产销售单位（18位社会信用编码）--出口
     */
	@XdoSize(max = 200, message = "{货主单位/消费使用单位（18位社会信用编码）--进口 /生产销售单位（18位社会信用编码）--出口长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货主单位/消费使用单位（18位社会信用编码）--进口 /生产销售单位（18位社会信用编码）--出口")
	private  String ownerScc;
	/**
     * 货主单位代码（消费使用单位）
     */
	@XdoSize(max = 10, message = "{货主单位代码（消费使用单位）长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货主单位代码（消费使用单位）")
	private  String ownerCode;
	/**
     * 货主单位名称（消费使用单位）
     */
	@XdoSize(max = 255, message = "{货主单位名称（消费使用单位）长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货主单位名称（消费使用单位）")
	private  String ownerName;
	/**
     * 件数
     */
	@NotNull(message="{件数不能为空！}")
	@Digits(integer = 19, fraction = 0, message = "{件数必须为数字,整数位最大19位,小数最大0位!}")
	@ApiModelProperty("件数")
	private  Integer packNo;
	/**
     * 其他事项确认（特殊关系确认、价格影响确认、与货物有关的特许权使用费支付确认）
     */
	@XdoSize(max = 50, message = "{其他事项确认（特殊关系确认、价格影响确认、与货物有关的特许权使用费支付确认）长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("其他事项确认（特殊关系确认、价格影响确认、与货物有关的特许权使用费支付确认）")
	private  String promiseItems;
	/**
     * 目的地检验检疫机关企业编码
     */
	@XdoSize(max = 200, message = "{目的地检验检疫机关企业编码长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("目的地检验检疫机关企业编码")
	private  String purporgCode;
	/**
     * 关联编号字段（转出的手册、转入、转出的报关单）
     */
	@XdoSize(max = 18, message = "{关联编号字段（转出的手册、转入、转出的报关单）长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联编号字段（转出的手册、转入、转出的报关单）")
	private  String relativeId;
	/**
     * 关联备案号
     */
	@XdoSize(max = 12, message = "{关联备案号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联备案号")
	private  String relManualNo;
	/**
     * 预录入号码
     */
	@NotEmpty(message="{预录入号码不能为空！}")
	@XdoSize(max = 18, message = "{预录入号码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("预录入号码")
	private  String seqNo;
	/**
     * 监管方式
     */
	@XdoSize(max = 4, message = "{监管方式长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("监管方式")
	private  String tradeMode;
	/**
     * 启运国/运抵国
     */
	@NotEmpty(message="{启运国/运抵国不能为空！}")
	@XdoSize(max = 3, message = "{启运国/运抵国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("启运国/运抵国")
	private  String tradeCountry;
	/**
     * 运输工具名称
     */
	@XdoSize(max = 255, message = "{运输工具名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运输工具名称")
	private  String trafName;
	/**
     * 成交方式
     */
	@XdoSize(max = 1, message = "{成交方式长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("成交方式")
	private  String transMode;
	/**
     * 运输工具航次(班)号
     */
	@XdoSize(max = 32, message = "{运输工具航次(班)号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运输工具航次(班)号")
	private  String voyageNo;
	/**
     * 领证机关企业编码
     */
	@XdoSize(max = 10, message = "{领证机关企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("领证机关企业编码")
	private  String vsaorgCode;
	/**
     * 领证机关企业名称
     */
	@ApiModelProperty("领证机关企业名称")
	private  String vsaorgcodeName;
	/**
     * 包装种类
     */
	@XdoSize(max = 32, message = "{包装种类长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("包装种类")
	private  String wrapType;
	/**
     * 货主单位地区代码（境内目的地）
     */
	@XdoSize(max = 5, message = "{货主单位地区代码（境内目的地）长度不能超过5位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货主单位地区代码（境内目的地）")
	private  String districtCode;
	/**
     * 内销比率
     */
	@Digits(integer = 14, fraction = 5, message = "{内销比率必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("内销比率")
	private  BigDecimal inRatio;
	/**
     * 收结汇方式（出口用）
     */
	@XdoSize(max = 1, message = "{收结汇方式（出口用）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收结汇方式（出口用）")
	private  String payWay;
	/**
     * 许可证编号
     */
	@XdoSize(max = 20, message = "{许可证编号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("许可证编号")
	private  String licenseNo;
	/**
     * 结汇证号（批准文号）
     */
	@XdoSize(max = 32, message = "{结汇证号（批准文号）长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("结汇证号（批准文号）")
	private  String apprNo;
	/**
     * 申报口岸代码
     */
	@XdoSize(max = 4, message = "{申报口岸代码长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报口岸代码")
	private  String declPort;
	/**
     * 经营单位性质
     */
	@XdoSize(max = 1, message = "{经营单位性质长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("经营单位性质")
	private  String coOwner;
	/**
     * 纳税义务人识别号
     */
	@XdoSize(max = 32, message = "{纳税义务人识别号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("纳税义务人识别号")
	private  String taxyRgNo;
	/**
     * 监管手续费标志字段
     */
	@XdoSize(max = 1, message = "{监管手续费标志字段长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("监管手续费标志字段")
	private  String mnlJgfFlag;
	/**
     * 监管手续费
     */
	@Digits(integer = 15, fraction = 4, message = "{监管手续费必须为数字,整数位最大15位,小数最大4位!}")
	@ApiModelProperty("监管手续费")
	private  BigDecimal serviceFee;
	/**
     * 监管手续费率
     */
	@Digits(integer = 14, fraction = 5, message = "{监管手续费率必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("监管手续费率")
	private  BigDecimal serviceRate;
	/**
     * 报关员联系方式
     */
	@XdoSize(max = 255, message = "{报关员联系方式长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("报关员联系方式")
	private  String bpNo;
	/**
     * 制单人
     */
	@XdoSize(max = 32, message = "{制单人长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("制单人")
	private  String typistNo;
	/**
     * 录入人
     */
	@XdoSize(max = 50, message = "{录入人长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("录入人")
	private  String inputNo;
	/**
     * 报关单打印日期/时间
     */
	@ApiModelProperty("报关单打印日期/时间")
	@JsonProperty("pDate")
	private  Date pDate;
	/**
    * 报关单打印日期/时间-开始
    */
	@ApiModelProperty("报关单打印日期/时间-开始")
	private String pDateFrom;
	/**
    * 报关单打印日期/时间-结束
    */
	@ApiModelProperty("报关单打印日期/时间-结束")
    private String pDateTo;
	/**
     * 修改次数
     */
	@Digits(integer = 19, fraction = 0, message = "{修改次数必须为数字,整数位最大19位,小数最大0位!}")
	@ApiModelProperty("修改次数")
	private  Integer modiNo;
	/**
     * 运输方式代码
     */
	@XdoSize(max = 1, message = "{运输方式代码长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运输方式代码")
	private  String trafMode;
	/**
     * 申报单位10位检验检疫编码
     */
	@XdoSize(max = 10, message = "{申报单位10位检验检疫编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报单位10位检验检疫编码")
	private  String agentRegCode;
	/**
     * 贸易国别（地区）代码
     */
	@XdoSize(max = 3, message = "{贸易国别（地区）代码长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("贸易国别（地区）代码")
	private  String tradeAreaCode;
	/**
     * 保险费币制 CURR
     */
	@XdoSize(max = 3, message = "{保险费币制 CURR长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("保险费币制 CURR")
	private  String insurCurr;
	/**
     * 插入时间
     */
	@NotNull(message="{插入时间不能为空！}")
	@ApiModelProperty("插入时间")
	private  Date myInsertTime;
	/**
    * 插入时间-开始
    */
	@ApiModelProperty("插入时间-开始")
	private String myInsertTimeFrom;
	/**
    * 插入时间-结束
    */
	@ApiModelProperty("插入时间-结束")
    private String myInsertTimeTo;
	/**
     * 更新时间
     */
	@ApiModelProperty("更新时间")
	private  Date myUpdateTime;
	/**
    * 更新时间-开始
    */
	@ApiModelProperty("更新时间-开始")
	private String myUpdateTimeFrom;
	/**
    * 更新时间-结束
    */
	@ApiModelProperty("更新时间-结束")
    private String myUpdateTimeTo;
	/**
     * 数据来源（0：固定 1：电子口岸 2：爬网 3：组合）
     */
	@NotEmpty(message="{数据来源（0：固定 1：电子口岸 2：爬网 3：组合）不能为空！}")
	@XdoSize(max = 1, message = "{数据来源（0：固定 1：电子口岸 2：爬网 3：组合）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("数据来源（0：固定 1：电子口岸 2：爬网 3：组合）")
	private  String dataSource;
	/**
     * B/L号（检验检疫专用）
     */
	@ApiModelProperty("B/L号（检验检疫专用）")
	private  String ciqBillNo;
	/**
     * 特殊关系确认
     */
	@XdoSize(max = 1, message = "{特殊关系确认长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("特殊关系确认")
	private  String confirmSpecial;
	/**
     * 价格影响确认
     */
	@XdoSize(max = 1, message = "{价格影响确认长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("价格影响确认")
	private  String confirmPrice;
	/**
     * 支付特许权使用费确认
     */
	@XdoSize(max = 1, message = "{支付特许权使用费确认长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("支付特许权使用费确认")
	private  String confirmRoyalties;
	/**
     * 业务事项-自报自缴(0:否、1:是)  （税单无纸化、自主报税、担保验放、自报自缴）
     */
	@XdoSize(max = 1, message = "{业务事项-自报自缴(0:否、1:是)  （税单无纸化、自主报税、担保验放、自报自缴）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("业务事项-自报自缴(0:否、1:是)  （税单无纸化、自主报税、担保验放、自报自缴）")
	private  String cusRemarkAuto;
	/**
     * 业务事项-水运中转(0:否、1:是)  （税单无纸化、自主报税、担保验放、自报自缴）
     */
	@XdoSize(max = 1, message = "{业务事项-水运中转(0:否、1:是)  （税单无纸化、自主报税、担保验放、自报自缴）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("业务事项-水运中转(0:否、1:是)  （税单无纸化、自主报税、担保验放、自报自缴）")
	private  String cusRemarkWater;
	/**
     * 申报方式标志
     */
	@XdoSize(max = 1, message = "{申报方式标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报方式标志")
	private  String ediId;
	/**
     * EDI申报备注
     */
	@XdoSize(max = 32, message = "{EDI申报备注长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("EDI申报备注")
	private  String ediRemark;
	/**
     * EDI申报人标识
     */
	@XdoSize(max = 20, message = "{EDI申报人标识长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("EDI申报人标识")
	private  String partnerId;
	/**
     * 入境口岸(进口专用)
     */
	@XdoSize(max = 10, message = "{入境口岸(进口专用)长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("入境口岸(进口专用)")
	private  String entryPortCode;
	/**
     * 报关人员证号
     */
	@XdoSize(max = 50, message = "{报关人员证号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("报关人员证号")
	private  String dclrNo;
	/**
     * 境内收发货人检验检疫编码
     */
	@XdoSize(max = 50, message = "{境内收发货人检验检疫编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境内收发货人检验检疫编码")
	private  String tradeCiqCode;
	/**
     * 关联号码
     */
	@ApiModelProperty("关联号码")
	private  String correlationDeclno;
}
