package com.dcjet.cs.dto.mat.imp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate dcits
 *
 * @author: cc
 * @date: 2019-07-08
 */
@Setter
@Getter
@ApiModel(value = "备案物料导入验证")
public class MatImgexgOrgImportParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 备案料号
     */
    @NotBlank(message = "{备案料号不能为空}")
    @XdoSize(max = 32, message = "{备案料号长度不能超过32位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("备案料号")
    private String copGNo;
    /**
     * 商品名称
     */
    @NotBlank(message = "{商品名称不能为空}")
    @XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("商品名称")
    private String GName;
    /**
     * 商品编码
     */
    @NotBlank(message = "{商品编码不能为空}")
    @XdoSize(max = 10, message = "{商品编码长度不能超过10位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("商品编码")
    private String codeTS;
    /**
     * 备案序号
     */
    @XdoSize(max = 11, message = "{备案序号长度不能超过11位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("备案序号")
    private String serialNo;
    /**
     * 申报规格型号
     */
    @NotBlank(message = "{申报规格型号不能为空}")
    @XdoSize(max = 255, message = "{申报规格型号长度不能超过255位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("申报规格型号")
    private String GModel;
    /**
     * 申报计量单位
     */
    @NotBlank(message = "{申报计量单位不能为空}")
    @XdoSize(max = 3, message = "{申报计量单位长度不能超过3位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("申报计量单位")
    private String unit;
    /**
     * 申报数量
     */
    @Digits(integer = 11, fraction = 5, message = "{申报数量不能超过总长度16，小数位不能超过5位}")
    @ApiModelProperty("申报数量")
    private BigDecimal qty;
    /**
     * 产销国(地区)
     */
    @XdoSize(max = 3, message = "{产销国(地区)长度不能超过3位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("产销国(地区)")
    private String country;
    /**
     * 币制
     */
    @XdoSize(max = 3, message = "{币制长度不能超过3位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 备案单价
     */
    @Digits(integer = 11, fraction = 5, message = "{备案单价不能超过总长度16,小数位不能超过5位}")
    @ApiModelProperty("备案单价")
    private BigDecimal decPrice;
    /**
     * 备案总价
     */
    @Digits(integer = 11, fraction = 5, message = "{备案总价不能超过总长度16,小数位不能超过5位}")
    @ApiModelProperty("备案总价")
    private BigDecimal decTotal;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 备案有效期
     */
    @ApiModelProperty("备案有效期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date recordDate;
    /**
     * 减免方式
     */
    @ApiModelProperty("减免方式")
    private  String dutyMode;
}

