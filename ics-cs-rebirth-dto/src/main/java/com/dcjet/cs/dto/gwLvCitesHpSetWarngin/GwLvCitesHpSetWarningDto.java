package com.dcjet.cs.dto.gwLvCitesHpSetWarngin;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 7/21/2023
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class GwLvCitesHpSetWarningDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * SID
      */
    @ApiModelProperty("SID")
	private  String sid;
	/**
      * 插入时间
      */
    @ApiModelProperty("插入时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 插入人
      */
    @ApiModelProperty("插入人")
	private  String insertUser;
	/**
      * 修改日期
      */
    @ApiModelProperty("修改日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 修改人
      */
    @ApiModelProperty("修改人")
	private  String updateUser;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 在途预处理生成明细HP预警
      */
    @ApiModelProperty("在途预处理生成明细HP预警")
	private  Integer inTransitTerm;
	/**
      * 保税在库HP预警
      */
    @ApiModelProperty("保税在库HP预警")
	private  Integer bondTerm;
	/**
      * 出证清单模块HP预警
      */
    @ApiModelProperty("出证清单模块HP预警")
	private  Integer toNotarizeBillTerm;
}
