package com.dcjet.cs.dto.imp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class ParamModel<T> implements Serializable {

    /**
     * 手(账)册企业内部编号
     */
    @ApiModelProperty("手(账)册企业内部编号")
    private String copEmsNo;
    /**
     * 手（账）备案号
     */
    @ApiModelProperty("手(账)册企业内部编号")
    private String emsNo;
    /**
     * 导入类型：1：新增导入 2:修改导入 3:删除导入
     */
    @ApiModelProperty("导入类型：1：新增导入 2:修改导入 3:删除导入")
    private String importType;

    /**
     * 用于追踪导入任务，每次请求使用不同值，建议使用GUID
     */
    @ApiModelProperty("用于追踪导入任务，每次请求使用不同值，建议使用GUID")
    private String taskId;

    /**
     * 查询参数
     */
    @ApiModelProperty("查询参数")
    private List<T> data;

}
