package com.dcjet.cs.dto.erp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DecErpListCertificateSearchParam implements Serializable {

    /***
     *
     */
    @ApiModelProperty(value = "提单ID")
    @NotEmpty(message = "{提单ID不能为空}")
    private String headId;

    /***
     *
     */
    @ApiModelProperty(value = "进出口类型")
    @NotEmpty(message = "{进出口类型不能为空}")
    private String iemark;

    @JsonIgnore
    private String tradeCode;
}
