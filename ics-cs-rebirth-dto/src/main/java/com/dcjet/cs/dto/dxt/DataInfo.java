package com.dcjet.cs.dto.dxt;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@Setter @Getter
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "DataInfo")
@XmlType(propOrder = {
        "BusinessData"
})
public class DataInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private com.dcjet.cs.dto.dxt.BusinessData BusinessData;
}
