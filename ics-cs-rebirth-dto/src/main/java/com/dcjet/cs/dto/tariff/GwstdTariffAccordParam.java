package com.dcjet.cs.dto.tariff;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2019-11-26
 */
@Setter @Getter
@ApiModel(value = "进口协定税税率传入参数")
public class GwstdTariffAccordParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 税号
     */
	@NotEmpty(message="{税号不能为空！}")
	@XdoSize(max = 10, message = "{税号长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("税号")
	private  String codeTS;
	/**
     * 协定名称
     */
	@XdoSize(max = 100, message = "{协定名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("协定名称")
	private  String agreementName;
	/**
     * 协定编号
     */
	@XdoSize(max = 20, message = "{协定编号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("协定编号")
	private  String agreementCode;
	/**
     * 进口协定原产国(地区)
     */
	@XdoSize(max = 50, message = "{进口协定原产国(地区)长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("进口协定原产国(地区)")
	private  String countryName;
	/**
     * 进口协定税率
     */
	@Digits(integer = 6, fraction = 0, message = "{进口协定税率必须为数字,整数位最大6位,小数最大0位!}")
	@ApiModelProperty("进口协定税率")
	private  Integer impAgreementRate;
	/**
     * 进口协定原产国code码
     */
	@XdoSize(max = 50, message = "{进口协定原产国code码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("进口协定原产国code码")
	private  String countryCode;
	/**
	 * 商品名称
	 */
	@ApiModelProperty("商品名称")
	private String gName;
	/**
	 * 进口最惠国税率(包含百分比符号及从量信息)
	 */
	@ApiModelProperty("进口最惠国税率(包含百分比符号及从量信息)")
	private String impDiscountRate;
	/**
	 * 进口普通税率(包含百分比符号及从量信息)
	 */
	@ApiModelProperty("进口普通税率(包含百分比符号及从量信息)")
	private String impOrdinaryRate;
	/**
	 * 进口暂定税率(包含百分比符号及从量信息)
	 */
	@ApiModelProperty("进口暂定税率")
	private String impTempRate;
	/**
	 * 进口消费税税率
	 */
	@ApiModelProperty("进口消费税税率")
	private String impConsumeRate;
	/**
	 * 增值税税率
	 */
	@ApiModelProperty("增值税税率")
	private String addTaxRate;
	/**
	 * 进口废弃电器电子基金
	 */
	@ApiModelProperty("进口废弃电器电子基金")
	private String impFundRate;
	/**
	 * 保障措施关税税率
	 */
	@ApiModelProperty("保障措施关税税率")
	private String impSafeguardRate;
	/**
	 * 出口关税税率
	 */
	@ApiModelProperty("出口关税税率")
	private String expTaxRate;
	/**
	 * 出口暂定税率
	 */
	@ApiModelProperty("出口暂定税率")
	private String expTempRate;
}
