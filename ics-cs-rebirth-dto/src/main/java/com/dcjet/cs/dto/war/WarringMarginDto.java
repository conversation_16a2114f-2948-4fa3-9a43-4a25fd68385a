package com.dcjet.cs.dto.war;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 * @date: 2019-4-19
 */
@ApiModel(value = "手册余量预警返回信息")
@Setter @Getter
public class WarringMarginDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
	 * 状态
	 */
	@ApiModelProperty("STATUS")
	private String status;
	/**
	 * 状态名称
	 */
	@ApiModelProperty("STATUS_NAME")
	private String statusName;
	/**
	 * 物料类型
	 */
	@ApiModelProperty("G_MARK")
	private String GMark;

	/**
	 * 物料类型
	 */
	@ApiModelProperty("G_MARK_NAME")
	@JsonProperty("gMarkName")
	private String gMarkName;
	/**
	 * 备案号
	 */
	@ApiModelProperty("EMS_NO")
	private String emsNo;
	/**
	 * 备案序号
	 */
	@ApiModelProperty("SERIAL_NO")
	private BigDecimal serialNo;
	/**
	 * 备案名称
	 */
	@ApiModelProperty("G_NAME")
	private String GName;
	/**
	 * 商品编码
	 */
	@ApiModelProperty("CODE_T_S")
	private String codeTS;
	/**
	 * 规格型号
	 */
	@ApiModelProperty("G_MODEL")
	private String GModel;
	/**
	 * 单价
	 */
	@ApiModelProperty("DEC_PRICE")
	private BigDecimal decPrice;
	/**
	 * 币值
	 */
	@ApiModelProperty("CURR")
	private String curr;
	/**
	 * 数量
	 */
	@ApiModelProperty("QTY")
	private BigDecimal qty;
	/**
	 * 美元数量
	 */
	@ApiModelProperty("USED_QTY")
	private BigDecimal usedQty;
	/**
	 * 余量
	 */
	@ApiModelProperty("MARGIN_QTY")
	private BigDecimal marginQty;
	/**
	 * 余量百分比
	 */
	@ApiModelProperty("MARGIN_QTY_RATE")
	private BigDecimal marginQtyRate;
	/**
	 * 企业代码
	 */
	@ApiModelProperty("TRADE_CODE")
	private String tradeCode;
	/**
	 * 企业代码
	 */
	@ApiModelProperty("COP_G_NO")
	private String copGNo;


}
