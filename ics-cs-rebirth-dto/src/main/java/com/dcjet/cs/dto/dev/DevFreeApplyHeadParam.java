package com.dcjet.cs.dto.dev;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2020-5-13
 */
@Setter
@Getter
@ApiModel(value = "免税申请表表头传入参数")
public class DevFreeApplyHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 企业内部编号
     */
    @XdoSize(max = 32, message = "{企业内部编号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业内部编号")
    private String emsListNo;
    /**
     * 业务种类
     */
    @XdoSize(max = 50, message = "{业务种类长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("业务种类")
    private String billType;
    /**
     * 业务种类名称
     */
    @XdoSize(max = 100, message = "{业务种类名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("业务种类名称")
    private String billTypeName;
    /**
     * 企业代码
     */
    @XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 企业名称
     */
    @XdoSize(max = 70, message = "{企业名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业名称")
    private String tradeName;
    /**
     * 审批依据
     */
    @XdoSize(max = 50, message = "{审批依据长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审批依据")
    private String apprFrom;
    /**
     * 审批依据名称
     */
    @NotEmpty(message = "{审批依据不能为空！}")
    @XdoSize(max = 100, message = "{审批依据名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审批依据名称")
    private String apprFromName;
    /**
     * 进出口标记，I进口 E出口
     */
    @NotEmpty(message = "{进出口类型不能为空！}")
    @XdoSize(max = 4, message = "{进出口类型长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("进出口标记，I进口 E出口")
    private String IEMark;
    /**
     * 征免性质
     */
    @NotEmpty(message = "{征免性质不能为空！}")
    @XdoSize(max = 3, message = "{征免性质长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("征免性质")
    private String cutMode;
    /**
     * 项目确认书编号
     */
    //@NotEmpty(message = "{项目确认书编号不能为空！}")
    @XdoSize(max = 50, message = "{项目确认书编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("项目确认书编号")
    private String itemNo;
    /**
     * 产业政策审批条目
     */
    @XdoSize(max = 50, message = "{产业政策审批条目长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("产业政策审批条目")
    private String apprItem;
    /**
     * 产业政策审批条目名称
     */
    //@NotEmpty(message = "{产业政策审批条目不能为空！}")
    @XdoSize(max = 100, message = "{产业政策审批条目名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("产业政策审批条目名称")
    private String apprItemName;
    /**
     * 审批部门
     */
    @XdoSize(max = 50, message = "{审批部门长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审批部门")
    private String apprDept;
    /**
     * 审批部门名称
     */
    //@NotEmpty(message = "{审批部门不能为空！}")
    @XdoSize(max = 100, message = "{审批部门名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审批部门名称")
    private String apprDeptName;
    /**
     * 许可证号
     */
    @XdoSize(max = 50, message = "{许可证号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("许可证号")
    private String licenseNo;
    /**
     * 备案号
     */
    @XdoSize(max = 20, message = "{备案号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备案号")
    private String emsNo;
    /**
     * 合同协议号
     */
    @XdoSize(max = 30, message = "{合同协议号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("合同协议号")
    private String contrNo;
    /**
     * 成交方式
     */
    @XdoSize(max = 6, message = "{成交方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("成交方式")
    private String transMode;
    /**
     * 项目性质
     */
    @XdoSize(max = 20, message = "{项目性质长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("项目性质")
    private String itemMode;
    /**
     * 入境口岸/出境口岸
     */
    @XdoSize(max = 6, message = "{入境口岸/出境口岸长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("入境口岸/出境口岸")
    private String entryPort;
    /**
     * 是否申报进口，0 否 1 是
     */
    @NotEmpty(message = "{是否申报进口不能为空！}")
    @XdoSize(max = 1, message = "{是否申报进口长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("是否申报进口，0 否 1 是")
    private String isDeclare;
    /**
     * 免表编号
     */
    @XdoSize(max = 12, message = "{免表编号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("免表编号")
    private String exemptsNo;
    /**
     * 有效日期
     */
    @ApiModelProperty("有效日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date validDate;
    /**
     * 有效日期-开始
     */
    @ApiModelProperty("有效日期-开始")
    private String validDateFrom;
    /**
     * 有效日期-结束
     */
    @ApiModelProperty("有效日期-结束")
    private String validDateTo;
    /**
     * 主管海关
     */
    @XdoSize(max = 4, message = "{主管海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("主管海关")
    private String masterCustoms;
    /**
     * 申报地海关
     */
    @XdoSize(max = 4, message = "{申报地海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报地海关")
    private String declareCustoms;
    /**
     * 联系人
     */
    @XdoSize(max = 20, message = "{联系人长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系人")
    private String linkMan;
    /**
     * 联系电话
     */
    @XdoSize(max = 11, message = "{联系电话长度不能超过11位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系电话")
    private String linkManTel;
    /**
     * 税款担保原因
     */
    @XdoSize(max = 100, message = "{税款担保原因长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("税款担保原因")
    private String taxAssureReason;
    /**
     * 供应商编码
     */
    @XdoSize(max = 50, message = "{供应商编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @XdoSize(max = 100, message = "{供应商名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 申报单位编码
     */
    @XdoSize(max = 10, message = "{申报单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位编码")
    private String declareCode;
    /**
     * 申报单位名称
     */
    @XdoSize(max = 100, message = "{申报单位名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位名称")
    private String declareName;
    /**
     * 是否有效，0 否 1是
     */
    @NotEmpty(message = "{是否有效不能为空！}")
    @XdoSize(max = 1, message = "{是否有效长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("是否有效，0 否 1是")
    private String isEnabled;
    /**
     * 报关单号
     */
    @XdoSize(max = 18, message = "{报关单号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关单号")
    private String entryNo;
    /**
     * 备注
     */
    @XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String note;
    /**
     * 状态，0 暂存 1 报关生成 2 报关完成
     */
    @XdoSize(max = 10, message = "{状态长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("状态，0 暂存 1 报关生成 2 报关完成")
    private String status;
    /**
     * 制单人姓名
     */
    @XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
    /**
     * 提单内部编号
     */
    @XdoSize(max = 50, message = "{提单内部编号不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("提单内部编号")
    private String erpEmsListNo;
    /**
     * 提单模板ID
     */
    @XdoSize(max = 50, message = "{提单模板ID不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("提单模板ID")
    private String templateId;
    /**
     * 担保编号
     */
    @XdoSize(max = 30, message = "{担保编号不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("担保编号")
    private String assureNo;

    @ApiModelProperty("暂存单编号")
    @XdoSize(max = 18, message = "{暂存单编号不能超过18位字节长度(一个汉字2位字节长度)!}")
    private String tmpNo;
    @ApiModelProperty("中心统一编号")
    @XdoSize(max = 18, message = "{中心统一编号不能超过18位字节长度(一个汉字2位字节长度)!}")
    private String seqNo;
    @ApiModelProperty("免表类型")
    @XdoSize(max = 1, message = "{免表类型不能超过1位字节长度(一个汉字2位字节长度)!}")
    private String mType;
    @ApiModelProperty("项目统一编号")
    @XdoSize(max = 18, message = "{项目统一编号不能超过18位字节长度(一个汉字2位字节长度)!}")
    private String projectId;
    @ApiModelProperty("申请人统一社会信用代码")
    @XdoSize(max = 18, message = "{申请人统一社会信用代码不能超过18位字节长度(一个汉字2位字节长度)!}")
    private String applyCoScc;
    private String applyCode;
    private String applyName;
    @ApiModelProperty("收发货人统一社会信用代码")
    @XdoSize(max = 18, message = "{收发货人统一社会信用代码不能超过18位字节长度(一个汉字2位字节长度)!}")
    private String comCoScc;
    private String comCode;
    private String comName;
    @ApiModelProperty("状况报告书递交标志")
    @XdoSize(max = 1, message = "{状况报告书递交标志不能超过1位字节长度(一个汉字2位字节长度)!}")
    private String hasSpecFile;
    @ApiModelProperty("手机")
    @XdoSize(max = 20, message = "{手机不能超过20位字节长度(一个汉字2位字节长度)!}")
    private String contactCellphone;
    @ApiModelProperty("业务类型")
    @XdoSize(max = 20, message = "{业务类型不能超过20位字节长度(一个汉字2位字节长度)!}")
    private String businessType;
    @ApiModelProperty("申请形式")
    @XdoSize(max = 1, message = "{申请形式不能超过1位字节长度(一个汉字2位字节长度)!}")
    private String applType;
    @ApiModelProperty("受托单位统一社会信用代码")
    @XdoSize(max = 18, message = "{受托单位统一社会信用代码不能超过18位字节长度(一个汉字2位字节长度)!}")
    private String entrustedCoScc;
    @ApiModelProperty("项目资金性质")
    @XdoSize(max = 1, message = "{项目资金性质不能超过1位字节长度(一个汉字2位字节长度)!}")
    private String projectFund;
    @ApiModelProperty("回执状态")
    @XdoSize(max = 5, message = "{回执状态不能超过5位字节长度(一个汉字2位字节长度)!}")
    private String receiptStatus;

    /**
     * 发送时间-开始
     */
    @ApiModelProperty("发送时间-开始")
    private String declareDateFrom;
    /**
     * 发送时间-结束
     */
    @ApiModelProperty("发送时间-结束")
    private String declareDateTo;


    /**
     * 审核时间-开始
     */
    @ApiModelProperty("审核时间-开始")
    private String apprDateFrom;
    /**
     * 审核时间-结束
     */
    @ApiModelProperty("审核时间-结束")
    private String apprDateTo;
    /**
     * 内审员
     */
    @XdoSize(max = 50, message = "{内审员长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("内审员")
    private String apprUserParam;
    /**
     * 审核状态
     */
    @XdoSize(max = 3, message = "{审核状态长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审核状态")
    private String apprStatus;
    /**
     * 申请人所在地
     */
    @XdoSize(max = 100, message = "{申请人所在地长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申请人所在地")
    private String applyAddress;
    /**
     * 企业种类
     */
    @ApiModelProperty("企业种类")
    private String enterpriseType;
    /**
     * 使用地点
     */
    @ApiModelProperty("使用地点")
    @XdoSize(max = 100, message = "{使用地点长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    private String useAddress;
    /**
     * 报关状态
     */
    @ApiModelProperty("报关状态")
    private String entryStatus;
    /**
     * 报关单统一编号
     */
    @ApiModelProperty("报关单统一编号")
    private String entrySeqNo;
    /**
     * 概要发送日期
     */
    @ApiModelProperty("概要发送日期")
    private Date entryDeclareDate;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 运输工具名称
     */
    @ApiModelProperty("运输工具名称")
    private String trafName;
    /**
     * 航次号
     */
    @ApiModelProperty("航次号")
    private String voyageNo;
    /**
     * 提运单号
     */
    @ApiModelProperty("提运单号")
    private String hwab;
    /**
     * 监管方式
     */
    @ApiModelProperty("监管方式")
    private String tradeMode;
    /**
     * 毛重
     */
    @ApiModelProperty("毛重")
    private BigDecimal grossWt;
    /**
     * 是否涉证
     */
    @ApiModelProperty("是否涉证")
    private String cardMark;
    /**
     * 是否涉检
     */
    @ApiModelProperty("是否涉检")
    private String checkMark;
    /**
     * 是否涉税
     */
    @ApiModelProperty("是否涉税")
    private String taxMark;
    /**
     * 报关单申报地海关
     */
    @ApiModelProperty("报关单申报地海关")
    private String entryCustoms;
    /**
     * 是否申报完成 0 未申报 1 概要申报失败 2 概要申报成功 3 完整申报成功
     */
    @ApiModelProperty("是否申报完成 0 未申报 1 概要申报失败 2 概要申报成功 3 完整申报成功")
    private String isDeclareSuccess;
    /**
     * 报关单申报单位代码
     */
    @ApiModelProperty("报关单申报单位代码")
    private String entryDeclareCode;
    /**
     * 报关单申报单位名称
     */
    @ApiModelProperty("报关单申报单位名称")
    private String entryDeclareName;
    /**
     * 报关单申报单位社会信用代码
     */
    @ApiModelProperty("报关单申报单位社会信用代码")
    private String entryDeclareScc;

    /**
     * 录入日期-开始
     */
    @ApiModelProperty("录入日期-开始")
    private String insertTimeFrom;
    /**
     * 录入日期-结束
     */
    @ApiModelProperty("录入日期-结束")
    private String insertTimeTo;
    /**
     * 目的地海关
     */
    @ApiModelProperty("目的地海关")
    private String destinationCustoms;
}
