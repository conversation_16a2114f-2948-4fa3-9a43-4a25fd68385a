package com.dcjet.cs.dto.erp.sap;


import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date: 2019-6-19
 */
@Setter
@Getter
@ApiModel(description= "物料传入参数")
public class ErpMatImgexgParam {
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private  String sid;

    @ApiModelProperty("保完税标志")
    private String bondMark;

    @ApiModelProperty("转换后报完税标记")
    private String bondMarkConvert;

    @ApiModelProperty("提取状态（0 未提取 1已提取）")
    private String status;

    @ApiModelProperty("数据状态(1：修改，2：删除，3：新增)")
    private Integer modifyMark;

    @ApiModelProperty("企业料号")
    private String facGNo;

    @ApiModelProperty(value = "备案料号")
    private String copGNo;

    @ApiModelProperty("物料类型")
    private String GMark;

    @ApiModelProperty("转换后物料类型")
    private String GMarkConvert;

    @XdoSize(max = 100, message = "{中文名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("中文名称")
    private String copGName;

    @ApiModelProperty(value = "英文名称")
    @XdoSize(max = 255, message = "{英文名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    private String copGNameEn;

    @XdoSize(max = 60, message = "{传输批次号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("传输批次号")
    private String tempOwner;

    @ApiModelProperty("接收时间-起 yyyy-MM-dd")
    private String insertTimeFrom;

    @ApiModelProperty("接收时间-止 yyyy-MM-dd")
    private String insertTimeTo;

    @ApiModelProperty("供应商/客户名称")
    private String supplierName;

    @ApiModelProperty("供应商/客户代码")
    private String supplierCode;

    @ApiModelProperty("单价")
    private BigDecimal decPrice;

    @ApiModelProperty("净重")
    private BigDecimal netWt;

    @ApiModelProperty("转换后申报计量单位")
    private String unitConvert;

    @ApiModelProperty(value = "ERP计量单位")
    private String unitErp;

    @ApiModelProperty(value = "转换后ERP计量单位")
    private String unitErpConvert;

    @ApiModelProperty(value = "转换后币制")
    private String currConvert;

    private String tradeCode;
    @ApiModelProperty(value = "平方米")
    private BigDecimal itemArea;
    @ApiModelProperty(value = "净重数量")
    private BigDecimal qtyWt;

    @ApiModelProperty("备案号")
    private String emsNo;
}
