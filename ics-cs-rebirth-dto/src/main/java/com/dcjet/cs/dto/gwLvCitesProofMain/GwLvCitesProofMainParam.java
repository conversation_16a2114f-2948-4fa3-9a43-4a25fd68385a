package com.dcjet.cs.dto.gwLvCitesProofMain;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 * <AUTHOR>
 * @date: 2022-6-20
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class GwLvCitesProofMainParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 企业编码
     */
	@XdoSize(max = 10, message = "企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;

	private String trMark;

	/**
     * 单据状态：0-暂存  1-证明办结  2-关联SID  3-报关完成
     */
	@ApiModelProperty("单据状态：0-暂存  1-证明办结  2-关联SID  3-报关完成")
	private  String status;
	/**
     * 品牌
     */
	@NotEmpty(message="品牌不能为空！")
	@ApiModelProperty("品牌")
	private  String brandCode;
	/**
     * 进口证明流水号
     */
	@NotEmpty(message="进口证明流水号不能为空！")
	@XdoSize(max = 30, message = "进口证明流水号长度不能超过30位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("进口证明流水号")
	private  String proofNo;
	/**
     * 允许进口证明号
     */
	@XdoSize(max = 18, message = "允许进口证明号长度不能超过18位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("允许进口证明号")
	private  String importProofNo;
	/**
     * 证明办结日期
     */
	@ApiModelProperty("证明办结日期")
	private  Date proofPunishDate;
	/**
     * Shipment ID
     */
	@XdoSize(max = 100, message = "Shipment ID长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("Shipment ID")
	private  String shipmentId;
	/**
     * 关联日期
     */
	@ApiModelProperty("关联日期")
	private  Date linkeDate;
	/**
     * 报关日期
     */
	@ApiModelProperty("报关日期")
	private  Date cnApprovalDate;
	/**
     * 报关单号
     */
	@XdoSize(max = 18, message = "报关单号长度不能超过18位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("报关单号")
	private  String entryNo;

	private String batchFileNo;

	private String outSideNo;

	private String isQuota;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date outSideProofValid;
	
	private String batchFileSerial;

	
}
