package com.dcjet.cs.dto.entCompare;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2020-10-14
 */
@ApiModel(value = "报关复核结果返回信息")
@Setter
@Getter
public class EntryCompareResDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private String sid;
    /**
     * 统一编号
     */
    @ApiModelProperty("统一编号")
    private String seqNo;
    /**
     * 对比标记（1-比对成功 2-比对异常 3-关务缺失）
     */
    @ApiModelProperty("对比标记（1-比对成功 2-比对异常 3-关务缺失）")
    private String entFlag;
    /**
     * 单据内部编号
     */
    @ApiModelProperty("单据内部编号")
    private String emsListNo;
    /**
     * 进出口标记
     */
    @ApiModelProperty("进出口标记")
    @JsonProperty("iEMark")
    private String iEMark;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateUser;
    /**
     * 报关申报日期
     */
    @ApiModelProperty("报关申报日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date declareDate;
    /**
     * 报关单号
     */
    @ApiModelProperty("报关单号")
    private String entryNo;
    /**
     * 监管方式
     */
    @ApiModelProperty("监管方式")
    private String tradeMode;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 进出口标记
     */
    @ApiModelProperty("进出口标记")
    private String IEMark;
    /**
     * 申报单位
     */
    @ApiModelProperty("申报单位")
    private String agentCode;
}
