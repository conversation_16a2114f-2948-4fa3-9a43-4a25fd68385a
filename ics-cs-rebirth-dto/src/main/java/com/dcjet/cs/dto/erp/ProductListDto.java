package com.dcjet.cs.dto.erp;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2019-8-26
 */
@ApiModel(value = "ProductListDto返回信息")
@Setter @Getter
public class ProductListDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private  String sid;
	/**
      * 单证编号
      */
    @ApiModelProperty("单证编号")
	private  String billNo;
	/**
      * 序号
      */
    @ApiModelProperty("序号")
	private  Integer lineNo;
	/**
      * 库位标识
      */
    @ApiModelProperty("库位标识")
	private  String warehouseNo;
	/**
      * 出入库类型，0：料件入库，1：料件出库
      */
    @ApiModelProperty("出入库类型，0：料件入库，1：料件出库")
	private  String ieType;
	/**
      * 关联编号
      */
    @ApiModelProperty("关联编号")
	private  String linkedNo;
	/**
      * SO号
      */
    @ApiModelProperty("SO号")
	private  String soNo;
	/**
      * 发票号
      */
    @ApiModelProperty("发票号")
	private  String invoiceNo;
	/**
      * 交易日期
      */
    @ApiModelProperty("交易日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date deliveryDate;
	/**
      * 企业料号
      */
    @ApiModelProperty("企业料号")
	private  String facGNo;
	/**
      * 客户料号
      */
    @ApiModelProperty("客户料号")
	private  String customerGNo;
	/**
      * 数量
      */
    @ApiModelProperty("数量")
	private  BigDecimal qty;
	/**
      * 交易单位
      */
    @ApiModelProperty("交易单位")
	private  String unit;
	/**
      * 单价
      */
    @ApiModelProperty("单价")
	private  BigDecimal decPrice;
	/**
      * 总价
      */
    @ApiModelProperty("总价")
	private  BigDecimal decTotal;
	/**
      * 币制
      */
    @ApiModelProperty("币制")
	private  String curr;
	/**
      * 保完税标记
      */
    @ApiModelProperty("保完税标记")
	private  String bondMark;
	/**
      * 客户代码
      */
    @ApiModelProperty("客户代码")
	private  String customerCode;
	/**
      * 买方地址
      */
    @ApiModelProperty("买方地址")
	private  String buyAddress;
	/**
      * 进口方式
      */
    @ApiModelProperty("进口方式")
	private  String outWay;
	/**
      * 单耗版本号
      */
    @ApiModelProperty("单耗版本号")
	private  String bomVersion;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 用户/导入批次号
      */
    @ApiModelProperty("用户/导入批次号")
	private  String tempOwner;
	/**
      * 数据标识 0 正常 1 错误
      */
    @ApiModelProperty("数据标识 0 正常 1 错误")
	private  Integer tempFlag;
	/**
      * 错误说明
      */
    @ApiModelProperty("错误说明")
	private  String tempRemark;
	/**
      * 数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）
      */
    @ApiModelProperty("数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）")
	private  Integer modifyMark;
	/**
      * ERP最后变更日期
      */
    @ApiModelProperty("ERP最后变更日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date lastModifyDate;

	/**
      * 数据来源 0 手工录入 1ERP接口 2导入
      */
    @ApiModelProperty("数据来源 0 手工录入 1ERP接口 2导入")
	private  String dataSource;
	/**
      * 数据状态 0 正常，1 已提取
      */
    @ApiModelProperty("数据状态 0 正常，1 已提取")
	private  String status;
	/**
      * 流水号
      */
    @ApiModelProperty("流水号")
	private  Integer serialNo;
	/**
      * 物料类型
      */
    @ApiModelProperty("物料类型")
	private  String GMark;

	/**
	 * 仓库编号
	 */
	@ApiModelProperty("仓库编号")
	private String warehouse;

	/**
	 * 工厂代码
	 */
	@ApiModelProperty("工厂代码")
	private String factory;
}
