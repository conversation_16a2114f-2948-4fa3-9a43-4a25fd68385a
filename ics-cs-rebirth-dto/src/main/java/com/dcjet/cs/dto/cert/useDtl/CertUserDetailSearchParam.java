package com.dcjet.cs.dto.cert.useDtl;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "使用详情查询参数")
@Setter @Getter
public class CertUserDetailSearchParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单据内部编号")
    private String emsListNo;

    @ApiModelProperty(value = "清单内部编号")
    private String emsListNoBill;

    @ApiModelProperty(value = "企业料号")
    private String facGNo;

    @ApiModelProperty(value="商品编码")
    private String codeTS;

    @ApiModelProperty(value = "商品名称")
    private String gname;

    @ApiModelProperty(value="制单日期起")
    private Date insertTimeFrom;

    @ApiModelProperty(value="制单日期止")
    private Date insertTimeTo;

    @ApiModelProperty(value="证书台账ID")
    private String certificateId;

}
