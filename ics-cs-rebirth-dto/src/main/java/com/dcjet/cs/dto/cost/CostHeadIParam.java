package com.dcjet.cs.dto.cost;

import com.dcjet.cs.dto.base.BasicParam;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 费用明细表头
 * <p>
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2020-02-27
 */
@ApiModel(value = "费用明细表头 新增修改参数")
@Setter
@Getter
public class CostHeadIParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;
    ;


    /**
     * 付款日期
     */
    @ApiModelProperty(value = "付款日期")
    private Date payDate;

    /**
     * 付汇单号
     */
    @ApiModelProperty(value = "账单编号")
    @XdoSize(max = 30, message = "{账单编号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    private String payNo;

    /**
     * 请款日期
     */
    @ApiModelProperty(value = "请款日期")
    private Date applyDate;

    /**
     * 请款批号
     */
    @ApiModelProperty(value = "请款批号")
    @XdoSize(max = 30, message = "{请款批号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    private String applyNo;


    /**
     * 确认1
     */
    @ApiModelProperty(value = "确认1")
    @XdoSize(max = 60, message = "{确认1长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    private String confirm1;

    /**
     * 确认2
     */
    @ApiModelProperty(value = "确认2")
    @XdoSize(max = 60, message = "{确认2长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    private String confirm2;


    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    @XdoSize(max = 50, message = "{发票号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String invoiceNo;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    private String note;


    /**
     * 收款单位编码
     */
    @ApiModelProperty(value = "收款单位编码")
    @XdoSize(max = 100, message = "{收款单位编码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    private String remitteeCode;

    /**
     * 收款单位名称
     */
    @ApiModelProperty(value = "收款单位名称")
    @XdoSize(max = 200, message = "{收款单位名称长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    private String remitteeName;

    /**
     * 清单SID
     */
    @ApiModelProperty(value="清单SID")
    @NotEmpty(message = "{清单SID不能空}")
    private String billHeadId;

    /**
     * 收款单位类型
     */
    @ApiModelProperty(value = "收款单位类型")
    private String remitteeType;

    /**
     * 账单提单号
     */
    @XdoSize(max = 50, message = "{账单提单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("账单提单号")
    private String billDeliveryId;
    /**
     * 账单总件数
     */
    @Digits(integer = 11, fraction = 0, message = "{账单总件数必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("账单总件数")
    private BigDecimal billPackNum;
    /**
     * 账单总体积
     */
    @Digits(integer = 14, fraction = 5, message = "{账单总体积必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("账单总体积")
    private BigDecimal billVolume;
    /**
     * 账单总进口金额
     */
    @Digits(integer = 20, fraction = 5, message = "{账单总进口金额必须为数字,整数位最大20位,小数最大5位!}")
    @ApiModelProperty("账单总进口金额")
    private BigDecimal billSumDecTotal;
    /**
     * 账单总毛重
     */
    @Digits(integer = 14, fraction = 5, message = "{账单总毛重必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("账单总毛重")
    private BigDecimal billGrossWt;


}
