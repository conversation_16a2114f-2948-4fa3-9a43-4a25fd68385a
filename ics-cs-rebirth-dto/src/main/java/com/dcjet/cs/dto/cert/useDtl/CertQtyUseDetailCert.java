package com.dcjet.cs.dto.cert.useDtl;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "证书使用详情-数量")
@Setter @Getter
public class CertQtyUseDetailCert extends CertTimesUseDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业料号")
    private String facGNo;

    @ApiModelProperty(value = "备案料号")
    private String copGNo;

    @ApiModelProperty(value = "物料类型标志")
    private String gmark;

    @ApiModelProperty(value = "备案号")
    private String emsNo;

    @ApiModelProperty(value = "商品名称")
    private String gname;

    @ApiModelProperty(value = "商品编码")
    private String codeTS;

    @ApiModelProperty(value = "已用数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "单价")
    private BigDecimal decPrice;

    @ApiModelProperty(value = "总价")
    private BigDecimal decTotal;

    @ApiModelProperty(value = "币制")
    private String curr;

    @ApiModelProperty(value = "表体可用数量")
    private BigDecimal listTotalQty;

    @ApiModelProperty(value = "表体剩余数量")
    private BigDecimal listRemainQty;


}
