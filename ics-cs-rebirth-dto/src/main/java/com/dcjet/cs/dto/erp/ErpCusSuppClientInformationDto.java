package com.dcjet.cs.dto.erp;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2019-10-15
 */
@ApiModel(value = "ErpCusSuppClientInformationDto返回信息")
@Setter @Getter
public class ErpCusSuppClientInformationDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)
      */
    @ApiModelProperty("客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)")
	private  String customerType;
	/**
      * 客户代码
      */
    @ApiModelProperty("客户代码")
	private  String customerCode;
	/**
      * 客户中文名称
      */
    @ApiModelProperty("客户中文名称")
	private  String companyName;
	/**
      * 海关信用等级
      */
    @ApiModelProperty("海关信用等级")
	private  String customsCreditRating;
	/**
      * 商检代码
      */
    @ApiModelProperty("商检代码")
	private  String inspectionCode;
	/**
      * 社会信用代码
      */
    @ApiModelProperty("社会信用代码")
	private  String creditCode;
	/**
      * 海关注册编码
      */
    @ApiModelProperty("海关注册编码")
	private  String declareCode;
	/**
      * 企业名称缩写
      */
    @ApiModelProperty("企业名称缩写")
	private  String companyNameShort;
	/**
      * 客户电话
      */
    @ApiModelProperty("客户电话")
	private  String telephoneNo;
	/**
      * 客户联系人
      */
    @ApiModelProperty("客户联系人")
	private  String linkmanName;
	/**
      * 联系人职务
      */
    @ApiModelProperty("联系人职务")
	private  String linkmanDuty;
	/**
      * 联系人电话
      */
    @ApiModelProperty("联系人电话")
	private  String mobilePhone;
	/**
      * 联系人邮箱
      */
    @ApiModelProperty("联系人邮箱")
	@JsonProperty("eMail")
	private  String eMail;
	/**
      * 企业中文地址
      */
    @ApiModelProperty("企业中文地址")
	private  String address;
	/**
      * 企业英文名称
      */
    @ApiModelProperty("企业英文名称")
	private  String companyNameEn;
	/**
      * 英文国家
      */
    @ApiModelProperty("英文国家")
	private  String countryEn;
	/**
      * 英文地区
      */
    @ApiModelProperty("英文地区")
	private  String areaEn;
	/**
      * 英文城市
      */
    @ApiModelProperty("英文城市")
	private  String cityEn;
	/**
      * 英文地址
      */
    @ApiModelProperty("英文地址")
	private  String addressEn;
	/**
      * 客户电话(英文)
      */
    @ApiModelProperty("客户电话(英文)")
	private  String telephoneNoEn;
	/**
      * 客户联系人(英文)
      */
    @ApiModelProperty("客户联系人(英文)")
	private  String linkmanNameEn;
	/**
      * 联系人电话(英文)
      */
    @ApiModelProperty("联系人电话(英文)")
	private  String mobilePhoneEn;
	/**
      * 联系人邮箱(英文)
      */
    @ApiModelProperty("联系人邮箱(英文)")
	@JsonProperty("eMailEn")
	private  String eMailEn;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;

//	/**
//      * 所属企业编码
//      */
//    @ApiModelProperty("所属企业编码")
//	private  String tradeCode;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 联系人职务(英文)
      */
    @ApiModelProperty("联系人职务(英文)")
	private  String linkmanDutyEn;
	/**
      * AEO代码
      */
    @ApiModelProperty("AEO代码")
	private  String aeoCode;
	/**
      * 申报地海关
      */
    @ApiModelProperty("申报地海关")
	private  String masterCustoms;
	/**
      * 传真
      */
    @ApiModelProperty("传真")
	private  String fax;
	/**
      * 邮编
      */
    @ApiModelProperty("邮编")
	private  String postal;
	/**
      * 中文国家
      */
    @ApiModelProperty("中文国家")
	private  String country;
	/**
      * 中文地区
      */
    @ApiModelProperty("中文地区")
	private  String area;
	/**
      * 中文城市
      */
    @ApiModelProperty("中文城市")
	private  String city;
	/**
      * 发票中文地址
      */
    @ApiModelProperty("发票中文地址")
	private  String invoiceAddress;
	/**
      * 发票英文地址
      */
    @ApiModelProperty("发票英文地址")
	private  String invoiceAddressEn;
	/**
      * 送货中文地址
      */
    @ApiModelProperty("送货中文地址")
	private  String deliverAddress;
	/**
      * 送货英文地址
      */
    @ApiModelProperty("送货英文地址")
	private  String deliverAddressEn;
	/**
      * 用户/导入批次号
      */
    @ApiModelProperty("用户/导入批次号")
	private  String tempOwner;
	/**
      * 数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）
      */
    @ApiModelProperty("数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）")
	private  Integer modifyMark;
	/**
      * ERP最后变更日期
      */
    @ApiModelProperty("ERP最后变更日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date lastModifyDate;
	/**
      * 数据来源 0 手工录入 1ERP接口 2导入
      */
    @ApiModelProperty("数据来源 0 手工录入 1ERP接口 2导入")
	private  String dataSource;
	/**
      * 数据状态 0 未提取，1 提取过
      */
    @ApiModelProperty("数据状态 0 未提取，1 提取过")
	private  String status;
}
