package com.dcjet.cs.dto.war;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2019-4-19
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class WarringRepairParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 序号
     */
	@Digits(integer = 11, fraction = 0, message = "{序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("序号")
	private BigDecimal serialNo;
	/**
     * 修理物料批次号
     */
	@XdoSize(max = 100, message = "{修理物品批次长度不能超过100位字节长度（一个汉字2位字节长度）！}")
	@ApiModelProperty("修理物料批次号")
	private  String documentNo;
	/**
     * 修理物品名称
     */
	@XdoSize(max = 255, message = "{修理物品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修理物品名称")
	private  String documentName;
	/**
     * 修理物料复出复进日期
     */
	@ApiModelProperty("修理物料复出复进日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date dateEnd;
	/**
    * 修理物料复出复进日期-开始
    */
	@ApiModelProperty("修理物料复出复进日期-开始")
	private String dateEndFrom;
	/**
    * 修理物料复出复进日期-结束
    */
	@ApiModelProperty("修理物料复出复进日期-结束")
    private String dateEndTo;
	/**
     * 备注
     */
	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
	 * 数据状态
	 */
	@ApiModelProperty("数据状态")
	private String dataStatus;

	/**
	 * 制单员
	 */
	private String insertUser;

	/**
	 * 提单录入日期-开始
	 */
	private String insertTimeFrom;
	/**
	 * 提单录入日期-结束
	 */
	private String insertTimeTo;
}
