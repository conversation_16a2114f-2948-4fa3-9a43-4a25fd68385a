package com.dcjet.cs.dto.cost;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
/**
 *
 * <AUTHOR>
 * @date: 2021-8-15
 */
@Setter @Getter
@ApiModel(value = "进口费用比对表头传入参数")
public class CostCompareIHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 账单编号
     */
	@XdoSize(max = 50, message = "{账单编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("账单编号")
	private  String payNo;
	/**
     * 收款单位类型（1 货代 2 报关行）
     */
	@XdoSize(max = 1, message = "{收款单位类型（1 货代 2 报关行）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收款单位类型（1 货代 2 报关行）")
	private  String remitteeType;
	/**
     * 收款单位编码
     */
	@XdoSize(max = 100, message = "{收款单位编码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收款单位编码")
	private  String remitteeCode;
	/**
     * 收款单位名称
     */
	@XdoSize(max = 200, message = "{收款单位名称长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收款单位名称")
	private  String remitteeName;
	/**
     * 实际总费用
     */
	@Digits(integer = 9, fraction = 5, message = "{实际总费用必须为数字,整数位最大9位,小数最大5位!}")
	@ApiModelProperty("实际总费用")
	private  BigDecimal actualCostRmbTotal;
	/**
     * 预估总费用
     */
	@Digits(integer = 9, fraction = 5, message = "{预估总费用必须为数字,整数位最大9位,小数最大5位!}")
	@ApiModelProperty("预估总费用")
	private  BigDecimal estimateCostRmbTotal;
	/**
     * 总差异
     */
	@Digits(integer = 9, fraction = 5, message = "{总差异必须为数字,整数位最大9位,小数最大5位!}")
	@ApiModelProperty("总差异")
	private  BigDecimal differenceTotal;
	private  String differenceCompare;
	/**
     * 备注
     */
	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 审核人
     */
	@XdoSize(max = 50, message = "{审核人长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("审核人")
	private  String auditor;
	/**
     * 审核状态(0 未审核、1 待审核、2 审核通过、3 审核退回)
     */
	@XdoSize(max = 1, message = "{审核状态(0 未审核、1 待审核、2 审核通过、3 审核退回)长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("审核状态(0 未审核、1 待审核、2 审核通过、3 审核退回)")
	private  String auditStatus;
	/**
     * 审核意见
     */
	@XdoSize(max = 255, message = "{审核意见长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("审核意见")
	private  String auditOpinion;
	/**
     * 比对状态(0 未比对、1 比对中、2 比对成功、3 比对失败)
     */
	@XdoSize(max = 1, message = "{比对状态长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("比对状态(0 未比对、1 比对中、2 比对成功、3 比对失败")
	private  String compareStatus;
	/**
     * 企业编码
     */
	@XdoSize(max = 10, message = "{企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 制单人姓名
     */
	@XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("制单人姓名")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
	private  String lockMark;
	/**
	 * 发送人
	 */
	private String sendUser;
	/**
	 * 发送人
	 */
	private String sendUserName;
}
