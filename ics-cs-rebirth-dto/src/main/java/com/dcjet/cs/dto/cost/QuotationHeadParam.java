package com.dcjet.cs.dto.cost;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2020-7-8
 */
@Setter
@Getter
@ApiModel(value = "报价单表头传入参数")
public class QuotationHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 报价单编号
     */
    @NotEmpty(message = "{报价单编号不能为空！}")
    @XdoSize(max = 50, message = "{报价单编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报价单编号")
    private String quoNo;
    /**
     * 报价单名称
     */
    @NotEmpty(message = "{报价单名称不能为空！}")
    @XdoSize(max = 100, message = "{报价单名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报价单名称")
    private String quoName;
    /**
     * 收款单位类型（1 货代 2 报关行）
     */
    @NotEmpty(message = "{收款单位类型不能为空！}")
    @XdoSize(max = 1, message = "{收款单位类型（1 货代 2 报关行）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("收款单位类型（1 货代 2 报关行）")
    private String exchequerType;
    /**
     * 收款单位名称
     */
    @NotEmpty(message = "{收款单位名称不能为空！}")
    @XdoSize(max = 100, message = "{收款单位名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("收款单位名称")
    private String exchequerName;
    /**
     * 进出口类型（I 进口 E 出口）
     */
    @NotEmpty(message = "{进出口类型（I 进口 E 出口）不能为空！}")
    @XdoSize(max = 1, message = "{进出口类型（I 进口 E 出口）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("进出口类型（I 进口 E 出口）")
    private String ieType;
    /**
     * 有效开始日期
     */
    @ApiModelProperty("有效开始日期")
    private Date effDateFrom;
    /**
     * 有效结束日期
     */
    @ApiModelProperty("有效结束日期")
    private Date effDateTo;
    /**
     * 有效开始日期查询条件
     */
    @ApiModelProperty("有效开始日期查询条件")
    private String effDateFromStr;
    /**
     * 有效结束日期查询条件
     */
    @ApiModelProperty("有效结束日期查询条件")
    private String effDateToStr;
    /**
     * 当前国家
     */
    @XdoSize(max = 10, message = "{当前国家长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("当前国家")
    private String country;
    /**
     * 币制
     */
    @XdoSize(max = 10, message = "{币制长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 物品种类（1 文件 2货物）
     */
    @XdoSize(max = 1, message = "{物品种类（1 文件 2货物）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("物品种类（1 文件 2货物）")
    private String goodsType;
    /**
     * 服务类型（1 经济 2 优先）
     */
    @XdoSize(max = 1, message = "{服务类型（1 经济 2 优先）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("服务类型（1 经济 2 优先）")
    private String serviceType;
    /**
     * 备注
     */
    @XdoSize(max = 500, message = "{备注长度不能超过500位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String note;
    /**
     * 企业编码
     */
    @XdoSize(max = 10, message = "{企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 报价单类型（1 一般报价单 2 快件报价单）
     */
    @NotEmpty(message = "{报价单类型不能为空！}")
    @XdoSize(max = 1, message = "{报价单类型（1 一般报价单 2 快件报价单）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报价单类型（1 一般报价单 2 快件报价单）")
    private String quoType;
    /**
     * 使用状态（0 未使用 1 已使用）
     */
    @XdoSize(max = 1, message = "{使用状态（0 未使用 1 已使用）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("使用状态（0 未使用 1 已使用）")
    private String status;
    /**
     * 运输方式
     */
    @XdoSize(max = 20, message = "{运输方式长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 贸易条款
     */
    @XdoSize(max = 20, message = "{贸易条款长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("贸易条款")
    private String tradeTerms;
    /**
     * 启运港
     */
    @XdoSize(max = 30, message = "{启运港长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("启运港")
    private String despPort;
    /**
     * 目的港
     */
    @XdoSize(max = 30, message = "{目的港长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("目的港")
    private String destPort;

    /**
     * 进出境关别
     */
    @ApiModelProperty("进出境关别")
    @XdoSize(max = 60, message = "{进出境关别长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    private String iePort;

    /**
     * 国内物流属性
     */
    @ApiModelProperty("国内物流属性")
    @XdoSize(max = 30, message = "{国内物流属性长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    private String domesticLogistics;

    /**
     * 国际物流属性
     */
    @ApiModelProperty("国际物流属性")
    @XdoSize(max = 30, message = "{国际物流属性长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    private String internationalLogistics;

    /**
     * 国别类型 1 DHL 2 UPS
     */
    private String countryType;

    private String insertTimeStr;
    private String headId;
}
