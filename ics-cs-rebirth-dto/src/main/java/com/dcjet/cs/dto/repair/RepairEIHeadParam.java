package com.dcjet.cs.dto.repair;

import com.dcjet.cs.dto.base.BasicParam;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * 修理复运进境表头
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "修理复运进境表头新增修改参数")
@Setter @Getter
public class RepairEIHeadParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单据内部编号")
    @XdoSize(max = 32, message = "{单据内部编号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @NotEmpty(message = "{单据内部编号不能为空}")
    private String emsListNo;
    
    @ApiModelProperty(value = "复运完成日期") 
    private Date completeDate;
    
    @ApiModelProperty(value = "备注")
    @XdoSize(max = 512, message = "{备注长度不能超过512位字节长度(一个汉字2位字节长度)!}") 
    private String note;
    
}
