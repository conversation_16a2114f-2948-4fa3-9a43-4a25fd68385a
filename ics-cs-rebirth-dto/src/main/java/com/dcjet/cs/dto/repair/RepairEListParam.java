package com.dcjet.cs.dto.repair;

import com.dcjet.cs.dto.base.BasicParam;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 出境修理表体
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "出境修理表体新增修改参数")
@Setter @Getter
public class RepairEListParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

}