package com.dcjet.cs.dto.deep;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2019-11-20
 */
@ApiModel(value = "SjgOutBillTdListDto")
@Setter @Getter
public class SjgOutBillTdListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 转出企业料号
      */
    @ApiModelProperty("转出企业料号")
	private  String facGNoOut;
	/**
      * 转出备案料号
      */
    @ApiModelProperty("转出备案料号")
	private  String copGNoOut;
	/**
      * 转出备案序号
      */
    @ApiModelProperty("转出备案序号")
//	@JsonProperty("gNoOut")
	private  Integer gNoOut;
	/**
      * 数量
      */
    @ApiModelProperty("数量")
	private  BigDecimal decQty;
	/**
      * 单价
      */
    @ApiModelProperty("单价")
	private  BigDecimal decPrice;
	/**
      * 总价
      */
    @ApiModelProperty("总价")
	private  BigDecimal decTotal;
	/**
      * 转入备案序号
      */
    @ApiModelProperty("转入备案序号")
//	@JsonProperty("gNoIn")
	private  Integer gNoIn;
	/**
      * 转入备案料号
      */
    @ApiModelProperty("转入备案料号")
	private  String copGNoIn;
	/**
      * 申报计量单位
      */
    @ApiModelProperty("申报计量单位")
	private  String unit;
	/**
      * 币制
      */
    @ApiModelProperty("币制")
	private  String curr;
	/**
      * 报关单序号
      */
    @ApiModelProperty("报关单序号")
	private  Integer entryGNo;
	/**
      * 深加工转出料件表头的SID
      */
    @ApiModelProperty("深加工转出料件表头的SID")
	private  String headId;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建日期
      */
    @ApiModelProperty("创建日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 变更人
      */
    @ApiModelProperty("变更人")
	private  String updateUser;
	/**
      * 变更日期
      */
    @ApiModelProperty("变更日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 制单人姓名
      */
    @ApiModelProperty("制单人姓名")
	private  String insertUserName;
	/**
      * 修改人姓名
      */
    @ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
//	@JsonProperty("gName")
	private  String gName;
	/**
      * 清单序号
      */
    @ApiModelProperty("清单序号")
	private  Integer billGNo;
	/**
      * SO料号
      */
    @ApiModelProperty("SO料号")
	private  String soNo;
	/**
      * 客户编码
      */
    @ApiModelProperty("客户编码")
	private  String supplierCode;
	/**
      * 客户名称
      */
    @ApiModelProperty("客户名称")
	private  String supplierName;
	/**
      * 成本中心
      */
    @ApiModelProperty("成本中心")
	private  String costCenter;
	/**
      * 单耗版本号
      */
    @ApiModelProperty("单耗版本号")
	private  String bomVersion;
	/**
	 * 拆分状态 0未拆分 1已拆分
	 */
	@ApiModelProperty("拆分状态")
	private  String splitStatus;
}
