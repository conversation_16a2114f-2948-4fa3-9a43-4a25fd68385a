package com.dcjet.cs.dto.receipt;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2019-5-21
 */
@Setter @Getter
@ApiModel(value = "发票表体传入参数")
public class ReceiptTemplateListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 发票模板表头SID
     */
	@XdoSize(max = 40, message = "{发票模板表头SID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发票模板表头SID")
	private  String headId;
	/**
     * 单据类型
     */
	@NotEmpty(message="{单据类型不能为空！}")
	@XdoSize(max = 1, message = "{单据类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("单据类型")
	private  String billType;
	/**
     * 顺序号
     */
	@Digits(integer = 9, fraction = 0, message = "{顺序号必须为数字,整数位最大9位,小数最大0位!}")
	@ApiModelProperty("顺序号")
	private  Integer serialNo;

	/**
	 * 模板名称
	 */
	@NotEmpty(message="{模板名称不能为空！}")
	@XdoSize(max = 50, message = "{模板名称长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("模板名称")
	private String templateName;

	/**
	 * 表体合并字段
	 */
	@XdoSize(max = 200, message = "{表体合并字段不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("表体合并字段")
	private String groupField;

	/**
	 * 分页配置
	 */
	@ApiModelProperty("分页配置")
	private String pagingConfiguration;

}
