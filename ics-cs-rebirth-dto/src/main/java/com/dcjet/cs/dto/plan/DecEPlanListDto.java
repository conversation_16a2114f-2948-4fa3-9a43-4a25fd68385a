package com.dcjet.cs.dto.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021-9-5
 */
@ApiModel(value = "出货管理-出口计划表体返回信息")
@Setter
@Getter
public class DecEPlanListDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 表头sid
     */
    @ApiModelProperty("表头sid")
    private String headId;
    /**
     * 数据来源 1录入;2数据提取
     */
    @ApiModelProperty("数据来源 1录入;2数据提取")
    private String dataSource;
    /**
     * 出货ID
     */
    @ApiModelProperty("出货ID")
    private String linkedNo;
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    private Integer lineNo;
    /**
     * 企业料号
     */
    @ApiModelProperty("企业料号")
    private String facGNo;
    /**
     * 保完税标志
     */
    @ApiModelProperty("保完税标志")
    private String bondMark;
    /**
     * 物料类型
     */
    @ApiModelProperty("物料类型")
    @JsonProperty("gMark")
    private String gMark;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String copGName;
    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    @JsonProperty("gModel")
    private String gModel;
    /**
     * 交易数量
     */
    @ApiModelProperty("交易数量")
    private BigDecimal qty;
    /**
     * ERP计量单位
     */
    @ApiModelProperty("ERP计量单位")
    private String unitErp;
    /**
     * 材料费
     */
    @ApiModelProperty("材料费")
    private BigDecimal decPriceMaterials;
    /**
     * 加工费
     */
    @ApiModelProperty("加工费")
    private BigDecimal decPriceProcess;
    /**
     * 币制
     */
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 箱号
     */
    @ApiModelProperty("箱号")
    private String cartonNo;
    /**
     * 销售订单号码
     */
    @ApiModelProperty("销售订单号码")
    private String soNo;
    /**
     * 车间订单
     */
    @ApiModelProperty("车间订单")
    private String shopOrder;
    /**
     * 客户PO
     */
    @ApiModelProperty("客户PO")
    private String customerPoNo;
    /**
     * 净重
     */
    @ApiModelProperty("净重")
    private BigDecimal netWt;
    /**
     * 毛重
     */
    @ApiModelProperty("毛重")
    private BigDecimal grossWt;
    /**
     * 体积
     */
    @ApiModelProperty("体积")
    private BigDecimal volume;
    /**
     * 备注1
     */
    @ApiModelProperty("备注1")
    private String remark1;
    /**
     * 备注2
     */
    @ApiModelProperty("备注2")
    private String remark2;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 变更人
     */
    @ApiModelProperty("变更人")
    private String updateUser;
    /**
     * 变更人名称
     */
    @ApiModelProperty("变更人名称")
    private String insertUserName;
    /**
     * 变更人名称
     */
    @ApiModelProperty("变更人名称")
    private String updateUserName;
    /**
     * 变更日期
     */
    @ApiModelProperty("变更日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;
}
