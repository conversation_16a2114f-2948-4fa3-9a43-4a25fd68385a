package com.dcjet.cs.dto.ftpConfig;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2019-10-22
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class GwstdFtpConfigParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * ftp IP地址
     */
	@NotEmpty(message="{ftp IP地址不能为空！}")
	@XdoSize(max = 15, message = "{ftp IP地址长度不能超过15位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("ftp IP地址")
	private  String ipAddr;
	/**
     * ftp 端口
     */
	@NotNull(message="{ftp 端口不能为空！}")
	@Digits(integer = 8, fraction = 0, message = "{ftp 端口必须为数字,整数位最大8位,小数最大0位!}")
	@ApiModelProperty("ftp 端口")
	private  Integer port;
	/**
     * 账号
     */
	@NotEmpty(message="{账号不能为空！}")
	@XdoSize(max = 20, message = "{账号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("账号")
	private  String ftpAccount;
	/**
     * 密码
     */
	@NotEmpty(message="{密码不能为空！}")
	@XdoSize(max = 50, message = "{密码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("密码")
	private  String ftpPwd;
	/**
     * 服务器文件路径
     */
	@XdoSize(max = 100, message = "{服务器文件路径长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("服务器文件路径")
	private  String serverFilePath;
	/**
     * FTP业务类型
     */
	@XdoSize(max = 255, message = "{FTP业务类型长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("FTP业务类型")
	private  String ftpType;
	/**
     * 企业代码
     */
	@NotEmpty(message="{企业代码不能为空！}")
	@XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
     * 是否启用 1：启用 0：禁用 默认 1
     */
	@ApiModelProperty("是否启用 1：启用 0：禁用 默认 1")
	private  String isEnable;
	/**
	 * 是否自动匹配 0.自动匹配 1.手动匹配
	 */
	@ApiModelProperty("是否自动匹配 0.自动匹配 1.手动匹配")
	private  String matchType;
	/**
	 * 编码
	 */
	@NotEmpty(message="{编码不能为空！}")
	@ApiModelProperty("编码")
	private  String coding;
}
