package com.dcjet.cs.dto.mrp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "内销单价算计算范围")
public class MrpPriceCalcRangeDto {


    @ApiModelProperty("核注清单编号")
    private  String listNo;

    @ApiModelProperty("监管方式")
    private  String tradeMode;


    @ApiModelProperty("清单申报日期")
    private Date declareDate;

    @ApiModelProperty("报关单号")
    private  String entryNo;

    @ApiModelProperty("成交方式")
    private  String transMode;

    @ApiModelProperty("运费")
    private String fee;

    @ApiModelProperty("保费")
    private String insur;

    @ApiModelProperty("杂费")
    private String other;

    @ApiModelProperty("备案料号")
    private String copGNo;

    @ApiModelProperty("商品编码")
    private String codeTS;

    @ApiModelProperty("商品名称")
    private String GName;

    @ApiModelProperty("申报数量")
    private BigDecimal qty;

    @ApiModelProperty("计量单位")
    private String unit;

    @ApiModelProperty("原产国")
    private String originCountry;

    @ApiModelProperty("申报单价")
    private BigDecimal decPrice;

    @ApiModelProperty("申报总价")
    private BigDecimal decTotal;

    @ApiModelProperty("币制")
    private String curr;

    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;
}
