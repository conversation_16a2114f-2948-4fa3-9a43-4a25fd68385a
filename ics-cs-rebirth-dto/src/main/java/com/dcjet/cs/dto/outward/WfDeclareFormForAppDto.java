package com.dcjet.cs.dto.outward;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2023-5-23
 */
@ApiModel(value = "返回信息")
@Setter
@Getter
public class WfDeclareFormForAppDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 申报表编号
     */
    @ApiModelProperty("申报表编号")
    private String gcdNo;
    /**
     * 预录入统一编号
     */
    @ApiModelProperty("预录入统一编号")
    private String preSeqNo;
    /**
     * 企业内部编号
     */
    @ApiModelProperty("企业内部编号")
    private String copEmsNo;
    /**
     * 委托方手账册号
     */
    @ApiModelProperty("委托方手账册号")
    private String emsNo;
    /**
     * 全工序外发标志(0-否，1-是)
     */
    @ApiModelProperty("全工序外发标志(0-否，1-是)")
    private String trfFlag;
    /**
     * 申报日期
     */
    @ApiModelProperty("申报日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date declareDate;
    /**
     * 数据来源
     */
    @ApiModelProperty("数据来源")
    private String dataSource;
    /**
     * 委托方海关代码
     */
    @ApiModelProperty("委托方海关代码")
    private String receiveCode;
    /**
     * 收回主要货物
     */
    @ApiModelProperty("收回主要货物")
    private String trfinGoods;
    /**
     * 外发主要货物
     */
    @ApiModelProperty("外发主要货物")
    private String trfoutGoods;
    /**
     * 变更次数
     */
    @ApiModelProperty("变更次数")
    private String changeTimes;
    /**
     * 委托方地区代码
     */
    @ApiModelProperty("委托方地区代码")
    private String trfoutArea;
    /**
     * 委托加工合同号
     */
    @ApiModelProperty("委托加工合同号")
    private String trfoutContract;
    /**
     * 承揽者海关代码
     */
    @ApiModelProperty("承揽者海关代码")
    private String trfinCode;
    /**
     * 承揽者名称
     */
    @ApiModelProperty("承揽者名称")
    private String trfinName;
    /**
     * 委托方主管海关
     */
    @ApiModelProperty("委托方主管海关")
    private String trfoutCustom;
    /**
     * 承揽者所在地地区代码
     */
    @ApiModelProperty("承揽者所在地地区代码")
    private String trfinArea;
    /**
     * 委托方申报人/电话
     */
    @ApiModelProperty("委托方申报人/电话")
    private String trfoutDecPerson;
    /**
     * 委托方法人/电话
     */
    @ApiModelProperty("委托方法人/电话")
    private String trfoutLegalPerson;
    /**
     * 承揽者所在地主管海关
     */
    @ApiModelProperty("承揽者所在地主管海关")
    private String trfinCustom;
    /**
     * 承揽者社会信用代码
     */
    @ApiModelProperty("承揽者社会信用代码")
    private String trfinCreditCode;
    /**
     * 委托方联系人/电话
     */
    @ApiModelProperty("委托方联系人/电话")
    private String trfoutLinkPerson;
    /**
     * 承揽者联系人/电话
     */
    @ApiModelProperty("承揽者联系人/电话")
    private String trfinLinkPerson;
    /**
     * 承揽者法人/电话
     */
    @ApiModelProperty("承揽者法人/电话")
    private String trfinLegalPerson;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inputDate;
    /**
     * 外发申报金额(人民币)
     */
    @ApiModelProperty("外发申报金额(人民币)")
    private BigDecimal decAmount;
    /**
     * 实际收货总金额(人民币)
     */
    @ApiModelProperty("实际收货总金额(人民币)")
    private BigDecimal trfinAmount;
    /**
     * 实际发货总金额(人民币)
     */
    @ApiModelProperty("实际发货总金额(人民币)")
    private BigDecimal trfoutAmount;
    /**
     * 申报表有效期
     */
    @ApiModelProperty("申报表有效期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validDate;
    /**
     * 委托方信用代码
     */
    @ApiModelProperty("委托方信用代码")
    private String receiveCreditCode;
}
