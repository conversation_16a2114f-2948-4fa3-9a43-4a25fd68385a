package com.dcjet.cs.dto.invoice;
import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2019-6-12
 */
@ApiModel(value = "出口发票箱单表体返回信息")
@Setter
@Getter
public class DecInvoiceBoxListEDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 
    */
    @ApiModelProperty("")
	private  String sid;
    /**
    * 模板名称
    */
    @ApiModelProperty("模板名称")
	private  String templateName;
    /**
    * 单据类型
    */
    @ApiModelProperty("单据类型")
	private  String billType;
    /**
    * 单据号
    */
    @ApiModelProperty("单据号")
	private  String billNo;
    /**
    * 模板显示顺序号
    */
    @ApiModelProperty("模板显示顺序号")
	private  Integer serialNo;
    /**
    * SID
    */
    @ApiModelProperty("SID")
	private  String templateId;
    /**
    * 对应随附单据id
    */
    @ApiModelProperty("对应随附单据id")
	private  String attachId;
    /**
    * 表头id
    */
    @ApiModelProperty("表头id")
	private  String headId;

    /**
    * 生成文件名称
    */
    @ApiModelProperty("生成文件名称")
	private  String buildFileName;
    /**
    * 文件服务器返回链接地址
    */
    @ApiModelProperty("文件服务器返回链接地址")
	private  String fdfsId;
    /**
     * 文件服务器返回链接地址PDF
     */
    @ApiModelProperty("文件服务器返回链接地址PDF")
    private  String fdfsPdfId;
}
