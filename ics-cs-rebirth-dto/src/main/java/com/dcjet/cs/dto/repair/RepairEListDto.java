package com.dcjet.cs.dto.repair;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 出境修理表体
 * 
 * generated by Generate dc
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "出境修理表体 DTO")
@Setter @Getter
public class RepairEListDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @ApiModelProperty(value = "表头主键")
    private String headId;
    
    @ApiModelProperty(value = "物料类型")
    private String gMark;
    
    @ApiModelProperty(value = "企业料号")
    private String facGNo;
    
    @ApiModelProperty(value = "商品编码")
    private String codeTS;
    
    @ApiModelProperty(value = "商品名称")
    private String gName;
    
    @ApiModelProperty(value = "申报数量")
    private BigDecimal qty;
    
    @ApiModelProperty(value = "申报单价")
    private BigDecimal decPrice;
    
    @ApiModelProperty(value = "申报总价")
    private BigDecimal decTotal;
    
    @ApiModelProperty(value = "规格型号")
    private String gModel;
    
    @ApiModelProperty(value = "计量单位")
    private String unit;
    
    @ApiModelProperty(value = "币制")
    private String curr;

    @ApiModelProperty(value = "剩余数量")
    private BigDecimal remainQty;

    @ApiModelProperty("复运数量")
    private BigDecimal useQty;

    @ApiModelProperty("提取数量")
    private BigDecimal extractQty;



    @ApiModelProperty(value = "单据内部编号")
    private String emsListNo;

    @ApiModelProperty(value = "报关单号")
    private String entryNo;

    @ApiModelProperty(value = "报关单申报日期")
    private String declareDate;

    @ApiModelProperty(value = "维修单价")
    private BigDecimal repairPrice;

    public BigDecimal getExtractQty() {
        if (extractQty == null) {
            extractQty = remainQty;
        }
        return extractQty;
    }

    public BigDecimal getUseQty() {
        if (useQty == null) {
            useQty = qty.subtract(remainQty);
        }
        return useQty;
    }

    public BigDecimal getRepairPrice() {
        if (repairPrice == null) {
            return BigDecimal.ZERO;
        }
        return repairPrice;
    }
}