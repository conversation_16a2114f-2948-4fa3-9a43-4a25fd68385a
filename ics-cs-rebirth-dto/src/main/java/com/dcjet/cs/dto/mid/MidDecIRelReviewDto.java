package com.dcjet.cs.dto.mid;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2020-6-16
 */
@ApiModel(value = "中期核查进口关联核对追溯返回信息")
@Setter @Getter
public class MidDecIRelReviewDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private String sid;
	/**
      * 申报日期
      */
    @ApiModelProperty("申报日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date declareDate;
	/**
      * 报关单号
      */
    @ApiModelProperty("报关单号")
	private String entryNo;
	/**
      * 核注清单编号
      */
    @ApiModelProperty("核注清单编号")
	private String listNo;
	/**
      * 单据内部编号
      */
    @ApiModelProperty("单据内部编号")
	private String emsListNo;
	/**
      * 企业料号
      */
    @ApiModelProperty("企业料号")
	private String facGNo;
	/**
      * 申报数量
      */
    @ApiModelProperty("申报数量")
	private BigDecimal qty;
	/**
      * 备案号
      */
    @ApiModelProperty("备案号")
	private String emsNo;
	/**
      * 备案序号
      */
    @ApiModelProperty("备案序号")
	private Long serialNo;
	/**
      * 备案料号
      */
    @ApiModelProperty("备案料号")
	private String copGNo;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	private String GName;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private String insertUser;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date insertTime;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateTime;
	/**
      * 制单人姓名
      */
    @ApiModelProperty("制单人姓名")
	private String insertUserName;
	/**
      * 修改人姓名
      */
    @ApiModelProperty("修改人姓名")
	private String updateUserName;
	/**
	 * 入库关联编号
	 */
	@ApiModelProperty("入库关联编号")
	private String linkedNo;
	/**
	 * 保完税标记
	 */
	@ApiModelProperty("保完税标记")
	private String bondMark;
	/**
	 * 物料类型
	 */
	@ApiModelProperty("物料类型")
	private String GMark;
}
