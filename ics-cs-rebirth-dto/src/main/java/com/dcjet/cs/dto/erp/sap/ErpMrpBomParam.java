package com.dcjet.cs.dto.erp.sap;


import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date: 2019-11-6
 */
@Setter
@Getter
@ApiModel(description= "ERP内销销BOM接口传入参数")
public class ErpMrpBomParam {
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private  String sid;

    @ApiModelProperty("BOM版本号")
    private String exgVersion;

    @ApiModelProperty("成品料号")
    private String copExgNo;

    @ApiModelProperty("企业料号")
    private String copImgNo;

    @ApiModelProperty("提取状态（0 未提取;1分送集报;2 深加工 3内销）")
    private String status;

    @ApiModelProperty("数据状态(1：修改，2：删除，3：新增)")
    private Integer modifyMark;

    @ApiModelProperty("工厂")
    private String factory;

    @ApiModelProperty("有效起始日期")
    private String validBeginDate;

    @ApiModelProperty("有效结束日期")
    private String validEndDate;

    @ApiModelProperty("接收时间-from")
    private String insertTimeFrom;

    @ApiModelProperty("接收时间-from")
    private String insertTimeTo;

    @ApiModelProperty("更新时间-from")
    private String updateTimeFrom;

    @ApiModelProperty("更新时间-to")
    private String updateTimeTo;

    @XdoSize(max = 60, message = "{传输批次号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("传输批次号")
    private String tempOwner;

    private String tradeCode;
}
