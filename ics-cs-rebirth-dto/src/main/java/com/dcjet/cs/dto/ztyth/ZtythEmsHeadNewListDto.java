package com.dcjet.cs.dto.ztyth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 * @date: 2019-3-19
 */
@ApiModel(value = "ZtythEmsHeadDto返回信息")
@Setter @Getter
public class ZtythEmsHeadNewListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
	 * 企业内部编号
	 */
	@ApiModelProperty(name = "企业内部编号")
	private  String copEmsNo;
	/**
	 * 企业内部编号(别名)
	 */
	@ApiModelProperty(name = "企业内部编号(别名)")
	private  String copEmsAlias;
	/**
	 * 手账册号(新) TOTO:新版需要
	 */
	@ApiModelProperty("手账册号")
	private  String emsNo;
	/**
	 * 单据类型  11：金二手册 12：H200手册 21：金二账册 22：H2000账册
	 */
	@ApiModelProperty("11：金二手册 12：H200手册 21：金二账册 22：H2000账册")
	private String billFlag;
	/**
	 * 企业代码
	 */
	@ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
	 * 经营企业名称
	 */
	@ApiModelProperty("经营企业名称")
	private  String tradeName;
	/**
	 * 经营企业社会信用代码
	 */
	@ApiModelProperty("经营企业社会信用代码")
	private  String tradeCreditCode;
	/**
	 * 加工单位
	 */
	@ApiModelProperty("加工单位")
	private  String receiveCode;
	/**
	 * 加工单位名称
	 */
	@ApiModelProperty("加工单位名称")
	private  String receiveName;
	/**
	 * 加工单位社会信用代码
	 */
	@ApiModelProperty("加工单位社会信用代码")
	private  String receiveCreditCode;
}
