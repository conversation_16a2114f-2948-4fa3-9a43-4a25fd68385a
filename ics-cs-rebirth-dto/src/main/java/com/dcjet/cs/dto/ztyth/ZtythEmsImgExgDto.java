package com.dcjet.cs.dto.ztyth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 * @date: 2019-3-19
 */
@ApiModel(value = "ZtythEmsImgExgDto返回信息")
@Setter @Getter
public class ZtythEmsImgExgDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 序号
      */
    @ApiModelProperty("序号")
	private  BigDecimal GNo;
	/**
      * 商品编码
      */
    @ApiModelProperty("商品编码")
	private  String codeT;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	private  String GName;
	/**
      * 规格型号
      */
    @ApiModelProperty("规格型号")
	private  String GModel;
	/**
      * 产销国
      */
    @ApiModelProperty("产销国")
	private  String countryCode;
	/**
      * 备案数量
      */
    @ApiModelProperty("备案数量")
	private  BigDecimal qty;
	/**
      * 单位
      */
    @ApiModelProperty("单位")
	private  String unit;
	/**
      * 单价
      */
    @ApiModelProperty("单价")
	private  BigDecimal decPrice;
	/**
      * 总价
      */
    @ApiModelProperty("总价")
	private  BigDecimal apprAmt;
	/**
      * 币制
      */
    @ApiModelProperty("币制")
	private  String curr;
	/**
      * 征免方式
      */
    @ApiModelProperty("征免方式")
	private  String dutyMode;
	/**
      * 手册编号
      */
    @ApiModelProperty("手册编号")
	private  String hbNo;
}
