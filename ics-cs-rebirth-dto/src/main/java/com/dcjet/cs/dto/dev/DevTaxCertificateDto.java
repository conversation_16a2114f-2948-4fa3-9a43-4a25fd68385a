package com.dcjet.cs.dto.dev;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * 
 * <AUTHOR>
 * @date: 2019-10-8
 */
@ApiModel(value = "设备管理-征免税证明返回信息")
@Setter @Getter
public class DevTaxCertificateDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 征免税证明编号
      */
    @ApiModelProperty("征免税证明编号")
	private  String certNo;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	private  String GName;
	/**
      * 金额
      */
    @ApiModelProperty("金额")
	private  BigDecimal amout;
	/**
      * 币制
      */
    @ApiModelProperty("币制")
	private  String curr;
	/**
      * 项目确认书编号
      */
    @ApiModelProperty("项目确认书编号")
	private  String itemNo;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;

	/**
	 * 是否有附件，Y:存在，N:不存在
	 */
	@ApiModelProperty("是否有附件，Y:存在，N:不存在")
	private String attached;
}
