package com.dcjet.cs.dto.gwstd;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 *
 * <AUTHOR>
 * @date: 2021-7-29
 */
@ApiModel(value = "代理企业配置返回信息")
@Setter @Getter
public class GwstdAgentConfigDto implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private  String sid;
	/**
	 * 用户登录账号
	 */
	@ApiModelProperty("用户登录账号")
	private  String userNo;
	/**
	 * 报关行简码
	 */
	@ApiModelProperty("报关行简码")
	private  String declareAlias;
	/**
	 * 报关行企业十位代码
	 */
	@ApiModelProperty("报关行企业十位代码")
	private  String declareCode;
	/**
	 * 报关行企业名称
	 */
	@ApiModelProperty("报关行企业名称")
	private  String declareName;
	/**
	 * 报关行权限,包含 预录入单制单,报关追踪维护。格式：0,1(未授权/授权)
	 */
	@ApiModelProperty("报关行权限,包含 预录入单制单,报关追踪维护。格式：0,1(未授权/授权)")
	private  String declareAuth;
	/**
	 * 报关行生效标志
	 */
	@ApiModelProperty("报关行生效标志")
	private  String declareIsEnable;
	/**
	 * 货代简码
	 */
	@ApiModelProperty("货代简码")
	private  String forwardAlias;
	/**
	 * 货代企业十位代码
	 */
	@ApiModelProperty("货代企业十位代码")
	private  String forwardCode;
	/**
	 * 货代企业名称
	 */
	@ApiModelProperty("货代企业名称")
	private  String forwardName;
	/**
	 * 货代权限,包含 预录入单制单,物流追踪维护。格式：0,1...(未授权/授权)
	 */
	@ApiModelProperty("货代权限,包含 预录入单制单,物流追踪维护。格式：0,1...(未授权/授权)")
	private  String forwardAuth;
	/**
	 * 货代生效标志
	 */
	@ApiModelProperty("货代生效标志")
	private  String forwardIsEnable;
	/**
	 * 被代理企业海关编码
	 */
	@ApiModelProperty("被代理企业海关编码")
	private  String tradeCode;
	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private  String insertUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private  String updateUser;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
	 * 创建人名称
	 */
	@ApiModelProperty("创建人名称")
	private  String insertUserName;
	/**
	 * 修改人姓名
	 */
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
	 * 预报单企业简码
	 */
	@ApiModelProperty("预报单企业简码")
	private  String preDeclareAlias;
	/**
	 * 预报单企业十位代码
	 */
	@ApiModelProperty("预报单企业十位代码")
	private  String preDeclareCode;
	/**
	 * 预报单企业名称
	 */
	@ApiModelProperty("预报单企业名称")
	private  String preDeclareName;
	/**
	 * 预报单企业生效标志
	 */
	@ApiModelProperty("预报单企业生效标志")
	private  String preDeclareIsEnable;
}
