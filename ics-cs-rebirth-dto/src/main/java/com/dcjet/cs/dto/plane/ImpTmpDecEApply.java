package com.dcjet.cs.dto.plane;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021-1-25
 */
@Setter
@Getter
@ApiModel(value = "申请订舱传入参数")
public class ImpTmpDecEApply implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 状态，0：暂存
     */
    @XdoSize(max = 1, message = "状态，0：暂存长度不能超过1位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("状态，0：暂存")
    private String status;
    /**
     * 运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递
     */
    @XdoSize(max = 6, message = "运输方式长度不能超过6位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递")
    private String trafMode;
    /**
     * 运输方式名称
     */
    @XdoSize(max = 200, message = "运输方式名称长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("运输方式名称")
    private String trafName;

    /**
     * 最终目的国
     */
    @XdoSize(max = 10, message = "最终目的国长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("最终目的国")
    private String destinationCountry;
    /**
     * 订单订舱开船日
     */
    @ApiModelProperty("订单订舱开船日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String ccetdDate;
    /**
     * 订单订舱开船日-开始
     */
    @ApiModelProperty("订单订舱开船日-开始")
    private String ccetdDateFrom;
    /**
     * 订单订舱开船日-结束
     */
    @ApiModelProperty("订单订舱开船日-结束")
    private String ccetdDateTo;
    /**
     * 目的港
     */
    @XdoSize(max = 10, message = "目的港长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("目的港")
    private String destPort;
    /**
     * 售达方
     */
    @XdoSize(max = 50, message = "售达方长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("售达方")
    private String clientCode;
    /**
     * 送达方
     */
    @XdoSize(max = 50, message = "送达方长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("送达方")
    private String deliveryNo;
    /**
     * 国际贸易条件
     */
    @XdoSize(max = 10, message = "国际贸易条件长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("国际贸易条件")
    private String tradeTerms;
    /**
     * 国际贸易条件（部分2）
     */
    @XdoSize(max = 50, message = "国际贸易条件（部分2）长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("国际贸易条件（部分2）")
    private String tradeArea;
    /**
     * 单据号
     */
    @NotNull(message = "单据号不能为空！")
    @XdoSize(max = 50, message = "单据号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("单据号")
    private String linkedNo;
    /**
     * 序号
     */
    @NotNull(message = "单据序号不能为空！")
    @Digits(integer = 30, fraction = 0, message = "单据序号必须为数字,整数位最大30位,小数最大0位!")
    @ApiModelProperty("单据序号")
    private String lineNo;
    /**
     * 计划行编码
     */
    @Digits(integer = 30, fraction = 0, message = "计划行编码必须为数字,整数位最大30位,小数最大0位!")
    @ApiModelProperty("计划行编码")
    private String planLineNo;

    /**
     * 保完税标记
     */
    @NotEmpty(message = "保完税标记不能为空！")
    @XdoSize(max = 1, message = "保完税标记长度不能超过1位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("保完税标记")
    private String bondMark;
    /**
     * 物料类型 I 料件  E 成品
     */
    @NotEmpty(message = "物料类型不能为空！")
    @XdoSize(max = 1, message = "物料类型长度不能超过1位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("物料类型")
    private String GMark;
    /**
     * 企业料号
     */
    @NotEmpty(message = "企业料号不能为空！")
    @XdoSize(max = 50, message = "企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业料号")
    private String facGNo;
    /**
     * 商品名称
     */
    @XdoSize(max = 255, message = "商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("商品名称")
    private String GName;
    /**
     * 数量
     */
    @Digits(integer = 10, fraction = 8, message = "数量必须为数字,整数位最大10位,小数最大8位!")
    @ApiModelProperty("数量")
    private BigDecimal qty;
    /**
     * 交易单位
     */
    @XdoSize(max = 3, message = "交易单位长度不能超过3位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("交易单位")
    private String unitErp;
    /**
     * 单价
     */
    @Digits(integer = 10, fraction = 8, message = "单价必须为数字,整数位最大10位,小数最大8位!")
    @ApiModelProperty("单价")
    private BigDecimal decPrice;
    /**
     * 总价
     */
    @Digits(integer = 13, fraction = 8, message = "总价必须为数字,整数位最大13位,小数最大8位!")
    @ApiModelProperty("总价")
    private BigDecimal decTotal;
    /**
     * 币制
     */
    @XdoSize(max = 3, message = "币制长度不能超过3位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 净重
     */
    @Digits(integer = 13, fraction = 8, message = "净重必须为数字,整数位最大13位,小数最大8位!")
    @ApiModelProperty("净重")
    private BigDecimal netWt;
    /**
     * 毛重
     */
    @Digits(integer = 13, fraction = 8, message = "毛重必须为数字,整数位最大13位,小数最大8位!")
    @ApiModelProperty("毛重")
    private BigDecimal grossWt;
    /**
     * SO号(销售订单号)
     */
    @NotEmpty(message = "SO号(销售订单号)不能为空！")
    @XdoSize(max = 50, message = "SO号(销售订单号)长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("SO号(销售订单号)")
    private String soNo;
    /**
     * SO行号(销售订单行号)
     */
    @NotEmpty(message = "SO行号(销售订单行号)不能为空！")
    @Digits(integer = 30, fraction = 0, message = "SO行号(销售订单行号)必须为数字,整数位最大30位,小数最大0位!")
    @ApiModelProperty("SO行号(销售订单行号)")
    private String soLineNo;
    /**
     * SO日期(销售订单日期)
     */
    @ApiModelProperty("SO日期(销售订单日期)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String soDate;
    /**
     * SO日期(销售订单日期)-开始
     */
    @ApiModelProperty("SO日期(销售订单日期)-开始")
    private String soDateFrom;
    /**
     * SO日期(销售订单日期)-结束
     */
    @ApiModelProperty("SO日期(销售订单日期)-结束")
    private String soDateTo;
    /**
     * 发票号
     */
    @XdoSize(max = 50, message = "发票号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("发票号")
    private String invoiceNo;
    /**
     * 送达方PO号
     */
    @XdoSize(max = 50, message = "送达方PO号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("送达方PO号")
    private String deliveryPoNo;
    /**
     * 送达方PO行号
     */
    @Digits(integer = 30, fraction = 0, message = "送达方PO行号必须为数字,整数位最大30位,小数最大0位!")
    @ApiModelProperty("送达方PO行号")
    private String deliveryPoLineNo;
    /**
     * 售达方PO号
     */
    @XdoSize(max = 50, message = "售达方PO号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("售达方PO号")
    private String soldPoNo;
    /**
     * 售达方PO行号
     */
    @Digits(integer = 30, fraction = 0, message = "售达方PO行号必须为数字,整数位最大30位,小数最大0位!")
    @ApiModelProperty("售达方PO行号")
    private String soldPoLineNo;
    /**
     * ERP创建时间
     */
    @ApiModelProperty("ERP创建时间")
    private Date lastModifyDate;
    /**
     * ERP创建时间-开始
     */
    @ApiModelProperty("ERP创建时间-开始")
    private String lastModifyDateFrom;
    /**
     * ERP创建时间-结束
     */
    @ApiModelProperty("ERP创建时间-结束")
    private String lastModifyDateTo;
    /**
     * 境内货源地
     */
    @XdoSize(max = 5, message = "境内货源地长度不能超过5位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("境内货源地")
    private String districtCode;
    /**
     * 卖场订单DI PO
     */
    @XdoSize(max = 50, message = "卖场订单DI PO长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("卖场订单DI PO")
    private String marketOrderNo;
    /**
     * 台/箱
     */
    @Digits(integer = 5, fraction = 0, message = "台/箱必须为数字,整数位最大5位,小数最大0位!")
    @ApiModelProperty("台/箱")
    private String containerNum;
    /**
     * 件数
     */
    @Digits(integer = 18, fraction = 0, message = "件数必须为数字,整数位最大18位,小数最大0位!")
    @ApiModelProperty("件数")
    private BigDecimal packNum;
    /**
     * 体积
     */
    @Digits(integer = 11, fraction = 5, message = "体积必须为数字,整数位最大11位,小数最大5位!")
    @ApiModelProperty("体积")
    private BigDecimal volume;
    /**
     * 拒绝原因
     */
    @XdoSize(max = 50, message = "拒绝原因长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拒绝原因")
    private String refusedReason;
    /**
     * 订单计划开船日
     */
    @ApiModelProperty("订单计划开船日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String cietdDate;
    /**
     * 订单订舱开船日-开始
     */
    @ApiModelProperty("订单订舱开船日-开始")
    private String cietdDateFrom;
    /**
     * 订单订舱开船日-结束
     */
    @ApiModelProperty("订单订舱开船日-结束")
    private String cietdDateTo;
    /**
     * 客户料号
     */
    @ApiModelProperty("客户料号")
    private String customerGNo;
    /**
     * 用户
     */
    @ApiModelProperty("用户")
    private String tempOwner;
    /**
     * 标识
     */
    @ApiModelProperty("标识")
    private Integer tempMark;
    /**
     * 提示
     */
    @ApiModelProperty("提示")
    private String tempRemark;
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    private int tempIndex;
}
