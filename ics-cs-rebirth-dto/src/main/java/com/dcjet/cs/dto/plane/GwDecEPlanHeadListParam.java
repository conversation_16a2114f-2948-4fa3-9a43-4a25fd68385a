package com.dcjet.cs.dto.plane;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021-1-25
 */
@Setter
@Getter
@ApiModel(value = "发货订舱柜型信息传入参数")
public class GwDecEPlanHeadListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;
    /**
     * 制单人
     */
    @ApiModelProperty("制单人")
    private String insertUser;
    /**
     * 状态，0：暂存、1 发货单号已生成、2 发票号已生成、3 已提取
     */
    @XdoSize(max = 1, message = "状态长度不能超过1位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("状态，0：暂存、1 发货单号已生成、2 发票号已生成、3 已提取")
    private String status;
    /**
     * 柜型
     */
    @XdoSize(max = 50, message = "柜型长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("柜型")
    private String cabinetType;
    /**
     * 柜号
     */
    @XdoSize(max = 50, message = "柜号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("柜号")
    private String containerNo;
    /**
     * 拖柜日期
     */
    @ApiModelProperty("拖柜日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cabinetDate;

    /**
     * 拖柜日期-开始
     */
    @ApiModelProperty("拖柜日期-开始")
    private String cabinetDateFrom;
    /**
     * 拖柜日期-结束
     */
    @ApiModelProperty("拖柜日期-结束")
    private String cabinetDateTo;
    /**
     * 分票号
     */
    @XdoSize(max = 10, message = "分票号长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("分票号")
    private String branchNo;
    /**
     * 拖柜备注
     */
    @XdoSize(max = 255, message = "拖柜备注长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拖柜备注")
    private String note;
    /**
     * 订舱流水号
     */
    @XdoSize(max = 32, message = "订舱流水号长度不能超过32位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("订舱流水号")
    private String bookingListNo;
    /**
     * 申请订舱表头SID
     */
    @ApiModelProperty("申请订舱表头SID")
    private String headId;

    /**
     * 发货单号
     */
    @XdoSize(max = 50, message = "发货单号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("发货单号")
    private String billNo;
    /**
     * 发票号
     */
    @XdoSize(max = 50, message = "发票号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("发票号")
    private String invoiceNo;
    /**
     * 货代编码
     */
    @XdoSize(max = 50, message = "货代编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("货代编码")
    private String forwardCode;
    /**
     * 货代名称
     */
    @XdoSize(max = 200, message = "货代名称长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("货代名称")
    private String forwardName;
    /**
     * BookingID
     */
    @XdoSize(max = 50, message = "BookingID长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("BookingID")
    private String bookingId;
    /**
     * 分提运单(货代提单号)
     */
    @XdoSize(max = 50, message = "分提运单(货代提单号)长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("分提运单(货代提单号)")
    private String hawb;
    /**
     * 放舱ETD
     */
    @ApiModelProperty("放舱ETD")
    private Date etd;
    /**
     * 放舱ETD-开始
     */
    @ApiModelProperty("放舱ETD-开始")
    private String etdFrom;
    /**
     * 放舱ETD-结束
     */
    @ApiModelProperty("放舱ETD-结束")
    private String etdTo;
    /**
     * 柜型柜量·
     */
    @XdoSize(max = 500, message = "柜型柜量长度不能超过500位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("柜型柜量")
    private String ctn;
    /**
     * 单证备注
     */
    @XdoSize(max = 100, message = "单证备注长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("单证备注")
    private String remark;
    /**
     * 运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递
     */
    @XdoSize(max = 6, message = "运输方式长度不能超过6位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递")
    private String trafMode;
    /**
     * 运输方式名称
     */
    @XdoSize(max = 200, message = "运输方式名称长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("运输方式名称")
    private String trafName;
    /**
     * 订单计划开船日
     */
    @ApiModelProperty("订单计划开船日")
    private Date cietdDate;
    /**
     * 订单计划开船日-开始
     */
    @ApiModelProperty("订单计划开船日-开始")
    private String cietdDateFrom;
    /**
     * 订单计划开船日-结束
     */
    @ApiModelProperty("订单计划开船日-结束")
    private String cietdDateTo;
    /**
     * 订单订舱开船日
     */
    @ApiModelProperty("订单订舱开船日")
    private Date ccetdDate;
    /**
     * 订单订舱开船日-开始
     */
    @ApiModelProperty("订单订舱开船日-开始")
    private String ccetdDateFrom;
    /**
     * 订单订舱开船日-结束
     */
    @ApiModelProperty("订单订舱开船日-结束")
    private String ccetdDateTo;
    /**
     * 最终目的国
     */
    @XdoSize(max = 10, message = "最终目的国长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("最终目的国")
    private String destinationCountry;
    /**
     * 目的港
     */
    @XdoSize(max = 100, message = "目的港长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("目的港")
    private String destPort;
    /**
     * 客户代码(售达方代码)
     */
    @XdoSize(max = 100, message = "客户代码(售达方代码)长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("客户代码(售达方代码)")
    private String clientCode;
    /**
     * 送达方编码
     */
    @XdoSize(max = 50, message = "送达方编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("送达方编码")
    private String deliveryNo;
    /**
     * 贸易条款
     */
    @XdoSize(max = 5, message = "贸易条款长度不能超过5位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("贸易条款")
    private String tradeTerms;
    /**
     * 贸易条款(地区)
     */
    @XdoSize(max = 50, message = "贸易条款(地区)长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("贸易条款(地区)")
    private String tradeArea;
    /**
     * 单据号
     */
    @XdoSize(max = 50, message = "单据号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("单据号")
    private String linkedNo;
    /**
     * 序号
     */
    @Digits(integer = 30, fraction = 0, message = "序号必须为数字,整数位最大30位,小数最大0位!")
    @ApiModelProperty("序号")
    private Integer lineNo;
    /**
     * 计划行编码
     */
    @Digits(integer = 30, fraction = 0, message = "计划行编码必须为数字,整数位最大30位,小数最大0位!")
    @ApiModelProperty("计划行编码")
    private Integer planLineNo;
    /**
     * 保完税标记
     */
    @NotEmpty(message = "保完税标记不能为空！")
    @XdoSize(max = 30, message = "保完税标记长度不能超过30位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("保完税标记")
    private String bondMark;
    /**
     * 物料类型 I 料件  E 成品
     */
    @NotEmpty(message = "物料类型不能为空！")
    @XdoSize(max = 30, message = "物料类型长度不能超过30位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("物料类型 I 料件  E 成品")
    @JsonProperty("gMark")
    private String gMark;
    /**
     * 企业料号
     */
    @NotEmpty(message = "企业料号不能为空！")
    @XdoSize(max = 50, message = "企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业料号")
    private String facGNo;
    /**
     * 商品名称
     */
    @XdoSize(max = 255, message = "商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("商品名称")
    private String GName;
    /**
     * 数量
     */
    @Digits(integer = 10, fraction = 8, message = "数量必须为数字,整数位最大10位,小数最大8位!")
    @ApiModelProperty("数量")
    private BigDecimal qty;
    /**
     * 交易单位
     */
    @XdoSize(max = 10, message = "交易单位长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("交易单位")
    private String unitErp;
    /**
     * 单价
     */
    @NotNull(message = "单价不能为空！")
    @Digits(integer = 10, fraction = 8, message = "单价必须为数字,整数位最大10位,小数最大8位!")
    @ApiModelProperty("单价")
    private BigDecimal decPrice;
    /**
     * 总价
     */
    @Digits(integer = 13, fraction = 8, message = "总价必须为数字,整数位最大13位,小数最大8位!")
    @ApiModelProperty("总价")
    private BigDecimal decTotal;
    /**
     * 币制
     */
    @XdoSize(max = 10, message = "币制长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 净重
     */
    @Digits(integer = 13, fraction = 8, message = "净重必须为数字,整数位最大13位,小数最大8位!")
    @ApiModelProperty("净重")
    private BigDecimal netWt;
    /**
     * 毛重
     */
    @Digits(integer = 13, fraction = 8, message = "毛重必须为数字,整数位最大13位,小数最大8位!")
    @ApiModelProperty("毛重")
    private BigDecimal grossWt;
    /**
     * SO号(销售订单号)
     */
    @XdoSize(max = 50, message = "SO号(销售订单号)长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("SO号(销售订单号)")
    private String soNo;
    /**
     * SO行号(销售订单行号)
     */
    @Digits(integer = 30, fraction = 0, message = "SO行号(销售订单行号)必须为数字,整数位最大30位,小数最大0位!")
    @ApiModelProperty("SO行号(销售订单行号)")
    private Integer soLineNo;
    /**
     * SO日期(销售订单日期)
     */
    @ApiModelProperty("SO日期(销售订单日期)")
    private Date soDate;
    /**
     * SO日期(销售订单日期)-开始
     */
    @ApiModelProperty("SO日期(销售订单日期)-开始")
    private String soDateFrom;
    /**
     * SO日期(销售订单日期)-结束
     */
    @ApiModelProperty("SO日期(销售订单日期)-结束")
    private String soDateTo;
    /**
     * 送达方PO号
     */
    @XdoSize(max = 50, message = "送达方PO号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("送达方PO号")
    private String deliveryPoNo;
    /**
     * 送达方PO行号
     */
    @Digits(integer = 30, fraction = 0, message = "送达方PO行号必须为数字,整数位最大30位,小数最大0位!")
    @ApiModelProperty("送达方PO行号")
    private Integer deliveryPoLineNo;
    /**
     * 售达方PO号
     */
    @XdoSize(max = 50, message = "售达方PO号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("售达方PO号")
    private String soldPoNo;
    /**
     * 售达方PO行号
     */
    @Digits(integer = 30, fraction = 0, message = "售达方PO行号必须为数字,整数位最大30位,小数最大0位!")
    @ApiModelProperty("售达方PO行号")
    private Integer soldPoLineNo;
    /**
     * ERP创建时间
     */
    @ApiModelProperty("ERP创建时间")
    private Date lastModifyDate;
    /**
     * ERP创建时间-开始
     */
    @ApiModelProperty("ERP创建时间-开始")
    private String lastModifyDateFrom;
    /**
     * ERP创建时间-结束
     */
    @ApiModelProperty("ERP创建时间-结束")
    private String lastModifyDateTo;
    /**
     * 境内货源地
     */
    @XdoSize(max = 5, message = "境内货源地长度不能超过5位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("境内货源地")
    private String districtCode;
    /**
     * 卖场订单DI PO
     */
    @XdoSize(max = 50, message = "卖场订单DI PO长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("卖场订单DI PO")
    private String marketOrderNo;
    /**
     * 台/箱
     */
    @Digits(integer = 5, fraction = 0, message = "台/箱必须为数字,整数位最大5位,小数最大0位!")
    @ApiModelProperty("台/箱")
    private Integer containerNum;
    /**
     * 件数
     */
    @Digits(integer = 18, fraction = 0, message = "件数必须为数字,整数位最大18位,小数最大0位!")
    @ApiModelProperty("件数")
    private BigDecimal packNum;
    /**
     * 体积
     */
    @Digits(integer = 11, fraction = 5, message = "体积必须为数字,整数位最大11位,小数最大5位!")
    @ApiModelProperty("体积")
    private BigDecimal volume;
    /**
     * 拒绝原因
     */
    @XdoSize(max = 50, message = "拒绝原因长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拒绝原因")
    private String refusedReason;
    /**
     * 客户料号
     */
    @XdoSize(max = 50, message = "客户料号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("客户料号")
    private String customerGNo;
    /**
     * plan表SID
     */
    @XdoSize(max = 50, message = "plan表SID长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("plan表SID")
    private String listSid;

    /**
     * 货代单号
     */
    @XdoSize(max = 50, message = "货代单号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("货代单号")
    private String forwardNo;
    @ApiModelProperty("shipment_id")
    private String shipmentId;
}
