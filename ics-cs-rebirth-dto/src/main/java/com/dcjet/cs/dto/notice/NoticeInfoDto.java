package com.dcjet.cs.dto.notice;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2021-5-19
 */
@ApiModel(value = "预警通知设置dto")
@Setter @Getter
public class NoticeInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键SID
      */
    @ApiModelProperty("主键SID")
	private  String sid;
	/**
      * 业务类型ID
      */
    @ApiModelProperty("业务类型Code")
	private  String bizCode;
	/**
      * 通知类型 0关闭 1实时
      */
    @ApiModelProperty("通知类型 0关闭 1实时")
	private  String noticeType;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String emailTitle;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String emailContent;
	/**
      * 插入时间
      */
    @ApiModelProperty("插入时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 插入用户
      */
    @ApiModelProperty("插入用户")
	private  String insertUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 更新用户
      */
    @ApiModelProperty("更新用户")
	private  String updateUser;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 制单人姓名
      */
    @ApiModelProperty("制单人姓名")
	private  String insertUserName;
	/**
      * 修改人姓名
      */
    @ApiModelProperty("修改人姓名")
	private  String updateUserName;

	/**
	 * 收件人
	 */
	@ApiModelProperty("收件人")
	private  String recipient;
	/**
	 * 抄送人
	 */
	@ApiModelProperty("抄送人")
	private  String carbonCopy;
	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String note;

	@ApiModelProperty("业务类型名称")
	private String bizName;
}
