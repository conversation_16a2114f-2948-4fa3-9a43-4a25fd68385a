package com.dcjet.cs.dto.mid;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2020-6-16
 */
@ApiModel(value = "中期核查进口关联核对返回信息")
@Setter @Getter
public class MidDecIRelCheckDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private String sid;
	/**
      * 物料类型标识
      */
    @ApiModelProperty("物料类型标识")
	private String GMark;
	/**
      * 保完税标志
      */
    @ApiModelProperty("保完税标志")
	private String bondMark;
	/**
      * 企业料号
      */
    @ApiModelProperty("企业料号")
	private String facGNo;
	/**
      * 备案料号
      */
    @ApiModelProperty("备案料号")
	private String copGNo;
	/**
      * 入库关联号
      */
    @ApiModelProperty("入库关联号")
	private String linkedNo;
	/**
      * 申报数量
      */
    @ApiModelProperty("申报数量")
	private BigDecimal qty;
	/**
      * 入库数量
      */
    @ApiModelProperty("入库数量")
	private BigDecimal whQty;
	/**
      * 转换申报单位数量
      */
    @ApiModelProperty("转换申报单位数量")
	private BigDecimal qtyConvert;
	/**
	 * 差异
	 */
	@ApiModelProperty("差异")
	private BigDecimal diff;
	/**
	 * 业务排序字段
	 */
	@ApiModelProperty("业务排序字段")
	private BigDecimal orderId;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private String insertUser;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date insertTime;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateTime;
	/**
      * 制单人姓名
      */
    @ApiModelProperty("制单人姓名")
	private String insertUserName;
	/**
      * 修改人姓名
      */
    @ApiModelProperty("修改人姓名")
	private String updateUserName;

	/**
	 * 申报开始日期
	 */
	@ApiModelProperty("申报开始日期")
	private Date declareDateFrom;
	/**
	 * 申报结束日期
	 */
	@ApiModelProperty("申报结束日期")
	private Date declareDateTo;
	/**
	 * 出库开始日期
	 */
	@ApiModelProperty("出库开始日期")
	private Date whDateFrom;
	/**
	 * 出库结束日期
	 */
	@ApiModelProperty("出库结束日期")
	private Date whDateTo;
}
