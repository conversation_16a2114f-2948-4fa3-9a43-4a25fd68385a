package com.dcjet.cs.dto.repair;

import com.dcjet.cs.dto.base.BasicParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@ApiModel(value = "提取数据参数")
@Setter
@Getter
public class RepairEIListExtractionParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("表头ID")
    @NotEmpty(message = "{表头主键不能为空}")
    private String headId;

    @ApiModelProperty(value = "单据内部编号")
    private String emsListNo;

    @ApiModelProperty(value = "报关单号")
    private String entryNo;

    @ApiModelProperty(value = "企业料号")
    private String facGNo;

    @ApiModelProperty(value = "商品编码")
    private String codeTS;

    @ApiModelProperty(value = "商品名称")
    private String gName;


    @ApiModelProperty("提取数据")
    private List<RepairEIListExtractionDataParam> data;


    /***
     * 企业编码
     */
    @JsonIgnore
    private String tradeCode;

}
