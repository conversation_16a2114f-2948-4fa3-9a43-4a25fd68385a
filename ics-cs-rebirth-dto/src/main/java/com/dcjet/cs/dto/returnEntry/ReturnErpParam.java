package com.dcjet.cs.dto.returnEntry;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @description:
 * @author: WJ
 * @createDate: 2020/7/22 13:25
 */
@Setter
@Getter
public class ReturnErpParam {

    /**
     * 业务类型,I_ENTRY:进口 E_ENTRY:出口
     */
    private String businessType;
    /**
     * 月数
     */
    private String month;
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private String startTime;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private String endTime;

    private String tradeCode;

    /**
     * 锁定标志
     */
    private  String lockMark;
}
