package com.dcjet.cs.dto.deep;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 *
 * <AUTHOR>
 * @date: 2019-11-22
 */
@Setter @Getter
@ApiModel(value = "SjgOutBillListTempParam")
public class SjgOutBillListTempParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 转出企业料号
     */
	@NotEmpty(message="{转出企业料号不能为空！}")
	@XdoSize(max = 50, message = "{转出企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出企业料号")
	private  String facGNoOut;
	/**
     * 转出备案料号
     */
	@XdoSize(max = 32, message = "{转出备案料号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出备案料号")
	private  String copGNoOut;
	/**
     * 转出备案序号
     */
	@Digits(integer = 11, fraction = 0, message = "{转出备案序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("转出备案序号")
//	@JsonProperty("gNoOut")
	private  Integer gNoOut;
	/**
     * 数量
     */
	@NotNull(message="{数量不能为空！}")
	@Digits(integer = 14, fraction = 5, message = "{数量必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("数量")
	private  BigDecimal decQty;
	/**
     * 单价
     */
	@NotNull(message="{单价不能为空！}")
	@Digits(integer = 14, fraction = 5, message = "{单价必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("单价")
	private  BigDecimal decPrice;
	/**
     * 总价
     */
	@NotNull(message="{总价不能为空！}")
	@Digits(integer = 14, fraction = 5, message = "{总价必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("总价")
	private  BigDecimal decTotal;
	/**
     * 转入备案序号
     */
	@Digits(integer = 11, fraction = 0, message = "{转入备案序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("转入备案序号")
//	@JsonProperty("gNoIn")
	private  Integer gNoIn;
	/**
     * 转入备案料号
     */
	@XdoSize(max = 32, message = "{转入备案料号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转入备案料号")
	private  String copGNoIn;
	/**
     * 申报计量单位
     */
	@XdoSize(max = 3, message = "{申报计量单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报计量单位")
	private  String unit;
	/**
     * 币制
     */
	@NotEmpty(message="{币制不能为空！}")
	@XdoSize(max = 3, message = "{币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("币制")
	private  String curr;
	/**
     * 报关单序号
     */
	@Digits(integer = 5, fraction = 0, message = "{报关单序号必须为数字,整数位最大5位,小数最大0位!}")
	@ApiModelProperty("报关单序号")
	private  Integer entryGNo;
	/**
     * 匹配结果，0：未匹配，1：已匹配
     */
	@Digits(integer = 1, fraction = 0, message = "{匹配结果，0：未匹配，1：已匹配必须为数字,整数位最大1位,小数最大0位!}")
	@ApiModelProperty("匹配结果，0：未匹配，1：已匹配")
	private  Integer matchResult;
	/**
     * 深加工转出料件表头的SID
     */
	@XdoSize(max = 40, message = "{深加工转出料件表头的SID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("深加工转出料件表头的SID")
	private  String headId;
	/**
     * 录入顺序或导入顺序号
     */
	@Digits(integer = 6, fraction = 0, message = "{录入顺序或导入顺序号必须为数字,整数位最大6位,小数最大0位!}")
	@ApiModelProperty("录入顺序或导入顺序号")
	private  Integer serialNo;
	/**
     * 收;发货日期
     */
	@ApiModelProperty("收;发货日期")
	private  Date arrivalDate;
	/**
    * 收;发货日期-开始
    */
	@ApiModelProperty("收;发货日期-开始")
	private String arrivalDateFrom;
	/**
    * 收;发货日期-结束
    */
	@ApiModelProperty("收;发货日期-结束")
    private String arrivalDateTo;
	/**
     * SO料号
     */
	@XdoSize(max = 100, message = "{SO料号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("SO料号")
	private  String soNo;
	/**
     * 客户编码
     */
	@XdoSize(max = 50, message = "{客户编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户编码")
	private  String supplierCode;
	/**
     * 客户名称
     */
	@XdoSize(max = 255, message = "{客户名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户名称")
	private  String supplierName;
	/**
     * 出入库编号
     */
	@XdoSize(max = 50, message = "{出入库编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("出入库编号")
	private  String listNoIo;
	/**
     * 发货单对应序号(行号)
     */
	@Digits(integer = 5, fraction = 0, message = "{发货单对应序号(行号)必须为数字,整数位最大5位,小数最大0位!}")
	@ApiModelProperty("发货单对应序号(行号)")
	private  Integer lineNo;
	/**
     * 成本中心
     */
	@XdoSize(max = 50, message = "{成本中心长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("成本中心")
	private  String costCenter;
	/**
     * 数据来源  1：界面新增  2：界面导入  3：接口提取
     */
	@XdoSize(max = 4, message = "{数据来源  1：界面新增  2：界面导入  3：接口提取长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("数据来源  1：界面新增  2：界面导入  3：接口提取")
	private  String dataSource;
	/**
     * 对应采购对账表中的SID
     */
	@XdoSize(max = 50, message = "{对应采购对账表中的SID长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("对应采购对账表中的SID")
	private  String businessId;
	/**
     * 制单人姓名
     */
	@XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("制单人姓名")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
     * ERP交易单位
     */
	@XdoSize(max = 20, message = "{ERP交易单位长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("ERP交易单位")
	private  String unitErp;
	/**
     * 单耗版本号
     */
	@NotEmpty(message="{单耗版本号不能为空！}")
	@XdoSize(max = 8, message = "{单耗版本号长度不能超过8位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("单耗版本号")
	private  String bomVersion;
	/**
     * 导入用户
     */
	@XdoSize(max = 36, message = "{导入用户长度不能超过36位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("导入用户")
	private  String tempOwner;
	/**
     * 校验标志
     */
	@Digits(integer = 4, fraction = 0, message = "{校验标志必须为数字,整数位最大4位,小数最大0位!}")
	@ApiModelProperty("校验标志")
	private  Integer tempFlag;
	/**
     * 错误信息
     */
	@XdoSize(max = 2000, message = "{错误信息长度不能超过2000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("错误信息")
	private  String tempRemark;
}
