package com.dcjet.cs.dto.valid;

import org.springframework.beans.factory.annotation.Value;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.nio.charset.Charset;

/**
 * @description:
 * @author: WJ
 * @createDate: 2021/7/21 17:36
 */
public class LengthValidator implements ConstraintValidator<ValidLength, String> {
    @Value("${dc.db.charset:GBK}")
    private String charset;

    private ValidLength size;

    @Override
    public void initialize(ValidLength constraintAnnotation) {
        this.size = constraintAnnotation;
    }

    /**
     * Implements the validation logic.
     * The state of {@code value} must not be altered.
     * <p>
     * This method can be accessed concurrently, thread-safety must be ensured
     * by the implementation.
     *
     * @param value   object to validate
     * @param context context in which the constraint is evaluated
     * @return {@code false} if {@code value} does not pass the constraint
     */
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || "".equals(value)) {
            return true;
        }
        int length;
        try {
            length = value.getBytes(charset).length;
        } catch (Exception e) {
            try {
                length = value.getBytes(Charset.forName("GBK")).length;
            } catch (Exception e1){
                // 如果失败，虚拟机就直接狗带了，不需要了
                length = value.getBytes().length;
            }
        }
        return (length > size.max() || length < size.min()) ? false : true;
    }
}
