package com.dcjet.cs.dto.gwLvCitesHp1;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.beans.Transient;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2022-4-13
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class GwLvCitesHpParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 状态
     */
	@ApiModelProperty("状态")
	private  String isOpen;

	private  String dataSource;

	private String element;

	private String isSendedExpireWarn;

	private String isSendedLapseWarn;

	private String elementNum;
	private String useWay;
	private String batchType;
	private String englishName;
	private String remark;
	private String formulaNo;

	private String isExpired;

	private String brandCode;



	/**
     * 企业编码
     */
	@XdoSize(max = 10, message = "企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * HP号码
     */
	@NotEmpty(message="HP号码不能为空！")
	@XdoSize(max = 150, message = "HP号码长度不能超过150位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("HP号码")
	private  String hpCode;
	/**
     * 批件到期日期
     */
	@ApiModelProperty("批件到期日期")
	private  Date hpMaturityDate;
	/**
     * HP中文名
     */
	@NotEmpty(message="HP中文名不能为空！")
	@XdoSize(max = 200, message = "HP中文名长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("HP中文名")
	private  String hpName;
	/**
     * 原产国
     */
	@NotEmpty(message="原产国不能为空！")
	@XdoSize(max = 50, message = "原产国长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("原产国")
	private  String originCountry;
	/**
     * 涉濒危
     */
	@NotEmpty(message="涉濒危不能为空！")
	@ApiModelProperty("涉濒危")
	private  String isCites;
	/**
     * 物种中文名
     */
	@ApiModelProperty("物种中文名")
	private  String speciesName;
	/**
     * 拉丁学名
     */
	@XdoSize(max = 100, message = "拉丁学名长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拉丁学名")
	private  String scientificName;
	/**
     * 单位濒危含量
     */
	@Digits(integer = 7, fraction = 4, message = "单位濒危含量必须为数字,整数位最大7位,小数最大4位!")
	@ApiModelProperty("单位濒危含量")
	private  BigDecimal riskQuantity;
	/**
     * 涉濒危净含量/套
     */
	@Digits(integer = 7, fraction = 4, message = "涉濒危净含量/套必须为数字,整数位最大7位,小数最大4位!")
	@ApiModelProperty("涉濒危净含量/套")
	private  BigDecimal riskSuitQuantity;

	private String firstBatch;

	/**
	 * 涉进口原产地证 0-否，1-是
	 */
	@XdoSize(max = 1, message = "涉进口原产地证 0-否，1-是长度不能超过1位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("涉进口原产地证 0-否，1-是")
	private  String involveCradleProof;
	/**
	 * 物种中文名1（COO）
	 */
	@XdoSize(max = 100, message = "物种中文名1（COO）长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("物种中文名1（COO）")
	private  String speciesName1;
	/**
	 * 拉丁学名1（COO）
	 */
	@XdoSize(max = 100, message = "拉丁学名1（COO）长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拉丁学名1（COO）")
	private  String scientificName1;
	/**
	 * 单位濒危含量1（COO）
	 */
	@Digits(integer = 3, fraction = 7, message = "单位濒危含量1（COO）必须为数字,整数位最大3位,小数最大7位!")
	@ApiModelProperty("单位濒危含量1（COO）")
	private  BigDecimal riskQuantity1;
	/**
	 * 密度1（COO）
	 */
	@Digits(integer = 5, fraction = 5, message = "密度1（COO）必须为数字,整数位最大5位,小数最大5位!")
	@ApiModelProperty("密度1（COO）")
	private  BigDecimal density1;
	/**
	 * 物种中文名2（COO）
	 */
	@XdoSize(max = 100, message = "物种中文名2（COO）长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("物种中文名2（COO）")
	private  String speciesName2;
	/**
	 * 拉丁学名2（COO）
	 */
	@XdoSize(max = 100, message = "拉丁学名2（COO）长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拉丁学名2（COO）")
	private  String scientificName2;
	/**
	 * 单位濒危含量2（COO）
	 */
	@Digits(integer = 3, fraction = 7, message = "单位濒危含量2（COO）必须为数字,整数位最大3位,小数最大7位!")
	@ApiModelProperty("单位濒危含量2（COO）")
	private  BigDecimal riskQuantity2;
	/**
	 * 密度2（COO）
	 */
	@Digits(integer = 5, fraction = 5, message = "密度2（COO）必须为数字,整数位最大5位,小数最大5位!")
	@ApiModelProperty("密度2（COO）")
	private  BigDecimal density2;
	/**
	 * 物种中文名3（COO）
	 */
	@XdoSize(max = 100, message = "物种中文名3（COO）长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("物种中文名3（COO）")
	private  String speciesName3;
	/**
	 * 拉丁学名3（COO）
	 */
	@XdoSize(max = 100, message = "拉丁学名3（COO）长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拉丁学名3（COO）")
	private  String scientificName3;
	/**
	 * 单位濒危含量3（COO）
	 */
	@Digits(integer = 3, fraction = 7, message = "单位濒危含量3（COO）必须为数字,整数位最大3位,小数最大7位!")
	@ApiModelProperty("单位濒危含量3（COO）")
	private  BigDecimal riskQuantity3;
	/**
	 * 密度3（COO）
	 */
	@Digits(integer = 5, fraction = 5, message = "密度3（COO）必须为数字,整数位最大5位,小数最大5位!")
	@ApiModelProperty("密度3（COO）")
	private  BigDecimal density3;
	/**
	 * 物种中文名4（COO）
	 */
	@XdoSize(max = 100, message = "物种中文名4（COO）长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("物种中文名4（COO）")
	private  String speciesName4;
	/**
	 * 拉丁学名4（COO）
	 */
	@XdoSize(max = 100, message = "拉丁学名4（COO）长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拉丁学名4（COO）")
	private  String scientificName4;
	/**
	 * 单位濒危含量4（COO）
	 */
	@Digits(integer = 3, fraction = 7, message = "单位濒危含量4（COO）必须为数字,整数位最大3位,小数最大7位!")
	@ApiModelProperty("单位濒危含量4（COO）")
	private  BigDecimal riskQuantity4;
	/**
	 * 密度4（COO）
	 */
	@Digits(integer = 5, fraction = 5, message = "密度4（COO）必须为数字,整数位最大5位,小数最大5位!")
	@ApiModelProperty("密度4（COO）")
	private  BigDecimal density4;
	/**
	 * 物种中文名5（COO）
	 */
	@XdoSize(max = 100, message = "物种中文名5（COO）长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("物种中文名5（COO）")
	private  String speciesName5;
	/**
	 * 拉丁学名5（COO）
	 */
	@XdoSize(max = 100, message = "拉丁学名5（COO）长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拉丁学名5（COO）")
	private  String scientificName5;
	/**
	 * 单位濒危含量5（COO）
	 */
	@Digits(integer = 3, fraction = 7, message = "单位濒危含量5（COO）必须为数字,整数位最大3位,小数最大7位!")
	@ApiModelProperty("单位濒危含量5（COO）")
	private  BigDecimal riskQuantity5;
	/**
	 * 密度5（COO）
	 */
	@Digits(integer = 5, fraction = 5, message = "密度5（COO）必须为数字,整数位最大5位,小数最大5位!")
	@ApiModelProperty("密度5（COO）")
	private  BigDecimal density5;


}
