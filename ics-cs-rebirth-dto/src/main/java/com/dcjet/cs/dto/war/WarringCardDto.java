package com.dcjet.cs.dto.war;
import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2019-4-19
 */
@ApiModel(value = "WarringCardDto返回信息")
@Setter @Getter
public class WarringCardDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
      * 序号
      */
    @ApiModelProperty("序号")
	private BigDecimal serialNo;
	/**
      * 单据号
      */
    @ApiModelProperty("单据号")
	private  String documentNo;
	/**
      * 证件名称
      */
    @ApiModelProperty("证件名称")
	private  String documentName;
	/**
      * 有效期起
      */
    @ApiModelProperty("有效期起")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date dateStart;
	/**
      * 有效期止
      */
    @ApiModelProperty("有效期止")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date dateEnd;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 发证机关
      */
    @ApiModelProperty("发证机关")
	private  String authority;
	/**
      * 持卡人
      */
    @ApiModelProperty("持卡人")
	private  String cardHolder;
	/**
      * 负责人
      */
    @ApiModelProperty("负责人")
	private  String chargePerson;
	/**
      * 状态 0 正常 1 注销
      */
    @ApiModelProperty("状态 0 正常 1 注销")
	private  String status;
	/**
      * 所属企业编码
      */
    @ApiModelProperty("所属企业编码")
	private  String tradeCode;
	/**
	 * 超期天数
	 */
	@ApiModelProperty("超期天数")
	private String days;

	/**
	 * 预警状态
	 */
	@ApiModelProperty("预警状态")
	private String dataStatus;
	/**
	 * 预警状态名称
	 */
	@ApiModelProperty("预警状态名称")
	private String dataStatusName;
	/**
	 * 预警天数
	 */
	@Digits(integer = 11, fraction = 0, message = "{预警天数必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("预警天数")
	private  Integer warringDays;
}
