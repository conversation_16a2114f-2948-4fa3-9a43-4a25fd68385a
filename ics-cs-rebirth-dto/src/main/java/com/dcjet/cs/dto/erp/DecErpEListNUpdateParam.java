package com.dcjet.cs.dto.erp;

import com.dcjet.cs.dto.base.BasicImportParam;
import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter @Getter
@ApiModel(value = "出口表体传入参数")
public class DecErpEListNUpdateParam extends BasicImportParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 表头SID
     */
	@XdoSize(max = 40, message = "{表头SID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("表头SID")
	private  String headId;
	/**
     * 单据内部编号
     */
	@XdoSize(max = 64, message = "{单据内部编号长度不能超过64位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("单据内部编号")
	private  String emsListNo;
	/**
     * 流水号
     */
	@Digits(integer = 11, fraction = 0, message = "{流水号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("流水号")
	@ExcelColumn(name = "顺序号")
	private BigDecimal serialNo;
	/**
     * 备案序号
     */
	@Digits(integer = 11, fraction = 0, message = "{备案序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("备案序号")
	private BigDecimal GNo;
	/**
     * 备案料号
     */
	@XdoSize(max = 100, message = "{备案料号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案料号")
	private  String copGNo;
	/**
     * 商品编码
     */
	@XdoSize(max = 10, message = "{商品编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("商品编码")
	private  String codeTS;
	/**
     * 商品名称
     */
	@XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("商品名称")
	@ExcelColumn(name = "商品名称")
	private  String GName;
	/**
     * 申报规格型号
     */
	@XdoSize(max = 255, message = "{申报规格型号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报规格型号")
	@ExcelColumn(name = "申报规格型号")
	private  String GModel;
	/**
     * 计量单位
     */
	@XdoSize(max = 6, message = "{计量单位长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("计量单位")
	private  String unit;
	/**
     * 法一单位
     */
	@XdoSize(max = 3, message = "{法一单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("法一单位")
	private  String unit1;
	/**
     * 法二单位
     */
	@XdoSize(max = 3, message = "{法二单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("法二单位")
	private  String unit2;
	/**
     * 原产国
     */
	@XdoSize(max = 3, message = "{原产国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("原产国")
	@ExcelColumn(name = "原产国", converter = "pcodeExcelConverter", tag = "COUNTRY_OUTDATED", IOC = true)
	private  String originCountry;
	/**
     * 最终目的国
     */
	@XdoSize(max = 3, message = "{最终目的国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("最终目的国")
	@ExcelColumn(name = "目的国", converter = "pcodeExcelConverter", tag = "COUNTRY_OUTDATED", IOC = true)
	private  String destinationCountry;
	/**
     * 申报单价
     */
	@Digits(integer = 10, fraction = 5, message = "{申报单价必须为数字,整数位最大10位,小数最大5位!}")
	@ApiModelProperty("申报单价")
	@ExcelColumn(name = "申报单价")
	private  BigDecimal decPrice;
	/**
     * 申报总价
     */
	@Digits(integer = 12, fraction = 4, message = "{申报总价必须为数字,整数位最大12位,小数最大4位!}")
	@ApiModelProperty("申报总价")
	@ExcelColumn(name = "申报总价")
	private  BigDecimal decTotal;
	/**
     * 美元统计总金额
     */
	@Digits(integer = 11, fraction = 5, message = "{美元统计总金额必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("美元统计总金额")
	private  BigDecimal usdPrice;
	/**
     * 币制
     */
	@XdoSize(max = 3, message = "{币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("币制")
	@ExcelColumn(name = "币制", IOC = true, tag = "CURR_OUTDATED", converter = "pcodeExcelConverter")
	private  String curr;
	/**
     * 法一数量
     */
	@Digits(integer = 11, fraction = 5, message = "{法一数量必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("法一数量")
	@ExcelColumn(name = "法一数量")
	private  BigDecimal qty1;
	/**
     * 法二数量
     */
	@Digits(integer = 11, fraction = 5, message = "{法二数量必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("法二数量")
	@ExcelColumn(name = "法二数量")
	private  BigDecimal qty2;
	/**
     * 重量比例因子
     */
	@Digits(integer = 11, fraction = 5, message = "{重量比例因子必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("重量比例因子")
	private  BigDecimal factorWt;
	/**
     * 第一比例因子
     */
	@Digits(integer = 11, fraction = 5, message = "{第一比例因子必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("第一比例因子")
	private  BigDecimal factor1;
	/**
     * 第二比例因子
     */
	@Digits(integer = 11, fraction = 5, message = "{第二比例因子必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("第二比例因子")
	private  BigDecimal factor2;
	/**
     * 申报数量
     */
	@Digits(integer = 11, fraction = 5, message = "{申报数量必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("申报数量")
	@ExcelColumn(name = "申报数量")
	private  BigDecimal qty;
	/**
     * 毛重
     */
	@Digits(integer = 13, fraction = 5, message = "{毛重必须为数字,整数位最大13位,小数最大5位!}")
	@ApiModelProperty("毛重")
	@ExcelColumn(name = "毛重")
	private  BigDecimal grossWt;
	/**
     * 净重
     */
	@Digits(integer = 13, fraction = 8, message = "{净重必须为数字,整数位最大13位,小数最大8位!}")
	@ApiModelProperty("净重")
	@ExcelColumn(name = "净重")
	private  BigDecimal netWt;
	/**
     * 用途
     */
	@XdoSize(max = 4, message = "{用途长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("用途")
	private  String useType;
	/**
     * 征免方式
     */
	@XdoSize(max = 6, message = "{征免方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("征免方式")
	@ExcelColumn(name = "征免方式",tag = "LEVYMODE",converter = "pcodeExcelConverter",IOC = true)
	private  String dutyMode;
	/**
     * 单耗版本号
     */
	@XdoSize(max = 8, message = "{单耗版本号长度不能超过8位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("单耗版本号")
	@ExcelColumn(name = "单耗版本号")
	private  String exgVersion;
	/**
     * 归并序号
     */
	@Digits(integer = 11, fraction = 0, message = "{归并序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("归并序号")
	@ExcelColumn(name = "报关单商品序号")
	private BigDecimal entryGNo;
	/**
     * 备注
     */
	@XdoSize(max = 512, message = "{备注长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	@ExcelColumn(name = "备注")
	private  String note;
//	/**
//     * 账册企业内部编号
//     */
//	@XdoSize(max = 30, message = "{账册企业内部编号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
//	@ApiModelProperty("账册企业内部编号")
//	private  String copEmsNo;
	/**
     * 归类标记
     */
	@XdoSize(max = 4, message = "{归类标记长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("归类标记")
	private  String classMark;
	/**
     * 入库时间
     */
	@ApiModelProperty("入库时间")
	private  Date arrivalDate;
	/**
    * 入库时间-开始
    */
	@ApiModelProperty("入库时间-开始")
	private String arrivalDateFrom;
	/**
    * 入库时间-结束
    */
	@ApiModelProperty("入库时间-结束")
    private String arrivalDateTo;
	/**
     * 保完税标识 0 保税 1 非保税
     */
	@XdoSize(max = 1, message = "{保完税标识 0 保税 1 非保税长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("保完税标识 0 保税 1 非保税")
	private  String bondMark;
	/**
     * 物料类型标识
     */
	@XdoSize(max = 2, message = "{物料类型标识长度不能超过2位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("物料类型标识")
	private  String GMark;
	/**
     * 备案号
     */
	@XdoSize(max = 12, message = "{备案号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案号")
	private  String emsNo;
	/**
     * 中文名称
     */
	@XdoSize(max = 255, message = "{中文名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("中文名称")
	private  String copGName;
	/**
     * 中文规格型号
     */
	@XdoSize(max = 255, message = "{料号申报要素长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("料号申报要素")
	@ExcelColumn(name = "料号申报要素")
	private  String copGModel;
	/**
     * 销售订单号
     */
	@XdoSize(max = 30, message = "{销售订单号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("销售订单号")
	@ExcelColumn(name = "销售订单号")
	private  String orderNo;
	/**
     * 发票号码
     */
	@XdoSize(max = 100, message = "{发票号码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发票号码")
	@ExcelColumn(name = "发票号码")
	private  String invoiceNo;
	/**
     * 体积
     */
	@Digits(integer = 11, fraction = 5, message = "{体积必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("体积")
	@ExcelColumn(name = "体积")
	private  BigDecimal volume;
	/**
     * 供应商编码
     */
	@XdoSize(max = 50, message = "{供应商编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("供应商编码")
	@ExcelColumn(name = "客户")
	private  String supplierCode;
	/**
     * 供应商名称
     */
	@XdoSize(max = 255, message = "{供应商名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("供应商名称")
	private  String supplierName;
	/**
     * 监管方式
     */
	@XdoSize(max = 6, message = "{监管方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("监管方式")
	private  String tradeMode;
	/**
     * 企业料号
     */
	@XdoSize(max = 100, message = "{企业料号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业料号")
	@ExcelColumn(name = "企业料号")
	private  String facGNo;
	/**
	 * 关联单号
	 */
	@XdoSize(max = 50, message = "{关联单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联单号")
	@ExcelColumn(name = "关联单号")
	private  String linkedNo;

	/**
	 * 企业订单号
	 */
	@XdoSize(max = 50, message = "{企业订单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业订单号")
	private  String customerOrderNo;

	/**
	 * 企业料号
	 */
	@XdoSize(max = 50, message = "{企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业料号")
	private  String customerGNo;
	/**
	 * 警告标志 0：非警告  1：警告
	 */
	@ApiModelProperty("警告标志 0：非警告  1：警告")
	private  String warningMark;

	/**
	 * 备注1
	 */
	@XdoSize(max = 250, message = "{Remark1长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注1")
	@ExcelColumn(name = "Remark1")
	private  String note1;
	/**
	 * 备注2
	 */
	@XdoSize(max = 250, message = "{Remark2长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注2")
	@ExcelColumn(name = "Remark2")
	private  String note2;
	/**
	 * 备注3
	 */
	@XdoSize(max = 250, message = "{Remark3长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注3")
	@ExcelColumn(name = "Remark3")
	private  String note3;
	/**
	 * 境内目的地(国内地区)
	 */
	@XdoSize(max = 6, message = "{境内目的地(国内地区)长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境内目的地(国内地区)")
	@ExcelColumn(name = "境内货源地(国内地区)",tag = "AREA",converter = "pcodeExcelConverter",IOC = true)
	private  String districtCode;

	/**
	 * 境内目的地(行政区域)
	 */
	@XdoSize(max = 6, message = "{境内目的地(行政区域)长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境内目的地(行政区域)")
	@ExcelColumn(name = "境内货源地(行政区域)",tag = "POST_AREA",converter = "pcodeExcelConverter",IOC = true)
	private  String districtPostCode;
	/**
	 * 成本中心
	 */
	@XdoSize(max = 50, message = "{成本中心长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("成本中心")
	@ExcelColumn(name = "成本中心")
	private  String costCenter;
	/**
	 * 关联单号行号
	 */
	@XdoSize(max = 50, message = "{关联单号序号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联单序号")
	@ExcelColumn(name = "关联单序号")
	private  String lineNo;

	/**
	 * 模拟项号
	 */
	@ApiModelProperty("模拟项号")
	@ExcelColumn(name = "模拟项号")
	private  String itemNo;
	/**
	 * 数据状态
	 */
	@ApiModelProperty("数据状态")
	private String status;


	/**
	 * 采购人员
	 */
	@XdoSize(max = 100, message = "{采购人员长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("采购人员")
	private  String buyer;
	/**
	 * 清单归并序号
	 */
	@Digits(integer = 11, fraction = 0, message = "{归并序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("清单归并序号")
	@ExcelColumn(name = "清单归并序号")
	private BigDecimal billGNo;

	/**
	 * 出库关联单号
	 */
	@XdoSize(max = 100, message = "{出库关联单号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("出库关联单号")
	@ExcelColumn(name = "出库关联单号")
	private  String inOutNo;
	/**
	 * CIQ编码
	 */
	@XdoSize(max=3, message="{CIQ代码长度不能超过3个字符(一个汉字2位字节长度)}")
	@ApiModelProperty("CIQ编码")
	@ExcelColumn(name = "CIQ代码")
	private String ciqNo;
	/**
	 * 分送清单编号
	 */
	@XdoSize(max=20, message="{分送清单编号长度不能超过20个字符(一个汉字2位字节长度)}")
	@ApiModelProperty("分送清单编号")
	private  String fsListNo;
	/**
	 * 单重
	 */
//	@Digits(integer = 13, fraction = 8, message = "{单重必须为数字,整数位最大13位,小数最大8位!}")
	@ApiModelProperty("单重")
	private  BigDecimal singleWeight;
	/**
	 * 特许权使用费确认
	 */
	@ApiModelProperty("特许权使用费确认")
	@XdoSize(max=1, message="{特许权使用费确认长度不能超过1个字符}")
	//@ExcelColumn(name = "特许权使用费确认")
	private  String confirmRoyalties;
	/**
	 * 特许权关联单号
	 */
	@ApiModelProperty("特许权关联单号")
	@XdoSize(max=100, message="{特许权关联单号长度不能超过100个字符(一个汉字2位字节长度)}")
	@ExcelColumn(name = "特许权关联单号")
	private  String confirmRoyaltiesNo;
	/**
	 * 插入人
	 */
	@ApiModelProperty("插入人")
	private  String insertUser;/**
	 * 制单人姓名
	 */
	@XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("制单人姓名")
	private String insertUserName;
	/**
	 * 创建日期
	 */
	@ApiModelProperty("创建日期")
	private Date insertTime;
	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateUser;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 修改人姓名
	 */
	@XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
	 * 所属企业编码
	 */
	@ApiModelProperty("所属企业编码")
	private String tradeCode;
	/**
	 * 征免性质
	 */
	@XdoSize(max = 3, message = "{征免性质长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("征免性质")
	private  String cutMode;
	/**
	 * 实收总价
	 */
	@Digits(integer = 10, fraction = 5, message = "{实收总价必须为数字,整数位最大10位,小数最大5位!}")
	@ApiModelProperty("实收总价")
	private  BigDecimal payTotal;
	/**
	 * 工费
	 */
	@Digits(integer = 10, fraction = 5, message = "{工费必须为数字,整数位最大10位,小数最大5位!}")
	@ApiModelProperty("工费")
	@ExcelColumn(name = "工费")
	private  BigDecimal laborCost;
	/**
	 * 料费
	 */
	@Digits(integer = 10, fraction = 5, message = "{料费必须为数字,整数位最大10位,小数最大5位!}")
	@ApiModelProperty("料费")
	@ExcelColumn(name = "料费")
	private  BigDecimal matCost;
	/**
	 * 客供总价
	 */
	@Digits(integer = 10, fraction = 5, message = "{客供总价必须为数字,整数位最大10位,小数最大5位!}")
	@ApiModelProperty("客供总价")
	private  BigDecimal clientTotal;

	/**
	 * 件数
	 */
	@Digits(integer = 9, fraction = 0, message = "{件数必须为数字,整数位最大9位,小数最大0位!}")
	@ApiModelProperty("件数")
	@ExcelColumn(name = "件数")
	private BigDecimal packNum;
	/**
	 * 批次号
	 */
	@XdoSize(max = 100, message = "{批次号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("批次号")
	@ExcelColumn(name = "批次号")
	private String batchNo;
	/**
	 * 箱数
	 */
	@Digits(integer = 9, fraction = 0, message = "{箱数必须为数字,整数位最大9位!}")
	@ApiModelProperty("箱数")
	@ExcelColumn(name = "箱数")
	private BigDecimal listCartonNum;

	/**
	 * 重点商品标识
	 */
	@ApiModelProperty("重点商品标识")
	private String keyProductMark;

}
