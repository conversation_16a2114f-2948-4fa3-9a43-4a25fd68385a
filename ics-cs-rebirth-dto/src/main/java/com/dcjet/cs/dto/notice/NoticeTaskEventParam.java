package com.dcjet.cs.dto.notice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 业务类型Code             类型说明                    数据源IDdesc
 *
 * DEC_I_SENDED         进口预报单已分单                进口提单表头ID
 * DEC_I_RECEIVED       进口非保税预录入单已接受委托      进口提单表头ID
 *
 * ENTRY_I_SENDED       进口报关单草单已发送             emsListNo
 * ENTRY_I_RECEIVED     接收到进口申报前草单             emsListNo
 * ENTRY_E_SENDED       出口报关单草单已发送             emsListNo
 * ENTRY_E_RECEIVED     接收到出口申报前草单             emsListNo
 *
 * ENTRY_I_NO_PASS      进口报关单复核退回               emsListNo
 * ENTRY_I_PASS         进口报关单复核通过               emsListNo
 * ENTRY_E_NO_PASS      出口报关单复核退回               emsListNo
 * ENTRY_E_PASS         出口报关单复核通过               emsListNo
 */
@Setter @Getter
@ApiModel(value = "邮件通知事件参数")
public class NoticeTaskEventParam {

    @ApiModelProperty("业务类型Code")
    private String bizCode;

    @ApiModelProperty("数据源ID")
    private String bizDataId;

    @ApiModelProperty("企业内部编号")
    private String tradeCode;

    public NoticeTaskEventParam(String bizCode, String tradeCode, String bizDataId) {
        this.bizCode = bizCode;
        this.tradeCode = tradeCode;
        this.bizDataId = bizDataId;
    }
}
