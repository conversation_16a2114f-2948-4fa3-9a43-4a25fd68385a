package com.dcjet.cs.dto.erp;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2019-8-26
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class ProductListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 单证编号
     */
	@NotEmpty(message="{单证编号不能为空！}")
	@XdoSize(max = 50, message = "{单证编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("单证编号")
	private  String billNo;
	/**
	 * 企业代码
	 */
	@NotEmpty(message="{企业代码不能为空！}")
	@XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
     * 库位标识
     */
	@NotEmpty(message="{库位标识不能为空！}")
	@XdoSize(max = 30, message = "{库位标识长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("库位标识")
	private  String warehouseNo;
	/**
     * 关联编号
     */
	@XdoSize(max = 50, message = "{关联编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联编号")
	private  String linkedNo;
	/**
     * SO号
     */
	@XdoSize(max = 30, message = "{SO号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("SO号")
	private  String soNo;
	/**
     * 发票号
     */
	@XdoSize(max = 30, message = "{发票号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发票号")
	private  String invoiceNo;
	/**
     * 交易日期
     */
	@NotNull(message="{交易日期不能为空！}")
	@ApiModelProperty("交易日期")
	private  Date deliveryDate;
	/**
    * 交易日期-开始
    */
	@ApiModelProperty("交易日期-开始")
	private String deliveryDateFrom;
	/**
    * 交易日期-结束
    */
	@ApiModelProperty("交易日期-结束")
    private String deliveryDateTo;
	/**
     * 企业料号
     */
	@NotEmpty(message="{企业料号不能为空！}")
	@XdoSize(max = 50, message = "{企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业料号")
	private  String facGNo;
	/**
     * 客户料号
     */
	@NotEmpty(message="{客户料号不能为空！}")
	@XdoSize(max = 50, message = "{客户料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户料号")
	private  String customerGNo;
	/**
     * 保完税标记
     */
	@XdoSize(max = 30, message = "{保完税标记长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("保完税标记")
	private  String bondMark;
	/**
     * 客户代码
     */
	@XdoSize(max = 15, message = "{客户代码长度不能超过15位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户代码")
	private  String customerCode;
	/**
     * 进口方式
     */
	@XdoSize(max = 50, message = "{进口方式长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("进口方式")
	private  String outWay;
	/**
     * 用户/导入批次号
     */
	@XdoSize(max = 36, message = "{用户/导入批次号长度不能超过36位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("用户/导入批次号")
	private  String tempOwner;
	/**
     * ERP最后变更日期
     */
	@ApiModelProperty("ERP最后变更日期")
	private  Date lastModifyDate;
	/**
    * ERP最后变更日期-开始
    */
	@ApiModelProperty("ERP最后变更日期-开始")
	private String lastModifyDateFrom;
	/**
    * ERP最后变更日期-结束
    */
	@ApiModelProperty("ERP最后变更日期-结束")
    private String lastModifyDateTo;
	/**
     * 物料类型
     */
	@XdoSize(max = 30, message = "{物料类型长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("物料类型")
	private  String GMark;

	/**
	 * 锁定标志（多进程生成、解析时，防止并发取任务）
	 */
	@XdoSize(max = 50, message = "{锁定标志（多进程生成、解析时，防止并发取任务）长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("锁定标志（多进程生成、解析时，防止并发取任务）")
	private  String lockMark;


	/**
	 * 标记是否是深加工模块
	 */
	@ApiModelProperty("标记是否是深加工模块")
	private  String pickUp;

	/**
	 * 仓库编号
	 */
	@XdoSize(max = 50, message = "{仓库编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("仓库编号")
	private String warehouse;

	/**
	 * 工厂代码
	 */
	@XdoSize(max = 50, message = "{工厂代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("工厂代码")
	private String factory;
}
