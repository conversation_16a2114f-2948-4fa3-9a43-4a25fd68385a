package com.dcjet.cs.dto.mat;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2020-5-15
 */
@Setter @Getter
@ApiModel(value = "单损耗表头传入参数")
public class EmsHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 企业内部编号
     */
	@ApiModelProperty("企业内部编号")
	private String copEmsNo;
	/**
     * 预录入统一编号
     */
	@ApiModelProperty("预录入统一编号")
	private String preSeqNo;
	/**
     * 手账册编号
     */
	@ApiModelProperty("手账册编号")
	private String emsNo;
	/**
     * 有效期
     */
	@ApiModelProperty("有效期")
	private Date validDate;
	/**
    * 有效期-开始
    */
	@ApiModelProperty("有效期-开始")
	private String validDateFrom;
	/**
    * 有效期-结束
    */
	@ApiModelProperty("有效期-结束")
    private String validDateTo;
	/**
     * 审核人
     */
	@ApiModelProperty("审核人")
	private String apprUser;
	/**
     * 审核时间
     */
	@ApiModelProperty("审核时间")
	private Date apprDate;
	/**
    * 审核时间-开始
    */
	@ApiModelProperty("审核时间-开始")
	private String apprDateFrom;
	/**
    * 审核时间-结束
    */
	@ApiModelProperty("审核时间-结束")
    private String apprDateTo;
	/**
     * 审核状态(-1 内审退回,0 暂存,2 待审核,8 内审通过,9 发送备案中(提交后端任务),JA 发送备案(发送金二成功),10 发送备案失败(发送金二失败/部分失败),JC 备案通过,JD 备案退回)
     */
	@XdoSize(max = 3, message = "{审核状态(-1 内审退回,0 暂存,2 待审核,8 内审通过,9 发送备案中(提交后端任务),JA 发送备案(发送金二成功),10 发送备案失败(发送金二失败/部分失败),JC 备案通过,JD 备案退回)长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("审核状态(-1 内审退回,0 暂存,2 待审核,8 内审通过,9 发送备案中(提交后端任务),JA 发送备案(发送金二成功),10 发送备案失败(发送金二失败/部分失败),JC 备案通过,JD 备案退回)")
	private String apprStatus;
	/**
     * 审核人名称
     */
	@XdoSize(max = 50, message = "{审核人名称长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("审核人名称")
	private String apprUserName;
	/**
     * 发送备案时间
     */
	@ApiModelProperty("发送备案时间")
	private Date sendApiDate;
	/**
    * 发送备案时间-开始
    */
	@ApiModelProperty("发送备案时间-开始")
	private String sendApiDateFrom;
	/**
    * 发送备案时间-结束
    */
	@ApiModelProperty("发送备案时间-结束")
    private String sendApiDateTo;
	/**
     * 发送人
     */
	@ApiModelProperty("发送人")
	private String sendUser;
	/**
     * 发送人姓名
     */
	@ApiModelProperty("发送人姓名")
	private String sendUserName;
	/**
     * 发送api任务id 
     */
	@ApiModelProperty("发送api任务id ")
	private String sendApiTaskId;
	/**
     * 企业代码
     */
	@ApiModelProperty("企业代码")
	private String tradeCode;
	/**
     * 是否备案
     */
	@ApiModelProperty("是否备案")
	private String isPass;
	/**
     * 单据标记 11：金二手册 12：H200手册 21：金二账册 22：H2000账册 31:点训通
     */
	@ApiModelProperty("单据标记 11：金二手册 12：H200手册 21：金二账册 22：H2000账册 31:点训通")
	private String billFlag;
	/**
     * 数据来源 0 手工录入 其他待定
     */
	@ApiModelProperty("数据来源 0 手工录入 其他待定")
	private String dataSource;
	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	@XdoSize(max = 100,message = "{备注长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	private String note;
	/**
	 * 内审意见
	 */
	@ApiModelProperty("内审意见")
	private String apprNote;
	/**
	 * 区分普通列表页面和审核页面
	 */
	@ApiModelProperty("页面类型(1-单损耗信息页面,2-单损耗审核页面)")
	private String type;
	/**
	 * 登录账号
	 */
	@ApiModelProperty("登录账号")
	private String userNo;
	/**
	 * 发送内审时间-起
	 */
	@ApiModelProperty("发送内审时间-起")
	private String sendApprDateFrom;
	/**
	 * 发送内审时间-止
	 */
	@ApiModelProperty("发送内审时间-止")
	private String sendApprDateTo;

	/**
	 * 创建时间-开始
	 */
	@ApiModelProperty("录入日期-开始")
	private String insertTimeFrom;
	/**
	 * 创建时间-结束
	 */
	@ApiModelProperty("录入日期-结束")
	private String insertTimeTo;


}
