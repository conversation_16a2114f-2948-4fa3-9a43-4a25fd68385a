package com.dcjet.cs.dto.mrp;

import com.dcjet.cs.dto.base.BasicParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ApiModel(value = "内销单价计算及审核 计算参数")
@Setter
@Getter
public class MrpPriceStartParam extends BasicParam {


    /***
     * 最新汇率
     */
    public static final String EXCHANGE_RATE_LATEST = "latest";

    /***
     * 申报日期
     */
    public static final String EXCHANGE_RATE_DECLARE_DATE = "declareDate";

    /**
     *表头主键
     */
    @ApiModelProperty(value = "表头主键")
    @XdoSize(max = 40, message = "{表头主键长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @NotEmpty(message="{表头主键不能为空！}")
    private String headId;

    /**
     * 单价计算范围
     * 1. 按申报日期
     * 2. 按已匹配核注清单
     */
    @ApiModelProperty("单价计算范围")
    private int scope;

    /**
     * 清单申报日期-开始
     */
    @ApiModelProperty("申报日期-开始")
    private String declareDateFrom;

    /**
     * 申报日期
     */
    @ApiModelProperty("申报日期-结束")
    private String declareDateTo;

    /**
     *价格类型 min,max, wei_avg
     */
    @ApiModelProperty(value = "价格类型 min,max, wei_avg")
    private String priceType;

    @ApiModelProperty(value = "汇率取值, latest 最新，declareDate 申报日期")
    private String exchangeRateType;

    @ApiModelProperty(value = "比制取值, rmb，usd")
    private String currType;

    /***
     * 企业编码 用于后端查询数据库
     */
    @JsonIgnore
    private String tradeCode;

    /***
     * emsNo 后端查询数据库
     */
    @JsonIgnore
    private String emsNo;

    /***
     * 企业/备案料号
     */
    @JsonIgnore
    private String no;

    /***
     * 生成描述
     *
     * @return
     */
    public String toDescription() {
        StringBuilder sb = new StringBuilder();
        if (!StringUtils.isEmpty(declareDateFrom)) {
            sb.append(declareDateFrom);
        }
        if (!StringUtils.isEmpty(declareDateFrom) && !StringUtils.isEmpty(declareDateTo)) {
            sb.append("～");
        }

        if (!StringUtils.isEmpty(declareDateTo)) {
            sb.append(declareDateTo);
        }
        if (sb.length() > 0) {
            sb.append(" ");
        }

        Map<String, String> map = new HashMap<String, String>() {{
            put("min", "最低价");
            put("max", "最高价");
            put("wei_avg", "加权平均");
        }};

        sb.append(map.get(priceType));
        sb.append(" ");
        map = new HashMap<String, String>() {{
            put(EXCHANGE_RATE_LATEST, "取最新汇率");
            put(EXCHANGE_RATE_DECLARE_DATE, "按清单申报日期取汇率");
        }};

        sb.append(map.get(exchangeRateType));
        return sb.toString();
    }
}
