package com.dcjet.cs.dto.ocrTrans;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2022-4-1
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class GwLvOcrTransParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 转换前的值
     */
	@NotEmpty(message="转换前的值不能为空！")
	@XdoSize(max = 20, message = "转换前的值长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("转换前的值")
	private  String beforeConvert;
	/**
     * 转换前的值
     */
	@NotEmpty(message="转换前的值不能为空！")
	@XdoSize(max = 20, message = "转换前的值长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("转换前的值")
	private  String afterConvert;
	/**
     * 所需要影响的字段类型(0.字符 1.数值)
     */
	@NotEmpty(message="所需要影响的字段类型(0.字符 1.数值)不能为空！")
	@XdoSize(max = 2, message = "所需要影响的字段类型(0.字符 1.数值)长度不能超过2位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("所需要影响的字段类型(0.字符 1.数值)")
	private  String fieldType;
}
