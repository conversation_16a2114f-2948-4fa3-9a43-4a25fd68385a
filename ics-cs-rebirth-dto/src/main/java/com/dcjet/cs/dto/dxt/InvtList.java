package com.dcjet.cs.dto.dxt;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.List;

@Setter @Getter
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "InvtList")
@XmlType(propOrder = {
        "BillListFullEntity"
})
public class InvtList implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<com.dcjet.cs.dto.dxt.BillListFullEntity> BillListFullEntity;
}
