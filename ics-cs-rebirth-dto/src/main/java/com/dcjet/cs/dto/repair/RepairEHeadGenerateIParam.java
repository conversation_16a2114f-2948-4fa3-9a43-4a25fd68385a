package com.dcjet.cs.dto.repair;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class RepairEHeadGenerateIParam {

    @ApiModelProperty(value = "{主键不能为空}")
    @NotEmpty(message = "{主键不能为空}")
    private String sid;

    @ApiModelProperty(value = "单据内部编号")
    @XdoSize(max = 32, message = "{单据内部编号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @NotEmpty(message = "{单据内部编号不能为空}")
    private String emsListNo;
}
