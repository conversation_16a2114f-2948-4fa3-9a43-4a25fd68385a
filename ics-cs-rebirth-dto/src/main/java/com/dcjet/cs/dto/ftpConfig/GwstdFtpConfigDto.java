package com.dcjet.cs.dto.ftpConfig;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2019-10-22
 */
@ApiModel(value = "GwstdFtpConfigDto返回信息")
@Setter @Getter
public class GwstdFtpConfigDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * ftp IP地址
      */
    @ApiModelProperty("ftp IP地址")
	private  String ipAddr;
	/**
      * ftp 端口
      */
    @ApiModelProperty("ftp 端口")
	private  Integer port;
	/**
      * 账号
      */
    @ApiModelProperty("账号")
	private  String ftpAccount;
	/**
      * 密码
      */
    @ApiModelProperty("密码")
	private  String ftpPwd;
	/**
      * 服务器文件路径
      */
    @ApiModelProperty("服务器文件路径")
	private  String serverFilePath;
	/**
      * FTP业务类型
      */
    @ApiModelProperty("FTP业务类型")
	private  String ftpType;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 是否启用 1：启用 0：禁用 默认 1
      */
    @ApiModelProperty("是否启用 1：启用 0：禁用 默认 1")
	private  String isEnable;
	/**
	 * 是否自动匹配 0.自动匹配 1.手动匹配
	 */
	@ApiModelProperty("是否自动匹配 0.自动匹配 1.手动匹配")
	private  String matchType;
	/**
	 * 编码
	 */
	@ApiModelProperty("编码")
	private  String coding;
}
