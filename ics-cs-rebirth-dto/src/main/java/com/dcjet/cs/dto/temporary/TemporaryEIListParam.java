package com.dcjet.cs.dto.temporary;

import com.dcjet.cs.dto.base.BasicParam;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 复运进境表体
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "复运进境表体新增修改参数")
@Setter @Getter
public class TemporaryEIListParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @ApiModelProperty(value = "表头主键")
    @XdoSize(max = 40, message = "{表头主键长度不能超过40位字节长度(一个汉字2位字节长度)!}") 
    private String headId;

    @ApiModelProperty(value = "维修单价")
    @Digits(integer = 15, fraction = 5, message = "{维修单价必须为数字,整数位最大15位,小数最大5位!}")
    private BigDecimal temporaryPrice;

    @ApiModelProperty(value = "备注")
    @XdoSize(max = 512, message = "{备注长度不能超过512位字节长度(一个汉字2位字节长度)!}") 
    private String note;
    
}
