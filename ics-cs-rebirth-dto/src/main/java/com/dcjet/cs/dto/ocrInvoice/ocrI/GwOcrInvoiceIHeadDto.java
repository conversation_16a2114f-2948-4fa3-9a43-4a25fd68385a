package com.dcjet.cs.dto.ocrInvoice.ocrI;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.beans.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2021-9-29
 */
@ApiModel(value = "GwOcrInvoiceIHeadDto")
@Setter @Getter
public class GwOcrInvoiceIHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 发票号码
      */
    @ApiModelProperty("发票号码")
	private  String invoiceNo;
	/**
      * 发票日期
      */
    @ApiModelProperty("发票日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date invoiceDate;
	/**
      * 装箱单号码
      */
    @ApiModelProperty("装箱单号码")
	private  String packingNo;
	/**
      * 关联号码
      */
    @ApiModelProperty("关联号码")
	private  String referenceNo;
	/**
      * 发货人
      */
    @ApiModelProperty("发货人")
	private  String shipper;
	/**
      * 境外发货人代码
      */
    @ApiModelProperty("境外发货人代码")
	private  String shipperCode;
	/**
      * 收货人
      */
    @ApiModelProperty("收货人")
	private  String consignee;
	/**
      * 贸易条款
      */
    @ApiModelProperty("贸易条款")
	private  String tradeTerms;
	/**
      * 总数量
      */
    @ApiModelProperty("总数量")
	private  BigDecimal qty;
	/**
      * 总数量单位
      */
    @ApiModelProperty("总数量单位")
	private  String unit;
	/**
      * 转换后单位
      */
    @ApiModelProperty("转换后单位")
	private  String unitConvert;
	/**
      * 总金额
      */
    @ApiModelProperty("总金额")
	private  BigDecimal totalAmount;
	/**
      * 币制
      */
    @ApiModelProperty("币制")
	private  String curr;
	/**
      * 转换后币制
      */
    @ApiModelProperty("转换后币制")
	private  String currConvert;
	/**
      * 统一原产国
      */
    @ApiModelProperty("统一原产国")
	private  String originCountry;
	/**
      * 转换后原产国
      */
    @ApiModelProperty("转换后原产国")
	private  String originCountryConvert;
	/**
      * 统一订单号
      */
    @ApiModelProperty("统一订单号")
	private  String orderNo;
	/**
      * 托盘数(件数)
      */
    @ApiModelProperty("托盘数(件数)")
	private  Integer palletNum;
	/**
      * 散箱数
      */
    @ApiModelProperty("散箱数")
	private  Integer bulkCtns;
	/**
      * 总箱数
      */
    @ApiModelProperty("总箱数")
	private  Integer totalCtns;
	/**
      * 总件数
      */
    @ApiModelProperty("总件数")
	private  Integer packNum;
	/**
      * 总净重
      */
    @ApiModelProperty("总净重")
	private  BigDecimal netWt;
	/**
      * 净重单位
      */
    @ApiModelProperty("净重单位")
	private  String netWtUnit;
	/**
      * 转换后净重单位
      */
    @ApiModelProperty("转换后净重单位")
	private  String netWtUnitConvert;
	/**
      * 总毛重
      */
    @ApiModelProperty("总毛重")
	private  BigDecimal grossWt;
	/**
      * 毛重单位
      */
    @ApiModelProperty("毛重单位")
	private  String grossWtUnit;
	/**
      * 转换后毛重单位
      */
    @ApiModelProperty("转换后毛重单位")
	private  String grossWtUnitConvert;
	/**
      * 总体积
      */
    @ApiModelProperty("总体积")
	private  BigDecimal volume;
	/**
      * 生成状态(1已识别 2已生成)
      */
    @ApiModelProperty("状态(1已识别 2已生成)")
	private  String createStatus;
	/**
      * 修改状态（1未修改；2已修改）
      */
    @ApiModelProperty("修改状态（1未修改；2已修改）")
	private  String modifyStatus;
	/**
      * 识别时间(暂时写入插入时间)
      */
    @ApiModelProperty("识别时间(暂时写入插入时间)")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date scanTime;
	/**
      * 生成到预录入单后返回的表头ID
      */
    @ApiModelProperty("生成到预录入单后返回的表头ID")
	private  String headId;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建人名称
      */
    @ApiModelProperty("创建人名称")
	private  String insertUserName;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 修改人
      */
    @ApiModelProperty("修改人")
	private  String updateUser;
	/**
      * 修改人名称
      */
    @ApiModelProperty("修改人名称")
	private  String updateUserName;
	/**
      * 修改数据时间
      */
    @ApiModelProperty("修改数据时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 文件类型(INVOICE.发票 PACKING.箱单)
      */
    @ApiModelProperty("文件类型(INVOICE.发票 PACKING.箱单 INV_PACK.发票&箱单)")
	private  String dataType;

	@ApiModelProperty("单据内部编号")
	private String emsListNo;

	@ApiModelProperty("保完税标志")
	private String bondMark;

	@ApiModelProperty("监管方式")
	private String tradeMode;

	@ApiModelProperty("备案号")
	private String emsNo;

	@ApiModelProperty("任务ID")
	private String taskId;

	@ApiModelProperty("location信息")
	private GwOcrInvoiceIHeadLocDto loc;

	@ApiModelProperty("业务编号")
	private String businessNo;

	@ApiModelProperty("主单号")
	private String mawb;

	@ApiModelProperty("分单号")
	private String hawb;

	@ApiModelProperty("货代")
	private String forwarder;

	@ApiModelProperty("启运港")
	private String depport;

	@ApiModelProperty("目的港")
	private String desport;

	@ApiModelProperty("提单日期")
	private String billDate;

	@ApiModelProperty("航班号")
	private String fltNo;

	@ApiModelProperty("航班日期")
	private String fltDate;

	@ApiModelProperty("头程航班号")
	private String fltNo1st ;

	@ApiModelProperty("头程航班日期")
	private String fltDate1st;

	@ApiModelProperty("运费")
	private BigDecimal freight;

	@ApiModelProperty("计费重量")
	private BigDecimal totalChwt;
	private String extendFiled3;

	public void setBusinessNoByDataType() {
		if ("INVOICE".equals(this.dataType)) {
			this.businessNo = this.invoiceNo + "(发票)";
		}
		if ("PACKING".equals(this.dataType)) {
			this.businessNo = this.invoiceNo + "(箱单)";
		}
		if ("LADING".equals(this.dataType)) {
			this.businessNo = this.mawb + "(提单)";
		}
	}

	/**
	 * 送货单号
	 */
	@ApiModelProperty("送货单号")
	private String deliveryNoteNo;

	@ApiModelProperty("报关单号")
	private String entryNo;

}
