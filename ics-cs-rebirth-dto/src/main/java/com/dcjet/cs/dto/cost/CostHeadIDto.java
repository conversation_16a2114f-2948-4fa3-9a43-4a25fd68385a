package com.dcjet.cs.dto.cost;

import com.dcjet.cs.dto.base.BasicDto;
import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 费用明细表头
 * <p>
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2020-02-27
 */
@ApiModel(value = "费用明细表头 DTO")
@Setter
@Getter
public class CostHeadIDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 附件数量
     */
    @ApiModelProperty("附件数量")
    private Integer attachmentCount;

    /**
     * 请款日期
     */
    @ApiModelProperty(value = "请款日期")
    private Date applyDate;

    /**
     * 请款批号
     */
    @ApiModelProperty(value = "请款批号")
    private String applyNo;

    /**
     * 清单SID
     */
    @ApiModelProperty(value = "清单SID")
    private String billHeadId;

    /**
     * 确认1
     */
    @ApiModelProperty(value = "确认1")
    private String confirm1;

    /**
     * 确认2
     */
    @ApiModelProperty(value = "确认2")
    private String confirm2;

    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    /**
     * 锁定 0 未锁定 1 锁定
     */
    @ApiModelProperty(value = "锁定 0 未锁定 1 锁定")
    private String locked;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 付款日期
     */
    @ApiModelProperty(value = "付款日期")
    private Date payDate;

    /**
     * 账单编号
     */
    @ApiModelProperty(value = "账单编号")
    private String payNo;

    /**
     * 是否付汇 0 否 1 是
     */
    @ApiModelProperty(value = "是否付汇 0 否 1 是")
    private String payType;

    /**
     * 收款单位编码
     */
    @ApiModelProperty(value = "收款单位编码")
    private String remitteeCode;

    /**
     * 收款单位名称
     */
    @ApiModelProperty(value = "收款单位名称")
    private String remitteeName;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @ExcelColumn(map = "0:未维护,1:已维护")
    private String status;


    /*******报关单信息*****/


    /**
     * 单据内部编号
     */
    @ApiModelProperty("单据内部编号")
    private String emsListNo;

    /**
     * 申报单位名称
     */
    @ApiModelProperty("申报单位名称")
    private String declareName;


    /**
     * 货代编码
     */
    @ApiModelProperty("货代编码")
    private String forwardCode;

    /**
     * 货代名称
     */
    @ApiModelProperty("货代名称")
    private String forwardName;


    /**
     * 申报单位编码
     */
    @ApiModelProperty("申报单位编码")
    private String declareCode;

    /**
     * 进境关别代码
     */
    @ApiModelProperty("进境关别代码")
    @ExcelColumn(tag = "CUSTOMS_REL", converter = "pcodeExcelConverter", IOC = true)
    private String IEPort;
    /**
     * 申报地海关
     */
    @ApiModelProperty("申报地海关")
    @ExcelColumn(tag = "CUSTOMS_REL", converter = "pcodeExcelConverter", IOC = true)
    private String masterCustoms;

    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    @ExcelColumn(tag = "TRANSF", converter = "pcodeExcelConverter", IOC = true)
    private String trafMode;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String erpNote;
    /**
     * 制单日期
     */
    @ApiModelProperty("制单日期")
    private Date erpInsertTime;
    /**
     * 成交方式
     */
    @ApiModelProperty("成交方式")
    private String transMode;

    /**
     * 总净重
     */
    @ApiModelProperty("总净重")
    private BigDecimal netWt;

    /**
     * 包装种类
     */
    @ApiModelProperty("包装种类")
    @ExcelColumn(tag = "WRAP", converter = "pcodeExcelConverter", IOC = true)
    private String wrapType;

    /**
     * 总毛重
     */
    @ApiModelProperty("总毛重")
    private BigDecimal grossWt;

    /**
     * 境外发货人代码
     */
    @ApiModelProperty("境外发货人代码")
    private String overseasShipper;
    /**
     * 境外发货人名称
     */
    @ApiModelProperty("境外发货人名称")
    private String overseasShipperName;
    /**
     * 运抵国
     */
    @ApiModelProperty("运抵国")
    @ExcelColumn(tag = "COUNTRY_OUTDATED", converter = "pcodeExcelConverter", IOC = true)
    private String tradeCountry;

    /**
     * 数量汇总
     */
    @ApiModelProperty("数量汇总")
    private BigDecimal sumQty;
    /**
     * 总价汇总
     */
    @ApiModelProperty("总价汇总")
    private BigDecimal sumTotal;
    /**
     * 币制
     */
    @ApiModelProperty("币制")
    @ExcelColumn(tag = "CURR_OUTDATED", converter = "pcodeExcelConverter", IOC = true)
    private String curr;
    /**
     * 报关单号
     */
    @ApiModelProperty("报关单号")
    private String entryNo;

    /**
     * 贸易条款
     */
    @ApiModelProperty("贸易条款")
    private String tradeTerms;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String GName;

    @ApiModelProperty("提运单号")
    private String HAWB;


    /**
     * 提单表头发票号
     */
    @ApiModelProperty("提单表头发票号")
    private String billInvoiceNo;

    /***
     * 供应商
     */
    @ApiModelProperty("供应商")
    private String supplier;

    /***
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal packNum;

    /***
     * 人民币费用总额
     */
    @ApiModelProperty("人民币费用总额")
    private BigDecimal rmbSumTotal;


    /**
     * 费用科目代码
     */
    @ApiModelProperty(value = "费用科目代码")
    private String costCourseCode;

    /**
     * 费用科目名称
     */
    @ApiModelProperty(value = "费用科目名称")
    private String costCourseName;

    /**
     * 币制
     */
    @ApiModelProperty(value = "币制")
    @ExcelColumn(tag = "CURR_OUTDATED", converter = "pcodeExcelConverter", IOC = true)
    private String bodyCurr;

    /**
     * 费用
     */
    @ApiModelProperty(value = "费用")
    private String decTotal;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String bodyNote;

    /**
     * 人民币费用
     */
    @ApiModelProperty(value = "人民币费用")
    private BigDecimal rmbDecTotal;

    /***
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;

    /**
     * 计费重量
     */
    @ApiModelProperty(value = "计费重量")
    private BigDecimal cweight;

    /**
     * 收款单位类型
     */
    @ApiModelProperty(value = "收款单位类型")
    private String remitteeType;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 运费
     */
    @ApiModelProperty(value = "运费")
    private BigDecimal feeRate;
    /**
     * 运费币制
     */
    @ApiModelProperty(value = "运费币制")
    private String feeCurr;
    /**
     * 运费类型 1:率,2:单价,3:总价
     */
    @ApiModelProperty(value = "运费类型 1:率,2:单价,3:总价")
    private String feeMark;
    /**
     * 保费
     */
    @ApiModelProperty(value = "保费")
    private BigDecimal insurRate;
    /**
     * 保费币制
     */
    @ApiModelProperty(value = "保费币制")
    private String insurCurr;
    /**
     * 保费类型 1:率,2:单价,3:总价
     */
    @ApiModelProperty(value = "保费类型 1:率,2:单价,3:总价")
    private String insurMark;
    /**
     * 实际运费
     */
    @ApiModelProperty(value = "实际运费")
    private BigDecimal actualFeeRate;
    /**
     * 实际运费币制
     */
    @ApiModelProperty(value = "实际运费币制")
    private String actualFeeCurr;
    /**
     * 实际运费类型 1:率,2:单价,3:总价
     */
    @ApiModelProperty(value = "实际运费类型 1:率,2:单价,3:总价")
    private String actualFeeMark;
    /**
     * 实际保费
     */
    @ApiModelProperty(value = "实际保费")
    private BigDecimal actualInsurRate;
    /**
     * 实际保费币制
     */
    @ApiModelProperty(value = "实际保费币制")
    private String actualInsurCurr;
    /**
     * 实际保费类型 1:率,2:单价,3:总价
     */
    @ApiModelProperty(value = "实际保费类型 1:率,2:单价,3:总价")
    private String actualInsurMark;
    /**
     * 运保费是否一致(0 是 1 否)
     */
    @ApiModelProperty(value = "运保费是否一致(0 是 1 否)")
    private String isAgreement;
    /**
     * 运保费备注
     */
    @ApiModelProperty(value = "运保费备注")
    private String actualNote;
    /**
     * 账单提单号
     */
    @ApiModelProperty("账单提单号")
    private String billDeliveryId;
    /**
     * 账单总件数
     */
    @ApiModelProperty("账单总件数")
    private BigDecimal billPackNum;
    /**
     * 账单总体积
     */
    @ApiModelProperty("账单总体积")
    private BigDecimal billVolume;
    /**
     * 账单总进口金额
     */
    @ApiModelProperty("账单总进口金额")
    private BigDecimal billSumDecTotal;
    /**
     * 账单总毛重
     */
    @ApiModelProperty("账单总毛重")
    private BigDecimal billGrossWt;
    /**
     * 杂费币制
     */
    @ApiModelProperty("杂费币制")
    private String otherCurr;
    /**
     * 杂费类型
     */
    @ApiModelProperty("杂费类型")
    private String otherMark;
    /**
     * 杂费
     */
    @ApiModelProperty("杂费")
    private BigDecimal otherRate;
    /**
     * 账单总毛重
     */
    @ApiModelProperty("账单总毛重")
    private BigDecimal noRateAmount;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}