package com.dcjet.cs.dto.mid;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2020-6-16
 */
@ApiModel(value = "中期核查出库明细返回信息")
@Setter @Getter
public class MidExgEListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 保完税标志
      */
    @ApiModelProperty("保完税标志")
	private  String bondMark;
	/**
      * 物料类型标识
      */
    @ApiModelProperty("物料类型标识")
	@JsonProperty("gMark")
	private  String gMark;
	/**
      * 关联单号
      */
    @ApiModelProperty("关联单号")
	private  String linkedNo;
	/**
      * 出库单号
      */
    @ApiModelProperty("出库单号")
	private  String whNo;
	/**
      * 出库单行号
      */
    @ApiModelProperty("出库单行号")
	private  String whLineNo;
	/**
      * 出库料号
      */
    @ApiModelProperty("出库料号")
	private  String whGNo;
	/**
      * 出库数量
      */
    @ApiModelProperty("出库数量")
	private  BigDecimal whQty;
	/**
      * 出库单位
      */
    @ApiModelProperty("出库单位")
	private  String whUnit;
	/**
      * 出库日期
      */
    @ApiModelProperty("出库日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date whDate;
	/**
      * 出库仓别
      */
    @ApiModelProperty("出库仓别")
	private  String whStore;
	/**
	 * 出库类型（0：料件出库，1：成品出库）
	 */
	@ApiModelProperty("出库类型（0：料件出库，1：成品出库）")
	private String whType;
	/**
	 * 出库表SID
	 */
	@ApiModelProperty("出库表SID")
	private String whSid;
	/**
      * 申报单位
      */
    @ApiModelProperty("申报单位")
	private  String unit;
	/**
      * 转换申报单位数量
      */
    @ApiModelProperty("转换申报单位数量")
	private  BigDecimal qty;
	/**
      * 备案料号
      */
    @ApiModelProperty("备案料号")
	private  String copGNo;
	/**
      * 数据来源（0 手工录入，1 ERP接口，2 导入）
      */
    @ApiModelProperty("数据来源（0 手工录入，1 ERP接口，2 导入）")
	private  String dataSource;
	/**
	 * 出口方式
	 */
	@ApiModelProperty("出口方式")
	private String outWay;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 制单人姓名
      */
    @ApiModelProperty("制单人姓名")
	private  String insertUserName;
	/**
      * 修改人姓名
      */
    @ApiModelProperty("修改人姓名")
	private  String updateUserName;
}
