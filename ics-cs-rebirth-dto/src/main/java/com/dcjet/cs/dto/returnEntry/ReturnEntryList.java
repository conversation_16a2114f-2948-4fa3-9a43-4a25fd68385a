package com.dcjet.cs.dto.returnEntry;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description:
 * @author: WJ
 * @createDate: 2020/7/24 11:01
 */
@Setter
@Getter
public class ReturnEntryList {
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private  String gName;

    /**
     * 申报总价
     */
    @ApiModelProperty("申报总价")
    private BigDecimal declTotal;

    /**
     * 申报数量
     */
    @ApiModelProperty("申报数量")
    private  BigDecimal gQty;

    /**
     * 商品编号
     */
    private String codets;
}
