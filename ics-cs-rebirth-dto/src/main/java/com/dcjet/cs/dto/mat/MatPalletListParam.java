package com.dcjet.cs.dto.mat;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2021-4-12
 */
@Setter
@Getter
@ApiModel(value = "包装信息客户信息传入参数")
public class MatPalletListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 企业代码
     */
    @XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 制单人姓名
     */
    @XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
    /**
     * 表头SID
     */
    @XdoSize(max = 40, message = "{表头SID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("表头SID")
    private String headId;
    /**
     * 客户CODE
     */
    @XdoSize(max = 50, message = "{客户CODE长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户CODE")
    private String clientCode;
    /**
     * 客户名称
     */
    @XdoSize(max = 255, message = "{客户名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户名称")
    private String clientName;
    /**
     * 联系人1
     */
    @XdoSize(max = 512, message = "{联系人1长度不能超过512位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系人1")
    private String linkmanName1;
    /**
     * 联系人2
     */
    @XdoSize(max = 512, message = "{联系人2长度不能超过512位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系人2")
    private String linkmanName2;
    /**
     * 联系人3
     */
    @XdoSize(max = 512, message = "{联系人3长度不能超过512位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系人3")
    private String linkmanName3;
    /**
     * 类型(BILL TO 类型B---NOTIFY 类型N)
     */
    @ApiModelProperty("类型(BILL TO 类型B---NOTIFY 类型N)")
    private String packageType;
}
