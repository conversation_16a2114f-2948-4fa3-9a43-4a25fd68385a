package com.dcjet.cs.dto.deep;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2019-11-5
 */
@ApiModel(value = "SjgInBillHeadDto")
@Setter @Getter
public class SjgInBillHeadDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 转入单据内部编号
      */
    @ApiModelProperty("转入单据内部编号")
	private  String emsListNo;
	/**
      * 转出方备案号
      */
    @ApiModelProperty("转出方备案号")
	private  String emsNoOut;
	/**
      * 转出方海关十位代码
      */
    @ApiModelProperty("转出方海关十位代码")
	private  String tradeCodeOut;
	/**
      * 转出方企业名称
      */
    @ApiModelProperty("转出方企业名称")
	private  String tradeNameOut;
	/**
      * 转入方备案号
      */
    @ApiModelProperty("转入方备案号")
	private  String emsNoIn;
	/**
      * 申请表编号
      */
    @ApiModelProperty("申请表编号")
	private  String applyNo;
	/**
      * 转入报关单号
      */
    @ApiModelProperty("转入报关单号")
	private  String entryNoIn;
	/**
      * 转出报关单号
      */
    @ApiModelProperty("转出报关单号")
	private  String entryNoOut;
	/**
      * 状态
      */
    @ApiModelProperty("状态")
	private  String status;
	/**
      * 结转日期开始
      */
    @ApiModelProperty("结转日期开始")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date beginDate;
	/**
      * 结转日期结束
      */
    @ApiModelProperty("结转日期结束")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date endDate;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String remark;
	/**
      * 转入方海关十位代码
      */
    @ApiModelProperty("转入方海关十位代码")
	private  String tradeCodeIn;
	/**
      * 数据来源
      */
    @ApiModelProperty("数据来源")
	private  String dataSource;
	/**
      * 转出方代码(供应商客户代码)
      */
    @ApiModelProperty("转出方代码(供应商客户代码)")
	private  String customerCodeOut;
	/**
      * 监管方式
      */
    @ApiModelProperty("监管方式")
	private  String tradeMode;
	/**
      * 深加工转入备案表头ID
      */
    @ApiModelProperty("深加工转入备案表头ID")
	private  String headId;
	/**
      * 提单模板表头
      */
    @ApiModelProperty("提单模板表头")
	private  String templeteId;
	/**
      * 总净重
      */
    @ApiModelProperty("总净重")
	private  BigDecimal netWt;
	/**
      * 总毛重
      */
    @ApiModelProperty("总毛重")
	private  BigDecimal grossWt;
	/**
      * 件数
      */
    @ApiModelProperty("件数")
	private  BigDecimal packNum;
	/**
      * 数量汇总
      */
    @ApiModelProperty("数量汇总")
	private  BigDecimal sumQty;
	/**
      * 金额汇总
      */
    @ApiModelProperty("金额汇总")
	private  BigDecimal sumDecTotal;
	/**
      * 收发货单编号
      */
    @ApiModelProperty("收发货单编号")
	private  String rsBillNo;
	/**
	 * 发票号
	 */
	@ApiModelProperty("发票号")
	private String invoiceNo;
}
