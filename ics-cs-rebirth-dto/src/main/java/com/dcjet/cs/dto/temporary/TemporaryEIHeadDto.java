package com.dcjet.cs.dto.temporary;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 复运进境表头
 * 
 * generated by Generate dc
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "复运进境表头 DTO")
@Setter @Getter
public class TemporaryEIHeadDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @ApiModelProperty(value = "状态")
    private String status;
    
    @ApiModelProperty(value = "单据内部编号")
    private String emsListNo;
    
    @ApiModelProperty(value = "报关单号")
    private String entryNo;
    
    @ApiModelProperty(value = "报关单申报日期")
    private String declareDate;
    
    @ApiModelProperty(value = "复运完成日期")
    private Date completeDate;
    
    @ApiModelProperty(value = "备注")
    private String note;
    
}