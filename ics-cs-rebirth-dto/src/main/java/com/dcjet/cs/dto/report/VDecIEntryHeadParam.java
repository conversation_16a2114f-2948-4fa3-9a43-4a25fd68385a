package com.dcjet.cs.dto.report;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-04-23
 */
@ApiModel(value = "进口报关单报表表头传入参数")
@Setter
@Getter
public class VDecIEntryHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;

    /**
     * 申报单位代码
     */
    @XdoSize(max = 10, message = "{申报单位代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位代码")
    private  String declareCode;
    /**
     * 申报企业名称
     */
    @XdoSize(max = 70, message = "{申报企业名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报企业名称")
    private  String declareName;
    /**
     * 批准文号
     */
    @XdoSize(max = 32, message = "{批准文号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("批准文号")
    private  String apprNo;
    /**
     * 清单内部编号
     */
    @XdoSize(max = 32, message = "{清单内部编号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("清单内部编号")
    private  String emsListNo;
    /**
     * 合同号
     */
    @XdoSize(max = 32, message = "{合同号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("合同号")
    private  String contrNo;
    /**
     * 录入单位编号
     */
    @XdoSize(max = 10, message = "{录入单位编号长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("录入单位编号")
    private  String inputCode;
    /**
     * 录入单位名称
     */
    @XdoSize(max = 70, message = "{录入单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("录入单位名称")
    private  String inputName;
    /**
     * 主管海关（申报地海关）
     */
    @XdoSize(max = 4, message = "{主管海关（申报地海关）长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("主管海关（申报地海关）")
    private  String masterCustoms;
    /**
     * 征免性质
     */
    @XdoSize(max = 6, message = "{征免性质长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("征免性质")
    private  String cutMode;
    /**
     * 数据来源
     */
    @XdoSize(max = 5, message = "{数据来源长度不能超过5位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("数据来源")
    private  String dataSource;
    /**
     * 经停港/指运港
     */
    @XdoSize(max = 6, message = "{经停港/指运港长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("经停港/指运港")
    private  String destPort;
    /**
     * 报关标志
     */
    @XdoSize(max = 1, message = "{报关标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关标志")
    private  String dclcusMark;
    /**
     * 报关单号
     */
    @XdoSize(max = 18, message = "{报关单号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关单号")
    private  String entryNo;
    /**
     * 报关单类型
     */
    @XdoSize(max = 1, message = "{报关单类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关单类型")
    private  String entryType;
    /**
     * 运费币制
     */
    @XdoSize(max = 3, message = "{运费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运费币制")
    private  String feeCurr;
    /**
     * 运费标记
     */
    @XdoSize(max = 1, message = "{运费标记长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运费标记")
    private  String feeMark;
    /**
     * 运费／率
     */
    @Digits(integer = 14, fraction = 5, message = "{运费／率必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("运费／率")
    private  BigDecimal feeRate;
    /**
     * 毛重
     */
    @Digits(integer = 14, fraction = 5, message = "{毛重必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("毛重")
    private  BigDecimal grossWt;
    /**
     * 进出口日期
     */
    @ApiModelProperty("进出口日期")
    private  Date IEDate;
    /**
     * 进出口日期-开始
     */
    @ApiModelProperty("进出口日期-开始")
    private String iEDateFrom;
    /**
     * 进出口日期-结束
     */
    @ApiModelProperty("进出口日期-结束")
    private String iEDateTo;
    /**
     * 进出口标志
     */
    @XdoSize(max = 1, message = "{进出口标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("进出口标志")
    private  String IEMark;
    /**
     * 进出口岸
     */
    @XdoSize(max = 4, message = "{进出口岸长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("进出口岸")
    private  String IEPort;
    /**
     * 录入员名称
     */
    @XdoSize(max = 32, message = "{录入员名称长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("录入员名称")
    private  String inputerName;
    /**
     * 保险费币制
     */
    @XdoSize(max = 3, message = "{保险费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("保险费币制")
    private  String insurCurr;
    /**
     * 保险费标记
     */
    @XdoSize(max = 1, message = "{保险费标记长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("保险费标记")
    private  String insurMark;
    /**
     * 保险费／率
     */
    @Digits(integer = 14, fraction = 5, message = "{保险费／率必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("保险费／率")
    private  BigDecimal insurRate;
    /**
     * 许可证编号
     */
    @XdoSize(max = 20, message = "{许可证编号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("许可证编号")
    private  String licenseNo;
    /**
     * 账册编号
     */
    @XdoSize(max = 12, message = "{账册编号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("账册编号")
    private  String emsNo;
    /**
     * 净重
     */
    @Digits(integer = 14, fraction = 5, message = "{净重必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("净重")
    private  BigDecimal netWt;
    /**
     * 备注
     */
    @XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private  String note;
    /**
     * 杂费币制
     */
    @XdoSize(max = 3, message = "{杂费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("杂费币制")
    private  String otherCurr;
    /**
     * 杂费标志
     */
    @XdoSize(max = 1, message = "{杂费标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("杂费标志")
    private  String otherMark;
    /**
     * 杂费率
     */
    @Digits(integer = 14, fraction = 5, message = "{杂费率必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("杂费率")
    private  BigDecimal otherRate;
    /**
     * 消费使用/生产销售 单位代码
     */
    @XdoSize(max = 18, message = "{消费使用/生产销售 单位代码 长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("消费使用/生产销售 单位代码 ")
    private  String ownerCode;
    /**
     * 消费使用/生产销售 单位名称
     */
    @XdoSize(max = 70, message = "{消费使用/生产销售 单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("消费使用/生产销售 单位名称")
    private  String ownerName;
    /**
     * 件数
     */
    @Digits(integer = 9, fraction = 0, message = "{件数必须为数字,整数位最大9位,小数最大0位!}")
    @ApiModelProperty("件数")
    private  Integer packNum;
    /**
     * 预录入编号
     */
    @XdoSize(max = 9, message = "{预录入编号长度不能超过9位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("预录入编号")
    private  String preEntryNo;
    /**
     * 风险参数
     */
    @XdoSize(max = 10, message = "{风险参数长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("风险参数")
    private  String risk;
    /**
     * 报关单统一编号
     */
    @XdoSize(max = 18, message = "{报关单统一编号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关单统一编号")
    private  String seqNo;
    /**
     * 启运国/运抵国
     */
    @XdoSize(max = 3, message = "{启运国/运抵国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("启运国/运抵国")
    private  String tradeCountry;
    /**
     * 监管方式
     */
    @XdoSize(max = 4, message = "{监管方式长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("监管方式")
    private  String tradeMode;
    /**
     * 境内收发货人编号
     */
    @XdoSize(max = 18, message = "{境内收发货人编号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内收发货人编号")
    private  String tradeCode;
    /**
     * 运输方式代码
     */
    @XdoSize(max = 6, message = "{运输方式代码长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运输方式代码")
    private  String trafMode;
    /**
     * 运输工具代码及名称
     */
    @XdoSize(max = 50, message = "{运输工具代码及名称长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运输工具代码及名称")
    private  String trafName;
    /**
     * 境内收发货人名称
     */
    @XdoSize(max = 70, message = "{境内收发货人名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内收发货人名称")
    private  String tradeName;
    /**
     * 成交方式
     */
    @XdoSize(max = 10, message = "{成交方式长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("成交方式")
    private  String transMode;
    /**
     * 单据类型
     */
    @XdoSize(max = 2, message = "{单据类型长度不能超过2位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("单据类型")
    private  String listType;
    /**
     * 包装种类
     */
    @XdoSize(max = 50, message = "{包装种类长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("包装种类")
    private  String wrapType;
    /**
     * 备案清单类型
     */
    @XdoSize(max = 1, message = "{备案清单类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备案清单类型")
    private  String billType;
    /**
     * 录入单位社会信用代码
     */
    @XdoSize(max = 18, message = "{录入单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("录入单位社会信用代码")
    private  String inputCreditCode;
    /**
     * 收发货人社会信用代码
     */
    @XdoSize(max = 18, message = "{收发货人社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("收发货人社会信用代码")
    private  String tradeCreditCode;
    /**
     * 申报单位社会信用代码
     */
    @XdoSize(max = 18, message = "{申报单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位社会信用代码")
    private  String declareCreditCode;
    /**
     * 消费使用/生产销售单位单位社会信用代码
     */
    @XdoSize(max = 18, message = "{消费使用/生产销售单位单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("消费使用/生产销售单位单位社会信用代码")
    private  String ownerCreditCode;
    /**
     * 价格说明
     */
    @XdoSize(max = 32, message = "{价格说明长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("价格说明")
    private  String promiseItems;
    /**
     * 贸易国别
     */
    @XdoSize(max = 3, message = "{贸易国别长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("贸易国别")
    private  String tradeNation;
    /**
     * 入境口岸代码
     */
    @XdoSize(max = 8, message = "{入境口岸代码长度不能超过8位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("入境口岸代码")
    private  String entryPort;
    /**
     * 货物存放地点
     */
    @XdoSize(max = 100, message = "{货物存放地点长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货物存放地点")
    private  String warehouse;
    /**
     * 境外收发货人代码
     */
    @XdoSize(max = 50, message = "{境外收发货人代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外收发货人代码")
    private  String overseasShipper;
    /**
     * 境外发货人名称
     */
    @XdoSize(max = 250, message = "{境外发货人名称长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外发货人名称")
    private  String overseasShipperName;
    /**
     * 境外发货人名称 （外文）
     */
    @XdoSize(max = 200, message = "{境外发货人名称 （外文）长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外发货人名称 （外文）")
    private  String overseasShipperEname;
    /**
     * 境外收发货人地址
     */
    @XdoSize(max = 200, message = "{境外收发货人地址长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外收发货人地址")
    private  String overseasShipperAddr;
    /**
     * 境外收货人编码
     */
    @XdoSize(max = 50, message = "{境外收货人编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外收货人编码")
    private  String overseasconsigneeCode;
    /**
     * 境外收货人名称 (外文）
     */
    @XdoSize(max = 200, message = "{境外收货人名称 (外文）长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境外收货人名称 (外文）")
    private  String overseasconsigneeEname;
    /**
     * 境内收发货人名称 （外文）
     */
    @XdoSize(max = 200, message = "{境内收发货人名称 （外文） 长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内收发货人名称 （外文） ")
    private  String tradeEname;
    /**
     * 关联号码
     */
    @XdoSize(max = 255, message = "{关联号码长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("关联号码")
    private  String correlationNo;
    /**
     * 申报日期
     */
    @XdoSize(max = 20, message = "{申报日期长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报日期")
    private  String DDate;

    @ApiModelProperty("创建日期-开始")
    private  String DDateFrom;

    @ApiModelProperty("创建日期-结束")
    private  String DDateTo;
    /**
     * 航次号
     */
    @XdoSize(max = 50, message = "{航次号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("航次号")
    private  String voyageNo;
    /**
     * 报关单状态编码
     */
    @XdoSize(max = 20, message = "{报关单状态编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关单状态编码")
    private  String status;
    /**
     * 提运单号
     */
    @XdoSize(max = 50, message = "{提运单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("提运单号")
    private  String hawb;
    /**
     * 保税标识 0-保税 1-非保税
     */
    @XdoSize(max = 1, message = "{保税标识 0-保税 1-非保税长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("保税标识 0-保税 1-非保税")
    private  String bondMark;
    /**
     * 核销周期
     */
    @XdoSize(max = 5, message = "{核销周期长度不能超过5位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("核销周期")
    private  String dcrCycle;
    /**
     * 立交桥编号
     */
    @XdoSize(max = 30, message = "{立交桥编号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("立交桥编号")
    private  String ljqNo;
    /**
     * -1 未发送 0 发送失败  1发送成功 2发送中
     */
    @XdoSize(max = 3, message = "{发送状态长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("-1 未发送 0 发送失败  1发送成功 2发送中")
    private  String sendApiStatus;
    /**
     * 发送api任务id
     */
    @XdoSize(max = 36, message = "{发送api任务id长度不能超过36位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发送api任务id")
    private  String sendApiTaskId;
    /**
     * 发送api错误标志 报关金二审核结果（一般贸易使用） 0 正常 1 错误
     */
    @XdoSize(max = 1, message = "{发送api错误标志 报关金二审核结果（一般贸易使用） 0 正常 1 错误长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发送api错误标志 报关金二审核结果（一般贸易使用） 0 正常 1 错误")
    private  String sendApiErrorMark;
    /**
     * 创建日期-开始
     */
    @ApiModelProperty("创建日期-开始")
    private String insertTimeFrom;
    /**
     * 创建日期-结束
     */
    @ApiModelProperty("创建日期-结束")
    private String insertTimeTo;
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String apprStatus;

    /**
     * 委托日期
     */
    @ApiModelProperty("委托日期")
    private  Date entrustDate;
    /**
     * 委托日期-开始
     */
    @ApiModelProperty("委托日期-开始")
    private String entrustDateFrom;
    /**
     * 委托日期-结束
     */
    @ApiModelProperty("委托日期-结束")
    private String entrustDateTo;
    /**
     * 委托人
     */
    @XdoSize(max = 50, message = "{委托人长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("委托人")
    private  String entrustPerson;
    /**
     * 商检查验日期
     */
    @ApiModelProperty("商检查验日期")
    private  Date checkDate;
    /**
     * 商检查验日期-开始
     */
    @ApiModelProperty("商检查验日期-开始")
    private String checkDateFrom;
    /**
     * 商检查验日期-结束
     */
    @ApiModelProperty("商检查验日期-结束")
    private String checkDateTo;
    /**
     * 商检查验原因
     */
    @XdoSize(max = 50, message = "{商检查验原因长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商检查验原因")
    private  String checkReason;
    /**
     * 商检查验结果
     */
    @XdoSize(max = 50, message = "{商检查验结果长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商检查验结果")
    private  String checkResult;
    /**
     * 完税日期
     */
    @ApiModelProperty("完税日期")
    private  Date dutyDate;
    /**
     * 完税日期-开始
     */
    @ApiModelProperty("完税日期-开始")
    private String dutyDateFrom;
    /**
     * 完税日期-结束
     */
    @ApiModelProperty("完税日期-结束")
    private String dutyDateTo;
    /**
     * 缴税方式
     */
    @XdoSize(max = 1, message = "{缴税方式长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("缴税方式")
    private  String dutyType;
    /**
     * 完税总价
     */
    @Digits(integer = 11, fraction = 5, message = "{完税总价必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("完税总价")
    private  BigDecimal dutyTotal;
    /**
     * 关税
     */
    @Digits(integer = 11, fraction = 5, message = "{关税必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("关税")
    private  BigDecimal dutyPrice;
    /**
     * 增值税
     */
    @Digits(integer = 11, fraction = 5, message = "{增值税必须为数字,整数位最大11位,小数最大5位!}")
    @ApiModelProperty("增值税")
    private  BigDecimal taxPrice;
    /**
     * 放行日期
     */
    @ApiModelProperty("放行日期")
    private  Date passDate;
    /**
     * 放行日期-开始
     */
    @ApiModelProperty("放行日期-开始")
    private String passDateFrom;
    /**
     * 放行日期-结束
     */
    @ApiModelProperty("放行日期-结束")
    private String passDateTo;
    /**
     * 海关查验日期
     */
    @ApiModelProperty("海关查验日期")
    private  Date customsCheckDate;
    /**
     * 海关查验日期-开始
     */
    @ApiModelProperty("海关查验日期-开始")
    private String customsCheckDateFrom;
    /**
     * 海关查验日期-结束
     */
    @ApiModelProperty("海关查验日期-结束")
    private String customsCheckDateTo;
    /**
     * 海关查验原因
     */
    @XdoSize(max = 50, message = "{海关查验原因长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("海关查验原因")
    private  String customsCheckReason;
    /**
     * 海关查验结果
     */
    @XdoSize(max = 50, message = "{海关查验结果长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("海关查验结果")
    private  String customsCheckResult;
    /**
     * 财务单据号
     */
    @XdoSize(max = 255, message = "{财务单据号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("财务单据号")
    private  String financeNo;
    /**
     * 入/出库单号
     */
    @XdoSize(max = 255, message = "{入/出库单号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("入/出库单号")
    private  String inOutNo;
    /**
     * 到货通知日期
     */
    @ApiModelProperty("到货通知日期")
    private  Date noticeDate;
    /**
     * 到货通知日期-开始
     */
    @ApiModelProperty("到货通知日期-开始")
    private String noticeDateFrom;
    /**
     * 到货通知日期-结束
     */
    @ApiModelProperty("到货通知日期-结束")
    private String noticeDateTo;
    /**
     * 破损标记0-正常 1-破损
     */
    @XdoSize(max = 1, message = "{破损标记0-正常 1-破损长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("破损标记0-正常 1-破损")
    private  String damageMark;
    /**
     * 报关费用
     */
    @Digits(integer = 14, fraction = 5, message = "{报关费用必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("报关费用")
    private  BigDecimal logisticsFee;
    /**
     * 物流费用
     */
    @Digits(integer = 14, fraction = 5, message = "{物流费用必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("物流费用")
    private  BigDecimal customsFee;
    /**
     * 送货单号
     */
    @ApiModelProperty("送货单号")
    private  String deliveryNo;
    /**
     * 车牌号
     */
    @XdoSize(max = 100, message = "{车牌号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("车牌号")
    private  String plateNum;
    /**
     * 送货人
     */
    @XdoSize(max = 16, message = "{送货人长度不能超过16位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("送货人")
    private  String deliveryPerson;
    /**
     * 送货日期
     */
    @ApiModelProperty("送货日期")
    private  Date deliveryDate;
    /**
     * 送货日期-开始
     */
    @ApiModelProperty("送货日期-开始")
    private String deliveryDateFrom;
    /**
     * 送货日期-结束
     */
    @ApiModelProperty("送货日期-结束")
    private String deliveryDateTo;
    /**
     * 关联核注清单编号
     */
    @XdoSize(max = 64, message = "{关联核注清单编号长度不能超过64位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("关联核注清单编号")
    private  String relListNo;
    /**
     * 关联账册（手册）编号
     */
    @XdoSize(max = 12, message = "{关联账册（手册）编号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("关联账册（手册）编号")
    private  String relEmsNo;
    /**
     * 核注清单编号
     */
    @XdoSize(max = 64, message = "{核注清单编号长度不能超过64位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("核注清单编号")
    private  String listNo;
    /**
     * 清单申报日期
     */
    @ApiModelProperty("清单申报日期")
    private  Date billDeclareDate;
    /**
     * 清单申报日期-开始
     */
    @ApiModelProperty("清单申报日期-开始")
    private String billDeclareDateFrom;
    /**
     * 清单申报日期-结束
     */
    @ApiModelProperty("清单申报日期-结束")
    private String billDeclareDateTo;
    /**
     * 清单状态
     */
    @ApiModelProperty("清单状态")
    private String billStatus;
    /**
     * 提单内部编号
     */
    @ApiModelProperty("提单内部编号")
    private String erpEmsListNo;
    /**
     * 提单制单日期
     */
    @ApiModelProperty("提单制单日期")
    private String erpInsertTime;
    /**
     * 提单制单日期-开始
     */
    @ApiModelProperty("提单制单日期-开始")
    private String erpInsertTimeFrom;
    /**
     * 提单制单日期-结束
     */
    @ApiModelProperty("提单制单日期-结束")
    private String erpInsertTimeTo;

    /**
     * 物料类型
     */
    @ApiModelProperty("物料类型")
    private String GMark;

    /**
     * 总数量
     */
    @ApiModelProperty("总数量")
    private String qtyAll;
    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private String totalAll;


    @ApiModelProperty("制单员")
    private String entryInsertUser;


    /**
     * 关税缓征利息
     */
    @ApiModelProperty("关税缓征利息")
    private String dutyInterest;
    /**
     * 增值税缓征利息
     */
    @ApiModelProperty("增值税缓征利息")
    private String taxInterest;

//    /**
//     * 关检关联号
//     */
//    @XdoSize(max = 18, message = "{关检关联号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("关检关联号")
//    private  String copNo;
//    /**
//     * 报关/转关关系标志
//     */
//    @XdoSize(max = 1, message = "{报关/转关关系标志 长度不能超过1位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("报关/转关关系标志 ")
//    private  String declTrnrel;
//    /**
//     * 申报人标识
//     */
//    @XdoSize(max = 20, message = "{申报人标识长度不能超过20位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("申报人标识")
//    private  String partenerId;
//    /**
//     * 打印日期
//     */
//    @ApiModelProperty("打印日期")
//    private  Date printDate;
//    /**
//     * 打印日期-开始
//     */
//    @ApiModelProperty("打印日期-开始")
//    private String printDateFrom;
//    /**
//     * 打印日期-结束
//     */
//    @ApiModelProperty("打印日期-结束")
//    private String printDateTo;
//    /**
//     * 通关申请单号
//     */
//    @XdoSize(max = 18, message = "{通关申请单号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("通关申请单号")
//    private  String tgdNo;
//    /**
//     * 录入员 IC 卡号
//     */
//    @XdoSize(max = 30, message = "{录入员 IC 卡号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("录入员 IC 卡号")
//    private  String typistNo;
//    /**
//     * 担保验放标志
//     */
//    @XdoSize(max = 1, message = "{担保验放标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("担保验放标志")
//    private  String chkSurety;
//    /**
//     * 查验分流
//     */
//    @XdoSize(max = 1, message = "{查验分流长度不能超过1位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("查验分流")
//    private  String checkFlow;
//    /**
//     * 税收征管标记
//     */
//    @XdoSize(max = 1, message = "{税收征管标记长度不能超过1位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("税收征管标记")
//    private  String taxAaminMark;
//    /**
//     * 标记唛码
//     */
//    @XdoSize(max = 256, message = "{标记唛码长度不能超过256位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("标记唛码")
//    private  String markNo;
//    /**
//     * 启运港代码
//     */
//    @XdoSize(max = 8, message = "{启运港代码长度不能超过8位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("启运港代码")
//    private  String despPort;
//    /**
//     * B/L 号
//     */
//    @XdoSize(max = 50, message = "{B/L 号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("B/L 号")
//    private  String blNo;
//    /**
//     * 口岸检验检疫机关
//     */
//    @XdoSize(max = 10, message = "{口岸检验检疫机关长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("口岸检验检疫机关")
//    private  String insporgCode;
//    /**
//     * 特种业务标识
//     */
//    @XdoSize(max = 10, message = "{特种业务标识长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("特种业务标识")
//    private  String specdeclFlag;
//    /**
//     * 目的地检验检疫机关
//     */
//    @XdoSize(max = 10, message = "{目的地检验检疫机关长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("目的地检验检疫机关")
//    private  String purporgCode;
//    /**
//     * 启运日期
//     */
//    @XdoSize(max = 20, message = "{启运日期长度不能超过20位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("启运日期")
//    private  String despDate;
//    /**
//     * 卸毕日期
//     */
//    @XdoSize(max = 20, message = "{卸毕日期长度不能超过20位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("卸毕日期")
//    private  String cmpldschrgDt;
//    /**
//     * 关联理由
//     */
//    @XdoSize(max = 20, message = "{关联理由长度不能超过20位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("关联理由")
//    private  String correlationReasonflag;
//    /**
//     * 领证机关
//     */
//    @XdoSize(max = 10, message = "{领证机关长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("领证机关")
//    private  String vsaorgCode;
//    /**
//     * 原集装箱标识
//     */
//    @XdoSize(max = 1, message = "{原集装箱标识长度不能超过1位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("原集装箱标识")
//    private  String origboxFlag;
//    /**
//     * 申报人员姓名
//     */
//    @XdoSize(max = 10, message = "{申报人员姓名长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("申报人员姓名")
//    private  String declarerName;
//    /**
//     * 有无其他包装
//     */
//    @XdoSize(max = 1, message = "{有无其他包装长度不能超过1位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("有无其他包装")
//    private  String nootherPack;
//    /**
//     * 检验检疫受理机关
//     */
//    @XdoSize(max = 10, message = "{检验检疫受理机关长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("检验检疫受理机关")
//    private  String orgCode;
//    /**
//     * EDI 申报备注
//     */
//    @XdoSize(max = 128, message = "{EDI 申报备注长度不能超过128位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("EDI 申报备注")
//    private  String ediRemark;
//    /**
//     * EDI 申报备注2
//     */
//    @XdoSize(max = 128, message = "{EDI 申报备注2长度不能超过128位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("EDI 申报备注2")
//    private  String ediRemark2;
//    /**
//     * 境内收发货人检验 检疫编码
//     */
//    @XdoSize(max = 10, message = "{境内收发货人检验 检疫编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("境内收发货人检验 检疫编码")
//    private  String tradeCiqCode;
//    /**
//     * 消费使用/生产销 售单位检验检疫编 码
//     */
//    @XdoSize(max = 10, message = "{消费使用/生产销 售单位检验检疫编 码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("消费使用/生产销 售单位检验检疫编 码")
//    private  String ownerCiqCode;
//    /**
//     * 申报单位检验检疫编码
//     */
//    @XdoSize(max = 10, message = "{申报单位检验检疫编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("申报单位检验检疫编码")
//    private  String declCiqCode;
//    /**
//     * 提单表头id
//     */
//    @XdoSize(max = 36, message = "{提单表头id长度不能超过36位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("提单表头id")
//    private  String erpHeadId;
//    /**
//     * 清单表头id
//     */
//    @XdoSize(max = 36, message = "{清单表头id长度不能超过36位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("清单表头id")
//    private  String billHeadId;
//    /**
//     * 归类标志
//     */
//    @XdoSize(max = 1, message = "{归类标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("归类标志")
//    private  String classMark;
//    /**
//     * 工缴费
//     */
//    @Digits(integer = 15, fraction = 4, message = "{工缴费必须为数字,整数位最大15位,小数最大4位!}")
//    @ApiModelProperty("工缴费")
//    private  BigDecimal workUsd;
//    /**
//     * 产品有效期
//     */
//    @XdoSize(max = 20, message = "{产品有效期长度不能超过20位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("产品有效期")
//    private  String prodValiddt;
//    /**
//     * 产品保质期
//     */
//    @XdoSize(max = 20, message = "{产品保质期长度不能超过20位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("产品保质期")
//    private  String prodQgp;
//    /**
//     * 货物属性代码
//     */
//    @XdoSize(max = 20, message = "{货物属性代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("货物属性代码")
//    private  String goodsAttr;
//    /**
//     * 成份/原料/组份
//     */
//    @XdoSize(max = 400, message = "{成份/原料/组份 长度不能超过400位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("成份/原料/组份 ")
//    private  String stuff;
//    /**
//     * UN编码
//     */
//    @XdoSize(max = 20, message = "{UN编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("UN编码")
//    private  String unCode;
//    /**
//     * 危险货物名称
//     */
//    @XdoSize(max = 80, message = "{危险货物名称长度不能超过80位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("危险货物名称")
//    private  String dangName;
//    /**
//     * 危包类别
//     */
//    @XdoSize(max = 4, message = "{危包类别长度不能超过4位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("危包类别")
//    private  String dangPackType;
//    /**
//     * 危包规格
//     */
//    @XdoSize(max = 40, message = "{危包规格长度不能超过40位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("危包规格")
//    private  String dangPackSpec;
//    /**
//     * 境外生产企业名称
//     */
//    @XdoSize(max = 100, message = "{境外生产企业名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("境外生产企业名称")
//    private  String engmanentCnm;
//    /**
//     * 非危险化学品
//     */
//    @XdoSize(max = 1, message = "{非危险化学品长度不能超过1位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("非危险化学品")
//    private  String nodangFlag;
//    /**
//     * 检验检疫货物规格
//     */
//    @XdoSize(max = 255, message = "{检验检疫货物规格长度不能超过255位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("检验检疫货物规格")
//    private  String goodsSpec;
//    /**
//     * 货物型号
//     */
//    @XdoSize(max = 255, message = "{货物型号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("货物型号")
//    private  String goodsModel;
//    /**
//     * 货物品牌
//     */
//    @XdoSize(max = 255, message = "{货物品牌长度不能超过255位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("货物品牌")
//    private  String goodsBrand;
//    /**
//     * 生产日期
//     */
//    @XdoSize(max = 20, message = "{生产日期长度不能超过20位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("生产日期")
//    private  String produceDate;
//    /**
//     * 生产批号
//     */
//    @XdoSize(max = 512, message = "{生产批号长度不能超过512位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("生产批号")
//    private  String prodbatchNo;
//    /**
//     * 检验检疫名称
//     */
//    @XdoSize(max = 255, message = "{检验检疫名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("检验检疫名称")
//    private  String ciqName;
//    /**
//     * 生产单位注册号
//     */
//    @XdoSize(max = 20, message = "{生产单位注册号 长度不能超过20位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("生产单位注册号 ")
//    private  String mnufctrRegno;
//    /**
//     * 生产单位名称
//     */
//    @XdoSize(max = 150, message = "{生产单位名称 长度不能超过150位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("生产单位名称 ")
//    private  String mnufcrRegname;
//    /**
//     * 报关单表头id
//     */
//    @XdoSize(max = 36, message = "{报关单表头id长度不能超过36位字节长度(一个汉字2位字节长度)!}")
//    @ApiModelProperty("报关单表头id")
//    private  String headId;

    /**
     * 贸易条款
     */
    @ApiModelProperty("贸易条款")
    private  String tradeTerms;

    /**
     * 计划进口日期(邀请日期)
     */
    @ApiModelProperty("计划进口日期(邀请日期)")
    private Date inviteDate;

    /**
     * 计费重量
     */
    @ApiModelProperty("计费重量")
    private BigDecimal cweight;
    /**
     * 发票号码(预录入单表头)
     */
    @ApiModelProperty("发票号码")
    private  String invoiceNo;
    /**
     * 承运人(物流追踪)
     */
    @ApiModelProperty("承运人")
    private  String carried;

    /** 备案序号 */
    private BigDecimal GNo;
    /**
     * 报关单状态
     */
    @ApiModelProperty("报关单状态")
    private String entryStatus;
}
