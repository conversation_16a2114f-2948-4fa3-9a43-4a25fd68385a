package com.dcjet.cs.dto.statRpt.erp.dto;

import com.dcjet.cs.dto.statRpt.erp.PercentOfPassCommonDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@ApiModel("进出口统计dto")
@EqualsAndHashCode(callSuper = true)
public class PercentOfAgent extends PercentOfPassCommonDto {

    @ApiModelProperty("报关行Code")
    private String agentCode;

    @ApiModelProperty("报关行名称")
    private String agentName;

    private String jkCnt;

    private String ckCnt;

    private BigDecimal jkPrice;

    private BigDecimal ckPrice;

}
