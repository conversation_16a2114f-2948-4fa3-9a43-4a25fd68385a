package com.dcjet.cs.dto.returnEntry.commonSub;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 返回订阅报关单数据传参
 */
@Data
@ApiModel("订阅报关单param")
public class ReturnSubEntryParam {
    private String ieMark;
    private String entryId;
    private String seqNo;
    private String decEmsListNo;
    private String ddateFrom;
    private String ddateTo;
    private String tradeCode;
}
