package com.dcjet.cs.dto.war;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ApiModel(value = "档案管理预警返回参数")
public class WarringArchivesDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 单据内部编号
     */
    @ApiModelProperty("单据内部编号")
    private String emsListNo;
    /**
     * 报关单号
     */
    @ApiModelProperty("报关单号")
    private String entryNo;
    /**
     * 制单日期
     */
    @ApiModelProperty("制单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 未归档单据
     */
    @ApiModelProperty("未归档单据")
    private String notFiledAttach;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;

}
