package com.dcjet.cs.dto.war;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @date: 2019-7-4
 */
@Setter @Getter
@ApiModel(value = "预警管理-通关业务设置传入参数")
public class WarringPassConfigParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;

	/**
	 * 发送内审校验同品名不同HS
	 */
	private String isMatSendAuditCheckHs;

	/**
	 * 内审通过意见允许自定义
	 */
	private String isVerifyOpinionCustom;

	/**
     * 备案号
     */
	@XdoSize(max = 20, message = "{备案号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案号")
	private  String emsNo;
	/**
     * 监管方式
     */
	@XdoSize(max = 6, message = "{监管方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("监管方式")
	private  String tradeMode;
	/**
     * 必填项json串
     */
	@XdoSize(max = 1000, message = "{必填项json串长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("必填项json串")
	private  String requiredField;
	/**
	 * 企业代码
	 */
	@XdoSize(max = 1, message = "{企业代码长度不能超过1位字节长度!}")
	@ApiModelProperty("同物料保税与非保税不得同时存在 0 可同时存在，1 不可同时存在")
	private  String uniqueness;
	/**
	 *是否校验总量一致性
	 */
	@XdoSize(max = 1, message = "{是否校验总量一致性长度不能超过1位字节长度!}")
	@ApiModelProperty("是否校验总量一致性 0 否，1 是")
	private  String checksum;

	/**
	 *随附单据类型json串
	 */
	@XdoSize(max = 2000, message = "{随附单据类型json串不能超过2000位字节长度!}")
	@ApiModelProperty("随附单据类型json串")
	private  String attachType;

	/**
	 *净重校验 不校验：0 提示：1  限制：2
	 */
	@XdoSize(max = 1, message = "{净重校验不能超过1位字节长度!}")
	@ApiModelProperty("净重校验 不校验：0 提示：1  限制：2")
	private String checkNetWt;

	/**
	 *净重校验百分比
	 */
	@Digits(integer = 3, fraction = 2, message = "{净重校验百分比必须为数字,整数位最大3位,小数最大2位!}")
	@ApiModelProperty("净重校验百分比")
	private BigDecimal netWtLimit;

	/**
	 *是否校验币值（备案币值，申报币值）0否 1是
	 */
	@XdoSize(max = 1, message = "{是否校验币值不能超过1位字节长度!}")
	@ApiModelProperty("是否校验币值（备案币值，申报币值）0否 1是")
	private String checkCurr;

	/**
	 *是否校验监管方式 0否 1是
	 */
	@XdoSize(max = 1, message = "{是否校验监管方式不能超过1位字节长度!}")
	@ApiModelProperty("是否校验监管方式")
	private String checkTradeMode;

	/**
	 *总净重校验 0否 1是
	 */
	@XdoSize(max = 1, message = "{是否校验总净重不能超过1位字节长度!}")
	@ApiModelProperty("是否校验总净重")
	private String checkNetWtTotal;
	/**
	 *是否随附单据 0否 1是
	 */
	@XdoSize(max = 1, message = "{是否随附单据不能超过1位字节长度!}")
	@ApiModelProperty("是否随附单据")
	private String checkAttach;

	/**
	 * 总净重校验百分比
	 */
	@ApiModelProperty("总净重校验百分比")
	@Digits(integer = 3, fraction = 2, message = "{总净重校验百分比必须为数字,整数位最大3位,小数最大2位!}")
	private BigDecimal netWtTotalLimit;
	/**
	 *是否备案物料自动提取  0否 1是
	 */
	@XdoSize(max = 1, message = "{是否备案物料自动提取不能超过1位字节长度!}")
	@ApiModelProperty("是否备案物料自动提取")
	private String autoMat;
	/**
	 *物料大小写状态  0 全部大写 1 大小写
	 */
	@XdoSize(max = 1, message = "{物料大小写状态不能超过1位字节长度!}")
	@ApiModelProperty("物料大小写状态")
	private String caseWrite;

	/**
	 * 繁转简实体
	 */
	@ApiModelProperty("繁转简实体")
	private String  configList;  //List<GwstdTranslateConfigParam>
	/**
	 *出口管理 表头最终目的国信息自动带到表体 0 不带出 1 带出
	 */
	@ApiModelProperty("出口管理 表头最终目的国信息自动带到表体 0 不带出 1 带出")
	private String countryMode;
	/**
	 *消费税率显示 0 不显示 1 显示
	 */
	@ApiModelProperty("消费税率显示 0 不显示 1 显示")
	private String checkExciseTax;

	/**
	 * 免表信息显示 0 不显示 1 显示
	 */
	@ApiModelProperty("免表信息显示 0 不显示 1 显示")
	private String dutyFreeApp;

	/**
	 * 总净重允许误差值
	 */
	@Digits(integer = 2, fraction = 5, message = "{净重允许误差值必须为数字,整数位最大2位,小数最大5位!}")
	@ApiModelProperty("总净重允许误差值")
	private BigDecimal netWtAllowErrVal;
	/**
	 * 进出口提单提取接口数据保完税标识校验
	 */
	@ApiModelProperty("进出口提单提取接口数据保完税标识")
	private String checkBondMark;
	/**
	 * 表体必填项json串
	 */
	@XdoSize(max = 1000, message = "{必填项json串长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("表体必填项json串")
	private  String listRequiredField;

	/**
	 * 物料单价校验 不校验：0 提示：1  限制：2
	 */
	@ApiModelProperty("表体必填项json串")
	private  String checkPrice;
	/**
	 * 物料单价校验百分比
	 */
	@Digits(integer = 2, fraction = 2, message = "{物料单价校验百分比必须为数字,整数位最大2位,小数最大2位!}")
	@ApiModelProperty("物料单价校验百分比")
	private  BigDecimal priceLimite;
	/**
	 * 进出口提单生成类型 不校验：0  小提单 1 大提单
	 */
	@ApiModelProperty("进出口提单生成类型 不校验：0  小提单 1 大提单")
	private  String checkDec;
	/**
	 * 进是否校验表体单价*数量等于总价  0否 1是
	 */
	@ApiModelProperty("进是否校验表体单价*数量等于总价  0否 1是")
	private String checkTotalPrice;
	/**
	 * 表体数量小数位数
	 */
	@ApiModelProperty("表体数量小数位数")
	private BigDecimal qtyDigit;
	/**
	 * 表体法一数量小数位数
	 */
	@ApiModelProperty("表体法一数量小数位数")
	private BigDecimal qty1Digit;
	/**
	 * 表体法二数量小数位数
	 */
	@ApiModelProperty("表体法二数量小数位数")
	private BigDecimal qty2Digit;
	/**
	 * 表体净重小数位数
	 */
	@ApiModelProperty("表体净重小数位数")
	private BigDecimal netWtDigit;
	/**
	 *毛重校验值
	 */
	@ApiModelProperty("毛重校验值")
	@Digits(integer = 3, fraction = 5, message = "{毛重校验值必须为数字,整数位最大3位,小数最大5位!}")
	private BigDecimal grossWtLimit;

	/**
	 *毛重校验 不校验：0 提示：1  限制：2
	 */
	@ApiModelProperty("重校验 不校验：0 提示：1  限制：2")
	private String checkGrossWt;
	/**
	 * 生成清单类型 0 旧 1 新
	 */
	@ApiModelProperty("生成清单类型 0 旧 1 新")
	private String billType;
	/**
	 *单耗版本号默认值确认 0 不默认 1 默认m
	 */
	@ApiModelProperty("单耗版本号默认值确认 1：YYYYMM01，2：按备案号默认值，3：按单损耗最新值，4：按内审通过单损耗最新值")
	private String checkExgVersion;
	/**
	 *集装箱校验(南京LG定制)  0 不校验 1 校验
	 */
	@ApiModelProperty("集装箱校验(南京LG定制)  0 不校验 1 校验")
	private String checkContainer;
	/**
	 * 备案默认手账册号
	 */
	@XdoSize(max = 20, message = "{备案默认手账册号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案默认手账册号")
	private  String emsNoMat;
	/**
	 * 必填项json串(进口非保税)
	 */
	@XdoSize(max = 1000, message = "{必填项json串(进口非保税)长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("必填项json串(进口非保税)")
	private  String reFieldINoBond;
	/**
	 * 必填项json串(出口保税)
	 */
	@XdoSize(max = 1000, message = "{必填项json串(出口保税)长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("必填项json串(出口保税)")
	private  String reFieldE;
	/**
	 * 必填项json串(出口保税)
	 */
	@XdoSize(max = 1000, message = "{必填项json串(出口非保税)长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("必填项json串(出口保税)")
	private  String reFieldENoBond;
	/**
	 * 提单表体必填项json串(进口非保税)
	 */
	@XdoSize(max = 1000, message = "{提单表体必填项json串(进口非保税)长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("提单表体必填项json串(进口非保税)")
	private  String listReFieldINoBond;
	/**
	 * 提单表体必填项json串(进口非保税)
	 */
	@XdoSize(max = 1000, message = "{提单表体必填项json串(出口保税)长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("提单表体必填项json串(出口保税)")
	private  String listReFieldE;
	/**
	 * 提单表体必填项json串(进口非保税)
	 */
	@XdoSize(max = 1000, message = "{提单表体必填项json串(出口非保税)长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("提单表体必填项json串(出口非保税)")
	private  String listReFieldENoBond;
	/**
	 * {备案号对应单耗版本号配置json串}
	 */
	@XdoSize(max = 1000, message = "{备案号对应单耗版本号配置json串}")
	@ApiModelProperty("{备案号对应单耗版本号配置json串}")
	private  String emsNoBomConfig;
	/**
	 * {物料HSCODE校验 不校验：0 校验1}
	 */
	@XdoSize(max = 1, message = "{物料HSCODE校验 不校验：0 校验1}")
	@ApiModelProperty("{物料HSCODE校验 不校验：0 校验1}")
	private  String checkHsCode;
	/**
	 * {运费生成规则 无：0  1 淳华规则 2 生益规则}
	 */
	@XdoSize(max = 1, message = "{运费生成规则 无：0  1 淳华规则 2 生益规则}")
	@ApiModelProperty("{运费生成规则 无：0  1 淳华规则 2 生益规则}")
	private  String feeType;
	/**
	 * {新旧预录入单表头控制 0 旧 1 新}
	 */
	@XdoSize(max = 1, message = "{新旧预录入单表头控制 0 旧 1 新}")
	@ApiModelProperty("{新旧预录入单表头控制 0 旧 1 新}")
	private  String decType;
	/**
	 * {报关单发送通道类型 0.不开通、1.金二上载单一、2.立交桥上载单一、3.立交桥暂存}
	 */
	@XdoSize(max = 1, message = "{报关单发送通道类型 0.不开通、1.金二上载单一、2.立交桥上载单一、3.立交桥暂存}")
	@ApiModelProperty("{报关单发送通道类型 0.不开通、1.金二上载单一、2.立交桥上载单一、3.立交桥暂存}")
	private String entryChannelType;
	/**
	 * 定制导入类型 1 光荣定制导入 2 埃玛克定制导入
	 */
	@ApiModelProperty("定制导入类型 1 光荣定制导入 2 埃玛克定制导入")
	private  String importType;
	/**
	 * 涉证校验提醒 1、不校验 2、涉证提醒 3、超次超量提醒 4、超次超量限制
	 */
	@XdoSize(max = 1, message = "{涉证校验提醒 0.不开通、1.金二上载单一、2.立交桥上载单一、3.立交桥暂存}")
	@ApiModelProperty("涉证校验提醒")
	private String involveCertCheck;

	@ApiModelProperty("持久化报关单数据  * 0 否 1 是")
	private String persistentEntry;
	/**
	 * 手册余量预警 不校验：0 提示：1  限制：2
	 *
	 */
	@ApiModelProperty("手册余量预警 不校验：0 提示：1  限制：2")
	private String checkEmlQty;
	/**
	 * 内审通过才允许打印草单
	 *
	 */
	@ApiModelProperty("内审通过才允许打印草单")
	private String checkAppr;

	@XdoSize(max = 1, message = "{证书超次校验 0 不校验 1证书超次提醒 2证书超次限制}")
	@ApiModelProperty("证书超次校验")
	private String involveCertTimesCheck;
	/**
	 * 是否自动生成随附单据：0 :否 , 1:EXCEL  ,2:PDF
	 *
	 */
	@ApiModelProperty("是否自动生成随附单据：0 :否 , 1:EXCEL  ,2:PDF")
	private String invoiceAttach;
	/**
	 * 新表头列表选择 3  4
	 *
	 */
	@ApiModelProperty("新表头列表选择 3  4")
	private String columnCount;

	/**
	 * 税单下载 不下载：0 下载:1
	 *
	 */
	@ApiModelProperty("税单下载 不下载：0 下载:1")
	private String autoDutyform;
	/**
	 * 税金汇总配置: 0不汇总;1按报关单税金汇总;2按预录入单税金汇总
	 *
	 */
	@ApiModelProperty("税金汇总配置: 0不汇总;1按报关单税金汇总;2按预录入单税金汇总")
	private String taxSummary;
	/**
	 * 订阅报关单比对（0 否 1 是）
	 */
	@ApiModelProperty("订阅报关单比对（0 否 1 是）")
	private String entCompareFlag;
	/**
	 * 进口提单表体与物料中心产销国校验  不校验：0 提示：1  限制：2
	 *
	 */
	@ApiModelProperty("进口提单表体与物料中心产销国校验  不校验：0 提示：1  限制：2")
	private String checkCountryI;
	/**
	 * 出口提单表体与物料中心产销国校验  不校验：0 提示：1  限制：2
	 *
	 */
	@ApiModelProperty("出口提单表体与物料中心产销国校验  不校验：0 提示：1  限制：2")
	private String checkCountryE;



//	/**
//	 * 是否自动计算表头总净重  0否 1是（因前端Integer类型控件无法赋值，此处采用String类型）
//	 */
//	@ApiModelProperty("是否自动计算表头总净重  0否 1是")
//	private String autoNetWt;
//	/**
//	 * 总净重保留小数位数
//	 */
//	@ApiModelProperty("总净重保留小数位数")
//	private String autoNetWtDigit;
//	/**
//	 * 是否自动计算表头总毛重  0否 1是
//	 */
//	@ApiModelProperty("是否自动计算表头总毛重  0否 1是")
//	private String autoGrossWt;
//	/**
//	 * 总毛重保留小数位数（因前端Integer类型控件无法赋值，此处采用String类型）
//	 */
//	@ApiModelProperty("总毛重保留小数位数")
//	private String autoGrossWtDigit;
//	/**
//	 * 是否自动计算表头体积  0否 1是
//	 */
//	@ApiModelProperty("是否自动计算表头体积  0否 1是")
//	private  String autoVolume;



	/**
	 * 出口-是否自动计算表头总净重 0否 1是
	 */
	@ApiModelProperty("是否自动计算表头总净重  0否 1是")
	private String autoNetWtE;
	/**
	 * 出口-总净重保留小数位数（因前端Integer类型控件无法赋值，此处采用String类型）
	 */
	@ApiModelProperty("总净重保留小数位数")
	private String autoNetWtDigitE;
	/**
	 * 出口-是否自动计算表头总毛重  0否 1是
	 */
	@ApiModelProperty("是否自动计算表头总毛重  0否 1是")
	private String autoGrossWtE;
	/**
	 * 出口-总毛重保留小数位数（因前端Integer类型控件无法赋值，此处采用String类型）
	 */
	@ApiModelProperty("总毛重保留小数位数")
	private String autoGrossWtDigitE;
	/**
	 * 出口-是否自动计算表头体积  0否 1是
	 */
	@ApiModelProperty("是否自动计算表头体积  0否 1是")
	private String autoVolumeE;
	/**
	 * 进口-是否自动计算表头总净重 0否 1是
	 */
	@ApiModelProperty("是否自动计算表头总净重  0否 1是")
	private String autoNetWtI;
	/**
	 * 进口-总净重保留小数位数（因前端Integer类型控件无法赋值，此处采用String类型）
	 */
	@ApiModelProperty("总净重保留小数位数")
	private String autoNetWtDigitI;
	/**
	 * 进口-是否自动计算表头总毛重  0否 1是
	 */
	@ApiModelProperty("是否自动计算表头总毛重  0否 1是")
	private String autoGrossWtI;
	/**
	 * 进口-总毛重保留小数位数（因前端Integer类型控件无法赋值，此处采用String类型）
	 */
	@ApiModelProperty("总毛重保留小数位数")
	private String autoGrossWtDigitI;
	/**
	 * 进口-是否自动计算表头体积  0否 1是
	 */
	@ApiModelProperty("是否自动计算表头体积  0否 1是")
	private String autoVolumeI;

	/**
	 * 进口是否自动带入物料中心数据  0否 1是
	 */
	@ApiModelProperty("进口是否自动带入物料中心数据  0否 1是")
	private String matDataI;
	/**
	 * 出口是否自动带入物料中心数据  0否 1是
	 */
	@ApiModelProperty("出口是否自动带入物料中心数据  0否 1是")
	private String matDataE;

	/**
	 * 出口表头运抵国（地区）和表体目的国系统自动比对 不校验：0 提示：1  限制：2
	 */
	@ApiModelProperty("出口表头运抵国（地区）和表体目的国系统自动比对 不校验：0 提示：1  限制：2")
	private String checkBodyCountryE;
	/**
	 * 单耗版本号校验确认 无：0  1日月新规则(M+7位阿拉伯数字)
	 */
	@ApiModelProperty("单耗版本号校验确认 无：0  1日月新规则(M+7位阿拉伯数字)")
	private String reExgVersion;
	/**
	 * 出口备注自动获取栏位： 0 无 1 日月新出口备注生成规则
	 */
	@ApiModelProperty("出口备注自动获取栏位： 0 无 1 日月新出口备注生成规则")
	private String remarkTypeE;
	/**
	 * 表头发票号取值： 0 无  1 取表体第一个 2 取表体第一个,多个时+等 3 使用分隔符组装
	 */
	@ApiModelProperty("表头发票号取值： 0 无  1 取表体第一个 2 取表体第一个,多个时+等 3 使用分隔符组装")
	private String invoiceNoType;
	/**
	 * 表头发票号取值分隔符
	 */
	@ApiModelProperty("表头发票号取值分隔符")
	private String invoiceNoSplit;
	/**
	 * 表头合同号取值： 0 无  1 取表体第一个 2 取表体第一个,多个时+等 3 使用分隔符组装
	 */
	@ApiModelProperty("表头合同号取值： 0 无  1 取表体第一个 2 取表体第一个,多个时+等 3 使用分隔符组装")
	private String contrNoType;
	/**
	 * 表头合同号取值分隔符
	 */
	@ApiModelProperty("表头合同号取值分隔符")
	private String contrNoSplit;
	/**
	 *随附单据类型上传按钮json串
	 */
	@ApiModelProperty("随附单据类型上传按钮json串")
	@XdoSize(max = 2000, message = "{随附单据类型json串不能超过2000位字节长度!}")
	private  String attachCheckType;
	/**
	 *BOM完整性不通过是否折料
	 */
	@ApiModelProperty("BOM完整性不通过是否折料  不校验：0 提示：1  限制：2")
	private  String bomCheck;
	/**
	 * 预录入单表体毛重生成规则 0 无 1表体毛重取箱单信息毛重(泰马克)
	 */
	@ApiModelProperty("预录入单表体毛重生成规则")
	private String grossWtType;
	/**
	 * 预录入单表体体积生成规则 0 无 1表体体积取箱单信息体积(泰马克)
	 */
	@ApiModelProperty("预录入单表体体积生成规则")
	private String volumeType;
	/**
	 * 进口表体总价小数位数
	 */
	@ApiModelProperty("进口表体总价小数位数")
	private Integer decTotalDigitI;
	/**
	 * 进口表头总价小数位数
	 */
	@ApiModelProperty("进口表头总价小数位数")
	private Integer headDecTotalDigitI;
	/**
	 * 进口表体体积小数位数
	 */
	@ApiModelProperty("进口表体体积小数位数")
	private Integer volumeDigitI;
	/**
	 * 进口表头体积小数位数
	 */
	@ApiModelProperty("进口表头体积小数位数")
	private Integer headVolumeDigitI;
	/**
	 * 出口表体总价小数位数
	 */
	@ApiModelProperty("出口表体总价小数位数")
	private Integer decTotalDigitE;
	/**
	 * 出口表头总价小数位数
	 */
	@ApiModelProperty("出口表头总价小数位数")
	private Integer headDecTotalDigitE;
	/**
	 * 出口表体体积小数位数
	 */
	@ApiModelProperty("出口表体体积小数位数")
	private Integer volumeDigitE;
	/**
	 * 出口表头体积小数位数
	 */
	@ApiModelProperty("出口表头体积小数位数")
	private Integer headVolumeDigitE;
	/**
	 * 保税出口料费自动汇总
	 */
	@ApiModelProperty("保税出口料费自动汇总")
	private String autoSumMatTotalBE;
	/**
	 * 保税出口系统加工费自动汇总
	 */
	@ApiModelProperty("保税出口系统加工费自动汇总")
	private String autoSumProcessTotalBE;
	/**
	 * 保税出口客供金额自动汇总
	 */
	@ApiModelProperty("保税出口客供金额自动汇总")
	private String autoSumClientTotalBE;
	/**
	 * 总净重校验 不校验：0 提示：1
	 */
	@ApiModelProperty("总净重校验 不校验：0 提示：1")
	private String otherRateCheck;
	/**
	 * 是否获取舱单信息（0 否 1 是）
	 */
	@ApiModelProperty("是否获取舱单信息（0 否 1 是）")
	private String getManifestInfo;


	/**
	 * 进口保税申报规格型号、申报要素  规格型号：0、申报要素：1
	 */
	@ApiModelProperty("进口保税申报规格型号、申报要素  可修改：0、不可修改：1")
	private String canEditBodyIBond;
	/**
	 * 进口非保税申报规格型号、申报要素  可修改：0、不可修改：1
	 */
	@ApiModelProperty("进口非保税申报规格型号、申报要素  可修改：0、不可修改：1")
	private String canEditBodyINobond;
	/**
	 * 出口保税申报规格型号  可修改：0、不可修改：1
	 */
	@ApiModelProperty("出口保税申报规格型号、申报要素  可修改：0、不可修改：1")
	private String canEditBodyEBond;
	/**
	 * 出口非保税申报规格型号  可修改：0、不可修改：1
	 */
	@ApiModelProperty("出口非保税申报规格型号、申报要素  可修改：0、不可修改：1")
	private String canEditBodyENobond;
	/**
	 * 是否校验RCEP税率  不校验：0、校验：1
	 */
	@ApiModelProperty("是否校验RCEP税率")
	private String checkRcep;

	/**
	 * 进口是否自动汇总表头总件数  0否 1是
	 */
	@ApiModelProperty("进口是否自动汇总表头总件数  0否 1是")
	private String packNumI;
	/**
	 * 出口是否自动汇总表头总件数  0否 1是
	 */
	@ApiModelProperty("出口是否自动汇总表头总件数  0否 1是")
	private String packNumE;

	/**
	 * 税单附件采集 不下载：0 下载:1
	 */
	@ApiModelProperty("税单附件采集 不下载：0 下载:1")
	private String taxAttach;
	/**
	 * 税单信息回填报关追踪 0不开通 1开通 默认开通
	 */
	@ApiModelProperty("税单信息回填报关追踪 0不开通 1开通 默认开通")
	private String taxBackfill;
	/**
	 * 发票模板是否分页 0否 1是
	 */
	@ApiModelProperty("发票模板是否分页 0否 1是")
	private String pageConfigure;
	/**
	 * 税单附件下载 0:关 1：开
	 */
	@ApiModelProperty("税单附件下载 0:关 1：开")
	private String downloadTaxBill;
	/**
	 * 费用维护维护实际运保费 0 关 1 开
	 */
	@ApiModelProperty("费用维护维护实际运保费 0 关 1 开")
	private String actualMainCost;
	/**
	 * 订阅报关单回填  0:不回填 1：回填
	 */
	@ApiModelProperty("订阅报关单回填  0:不回填 1：回填")
	private String subscriptionEntry;


	/**
	 * 保税进口单价与上一票校验 0：不校验 1：提示 2：限制
	 */
	@ApiModelProperty("保税进口单价与上一票校验 0：不校验 1：提示 2：限制")
	private String checkIDecPrice;
	/**
	 * 保税出口单价与上一票校验 0：不校验 1：提示 2：限制
	 */
	@ApiModelProperty("保税出口单价与上一票校验 0：不校验 1：提示 2：限制")
	private String checkEDecPrice;
	/**
	 * 保税进口单重与上一票校验 0：不校验 1：提示 2：限制
	 */
	@ApiModelProperty("保税进口单重与上一票校验 0：不校验 1：提示 2：限制")
	private String checkISingleWeight;
	/**
	 * 保税出口单重与上一票校验 0：不校验 1：提示 2：限制
	 */
	@ApiModelProperty("保税出口单重与上一票校验 0：不校验 1：提示 2：限制")
	private String checkESingleWeight;
	/**
	 * 保税进口原产国与上一票校验 0：不校验 1：提示 2：限制
	 */
	@ApiModelProperty("保税进口原产国与上一票校验 0：不校验 1：提示 2：限制")
	private String checkIOriginCountry;

	/**
	 * 进口预录入单表单价与清单表体单价校验百分比
	 */
	@Digits(integer = 3, fraction = 0, message = "{保税进口单价与上一票校验百分比必须为数字,整数位最大3位!}")
	@ApiModelProperty("进口预录入单表单价与清单表体单价校验百分比")
	private BigDecimal priceIPercentage;
	/**
	 * 出口预录入单表单价与清单表体单价校验百分比
	 */
	@Digits(integer = 3, fraction = 0, message = "{保税进口单价与上一票校验百分比必须为数字,整数位最大3位!}")
	@ApiModelProperty("出口预录入单表单价与清单表体单价校验百分比")
	private BigDecimal priceEPercentage;
	/**
	 * 进口预录入单表单与清单表重单价校验百分比
	 */
	@Digits(integer = 3, fraction = 0, message = "{保税进口单重与上一票校验百分比必须为数字,整数位最大3位!}")
	@ApiModelProperty("进口预录入单表单与清单表重单价校验百分比")
	private BigDecimal singleIPercentage;
	/**
	 * 出口预录入单表单与清单表重单价校验百分比
	 */
	@Digits(integer = 3, fraction = 0, message = "{保税出口单重与上一票校验百分比必须为数字,整数位最大3位!}")
	@ApiModelProperty("出口预录入单表单与清单表重单价校验百分比")
	private BigDecimal singleEPercentage;
	/**
	 * 出口体积重比例
	 */
	@ApiModelProperty("出口体积重比例")
	@Digits(integer = 12, fraction = 5, message = "{出口体积重比例必须为数字,整数位最大12位,小数位5位!}")
	private BigDecimal bulkWeightScale;

	/**
	 * 发票模块预录入单来源 0:手动新增 1:内审通过自动产生
	 */
	@ApiModelProperty("发票模块预录入单来源 0:手动新增 1:内审通过自动产生")
	private String invoiceSource;

	/**
	 * 内销定制：0：否 1：是
	 */
	@ApiModelProperty("内销定制：0：否 1：是")
	private String unitCalculate;

	/**
	 * 同步国际物流日期：0：否 1：是
	 */
	@ApiModelProperty("同步国际物流日期：0：否 1：是")
	private String syncInterLogistics;
	/**
	 * 禁限类商编发送内审时是否校验 1：是 0：否
	 */
	@ApiModelProperty("禁限类商编发送内审时是否校验 1：是 0：否")
	private String prohibitiveLimit;
	/**
	 * 进口提运单号重复校验 0: 不校验 1: 提示 2: 限制
	 */
	@ApiModelProperty("进口提运单号重复校验 0: 不校验 1: 提示 2: 限制")
	private String repeatHawbCheck;
	/**
	 * 箱单维护校验 否 1：是
	 */
	@ApiModelProperty("箱单维护校验 否 1：是")
	private String packingMaintenanceCheck;

	/**
	 * 出口表头自动汇总(住立) 0：否 1：是
	 */
	@ApiModelProperty("出口表头自动汇总(住立) 否 1：是")
	private String packingSummary;
	/**
	 * 运单通道 0 无 1 DHL
	 */
	@ApiModelProperty("运单通道 0 无 1 DHL")
	private String waybillChannel;
	/**
	 * 标记唛码 0：不处理 1：空值默认发送N/M
	 */
	@ApiModelProperty("标记唛码 0：不处理 1：空值默认发送N/M")
	private String checkMarkNo;
	/**
	 * 审核意见 0：带默认值 1：不带值
	 */
	@ApiModelProperty("审核意见 0：带默认值 1：不带值")
	private String reviewComments;
	/**
	 * 快递下单表体发送配置 0：不发送 1：发送
	 */
	@ApiModelProperty("快递下单表体发送配置 0：不发送 1：发送")
	private String expressPlacement;
	/**
	 * 报关单随附单据 0不发送 1发送
	 */
	@ApiModelProperty("报关单随附单据 0不发送 1发送")
	private String entryDocumentsAttached;
	/**
	 * 重点商品启用 0：否 1：是
	 */
	@ApiModelProperty("重点商品启用 0：否 1：是")
	private String productActivation;
	/**
	 * 法一数量为0提示 0：不校验 1：提示 2：限制
	 */
	@ApiModelProperty("法一数量为0提示 0：不校验 1：提示 2：限制")
	private String qtyCheck;
	/**
	 * DHL关税月结账号
	 */
	@ApiModelProperty("DHL关税月结账号")
	private BigDecimal dhlDutyAccount;
	/**
	 * OCR识别发票模板分组号
	 */
	private String ocrGroupNo;
	/**
	 * OCR识别报关单模板分组号
	 */
	private String ocrEntryNo;
}
