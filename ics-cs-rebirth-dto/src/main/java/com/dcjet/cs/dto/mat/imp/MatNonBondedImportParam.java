package com.dcjet.cs.dto.mat.imp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoEnum;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate dcits
 *
 * @author: cc
 * @date: 2019-07-08
 */
@Setter
@Getter
@ApiModel(value = "非保税物料导入验证")
public class MatNonBondedImportParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 料号
     */
    @NotBlank(message = "{料号不能为空}")
    @XdoSize(max = 100, message = "{料号长度不能超过100位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("料号")
    private String copGNo;
    /**
     * 物料类型
     */
    @NotBlank(message = "{物料类型不能为空}")
    @XdoSize(max = 4, message = "{物料类型长度不能超过4位字节长度(一个汉字2位字节长度)}")
    @XdoEnum(values = {"I", "E","2","3","4","5"}, message = "{物料类型只能是I,E,2,3,4,5}", required = false)
    @ApiModelProperty("物料类型")
    private String GMark;
    /**
     * 商品编码
     */
    @NotBlank(message = "{商品编码不能为空}")
    @XdoSize(max = 10, message = "{商品编码长度不能超过10位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("商品编码")
    private String codeTS;
    /**
     * 商品名称
     */
    @NotBlank(message = "{商品名称不能为空}")
    @XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("商品名称")
    private String GName;
    /**
     * 规格型号
     */
    @NotBlank(message = "{规格型号不能为空}")
    @XdoSize(max = 255, message = "{规格型号长度不能超过255位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("规格型号")
    private String GModel;
    /**
     * 申报计量单位
     */
    @NotBlank(message = "{申报计量单位不能为空}")
    @XdoSize(max = 3, message = "{申报计量单位长度不能超过3位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("申报计量单位")
    private String unit;
    /**
     * 中文名称
     */
    @XdoSize(max = 255, message = "{中文名称长度不能超过255位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("中文名称")
    private String copGName;
    /**
     * 中文规格型号
     */
    @XdoSize(max = 255, message = "{中文规格型号长度不能超过255位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("中文规格型号")
    private String copGModel;
    /**
     * 产销国(地区)
     */
    @XdoSize(max = 3, message = "{产销国(地区)长度不能超过3位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("产销国(地区)")
    private String countryCode;
    /**
     * 单价
     */
    @Digits(integer = 11, fraction = 5, message = "{单价不能超过总长度16，小数位不能超过5位}")
    @ApiModelProperty("单价")
    private BigDecimal decPrice;
    /**
     * 币制
     */
    @XdoSize(max = 3, message = "{币制长度不能超过3位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("币制")
    private String curr;

    @XdoSize(max = 255, message = "{英文名称长度不能超过255位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("英文名称")
    private String copGNameEn;

    @XdoSize(max = 255, message = "{英文规格型号长度不能超过255位字节长度(一个汉字2位字节长度)}")
    @ApiModelProperty("英文规格型号")
    private String copGModelEn;

    @Digits(integer = 11, fraction = 5, message = "{法一比例因子不能超过总长度16，小数位不能超过5位}")
    @ApiModelProperty("法一比例因子")
    private BigDecimal factor1;

    @Digits(integer = 11, fraction = 5, message = "{法二比例因子不能超过总长度16，小数位不能超过5位}")
    @ApiModelProperty("法二比例因子")
    private BigDecimal factor2;

    @XdoSize(max = 20, message = "{ERP计量单位长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("ERP计量单位")
    private String unitErp;

    @Digits(integer = 8, fraction = 8, message = "{ERP比例因子不能超过总长度16，小数位不能超过8位}")
    @ApiModelProperty("ERP比例因子")
    private BigDecimal factorErp;

    @Digits(integer = 11, fraction = 5, message = "{净重数量不能超过总长度16，小数位不能超过5位}")
    @ApiModelProperty("净重数量")
    private BigDecimal qtyWt;

    @Digits(integer = 14, fraction = 8, message = "{对应净重不能超过总长度14，小数位不能超过8位}")
    @ApiModelProperty("对应净重")
    private BigDecimal netWt;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 有效期开始日期
     */
    @ApiModelProperty("有效期开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date recordDateStart;
    /**
     * 有效期结束日期
     */
    @ApiModelProperty("有效期结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date recordDateEnd;

    /**
     * 原始物料
     */
    @XdoSize(max=50, message="{原始物料长度不能超过50个字符(一个汉字2位字节长度)}")
    @ApiModelProperty("原始物料")
    private String originalGNo;
    /**
     * 成本中心
     */
    @XdoSize(max=30, message="{成本中心长度不能超过30个字符(一个汉字2位字节长度)}")
    @ApiModelProperty("成本中心")
    private String costCenter;
    /**
     * CIQ编码
     */
    @XdoSize(max=3, message="{CIQ代码长度不能超过3个字符(一个汉字2位字节长度)}")
    @ApiModelProperty("CIQ编码")
    private String ciqNo;
    /**
     * 客供单价
     */
    @Digits(integer = 10, fraction = 5, message = "{客供单价必须为数字,整数位最大10位,小数最大5位!}")
    @ApiModelProperty("客供单价")
    private BigDecimal clientPrice;
    /**
     * 检验检疫
     */
    @ApiModelProperty("检验检疫")
    private String inspmonitorcond;

    @XdoSize(max = 255, message = "{商品用途长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品用途")
    private String useTo;
    @XdoSize(max = 255, message = "{商品构成长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品构成")
    private String structure;
    /**
     * 客户料号
     */
    @XdoSize(max = 100, message = "{客户料号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    private String customerGNo;

    /**
     * 客户/供应商代码
     */
    @XdoSize(max = 50, message = "{客户/供应商代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户/供应商代码")
    private String supplierCode;

    /**
     * 是否是危化品
     */
    @XdoSize(max = 1, message = "{是否是危化品长度不能超过1位字节长度(一个汉字2位字节长度)}")
    @XdoEnum(values = {"0", "1"}, message = "{是否是危化品只能是0,1}", required = false)
    @ApiModelProperty("是否是危化品")
    private String isHazchem;

    /**
     * 是否是危险货物
     */
    @XdoSize(max = 1, message = "{是否是危险货物长度不能超过1位字节长度(一个汉字2位字节长度)}")
    @XdoEnum(values = {"0", "1"}, message = "{是否是危险货物只能是0,1}", required = false)
    @ApiModelProperty("是否是危险货物")
    private String isDg;
}

