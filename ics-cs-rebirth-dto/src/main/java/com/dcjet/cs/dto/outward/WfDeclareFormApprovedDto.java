package com.dcjet.cs.dto.outward;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @date: 2023-5-23
 */
@ApiModel(value = "返回信息")
@Setter
@Getter
public class WfDeclareFormApprovedDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 申报表编号
     */
    @ApiModelProperty("申报表编号")
    private String applyNo;
    /**
     * 企业内部编号
     */
    @ApiModelProperty("企业内部编号")
    private String emsCopNo;
    /**
     * 申报日期
     */
    @ApiModelProperty("申报日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date declareDate;
    /**
     * 申报表有效期
     */
    @ApiModelProperty("申报表有效期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyVaildDate;
    /**
     * 承揽者名称
     */
    @ApiModelProperty("承揽者名称")
    private String contractorTradeName;
    /**
     * 承揽者所在地主管海关
     */
    @ApiModelProperty("承揽者所在地主管海关")
    private String contractorMasterCustoms;
    /**
     * 承揽者所在地地区代码
     */
    @ApiModelProperty("承揽者所在地地区代码")
    private String contractorDistrictCode;
    /**
     * 承揽者海关代码
     */
    @ApiModelProperty("承揽者海关代码")
    private String contractorTradeCode;
    /**
     * 承揽者社会信用代码
     */
    @ApiModelProperty("承揽者社会信用代码")
    private String contractorCreditCode;
    /**
     * 承揽者联系人/电话
     */
    @ApiModelProperty("承揽者联系人/电话")
    private String contractorMobilePhone;
    /**
     * 承揽者法人/电话
     */
    @ApiModelProperty("承揽者法人/电话")
    private String contractorTelephoneNo;
    /**
     * 委托方手账册号
     */
    @ApiModelProperty("委托方手账册号")
    private String entrustEmsNo;
    /**
     * 企业代码
     */
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 委托方海关代码
     */
    @ApiModelProperty("委托方海关代码")
    private String entrustTradeCode;
    /**
     * 委托方主管海关
     */
    @ApiModelProperty("委托方主管海关")
    private String entrustMasterCustoms;
    /**
     * 委托方信用代码
     */
    @ApiModelProperty("委托方信用代码")
    private String entrustCreditCode;
    /**
     * 委托方企业名称
     */
    @ApiModelProperty("委托方企业名称")
    private String entrustTradeName;
    /**
     * 委托方申报人/电话
     */
    @ApiModelProperty("委托方申报人/电话")
    private String entrustMobilePhone;
    /**
     * 委托加工合同号
     */
    @ApiModelProperty("委托加工合同号")
    private String entrustContrNo;
    /**
     * 全工序外发标志(0-否，1-是)
     */
    @ApiModelProperty("全工序外发标志(0-否，1-是)")
    private String outSendFlag;
    /**
     * 外发申报金额(人民币)
     */
    @ApiModelProperty("外发申报金额(人民币)")
    private BigDecimal outSendDeclareAmount;
    /**
     * 外发主要货物
     */
    @ApiModelProperty("外发主要货物")
    private String outSendGoods;
    /**
     * 申报单位编码
     */
    @ApiModelProperty("申报单位编码")
    private String declareTradeCode;
    /**
     * 申报单位名称
     */
    @ApiModelProperty("申报单位名称")
    private String declareTradeName;
    /**
     * 申报单位社会信用代码
     */
    @ApiModelProperty("申报单位社会信用代码")
    private String declareCreditCode;
    /**
     * 收回主要货物
     */
    @ApiModelProperty("收回主要货物")
    private String receiveGoods;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 数据来源
     */
    @ApiModelProperty("数据来源")
    private String dataSource;
    /**
     * 承揽者企业代码
     */
    @ApiModelProperty("承揽者企业代码")
    private String contractorCompanyCode;
    /**
     * 承揽者所在地地区名称
     */
    @ApiModelProperty("承揽者所在地地区名称")
    private String contractorDistrictName;
    /**
     * 制单人姓名
     */
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
    /**
     * 显示企业名称及代码
     */
    @ApiModelProperty("显示企业名称及代码")
    private String tradeCodeAndName;
    /**
     * 申报状态(A0-暂存 A1-申报中 0-申报中 Y-申报中 A2-退单 -1-退单 Z-退单 2-退单 3-转人工 1-审核通过)
     */
    @ApiModelProperty("申报状态(A0-暂存 A1-申报中 0-申报中 Y-申报中 A2-退单 -1-退单 Z-退单 2-退单 3-转人工 1-审核通过)")
    private String appStatus;
    /**
     * 发送状态(0未发送 1发送中 2发送成功 3发送失败)
     */
    @ApiModelProperty("发送状态(0未发送 1发送中 2发送成功 3发送失败)")
    private String sendStatus;
    /**
     * 启用状态(1启用 -1停用)
     */
    @ApiModelProperty("启用状态(1启用 -1停用)")
    private String status;
    /**
     * 预录入统一编号
     */
    @ApiModelProperty("预录入统一编号")
    private String preSeqNo;
    /**
     * 变更次数
     */
    @ApiModelProperty("变更次数")
    private Integer changeTimes;
    /**
     * 备案批准日期
     */
    @ApiModelProperty("备案批准日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date filingsApprovalDate;
    /**
     * 担保编号
     */
    @ApiModelProperty("担保编号")
    private String guaranteeNo;
    /**
     * 变更批准日期
     */
    @ApiModelProperty("变更批准日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeApprovalDate;
    /**
     * 委托方法人/电话
     */
    @ApiModelProperty("委托方法人/电话")
    private String entrustLegalTelNo;
    /**
     * 委托方联系人/电话
     */
    @ApiModelProperty("委托方联系人/电话")
    private String entrustContactsTelNo;
    /**
     * 委托方地区代码
     */
    @ApiModelProperty("委托方地区代码")
    private String entrustRegionCode;
    /**
     * 实际发货总金额(人民币)
     */
    @ApiModelProperty("实际发货总金额(人民币)")
    private BigDecimal actualShipmentAmount;
    /**
     * 实际收货总金额(人民币)
     */
    @ApiModelProperty("实际收货总金额(人民币)")
    private BigDecimal actualReceiptAmount;
    /**
     * 申报类型(1-备案; 2-变更)
     */
    @ApiModelProperty("申报类型(1-备案; 2-变更)")
    private String appType;
    /**
     * 申报(发送)返回值
     */
    @ApiModelProperty("申报(发送)返回值")
    private String sendMessage;
}
