package com.dcjet.cs.dto.cert;

import com.dcjet.cs.dto.base.BasicParam;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
/**
 *
 * <AUTHOR>
 * @date: 2020-10-10
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class CertificateDeductParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 证件台账表体ID
     */
	@XdoSize(max = 40, message = "{证件台账表体ID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("证件台账表体ID")
	private  String certificateListId;
	/**
     * 提单表体ID
     */
	@XdoSize(max = 40, message = "{提单表体ID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("提单表体ID")
	private  String decErpListId;
	/**
     * 本次扣量
     */
	@Digits(integer = 14, fraction = 5, message = "{本次扣量必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("本次扣量")
	private  BigDecimal qty;
	/**
     * 证件台账表头ID
     */
	@XdoSize(max = 40, message = "{证件台账表头ID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("证件台账表头ID")
	private  String certificateId;
	/**
     * 提单表头ID
     */
	@XdoSize(max = 40, message = "{提单表头ID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("提单表头ID")
	private  String decErpHeadId;
	/**
     * 生效标记：0无效 1已扣量 2已扣次扣量
     */
	@XdoSize(max = 1, message = "{生效标记：0无效 1已扣量 2已扣次扣量长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("生效标记：0无效 1已扣量 2已扣次扣量")
	private  String validMark;
	/**
     * 控制类型
     */
	@XdoSize(max = 1, message = "{控制类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("控制类型")
	private  String ctrlType;
	/**
     * 涉证目录表头ID
     */
	@XdoSize(max = 40, message = "{涉证目录表头ID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("涉证目录表头ID")
	private  String catalogId;
	/**
     * 涉证目录表体ID
     */
	@XdoSize(max = 40, message = "{涉证目录表体ID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("涉证目录表体ID")
	private  String catalogListId;
	/**
     * 清单表头ID
     */
	@XdoSize(max = 40, message = "{清单表头ID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("清单表头ID")
	private  String billHeadId;
}
