package com.dcjet.cs.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter @Getter
@ApiModel(value = "进出口模板传入参数")
public class DecErpHeadTempleteParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;

	/**
     * 企业代码
     */
	@XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
     * 经营企业名称
     */
	@XdoSize(max = 70, message = "{经营企业名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("经营企业名称")
	private  String tradeName;
	/**
     * 经营企业社会信用代码
     */
	@XdoSize(max = 18, message = "{经营企业社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("经营企业社会信用代码")
	private  String tradeCreditCode;
	/**
     * 申报企业编码
     */
	@XdoSize(max = 10, message = "{申报企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报企业编码")
	private  String declareCode;
	/**
     * 申报企业名称
     */
	@XdoSize(max = 70, message = "{申报企业名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报企业名称")
	private  String declareName;
	/**
     * 申报企业社会信用代码
     */
	@XdoSize(max = 18, message = "{申报企业社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报企业社会信用代码")
	private  String declareCreditCode;
	/**
     * 录入企业编号
     */
	@XdoSize(max = 10, message = "{录入企业编号长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("录入企业编号")
	private  String inputCode;
	/**
     * 录入单位名称
     */
	@XdoSize(max = 70, message = "{录入单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("录入单位名称")
	private  String inputName;
	/**
     * 录入企业社会信用代码
     */
	@XdoSize(max = 18, message = "{录入企业社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("录入企业社会信用代码")
	private  String inputCreditCode;
	/**
     * 供应商编码
     */
	@XdoSize(max = 20, message = "{供应商编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("供应商编码")
	private  String supplierCode;
	/**
     * 供应商
     */
	@XdoSize(max = 100, message = "{供应商长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("供应商")
	private  String supplierName;
	/**
     * 货代编码
     */
	@XdoSize(max = 50, message = "{货代编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货代编码")
	private  String forwardCode;
	/**
     * 货代
     */
	@XdoSize(max = 100, message = "{货代长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货代")
	private  String forwardName;
	/**
     * 出境关别(出口口岸)代码
     */
	@XdoSize(max = 4, message = "{出境关别(出口口岸)代码长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("出境关别(出口口岸)代码")
	private  String IEPort;
	/**
     *申报地海关
     */
	@XdoSize(max = 4, message = "{主管海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报地海关")
	private  String masterCustoms;
	/**
     * 监管方式(贸易方式)代码
     */
	@XdoSize(max = 6, message = "{监管方式(贸易方式)代码长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("监管方式(贸易方式)代码")
	private  String tradeMode;
	/**
     * 运输方式代码
     */
	@XdoSize(max = 6, message = "{运输方式代码长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运输方式代码")
	private  String trafMode;

	/**
     * 贸易国别
     */
	@XdoSize(max = 6, message = "{贸易国别长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("贸易国别")
	private  String tradeNation;
	/**
     * 归并类型0-自动归并 1-人工归并
     */
	@XdoSize(max = 1, message = "{归并类型0-自动归并 1-人工归并长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("归并类型0-自动归并 1-人工归并")
	private  String mergeType;
	/**
     * 备注
     */
	@XdoSize(max = 512, message = "{备注长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 录入日期
     */
	@ApiModelProperty("录入日期")
	private  Date inputDate;
	/**
    * 录入日期-开始
    */
	@ApiModelProperty("录入日期-开始")
	private String inputDateFrom;
	/**
    * 录入日期-结束
    */
	@ApiModelProperty("录入日期-结束")
    private String inputDateTo;
	/**
     * 成交方式
     */
	@XdoSize(max = 6, message = "{成交方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("成交方式")
	private  String transMode;
	/**
     * 启运、运抵国别代码
     */
	@XdoSize(max = 3, message = "{启运、运抵国别代码长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("启运、运抵国别代码")
	private  String tradeCountry;
	/**
     * 启运港
     */
	@XdoSize(max = 6, message = "{启运港长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("启运港")
	private  String despPort;
	/**
     * 价格说明
     */
	@XdoSize(max = 20, message = "{价格说明长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("价格说明")
	private  String promiseItems;
	/**
     * 有效标志 0 有效，1 无效
     */
	@XdoSize(max = 1, message = "{有效标志 0 有效，1 无效长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("有效标志 0 有效，1 无效")
	private  String validMark;
	/**
     * 备案号
     */
	@XdoSize(max = 20, message = "{备案号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案号")
	private  String emsNo;
	/**
     * 许可证号
     */
	@XdoSize(max = 50, message = "{许可证号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("许可证号")
	private  String licenseNo;

	/**
     * 指运港/装货港
     */
	@XdoSize(max = 40, message = "{指运港/装货港长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("指运港/装货港")
	private  String destPort;
	/**
     * 包装种类
     */
	@XdoSize(max = 50, message = "{包装种类长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("包装种类")
	private  String wrapType;

	/**
    * 出口日期-开始
    */
	@ApiModelProperty("出口日期-开始")
	private String iEDateFrom;
	/**
    * 出口日期-结束
    */
	@ApiModelProperty("出口日期-结束")
    private String iEDateTo;
	/**
     * 境内货源地/目的地
     */
	@XdoSize(max = 5, message = "{境内货源地/目的地长度不能超过5位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境内货源地/目的地")
	private  String districtCode;
	/**
     * 收/发货人CODE
     */
	/*@XdoSize(max = 20, message = "{收/发货人CODE长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收/发货人CODE")
	private  String receiveCode;*/
	/**
     * 收/发货人NAME
     */
	/*@XdoSize(max = 255, message = "{收/发货人NAME长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收/发货人NAME")
	private  String receiveName;*/

	/**
     * 境外发货人code
     */
	@XdoSize(max = 255, message = "{境外发货人code长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境外发货人code")
	private  String overseasShipper;
	/**
     * 境外发货人name
     */
	@XdoSize(max = 255, message = "{境外发货人name长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境外发货人name")
	private  String overseasShipperName;
	/**
     * 征免性质
     */
	@XdoSize(max = 3, message = "{征免性质长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("征免性质")
	private  String cutMode;
	/**
     * 货物存放地点
     */
	@XdoSize(max = 255, message = "{货物存放地点长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货物存放地点")
	private  String warehouse;
	/**
     * 最终目的国
     */
	@XdoSize(max = 3, message = "{最终目的国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("最终目的国")
	private  String destinationCountry;
	/**
     * 入境口岸/出境口岸
     */
	@XdoSize(max = 6, message = "{入境口岸/出境口岸长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("入境口岸/出境口岸")
	private  String entryPort;
	/**
     * 审核状态
     */
	@XdoSize(max = 3, message = "{审核状态长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("审核状态")
	private  String status;
	/**
     * 进出口标志I-进口 E-出口
     */
	@XdoSize(max = 1, message = "{进出口标志I-进口 E-出口长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("进出口标志I-进口 E-出口")
	private  String IEMark;
	/**
     * 模板名称
     */
	@NotBlank(message = "{模板名称不能为空}")
	@XdoSize(max = 255, message = "{模板名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("模板名称")
	private  String tempName;
	/**
	 * 保完税标识 0 保税 1 非保税
	 */
	@ApiModelProperty("保完税标识 0 保税 1 非保税")
	private  String bondMark;

	/**
	 * shipTo代码
	 */
	@ApiModelProperty("shipTo代码")
	private  String shipTo;
	/**
	 * 申报单位编码
	 */
	@XdoSize(max = 50, message = "{申报单位编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报单位编码(基础信息)")
	private  String declareCodeCustoms;
	/**
	 * 申报单位名称
	 */
	@XdoSize(max = 100, message = "{申报单位名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报单位名称(基础信息)")
	private  String declareNameCustoms;
	/**
	 * 清单归并类型
	 */
	@ApiModelProperty("清单归类类型")
	private  String billType;
	/**
	 * 关联账册（手册）编号
	 */
	@ApiModelProperty("关联账册（手册）编号")
	private  String relEmsNo;


	private String Lister;
	/**
	 * 集装箱类型
	 */
	@ApiModelProperty("集装箱类型")
	@XdoSize(max = 20, message = "{申报单位名称长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	private  String containerType;
	/**
	 * 集装箱数量
	 */
	@ApiModelProperty("集装箱数量")
	@Digits(integer = 5, fraction = 0, message = "{集装箱数量必须为数字,整数位最大5位!}")
	private  String containerNum;
	/**
	 * 包装种类2
	 */
	@ApiModelProperty("包装种类2")
	@XdoSize(max = 50, message = "{包装种类2长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private  String wrapType2;

	/**
	 * 贸易条款
	 */
	@ApiModelProperty("贸易条款")
	@XdoSize(max = 50, message = "{贸易条款长度不能超过50位 字节长度(一个汉字2位字节长度)!}")
	private  String tradeTerms;
	/**
	 * 料件成品标记
	 */
	@ApiModelProperty("料件成品标记")
	private String GMark;
	/**
	 * 报关标志
	 */
	@ApiModelProperty("报关标志")
	private  String dclcusMark;
	/**
	 * 报关类型
	 */
	@ApiModelProperty("报关类型")
	private  String dclcusType;
	/**
	 * 报关单类型
	 */
	@ApiModelProperty("报关单类型")
	private  String entryType;

	/**
	 * 境内货源地/境内目的地(行政区域)
	 */
	@XdoSize(max = 6, message = "{境内目的地/境内目的地(行政区域)长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境内货源地/境内目的地(行政区域)")
	private  String districtPostCode;
	/**
	 * shipFrom代码
	 */
	@XdoSize(max = 50, message = "{shipFrom代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("shipFrom代码")
	private String shipFrom;
	/**
	 * 清单类型
	 */
	@ApiModelProperty("清单类型")
	private  String billListType;

	/**
	 * 进口: 计划进口日期  出口:计划出口日期(邀请日期)
	 */
	@ApiModelProperty("计划进口日期/计划出口日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date inviteDate;

	/**
	 * 计费重量
	 */
	@ApiModelProperty("计费重量")
	private BigDecimal cweight;

	/**
	 * 清单申报单位
	 */
	@ApiModelProperty("清单申报单位")
	private  String agentCode;
	/**
	 * 清单申报单位名称
	 */
	@ApiModelProperty("清单申报单位名称")
	private  String agentName;
	/**
	 * 清单申报单位社会统一信用代码
	 */
	@ApiModelProperty("清单申报单位社会信用代码")
	private  String agentCreditCode;
	/**
	 * 清单申报单位编码
	 */
	@ApiModelProperty("清单申报单位编码(基础信息)")
	private  String agentCodeCustoms;
	/**
	 * 清单申报单位名称
	 */
	@ApiModelProperty("清单申报单位名称(基础信息)")
	private  String agentNameCustoms;
	/**
	 * 单据类型 0大提单 1 小提单
	 */
	@ApiModelProperty("单据类型")
	private String decType;
}
