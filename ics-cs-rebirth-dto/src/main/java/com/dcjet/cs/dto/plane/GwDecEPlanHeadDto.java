package com.dcjet.cs.dto.plane;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021-1-25
 */
@ApiModel(value = "单证订舱表头返回信息")
@Setter
@Getter
public class GwDecEPlanHeadDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 状态，0：暂存
     */
    @ApiModelProperty("状态，0：暂存")
    private String status;
    /**
     * 订舱流水号
     */
    @ApiModelProperty("订舱流水号")
    private String bookingListNo;
    /**
     * 货代编码
     */
    @ApiModelProperty("货代编码")
    private String forwardCode;
    /**
     * 货代名称
     */
    @ApiModelProperty("货代名称")
    private String forwardName;
    /**
     * BookingID
     */
    @ApiModelProperty("BookingID")
    private String bookingId;
    /**
     * 分提运单(货代提单号)
     */
    @ApiModelProperty("分提运单(货代提单号)")
    private String hawb;
    /**
     * 放舱ETD
     */
    @ApiModelProperty("放舱ETD")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date etd;
    /**
     * 柜型柜量·
     */
    @ApiModelProperty("柜型柜量·")
    private String ctn;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递
     */
    @ApiModelProperty("运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递")
    private String trafMode;
    /**
     * 运输方式名称
     */
    @ApiModelProperty("运输方式名称")
    private String trafName;
    /**
     * 订单计划开船日
     */
    @ApiModelProperty("订单计划开船日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cietdDate;
    /**
     * 订单订舱开船日
     */
    @ApiModelProperty("订单订舱开船日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ccetdDate;
    /**
     * 最终目的国
     */
    @ApiModelProperty("最终目的国")
    private String destinationCountry;
    /**
     * 目的港
     */
    @ApiModelProperty("目的港")
    private String destPort;
    /**
     * 客户代码(售达方代码)
     */
    @ApiModelProperty("客户代码(售达方代码)")
    private String clientCode;
    /**
     * 送达方编码
     */
    @ApiModelProperty("送达方编码")
    private String deliveryNo;
    /**
     * 贸易条款
     */
    @ApiModelProperty("贸易条款")
    private String tradeTerms;
    /**
     * 贸易条款(地区)
     */
    @ApiModelProperty("贸易条款(地区)")
    private String tradeArea;

    @ApiModelProperty("售达方名称")
    private String clientName;

    @ApiModelProperty("售达方地址")
    private String address;

    @ApiModelProperty("送达方名称")
    private String deliveryName;
    /**
     * 货代单号
     */
    @ApiModelProperty("货代单号")
    private String forwardNo;
    /**
     * 跟单备注
     */
    @ApiModelProperty("跟单备注")
    private String remark;
}
