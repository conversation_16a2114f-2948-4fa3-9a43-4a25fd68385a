package com.dcjet.cs.dto.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2022-7-28
 */
@ApiModel(value = "返回信息")
@Setter
@Getter
public class ExportPlanPackingDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 表头SID
     */
    @ApiModelProperty("表头SID")
    private String headId;
    /**
     * 企业代码
     */
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 企业料号
     */
    @ApiModelProperty("企业料号")
    private String facGNo;
    /**
     * 申报数量
     */
    @ApiModelProperty("申报数量")
    private BigDecimal qty;
    /**
     * 箱数
     */
    @ApiModelProperty("箱数")
    private Integer cartonNum;
    /**
     * 托盘数(件数)
     */
    @ApiModelProperty("托盘数(件数)")
    private Integer palletNum;
    /**
     * 箱毛重
     */
    @ApiModelProperty("箱毛重")
    private BigDecimal cartonGrossWt;
    /**
     * 托毛重
     */
    @ApiModelProperty("托毛重")
    private BigDecimal palletGrossWt;
    /**
     * 箱体积
     */
    @ApiModelProperty("箱体积")
    private BigDecimal cartonVolume;
    /**
     * 托体积
     */
    @ApiModelProperty("托体积")
    private BigDecimal palletVolume;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String other;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateUser;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 制单人姓名
     */
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
    /**
     * 栈板
     */
    @ApiModelProperty("栈板")
    private String pallet;
    /**
     * 包装类型
     */
    @ApiModelProperty("包装类型")
    private String wrapType;
    /**
     * 栈板讫
     */
    @ApiModelProperty("栈板讫")
    private String completionPallet;
    /**
     * 单据号(出货单号)
     */
    @ApiModelProperty("单据号(出货单号)")
    private String linkedNo;
    /**
     * 客户料号
     */
    @ApiModelProperty("客户料号")
    private String customerGNo;
    /**
     * 包装箱从（from）
     */
    @ApiModelProperty("包装箱从（from）")
    private Integer packingFrom;
    /**
     * 包装箱到（to）
     */
    @ApiModelProperty("包装箱到（to）")
    private Integer packingTo;
    /**
     * 包装单位
     */
    @ApiModelProperty("包装单位")
    private String packingUnit;
    /**
     * 整箱数
     */
    @ApiModelProperty("整箱数")
    private Integer wholeCaseNum;
    /**
     * 单箱数量
     */
    @ApiModelProperty("单箱数量")
    private BigDecimal cartonQty;
    /**
     * 数量小计
     */
    @ApiModelProperty("数量小计")
    private BigDecimal cartonQtyTotal;
    /**
     * 销售单位(申报单位)
     */
    @ApiModelProperty("销售单位(申报单位)")
    private String unit;
    /**
     * 净重
     */
    @ApiModelProperty("净重")
    private BigDecimal netWt;
    /**
     * 净重小计
     */
    @ApiModelProperty("净重小计")
    private BigDecimal netWtTotal;
    /**
     * 毛重
     */
    @ApiModelProperty("毛重")
    private BigDecimal grossWt;
    /**
     * 毛重小计
     */
    @ApiModelProperty("毛重小计")
    private BigDecimal grossWtTotal;
    /**
     * 重量单位
     */
    @ApiModelProperty("重量单位")
    private String weightUnit;
    /**
     * 单箱-长
     */
    @ApiModelProperty("单箱-长")
    private BigDecimal cartonL;
    /**
     * 单箱-宽
     */
    @ApiModelProperty("单箱-宽")
    private BigDecimal cartonW;
    /**
     * 单箱-高
     */
    @ApiModelProperty("单箱-高")
    private BigDecimal cartonH;
    /**
     * 含栈板总毛重(KG)
     */
    @ApiModelProperty("含栈板总毛重(KG)")
    private BigDecimal allGrossWt;
    /**
     * 包装栈板-长
     */
    @ApiModelProperty("包装栈板-长")
    private BigDecimal palletL;
    /**
     * 包装栈板-宽
     */
    @ApiModelProperty("包装栈板-宽")
    private BigDecimal palletW;
    /**
     * 包装栈板-高
     */
    @ApiModelProperty("包装栈板-高")
    private BigDecimal palletH;
    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String palletUnit;
}
