package com.dcjet.cs.dto.ztyth;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @date: 2019-3-19
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class ZtythEmsImgExgParam implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 序号
     */
	@NotNull(message="{序号不能为空！}")
	@Digits(integer = 9, fraction = 0, message = "{序号必须为数字,整数位最大9位,小数最大0位!}")
	@ApiModelProperty("序号")
	private  BigDecimal GNo;
	/**
     * 手册编号
     */
	@NotEmpty(message="{手册编号不能为空！}")
	@XdoSize(max = 12, message = "{手册编号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("手册编号")
	private  String hbNo;
	@ApiModelProperty("成品料件标志 I:料件 E:成品")
	private String GMark;
	@ApiModelProperty("企业内部编号")
	private String copEmsNo;
	@ApiModelProperty("任务类型")
	private String jobType;
}
