package com.dcjet.cs.dto.erp;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-4-22
 */
@ApiModel(value = "出口表头返回信息")
@Setter
@Getter
public class DecErpEHeadNDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private String sid;
    /**
     * 单据内部编号
     */
    @ApiModelProperty("单据内部编号")
    private String emsListNo;
    /**
     * 企业代码
     */
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 经营企业名称
     */
    @ApiModelProperty("经营企业名称")
    private String tradeName;
    /**
     * 经营企业社会信用代码
     */
    @ApiModelProperty("经营企业社会信用代码")
    private String tradeCreditCode;
    /**
     * 申报单位编码
     */
    @ApiModelProperty("申报单位编码")
    private String declareCode;
    /**
     * 申报单位名称
     */
    @ApiModelProperty("申报单位名称")
    private String declareName;
    /**
     * 申报单位社会信用代码
     */
    @ApiModelProperty("申报单位社会信用代码")
    private String declareCreditCode;
    /**
     * 录入单位编号
     */
    @ApiModelProperty("录入单位编号")
    private String inputCode;
    /**
     * 录入单位名称
     */
    @ApiModelProperty("录入单位名称")
    private String inputName;
    /**
     * 录入单位社会信用代码
     */
    @ApiModelProperty("录入单位社会信用代码")
    private String inputCreditCode;
    /**
     * 供应商编码
     */
    @ApiModelProperty("供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 货代编码
     */
    @ApiModelProperty("货代编码")
    private String forwardCode;
    /**
     * 货代名称
     */
    @ApiModelProperty("货代名称")
    private String forwardName;
    /**
     * 出境关别
     */
    @ApiModelProperty("出境关别")
    private String IEPort;
    /**
     * 申报地海关
     */
    @ApiModelProperty("申报地海关")
    private String masterCustoms;
    /**
     * 监管方式
     */
    @ApiModelProperty("监管方式")
    private String tradeMode;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 料件成品标记
     */
    @ApiModelProperty("料件成品标记")
    private String GMark;
    /**
     * 贸易国别
     */
    @ApiModelProperty("贸易国别")
    private String tradeNation;
    /**
     * 归并类型0-自动归并 1-人工归并
     */
    @ApiModelProperty("归并类型0-自动归并 1-人工归并")
    private String mergeType;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 录入日期
     */
    @ApiModelProperty("录入日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inputDate;
    /**
     * 成交方式
     */
    @ApiModelProperty("成交方式")
    private String transMode;
    /**
     * 运抵国
     */
    @ApiModelProperty("运抵国")
    private String tradeCountry;
    /**
     * 启运港
     */
    @ApiModelProperty("启运港")
    private String despPort;
    /**
     * 价格说明
     */
    @ApiModelProperty("价格说明")
    private String promiseItems;
    /**
     * 有效标志 0 有效，1 无效
     */
    @ApiModelProperty("有效标志 0 有效，1 无效")
    private String validMark;
    /**
     * 备案号
     */
    @ApiModelProperty("备案号")
    private String emsNo;
    /**
     * 主提运单
     */
    @ApiModelProperty("主提运单")
    private String mawb;
    /**
     * 提运单号
     */
    @ApiModelProperty("提运单号")
    private String hawb;
    /**
     * 合同协议号
     */
    @ApiModelProperty("合同协议号")
    private String contrNo;
    /**
     * 许可证号
     */
    @ApiModelProperty("许可证号")
    private String licenseNo;
    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private String invoiceNo;
    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal packNum;
    /**
     * 体积
     */
    @ApiModelProperty("体积")
    private BigDecimal volume;
    /**
     * 总净重
     */
    @ApiModelProperty("总净重")
    private String netWt;
    /**
     * 指运港
     */
    @ApiModelProperty("指运港")
    private String destPort;
    /**
     * 包装种类
     */
    @ApiModelProperty("包装种类")
    private String wrapType;
    /**
     * 运输工具名称
     */
    @ApiModelProperty("运输工具名称")
    private String trafName;
    /**
     * 航次号
     */
    @ApiModelProperty("航次号")
    private String voyageNo;
    /**
     * 杂费币制
     */
    @ApiModelProperty("杂费币制")
    private String otherCurr;
    /**
     * 杂费类型
     */
    @ApiModelProperty("杂费类型")
    private String otherMark;
    /**
     * 杂费
     */
    @ApiModelProperty("杂费")
    private BigDecimal otherRate;
    /**
     * 运费币制
     */
    @ApiModelProperty("运费币制")
    private String feeCurr;
    /**
     * 运费类型
     */
    @ApiModelProperty("运费类型")
    private String feeMark;
    /**
     * 运费
     */
    @ApiModelProperty("运费")
    private BigDecimal feeRate;
    /**
     * 保费币制
     */
    @ApiModelProperty("保费币制")
    private String insurCurr;
    /**
     * 保费类型
     */
    @ApiModelProperty("保费类型")
    private String insurMark;
    /**
     * 保费
     */
    @ApiModelProperty("保费")
    private BigDecimal insurRate;
    /**
     * 总毛重
     */
    @ApiModelProperty("总毛重")
    private String grossWt;
    /**
     * 出口日期
     */
    @ApiModelProperty("出口日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date iedate;
    /**
     * 境内货源地
     */
    @ApiModelProperty("境内货源地")
    private String districtCode;
    /**
     * 境内发货人编码
     */
    @ApiModelProperty("境内发货人编码")
    private String receiveCode;
    /**
     * 境内发货人名称
     */
    @ApiModelProperty("境内发货人名称")
    private String receiveName;
    /**
     * 航班日期
     */
    @ApiModelProperty("航班日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date voyageDate;
    /**
     * 出货日期
     */
    @ApiModelProperty("出货日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date shipDate;
    /**
     * 境外发货人编码
     */
    @ApiModelProperty("境外发货人编码")
    private String overseasShipper;
    /**
     * 境外发货人名称
     */
    @ApiModelProperty("境外发货人名称")
    private String overseasShipperName;
    /**
     * 征免性质
     */
    @ApiModelProperty("征免性质")
    private String cutMode;
    /**
     * 货物存放地点
     */
    @ApiModelProperty("货物存放地点")
    private String warehouse;
    /**
     * 最终目的国
     */
    @ApiModelProperty("最终目的国")
    private String destinationCountry;
    /**
     * 入境口岸/出境口岸
     */
    @ApiModelProperty("入境口岸/出境口岸")
    private String entryPort;

    /**
     * 发送时间
     */
    @ApiModelProperty("发送时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date declareDate;
    /**
     * 发送人
     */
    @ApiModelProperty("发送人")
    private String sendUser;

    /**
     * 发送人
     */
    @ApiModelProperty("发送人")
    private String sendUserName;
    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date apprDate;
    /**
     * 内审员
     */
    @ApiModelProperty("内审员")
    private String apprUser;
    /**
     * 内审员
     */
    @ApiModelProperty("内审员")
    private String apprUserName;
    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String apprStatus;

    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String apprStatusName;

    /**
     * 随附单据是否上传
     */
    @ApiModelProperty("随附单据是否上传 0 无 1 有")
    private String attach;
    /**
     * 随附单据是否上传
     */
    @ApiModelProperty("随附单据是否上传 0 无 1 有")
    public String getAttachName() {
        return ("0".equals(this.attach)?"无":"有");
    }
    /**
     * 保完税标识 0 保税 1 非保税
     */
    @ApiModelProperty("保完税标识 0 保税 1 非保税")
    private String bondMark;

    /**
     * 数据来源 1录入;2导入;3数据提取;4集报提取;5深加工生成;6内销生成
     */
    @ApiModelProperty("数据来源 1录入;2导入;3数据提取;4集报提取;5深加工生成;6内销生成")
    private String dataSource;

    /**
     * 报关标志
     */
    @ApiModelProperty("报关标志")
    private String dclcusMark;
    /**
     * 报关类型
     */
    @ApiModelProperty("报关类型")
    private String dclcusType;
    /**
     * 报关单类型
     */
    @ApiModelProperty("报关单类型")
    private String entryType;
    /**
     * 申请表编号/申报表编号
     */
    @ApiModelProperty("申请表编号/申报表编号")
    private String applyNo;
    /**
     * 关联核注清单编号
     */
    @ApiModelProperty("关联核注清单编号")
    private String relListNo;
    /**
     * 关联账册（手册）编号
     */
    @ApiModelProperty("关联账册（手册）编号")
    private String relEmsNo;
    /**
     * 境内目的地(行政区域)
     */
    @ApiModelProperty("境内目的地(行政区域)")
    private String districtPostCode;
    /**
     * shipTo代码
     */
    @ApiModelProperty("shipTo代码")
    private String shipTo;
    /**
     * 申报单位编码
     */
    @ApiModelProperty("申报单位编码(基础信息)")
    private String declareCodeCustoms;
    /**
     * 申报单位名称
     */
    @ApiModelProperty("申报单位名称(基础信息)")
    private String declareNameCustoms;
    /**
     * 清单归并类型
     */
    @ApiModelProperty("清单归类类型")
    private String billType;
    /**
     * 数量汇总
     */
    @ApiModelProperty("数量汇总")
    private BigDecimal sumQty;
    /**
     * 总价汇总
     */
    @ApiModelProperty("总价汇总")
    private BigDecimal sumDecTotal;
    /**
     * 币制
     */
    @ApiModelProperty("币制")
    private String curr;

    /**
     * 报关单号
     */
    @ApiModelProperty("报关单号")
    private String entryNo;

    /**
     * 集装箱类型
     */
    @ApiModelProperty("集装箱类型")
    private String containerType;
    /**
     * 集装箱数量
     */
    @ApiModelProperty("集装箱数量")
    private String containerNum;

    /**
     * 包装种类2
     */
    @ApiModelProperty("包装种类2")
    private String wrapType2;

    /**
     * 贸易条款
     */
    @ApiModelProperty("贸易条款")
    private String tradeTerms;

    private String sendUserFull;
    public String getSendUserFull() {
        //return (this.sendUser == null ? "" : this.sendUser) + (this.sendUserName == null ? "" : "(" + this.sendUserName + ")");
        //return (this.getUpdateUser()==null?"":this.getUpdateUser())+ (this.getUpdateUserName()==null?"":"("+this.getUpdateUserName()+")");
        return getUpdateUser();
    }
    private String apprUserFull;
    public String getApprUserFull() {
        return (this.apprUser == null ? "" : this.apprUser) + (this.apprUserName == null ? "" : "(" + this.apprUserName + ")");
    }

    public String getMaker(){
        return getUserName();
    }

    /**
     * 仓库代码
     */
    @ApiModelProperty("仓库代码")
    private String warehouseCode;
    /**
     * 订单日期
     */
    @ApiModelProperty("订单日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;
    /**
     * 货物类型 0 集货 1 散货
     */
    @ApiModelProperty("货物类型 0 集货 1 散货")
    private String goodsType;
    /**
     * 柜/罐号
     */
    @ApiModelProperty("柜/罐号")
    private String tankNo;
    /**
     * 保证金金额
     */
    @ApiModelProperty("保证金金额")
    private BigDecimal bondPrice;
    /**
     * 缴纳日期
     */
    @ApiModelProperty("缴纳日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date paymentDate;
        /**
     * 清单类型
     */
    @ApiModelProperty("清单类型")
    private String billListType;
    /**
     * shipto代码
     */
    @ApiModelProperty("shipto代码")
    private  String shipToCode;
    /**
     * shipto名称
     */
    @ApiModelProperty("shipto名称")
    private  String shipToName;
    /**
     * shipto地址
     */
    @ApiModelProperty("shipto地址")
    private  String shipToAddress;
    /**
     * 内部备注
     */
    @ApiModelProperty("内部备注")
    private  String remark;
    /**
     * 清单申报单位(原报关单申报单位，生成清单时调整回去)
     */
    @ApiModelProperty("清单申报单位")
    private  String agentCode;
    /**
     * 清单申报单位名称(原报关单申报单位，生成清单时调整回去)
     */
    @ApiModelProperty("清单申报单位名称")
    private  String agentName;
    /**
     * 清单申报单位社会统一信用代码(原报关单申报单位，生成清单时调整回去)
     */
    @ApiModelProperty("清单申报单位社会信用代码")
    private  String agentCreditCode;
    /**
     * 清单申报单位编码
     */
    @ApiModelProperty("清单申报单位编码(基础信息)")
    private  String agentCodeCustoms;
    /**
     * 清单申报单位名称
     */
    @ApiModelProperty("清单申报单位名称(基础信息)")
    private  String agentNameCustoms;


    /**
     * 计划出口日期(邀请日期)
     */
    @ApiModelProperty("计划出口日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private  Date inviteDate;

    /**
     * 计费重量
     */
    @ApiModelProperty("计费重量")
    private BigDecimal cweight;

    /**
     * 生成销售单位编码
     */
    @ApiModelProperty("生成销售单位编码")
    private  String ownerCode;
    /**
     * 生成销售单位名称
     */
    @ApiModelProperty("生成销售单位名称")
    private  String ownerName;
    /**
     * 生成销售单位社会信用代码
     */
    @ApiModelProperty("生成销售单位社会信用代码")
    private  String ownerCreditCode;
    /**
     * 境外发货人AEO编码
     */
    @ApiModelProperty("境外发货人AEO编码")
    private String overseasShipperAeo;
    /**
     * 付款方式
     */
    @ApiModelProperty("付款方式")
    private String paymentMode;
    /**
     * 贸易条款(地区)
     */
    @ApiModelProperty("付款方式")
    private String tradeArea;
    /**
     * 随附单据锁定状态 0 未锁定 1 锁定
     */
    @ApiModelProperty("随附单据锁定状态")
    private String attachLock;
    /**
     * 单据类型 0大提单 1 小提单
     */
    @ApiModelProperty("单据类型")
    private String decType;
    /**
     * 预报单编号
     */
    @ApiModelProperty("预报单编号")
    private  String preEmsListNo;
    /**
     * 接单状态
     */
    @ApiModelProperty("接单状态")
    private  String status;
    /**
     * 接单状态
     */
    @ApiModelProperty("接单状态")
    private  String preStatus;
    /**
     * SA
     */
    @ApiModelProperty("SA")
    private  String sa;
    /**
     * 接单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("接单时间")
    private  Date preDate;

    /**
     * 系统加工费
     */
    @ApiModelProperty("系统加工费")
    private  BigDecimal decTotalProcess;
    /**
     * 内包装
     */
    @ApiModelProperty("内包装")
    private  String nootherPack;
    /**
     * 客供总价
     */
    @ApiModelProperty("客供总价")
    private  BigDecimal clientTotal;
    /**
     * 实收总价
     */
    @ApiModelProperty("实收总价")
    private  BigDecimal payTotal;
    /**
     * 系统加工费币制
     */
    @ApiModelProperty("系统加工费币制")
    private  String decTotalProcessCurr;
    /**
     * 客供总价币制
     */
    @ApiModelProperty("客供总价币制")
    private  String clientTotalCurr;
    /**
     * 实收总价币制
     */
    @ApiModelProperty("实收总价币制")
    private  String payTotalCurr;

    @ApiModelProperty("封装")
    private  String packages;
    /**
     * billTo代码
     */
    @ApiModelProperty("billTo代码")
    private  String billTo;
    /**
     * notify代码
     */
    @ApiModelProperty("notify代码")
    private  String notify;
    /**
     * 关联报关单收货企业社会统一信用代码
     */
    @ApiModelProperty("关联报关单收货企业社会统一信用代码")
    private String relEntryReceiveCreditCode;
    /**
     * 关联报关单海关收发货单位编码
     */
    @ApiModelProperty("关联报关单海关收发货单位编码")
    private String relEntryReceiveCode;
    /**
     * 关联报关单收发货单位名称
     */
    @ApiModelProperty("关联报关单收发货单位名称")
    private String relEntryReceiveName;
    /**
     * 报关/转关关系标志
     */
    @ApiModelProperty("报关/转关关系标志")
    private String declTrnrel;
    /**
     * 料费
     */
    @ApiModelProperty("料费")
    private BigDecimal matTotal;

    /**
     * 表体是否移动过（0 否 1 是）
     */
    @ApiModelProperty("表体是否移动过（0 否 1 是）")
    private String moveFlag;
    /**
     * 报关单通关类型
     */
    @ApiModelProperty("报关单通关类型")
    private String entryClearanceType;
    /**
     * 发票日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("发票日期")
    private Date invoiceDate;
    /**
     * 申报日期
     */
    @ApiModelProperty("申报日期")
    private String entryDeclareDate;
    /**
     * 报价单编号
     */
    @ApiModelProperty("报价单编号")
    private String quoNo;
    /**
     * 是否疫情附加费
     */
    @ApiModelProperty("是否疫情附加费")
    private String yqRateFlag;
    /**
     * 其他费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCost;
    /**
     * 第三方申报端口
     */
    @ApiModelProperty("第三方申报端口")
    private String thirdDeclare;
    /**
     * 运单通道
     */
    @ApiModelProperty("运单通道")
    private String expressChannel;
    /**
     * 运单号
     */
    @ApiModelProperty("运单号")
    private String awb;
    /**
     * DHL税款支付方式 1：DHL垫付，2：客户网服，3：DTP，4：预付，5：客户自付，7：机场自取，8：关税月结，9：汇总征税
     */
    @ApiModelProperty("DHL税款支付方式 1：DHL垫付，2：客户网服，3：DTP，4：预付，5：客户自付，7：机场自取，8：关税月结，9：汇总征税")
    private String dhlDutyMode;
    /**
     * 分送集报 0：不发送 1：发送
     */
    @ApiModelProperty("分送集报")
    private String collectedreports;

    /**
     * 投保主键
     */
    @ApiModelProperty("投保主键")
    private String insureId;

    /**
     * 投保时间
     */
    @ApiModelProperty("投保时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date insureTime;

    /**
     * 保司名称
     */
    @ApiModelProperty("保司名称")
    private String insureCompany;
}
