package com.dcjet.cs.dto.cost;

import com.dcjet.cs.dto.base.BasicParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2019-11-22
 */
@Setter
@Getter
@ApiModel(value = "费用比对查询传入参数")
public class CostCompareParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 单据内部编号
     */
    @ApiModelProperty("单据内部编号")
    private String emsListNo;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 提运单号
     */
    @ApiModelProperty("提运单号")
    private String hawb;
    /**
     * 报关单号
     */
    @ApiModelProperty("报关单号")
    private String entryNo;
    /**
     * 收款单位代码
     */
    @ApiModelProperty("收款单位代码")
    private String remitteeCode;
    /**
     * 收款单位
     */
    @ApiModelProperty("收款单位")
    private String remitteeName;
    /**
     * 账单编号
     */
    @ApiModelProperty("账单编号")
    private String payNo;
    /**
     * 科目名称
     */
    @ApiModelProperty("科目名称")
    private String costCourseName;
    /**
     * 制单日期开始
     */
    @ApiModelProperty("制单日期开始")
    private String insertTimeFrom;
    /**
     * 制单日期结束
     */
    @ApiModelProperty("制单日期结束")
    private String insertTimeTo;
}
