package com.dcjet.cs.dto.mrp;

import com.dcjet.cs.dto.base.BasicParam;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 内销历史核扣明细
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2020-05-11
 */
@ApiModel(value = "内销历史核扣明细 新增修改参数")
@Setter @Getter
public class MrpHistoryVrfdedSearchParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

    
    /**
     *料件商品编码
     */
    @ApiModelProperty(value = "料件商品编码")
    @XdoSize(max = 10, message = "{料件商品编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    private String codeTS;
    
    /**
     *备案料件料号
     */
    @ApiModelProperty(value = "备案料件料号")
    @XdoSize(max = 50, message = "{备案料件料号长度不能超过50位字节长度(一个汉字2位字节长度)!}") 
    private String copGNo;
    
    /**
     *{内销对应的清单表体ID}？T_DEC_I_BILL_LIST#SID
     */
    @ApiModelProperty(value = "{内销对应的清单表体ID}")
    @XdoSize(max = 40, message = "{内销对应的清单表体ID}")
    private String decIBillListSid;
    
    /**
     *清单企业内部编号？T_DEC_I_BILL_HEAD#EMS_LIST_NO
     */
    @ApiModelProperty(value = "清单编号")
    @XdoSize(max = 64, message = "{清单编号,长度不能超过64位字节长度(一个汉字2位字节长度)!}")
    private String listNo;
    
    /**
     *备案号
     */
    @ApiModelProperty(value = "备案号")
    @XdoSize(max = 12, message = "{备案号长度不能超过12位字节长度(一个汉字2位字节长度)!}") 
    private String emsNo;
    
    /**
     *企业料件料号
     */
    @ApiModelProperty(value = "企业料件料号")
    @XdoSize(max = 50, message = "{企业料件料号长度不能超过50位字节长度(一个汉字2位字节长度)!}") 
    private String facGNo;
    
    /**
     *料件商品名称
     */
    @ApiModelProperty(value = "料件商品名称")
    @XdoSize(max = 255, message = "{料件商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}") 
    private String gName;
    
    /**
     *备案料件序号
     */
    @ApiModelProperty(value = "备案料件序号")
    @XdoSize(max = 10, message = "{备案料件序号长度不能超过10位字节长度(一个汉字2位字节长度)!}") 
    private String gNo;
    
    /**
     *备注
     */
    @ApiModelProperty(value = "备注")
    @XdoSize(max = 512, message = "{备注长度不能超过512位字节长度(一个汉字2位字节长度)!}") 
    private String note;
    
    /**
     *内销数量
     */
    @ApiModelProperty(value = "内销数量") 
    private Long qty;
    
    /**
     *监管方式
     */
    @ApiModelProperty(value = "监管方式")
    @XdoSize(max = 6, message = "{监管方式长度不能超过6位字节长度(一个汉字2位字节长度)!}") 
    private String tradeMode;
    
}
