package com.dcjet.cs.dto.receipt;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 封装后的通用表头表体返回对象
 *
 * <AUTHOR>
 * @date: 2019-5-22
 */
@ApiModel(value = "封装后的通用表头表体返回对象")
@Setter @Getter
public class ReceiptTemplateDto implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty("通用发票表头返回对象")
	public ReceiptTemplateHeadDto headParam;

	@ApiModelProperty("通用发票表体返回对象")
	public List<ReceiptTemplateListDto> listParam;
}
