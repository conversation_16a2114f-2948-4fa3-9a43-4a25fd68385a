package com.dcjet.cs.dto.mrp;

import com.dcjet.cs.dto.base.BasicParam;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * None
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2019-11-19
 */
@ApiModel(value = "新增修改参数")
@Setter @Getter
public class MrpManageParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     *批次号
     */
    @ApiModelProperty(value = "批次号")
    @XdoSize(max = 30, message = "{批次号长度不能超过30位字节长度(一个汉字2位字节长度)!}") 
    private String batchNo;
    
    /**
     *备案号
     */
    @ApiModelProperty(value = "备案号")
    @XdoSize(max = 12, message = "{备案号长度不能超过12位字节长度(一个汉字2位字节长度)!}") 
    private String emsNo;
    
    /**
     *结束时间
     */
    @ApiModelProperty(value = "结束时间") 
    private Date endDate;

    @ApiModelProperty(value = "报核次数")
    private Integer dcrTimes;

    /**
     *备注
     */
    @ApiModelProperty(value = "备注")
    @XdoSize(max = 512, message = "{备注长度不能超过512位字节长度(一个汉字2位字节长度)!}") 
    private String note;
    
    /**
     *开始时间
     */
    @ApiModelProperty(value = "开始时间") 
    private Date startDate;
}
