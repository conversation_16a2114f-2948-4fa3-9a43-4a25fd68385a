package com.dcjet.cs.dto.tax;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@ApiModel(value = "进口税金表体信息")
@Setter
@Getter
public class TaxListIDto {
    @ApiModelProperty("主键")
    private String sid;
    @ApiModelProperty("表头id")
    private String headId;
    @ApiModelProperty("关税率")
    private BigDecimal tariffRat;//关税率
    @ApiModelProperty("关税")
    private BigDecimal dutyValue;//关税
    @ApiModelProperty("增值税")
    private BigDecimal taxValue;//增值税
    @ApiModelProperty("消费税")
    private BigDecimal exciseTax;//消费税
    @ApiModelProperty("汇率")
    private BigDecimal rate;//汇率
    @ApiModelProperty("实际完税价格-税金合计")
    private BigDecimal taxTotal;//税金合计
    @ApiModelProperty("预估关税率")
    private BigDecimal tariffRatEstimate;//税金合计
    @ApiModelProperty("预估关税")
    private BigDecimal dutyValueEstimate;//预估关税
    @ApiModelProperty("预估增值税")
    private BigDecimal taxValueEstimate;//预估增值税
    @ApiModelProperty("预估消费税")
    private BigDecimal exciseTaxEstimate;//预估消费税
    @ApiModelProperty("预估完税价格-税金合计")
    private BigDecimal taxTotalEstimate;//预估税金合计
    @ApiModelProperty("预估汇率")
    private BigDecimal rateEstimate;//预估汇率
    @ApiModelProperty("关税类型")
    private String taxType;

    /**
     * 关联表字段
     */
    @ApiModelProperty("报关序号")
    private String serialNo; //报关序号
    @ApiModelProperty("备案序号")
    private String gNo;//备案序号
    @ApiModelProperty("商品名称")
    private String gName;//商品名称
    @ApiModelProperty("商品编码")
    private String codeTS;//商品编码
    @ApiModelProperty("申报数量")
    private BigDecimal qty;//申报数量
    @ApiModelProperty("申报单位")
    private String unit;//申报单位
    @ApiModelProperty("申报单价")
    private BigDecimal decPrice;//申报单价
    @ApiModelProperty("申报总价")
    private BigDecimal decTotal;//申报总价
    @ApiModelProperty("币制")
    private String curr;//币制 
    @ApiModelProperty("法一数量")
    private BigDecimal qty1; //法一数量
    @ApiModelProperty("法一单位")
    private String unit1;//法一单位
    @ApiModelProperty("法二数量")
    private BigDecimal qty2;//法二数量
    @ApiModelProperty("法二单位")
    private String unit2;//法二单位
    @ApiModelProperty("原产国")
    private String originCountry;//原产国
    /**
     * 报关单内部编号
     */
    @ApiModelProperty("报关单内部编号")
    private String emsListNo;
}
