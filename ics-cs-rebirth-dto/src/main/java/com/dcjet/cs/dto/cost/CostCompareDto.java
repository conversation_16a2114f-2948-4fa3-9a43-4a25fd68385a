package com.dcjet.cs.dto.cost;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date: 2019-11-22
 */
@ApiModel(value = "费用比对返回信息")
@Setter
@Getter
public class CostCompareDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 单据内部编号
     */
    @ApiModelProperty("单据内部编号")
    private String emsListNo;
    /**
     * 提运单号
     */
    @ApiModelProperty("提运单号")
    private String hawb;
    /**
     * 报关单号
     */
    @ApiModelProperty("报关单号")
    private String entryNo;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 账单编号
     */
    @ApiModelProperty("账单编号")
    private String payNo;
    /**
     * 报价单编号
     */
    @ApiModelProperty("报价单编号")
    private String quoNo;
    /**
     * 收款单位代码
     */
    @ApiModelProperty("收款单位代码")
    private String remitteeCode;
    /**
     * 收款单位
     */
    @ApiModelProperty("收款单位")
    private String remitteeName;
    /**
     * 科目名称
     */
    @ApiModelProperty("科目名称")
    private String costCourseName;
    /**
     * 实际费用
     */
    @ApiModelProperty("实际费用")
    private BigDecimal actlAmt;
    /**
     * 实际费用币制
     */
    @ApiModelProperty("实际费用币制")
    private String atclCurr;
    /**
     * 预估费用
     */
    @ApiModelProperty("预估费用")
    private BigDecimal predictAmt;
    /**
     * 预估费用币制
     */
    @ApiModelProperty("预估费用币制")
    private String predictCurr;
    /**
     * 实际费用(人民币)
     */
    @ApiModelProperty("实际费用(人民币)")
    private BigDecimal actlAmtRmb;
    /**
     * 预估费用(人民币)
     */
    @ApiModelProperty("预估费用(人民币)")
    private BigDecimal predictAmtRmb;
    /**
     * 差异(人民币)
     */
    @ApiModelProperty("差异(人民币)")
    private BigDecimal diffTotal;
}
