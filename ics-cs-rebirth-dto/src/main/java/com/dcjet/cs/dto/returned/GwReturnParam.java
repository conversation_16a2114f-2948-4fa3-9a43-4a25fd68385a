package com.dcjet.cs.dto.returned;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @description:
 * @author: WJ
 * @createDate: 2021/2/25 17:25
 */
@Setter
@Getter
public class GwReturnParam {
    /**
     * 单据内部编号(最终的提单内部编号)
     */
    @ApiModelProperty("单据内部编号(最终的提单内部编号)")
    private String emsListNo;
    /**
     * 手账册号（备案号）
     */
    @ApiModelProperty("备案号")
    private String emsNo;
    /**
     * 进出口标志
     */
    @ApiModelProperty("进出口标志")
    @JsonProperty("iEMark")
    private String iEMark;
    /**
     * 状态（0.暂存 1.已匹配完成 2.已生成报关）
     */
    @ApiModelProperty("状态（0.暂存 1.已匹配完成 2.已生成报关）")
    private String status;
    /**
     * 报关单号
     */
    @ApiModelProperty("报关单号")
    private String entryNo;
    /**
     * 料号
     */
    @ApiModelProperty("料号")
    private String facGNo;
    /**
     * 商品名称(企业料件/成品)
     */
    @ApiModelProperty("商品名称(企业料件/成品)")
    private String copGName;
    /**
     * 监管方式
     */
    @ApiModelProperty("监管方式")
    private String tradeMode;
    /**
     * 差异 大于0； 1 小于0； -1 等于0：0
     */
    @ApiModelProperty("差异")
    private String differences;

    private String tradeCode;
}
