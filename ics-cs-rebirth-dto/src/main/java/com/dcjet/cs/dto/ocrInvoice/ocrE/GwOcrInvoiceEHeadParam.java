package com.dcjet.cs.dto.ocrInvoice.ocrE;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2021-9-29
 */
@Setter @Getter
@ApiModel(value = "GwOcrInvoiceEHeadParam")
public class GwOcrInvoiceEHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 发票号码
     */
//	@NotEmpty(message="{发票号码不能为空！}")
	@XdoSize(max = 50, message = "{发票号码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发票号码")
	private String invoiceNo;
	/**
     * 发票日期
     */
	@ApiModelProperty("发票日期")
	private Date invoiceDate;
	/**
    * 发票日期-开始
    */
	@ApiModelProperty("发票日期-开始")
	private String invoiceDateFrom;
	/**
    * 发票日期-结束
    */
	@ApiModelProperty("发票日期-结束")
    private String invoiceDateTo;
	/**
     * 装箱单号码
     */
	@XdoSize(max = 50, message = "{装箱单号码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("装箱单号码")
	private String packingNo;
	/**
     * 关联号码
     */
	@XdoSize(max = 50, message = "{关联号码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联号码")
	private String referenceNo;
	/**
     * 发货人
     */
	@XdoSize(max = 255, message = "{发货人长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发货人")
	private String shipper;
	/**
     * 境外发货人代码
     */
	@XdoSize(max = 30, message = "{境外发货人代码长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境外发货人代码")
	private String shipperCode;
	/**
     * 收货人
     */
	@XdoSize(max = 255, message = "{收货人长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收货人")
	private String consignee;
	/**
     * 贸易条款
     */
	@XdoSize(max = 3, message = "{贸易条款长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("贸易条款")
	private String tradeTerms;
	/**
     * 总数量
     */
	@Digits(integer = 10, fraction = 5, message = "{总数量必须为数字,整数位最大10位,小数最大5位!}")
	@ApiModelProperty("总数量")
	private BigDecimal qty;
	/**
     * 总数量单位
     */
	@XdoSize(max = 20, message = "{总数量单位长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("总数量单位")
	private String unit;
	/**
     * 转换后单位
     */
	@XdoSize(max = 3, message = "{转换后单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转换后单位")
	private String unitConvert;
	/**
     * 总金额
     */
	@Digits(integer = 13, fraction = 5, message = "{总金额必须为数字,整数位最大13位,小数最大5位!}")
	@ApiModelProperty("总金额")
	private BigDecimal totalAmount;
	/**
     * 币制
     */
	@XdoSize(max = 20, message = "{币制长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("币制")
	private String curr;
	/**
     * 转换后币制
     */
	@XdoSize(max = 3, message = "{转换后币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转换后币制")
	private String currConvert;
	/**
     * 统一原产国
     */
	@XdoSize(max = 20, message = "{统一原产国长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("统一原产国")
	private String originCountry;
	/**
     * 转换后原产国
     */
	@XdoSize(max = 3, message = "{转换后原产国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转换后原产国")
	private String originCountryConvert;
	/**
     * 统一订单号
     */
	@XdoSize(max = 50, message = "{统一订单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("统一订单号")
	private String orderNo;
	/**
     * 托盘数(件数)
     */
	@Digits(integer = 5, fraction = 0, message = "{托盘数(件数)必须为数字,整数位最大5位,小数最大0位!}")
	@ApiModelProperty("托盘数(件数)")
	private Integer palletNum;
	/**
     * 散箱数
     */
	@Digits(integer = 5, fraction = 0, message = "{散箱数必须为数字,整数位最大5位,小数最大0位!}")
	@ApiModelProperty("散箱数")
	private Integer bulkCtns;
	/**
     * 总箱数
     */
	@Digits(integer = 5, fraction = 0, message = "{总箱数必须为数字,整数位最大5位,小数最大0位!}")
	@ApiModelProperty("总箱数")
	private Integer totalCtns;
	/**
     * 总件数
     */
	@Digits(integer = 5, fraction = 0, message = "{总件数必须为数字,整数位最大5位,小数最大0位!}")
	@ApiModelProperty("总件数")
	private Integer packNum;
	/**
     * 总净重
     */
	@Digits(integer = 13, fraction = 5, message = "{总净重必须为数字,整数位最大13位,小数最大5位!}")
	@ApiModelProperty("总净重")
	private BigDecimal netWt;
	/**
     * 净重单位
     */
	@XdoSize(max = 20, message = "{净重单位长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("净重单位")
	private String netWtUnit;
	/**
     * 转换后净重单位
     */
	@XdoSize(max = 3, message = "{转换后净重单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转换后净重单位")
	private String netWtUnitConvert;
	/**
     * 总毛重
     */
	@Digits(integer = 13, fraction = 5, message = "{总毛重必须为数字,整数位最大13位,小数最大5位!}")
	@ApiModelProperty("总毛重")
	private BigDecimal grossWt;
	/**
     * 毛重单位
     */
	@XdoSize(max = 20, message = "{毛重单位长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("毛重单位")
	private String grossWtUnit;
	/**
     * 转换后毛重单位
     */
	@XdoSize(max = 3, message = "{转换后毛重单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转换后毛重单位")
	private String grossWtUnitConvert;
	/**
     * 总体积
     */
	@Digits(integer = 13, fraction = 5, message = "{总体积必须为数字,整数位最大13位,小数最大5位!}")
	@ApiModelProperty("总体积")
	private BigDecimal volume;
	/**
     * 生成状态(1已识别 2已生成)
     */
	@NotEmpty(message="{生成状态(1已识别 2已生成)不能为空！}")
	@XdoSize(max = 1, message = "{生成状态(1已识别 2已生成)长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("生成状态(1已识别 2已生成)")
	private String createStatus;
	/**
     * 修改状态（1未修改；2已修改）
     */
	@NotEmpty(message="{修改状态（1未修改；2已修改）不能为空！}")
	@XdoSize(max = 1, message = "{修改状态（1未修改；2已修改）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修改状态（1未修改；2已修改）")
	private String modifyStatus;
	/**
     * 扫描时间(暂时写入插入时间)
     */
	@ApiModelProperty("扫描时间(暂时写入插入时间)")
	private Date scanTime;
	/**
     * 生成到预录入单后返回的表头ID
     */
	@XdoSize(max = 40, message = "{生成到预录入单后返回的表头ID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("生成到预录入单后返回的表头ID")
	private String headId;
	/**
     * 企业编码
     */
	@NotEmpty(message="{企业编码不能为空！}")
	@XdoSize(max = 10, message = "{企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业编码")
	private String tradeCode;
	/**
     * 创建人名称
     */
	@NotEmpty(message="{创建人名称不能为空！}")
	@XdoSize(max = 30, message = "{创建人名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("创建人名称")
	private String insertUserName;
	/**
     * 修改人名称
     */
	@XdoSize(max = 30, message = "{修改人名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修改人名称")
	private String updateUserName;
	/**
     * 数据类型(INVOICE.发票 PACKING.箱单)
     */
	@NotEmpty(message="{数据类型(INVOICE.发票 PACKING.箱单)不能为空！}")
	@XdoSize(max = 20, message = "{数据类型(INVOICE.发票 PACKING.箱单)长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("数据类型(INVOICE.发票 PACKING.箱单)")
	private String dataType;

	@ApiModelProperty("单据内部编号")
	private String emsListNo;

	@ApiModelProperty("任务ID")
	private String taskId;

	@ApiModelProperty("主提运单号")
	@XdoSize(max = 50, message = "{主提运单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private String mawb;

	@ApiModelProperty("分提运单号")
	@XdoSize(max = 50, message = "{分提运单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private String hawb;

	@ApiModelProperty("货代")
	@XdoSize(max = 255, message = "{货代长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	private String forwarder;

	@ApiModelProperty("启运港")
	@XdoSize(max = 50, message = "{启运港长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private String depport;

	@ApiModelProperty("目的港")
	@XdoSize(max = 50, message = "{目的港长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private String desport;

	@ApiModelProperty("提单日期")
	@XdoSize(max = 50, message = "{提单日期长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private String billDate;

	@ApiModelProperty("航班号")
	@XdoSize(max = 50, message = "{航班号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private String fltNo;

	@ApiModelProperty("航班日期")
	@XdoSize(max = 50, message = "{航班日期长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private String fltDate;

	@ApiModelProperty("头程航班号")
	@XdoSize(max = 50, message = "{头程航班号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private String fltNo1st ;

	@ApiModelProperty("头程航班日期")
	@XdoSize(max = 50, message = "{头程航班日期长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	private String fltDate1st;

	@ApiModelProperty("运费")
	@Digits(integer = 14, fraction = 5, message = "{运费必须为数字,整数位最大14位,小数最大5位!}")
	private BigDecimal freight;

	@ApiModelProperty("计费重量")
	@Digits(integer = 5, fraction = 2, message = "{计费重量必须为数字,整数位最大5位,小数最大2位!}")
	private BigDecimal totalChwt;
}
