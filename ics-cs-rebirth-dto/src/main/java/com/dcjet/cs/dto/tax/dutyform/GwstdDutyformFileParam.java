package com.dcjet.cs.dto.tax.dutyform;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2022-4-21
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class GwstdDutyformFileParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 税费单号
     */
	@NotEmpty(message="{税费单号不能为空！}")
	@XdoSize(max = 64, message = "{税费单号长度不能超过64位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("税费单号")
	private  String taxVouNo;
	/**
     * 华为云的核对单文件地址
     */
	@XdoSize(max = 255, message = "{华为云的核对单文件地址长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("华为云的核对单文件地址")
	private  String checkObsUrl;
	/**
     * 最终的核对单文件地址(云环境存obs地址,本地存本地路径)
     */
	@XdoSize(max = 255, message = "{最终的核对单文件地址(云环境存obs地址,本地存本地路径)长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("最终的核对单文件地址(云环境存obs地址,本地存本地路径)")
	private  String checkFinalPath;
	/**
     * 本地部署核对单下载出错的原因
     */
	@XdoSize(max = 1000, message = "{本地部署核对单下载出错的原因长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("本地部署核对单下载出错的原因")
	private  String checkDownErr;
	/**
     * 华为云的缴款书文件地址
     */
	@XdoSize(max = 255, message = "{华为云的缴款书文件地址长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("华为云的缴款书文件地址")
	private  String payObsUrl;
	/**
     * 最终的缴款书文件地址(云环境存obs地址,本地存本地路径)
     */
	@XdoSize(max = 255, message = "{最终的缴款书文件地址(云环境存obs地址,本地存本地路径)长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("最终的缴款书文件地址(云环境存obs地址,本地存本地路径)")
	private  String payFinalPath;
	/**
     * 本地部署缴款书下载出错的原因
     */
	@XdoSize(max = 255, message = "{本地部署缴款书下载出错的原因长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("本地部署缴款书下载出错的原因")
	private  String payDownErr;
}
