package com.dcjet.cs.dto.plane;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021-1-25
 */
@Setter
@Getter
@ApiModel(value = "发货订舱柜型信息返回参数")
public class GwDecESendHeadListDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 状态，0：暂存、1 发货单号已生成、2 发票号已生成、3 已提取
     */
    @ApiModelProperty("状态，0：暂存、1 发货单号已生成、2 发票号已生成、3 已提取")
    private String status;
    /**
     * 柜型
     */
    @ApiModelProperty("柜型")
    private String cabinetType;
    /**
     * 柜号
     */
    @ApiModelProperty("柜号")
    private String containerNo;
    /**
     * 拖柜日期
     */
    @ApiModelProperty("拖柜日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cabinetDate;
    /**
     * 分票号
     */
    @ApiModelProperty("分票号")
    private String branchNo;
    /**
     * 拖柜备注
     */
    @ApiModelProperty("拖柜备注")
    private String note;
    /**
     * 订舱流水号
     */
    @ApiModelProperty("订舱流水号")
    private String bookingListNo;
    /**
     * 申请订舱表头SID
     */
    @ApiModelProperty("申请订舱表头SID")
    private String headId;
    /**
     * 发货单号
     */
    @ApiModelProperty("发货单号")
    private String billNo;
    /**
     * 发票号
     */
    @ApiModelProperty("发票号")
    private String invoiceNo;
    /**
     * 货代编码
     */
    @ApiModelProperty("货代编码")
    private String forwardCode;
    /**
     * 货代名称
     */
    @ApiModelProperty("货代名称")
    private String forwardName;
    /**
     * BookingID
     */
    @ApiModelProperty("BookingID")
    private String bookingId;
    /**
     * 分提运单(货代提单号)
     */
    @ApiModelProperty("分提运单(货代提单号)")
    private String hawb;
    /**
     * 放舱ETD
     */
    @ApiModelProperty("放舱ETD")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date etd;
    /**
     * 柜型柜量·
     */
    @ApiModelProperty("柜型柜量")
    private String ctn;
    /**
     * 单证备注
     */
    @ApiModelProperty("单证备注")
    private String remark;
    /**
     * 运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递
     */
    @ApiModelProperty("运输方式 A：海运，B：公路，C：铁路，D：空运，E：快递")
    private String trafMode;
    /**
     * 运输方式名称
     */
    @ApiModelProperty("运输方式名称")
    private String trafName;
    /**
     * 订单计划开船日
     */
    @ApiModelProperty("订单计划开船日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cietdDate;
    /**
     * 订单订舱开船日
     */
    @ApiModelProperty("订单订舱开船日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ccetdDate;
    /**
     * 最终目的国
     */
    @ApiModelProperty("最终目的国")
    private String destinationCountry;
    /**
     * 目的港
     */
    @ApiModelProperty("目的港")
    private String destPort;
    /**
     * 客户代码(售达方代码)
     */
    @ApiModelProperty("客户代码(售达方代码)")
    private String clientCode;
    /**
     * 送达方编码
     */
    @ApiModelProperty("送达方编码")
    private String deliveryNo;
    /**
     * 贸易条款
     */
    @ApiModelProperty("贸易条款")
    private String tradeTerms;
    /**
     * 贸易条款(地区)
     */
    @ApiModelProperty("贸易条款(地区)")
    private String tradeArea;
    /**
     * 分单号对应表体数
     */
    @ApiModelProperty("分单号对应表体数")
    private String listCount;
    /**
     * 提取日期
     */
    @ApiModelProperty("提取日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date extractTime;
    @ApiModelProperty("售达方名称")
    private String clientName;

    @ApiModelProperty("售达方地址")
    private String address;

    @ApiModelProperty("送达方名称")
    private String deliveryName;
    /**
     * 单据号
     */
    @ApiModelProperty("单据号")
    private String linkedNo;
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    private Integer lineNo;
    /**
     * 计划行编码
     */
    @ApiModelProperty("计划行编码")
    private Integer planLineNo;
    /**
     * 保完税标记
     */
    @ApiModelProperty("保完税标记")
    private String bondMark;
    /**
     * 物料类型 I 料件  E 成品
     */
    @ApiModelProperty("物料类型 I 料件  E 成品")
    @JsonProperty("gMark")
    private String gMark;
    /**
     * 企业料号
     */
    @ApiModelProperty("企业料号")
    private String facGNo;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String GName;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal qty;
    /**
     * 交易单位
     */
    @ApiModelProperty("交易单位")
    private String unitErp;
    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal decPrice;
    /**
     * 总价
     */
    @ApiModelProperty("总价")
    private BigDecimal decTotal;
    /**
     * 币制
     */
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 净重
     */
    @ApiModelProperty("净重")
    private BigDecimal netWt;
    /**
     * 毛重
     */
    @ApiModelProperty("毛重")
    private BigDecimal grossWt;
    /**
     * SO号(销售订单号)
     */
    @ApiModelProperty("SO号(销售订单号)")
    private String soNo;
    /**
     * SO行号(销售订单行号)
     */
    @ApiModelProperty("SO行号(销售订单行号)")
    private Integer soLineNo;
    /**
     * SO日期(销售订单日期)
     */
    @ApiModelProperty("SO日期(销售订单日期)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date soDate;
    /**
     * 送达方PO号
     */
    @ApiModelProperty("送达方PO号")
    private String deliveryPoNo;
    /**
     * 送达方PO行号
     */
    @ApiModelProperty("送达方PO行号")
    private Integer deliveryPoLineNo;
    /**
     * 售达方PO号
     */
    @ApiModelProperty("售达方PO号")
    private String soldPoNo;
    /**
     * 售达方PO行号
     */
    @ApiModelProperty("售达方PO行号")
    private Integer soldPoLineNo;
    /**
     * ERP创建时间
     */
    @ApiModelProperty("ERP创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastModifyDate;
    /**
     * 境内货源地
     */
    @ApiModelProperty("境内货源地")
    private String districtCode;
    /**
     * 卖场订单DI PO
     */
    @ApiModelProperty("卖场订单DI PO")
    private String marketOrderNo;
    /**
     * 台/箱
     */
    @ApiModelProperty("台/箱")
    private Integer containerNum;
    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal packNum;
    /**
     * 体积
     */
    @ApiModelProperty("体积")
    private BigDecimal volume;
    /**
     * 拒绝原因
     */
    @ApiModelProperty("拒绝原因")
    private String refusedReason;
    /**
     * 客户料号
     */
    @ApiModelProperty("客户料号")
    private String customerGNo;
    /**
     * plan表SID
     */
    @ApiModelProperty("plan表SID")
    private String listSid;
    /**
     * 货代单号
     */
    @ApiModelProperty("货代单号")
    private String forwardNo;
    /**
     * 发送SAP流水单号
     */
    @ApiModelProperty("发送SAP流水单号")
    private String sendNo;

    @ApiModelProperty("表体发货单状态")
    private String listStatus;

    @ApiModelProperty("shipment_id")
    private String shipmentId;
}
