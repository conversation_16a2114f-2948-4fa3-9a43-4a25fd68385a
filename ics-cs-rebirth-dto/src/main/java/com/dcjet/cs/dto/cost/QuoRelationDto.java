package com.dcjet.cs.dto.cost;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2020-7-9
 */
@ApiModel(value = "返回信息")
@Setter
@Getter
public class QuoRelationDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private String sid;
    /**
     * 报价单表头sid
     */
    @ApiModelProperty("报价单表头sid")
    private String quoSid;
    /**
     * 报价单编号
     */
    @ApiModelProperty("报价单编号")
    private String quoNo;
    /**
     * 报价单名称
     */
    @ApiModelProperty("报价单名称")
    private String quoName;
    /**
     * 报价单类型
     */
    @ApiModelProperty("报价单类型")
    private String quoType;
    /**
     * 报价单表头备注
     */
    @ApiModelProperty("报价单表头备注")
    private String quoNote;
    /**
     * 报价汇总
     */
    @ApiModelProperty("报价汇总")
    private BigDecimal decTotal;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 对应供应商报价sid
     */
    @ApiModelProperty("对应供应商报价sid")
    private String headId;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateUser;

    private String effDateFrom;
    private String effDateTo;
    private String exchequerType;
    private String exchequerName;
    private String trafMode;
    private String tradeTerms;
    private String destPort;
    private String despPort;
}
