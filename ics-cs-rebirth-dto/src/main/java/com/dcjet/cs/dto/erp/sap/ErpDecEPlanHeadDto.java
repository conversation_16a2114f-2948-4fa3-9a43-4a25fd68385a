package com.dcjet.cs.dto.erp.sap;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-9-5
 */
@ApiModel(description = "ERP出口计划表头返回信息")
@Setter
@Getter
public class ErpDecEPlanHeadDto extends BasicDto {

    /**
     * 出货ID
     */
    @ApiModelProperty("出货ID")
    private  String linkedNo;
    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private  String invoiceNo;
    /**
     * 出货编号
     */
    @ApiModelProperty("出货编号")
    private  String shipmentNo;
    /**
     * 客户代码
     */
    @ApiModelProperty("客户代码")
    private  String clientCode;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private  String clientName;
    /**
     * 出货日期
     */
    @ApiModelProperty("出货日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private  Date shipmentDate;
    /**
     * 收货地址
     */
    @ApiModelProperty("收货地址")
    private  String shippingAddress;
    /**
     * 标记唛头
     */
    @ApiModelProperty("标记唛头")
    private  String marks;
    /**
     * 交货单号
     */
    @ApiModelProperty("交货单号")
    private  String deliveryOrderNo;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private  String trafMode;
    /**
     * 付款方式
     */
    @ApiModelProperty("付款方式")
    private  String paymentMode;
    /**
     * SHIP TO
     */
    @ApiModelProperty("SHIP TO")
    private  String shipTo;
    /**
     * 收货方
     */
    @ApiModelProperty("收货方")
    private  String receivingParty;
    /**
     * 通知方
     */
    @ApiModelProperty("通知方")
    private  String notifyParty;
    /**
     * 启运港
     */
    @ApiModelProperty("启运港")
    private  String despPort;
    /**
     * 目的港
     */
    @ApiModelProperty("目的港")
    private  String destPort;
    /**
     * 外包装数量
     */
    @ApiModelProperty("外包装数量")
    private  BigDecimal packingQty;
    /**
     * 主要品名
     */
    @ApiModelProperty("主要品名")
    private  String copGName;
    /**
     * 总毛重
     */
    @ApiModelProperty("总毛重")
    private  BigDecimal grossWtTotal;
    /**
     * 总体积
     */
    @ApiModelProperty("总体积")
    private  BigDecimal volumeTotal;
    /**
     * 备注1
     */
    @ApiModelProperty("备注1")
    private  String remark1;
    /**
     * 备注2
     */
    @ApiModelProperty("备注2")
    private  String remark2;
    /**
     * ERP创建时间
     */
    @ApiModelProperty("ERP创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private  Date lastUpdateTime;
    /**
     * 传输批次号
     */
    @ApiModelProperty("传输批次号")
    private  String tempOwner;

    @ApiModelProperty("提取状态")
    private String status;

    @ApiModelProperty("数据状态")
    private String modifyMark;

    @ApiModelProperty("接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("提取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date extractTime;
}
