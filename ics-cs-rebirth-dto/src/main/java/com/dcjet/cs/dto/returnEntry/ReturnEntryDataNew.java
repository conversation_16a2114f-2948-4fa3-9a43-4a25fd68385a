package com.dcjet.cs.dto.returnEntry;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("报关数据反馈实体New")
public class ReturnEntryDataNew {
    @ApiModelProperty("关联单据号")
    @JsonIgnore
    private String linkedNo;

    @ApiModelProperty("报关单号")
    private String entryNo;

    @ApiModelProperty("报关单申报日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date entryDeclareDate;

    @ApiModelProperty("清单内部编号")
    private String emsListNo;

    @ApiModelProperty("报关单统一编号")
    private String seqNo;

    @ApiModelProperty("运费币制")
    private  String feeCurr;
    @ApiModelProperty("运费类型")
    private  String feeMark;
    @ApiModelProperty("运费")
    private BigDecimal feeRate;
    @ApiModelProperty("保费币制")
    private  String insurCurr;
    @ApiModelProperty("保费类型")
    private  String insurMark;
    @ApiModelProperty("保费")
    private  BigDecimal insurRate;
}
