package com.dcjet.cs.dto.translate;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2019-11-8
 */
@ApiModel(value = "繁转简配置返回信息")
@Setter @Getter
public class GwstdTranslateConfigDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private  String sid;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String billType;
	/**
      * 业务名称
      */
    @ApiModelProperty("业务名称")
	private  String billName;
	/**
      * 
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 是否启用(0：否，1：是)
      */
    @ApiModelProperty("是否启用(0：否，1：是)")
	private  String isEnabled;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
}
