package com.dcjet.cs.dto.gwstd;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2020-11-27
 */
@ApiModel(value = "HS校验返回信息")
@Setter @Getter
public class GwstdHscodeDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 商品编码
      */
    @ApiModelProperty("商品编码")
	private  String codeTS;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gName;
	/**
      * 法一单位
      */
    @ApiModelProperty("法一单位")
	private  String unit1;
	/**
      * 法二单位
      */
    @ApiModelProperty("法二单位")
	private  String unit2;
	/**
      * 插入人
      */
    @ApiModelProperty("插入人")
	private  String insertUser;
	/**
      * 插入时间(入到关务库的时间)
      */
    @ApiModelProperty("插入时间(入到关务库的时间)")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 更新人(暂不使用)
      */
    @ApiModelProperty("更新人(暂不使用)")
	private  String updateUser;
	/**
      * 更新时间(暂不使用)
      */
    @ApiModelProperty("更新时间(暂不使用)")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;

	/** 海关商品编码 */
	private String customsCodeTS;
	/** 海关法一单位 */
	private String customsUnit1;
	/** 海关法二单位 */
	private String customsUnit2;
	/** 错误信息 */
	private String errorMsg;
	/** 备案料号 */
	private String copGNo;
	/** 备案号 */
	private String emsNo;
	/** 序号 */
	private Long serialNo;
	/** 规格型号 */
	private String gModel;
	/** 申报计量单位 */
	private String unit;
}
