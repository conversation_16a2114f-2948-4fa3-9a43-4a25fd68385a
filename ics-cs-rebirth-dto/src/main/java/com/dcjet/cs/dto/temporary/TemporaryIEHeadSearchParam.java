package com.dcjet.cs.dto.temporary;

import com.dcjet.cs.dto.base.BasicSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 复运进境表头
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "复运进境表头新增修改参数")
@Setter @Getter
public class TemporaryIEHeadSearchParam extends BasicSearchParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "单据内部编号")
    private String emsListNo;
    
    @ApiModelProperty(value = "报关单号")
    private String entryNo;
    
    @ApiModelProperty(value = "报关单申报日期-开始")
    private String declareDateFrom;

    @ApiModelProperty(value = "报关单申报日期-结束")
    private String declareDateTo;
    
    @ApiModelProperty(value = "复运完成日期-开始")
    private String completeDateFrom;

    @ApiModelProperty(value = "复运完成日期-结束")
    private String completeDateTo;

    @ApiModelProperty(value = "备注")
    private String note;

}