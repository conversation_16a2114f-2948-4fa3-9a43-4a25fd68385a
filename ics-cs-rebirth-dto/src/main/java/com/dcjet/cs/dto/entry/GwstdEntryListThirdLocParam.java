package com.dcjet.cs.dto.entry;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.*;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;

/**
 * <AUTHOR>
 * @date: 2023-3-17
 */
@Setter
@Getter
@ApiModel(value = "传入参数")
public class GwstdEntryListThirdLocParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 关联字段
     */
    @ApiModelProperty("表头关联字段")
    @NotEmpty(message = "{表头关联字段不能为空！}")
    private String headId;
    /**
     * 企业编码
     */
    @XdoSize(max = 10, message = "{企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 商品编号
     */
    @XdoSize(max = 400, message = "{商品编号长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品编号")
    private String codeTsLoc;
    /**
     * 成交币制
     */
    @XdoSize(max = 400, message = "{成交币制长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("成交币制")
    private String currLoc;
    /**
     * 申报单价
     */
    @XdoSize(max = 400, message = "{申报单价长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单价")
    private String declPriceLoc;
    /**
     * 申报总价
     */
    @XdoSize(max = 400, message = "{申报总价长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报总价")
    private String declTotalLoc;
    /**
     * 最终目的国
     */
    @XdoSize(max = 400, message = "{最终目的国长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("最终目的国")
    private String destinationCountryLoc;
    /**
     * 目的地（产地）代码-行政区域
     */
    @XdoSize(max = 400, message = "{目的地（产地）代码-行政区域长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("目的地（产地）代码-行政区域")
    private String districtCodeLoc;
    /**
     * 商品名称
     */
    @XdoSize(max = 400, message = "{商品名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品名称")
    @JsonProperty("gNameLoc")
    private String gNameLoc;
    /**
     * 商品规格、型号
     */
    @XdoSize(max = 400, message = "{商品规格、型号长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品规格、型号")
    @JsonProperty("gModelLoc")
    private String gModelLoc;
    /**
     * 商品序号
     */
    @XdoSize(max = 400, message = "{商品序号长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品序号")
    @JsonProperty("gNoLoc")
    private String gNoLoc;
    /**
     * 申报数量
     */
    @XdoSize(max = 400, message = "{申报数量长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报数量")
    @JsonProperty("gQtyLoc")
    private String gQtyLoc;
    /**
     * 申报计量单位
     */
    @XdoSize(max = 400, message = "{申报计量单位长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报计量单位")
    @JsonProperty("gUnitLoc")
    private String gUnitLoc;
    /**
     * 产销国
     */
    @XdoSize(max = 400, message = "{产销国长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("产销国")
    private String originCountryLoc;
    /**
     * 第一（法定）数量
     */
    @XdoSize(max = 400, message = "{第一（法定）数量长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("第一（法定）数量")
    private String qty1Loc;
    /**
     * 第一(法定)计量单位
     */
    @XdoSize(max = 400, message = "{第一(法定)计量单位长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("第一(法定)计量单位")
    private String unit1Loc;
    /**
     * 第二数量
     */
    @XdoSize(max = 400, message = "{第二数量长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("第二数量")
    private String qty2Loc;
    /**
     * 第二计量单位
     */
    @XdoSize(max = 400, message = "{第二计量单位长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("第二计量单位")
    private String unit2Loc;
    /**
     * 征减免税方式
     */
    @XdoSize(max = 400, message = "{征减免税方式长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("征减免税方式")
    private String dutyModeLoc;
    /**
     * 创建人名称
     */
    @XdoSize(max = 30, message = "{创建人名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("创建人名称")
    private String insertUserName;
    /**
     * 修改人名称
     */
    @XdoSize(max = 30, message = "{修改人名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改人名称")
    private String updateUserName;
    /**
     * 产销国名称
     */
    @XdoSize(max = 400, message = "{产销国名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("产销国名称")
    private String originCountryNameLoc;
    /**
     * 最终目的国名称
     */
    @XdoSize(max = 400, message = "{最终目的国名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("最终目的国名称")
    private String destinationCountryNameLoc;
    /**
     * 征减免税方式名称
     */
    @XdoSize(max = 400, message = "{征减免税方式名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("征减免税方式名称")
    private String dutyModeNameLoc;
    /**
     * 境内货源地/境内目的地名称
     */
    @XdoSize(max = 400, message = "{境内货源地/境内目的地名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内货源地/境内目的地名称")
    private String districtCodeNameLoc;
}
