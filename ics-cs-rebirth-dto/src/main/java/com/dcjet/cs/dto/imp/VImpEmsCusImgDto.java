package com.dcjet.cs.dto.imp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-04-21
 */
@ApiModel(value = " DTO")
@Setter
@Getter
public class VImpEmsCusImgDto  extends CommonError implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一键
     */
    @JsonIgnore
    @ApiModelProperty("唯一键")
    private String sid;
    /**
     * null
     */
    @ApiModelProperty("料号")
    private String copGNo;
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    private java.math.BigDecimal GNo;
    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String codeTS;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String GName;
    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String GModel;
    /**
     * 申报计量单位
     */
    @ApiModelProperty("申报计量单位")
    private String unit;
    /**
     * 申报数量
     */
    @ApiModelProperty("申报数量")
    private java.math.BigDecimal qty;
    /**
     * 申报单价
     */
    @ApiModelProperty("申报单价")
    private java.math.BigDecimal decPrice;
    /**
     * 申报总价
     */
    @ApiModelProperty("申报总价")
    private java.math.BigDecimal decTotal;
    /**
     * 币制
     */
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 征免方式
     */
    @ApiModelProperty("征免方式")
    private String dutyMode;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 企业内部编号
     */
    @ApiModelProperty("企业内部编号")
    private String copEmsNo;
    /**
     * 产销国
     */
    @ApiModelProperty("产销国")
    private String country;
    /**
     *单据标识
     * 11：金二手册 12：H2000手册 21：金二账册 22：H2000账册
     */
    @ApiModelProperty("单据标识")
    private String billFlag;
    /**
     * 重点商品标识
     */
    @ApiModelProperty("重点商品标识")
    private String keyProductMark;
    /**
     * 原产国
     */
    @ApiModelProperty("原产国")
    private String originCountry;
}
