package com.dcjet.cs.dto.outward;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
/**
 *
 * <AUTHOR>
 * @date: 2019-7-18
 */
@Setter @Getter
@ApiModel(value = "外发收货单-表头查询传入参数")
public class WfReceiveHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 收货单编号
     */
	@NotEmpty(message="{收货单编号不能为空！}")
	@XdoSize(max = 30, message = "{收货单编号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收货单编号")
	private  String billNo;
	/**
     * 制单日期
     */
	@ApiModelProperty("制单日期")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date createDate;
	/**
    * 制单日期-开始
    */
	@ApiModelProperty("制单日期-开始")
	private String createDateFrom;
	/**
    * 制单日期-结束
    */
	@ApiModelProperty("制单日期-结束")
    private String createDateTo;
	/**
     * 收货日期
     */
	@ApiModelProperty("收货日期")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date receiveDate;
	/**
    * 收货日期-开始
    */
	@ApiModelProperty("收货日期-开始")
	private String receiveDateFrom;
	/**
    * 收货日期-结束
    */
	@ApiModelProperty("收货日期-结束")
    private String receiveDateTo;
	/**
     * 企业内部编号
     */
	@NotEmpty(message="{企业内部编号不能为空！}")
	@XdoSize(max = 20, message = "{企业内部编号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业内部编号")
	private  String emsCopNo;
	/**
     * 申报表编号
     */
	@NotEmpty(message="{申报表编号不能为空！}")
	@XdoSize(max = 14, message = "{申报表编号长度不能超过14位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报表编号")
	private  String applyNo;
	/**
     * 申报日期
     */
	@NotNull(message="{申报日期不能为空！}")
	@ApiModelProperty("申报日期")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date declareDate;
	/**
    * 申报日期-开始
    */
	@ApiModelProperty("申报日期-开始")
	private String declareDateFrom;
	/**
    * 申报日期-结束
    */
	@ApiModelProperty("申报日期-结束")
    private String declareDateTo;
	/**
     * 承揽者海关代码
     */
	@XdoSize(max = 10, message = "{承揽者海关代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("承揽者海关代码")
	private  String contractorTradeCode;
	/**
     * 承揽者社会信用代码
     */
	@XdoSize(max = 20, message = "{承揽者社会信用代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("承揽者社会信用代码")
	private  String contractorCreditCode;
	/**
     * 承揽者名称
     */
	@NotEmpty(message="{承揽者名称不能为空！}")
	@XdoSize(max = 100, message = "{承揽者名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("承揽者名称")
	private  String contractorTradeName;
	/**
     * 承揽者所在地主管海关
     */
	@NotEmpty(message="{承揽者所在地主管海关不能为空！}")
	@XdoSize(max = 4, message = "{承揽者所在地主管海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("承揽者所在地主管海关")
	private  String contractorMasterCustoms;
	/**
     * 委托方手账册号
     */
	@NotEmpty(message="{委托方手账册号不能为空！}")
	@XdoSize(max = 12, message = "{委托方手账册号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("委托方手账册号")
	private  String entrustEmsNo;
	/**
     * 委托方主管海关
     */
	@NotEmpty(message="{委托方主管海关不能为空！}")
	@XdoSize(max = 4, message = "{委托方主管海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("委托方主管海关")
	private  String entrustMasterCustoms;
	/**
     * 委托方海关代码
     */
	@NotEmpty(message="{委托方海关代码不能为空！}")
	@XdoSize(max = 10, message = "{委托方海关代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("委托方海关代码")
	private  String tradeCode;
	/**
     * 委托方信用代码
     */
	@XdoSize(max = 20, message = "{委托方信用代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("委托方信用代码")
	private  String entrustCreditCode;
	/**
     * 委托方企业名称
     */
	@NotEmpty(message="{委托方企业名称不能为空！}")
	@XdoSize(max = 100, message = "{委托方企业名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("委托方企业名称")
	private  String entrustTradeName;
	/**
     * 收货申报人
     */
	@XdoSize(max = 30, message = "{收货申报人长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收货申报人")
	private  String receiver;
	/**
     * 申报单位编码
     */
	@NotEmpty(message="{申报单位编码不能为空！}")
	@XdoSize(max = 10, message = "{申报单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报单位编码")
	private  String declareTradeCode;
	/**
     * 申报单位名称
     */
	@NotEmpty(message="{申报单位名称不能为空！}")
	@XdoSize(max = 100, message = "{申报单位名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报单位名称")
	private  String declareTradeName;
	/**
     * 申报单位社会信用代码
     */
	@XdoSize(max = 20, message = "{申报单位社会信用代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报单位社会信用代码")
	private  String declareCreditCode;
	/**
     * 备注
     */
	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String remark;
	/**
     * 数据来源
     */
	@NotEmpty(message="{数据来源不能为空！}")
	@XdoSize(max = 1, message = "{数据来源长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("数据来源")
	private  String dataSource;
	/**
	 * T_WF_DECLARE_FORM表的SID
	 */
	@NotEmpty(message="{申报表关联主键不能为空！}")
	@XdoSize(max = 40, message = "{申报表关联主键不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("T_WF_DECLARE_FORM表的SID")
	private  String headId;
	/**
	 * 承揽者企业代码
	 */
	@NotEmpty(message="{承揽者企业代码不能为空！}")
	@XdoSize(max = 50, message = "{承揽者企业代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("承揽者企业代码")
	private  String contractorCompanyCode;
}
