package com.dcjet.cs.dto.mrp;

import com.dcjet.cs.dto.base.BasicParam;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 内销单价计算及审核
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2020-03-16
 */
@ApiModel(value = "内销单价计算及审核 查询参数")
@Setter @Getter
public class MrpBalanceParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

}