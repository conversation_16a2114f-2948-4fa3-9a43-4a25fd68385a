package com.dcjet.cs.dto.report;

import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-04-23
 */
@ApiModel(value = " DTO")
@Setter
@Getter
public class VDecEBillHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * null
     */
    @ApiModelProperty("null")
    private String sid;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String listNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String billSeqNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal changeTimes;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String emsNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String emsListNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String receiveCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String receiveCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String receiveName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declareCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declareCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declareName;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date declareDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String entryNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String seqNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relListNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEmsNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEntryNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEntryTradeCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEntryTradeCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEntryTradeName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEntryReceiveCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEntryReceiveCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEntryReceiveName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String corrEntryDeclareCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEntryDeclareCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEntryDeclareName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String IEPort;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String masterCustoms;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String IEMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String GMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeMode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String trafMode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String dclcusMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String dclcusType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String vrfdedMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String applyNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String rotateType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String inputCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String inputCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String inputName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String icCardNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date inputDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String status;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String relEntryDeclareCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String corrEntryDeclareCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String corrEntryDeclareName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String entryType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeCountry;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String note;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String insertUser;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date insertTime;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String updateUser;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date updateTime;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String source;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date preVrfdedDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date formalVrfdedDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String listIochkptStatus;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String vrfdedMode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String duCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String copEmsNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date entryDeclareDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String deliveryId;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String mergeType;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date arrivalDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String billListType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String entryStucd;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String chId;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String delareFlag;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String pocketId;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal curPocketNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal totalPocketQty;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String structuredType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String validMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String opType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String sysNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declareType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String headId;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String transMode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String trafName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String voyageNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String wrapType;
    /**
     * 包装种类
     */
    @ApiModelProperty("包装种类2")
    private  String wrapType2;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal packNum;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String bondMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String otherCurr;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String otherMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal otherRate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String feeCurr;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String feeMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal feeRate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String insurCurr;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String insurMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal insurRate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String supplierCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String supplierName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String forwardCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String forwardName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String mawb;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String hawb;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String contrNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date flightDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date shipDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String overseasShipper;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String overseasShipperName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String districtCountry;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String entryPort;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeNation;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String despPort;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String licenseNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal volume;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String cutMode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String destPort;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String warehouse;
    /**
     * 提单录入日期
     */
    @ApiModelProperty("提单录入日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date erpInsertTime;
    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private String invoiceNo;
    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String apprStatus;
    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String apprStatusName;


    /**
     * 海关查验日期
     */
    @ApiModelProperty("海关查验日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern="yyyy-MM-dd HH",timezone="GMT+8")
    @ExcelColumn(format = "yyyy-MM-dd HH")
    private  Date customsCheckDate;
    /**
     * 海关查验原因
     */
    @ApiModelProperty("海关查验原因")
    private  String customsCheckReason;
    /**
     * 海关查验结果
     */
    @ApiModelProperty("海关查验结果")
    private  String customsCheckResult;
    /**
     * 商检查验日期
     */
    @ApiModelProperty("商检查验日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern="yyyy-MM-dd HH",timezone="GMT+8")
    @ExcelColumn(format = "yyyy-MM-dd HH")
    private  Date checkDate;
    /**
     * 商检查验原因
     */
    @ApiModelProperty("商检查验原因")
    private  String checkReason;
    /**
     * 商检查验结果
     */
    @ApiModelProperty("商检查验结果")
    private  String checkResult;
    /**
     * 完税日期
     */
    @ApiModelProperty("完税日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern="yyyy-MM-dd HH",timezone="GMT+8")
    @ExcelColumn(format = "yyyy-MM-dd HH")
    private  Date dutyDate;
    /**
     * 缴税方式
     */
    @ApiModelProperty("缴税方式")
    private  String dutyType;
    /**
     * 完税总价
     */
    @ApiModelProperty("完税总价")
    private BigDecimal dutyTotal;
    /**
     * 关税
     */
    @ApiModelProperty("关税")
    private  BigDecimal dutyPrice;
    /**
     * 增值税
     */
    @ApiModelProperty("增值税")
    private  BigDecimal taxPrice;
    /**
     * 放行日期
     */
    @ApiModelProperty("放行日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern="yyyy-MM-dd HH",timezone="GMT+8")
    @ExcelColumn(format = "yyyy-MM-dd HH")
    private  Date passDate;

    /**
     * 出口日期
     */
    @ApiModelProperty("入口日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private  Date IEDate;
    /**
     * 总数量
     */
    @ApiModelProperty("总数量")
    private String qtyAll;
    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private String totalAll;

    /**
     * 提单制单人
     */
    @ApiModelProperty("提单制单人")
    private String erpInsertUser;
    /**
     * 提单录入人
     */
    @ApiModelProperty("提单制单人")
    private String erpInsertUserName;

    /**
     * 提单内部编号
     */
    @ApiModelProperty("提单内部编号")
    private String erpEmsListNo;
    /**
     * 订舱日期
     */
    @ApiModelProperty("订舱日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private  Date bookingDate;

    /**
     * 清单状态
     */
    @ApiModelProperty(name = "清单状态")
    private String billStatus;
    /**
     * 报关单状态
     */
    @ApiModelProperty("报关单状态")
    private String entryStatusName;
    /**
     * 报关单状态
     */
    @ApiModelProperty("报关单状态")
    private String entryStatus;
    /**
     * -1 未发送 0 发送失败  1发送成功 2发送中
     */
    @ApiModelProperty("-1 未发送 0 发送失败  1发送成功 2发送中")
    private  String sendApiStatus;

    /**
     * 总净重
     */
    @ApiModelProperty("总净重")
    private BigDecimal netWtTotal;
    /**
     * 总毛重
     */
    @ApiModelProperty("总毛重")
    private BigDecimal grossWtTotal;
    /**
     * 总毛重
     */
    @ApiModelProperty("总毛重")
    private BigDecimal volumeTotal;

    /**
     * 成本中心
     */
    @ApiModelProperty("成本中心")
    private String costCenter;
    /**
     * 集装箱类型
     */
    @ApiModelProperty("集装箱类型")
    private String containerType;
    /**
     * 集装箱数量
     */
    @ApiModelProperty("集装箱数量")
    private BigDecimal containerNum;
    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String apprUser;
    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String apprUserName;
    /**
     * 到港日期
     */
    @ApiModelProperty("到港日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date arrivalPortDate;
    /**
     * 查验单号
     */
    @ApiModelProperty("查验单号")
    private String inspectionNo;
    /**
     * 表头商品名称
     */
    @ApiModelProperty("表头商品名称")
    private String GNameAll;
    /**
     * 表头币制
     */
    @ApiModelProperty("表头币制")
    private String currAll;
    /**
     * 制单周次
     */
    @ApiModelProperty("制单周次")
    private String insertWeek;
    /**
     * 表头供应商
     */
    @ApiModelProperty("表头供应商")
    private String headSupplierName;
    /**
     * 贸易条款
     */
    @ApiModelProperty("贸易条款")
    private String tradeTerms;
    /**
     * 包装种类
     */
    @ApiModelProperty("包装种类")
    private  String wrapTypeName;

    public String getErpInsertUser() {
        return (this.erpInsertUser==null?"":this.erpInsertUser)+ (this.erpInsertUserName==null?"":"("+this.erpInsertUserName+")");
    }
    private String apprUserFull;
    public String getApprUserFull() {
        return (this.apprUser==null?"":this.apprUser)+ (this.apprUserName==null?"":"("+this.apprUserName+")");
    }
    /**
     * 内部备注
     */
    @ApiModelProperty("内部备注")
    private  String remark;
}
