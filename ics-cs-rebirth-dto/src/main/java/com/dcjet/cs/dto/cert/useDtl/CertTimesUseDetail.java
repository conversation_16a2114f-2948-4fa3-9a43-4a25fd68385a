package com.dcjet.cs.dto.cert.useDtl;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "证书使用详情-次数")
@Getter @Setter
public class CertTimesUseDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String sid;

    @ApiModelProperty(value = "清单内部编号")
    private String emsListNo;

    @ApiModelProperty(value = "单据内部编号")
    private String emsListNoBill;

    @ApiModelProperty(value = "制单日期")
    private Date insertTime;

    @ApiModelProperty(value = "清单核注编号")
    private String listNo;

    @ApiModelProperty(value = "报关单号")
    private String entryNo;

    @ApiModelProperty(value = "报关单申报日期")
    private Date entryDeclareDate;

}
