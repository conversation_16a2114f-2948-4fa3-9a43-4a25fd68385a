package com.dcjet.cs.dto.mat;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 * @date: 2019-4-17
 */
@ApiModel(value = "非保物料信息返回信息")
@Setter @Getter
public class TaxDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
	 * 进口普通税率(包含百分比符号及从量信息)
	 */
	@ApiModelProperty("进口普通税率(包含百分比符号及从量信息)")
	private String impOrdinaryRate;
	/**
	 * 进口最惠国税率(包含百分比符号及从量信息)
	 */
	@ApiModelProperty("进口最惠国税率(包含百分比符号及从量信息)")
	private String impDiscountRate;
	/**
	 * 进口暂定税率(包含百分比符号及从量信息)
	 */
	@ApiModelProperty("进口暂定税率(包含百分比符号及从量信息)")
	private String impTempRate;
	/**
	 * 增值税税率
	 */
	@ApiModelProperty("增值税税率")
	private String addTaxRate;

	/**
	 * 进口消费税从价定率
	 */
	@ApiModelProperty("进口消费税从价定率")
	private String impConsumeRatePerc;
	/**
	 * 进口消费税从量定额
	 */
	@ApiModelProperty("进口消费税从量定额")
	private String impConsumeRatePrice;
	/**
	 * 单位
	 */
	@ApiModelProperty("单位")
	private String impConsumeRateUnit;

}
