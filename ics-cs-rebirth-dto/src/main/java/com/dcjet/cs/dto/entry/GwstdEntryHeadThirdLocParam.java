package com.dcjet.cs.dto.entry;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2023-3-17
 */
@Setter
@Getter
@ApiModel(value = "传入参数")
public class GwstdEntryHeadThirdLocParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty(name = "主键")
    private String sid;
    /**
     * 企业编码
     */
    @XdoSize(max = 10, message = "{企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "企业编码")
    private String tradeCode;
    /**
     * 申报单位社会信用代码
     */
    @XdoSize(max = 400, message = "{申报单位社会信用代码长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "申报单位社会信用代码")
    private String agentCodeSccLoc;
    /**
     * 申报单位名称
     */
    @XdoSize(max = 400, message = "{申报单位名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "申报单位名称")
    private String agentNameLoc;
    /**
     * 提运单号码
     */
    @XdoSize(max = 400, message = "{提运单号码长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "提运单号码")
    private String billNoLoc;
    /**
     * 境内收发货人社会信用代码
     */
    @XdoSize(max = 400, message = "{境内收发货人社会信用代码长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "境内收发货人社会信用代码")
    private String tradeCodeSccLoc;
    /**
     * 境内收发货人名称
     */
    @XdoSize(max = 400, message = "{境内收发货人名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "境内收发货人名称")
    private String tradeNameLoc;
    /**
     * 境外收发货人编码
     */
    @XdoSize(max = 400, message = "{境外收发货人编码长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "境外收发货人编码")
    private String overseasTradeCodeLoc;
    /**
     * 合同协议号
     */
    @XdoSize(max = 400, message = "{合同协议号长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "合同协议号")
    private String contrNoLoc;
    /**
     * 征免性质编码
     */
    @XdoSize(max = 400, message = "{征免性质编码长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "征免性质编码")
    private String cutModeLoc;
    /**
     * 启运港(进口)
     */
    @XdoSize(max = 400, message = "{启运港(进口)长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "启运港(进口)/离境口岸(出口)")
    private String despPortCodeLoc;
    /**
     * 指运港/经停港(名称）
     */
    @XdoSize(max = 400, message = "{指运港/经停港(名称)长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "指运港/经停港(名称)")
    private String distinatePortLoc;
    /**
     * 申报日期
     */
    @ApiModelProperty(name = "申报日期")
    @JsonProperty("dDateLoc")
    private String dDateLoc;
    /**
     * 海关编号
     */
    @XdoSize(max = 400, message = "{海关编号长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "海关编号")
    private String entryIdLoc;
    /**
     * 运费
     */
    @XdoSize(max = 400, message = "{运费长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "运费")
    private String freightLoc;
    /**
     * 毛重
     */
    @XdoSize(max = 400, message = "{毛重长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "毛重")
    private String grossWtLoc;
    /**
     * 保费
     */
    @XdoSize(max = 400, message = "{保费长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "保费")
    private String insuranceLoc;
    /**
     * 进出口岸代码（进境关别（出境关别））
     */
    @XdoSize(max = 400, message = "{进出口岸代码(进境关别(出境关别))长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "进出口岸代码(进境关别(出境关别))")
    @JsonProperty("iEPortLoc")
    private String iEPortLoc;
    /**
     * 进出口日期
     */
    @XdoSize(max = 400, message = "{进出口日期长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "进出口日期")
    @JsonProperty("iEDateLoc")
    private String iEDateLoc;
    /**
     * 手册号码（备案号）
     */
    @XdoSize(max = 400, message = "{手册号码(备案号)长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "手册号码(备案号)")
    private String manualNoLoc;
    /**
     * 申报地海关
     */
    @XdoSize(max = 400, message = "{申报地海关长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "申报地海关")
    private String masterCustomsLoc;
    /**
     * 净重
     */
    @XdoSize(max = 400, message = "{净重长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "净重")
    private String netWtLoc;
    /**
     * 备注
     */
    @XdoSize(max = 400, message = "{备注长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "备注")
    private String noteLoc;
    /**
     * 杂费
     */
    @XdoSize(max = 400, message = "{杂费长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "杂费")
    private String otherLoc;
    /**
     * 货主单位/消费使用单位（18位社会信用编码）--进口 /生产销售单位（18位社会信用编码）--出口
     */
    @XdoSize(max = 400, message = "{货主单位/消费使用单位（18位社会信用编码）--进口 /生产销售单位（18位社会信用编码）--出口长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "货主单位/消费使用单位（18位社会信用编码）--进口 /生产销售单位（18位社会信用编码）--出口")
    private String ownerSccLoc;
    /**
     * 货主单位名称(消费使用单位)
     */
    @XdoSize(max = 400, message = "{货主单位名称(消费使用单位)长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "货主单位名称(消费使用单位)")
    private String ownerNameLoc;
    /**
     * 件数
     */
    @XdoSize(max = 400, message = "{件数长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "件数")
    private String packNoLoc;
    /**
     * 监管方式
     */
    @XdoSize(max = 400, message = "{监管方式长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "监管方式")
    private String tradeModeLoc;
    /**
     * 启运国/运抵国
     */
    @XdoSize(max = 400, message = "{启运国/运抵国长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "启运国/运抵国")
    private String tradeCountryLoc;
    /**
     * 运输工具名称
     */
    @XdoSize(max = 400, message = "{运输工具名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "运输工具名称")
    private String trafNameLoc;
    /**
     * 成交方式
     */
    @XdoSize(max = 400, message = "{成交方式长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "成交方式")
    private String transModeLoc;
    /**
     * 运输工具航次(班)号
     */
    @XdoSize(max = 400, message = "{运输工具航次(班)号长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "运输工具航次(班)号")
    private String voyageNoLoc;
    /**
     * 包装种类
     */
    @XdoSize(max = 400, message = "{包装种类长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "包装种类")
    private String wrapTypeLoc;
    /**
     * 许可证编号
     */
    @XdoSize(max = 400, message = "{许可证编号长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "许可证编号")
    private String licenseNoLoc;
    /**
     * 运输方式代码
     */
    @XdoSize(max = 400, message = "{运输方式代码长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "运输方式代码")
    private String trafModeLoc;
    /**
     * 贸易国别(地区)代码
     */
    @XdoSize(max = 400, message = "{贸易国别(地区)代码长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "贸易国别(地区)代码")
    private String tradeAreaCodeLoc;
    /**
     * 特殊关系确认
     */
    @XdoSize(max = 400, message = "{特殊关系确认长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "特殊关系确认")
    private String confirmSpecialLoc;
    /**
     * 价格影响确认
     */
    @XdoSize(max = 400, message = "{价格影响确认长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "价格影响确认")
    private String confirmPriceLoc;
    /**
     * 支付特许权使用费确认
     */
    @XdoSize(max = 400, message = "{支付特许权使用费确认长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "支付特许权使用费确认")
    private String confirmRoyaltiesLoc;
    /**
     * 入境口岸(进口专用)
     */
    @XdoSize(max = 400, message = "{入境口岸(进口专用)长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "入境口岸(进口专用)")
    private String entryPortCodeLoc;
    /**
     * 创建人
     */
    @XdoSize(max = 30, message = "{创建人长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "创建人")
    private String insertUser;
    /**
     * 创建人名称
     */
    @XdoSize(max = 30, message = "{创建人名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "创建人名称")
    private String insertUserName;
    /**
     * 修改人
     */
    @XdoSize(max = 30, message = "{修改人长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "修改人")
    private String updateUser;
    /**
     * 修改人名称
     */
    @XdoSize(max = 30, message = "{修改人名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "修改人名称")
    private String updateUserName;
    /**
     * 进出境关别名称
     */
    @XdoSize(max = 400, message = "{进出境关别名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "进出境关别名称")
    @JsonProperty("iEPortNameLoc")
    private String iEPortNameLoc;
    /**
     * 征免性质名称
     */
    @XdoSize(max = 400, message = "{征免性质名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "征免性质名称")
    private String cutModeNameLoc;
    /**
     * 监管方式名称
     */
    @XdoSize(max = 400, message = "{监管方式名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "监管方式名称")
    private String tradeModeNameLoc;
    /**
     * 贸易国名称
     */
    @XdoSize(max = 400, message = "{贸易国名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "贸易国名称")
    private String tradeAreaNameLoc;
    /**
     * 启运国/运抵国名称
     */
    @XdoSize(max = 400, message = "{启运国/运抵国名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "启运国/运抵国名称")
    private String tradeCountryNameLoc;
    /**
     * 指运港/经停港名称
     */
    @XdoSize(max = 400, message = "{指运港/经停港名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "指运港/经停港名称")
    private String distinatePortNameLoc;
    /**
     * 进出境口岸
     */
    @XdoSize(max = 400, message = "{进出境口岸长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "进出境口岸")
    private String entryPortNameLoc;
    /**
     * 进出境口岸
     */
    @XdoSize(max = 400, message = "{进出境口岸长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "进出境口岸")
    private String wrapTypeNameLoc;
    /**
     * 成交方式名称
     */
    @XdoSize(max = 400, message = "{成交方式名称长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "成交方式名称")
    private String transModeNameLoc;
    /**
     * 公式定价确认
     */
    @XdoSize(max = 400, message = "{公式定价确认长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "公式定价确认")
    private String confirmFormulaPriceLoc;
    /**
     * 暂定价格确认
     */
    @XdoSize(max = 400, message = "{暂定价格确认长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "暂定价格确认")
    private String confirmTempPriceLoc;
    /**
     * 自报自缴
     */
    @XdoSize(max = 400, message = "{自报自缴长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "自报自缴")
    private String dutySelfLoc;
    /**
     * 随附单证及编号
     */
    @XdoSize(max = 400, message = "{随附单证及编号长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "随附单证及编号")
    private String acmpNoLoc;
    /**
     * 货物存放地点
     */
    @XdoSize(max = 400, message = "{货物存放地点长度不能超过400位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty(name = "随附单证及编号")
    private String goodsplaceLoc;
}
