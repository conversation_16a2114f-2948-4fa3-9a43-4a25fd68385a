package com.dcjet.cs.dto.returnEntry;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("报关信息反馈dto")
public class ReturnEntryDto<T> {
    public final static String QUERY_SUCCESS = "查询成功";
    public final static String QUERY_FAIL = "查询失败";

    @ApiModelProperty("是否成功")
    private boolean success;
    @ApiModelProperty("返回信息")
    private String message;
    @ApiModelProperty("反馈数据")
    private T data;

    /***
     * @param success
     * @param message
     * @return
     */
    public static ReturnEntryDto createInstance(boolean success, String message){
        ReturnEntryDto ro = new ReturnEntryDto();
        ro.setSuccess(success);
        if(message != null && (!"".equals(message.trim()))) {
            ro.setMessage(message);
        }
        return ro;
    }
}
