package com.dcjet.cs.dto.mat;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2019-4-25
 */
@ApiModel(value = "随附单据返回信息")
@Setter @Getter
public class AttachedDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 海关10位编码
      */
    @ApiModelProperty("海关10位编码")
	private  String tradeCode;
	/**
      * 外键，关联手册/账册表头ID，或者清单、报核表头ID
      */
    @ApiModelProperty("外键，关联手册/账册表头ID，或者清单、报核表头ID")
	private  String businessSid;
	/**
      * 业务单证类型
      */
    @ApiModelProperty("业务单证类型")
	private  String businessType;

	/**
	 * 随附单据类型
	 */
	@ApiModelProperty("随附单据类型")
	private String acmpType;
	/**
	 * 随附单据编号
	 */
	@ApiModelProperty("随附单据编号")
	private String acmpNo;
	/**
      * 随附单据文件名称 企业端上传的文件名，含扩展名(非结构化必填)
      */
    @ApiModelProperty("随附单据文件名称 企业端上传的文件名，含扩展名(非结构化必填)")
	private  String fileName;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 导入文件名称
      */
    @ApiModelProperty("导入文件名称")
	private  String originFileName;
	/**
      * 文件服务器返回链接地址
      */
    @ApiModelProperty("文件服务器返回链接地址")
	private  String fdfsId;
	/**
	 * 数据来源
	 */
	@ApiModelProperty("数据来源")
	private String dataSource;

	/**
	 * 创建日期
	 */
	@ApiModelProperty("创建日期")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date insertTime;

	@ApiModelProperty("进口物料追踪附件地址")
	private String trackUrl;
	@ApiModelProperty("进口物料追踪附件原名")
	private String trackName;
	@ApiModelProperty("进口预录入单附件地址")
	private String decUrl;
	@ApiModelProperty("进口预录入单附件原名")
	private String decName;
}
