package com.dcjet.cs.dto.dxt;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "")
@Setter
@Getter
public class ImgExgOrgUpdateStatusReturn {

    /// <summary>
    /// 数据中心统一编号
    /// </summary>
    @JsonAlias("EportNo")
    public String seqNo;
    /// <summary>
    /// 海关编号
    /// </summary>
    @JsonAlias("EntryNo")
    public String entryNo;
    /// <summary>
    /// 处理结果
    /// </summary>
    @JsonAlias("Channel")
    public String status;

    @JsonAlias("EntryListNo")
    public String ljqNo;
    /// <summary>
    /// 审核备注
    /// </summary>
    @JsonAlias("Note")
    public String auditResult;
    /// <summary>
    /// 通知时间
    /// </summary>
    public String NoticeDate;
    /// <summary>
    /// 进出口日期
    /// </summary>
    @JsonAlias("ImportExportDate")
    public String DDate;
    public String InsertTime;
    /// <summary>
    /// 申报日期
    /// </summary>
    public String DeclareDate;

}
