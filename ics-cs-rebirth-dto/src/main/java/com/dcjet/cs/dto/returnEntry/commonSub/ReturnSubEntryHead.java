package com.dcjet.cs.dto.returnEntry.commonSub;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import xdo.interceptor.decimal.RemoveTailingZero;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订阅报关单表头接口返回实体
 */
@Data
@RemoveTailingZero
public class ReturnSubEntryHead {
    private String emsListNo;//报关单内部编号
    private String decEmsListNo;//预录单内部编号
    private String ieMark;//进出口标志
    private String seqNo;//报关单统一编号
    private String entryId;//报关单号
    private String entryStatus;//报关单状态

    private String agentCode;//申报单位海关注册编码
    private String agentName;//申报单位名称
    private String ownerCode;//消费使用单位代码
    private String ownerName;//消费使用单位名称
    private String tradeCode;//境内收发货人编码
    private String tradeName;//境内收发货人名称

    private String overseasTradeCode;//境外收发货人编码
    private String overseasTradeName;//境外收发货人名称

    private String masterCustoms;//申报地海关
    private String entryType;//报关单类型
    private String tradeMode;//监管方式
    private String tradeCountry;//启运国/抵运国
    private String trafName;//运输工具名称
    private String tradeAreaCode;//贸易国别（地区）代码
    private String transMode;//成交方式
    private String voyageNo;//运输工具航次(班)号
    private String trafMode;//运输方式代码
    private String billNo;//提运单号
    private String contrNo;//合同协议号
    private String cutMode;//征免性质编码
    private String despPortCode;//起运港/离境口岸
    private String distinatePort;//指运港;经停港(名称）
    private String iePort;//进境关别/出境关别
    private String declPort;//进境/出境口岸
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ieDate;//进出口日期
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ddate;//申报日期
    private String feeCurr;//运费币制
    private String feeMark;//运费标记
    private BigDecimal feeRate;//运费/率
    private String insurCurr;//保费币制
    private String insurMark;//保费标记
    private BigDecimal insurRate;//保费/率
    private String otherCurr;//杂费币制
    private String otherMark;//杂费标记
    private BigDecimal otherRate;//杂费/率

    private String goodsPlace;//存放地点
    private BigDecimal packNo;//件数
    private BigDecimal netWt;//净重
    private BigDecimal grossWt;//毛重
    private String wrapType;//包装种类

    private String manualNo;//备案号
    private String licenseNo;//许可证编号
    private String markNo;//标记唛码
    private String note;//备注

    //private String agentCodeScc;//申报单位社会信用代码
    //private String agentRegCode;//申报单位10位检验检疫编码
    //private String ownerScc;//消费使用单位社会信用代码
    //private String ownerCiqCode;//消费使用单位校验检疫编码
    //private String tradeCodeScc;//境内收发货人社会信用代码
    //private String tradeCiqCode;//境内收发货人检验检疫编码
    //private String billType;//备案清单类型
    //@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    //private Date despDate;//启运日期
    //private String correlationReasonFlag;//关联理由【关联报检号的关联理由】
    //private String inspOrgCode;//口岸检验检疫机关企业编码
    //private String orgCode;//检验检疫受理机关编码
    //private String purpOrgCode;//目的地检验检疫机关企业编码
    //private String vsaOrgCode;//领证机关企业编码
    //private String vsaOrgCodeName;//领证机关企业名称
    //private String promiseItems;//其他事项确认（特殊关系确认、价格影响确认、与货物有关的特许权使用费支付确认）
    //private String relativeId;//关联编号
    //private String relManualNo;//关联备案号
    //private String districtCode;//货主单位地区代码（境内目的地）
    //private String ciqBillNo;//B/L号
    //private String bpNo;//报关员联系方式
    //private String typistNo;//制单人
    //private String inputNo;//录入人

    /**
     * 价格说明字段
     */
    private String confirmSpecial;//特殊关系确认
    private String confirmPrice;//价格影响确认
    private String confirmRoyalties;//支付特许权使用费确认
    private String confirmFormulaPrice;//公式定价确认
    private String confirmTempPrice;//暂定价格确认
    private String cusRemarkAuto;//自报自缴

    @JsonIgnore
    private List<ReturnSubEntryList> entryList;
}
