package com.dcjet.cs.dto.mat;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2020-5-15
 */
@Setter @Getter
@ApiModel(value = "单损耗表体传入参数")
public class EmsConsumePreParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 对应手账册表头SID
     */
	@ApiModelProperty("对应手账册表头SID")
	private String headId;
	/**
     * 手账册编号
     */
	@ApiModelProperty("手账册编号")
	private String emsNo;
	/**
     * 序号
     */
	@ApiModelProperty("序号")
	private Integer GNo;
	/**
     * 成品序号
     */
	@ApiModelProperty("成品序号")
	private Integer exgGNo;
	/**
     * 成品料号
     */
	@ApiModelProperty("成品料号")
	private String copExgNo;
	/**
     * 料件序号
     */
	@ApiModelProperty("料件序号")
	private Integer imgGNo;
	/**
     * 料件料号
     */
	@ApiModelProperty("料件料号")
	private String copImgNo;
	/**
     * 单耗版本号
     */
	@ApiModelProperty("单耗版本号")
	private String exgVersion;
	/**
     * 单耗
     */
	@ApiModelProperty("单耗")
	private BigDecimal decAllConsume;
	/**
     * 净耗
     */
	@ApiModelProperty("净耗")
	private BigDecimal decCm;
	/**
     * 有形损耗率%
     */
	@ApiModelProperty("有形损耗率%")
	private BigDecimal decDmVisiable;
	/**
     * 无形损耗率%
     */
	@ApiModelProperty("无形损耗率%")
	private BigDecimal decDmInvisiable;
	/**
     * 保税料件比例%
     */
	@ApiModelProperty("保税料件比例%")
	private BigDecimal bondMtpckPrpr;
	/**
     * 单耗申报状态,1-已申报 2-未申报
     */
	@ApiModelProperty("单耗申报状态,1-已申报 2-未申报")
	private String ucnsDclStat;
	/**
     * 处理标志 修改标记代码 0-不变 1-修改 2-删除 3-增加 备案时新增；变更时修改、增加；删除暂时不用。海关审批通过时，自动将最新备案数据更新到未修改。
     */
	@ApiModelProperty("处理标志 修改标记代码 0-不变 1-修改 2-删除 3-增加 备案时新增；变更时修改、增加；删除暂时不用。海关审批通过时，自动将最新备案数据更新到未修改。")
	private String modifyMark;
	/**
     * 企业执行标志
     */
	@ApiModelProperty("企业执行标志")
	private String etpsExeMark;
	/**
     * 备注
     */
	@ApiModelProperty("备注")
	private String note;
	/**
     * 数据来源（0录入 1导入）
     */
	@ApiModelProperty("数据来源（0录入 1导入）")
	private String dataSource;
	/**
     * api 发送状态-1 未发送 0 发送失败  1发送成功 2发送中
     */
	@ApiModelProperty("api 发送状态-1 未发送 0 发送失败  1发送成功 2发送中")
	private String sendApiStatus;
	/**
     * 制单人姓名
     */
	@ApiModelProperty("制单人姓名")
	private String insertUserName;
	/**
     * 修改人姓名
     */
	@ApiModelProperty("修改人姓名")
	private String updateUserName;

	/**
	 * 审核人
	 */
	@ApiModelProperty("审核人")
	private  String apprUser;
	/**
	 * 审核时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty("审核时间")
	private Date apprDate;
	/**
	 * 审核状态
	 */
	@ApiModelProperty("审核状态")
	private String apprStatus;
	/**
	 * 审核人名称
	 */
	@ApiModelProperty("审核人名称")
	private String apprUserName;
	/**
	 * 核销标志
	 */
	@ApiModelProperty("核销标志")
	private String cavMark;
	/**
	 * 更新范围
	 */
	@ApiModelProperty("更新范围")
	private String updateScope;
}
