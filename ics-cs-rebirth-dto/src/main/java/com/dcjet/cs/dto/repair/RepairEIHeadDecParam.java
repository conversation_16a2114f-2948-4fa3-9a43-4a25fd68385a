package com.dcjet.cs.dto.repair;

import com.dcjet.cs.dto.base.BasicParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 修理复运进境表头
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "修理复运进境表头新增修改参数")
@Setter @Getter
public class RepairEIHeadDecParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "{主键不能为空}")
    @NotEmpty(message = "{主键不能为空}")
    private String sid;

    @ApiModelProperty(value = "模版id")
    private String templateId;
    
}
