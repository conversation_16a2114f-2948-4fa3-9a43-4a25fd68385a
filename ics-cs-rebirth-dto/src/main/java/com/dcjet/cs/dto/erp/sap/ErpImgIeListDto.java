package com.dcjet.cs.dto.erp.sap;

import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.dcjet.cs.dto.erp.sap.constants.ErpSapConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-8-14
 */
@ApiModel(description= "料件出入库返回信息")
@Setter
@Getter
public class ErpImgIeListDto {
    @ApiModelProperty("唯一键")
    private String sid;

    @ApiModelProperty("出入库单据号")
    private String billNo;

    @ApiModelProperty("单据序号")
    private Long lineNo;

    @ApiModelProperty("提取状态")
    @ExcelColumn(map = ErpSapConstants.status_MAP)
    private String status;

    @ApiModelProperty("数据状态")
    @ExcelColumn(map = ErpSapConstants.modifyMark_MAP)
    private String modifyMark;

    @ApiModelProperty("保完税标志")
    @ExcelColumn(map = ErpSapConstants.bondMark_MAP)
    private String bondMark;

    @ApiModelProperty("转换后保完税标记")
    @ExcelColumn(map = ErpSapConstants.bondMark_MAP)
    private String bondMarkConvert;

    @ApiModelProperty("库位别")
    private String warehouseNo;

    @ApiModelProperty("移动类型")
    @ExcelColumn(map = ErpSapConstants.imgIeType_MAP)
    private String ieType;

    @ApiModelProperty("清单关联编号")
    private String linkedNo;

    @ApiModelProperty("采购订单号")
    private String poNo;

    @ApiModelProperty("发票号")
    private String invoiceNo;

    @ApiModelProperty("交易日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    @ApiModelProperty("企业料号")
    private String facGNo;

    @ApiModelProperty("数量")
    private BigDecimal qty;

    @ApiModelProperty("ERP交易单位")
    private String unit;

    @ApiModelProperty("单价")
    private BigDecimal decPrice;

    @ApiModelProperty("总价")
    private BigDecimal decTotal;

    @ApiModelProperty("币制")
    @ExcelColumn(tag = ErpSapConstants.CURR, converter = ErpSapConstants.PCODE_CONVERTER, IOC = true)
    private String curr;

    @ApiModelProperty("转换后币制")
    @ExcelColumn(tag = ErpSapConstants.CURR, converter = ErpSapConstants.PCODE_CONVERTER, IOC = true)
    private String currConvert;

    @ApiModelProperty("供应商代码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;


    @ApiModelProperty("进口方式")
    @ExcelColumn(map = ErpSapConstants.inWay_MAP)
    private String inWay;

    @ApiModelProperty("成本中心")
    private String costCenter;

    @ApiModelProperty("备注")
    private String note;

    @ApiModelProperty("传输批次号")
    private String tempOwner;

    @ApiModelProperty("ERP创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastModifyDate;

    @ApiModelProperty("接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("提取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date extractTime;

    @ApiModelProperty("物料类型")
    private String GMark;

    @ApiModelProperty("业务员代号")
    private String businessMan;

    @ApiModelProperty("人员")
    private String person;

    @ApiModelProperty("供应商联系人")
    private String supplierLinkMan;

    @ApiModelProperty("采购单价")
    private BigDecimal poPrice;

    @ApiModelProperty("工厂代码")
    private String factory;

    @ApiModelProperty("仓库编号")
    private String warehouse;
    private String isClear;
    private String originalGNo;
    private String copGNameEn;
    private String businessNo;
    private String listId;
    private String fsListNo;
    private String applyNo;
    private String unitConvert;
    private String inWayConvert;
    private String tradeCode;
    private String GMarkConvert;
}
