package com.dcjet.cs.dto.report;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import xdo.interceptor.decimal.RemoveTailingZero;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@ApiModel(value = "报表出口表头表体返回信息")
@Setter @Getter
@RemoveTailingZero
public class VDecErpEHeadListHeadDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private  String sid;
	/**
      * 单据内部编号
      */
    @ApiModelProperty("单据内部编号")
	private  String erpEmsListNo;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 经营企业名称
      */
    @ApiModelProperty("经营企业名称")
	private  String tradeName;
	/**
      * 经营企业社会信用代码
      */
    @ApiModelProperty("经营企业社会信用代码")
	private  String tradeCreditCode;
	/**
      * 申报单位编码
      */
    @ApiModelProperty("申报单位编码")
	private  String declareCode;
	/**
      * 申报单位名称
      */
    @ApiModelProperty("申报单位名称")
	private  String declareName;
	/**
      * 申报单位社会信用代码
      */
    @ApiModelProperty("申报单位社会信用代码")
	private  String declareCreditCode;
	/**
      * 录入单位编号
      */
    @ApiModelProperty("录入单位编号")
	private  String inputCode;
	/**
      * 录入单位名称
      */
    @ApiModelProperty("录入单位名称")
	private  String inputName;
	/**
      * 录入单位社会信用代码
      */
    @ApiModelProperty("录入单位社会信用代码")
	private  String inputCreditCode;
	/**
      * 供应商编码
      */
    @ApiModelProperty("供应商编码")
	private  String supplierCode;
	/**
      * 供应商名称
      */
    @ApiModelProperty("供应商名称")
	private  String supplierName;
	/**
      * 货代编码
      */
    @ApiModelProperty("货代编码")
	private  String forwardCode;
	/**
      * 货代名称
      */
    @ApiModelProperty("货代名称")
	private  String forwardName;
	/**
      * 出境关别
      */
    @ApiModelProperty("出境关别")
	private  String IEPort;
	/**
      * 申报地海关
      */
    @ApiModelProperty("申报地海关")
	private  String masterCustoms;
	/**
      * 监管方式
      */
    @ApiModelProperty("监管方式")
	private  String tradeMode;
	/**
      * 运输方式
      */
    @ApiModelProperty("运输方式")
	private  String trafMode;
	/**
      * 料件成品标记
      */
    @ApiModelProperty("料件成品标记")
	private  String GMark;
	/**
      * 贸易国别
      */
    @ApiModelProperty("贸易国别")
	private  String tradeNation;
	/**
      * 归并类型0-自动归并 1-人工归并
      */
    @ApiModelProperty("归并类型0-自动归并 1-人工归并")
	private  String mergeType;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 录入日期
      */
    @ApiModelProperty("录入日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date inputDate;
	/**
      * 成交方式
      */
    @ApiModelProperty("成交方式")
	private  String transMode;
	/**
      * 运抵国
      */
    @ApiModelProperty("运抵国")
	private  String tradeCountry;
	/**
      * 启运港
      */
    @ApiModelProperty("启运港")
	private  String despPort;
	/**
      * 价格说明
      */
    @ApiModelProperty("价格说明")
	private  String promiseItems;
	/**
      * 有效标志 0 有效，1 无效
      */
    @ApiModelProperty("有效标志 0 有效，1 无效")
	private  String validMark;
	/**
      * 备案号
      */
    @ApiModelProperty("备案号")
	private  String emsNo;
	/**
      * 主提运单
      */
    @ApiModelProperty("主提运单")
	private  String mawb;
	/**
      * 提运单号
      */
    @ApiModelProperty("提运单号")
	private  String hawb;
	/**
      * 合同协议号
      */
    @ApiModelProperty("合同协议号")
	private  String contrNo;
	/**
      * 许可证号
      */
    @ApiModelProperty("许可证号")
	private  String licenseNo;
	/**
      * 发票号码
      */
    @ApiModelProperty("发票号码")
	private  String invoiceNo;
	/**
      * 件数
      */
    @ApiModelProperty("件数")
	private BigDecimal packNum;
	/**
      * 体积
      */
    @ApiModelProperty("体积")
	private  BigDecimal volume;
	/**
      * 总净重
      */
    @ApiModelProperty("总净重")
	private  BigDecimal netWt;
	/**
      * 指运港
      */
    @ApiModelProperty("指运港")
	private  String destPort;
	/**
      * 包装种类
      */
    @ApiModelProperty("包装种类")
	private  String wrapType;
	/**
	 * 包装种类
	 */
	@ApiModelProperty("包装种类2")
	private  String wrapType2;
	/**
      * 运输工具名称
      */
    @ApiModelProperty("运输工具名称")
	private  String trafName;
	/**
      * 航次号
      */
    @ApiModelProperty("航次号")
	private  String voyageNo;
	/**
      * 杂费币制
      */
    @ApiModelProperty("杂费币制")
	private  String otherCurr;
	/**
      * 杂费类型
      */
    @ApiModelProperty("杂费类型")
	private  String otherMark;
	/**
      * 杂费
      */
    @ApiModelProperty("杂费")
	private  BigDecimal otherRate;
	/**
      * 运费币制
      */
    @ApiModelProperty("运费币制")
	private  String feeCurr;
	/**
      * 运费类型
      */
    @ApiModelProperty("运费类型")
	private  String feeMark;
	/**
      * 运费
      */
    @ApiModelProperty("运费")
	private  BigDecimal feeRate;
	/**
      * 保费币制
      */
    @ApiModelProperty("保费币制")
	private  String insurCurr;
	/**
      * 保费类型
      */
    @ApiModelProperty("保费类型")
	private  String insurMark;
	/**
      * 保费
      */
    @ApiModelProperty("保费")
	private  BigDecimal insurRate;
	/**
      * 总毛重
      */
    @ApiModelProperty("总毛重")
	private  BigDecimal grossWt;
	/**
      * 出口日期
      */
    @ApiModelProperty("出口日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date IEDate;
	/**
      * 境内货源地
      */
    @ApiModelProperty("境内货源地")
	private  String districtCode;
	/**
      * 境内发货人编码
      */
    @ApiModelProperty("境内发货人编码")
	private  String receiveCode;
	/**
      * 境内发货人名称
      */
    @ApiModelProperty("境内发货人名称")
	private  String receiveName;
	/**
      * 航班日期
      */
    @ApiModelProperty("航班日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date voyageDate;
	/**
      * 出货日期
      */
    @ApiModelProperty("出货日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date shipDate;
	/**
      * 境外发货人编码
      */
    @ApiModelProperty("境外发货人编码")
	private  String overseasShipper;
	/**
      * 境外发货人名称
      */
    @ApiModelProperty("境外发货人名称")
	private  String overseasShipperName;
	/**
      * 征免性质
      */
    @ApiModelProperty("征免性质")
	private  String cutMode;
	/**
      * 货物存放地点
      */
    @ApiModelProperty("货物存放地点")
	private  String warehouse;
	/**
      * 最终目的国
      */
    @ApiModelProperty("最终目的国")
	private  String destinationCountry;
	/**
      * 入境口岸/出境口岸
      */
    @ApiModelProperty("入境口岸/出境口岸")
	private  String entryPort;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private  String insertUserName;

	/**
	 * 制单员
	 *//*
	@ApiModelProperty("制单员")
	private String erpInsertUser;*/
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 发送时间
      */
    @ApiModelProperty("发送时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date declareDate;
	/**
      * 发送人
      */
    @ApiModelProperty("发送人")
	private  String sendUser;

	/**
	 * 发送人
	 */
	@ApiModelProperty("发送人")
	private  String sendUserName;
	/**
	 * 内审员
	 */
	@ApiModelProperty("内审员")
	private  String apprUserName;

	/**
      * 审核时间
      */
    @ApiModelProperty("审核时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date apprDate;
	/**
      * 内审员
      */
    @ApiModelProperty("内审员")
	private  String apprUser;
	/**
      * 审核状态
      */
    @ApiModelProperty("审核状态")
	private  String apprStatus;

	/**
	 * 审核状态
	 */
	@ApiModelProperty("审核状态")
	private String apprStatusName;

	/**
	 * 随附单据是否上传
	 */
	@ApiModelProperty("随附单据是否上传 0 无 1 有")
	private String attach;
	/**
	 * 随附单据是否上传
	 */
	@ApiModelProperty("随附单据是否上传 0 无 1 有")
	private String attachName;
	/**
	 * 保完税标识 0 保税 1 非保税
	 */
	@ApiModelProperty("保完税标识 0 保税 1 非保税")
	private  String bondMark;

	/**
	 * 数据来源 0大提单 1 小提单
	 */
	@ApiModelProperty("数据来源 0大提单 1 小提单")
	private  String dataSource;

	/**
	 * 报关标志
	 */
	@ApiModelProperty("报关标志")
	private  String dclcusMark;
	/**
	 * 报关类型
	 */
	@ApiModelProperty("报关类型")
	private  String dclcusType;
	/**
	 * 报关单类型
	 */
	@ApiModelProperty("报关单类型")
	private  String entryType;
	/**
	 * 申请表编号/申报表编号
	 */
	@ApiModelProperty("申请表编号/申报表编号")
	private  String applyNo;
	/**
	 * 关联核注清单编号
	 */
	@ApiModelProperty("关联核注清单编号")
	private  String relListNo;
	/**
	 * 关联账册（手册）编号
	 */
	@ApiModelProperty("关联账册（手册）编号")
	private  String relEmsNo;
	/**
	 * 境内目的地(行政区域)
	 */
	@ApiModelProperty("境内目的地(行政区域)")
	private  String districtPostCode;
	/**
	 * shipTo代码
	 */
	@ApiModelProperty("shipTo代码")
	private  String shipTo;
	/**
	 * 申报单位编码
	 */
	@ApiModelProperty("申报单位编码(基础信息)")
	private  String declareCodeCustoms;
	/**
	 * 申报单位名称
	 */
	@ApiModelProperty("申报单位名称(基础信息)")
	private  String declareNameCustoms;

	/**
	 * 表头SID
	 */
	@ApiModelProperty("表头SID")
	private  String headId;
	/**
	 * 流水号
	 */
	@ApiModelProperty("流水号")
	private BigDecimal serialNo;
	/**
	 * 备案序号
	 */
	@ApiModelProperty("备案序号")
	private BigDecimal GNo;
	/**
	 * 备案料号
	 */
	@ApiModelProperty("备案料号")
	private  String copGNo;
	/**
	 * 商品编码
	 */
	@ApiModelProperty("商品编码")
	private  String codeTS;
	/**
	 * 商品名称
	 */
	@ApiModelProperty("商品名称")
	private  String GName;
	/**
	 * 商品名称
	 */
	@ApiModelProperty("商品名称")
	private String GNameAll;
	/**
	 * 申报规格型号
	 */
	@ApiModelProperty("申报规格型号")
	private  String GModel;
	/**
	 * 计量单位
	 */
	@ApiModelProperty("计量单位")
	private  String unit;
	/**
	 * 法一单位
	 */
	@ApiModelProperty("法一单位")
	private  String unit1;
	/**
	 * 法二单位
	 */
	@ApiModelProperty("法二单位")
	private  String unit2;
	/**
	 * 原产国
	 */
	@ApiModelProperty("原产国")
	private  String originCountry;
	/**
	 * 申报单价
	 */
	@ApiModelProperty("申报单价")
	private  BigDecimal decPrice;
	/**
	 * 申报总价
	 */
	@ApiModelProperty("申报总价")
	private  BigDecimal decTotal;
	/**
	 * 美元统计总金额
	 */
	@ApiModelProperty("美元统计总金额")
	private  BigDecimal usdPrice;
	/**
	 * 币制
	 */
	@ApiModelProperty("币制")
	private  String curr;
	/**
	 * 法一数量
	 */
	@ApiModelProperty("法一数量")
	private  BigDecimal qty1;
	/**
	 * 法二数量
	 */
	@ApiModelProperty("法二数量")
	private  BigDecimal qty2;
	/**
	 * 重量比例因子
	 */
	@ApiModelProperty("重量比例因子")
	private  BigDecimal factorWt;
	/**
	 * 第一比例因子
	 */
	@ApiModelProperty("第一比例因子")
	private  BigDecimal factor1;
	/**
	 * 第二比例因子
	 */
	@ApiModelProperty("第二比例因子")
	private  BigDecimal factor2;
	/**
	 * 申报数量
	 */
	@ApiModelProperty("申报数量")
	private  BigDecimal qty;
	/**
	 * 用途
	 */
	@ApiModelProperty("用途")
	private  String useType;
	/**
	 * 征免方式
	 */
	@ApiModelProperty("征免方式")
	private  String dutyMode;
	/**
	 * 单耗版本号
	 */
	@ApiModelProperty("单耗版本号")
	private  String exgVersion;
	/**
	 * 归并序号
	 */
	@ApiModelProperty("归并序号")
	private BigDecimal entryGNo;
	/**
	 * 账册企业内部编号
	 */
	@ApiModelProperty("账册企业内部编号")
	private  String copEmsNo;
	/**
	 * 归类标记
	 */
	@ApiModelProperty("归类标记")
	private  String classMark;
	/**
	 * 入库时间
	 */
	@ApiModelProperty("入库时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date arrivalDate;
	/**
	 * 中文名称
	 */
	@ApiModelProperty("中文名称")
	private  String copGName;
	/**
	 * 中文规格型号
	 */
	@ApiModelProperty("中文规格型号")
	private  String copGModel;
	/**
	 * 订单号码
	 */
	@ApiModelProperty("订单号码")
	private  String orderNo;
	/**
	 * 企业料号
	 */
	@ApiModelProperty("企业料号")
	private  String facGNo;
	/**
	 * 关联单号
	 */
	@ApiModelProperty("关联单号")
	private  String linkedNo;

	/**
	 * 企业订单号
	 */
	@ApiModelProperty("企业订单号")
	private  String customerOrderNo;

	/**
	 * 企业料号
	 */
	@ApiModelProperty("企业料号")
	private  String customerGNo;
	/**
	 * 警告标志 0：非警告  1：警告
	 */
	@ApiModelProperty("警告标志 0：非警告  1：警告")
	private  String warningMark;
	/**
	 * 警告信息
	 */
	@ApiModelProperty("警告信息")
	private  String warningMsg;

	/**
	 * 备注1
	 */
	@ApiModelProperty("备注1")
	private  String note1;
	/**
	 * 备注2
	 */
	@ApiModelProperty("备注2")
	private  String note2;
	/**
	 * 备注3
	 */
	@ApiModelProperty("备注3")
	private  String note3;
	/**
	 * 体积
	 */
	@ApiModelProperty("体积")
	private  BigDecimal volumeTotal;
	/**
	 * 总净重
	 */
	@ApiModelProperty("总净重")
	private  BigDecimal netWtTotal;
	/**
	 * 总毛重
	 */
	@ApiModelProperty("总毛重")
	private  BigDecimal grossWtTotal;
	/**
	 * 清单内部编号
	 */
	@ApiModelProperty("清单内部编号")
	private  String billEmsListNo;
	/**
	 * 清单编号
	 */
	@ApiModelProperty("清单编号")
	private  String listNo;
	/**
	 * 清单申报日期
	 */
	@ApiModelProperty("清单申报日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date billDeclareDate;
	/**
	 * 清单状态
	 */
	@ApiModelProperty("清单状态")
	private  String billStatus;
	/**
	 * 报关单号
	 */
	@ApiModelProperty("报关单号")
	private  String entryNo;
	/**
	 * 报关状态
	 */
	@ApiModelProperty("报关状态")
	private  String entryStatus;
	/**
	 * 报关单申报日期
	 */
	@ApiModelProperty("报关单申报日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date entryDeclareDate;


	/**
	 * 表体物料类型
	 */
	@ApiModelProperty("表体物料类型")
	private  String listGMark;
	/**
	 * 表体保完税标记
	 */
	@ApiModelProperty("表体保完税标记")
	private  String listBondMark;
	/**
	 * 表体单据内部编号
	 */
	@ApiModelProperty("表体单据内部编号")
	private  String listEmsListNo;


	/**
	 * 报关单统一编号
	 */
	@ApiModelProperty("报关单统一编号")
	private  String seqNo;
	/**
	 * 成本中心
	 */
	@ApiModelProperty("成本中心")
	private  String costCenter;
	/**
	 * 单据行号
	 */
	@ApiModelProperty("单据行号")
	private String lineNo;

	/**
	 *贸易条款
	 */
	@ApiModelProperty("贸易条款")
	private String tradeTerms;

	/**
	 *集装箱数量
	 */
	@ApiModelProperty("集装箱数量")
	private BigDecimal containerNum;
	/**
	 *集装箱类型
	 */
	@ApiModelProperty("集装箱类型")
	private String containerType;

	@Override
	public String getInsertUser() {
		return (this.insertUser==null?"":this.insertUser)+ (this.insertUserName==null?"":"("+this.insertUserName+")");
	}

	private String sendUserFull;
	public String getSendUserFull() {
		return (this.sendUser==null?"":this.sendUser)+ (this.sendUserName==null?"":"("+this.sendUserName+")");
	}
	private String apprUserFull;
	public String getApprUserFull() {
		return (this.apprUser==null?"":this.apprUser)+ (this.apprUserName==null?"":"("+this.apprUserName+")");
	}

	/**
	 * 海关查验结果
	 */
	@ApiModelProperty("海关查验结果")
	private String customsCheckResult;
	/**
	 * 运费
	 */
	@ApiModelProperty("运费")
	private  BigDecimal freight;
	/**
	 * 运费币制
	 */
	@ApiModelProperty("运费币制")
	private  String freightCurr;

	/**
	 * 递送日期
	 */
	@ApiModelProperty("递送日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date deliveryDate;
	/**
	 * 财务单据号
	 */
	@ApiModelProperty("财务单据号")
	private  String financeNo;
	/**
	 * 总数量
	 */
	@ApiModelProperty("总数量")
	private BigDecimal qtyAll;
	/**
	 * 总金额
	 */
	@ApiModelProperty("总金额")
	private BigDecimal totalAll;

	/**
	 * shipto名称
	 */
	@ApiModelProperty("shipto名称")
	private String shipToName;
	/**
	 * 总税款
	 */
	@Digits(integer = 14, fraction = 5, message = "{总税款必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("总税款")
	private BigDecimal taxTotal;
	/**
	 * 其他进口环节税
	 */
	@Digits(integer = 14, fraction = 5, message = "{其他进口环节税必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("其他进口环节税")
	private BigDecimal otherTax;

	/**
	 * 增值税
	 */
	@Digits(integer = 11, fraction = 5, message = "{增值税必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("增值税")
	private  BigDecimal taxPrice;
	/**
	 * 关税
	 */
	@Digits(integer = 11, fraction = 5, message = "{关税必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("关税")
	private  BigDecimal dutyPrice;
	/**
	 * 到港日期
	 */
	@ApiModelProperty("到港日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date arrivalPortDate;
	/**
	 * 到港日期-开始
	 */
	@ApiModelProperty("到港日期-开始")
	private String arrivalPortDateFrom;
	/**
	 * 到港日期-结束
	 */
	@ApiModelProperty("到港日期-结束")
	private String arrivalPortDateTo;
	/**
	 * 制单周次
	 */
	@ApiModelProperty("制单周次")
	private String insertWeek;
	/**
	 * 放行日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@ApiModelProperty("放行日期")
	private Date passDate;
	/**
	 * 表头原产国
	 */
	@ApiModelProperty("表头原产国")
	private String headDestinationCountry;
	/**
	 * 入/出库单号
	 */
	@ApiModelProperty("入/出库单号")
	private String inOutNo;
	/**
	 * 内部备注
	 */
	@ApiModelProperty("内部备注")
	private  String remark;
	/**
	 * 国际费用
	 */
	@ApiModelProperty("国际费用")
	private BigDecimal internationalCost;

	/**
	 * 国内费用
	 */
	@ApiModelProperty("国内费用")
	private BigDecimal domesticCost;

	/**
	 * 报关费
	 */
	@ApiModelProperty("报关费")
	private BigDecimal customsFee;
	/**
	 * 运费凭据号
	 */
	@ApiModelProperty("运费凭据号")
	private  String freightNo;
    /**
     * 订舱日期
     */
    @ApiModelProperty("订舱日期")
    private Date bookingDate;
	/**
	 * 表头币制
	 */
	@ApiModelProperty("表头币制")
	private String headCurr;
	/**
	 * 计费重量
	 */
	@ApiModelProperty("计费重量")
	private BigDecimal cweight;
	/**
	 * 总金额
	 */
	@ApiModelProperty("总金额")
	private BigDecimal sumDecTotal;
	/**
	 * 特殊关系确认
	 */
	@ApiModelProperty("特殊关系确认")
	private  String confirmSpecial;
	/**
	 * 价格影响确认
	 */
	@ApiModelProperty("价格影响确认")
	private  String confirmPrice;
	/**
	 * 支持特许权使用费确认
	 */
	@ApiModelProperty("支持特许权使用费确认")
	private  String confirmRoyalties;
	/**
	 * 水运中转
	 */
	@ApiModelProperty("水运中转")
	private  String waterTranspTransfer;
	/**
	 * 自报自缴
	 */
	@ApiModelProperty("自报自缴")
	private  String dutySelf;
	/**
	 * 报关状态名称
	 */
	@ApiModelProperty("报关状态名称")
	private String entryStatusName;
	/**
	 * 实收总价
	 */
	@ApiModelProperty("实收总价")
	private String payTotal;
	/**
	 * 实收总价币制
	 */
	@ApiModelProperty("实收总价币制")
	private String payTotalCurr;
	/**
	 * 总金额
	 */
	@ApiModelProperty("总金额")
	private String decTotalProcess;
	/**
	 * 总金额币制
	 */
	@ApiModelProperty("总金额币制")
	private String decTotalProcessCurr;
	/**
	 * 包装种类
	 */
	@ApiModelProperty("包装种类")
	private String wrapTypeName;

	/**
	 * 增值税缓征利息
	 */
	@ApiModelProperty("增值税缓征利息")
	private BigDecimal taxInterest;
	/**
	 * 公式价格确认
	 */
	@ApiModelProperty("公式价格确认")
	private String confirmFormulaPrice;
	/**
	 * 暂定价格确认
	 */
	@ApiModelProperty("暂定价格确认")
	private String confirmTempPrice;
	/**
	 * 发票日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@ApiModelProperty("发票日期")
	private Date invoiceDate;
	/**
	 * 贸易条款(地区)
	 */
	@ApiModelProperty("贸易条款(地区)")
	private String tradeArea;
	/**
	 * 车柜信息
	 */
	@ApiModelProperty("车柜信息")
	private String cabinetInformation;
}
