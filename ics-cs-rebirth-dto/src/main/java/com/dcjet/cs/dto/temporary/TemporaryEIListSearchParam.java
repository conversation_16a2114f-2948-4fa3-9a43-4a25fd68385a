package com.dcjet.cs.dto.temporary;

import com.dcjet.cs.dto.base.BasicSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 复运进境表体
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "复运进境表体新增修改参数")
@Setter @Getter
public class TemporaryEIListSearchParam extends BasicSearchParam implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "表头主键")
    private String headId;

    @ApiModelProperty(value = "表体主键")
    private String temporaryListId;

    @ApiModelProperty(value = "报关单号")
    private String entryNo;

    @ApiModelProperty(value = "物料类型")
    private String gMark;

    @ApiModelProperty(value = "企业料号")
    private String facGNo;

    @ApiModelProperty(value = "商品编码")
    private String codeTS;

    @ApiModelProperty(value = "商品名称")
    private String gName;

    @ApiModelProperty(value = "规格型号")
    private String gModel;

    @ApiModelProperty(value = "计量单位")
    private String unit;


}