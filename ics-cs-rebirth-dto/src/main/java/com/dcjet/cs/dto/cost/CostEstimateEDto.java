package com.dcjet.cs.dto.cost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel(value = "出口费用预估返回信息")
public class CostEstimateEDto {
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private String sid;
    /**
     * 单据内部编号
     */
    @ApiModelProperty("单据内部编号")
    private String emsListNo;

    /**
     * 维护状态（0 未预估 1 已预估 2 已修改）
     */
    @ApiModelProperty("维护状态（0 未预估 1 已预估 2 已修改）")
    private String maintainStatus;

    /**
     * 锁定状态（0 未锁定  1 已锁定）
     */
    @ApiModelProperty("锁定状态（0 未锁定  1 已锁定）")
    private String lockStatus;

    /**
     * 货代名称
     */
    @ApiModelProperty("货代名称")
    private String forwardName;

    /**
     * 申报单位名称
     */
    @ApiModelProperty("申报单位名称(基础信息)")
    private String declareNameCustoms;

    /**
     * 报价单编号
     */
    @ApiModelProperty("报价单编号")
    private String quoNo;

    /**
     * 出境关别代码
     */
    @ApiModelProperty("出境关别代码")
    private String IEPort;

    /**
     * 境外收货人名称
     */
    @ApiModelProperty("境外收货人名称")
    private String overseasShipperName;

    /**
     * 提运单号
     */
    @ApiModelProperty("提运单号")
    private String hawb;

    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private String invoiceNo;

    /**
     * 费用金额
     */
    @ApiModelProperty("费用金额")
    private BigDecimal quoTotal;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 计费重量
     */
    @ApiModelProperty("计费重量")
    private BigDecimal weight;
    /**
     * 制单人
     */
    @ApiModelProperty("制单人")
    private String insertUser;

    /**
     * 制单日期
     */
    @ApiModelProperty("制单日期")
    private Date insertTime;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 贸易条款
     */
    @ApiModelProperty("贸易条款")
    private String tradeTerms;
    /**
     * 运抵国
     */
    @ApiModelProperty("运抵国")
    private String tradeCountry;
    /**
     * 指运港
     */
    @ApiModelProperty("指运港")
    private String destPort;
    /**
     * 包装种类
     */
    @ApiModelProperty("包装种类")
    private String wrapType;
    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal packNum;
    /**
     * 总净重
     */
    @ApiModelProperty("总净重")
    private String netWt;
    /**
     * 总毛重
     */
    @ApiModelProperty("总毛重")
    private String grossWt;
    /**
     * 数量汇总
     */
    @ApiModelProperty("数量汇总")
    private BigDecimal sumQty;
    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal sumDecTotal;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 科目种类
     */
    @ApiModelProperty("科目种类")
    private String courseType;
    /**
     * 科目代码
     */
    @ApiModelProperty("科目代码")
    private String courseCode;
    /**
     * 预估金额
     */
    @ApiModelProperty("预估金额")
    private BigDecimal decTotal;
    /**
     * 币制
     */
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 人名币金额
     */
    @ApiModelProperty("人名币金额")
    private BigDecimal totalRmb;
    /**
     * 确认金额（RMB）
     */
    @ApiModelProperty("确认金额（RMB）")
    private BigDecimal confTotal;
    /**
     * 费用明细备注
     */
    @ApiModelProperty("费用明细备注")
    private String costNote;
    /**
     * 表头计费重量
     */
    @ApiModelProperty("表头计费重量")
    private BigDecimal cWeight;

    /**
     * 体积
     */
    @ApiModelProperty("体积")
    private BigDecimal volume;

    /**
     * 仓储天数
     */
    @ApiModelProperty("仓储天数")
    private Integer storeTime;

    /**
     * 是否维护车柜信息
     */
    @ApiModelProperty("是否维护车柜信息")
    private Integer cabinetCount;

    /**
     * 查验次数
     */
    @ApiModelProperty("查验次数")
    private Integer checkAmount;
    /**
     * 成交方式
     */
    private String transMode;
    /**
     * 申报单位
     */
    private String declareCode;
    /**
     * 货运代理
     */
    private String forwardCode;
    /**
     * 国内物流属性
     */
    private String domesticLogistics;
    /**
     * 国际物流属性
     */
    private String internationalLogistics;

    private String basicSid;
    /**
     * 合同协议号
     */
    @ApiModelProperty("合同协议号")
    private  String contrNo;
}
