package com.dcjet.cs.dto.translate;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2019-11-8
 */
@Setter @Getter
@ApiModel(value = "繁转简配置传入参数")
public class GwstdTranslateConfigParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 业务类型
     */
	@NotEmpty(message="{业务类型不能为空！}")
	@XdoSize(max = 50, message = "{业务类型长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("业务类型")
	private  String billType;
	/**
     * 业务名称
     */
	@XdoSize(max = 100, message = "{业务名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("业务名称")
	private  String billName;
	/**
     * 企业代码
     */
	@XdoSize(max = 10, message = "{长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
     * 是否启用(0：否，1：是)
     */
	@XdoSize(max = 1, message = "{是否启用(0：否，1：是)长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("是否启用(0：否，1：是)")
	private  String isEnabled;
}
