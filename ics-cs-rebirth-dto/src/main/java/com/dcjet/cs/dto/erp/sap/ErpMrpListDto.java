package com.dcjet.cs.dto.erp.sap;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-12-4
 */
@ApiModel(description= "ERP 内销明细返回信息")
@Setter
@Getter
public class ErpMrpListDto {
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private String sid;
    /**
     * 单据编号
     */
    @ApiModelProperty("单据编号")
    private String billNo;
    /**
     * 项次
     */
    @ApiModelProperty("项次")
    private String itemNo;
    /**
     * 客户代码
     */
    @ApiModelProperty("客户代码")
    private String customerNo;
    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private String soldDate;
    /**
     * 料号
     */
    @ApiModelProperty("料号")
    private String facGNo;
    /**
     * 物料类型
     */
    @ApiModelProperty("物料类型")
    private String GMark;
    /**
     * 成品版本号
     */
    @ApiModelProperty("成品版本号")
    private String exgVersion;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal qty;
    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;
    /**
     * 批号
     */
    @ApiModelProperty("批号")
    private String batchNo;
    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    private String factory;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * remark1
     */
    @ApiModelProperty("remark1")
    private String remark1;
    /**
     * remark2
     */
    @ApiModelProperty("remark2")
    private String remark2;
    /**
     * remark3
     */
    @ApiModelProperty("remark3")
    private String remark3;
    /**
     * 经营企业编码
     */
    @ApiModelProperty("经营企业编码")
    private String tradeCode;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateUser;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 用户/导入批次号
     */
    @ApiModelProperty("用户/导入批次号")
    private String tempOwner;
    /**
     * 数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）
     */
    @ApiModelProperty("数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）")
    private String modifyMark;
    /**
     * ERP最后变更日期
     */
    @ApiModelProperty("ERP最后变更日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastModifyDate;
    /**
     * 数据来源 0 手工录入 1ERP接口 2导入
     */
    @ApiModelProperty("数据来源 0 手工录入 1ERP接口 2导入")
    private String dataSource;
    /**
     * 数据状态 0 未提取，1 提取过
     */
    @ApiModelProperty("数据状态 0 未提取，1 提取过")
    private String status;
    /**
     * 数据清洗标记
     */
    @ApiModelProperty("数据清洗标记 0 待转换，1 转换成功 2 转换失败")
    private String isClear;
    /**
     * 转换后物料类型
     */
    @ApiModelProperty("转换后物料类型 I 料件 E 成品 2 设备 3 其他 4 半成品")
    private String GMarkConvert;
    /**
     * 转换后交易单位
     */
    @ApiModelProperty("转换后交易单位")
    private String unitConvert;
}
