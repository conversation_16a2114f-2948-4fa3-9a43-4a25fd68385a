package com.dcjet.cs.dto.outward;

import com.dcjet.cs.dto.mat.AttachedForAppDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2023-5-23
 */
@ApiModel(value = "外发申报表申报实体")
@Setter
@Getter
public class WFDecFrmAppDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @ApiModelProperty("任务ID")
    private String taskID;

    /**
     * 表头实体
     */
    @ApiModelProperty("表头实体")
    private WfDeclareFormForAppDto prHeadPre;

    /**
     * 附件信息
     */
    @ApiModelProperty("附件信息")
    private List<AttachedForAppDto> dcAcmps;
}
