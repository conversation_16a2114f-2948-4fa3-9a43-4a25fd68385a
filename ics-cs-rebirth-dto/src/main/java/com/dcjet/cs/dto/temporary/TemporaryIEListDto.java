package com.dcjet.cs.dto.temporary;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 复运进境表体
 * 
 * generated by Generate dc
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "复运进境表体 DTO")
@Setter @Getter
public class TemporaryIEListDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @ApiModelProperty(value = "表头主键")
    private String headId;
    
    @ApiModelProperty(value = "表体主键")
    private String temporaryListId;
    
    @ApiModelProperty(value = "报关单号")
    private String entryNo;
    
    @ApiModelProperty(value = "物料类型")
    private String gMark;
    
    @ApiModelProperty(value = "企业料号")
    private String facGNo;
    
    @ApiModelProperty(value = "商品编码")
    private String codeTS;
    
    @ApiModelProperty(value = "商品名称")
    private String gName;
    
    @ApiModelProperty(value = "规格型号")
    private String gModel;
    
    @ApiModelProperty(value = "计量单位")
    private String unit;
    
    @ApiModelProperty(value = "数量")
    private BigDecimal qty;
    
    @ApiModelProperty(value = "原单价")
    private BigDecimal decPrice;
    
    @ApiModelProperty(value = "新总价")
    private BigDecimal decTotal;
    
    @ApiModelProperty(value = "币制")
    private String curr;
    
    @ApiModelProperty(value = "备注")
    private String note;
    /**
     * 原产国
     */
    private String originCountry;
    /**
     * 最终目的国
     */
    private String destinationCountry;
    
}