package com.dcjet.cs.dto.cost;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2020-7-8
 */
@ApiModel(value = "报价单科目返回信息")
@Setter
@Getter
public class QuotationCourseDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private String sid;
    /**
     * 科目种类
     */
    @ApiModelProperty("科目种类")
    private String courseType;
    /**
     * 科目代码
     */
    @ApiModelProperty("科目名称")
    private String courseCode;
    /**
     * 计费种类（1 票数 2 计费重量 3 车柜型 4 天/重量计 5 天/体积计 6 体积）
     */
    @ApiModelProperty("计费种类（1 票数 2 计费重量 3 车柜型 4 天/重量计 5 天/体积计 6 体积）")
    private String chargeType;
    /**
     * 币制
     */
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 对应报价单表头sid
     */
    @ApiModelProperty("对应报价单表头sid")
    private String headId;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateUser;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 最低收费标准
     */
    @ApiModelProperty("最低收费标准")
    private BigDecimal minCharge;
    /**
     * 最高收费标准
     */
    @ApiModelProperty("最高收费标准")
    private BigDecimal maxCharge;
    /**
     * 柜属性（0 普货 1 冷柜）
     */
    @ApiModelProperty("柜属性（0 普货 1 冷柜）")
    private String cabinetType;
    /**
     * 免费仓储天数
     */
    @ApiModelProperty("免费仓储天数")
    private Integer freeStore;

    private String flag;
}
