package com.dcjet.cs.dto.mrp;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 内销历史核扣明细
 * 
 * generated by Generate dc
 * <AUTHOR>
 * @date: 2020-05-11
 */
@ApiModel(value = "内销历史核扣明细 DTO")
@Setter @Getter
public class MrpHistoryVrfdedDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    
    /**
     *料件商品编码
     */
    @ApiModelProperty(value = "料件商品编码")
    private String codeTS;
    
    /**
     *备案料件料号
     */
    @ApiModelProperty(value = "备案料件料号")
    private String copGNo;
    
    /**
     *内销对应的清单表体ID？
     */
    @ApiModelProperty(value = "内销对应的清单表体ID？")
    private String decIBillListSid;
    
    /**
     *清单申报日期
     */
    @ApiModelProperty(value = "清单申报日期")
    private Date declareDate;
    
    /**
     *备案号
     */
    @ApiModelProperty(value = "备案号")
    private String emsNo;
    
    /**
     *报关单号
     */
    @ApiModelProperty(value = "报关单号")
    private String entryNo;
    
    /**
     *企业料件料号
     */
    @ApiModelProperty(value = "企业料件料号")
    private String facGNo;
    
    /**
     *料件商品名称
     */
    @ApiModelProperty(value = "料件商品名称")
    private String gName;
    
    /**
     *备案料件序号
     */
    @ApiModelProperty(value = "备案料件序号")
    private String gNo;
    
    /**
     *清单编号？
     */
    @ApiModelProperty(value = "清单编号？")
    private String listNo;
    
    /**
     *备注
     */
    @ApiModelProperty(value = "备注")
    private String note;
    
    /**
     *内销数量
     */
    @ApiModelProperty(value = "内销数量")
    private BigDecimal qty;
    
    /**
     *监管方式
     */
    @ApiModelProperty(value = "监管方式")
    private String tradeMode;
    
}