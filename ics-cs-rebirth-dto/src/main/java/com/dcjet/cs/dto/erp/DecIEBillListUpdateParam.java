package com.dcjet.cs.dto.erp;

import com.dcjet.cs.dto.base.BasicImportParam;
import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2020-10-20
 */
@Setter
@Getter
@ApiModel(value = "规格型号修改导入传入参数")
public class DecIEBillListUpdateParam extends BasicImportParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 商品序号
     */
    @NotNull(message = "{商品序号不能为空！}")
    @Digits(integer = 11, fraction = 0, message = "{商品序号必须为数字,整数位最大11位,小数最大0位!}")
    @ApiModelProperty("商品序号")
    @ExcelColumn(name = "商品序号")
    private Integer serialNo;
    /**
     * 商品编码
     */
    @NotBlank(message = "{商品编码不能为空！}")
    @XdoSize(max = 10, message = "{商品编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品编码")
    @ExcelColumn(name = "商品编码")
    private String codeTS;
    /**
     * 商品名称
     */
    @NotBlank(message = "{商品名称不能为空！}")
    @XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品名称")
    @JsonProperty("gName")
    @ExcelColumn(name = "商品名称")
    private String gName;
    /**
     * 规格型号
     */
    @NotBlank(message = "{规格型号不能为空！}")
    @XdoSize(max = 255, message = "{规格型号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("规格型号")
    @JsonProperty("gModel")
    @ExcelColumn(name = "规格型号")
    private String gModel;
}
