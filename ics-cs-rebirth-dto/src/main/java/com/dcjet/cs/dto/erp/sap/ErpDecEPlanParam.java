package com.dcjet.cs.dto.erp.sap;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date: 2019-6-20
 */
@Setter
@Getter
@ApiModel(description = "出口计划整单 查询参数")
public class ErpDecEPlanParam {
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private  String sid;

    @ApiModelProperty("关联提单编号")
    private String linkedNo;

    @ApiModelProperty("提取状态（0 未提取 1已提取）")
    private String status;

    @ApiModelProperty("数据状态(1：修改，2：删除，3：新增)")
    private Integer modifyMark;

    @ApiModelProperty("发票号")
    private String invoiceNo;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("出货日期-起 yyyy-MM-dd")
    private String shipmentDateFrom;

    @ApiModelProperty("出货日期-止 yyyy-MM-dd")
    private String shipmentDateTo;

    @ApiModelProperty("接收时间-起 yyyy-MM-dd")
    private String insertTimeFrom;

    @ApiModelProperty("接收时间-止 yyyy-MM-dd")
    private String insertTimeTo;

    @ApiModelProperty("企业料号")
    private String facGNo;

    @ApiModelProperty("商品名称")
    private String copGName;

    @ApiModelProperty("销售订单号")
    private String soNo;

    private String tradeCode;
}
