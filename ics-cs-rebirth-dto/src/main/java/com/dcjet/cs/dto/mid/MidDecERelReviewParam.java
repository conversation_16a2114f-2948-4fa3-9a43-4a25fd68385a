package com.dcjet.cs.dto.mid;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2020-6-16
 */
@Setter @Getter
@ApiModel(value = "中期核查出口关联核对追溯传入参数")
public class MidDecERelReviewParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 申报日期
     */
	@ApiModelProperty("申报日期")
	private Date declareDate;
	/**
    * 申报日期-开始
    */
	@ApiModelProperty("申报日期-开始")
	private String declareDateFrom;
	/**
    * 申报日期-结束
    */
	@ApiModelProperty("申报日期-结束")
    private String declareDateTo;
	/**
     * 报关单号
     */
	@XdoSize(max = 18, message = "{报关单号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("报关单号")
	private String entryNo;
	/**
     * 核注清单编号
     */
	@XdoSize(max = 64, message = "{核注清单编号长度不能超过64位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("核注清单编号")
	private String listNo;
	/**
     * 单据内部编号
     */
	@XdoSize(max = 64, message = "{单据内部编号长度不能超过64位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("单据内部编号")
	private String emsListNo;
	/**
     * 企业料号
     */
	@NotEmpty(message="{企业料号不能为空！}")
	@XdoSize(max = 50, message = "{企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业料号")
	private String facGNo;
	/**
     * 申报数量
     */
	@Digits(integer = 14, fraction = 5, message = "{申报数量必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("申报数量")
	private BigDecimal qty;
	/**
     * 备案号
     */
	@XdoSize(max = 12, message = "{备案号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案号")
	private String emsNo;
	/**
     * 备案序号
     */
	@Digits(integer = 11, fraction = 0, message = "{备案序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("备案序号")
	private Integer serialNo;
	/**
     * 备案料号
     */
	@XdoSize(max = 32, message = "{备案料号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案料号")
	private String copGNo;
	/**
     * 商品名称
     */
	@XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("商品名称")
	private String GName;
	/**
     * 企业代码
     */
	@NotEmpty(message="{企业代码不能为空！}")
	@XdoSize(max = 50, message = "{企业代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业代码")
	private String tradeCode;
	/**
     * 制单人姓名
     */
	@XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("制单人姓名")
	private String insertUserName;
	/**
     * 修改人姓名
     */
	@XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修改人姓名")
	private String updateUserName;
	/**
	 * 出库关联编号
	 */
	@ApiModelProperty("出库关联编号")
	private String linkedNo;
	/**
	 * 保完税标记
	 */
	@ApiModelProperty("保完税标记")
	private String bondMark;
	/**
	 * 物料类型
	 */
	@ApiModelProperty("物料类型")
	private String GMark;
	/**
	 * 用来区分列表查询和点击链接查询
	 */
	@ApiModelProperty("查询类型(0:列表,1:链接)")
	private String queryType;
}
