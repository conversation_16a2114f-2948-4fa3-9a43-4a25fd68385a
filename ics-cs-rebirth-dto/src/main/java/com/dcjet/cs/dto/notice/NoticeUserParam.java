package com.dcjet.cs.dto.notice;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2021-5-19
 */
@Setter @Getter
@ApiModel(value = "预警通知设置-通知人param")
public class NoticeUserParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     *
     */
//	@NotEmpty(message="{不能为空！}")
	@XdoSize(max = 10, message = "{长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("")
	private  String addrCode;
	/**
     * 邮箱地址
     */
	@Pattern(regexp = "^[a-z0-9A-Z]+[-|a-z0-9A-Z._]+@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-z]{2,}$"

            , message = "邮箱格式错误")
	@NotEmpty(message="{邮箱地址不能为空！}")
	@XdoSize(max = 50, message = "{邮箱地址长度不能超过50位字节长度}")
	@ApiModelProperty("邮箱地址")
	private  String emailAddr;
	/**
     * 姓名
     */
	@XdoSize(max = 50, message = "{姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("姓名")
	private  String userName;
	/**
     * 备注
     */
	@XdoSize(max = 500, message = "{备注长度不能超过500位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 企业代码
     */
//	@NotEmpty(message="企业代码{不能为空！}")
	@XdoSize(max = 50, message = "{企业代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
     * 制单人姓名
     */
	@XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("制单人姓名")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
}
