package com.dcjet.cs.dto.repair;

import com.dcjet.cs.dto.base.BasicSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 出境修理表头
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "出境修理表头新增修改参数")
@Setter @Getter
public class RepairEHeadSearchParam extends BasicSearchParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "复运状态")
    private String status;

    @ApiModelProperty(value = "单据内部编号")
    private String emsListNo;

    @ApiModelProperty(value = "报关单号")
    private String entryNo;

    @ApiModelProperty(value = "报关单申报日期-开始")
    private String declareDateFrom;

    @ApiModelProperty(value = "报关单申报日期-结束")
    private String declareDateTo;
    
    @ApiModelProperty(value = "担保方式")
    private String assureType;

    @ApiModelProperty(value = "最迟复运日期-开始")
    private String endDateFrom;

    @ApiModelProperty(value = "最迟复运日期-结束")
    private String endDateTo;

    @ApiModelProperty(value = "复运完成日期-开始")
    private String completeDateFrom;

    @ApiModelProperty(value = "复运完成日期-结束")
    private String completeDateTo;


}