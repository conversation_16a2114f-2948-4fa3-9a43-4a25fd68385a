package com.dcjet.cs.dto.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-04-23
 */
@ApiModel(value = " DTO")
@Setter
@Getter
public class VDecEEntryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * null
     */
    @ApiModelProperty("null")
    private String sid;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String copNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declareCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declareName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String apprNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String emsListNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String contrNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String inputCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String inputName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String masterCustoms;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String cutMode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String dataSource;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declTrnrel;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String destPort;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String dclcusMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String entryNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String entryType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String feeCurr;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String feeMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal feeRate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal grossWt;
    /**
     * null
     */
    @ApiModelProperty("null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private String iedate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String IEMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String IEPort;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String inputerName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String insurCurr;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String insurMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal insurRate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String licenseNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String emsNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal netWt;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String note;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String otherCurr;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String otherMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal otherRate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String ownerCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String ownerName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal packNum;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String partenerId;
    /**
     * null
     */
    @ApiModelProperty("null")
    private Date printDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String preEntryNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String risk;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String seqNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tgdNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeCountry;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeMode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String trafMode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String trafName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String transMode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String listType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String typistNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String wrapType;
    /**
     * 包装种类
     */
    @ApiModelProperty("包装种类2")
    private  String wrapType2;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String chkSurety;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String billType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String inputCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declareCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String ownerCreditCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String promiseItems;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeNation;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String checkFlow;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String taxAaminMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String markNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String despPort;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String entryPort;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String warehouse;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String blNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String insporgCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String specdeclFlag;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String purporgCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String despDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String cmpldschrgDt;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String correlationReasonflag;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String vsaorgCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String origboxFlag;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declarerName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String nootherPack;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String orgCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String overseasShipper;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String overseasShipperName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String overseasShipperEname;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String overseasShipperAddr;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String overseasconsigneeCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String overseasconsigneeEname;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeEname;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String correlationNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String ediRemark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String ediRemark2;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String tradeCiqCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String ownerCiqCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declCiqCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String DDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String voyageNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String status;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String insertUser;
    /**
     * null
     */
    @ApiModelProperty("null")
    private Date insertTime;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String updateUser;
    /**
     * null
     */
    @ApiModelProperty("null")
    private Date updateTime;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String hawb;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String erpHeadId;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String bondMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String classMark;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String codeTS;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal decPrice;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal decTotal;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String copGNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String exgVersion;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal factor1;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String unit1;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal qty1;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String unit;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String GModel;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String GName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal serialNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String dutyMode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal qty;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String originCountry;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String unit2;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal qty2;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String curr;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String useTo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private BigDecimal workUsd;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String destinationCountry;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String ciqCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String declGoodsEname;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String origplaceCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String purPose;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String prodValiddt;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String prodQgp;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String goodsAttr;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String stuff;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String unCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String dangName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String dangPackType;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String dangPackSpec;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String engmanentCnm;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String nodangFlag;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String destCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String goodsSpec;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String goodsModel;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String goodsBrand;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String produceDate;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String prodbatchNo;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String districtCode;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String ciqName;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String mnufctrRegno;
    /**
     * null
     */
    @ApiModelProperty("null")
    private String mnufcrRegname;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String apprStatus;
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String apprStatusName;




    /**
     * 进出口日期
     */
    @ApiModelProperty("进出口日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @JsonProperty("iEDate")
    private  Date iEDate;
    /**
     * 进出口标志
     */
    @ApiModelProperty("进出口标志")
    @JsonProperty("iEMark")
    private  String iEMark;
    /**
     * 进出口岸
     */
    @ApiModelProperty("进出口岸")
    @JsonProperty("iEPort")
    private  String iEPort;


    /**
     * 申报日期
     */
    @ApiModelProperty("申报日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern="yyyy-MM-dd HH",timezone="GMT+8")
    @JsonProperty("dDate")
    private  String dDate;


    /**
     * 核销周期
     */
    @ApiModelProperty("核销周期")
    private  String dcrCycle;
    /**
     * 立交桥编号
     */
    @ApiModelProperty("立交桥编号")
    private  String ljqNo;
    /**
     * -1 未发送 0 发送失败  1发送成功 2发送中
     */
    @ApiModelProperty("-1 未发送 0 发送失败  1发送成功 2发送中")
    private  String sendApiStatus;
    /**
     * 发送api任务id
     */
    @ApiModelProperty("发送api任务id")
    private  String sendApiTaskId;
    /**
     * 发送api错误标志 报关金二审核结果（一般贸易使用） 0 正常 1 错误
     */
    @ApiModelProperty("发送api错误标志 报关金二审核结果（一般贸易使用） 0 正常 1 错误")
    private  String sendApiErrorMark;

    /**
     * 备案序号
     */
    @ApiModelProperty("备案序号")
    @JsonProperty("gNo")
    private  BigDecimal gNo;

    /**
     * 商品规格型号
     */
    @ApiModelProperty("商品规格型号")
    @JsonProperty("gModel")
    private  String gModel;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    @JsonProperty("gName")
    private  String gName;

    /**
     * 境内目的地/境内货源地 （行政）
     */
    @ApiModelProperty("境内目的地/境内货源地 （行政）")
    private  String districtPostCode;
    /**
     * 委托日期
     */
    @ApiModelProperty("委托日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern="yyyy-MM-dd HH",timezone="GMT+8")
    private  Date entrustDate;
    /**
     * 委托人
     */
    @ApiModelProperty("委托人")
    private  String entrustPerson;
    /**
     * 商检查验日期
     */
    @ApiModelProperty("商检查验日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern="yyyy-MM-dd HH",timezone="GMT+8")
    private  Date checkDate;
    /**
     * 商检查验原因
     */
    @ApiModelProperty("商检查验原因")
    private  String checkReason;
    /**
     * 商检查验结果
     */
    @ApiModelProperty("商检查验结果")
    private  String checkResult;
    /**
     * 完税日期
     */
    @ApiModelProperty("完税日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern="yyyy-MM-dd HH",timezone="GMT+8")
    private  Date dutyDate;
    /**
     * 缴税方式
     */
    @ApiModelProperty("缴税方式")
    private  String dutyType;
    /**
     * 完税总价
     */
    @ApiModelProperty("完税总价")
    private  BigDecimal dutyTotal;
    /**
     * 关税
     */
    @ApiModelProperty("关税")
    private  BigDecimal dutyPrice;
    /**
     * 增值税
     */
    @ApiModelProperty("增值税")
    private  BigDecimal taxPrice;
    /**
     * 放行日期
     */
    @ApiModelProperty("放行日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern="yyyy-MM-dd HH",timezone="GMT+8")
    private  Date passDate;
    /**
     * 海关查验日期
     */
    @ApiModelProperty("海关查验日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern="yyyy-MM-dd HH",timezone="GMT+8")
    private  Date customsCheckDate;
    /**
     * 海关查验原因
     */
    @ApiModelProperty("海关查验原因")
    private  String customsCheckReason;
    /**
     * 海关查验结果
     */
    @ApiModelProperty("海关查验结果")
    private  String customsCheckResult;
    /**
     * 财务单据号
     */
    @ApiModelProperty("财务单据号")
    private  String financeNo;
    /**
     * 入/出库单号
     */
    @ApiModelProperty("入/出库单号")
    private  String inOutNo;
    /**
     * 到货通知日期
     */
    @ApiModelProperty("到货通知日期")
    private  Date noticeDate;
    /**
     * 到货通知日期-开始
     */
    @ApiModelProperty("到货通知日期-开始")
    private String noticeDateFrom;
    /**
     * 到货通知日期-结束
     */
    @ApiModelProperty("到货通知日期-结束")
    private String noticeDateTo;
    /**
     * 破损标记0-正常 1-破损
     */
    @ApiModelProperty("破损标记0-正常 1-破损")
    private  String damageMark;
    /**
     * 报关费用
     */
    @ApiModelProperty("报关费用")
    private  BigDecimal logisticsFee;
    /**
     * 物流费用
     */
    @ApiModelProperty("物流费用")
    private  BigDecimal customsFee;
    /**
     * 送货单号
     */
    @ApiModelProperty("送货单号")
    private  String deliveryNo;
    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号")
    private  String plateNum;
    /**
     * 送货人
     */
    @ApiModelProperty("送货人")
    private  String deliveryPerson;
    /**
     * 送货日期
     */
    @ApiModelProperty("送货日期")
    private  Date deliveryDate;
    /**
     * 送货日期-开始
     */
    @ApiModelProperty("送货日期-开始")
    private String deliveryDateFrom;
    /**
     * 送货日期-结束
     */
    @ApiModelProperty("送货日期-结束")
    private String deliveryDateTo;
    /**
     * 关联核注清单编号
     */
    @ApiModelProperty("关联核注清单编号")
    private  String relListNo;
    /**
     * 关联账册（手册）编号
     */
    @ApiModelProperty("关联账册（手册）编号")
    private  String relEmsNo;
    /**
     * 核注清单编号
     */
    @ApiModelProperty("核注清单编号")
    private  String listNo;
    /**
     * 清单申报日期
     */
    @ApiModelProperty("清单申报日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private  Date declareDate;
    /**
     * 清单申报日期-开始
     */
    @ApiModelProperty("清单申报日期-开始")
    private String declareDateFrom;
    /**
     * 清单申报日期-结束
     */
    @ApiModelProperty("清单申报日期-结束")
    private String declareDateTo;
    /**
     * 清单状态
     */
    @ApiModelProperty("清单状态")
    private String billStatus;
    /**
     * 提单内部编号
     */
    @ApiModelProperty("提单内部编号")
    private String erpEmsListNo;
    /**
     * 提单制单日期
     */
    @ApiModelProperty("提单制单日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date erpInsertTime;
    /**
     * 表体备注
     */
    @ApiModelProperty("表体备注")
    private String listNote;
    /**
     * 物料类型
     */
    @ApiModelProperty("物料类型")
    private String GMark;
    /**
     * 总数量
     */
    @ApiModelProperty("总数量")
    private String qtyAll;
    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private String totalAll;
    /**
     * 报关单状态
     */
    @ApiModelProperty("报关单状态")
    private String entryStatus;
    /**
     * 报关单状态
     */
    @ApiModelProperty("报关单状态")
    private String entryStatusName;

//    /**
//     * 关检关联号
//     */
//    @ApiModelProperty("关检关联号")
//    private  String copNo;
//    /**
//     * 报关/转关关系标志
//     */
//    @ApiModelProperty("报关/转关关系标志 ")
//    private  String declTrnrel;
//    /**
//     * 报关标志
//     */
//    @ApiModelProperty("报关标志")
//    private  String dclcusMark;
//    /**
//     * 申报人标识
//     */
//    @ApiModelProperty("申报人标识")
//    private  String partenerId;
//    /**
//     * 打印日期
//     */
//    @ApiModelProperty("打印日期")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
//    private  Date printDate;

//    /**
//     * 风险参数
//     */
//    @ApiModelProperty("风险参数")
//    private  String risk;
//    /**
//     * 通关申请单号
//     */
//    @ApiModelProperty("通关申请单号")
//    private  String tgdNo;
//    /**
//     * 录入员 IC 卡号
//     */
//    @ApiModelProperty("录入员 IC 卡号")
//    private  String typistNo;
//    /**
//     * 担保验放标志
//     */
//    @ApiModelProperty("担保验放标志")
//    private  String chkSurety;
//    /**
//     * 查验分流
//     */
//    @ApiModelProperty("查验分流")
//    private  String checkFlow;
//    /**
//     * 税收征管标记
//     */
//    @ApiModelProperty("税收征管标记")
//    private  String taxAaminMark;
//    /**
//     * 标记唛码
//     */
//    @ApiModelProperty("标记唛码")
//    private  String markNo;
//    /**
//     * B/L 号
//     */
//    @ApiModelProperty("B/L 号")
//    private  String blNo;
//    /**
//     * 口岸检验检疫机关
//     */
//    @ApiModelProperty("口岸检验检疫机关")
//    private  String insporgCode;
//    /**
//     * 特种业务标识
//     */
//    @ApiModelProperty("特种业务标识")
//    private  String specdeclFlag;
//    /**
//     * 目的地检验检疫机关
//     */
//    @ApiModelProperty("目的地检验检疫机关")
//    private  String purporgCode;
//    /**
//     * 启运日期
//     */
//    @ApiModelProperty("启运日期")
//    private  String despDate;
//    /**
//     * 卸毕日期
//     */
//    @ApiModelProperty("卸毕日期")
//    private  String cmpldschrgDt;
//    /**
//     * 关联理由
//     */
//    @ApiModelProperty("关联理由")
//    private  String correlationReasonflag;
//    /**
//     * 领证机关
//     */
//    @ApiModelProperty("领证机关")
//    private  String vsaorgCode;
//    /**
//     * 原集装箱标识
//     */
//    @ApiModelProperty("原集装箱标识")
//    private  String origboxFlag;
//    /**
//     * 申报人员姓名
//     */
//    @ApiModelProperty("申报人员姓名")
//    private  String declarerName;
//    /**
//     * 有无其他包装
//     */
//    @ApiModelProperty("有无其他包装")
//    private  String nootherPack;
//    /**
//     * 检验检疫受理机关
//     */
//    @ApiModelProperty("检验检疫受理机关")
//    private  String orgCode;
//    /**
//     * EDI 申报备注
//     */
//    @ApiModelProperty("EDI 申报备注")
//    private  String ediRemark;
//    /**
//     * EDI 申报备注2
//     */
//    @ApiModelProperty("EDI 申报备注2")
//    private  String ediRemark2;
//    /**
//     * 境内收发货人检验 检疫编码
//     */
//    @ApiModelProperty("境内收发货人检验 检疫编码")
//    private  String tradeCiqCode;
//    /**
//     * 消费使用/生产销 售单位检验检疫编 码
//     */
//    @ApiModelProperty("消费使用/生产销 售单位检验检疫编 码")
//    private  String ownerCiqCode;
//    /**
//     * 申报单位检验检疫编码
//     */
//    @ApiModelProperty("申报单位检验检疫编码")
//    private  String declCiqCode;
//    /**
//     * 清单表头id
//     */
//    @ApiModelProperty("清单表头id")
//    private  String billHeadId;
//
//    /**
//     * 归类标志
//     */
//    @ApiModelProperty("归类标志")
//    private  String classMark;
//    /**
//     * 工缴费
//     */
//    @ApiModelProperty("工缴费")
//    private  BigDecimal workUsd;
//    /**
//     * 检验检疫编码
//     */
//    @ApiModelProperty("检验检疫编码")
//    private  String ciqCode;
//    /**
//     * 商品英文名称
//     */
//    @ApiModelProperty("商品英文名称")
//    private  String declGoodsEname;
//    /**
//     * 原产地区代码
//     */
//    @ApiModelProperty("原产地区代码 ")
//    private  String origplaceCode;
//    /**
//     * 用途代码
//     */
//    @ApiModelProperty("用途代码 ")
//    private  String purPose;
//    /**
//     * 产品有效期
//     */
//    @ApiModelProperty("产品有效期")
//    private  String prodValiddt;
//    /**
//     * 产品保质期
//     */
//    @ApiModelProperty("产品保质期")
//    private  String prodQgp;
//    /**
//     * 货物属性代码
//     */
//    @ApiModelProperty("货物属性代码")
//    private  String goodsAttr;
//    /**
//     * 成份/原料/组份
//     */
//    @ApiModelProperty("成份/原料/组份 ")
//    private  String stuff;
//    /**
//     * UN编码
//     */
//    @ApiModelProperty("UN编码")
//    private  String unCode;
//    /**
//     * 危险货物名称
//     */
//    @ApiModelProperty("危险货物名称")
//    private  String dangName;
//    /**
//     * 危包类别
//     */
//    @ApiModelProperty("危包类别")
//    private  String dangPackType;
//    /**
//     * 危包规格
//     */
//    @ApiModelProperty("危包规格")
//    private  String dangPackSpec;
//    /**
//     * 境外生产企业名称
//     */
//    @ApiModelProperty("境外生产企业名称")
//    private  String engmanentCnm;
//    /**
//     * 非危险化学品
//     */
//    @ApiModelProperty("非危险化学品")
//    private  String nodangFlag;
//    /**
//     * 检验检疫货物规格
//     */
//    @ApiModelProperty("检验检疫货物规格")
//    private  String goodsSpec;
//    /**
//     * 货物型号
//     */
//    @ApiModelProperty("货物型号")
//    private  String goodsModel;
//    /**
//     * 货物品牌
//     */
//    @ApiModelProperty("货物品牌")
//    private  String goodsBrand;
//    /**
//     * 生产日期
//     */
//    @ApiModelProperty("生产日期")
//    private  String produceDate;
//    /**
//     * 生产批号
//     */
//    @ApiModelProperty("生产批号")
//    private  String prodbatchNo;
//    /**
//     * 检验检疫名称
//     */
//    @ApiModelProperty("检验检疫名称")
//    private  String ciqName;
//    /**
//     * 生产单位注册号
//     */
//    @ApiModelProperty("生产单位注册号 ")
//    private  String mnufctrRegno;
//    /**
//     * 生产单位名称
//     */
//    @ApiModelProperty("生产单位名称 ")
//    private  String mnufcrRegname;
//    /**
//     * 报关单表头id
//     */
//    @ApiModelProperty("报关单表头id")
//    private  String headId;

    /**
     * 贸易条款
     */
    @ApiModelProperty("贸易条款")
    private  String tradeTerms;

    /**
     * 计划出口日期(邀请日期)
     */
    @ApiModelProperty("计划出口日期(邀请日期)")
    private Date inviteDate;

    /**
     * 计费重量
     */
    @ApiModelProperty("计费重量")
    private BigDecimal cweight;
    /**
     * 发票号码(预录入单表头)
     */
    @ApiModelProperty("发票号码")
    private  String invoiceNo;
    /**
     * 承运人(物流追踪)
     */
    @ApiModelProperty("承运人")
    private  String carrier;

    /**
     * 清单申报日期
     */
    @ApiModelProperty("清单申报日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private  Date billDeclareDate;
    /**
     * 清单申报日期-开始
     */
    @ApiModelProperty("清单申报日期-开始")
    private String billDeclareDateFrom;
    /**
     * 清单申报日期-结束
     */
    @ApiModelProperty("清单申报日期-结束")
    private String billDeclareDateTo;
}
