package com.dcjet.cs.dto.mat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：2018-11-30
 */
@ApiModel(value = "物料中心-进出口所需物料查询参数-MatIeInfoParam")
@Setter @Getter
public class MatIeInfoParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 备案号
     */
    @ApiModelProperty("备案号")
    private String emsNo;
    /**
     * 物料类型 I 料件  E 成品 2 设备 3 其他 4 半成品
     */
    @ApiModelProperty("物料类型 I 料件  E 成品 2 设备 3 其他 4 半成品")
    private String GMark;

    /**
     * 企业料号
     */
    @ApiModelProperty("企业料号")
    private String facGNo;
    /**
     * 保税标记
     */
    @ApiModelProperty("保税标记")
    private String bondedFlag;
    /**
     * 备案料号
     */
    @ApiModelProperty("备案料号")
    private String copGNo;

    /**
     * 企业代码
     */
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * CIQ编码
     */
    @ApiModelProperty("CIQ编码")
    private String ciqNo;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal qty;
    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal decPrice;
    /**
     * 总价
     */
    @ApiModelProperty("总价")
    private BigDecimal decTotal;
    /**
     * 贸易类型
     */
    @ApiModelProperty("贸易类型")
    private String tradeMode;
    /**
     * 进出口类型
     */
    private String IEMark;
    /**
     * 预录入单headId
     */
    private String headId;
    /**
     * 客供单价
     */
    @ApiModelProperty("客供单价")
    private BigDecimal clientPrice;
}
