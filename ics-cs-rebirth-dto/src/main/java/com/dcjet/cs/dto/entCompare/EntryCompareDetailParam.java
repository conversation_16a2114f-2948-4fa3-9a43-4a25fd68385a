package com.dcjet.cs.dto.entCompare;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2020-10-14
 */
@Setter
@Getter
@ApiModel(value = "报关复核异常信息传入参数")
public class EntryCompareDetailParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 对比记录sid
     */
    @XdoSize(max = 60, message = "{对比记录sid长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("对比记录sid")
    private String headId;
    /**
     * 异常栏位
     */
    @XdoSize(max = 100, message = "{异常栏位长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("异常栏位")
    private String fieldName;
    /**
     * 关务数据
     */
    @XdoSize(max = 100, message = "{关务数据长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("关务数据")
    private String gwField;
    /**
     * 订阅数据
     */
    @XdoSize(max = 100, message = "{订阅数据长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("订阅数据")
    private String hgField;
    /**
     * 企业编码
     */
    @XdoSize(max = 10, message = "{企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 数据类型（1-报关单表头 2-报关单表体）
     */
    @XdoSize(max = 2, message = "{数据类型（1-报关单表头 2-报关单表体）长度不能超过2位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("数据类型（1-报关单表头 2-报关单表体）")
    private String dataType;
    /**
     * 单据内部编号
     */
    @XdoSize(max = 60, message = "{单据内部编号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("单据内部编号")
    private String emsListNo;
    /**
     * 报关单申报日期
     */
    @ApiModelProperty("报关单申报日期")
    private Date declareDate;
    /**
     * 报关单申报日期-开始
     */
    @ApiModelProperty("报关单申报日期-开始")
    private String declareDateFrom;
    /**
     * 报关单申报日期-结束
     */
    @ApiModelProperty("报关单申报日期-结束")
    private String declareDateTo;
    /**
     * 报关单统一编号
     */
    @XdoSize(max = 60, message = "{报关单统一编号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关单统一编号")
    private String seqNo;
    /**
     * 报关单号
     */
    @XdoSize(max = 60, message = "{报关单号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关单号")
    private String entryNo;
    /**
     * 申报单位
     */
    @XdoSize(max = 200, message = "{申报单位长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报单位")
    private String agentCode;
    /**
     * 商品序号（表体数据）
     */
    @XdoSize(max = 30, message = "{商品序号（表体数据）长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品序号（表体数据）")
    @JsonProperty("gNo")
    private String gNo;
    /**
     * 监管方式
     */
    @XdoSize(max = 50, message = "{监管方式长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("监管方式")
    private String tradeMode;
    /**
     * 运输方式
     */
    @XdoSize(max = 30, message = "{运输方式长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 进出口标记
     */
    @ApiModelProperty("进出口标记")
    private String IEMark;
}
