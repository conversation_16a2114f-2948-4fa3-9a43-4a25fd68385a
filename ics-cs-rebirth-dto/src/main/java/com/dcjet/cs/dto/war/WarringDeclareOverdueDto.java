package com.dcjet.cs.dto.war;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ApiModel(value = "报关超期预警返回参数")
public class WarringDeclareOverdueDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 单据内部编号
     */
    @ApiModelProperty("单据内部编号")
    private String emsListNo;
    /**
     * 清单编号
     */
    @ApiModelProperty("清单编号")
    private String listNo;
    /**
     * 制单日期
     */
    @ApiModelProperty("制单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 制单人
     */
    @ApiModelProperty("制单人")
    private String insertUser;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 境外收发货人编码
     */
    @ApiModelProperty("境外收发货人编码")
    private String overseasShipper;
    /**
     * 境外收发货人名称
     */
    @ApiModelProperty("境外收发货人名称")
    private String overseasShipperName;
    /**
     * 进出口标识
     */
    @ApiModelProperty("进出口标识")
    private String IEMark;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;
}
