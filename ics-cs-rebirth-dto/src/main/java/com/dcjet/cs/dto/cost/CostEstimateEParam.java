package com.dcjet.cs.dto.cost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "进口费用预估")
public class CostEstimateEParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String sid;
    /**
     * 单据内部编号
     */
    @ApiModelProperty("单据内部编号")
    private String emsListNo;
    /**
     * 维护状态（0 未预估 1 已预估 2 已修改）
     */
    @ApiModelProperty("维护状态（0 未预估 1 已预估 2 已修改）")
    private String maintainStatus;

    /**
     * 锁定状态（0 未锁定  1 已锁定）
     */
    @ApiModelProperty("锁定状态（0 未锁定  1 已锁定）")
    private String lockStatus;

    /**
     * 报价单编号
     */
    @ApiModelProperty("报价单编号")
    private String quoNo;

    /**
     * 制单日期开始
     */
    @ApiModelProperty("制单日期开始")
    private String insertTimeFrom;

    /**
     * 制单日期结束
     */
    @ApiModelProperty("制单日期结束")
    private String insertTimeTo;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 计费重量
     */
    @ApiModelProperty("计费重量")
    private String weight;
    /**
     * 车柜型
     */
    @ApiModelProperty("车柜型")
    private String cabinetType;
    /**
     * 车柜数量
     */
    @ApiModelProperty("车柜数量")
    private Integer cabinetAmount;
    /**
     * 货代名称
     */
    @ApiModelProperty("货代名称")
    private String forwardName;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 申报单位
     */
    @ApiModelProperty("申报单位")
    private String declareNameCustoms;

    @ApiModelProperty("选择预录入单sid")
    private List<String> headIds;
    @ApiModelProperty("选择报价单sid")
    private List<String> quoSids;
    /**
     * 公共信息更新选项（0 全量更新 1 不更新 2 更新空值）
     */
    @ApiModelProperty("公共信息更新选项（0 全量更新 1 不更新 2 更新空值）")
    private String updateBasicInfoFlag;

    /**
     * 报价单更新选项（0 全量更新 1 追加更新）
     */
    @ApiModelProperty("报价单更新选项（0 全量更新 1 追加更新）")
    private String updateQuotationFlag;
    /**
     * 合同协议号
     */
    @ApiModelProperty("合同协议号")
    private  String contrNo;
}