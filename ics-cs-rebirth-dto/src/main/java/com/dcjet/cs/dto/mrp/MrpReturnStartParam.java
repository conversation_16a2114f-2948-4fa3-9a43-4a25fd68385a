package com.dcjet.cs.dto.mrp;

import com.dcjet.cs.dto.base.BasicParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 折料
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2019-11-27
 */
@ApiModel(value = "折料 新增修改参数")
@Setter @Getter
public class MrpReturnStartParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "BOM校验不通过确认折料")
    private String confirmKey;
}