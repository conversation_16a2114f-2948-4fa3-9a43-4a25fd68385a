package com.dcjet.cs.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021-11-1
 */
@ApiModel(value = "舱单信息返回信息")
@Setter
@Getter
public class ManifestInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @ApiModelProperty("")
    private String sid;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String insertUserName;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateUser;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新人名称
     */
    @ApiModelProperty("更新人名称")
    private String updateUserName;
    /**
     *
     */
    @ApiModelProperty("")
    private String tradeCode;
    /**
     * 进出口类型(中文)
     */
    @ApiModelProperty("进出口类型(中文)")
    @JsonProperty("iEType")
    private String iEType;
    /**
     * 保完税标记
     */
    @ApiModelProperty("保完税标记")
    private String bondMark;
    /**
     * 关区代码
     */
    @ApiModelProperty("关区代码")
    private String customsCode;
    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String trafMode;
    /**
     * 运输工具名称
     */
    @ApiModelProperty("运输工具名称")
    private String transportName;
    /**
     * 进出境日期
     */
    @ApiModelProperty("进出境日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("iEDate")
    private Date iEDate;
    /**
     * 航班/航次号
     */
    @ApiModelProperty("航班/航次号")
    private String voyageNo;
    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private Integer packNum;
    /**
     * 毛重
     */
    @ApiModelProperty("毛重")
    private BigDecimal grossWt;
    /**
     * 进口抵港确报标志
     */
    @ApiModelProperty("进口抵港确报标志")
    private String arrivalConfirmMark;
    /**
     * 理货标志
     */
    @ApiModelProperty("理货标志")
    private String tallyStatus;
    /**
     * 出口运抵状态
     */
    @ApiModelProperty("出口运抵状态")
    private String exportArrivalStatus;
    /**
     * 提单表头sid
     */
    @ApiModelProperty("提单表头sid")
    private String headId;
    /**
     * 进出口标记
     */
    @ApiModelProperty("进出口标记")
    private String ieMark;

}
