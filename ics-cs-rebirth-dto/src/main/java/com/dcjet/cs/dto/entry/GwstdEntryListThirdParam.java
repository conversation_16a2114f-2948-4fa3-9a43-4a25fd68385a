package com.dcjet.cs.dto.entry;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.*;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;

/**
 * <AUTHOR>
 * @date: 2023-3-17
 */
@Setter
@Getter
@ApiModel(value = "传入参数")
public class GwstdEntryListThirdParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 关联字段
     */
    @ApiModelProperty("表头关联字段")
    @NotEmpty(message = "{表头关联字段不能为空！}")
    private String headId;
    /**
     * 企业编码
     */
    @XdoSize(max = 10, message = "{企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 报关单中心统一编号（预录入号）
     */
    @XdoSize(max = 18, message = "{报关单中心统一编号（预录入号）长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关单中心统一编号（预录入号）")
    private String seqNo;
    /**
     * 检验检疫代码
     */
    @XdoSize(max = 20, message = "{检验检疫代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("检验检疫代码")
    private String ciqCode;
    /**
     * 境内目的地（商检）
     */
    @XdoSize(max = 200, message = "{境内目的地（商检）长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内目的地（商检）")
    private String ciqdestCode;
    /**
     * 检验检疫名称
     */
    @XdoSize(max = 600, message = "{检验检疫名称长度不能超过600位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("检验检疫名称")
    private String ciqName;
    /**
     * 商品编号
     */
    @XdoSize(max = 10, message = "{商品编号长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品编号")
    private String codeTs;
    /**
     * 合同商品项序号
     */
    @Digits(integer = 19, fraction = 0, message = "{合同商品项序号必须为数字,整数位最大19位,小数最大0位!}")
    @ApiModelProperty("合同商品项序号")
    private Integer contrItem;
    /**
     * 成交币制
     */
    @XdoSize(max = 6, message = "{成交币制长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("成交币制")
    private String curr;
    /**
     * 非危险化学品(危险货物信息)
     */
    @XdoSize(max = 6, message = "{非危险化学品(危险货物信息)长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("非危险化学品(危险货物信息)")
    private String dangerFlag;
    /**
     * 危险货物名称(危险货物信息)
     */
    @XdoSize(max = 600, message = "{危险货物名称(危险货物信息)长度不能超过600位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("危险货物名称(危险货物信息)")
    private String dangName;
    /**
     * 数据来源（0：固定 1：电子口岸 2：爬网 3：组合）
     */
    @XdoSize(max = 1, message = "{数据来源（0：固定 1：电子口岸 2：爬网 3：组合）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("数据来源（0：固定 1：电子口岸 2：爬网 3：组合）")
    private String dataSource;
    /**
     * 申报单价
     */
    @Digits(integer = 14, fraction = 5, message = "{申报单价必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("申报单价")
    private BigDecimal declPrice;
    /**
     * 申报总价
     */
    @Digits(integer = 15, fraction = 4, message = "{申报总价必须为数字,整数位最大15位,小数最大4位!}")
    @ApiModelProperty("申报总价")
    private BigDecimal declTotal;
    /**
     * 最终目的国
     */
    @XdoSize(max = 6, message = "{最终目的国长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("最终目的国")
    private String destinationCountry;
    /**
     * 目的地（产地）代码-行政区域
     */
    @XdoSize(max = 10, message = "{目的地（产地）代码-行政区域长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("目的地（产地）代码-行政区域")
    private String districtCode;
    /**
     * 货物属性代码(检验检疫)
     */
    @XdoSize(max = 20, message = "{货物属性代码(检验检疫)长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货物属性代码(检验检疫)")
    private String goodsAttr;
    /**
     * 货物品牌(检验检疫)
     */
    @XdoSize(max = 200, message = "{货物品牌(检验检疫)长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货物品牌(检验检疫)")
    private String goodsBrand;
    /**
     * 货物型号(检验检疫)
     */
    @XdoSize(max = 510, message = "{货物型号(检验检疫)长度不能超过510位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货物型号(检验检疫)")
    private String goodsModel;
    /**
     * 货物规格(检验检疫)
     */
    @XdoSize(max = 510, message = "{货物规格(检验检疫)长度不能超过510位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("货物规格(检验检疫)")
    private String goodsSpec;
    /**
     * 商品名称
     */
    @XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品名称")
//    @JsonProperty("gname")
    private String gname;
    /**
     * 商品规格、型号
     */
    @XdoSize(max = 255, message = "{商品规格、型号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商品规格、型号")
//    @JsonProperty("gmodel")
    private String gmodel;
    /**
     * 商品序号
     */
    @Digits(integer = 19, fraction = 0, message = "{商品序号必须为数字,整数位最大19位,小数最大0位!}")
    @ApiModelProperty("商品序号")
//    @JsonProperty("gno")
    private Integer gno;
    /**
     * 申报数量
     */
    @Digits(integer = 14, fraction = 5, message = "{申报数量必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("申报数量")
//    @JsonProperty("gqty")
    private BigDecimal gqty;
    /**
     * 申报计量单位
     */
    @XdoSize(max = 6, message = "{申报计量单位长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报计量单位")
//    @JsonProperty("gunit")
    private String gunit;
    /**
     * 生产日期(检验检疫)
     */
    @ApiModelProperty("生产日期(检验检疫)")
    private Date produceDate;
    /**
     * 产销国
     */
    @XdoSize(max = 6, message = "{产销国长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("产销国")
    private String originCountry;
    /**
     * 危包规格(危险货物信息)
     */
    @XdoSize(max = 510, message = "{危包规格(危险货物信息)长度不能超过510位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("危包规格(危险货物信息)")
    private String packModel;
    /**
     * 危包类别
     */
    @XdoSize(max = 4, message = "{危包类别长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("危包类别")
    private String packType;
    /**
     * 产品有效期(检验检疫)
     */
    @ApiModelProperty("产品有效期(检验检疫)")
    private Date prodValiddt;
    /**
     * 第一（法定）数量
     */
    @Digits(integer = 14, fraction = 5, message = "{第一（法定）数量必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("第一（法定）数量")
    private BigDecimal qty1;
    /**
     * 第一(法定)计量单位
     */
    @XdoSize(max = 6, message = "{第一(法定)计量单位长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("第一(法定)计量单位")
    private String unit1;
    /**
     * 第二数量
     */
    @Digits(integer = 14, fraction = 5, message = "{第二数量必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("第二数量")
    private BigDecimal qty2;
    /**
     * 第二计量单位
     */
    @XdoSize(max = 6, message = "{第二计量单位长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("第二计量单位")
    private String unit2;
    /**
     * 成分/原料/组分(检验检疫)
     */
    @XdoSize(max = 600, message = "{成分/原料/组分(检验检疫)长度不能超过600位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("成分/原料/组分(检验检疫)")
    private String stuff;
    /**
     * 危险货物编码
     */
    @XdoSize(max = 40, message = "{危险货物编码长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("危险货物编码")
    private String dangCode;
    /**
     * 用途
     */
    @XdoSize(max = 2, message = "{用途长度不能超过2位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("用途")
    private String useTo;
    /**
     * 征减免税方式
     */
    @XdoSize(max = 1, message = "{征减免税方式长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("征减免税方式")
    private String dutyMode;
    /**
     * 海关编号
     */
    @XdoSize(max = 18, message = "{海关编号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("海关编号")
    private String entryId;
    /**
     * 归类标志 0自动归类1人工归类
     */
    @XdoSize(max = 1, message = "{归类标志 0自动归类1人工归类长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("归类标志 0自动归类1人工归类")
    private String classMark;
    /**
     * 目的地（产地）代码-行政区域
     */
    @XdoSize(max = 12, message = "{目的地（产地）代码-行政区域长度不能超过12位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("目的地（产地）代码-行政区域")
    private String districtPostCode;
    /**
     * 工缴费(美元)
     */
    @Digits(integer = 15, fraction = 4, message = "{工缴费(美元)必须为数字,整数位最大15位,小数最大4位!}")
    @ApiModelProperty("工缴费(美元)")
    private BigDecimal workUsd;
    /**
     * 加工料件/成品货号
     */
    @XdoSize(max = 32, message = "{加工料件/成品货号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("加工料件/成品货号")
    private String exgNo;
    /**
     * 成交总价
     */
    @Digits(integer = 15, fraction = 4, message = "{成交总价必须为数字,整数位最大15位,小数最大4位!}")
    @ApiModelProperty("成交总价")
    private BigDecimal tradeTotal;
    /**
     * 生产单位注册号
     */
    @XdoSize(max = 50, message = "{生产单位注册号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("生产单位注册号")
    private String mnufctrreGno;
    /**
     * 原产地区代码
     */
    @XdoSize(max = 50, message = "{原产地区代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("原产地区代码")
    private String origPlaceCode;
    /**
     * UN编码
     */
    @XdoSize(max = 50, message = "{UN编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("UN编码")
    private String unCode;
    /**
     * 创建人名称
     */
    @XdoSize(max = 30, message = "{创建人名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("创建人名称")
    private String insertUserName;
    /**
     * 修改人名称
     */
    @XdoSize(max = 30, message = "{修改人名称长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改人名称")
    private String updateUserName;
    /**
     * 产销国名称
     */
    @XdoSize(max = 100, message = "{产销国名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("产销国名称")
    private String originCountryName;
    /**
     * 最终目的国名称
     */
    @XdoSize(max = 100, message = "{最终目的国名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("最终目的国名称")
    private String destinationCountryName;
    /**
     * 征减免税方式名称
     */
    @XdoSize(max = 100, message = "{征减免税方式名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("征减免税方式名称")
    private String dutyModeName;
    /**
     * 境内货源地/境内目的地名称
     */
    @XdoSize(max = 100, message = "{境内货源地/境内目的地名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("境内货源地/境内目的地名称")
    private String districtCodeName;
    /**
     * 第一(法定)计量单位转换
     */
    @XdoSize(max = 6, message = "{第一(法定)计量单位转换长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("第一(法定)计量单位转换")
    private String unit1Convert;
    /**
     * 第二(法定)计量单位转换
     */
    @XdoSize(max = 6, message = "{第二(法定)计量单位转换长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("第二(法定)计量单位转换")
    private String unit2Convert;
    /**
     * 申报计量单位转换
     */
    @XdoSize(max = 6, message = "{申报计量单位转换长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("申报计量单位转换")
//    @JsonProperty("gunitConvert")
    private String gunitConvert;
    /**
     * 币制转换
     */
    @XdoSize(max = 6, message = "{币制转换长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("币制转换")
    private String currConvert;
    /**
     * 原产国转换
     */
    @XdoSize(max = 6, message = "{原产国转换长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("原产国转换")
    private String originCountryConvert;
    /**
     * 最终目的国转换
     */
    @XdoSize(max = 6, message = "{最终目的国转换长度不能超过6位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("最终目的国转换")
    private String destinationCountryConvert;
    /**
     * 目的地（产地）代码-行政区域名称
     */
    @XdoSize(max = 100, message = "{目的地（产地）代码-行政区域名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("目的地（产地）代码-行政区域名称")
    private String districtPostCodeName;
}
