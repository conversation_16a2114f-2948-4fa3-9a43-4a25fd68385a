package com.dcjet.cs.dto.tax.param;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel(value = "进口税金编辑-传入参数")
public class TaxHeadIParam {

    @ApiModelProperty("主键")
    private String sid;

    @ApiModelProperty("备注")
    private String note;

    @ApiModelProperty("付税方式")
    private String dutyType;//付税方式

    @ApiModelProperty("支付日期")
    private Date payDate;//支付日期

    @Digits(integer = 9, fraction = 2, message = "{关税必须为数字,整数位最大9位,小数最大2位!}")
    @ApiModelProperty("关税")
    private BigDecimal dutyValue;

    @Digits(integer = 9, fraction = 2, message = "{增值税必须为数字,整数位最大9位,小数最大2位!}")
    @ApiModelProperty("增值税")
    private BigDecimal taxValue;
  
    @Digits(integer = 9, fraction = 2, message = "{消费税必须为数字,整数位最大9位,小数最大2位!}")
    @ApiModelProperty("消费税")
    private BigDecimal exciseTax;
    /**
     * 报关单内部编号
     */
    @XdoSize(max = 50, message = "{报关单内部编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关单内部编号")
    private  String emsListNo;
}
