package com.dcjet.cs.dto.mrp;

import com.dcjet.cs.dto.base.BasicParam;
import com.xdo.validation.annotation.XdoEnum;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * bom
 * <p>
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-11-25
 */
@ApiModel(value = "bom 新增修改参数")
@Setter
@Getter
public class MrpBomParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 保税料件比例
     */
    @ApiModelProperty(value = "保税料件比例")
    @Digits(integer = 18, fraction = 9, message = "{保税料件比例必须为数字,整数位最大18位,小数最大9位!}")
    private BigDecimal bondMtpckPrpr;

    /**
     * 料件商品编码
     */
    @ApiModelProperty(value = "料件商品编码")
    @XdoSize(max = 10, message = "{料件商品编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    private String codeTS;

    /**
     * 备案成品/半成品料号
     */
    @ApiModelProperty(value = "备案成品/半成品料号")
    @XdoSize(max = 50, message = "{备案成品/半成品料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String copExgNo;

    /**
     * 备案料件料号
     */
    @ApiModelProperty(value = "备案料件料号")
    @XdoSize(max = 50, message = "{备案料件料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String copImgNo;

    /**
     * 净耗
     */
    @ApiModelProperty(value = "净耗")
    @Digits(integer = 18, fraction = 9, message = "{净耗必须为数字,整数位最大18位,小数最大9位!}")
    @NotNull(message = "{净耗不能为空}")
    private BigDecimal decCm;

    /**
     * 备案号
     */
    @ApiModelProperty(value = "备案号")
    @XdoSize(max = 12, message = "{备案号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
    @NotNull(message = "{备案号不能为空}")
    private String emsNo;

    /**
     * 成品商品编码
     */
    @ApiModelProperty(value = "成品商品编码")
    @XdoSize(max = 10, message = "{成品商品编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    private String exgCodeTS;

    /**
     * 成品商品名称
     */
    @ApiModelProperty(value = "成品商品名称")
    @XdoSize(max = 255, message = "{成品商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    private String exgGName;

    /**
     * 成品序号
     */
    @ApiModelProperty(value = "成品序号")
    @XdoSize(max = 50, message = "{成品序号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String exgGNo;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @XdoSize(max = 30, message = "{版本号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @NotEmpty(message = "{版本号不能为空}")
    private String exgVersion;

    /**
     * 企业成品/半成品料号
     */
    @ApiModelProperty(value = "企业成品/半成品料号")
    @XdoSize(max = 50, message = "{企业成品/半成品料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @NotEmpty(message = "{企业成品/半成品料号不能为空}")
    private String facExgNo;

    /**
     * 企业料号
     */
    @ApiModelProperty(value = "企业料号")
    @XdoSize(max = 50, message = "{企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @NotEmpty(message = "{企业料号不能为空}")
    private String facGNo;

    /**
     * 成品半成品标识 E, 4
     */
    @ApiModelProperty(value = "成品半成品标识 I，E， 4")
    @XdoSize(max = 30, message = "{成品半成品标识 I，E， 4长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @XdoEnum(values = {"E", "4"}, message = "{成品半成品标识[gMark]限定为：E, 4}")
    private String gMark;

    /**
     * 料件商品名称
     */
    @ApiModelProperty(value = "料件商品名称")
    @XdoSize(max = 255, message = "{料件商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    private String gName;

    /**
     * 料件项号
     */
    @ApiModelProperty(value = "料件项号")
    @XdoSize(max = 50, message = "{料件项号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    private String imgGNo;

    /**
     * 无形损耗率
     */
    @ApiModelProperty(value = "无形损耗率")
    @Digits(integer = 11, fraction = 9, message = "{无形损耗率必须为数字,整数位最大11位,小数最大9位!}")
    private BigDecimal decDmInvisiable;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @XdoSize(max = 512, message = "{备注长度不能超过512位字节长度(一个汉字2位字节长度)!}")
    private String note;

    /**
     * 有形损耗率
     */
    @ApiModelProperty(value = "有形损耗率")
    @Digits(integer = 11, fraction = 9, message = "{有形损耗率必须为数字,整数位最大11位,小数最大9位!}")
    private BigDecimal decDmVisiable;


    public BigDecimal getBondMtpckPrpr() {
        if (bondMtpckPrpr == null) {
            return BigDecimal.valueOf(100);
        }
        return bondMtpckPrpr;
    }

    public BigDecimal getDecDmInvisiable() {
        if (decDmInvisiable == null) {
            return BigDecimal.ZERO;
        }
        return decDmInvisiable;
    }

    public BigDecimal getDecDmVisiable() {
        if (decDmVisiable == null) {
            return BigDecimal.ZERO;
        }
        return decDmVisiable;
    }
}
