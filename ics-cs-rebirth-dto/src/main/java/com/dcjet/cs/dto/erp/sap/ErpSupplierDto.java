package com.dcjet.cs.dto.erp.sap;

import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.dcjet.cs.dto.erp.sap.constants.ErpSapConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-9-12
 */
@ApiModel(description = "erp供应商返回信息")
@Setter
@Getter
public class ErpSupplierDto {
    @ApiModelProperty("SID")
    private String sid;

    @ApiModelProperty("企业代码")
    private String customerCode;

    @ApiModelProperty("企业类型")
    private String customerType;

    @ApiModelProperty("企业中文名称")
    private String companyName;

    @ApiModelProperty("企业英文名称")
    private String companyNameEn;
    @ApiModelProperty("海关注册编码")
    private String declareCode;

    @ApiModelProperty("社会信用代码")
    private String creditCode;

    @ApiModelProperty("中文国家")
    private String country;

    @ApiModelProperty("中文城市")
    private String city;

    @ApiModelProperty("中文地区")
    private String area;

    @ApiModelProperty("英文国家")
    private String countryEn;

    @ApiModelProperty("英文城市")
    private String cityEn;

    @ApiModelProperty("英文地区")
    private String areaEn;

    @ApiModelProperty("海关信用等级")
    private String customsCreditRating;

    @ApiModelProperty("AEO代码")
    private String aeoCode;

    @ApiModelProperty("中文联系人")
    private String linkmanName;

    @ApiModelProperty("联系人手机")
    private String mobilePhone;

    @ApiModelProperty("联系人邮箱")
    private String eMail;

    @ApiModelProperty("电话")
    private String linkManPhone;

    @ApiModelProperty("传真")
    private String fax;

    @ApiModelProperty("邮编")
    private String postal;

    @ApiModelProperty("中文地址")
    private String address;

    @ApiModelProperty("英文地址")
    private String addressEn;

    @ApiModelProperty("发票中文地址")
    private String invoiceAddress;

    @ApiModelProperty("发票英文地址")
    private String invoiceAddressEn;

    @ApiModelProperty("发货中文地址")
    private String deliverAddress;

    @ApiModelProperty("发货英文地址")
    private String deliverAddressEn;

    @ApiModelProperty("提取状态")
    @ExcelColumn(map = ErpSapConstants.status_MAP)
    private String status;

    @ApiModelProperty("数据状态")
    @ExcelColumn(map = ErpSapConstants.modifyMark_MAP)
    private Integer modifyMark;

    @ApiModelProperty("备注")
    private String note;

    @ApiModelProperty("传输批次号")
    private String tempOwner;

    /**
     * ERP最后变更日期
     */
    @ApiModelProperty("ERP创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastModifyDate;

    /**
     * 创建时间
     */
    @ApiModelProperty("接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("提取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date extractTime;
    private String tradeCode;
}
