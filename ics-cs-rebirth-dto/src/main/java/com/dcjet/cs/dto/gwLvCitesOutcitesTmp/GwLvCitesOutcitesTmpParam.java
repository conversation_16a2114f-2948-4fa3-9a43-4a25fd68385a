package com.dcjet.cs.dto.gwLvCitesOutcitesTmp;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 * <AUTHOR>
 * @date: 2022-4-14
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class GwLvCitesOutcitesTmpParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 企业编码
     */
	@XdoSize(max = 10, message = "企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 单证操作日期
     */
	@ApiModelProperty("单证操作日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date operateDate;

	private Integer orderBy;

	/**
     * TR标志(0-TR,1-LM)
     */
	@NotEmpty(message="TR标志(0-TR,1-LM)不能为空！")
	@XdoSize(max = 10, message = "TR标志(0-TR,1-LM)长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("TR标志(0-TR,1-LM)")
	private  String trMark;
	/**
     * 企业料号
     */
	@NotEmpty(message="企业料号不能为空！")
	@XdoSize(max = 30, message = "企业料号长度不能超过30位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业料号")
	private  String copGNo;
	/**
     * 外方CITES对应该企业料号的数量
     */
	@Digits(integer = 15, fraction = 0, message = "外方CITES对应该企业料号的数量必须为数字,整数位最大15位,小数最大0位!")
	@ApiModelProperty("外方CITES对应该企业料号的数量")
	private  Integer quantity;
	/**
     * 币制
     */
	@XdoSize(max = 4, message = "币制长度不能超过4位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币制")
	private  String curr;
	/**
     * 申报单价
     */
	@Digits(integer = 13, fraction = 5, message = "申报单价必须为数字,整数位最大13位,小数最大5位!")
	@ApiModelProperty("申报单价")
	private  BigDecimal declarePrice;
	/**
     * 外方CITES编号
     */
	@NotEmpty(message="外方CITES编号不能为空！")
	@XdoSize(max = 15, message = "外方CITES编号长度不能超过15位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("外方CITES编号")
	private  String outSideNo;
	/**
     * 外方证书到期日期
     */
	@ApiModelProperty("外方证书到期日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date outSideProofValid;

	private  String outSideProofValidTMP;


	/**
     * 是否quota
     */
	@XdoSize(max = 1, message = "是否quota长度不能超过1位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("是否quota")
	private  String isQuota;
	/**
     * Transport NO
     */
	@XdoSize(max = 30, message = "Transport NO长度不能超过30位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("Transport NO")
	private  String transportNo;
	/**
     * WEEK
     */
	@XdoSize(max = 7, message = "WEEK长度不能超过7位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("WEEK")
	private  String week;
	/**
     * 
     */
	@XdoSize(max = 1000, message = "长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String tempOwner;
	/**
     * 
     */
	@Digits(integer = 4, fraction = 0, message = "必须为数字,整数位最大4位,小数最大0位!")
	@ApiModelProperty("")
	private  Integer tempFlag;
	/**
     * 
     */
	@XdoSize(max = 1000, message = "长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String tempRemark;
	/**
     * 
     */
	@ApiModelProperty("")
	private  Integer tempIndex;
}
