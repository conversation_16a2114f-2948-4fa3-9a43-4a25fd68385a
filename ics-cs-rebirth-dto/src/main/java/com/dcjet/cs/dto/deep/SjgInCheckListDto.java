package com.dcjet.cs.dto.deep;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2019-11-9
 */
@ApiModel(value = "SjgInCheckListDto")
@Setter @Getter
public class SjgInCheckListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private  String sid;
	/**
      * 单证编号
      */
    @ApiModelProperty("单证编号")
	private  String billNo;
	/**
      * 序号
      */
    @ApiModelProperty("序号")
	private  Integer lineNo;
	/**
      * 移动类型
      */
    @ApiModelProperty("移动类型")
	private  String ieType;
	/**
      * 关联编号
      */
    @ApiModelProperty("关联编号")
	private  String linkedNo;
	/**
      * PO号
      */
    @ApiModelProperty("PO号")
	private  String poNo;
	/**
      * 发票号
      */
    @ApiModelProperty("发票号")
	private  String invoiceNo;
	/**
      * 交易日期
      */
    @ApiModelProperty("交易日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date deliveryDate;
	/**
      * 企业料号
      */
    @ApiModelProperty("企业料号")
	private  String facGNo;
	/**
      * 数量
      */
    @ApiModelProperty("数量")
	private  BigDecimal qty;
	/**
      * 交易单位
      */
    @ApiModelProperty("交易单位")
	private  String unit;
	/**
      * 单价
      */
    @ApiModelProperty("单价")
	private  BigDecimal decPrice;
	/**
      * 总价
      */
    @ApiModelProperty("总价")
	private  BigDecimal decTotal;
	/**
      * 币制
      */
    @ApiModelProperty("币制")
	private  String curr;
	/**
      * 转换后的保完税标记
      */
    @ApiModelProperty("转换后的保完税标记")
	private  String bondMark;
	/**
      * 供应商代码
      */
    @ApiModelProperty("供应商代码")
	private  String supplierCode;
	/**
      * 供应商联系人
      */
    @ApiModelProperty("供应商联系人")
	private  String supplierLinkMan;
	/**
      * 进口方式
      */
    @ApiModelProperty("进口方式")
	private  String inWay;
	/**
      * 结转状态
      */
    @ApiModelProperty("结转状态")
	private  String expStatus;
	/**
      * 业务员代号
      */
    @ApiModelProperty("业务员代号")
	private  String businessMan;
	/**
      * 人员
      */
    @ApiModelProperty("人员")
	private  String person;
	/**
      * 成本中心
      */
    @ApiModelProperty("成本中心")
	private  String costCenter;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 转换后的物料类型（I 料件  E 成品）
      */
    @ApiModelProperty("转换后的物料类型（I 料件  E 成品）")
//	@JsonProperty("gMark")
	private  String gMark;
	/**
      * 锁定标志（预留，暂不使用）
      */
    @ApiModelProperty("锁定标志（预留，暂不使用）")
	private  String lockMark;
	/**
      * 工厂代码
      */
    @ApiModelProperty("工厂代码")
	private  String factory;
	/**
      * 采购单价
      */
    @ApiModelProperty("采购单价")
	private  BigDecimal poPrice;
	/**
      * 对应ERP出入库表的SID
      */
    @ApiModelProperty("对应ERP出入库表的SID")
	private  String businessId;
	/**
      * 对账确认状态(0.未确认 1.已确认)
      */
    @ApiModelProperty("对账确认状态(0.未确认 1.已确认)")
	private  String confirmStatus;
	/**
      * 对账确认时间
      */
    @ApiModelProperty("对账确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 对账单编号
      */
    @ApiModelProperty("对账单编号")
	private  String checkNo;
	/**
      * 库位别
      */
    @ApiModelProperty("库位别")
	private  String warehouseNo;
	/**
	 * 供应商名称
	 */
	@ApiModelProperty("供应商名称")
	private String supplierName;

	/***
	 * 拆分序号
	 */
	@ApiModelProperty("拆分序号")
	private Integer splitSerialNo;

	/***
	 * 拆分ID
	 */
	@ApiModelProperty("拆分ID")
	private String splitId;

	/**
	 * 交易数量
	 */
	@ApiModelProperty("交易数量")
	private  BigDecimal qtyErp;
	/**
	 * 交易单位
	 */
	@ApiModelProperty("交易单位")
	private  String unitErp;
	/**
	 * erp数量转换标记  0：未转换 1：已转换
	 */
	@ApiModelProperty("转换标记")
	private String convertMark;

	/**
	 * 数据来源  1：界面新增  2：界面导入  3：接口提取
	 */
	@ApiModelProperty("数据来源  1：界面新增  2：界面导入  3：接口提取")
	private  String dataSource;
}
