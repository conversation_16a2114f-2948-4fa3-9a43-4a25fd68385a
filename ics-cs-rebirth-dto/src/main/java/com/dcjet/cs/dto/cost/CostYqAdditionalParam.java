package com.dcjet.cs.dto.cost;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date: 2022-8-10
 */
@Setter
@Getter
@ApiModel(value = "疫情附加费传入参数")
public class CostYqAdditionalParam implements Serializable {
    private static final long serialVersionUID = 1L;
    private String tradeCode;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * ESS(CNY)/KG  每公斤收费
     */
    @Digits(integer = 14, fraction = 5, message = "{ESS(CNY)/KG  每公斤收费必须为数字,整数位最大14位,小数最大5位!}")
    @ApiModelProperty("ESS(CNY)/KG  每公斤收费")
    private BigDecimal decPrice;
    /**
     * 进出口类型
     */
    @XdoSize(max = 2, message = "{进出口类型长度不能超过2位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("进出口类型")
    private String ieType;
    /**
     * 发货地
     */
    @XdoSize(max = 20, message = "{发货地长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发货地")
    private String startPlace;
    /**
     * 目的地
     */
    @XdoSize(max = 20, message = "{目的地长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("目的地")
    private String endPlace;
    /**
     * 国别类型 1 DHL 2 UPS
     */
    @XdoSize(max = 2, message = "{国别类型 1 DHL 2 UPS长度不能超过2位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("国别类型 1 DHL 2 UPS")
    private String countryType;
    /**
     * 收款单位
     */
    @XdoSize(max = 50, message = "{收款单位长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("收款单位")
    private String remitteeCode;

    private String curr;
}
