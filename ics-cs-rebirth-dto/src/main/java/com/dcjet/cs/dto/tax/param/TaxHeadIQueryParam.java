package com.dcjet.cs.dto.tax.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(value = "进口税金列表查询参数")
public class TaxHeadIQueryParam {

    @ApiModelProperty("单据内部编号")
    private String emsListNo;//模糊查询

    @ApiModelProperty("报关单号")
    private String entryNo;//模糊查询

    @ApiModelProperty("申报日期-开始")
    private String declareDateFrom;

    @ApiModelProperty("申报日期-结束")
    private String declareDateTo;

    @ApiModelProperty("监管方式")
    private String tradeMode;

    @ApiModelProperty("申报单位")
    private String declareName;//模糊查询

    @ApiModelProperty("付税方式")
    private String dutyType;

    @ApiModelProperty("支付日期-开始")
    private String payDateFrom;

    @ApiModelProperty("支付日期-结束")
    private String payDateTo;

    private String tradeCode;
    private String sid;
    @ApiModelProperty("提单表头单据内部编号")
    private String headEmsListNo;//提单表头单据内部编号
}
