package com.dcjet.cs.dto.erp.sap;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2019-11-25
 */
@ApiModel(value = "在制品返回信息")
@Setter @Getter
public class ErpWipStockDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private String sid;
	/**
      * WIP工单号
      */
    @ApiModelProperty("WIP工单号")
	private String woNo;
	/**
      * 企业料号
      */
    @ApiModelProperty("企业料号")
	private String facGNo;
	/**
      * 物料类型
      */
    @ApiModelProperty("物料类型")
	private String GMark;
	/**
      * 日期
      */
    @ApiModelProperty("日期")
	private String wipDate;
	/**
      * 数量
      */
    @ApiModelProperty("数量")
	private BigDecimal qty;
	/**
      * 交易单位
      */
    @ApiModelProperty("交易单位")
	private String unit;
	/**
	 * 成品版本号
	 */
	@ApiModelProperty("成品版本号")
	private String exgVersion;
	/**
      * REMARK1
      */
    @ApiModelProperty("REMARK1")
	private String remark1;
	/**
      * REMARK2
      */
    @ApiModelProperty("REMARK2")
	private String remark2;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private String insertUser;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date insertTime;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateTime;
	/**
      * 用户/导入批次号
      */
    @ApiModelProperty("用户/导入批次号")
	private String tempOwner;
	/**
      * 数据状态 0 未提取，1 提取过
      */
    @ApiModelProperty("数据状态 0 未提取，1 提取过")
	private String status;
	/**
      * 数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）
      */
    @ApiModelProperty("数据状态：1-修改 2-删除 3-增加（目前仅支持3.新增）")
	private String modifyMark;
	/**
      * ERP最后变更日期
      */
    @ApiModelProperty("ERP最后变更日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date lastModifyDate;
	/**
      * 数据来源 0 手工录入 1ERP接口 2导入
      */
    @ApiModelProperty("数据来源 0 手工录入 1ERP接口 2导入")
	private String dataSource;
	/**
	 * 清洗标志（0 未清洗 1清洗成功）
	 */
	@ApiModelProperty("清洗标志（0 未清洗 1清洗成功）")
	private String isClear;
	/**
	 * 转换后单位
	 */
	@ApiModelProperty("转换后单位")
	private String unitConvert;
}
