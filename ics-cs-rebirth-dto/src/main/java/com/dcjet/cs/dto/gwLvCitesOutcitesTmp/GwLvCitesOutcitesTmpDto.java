package com.dcjet.cs.dto.gwLvCitesOutcitesTmp;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2022-4-14
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class GwLvCitesOutcitesTmpDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * SID
      */
    @ApiModelProperty("SID")
	private  String sid;
	/**
      * 插入时间
      */
    @ApiModelProperty("插入时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 插入人
      */
    @ApiModelProperty("插入人")
	private  String insertUser;

	private Integer orderBy;

	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 单证操作日期
      */
    @ApiModelProperty("单证操作日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date operateDate;
	/**
      * TR标志(0-TR,1-LM)
      */
    @ApiModelProperty("TR标志(0-TR,1-LM)")
	private  String trMark;
	/**
      * 企业料号
      */
    @ApiModelProperty("企业料号")
	private  String copGNo;
	/**
      * 外方CITES对应该企业料号的数量
      */
    @ApiModelProperty("外方CITES对应该企业料号的数量")
	private  Integer quantity;
	/**
      * 币制
      */
    @ApiModelProperty("币制")
	private  String curr;
	/**
      * 申报单价
      */
    @ApiModelProperty("申报单价")
	private  BigDecimal declarePrice;
	/**
      * 外方CITES编号
      */
    @ApiModelProperty("外方CITES编号")
	private  String outSideNo;
	/**
      * 外方证书到期日期
      */
    @ApiModelProperty("外方证书到期日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date outSideProofValid;
	/**
      * 是否quota
      */
    @ApiModelProperty("是否quota")
	private  String isQuota;
	/**
      * Transport NO
      */
    @ApiModelProperty("Transport NO")
	private  String transportNo;
	/**
      * WEEK
      */
    @ApiModelProperty("WEEK")
	private  String week;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tempOwner;
	/**
      * 
      */
    @ApiModelProperty("")
	private  Integer tempFlag;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tempRemark;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tempIndex;
}
