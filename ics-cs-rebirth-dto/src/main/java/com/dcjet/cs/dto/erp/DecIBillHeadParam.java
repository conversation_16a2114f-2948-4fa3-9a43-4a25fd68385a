package com.dcjet.cs.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2019-4-22
 */
@Setter @Getter
@ApiModel(value = "进口清单表头传入参数")
public class DecIBillHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 核注清单编号
     */
	@XdoSize(max = 64, message = "{核注清单编号长度不能超过64位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("核注清单编号")
	private  String listNo;
	/**
     * 清单预录入统一编号
     */
	@XdoSize(max = 18, message = "{清单预录入统一编号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("清单预录入统一编号")
	private  String billSeqNo;
	/**
     * 变更次数
     */
	@Digits(integer = 11, fraction = 0, message = "{变更次数必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("变更次数")
	private BigDecimal changeTimes;
	/**
     * 账册（手册）编号
     */
	@XdoSize(max = 12, message = "{账册（手册）编号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("账册（手册）编号")
	private  String emsNo;
	/**
     * 清单企业内部编号
     */
	@NotEmpty(message="{清单企业内部编号不能为空！}")
	@XdoSize(max = 64, message = "{清单企业内部编号长度不能超过64位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("清单企业内部编号")
	private  String emsListNo;
//	/**
//     * 经营企业社会信用代码
//     */
//	@XdoSize(max = 18, message = "{经营企业社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
//	@ApiModelProperty("经营企业社会信用代码")
//	private  String tradeCreditCode;
//	/**
//     * 经营企业编码
//     */
//	@NotEmpty(message="{经营企业编码不能为空！}")
//	@XdoSize(max = 10, message = "{经营企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//	@ApiModelProperty("经营企业编码")
//	private  String tradeCode;
//	/**
//     * 经营企业名称
//     */
//	@NotEmpty(message="{经营企业名称不能为空！}")
//	@XdoSize(max = 70, message = "{经营企业名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
//	@ApiModelProperty("经营企业名称")
//	private  String tradeName;
	/**
     * 收货企业编码
     */
	@XdoSize(max = 10, message = "{收货企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收货企业编码")
	private  String receiveCode;
	/**
     * 收货企业社会信用代码
     */
	@XdoSize(max = 18, message = "{收货企业社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收货企业社会信用代码")
	private  String receiveCreditCode;
	/**
     * 收货企业名称
     */
	@XdoSize(max = 70, message = "{收货企业名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收货企业名称")
	private  String receiveName;
	/**
     * 申报企业社会信用代码
     */
	@XdoSize(max = 18, message = "{申报企业社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报企业社会信用代码")
	private  String declareCreditCode;
	/**
     * 申报企业编码
     */
	@NotEmpty(message="{申报企业编码不能为空！}")
	@XdoSize(max = 10, message = "{申报企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报企业编码")
	private  String declareCode;
	/**
     * 申报企业名称
     */
	@NotEmpty(message="{申报企业名称不能为空！}")
	@XdoSize(max = 70, message = "{申报企业名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报企业名称")
	private  String declareName;
	/**
     * 清单申报日期
     */
	@ApiModelProperty("清单申报日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private  Date declareDate;
	/**
    * 清单申报日期-开始
    */
	@ApiModelProperty("清单申报日期-开始")
	private String declareDateFrom;
	/**
    * 清单申报日期-结束
    */
	@ApiModelProperty("清单申报日期-结束")
    private String declareDateTo;
	/**
     * 报关单号
     */
	@XdoSize(max = 18, message = "{报关单号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("报关单号")
	private  String entryNo;
	/**
     * 报关单统一编号
     */
	@XdoSize(max = 18, message = "{报关单统一编号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("报关单统一编号")
	private  String seqNo;
	/**
     * 关联核注清单编号
     */
	@XdoSize(max = 64, message = "{关联核注清单编号长度不能超过64位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联核注清单编号")
	private  String relListNo;
	/**
     * 关联账册（手册）编号
     */
	@XdoSize(max = 12, message = "{关联账册（手册）编号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联账册（手册）编号")
	private  String relEmsNo;
	/**
     * 关联报关单编号
     */
	@XdoSize(max = 18, message = "{关联报关单编号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联报关单编号")
	private  String relEntryNo;
	/**
     * 关联报关单经营企业社会信用代码/生产销售(消费使用)
     */
	@XdoSize(max = 18, message = "{关联报关单经营企业社会信用代码/生产销售(消费使用)长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联报关单经营企业社会信用代码/生产销售(消费使用)")
	private  String relEntryTradeCreditCode;
	/**
     * 关联报关单经营企业编号/生产销售(消费使用)
     */
	@XdoSize(max = 10, message = "{关联报关单经营企业编号/生产销售(消费使用)长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联报关单经营企业编号/生产销售(消费使用)")
	private  String relEntryTradeCode;
	/**
     * 关联报关单经营企业名称/生产销售(消费使用)
     */
	@XdoSize(max = 70, message = "{关联报关单经营企业名称/生产销售(消费使用)长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联报关单经营企业名称/生产销售(消费使用)")
	private  String relEntryTradeName;
	/**
     * 关联报关单收货企业社会统一信用代码
     */
	@XdoSize(max = 18, message = "{关联报关单收货企业社会统一信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联报关单收货企业社会统一信用代码")
	private  String relEntryReceiveCreditCode;
	/**
     * 关联报关单海关收发货单位编码
     */
	@XdoSize(max = 10, message = "{关联报关单海关收发货单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联报关单海关收发货单位编码")
	private  String relEntryReceiveCode;
	/**
     * 关联报关单收发货单位名称
     */
	@XdoSize(max = 70, message = "{关联报关单收发货单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联报关单收发货单位名称")
	private  String relEntryReceiveName;
	/**
     * 对应报关单申报单位社会统一信用代码
     */
	@XdoSize(max = 18, message = "{对应报关单申报单位社会统一信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("对应报关单申报单位社会统一信用代码")
	private  String corrEntryDeclareCreditCode;
	/**
     * 关联报关单海关申报单位编码
     */
	@XdoSize(max = 10, message = "{关联报关单海关申报单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联报关单海关申报单位编码")
	private  String relEntryDeclareCode;
	/**
     * 关联报关单申报单位名称
     */
	@XdoSize(max = 70, message = "{关联报关单申报单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联报关单申报单位名称")
	private  String relEntryDeclareName;
	/**
     * 进出口口岸代码
     */
	@NotEmpty(message="{进出口口岸代码不能为空！}")
	@XdoSize(max = 4, message = "{进出口口岸代码长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("进出口口岸代码")
	private  String IEPort;
	/**
     *申报地海关
     */
	@XdoSize(max = 4, message = "{主管海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报地海关")
	private  String masterCustoms;
	/**
     * 进出口标记代码
     */
	@XdoSize(max = 4, message = "{进出口标记代码长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("进出口标记代码")
	private  String IEMark;
	/**
     * 料件成品标记代码
     */
	@XdoSize(max = 4, message = "{料件成品标记代码长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("料件成品标记代码")
	private  String GMark;
	/**
     * 监管方式代码
     */
	@NotEmpty(message="{监管方式代码不能为空！}")
	@XdoSize(max = 6, message = "{监管方式代码长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("监管方式代码")
	private  String tradeMode;
	/**
     * 运输方式代码
     */
	@NotEmpty(message="{运输方式代码不能为空！}")
	@XdoSize(max = 6, message = "{运输方式代码长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运输方式代码")
	private  String trafMode;
	/**
     * 是否报关标志
     */
	@XdoSize(max = 1, message = "{是否报关标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("是否报关标志")
	private  String dclcusMark;
	/**
     * 报关类型代码
     */
	@XdoSize(max = 1, message = "{报关类型代码长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("报关类型代码")
	private  String dclcusType;
	/**
     * 核扣标记代码
     */
	@XdoSize(max = 4, message = "{核扣标记代码长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("核扣标记代码")
	private  String vrfdedMark;
	/**
     * 申请表编号/申报表编号
     */
	@XdoSize(max = 50, message = "{申请表编号/申报表编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申请表编号/申报表编号")
	private  String applyNo;
	/**
     * 流转类型
     */
	@XdoSize(max = 1, message = "{流转类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("流转类型")
	private  String rotateType;
	/**
     * 录入企业编号
     */
	@XdoSize(max = 10, message = "{录入企业编号长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("录入企业编号")
	private  String inputCode;
	/**
     * 录入企业社会信用代码
     */
	@XdoSize(max = 18, message = "{录入企业社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("录入企业社会信用代码")
	private  String inputCreditCode;
	/**
     * 录入单位名称
     */
	@XdoSize(max = 70, message = "{录入单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("录入单位名称")
	private  String inputName;
	/**
     * 申报人IC卡号
     */
	@XdoSize(max = 20, message = "{申报人IC卡号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报人IC卡号")
	private  String icCardNo;
	/**
     * 录入日期
     */
	@ApiModelProperty("录入日期")
	private  Date inputDate;
	/**
    * 录入日期-开始
    */
	@ApiModelProperty("录入日期-开始")
	private String inputDateFrom;
	/**
    * 录入日期-结束
    */
	@ApiModelProperty("录入日期-结束")
    private String inputDateTo;
	/**
     * 清单状态
     */
	@XdoSize(max = 10, message = "{清单状态长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("清单状态")
	private  String status;
	/**
     * 关联报关单申报单位社会统一信用代码
     */
	@XdoSize(max = 18, message = "{关联报关单申报单位社会统一信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联报关单申报单位社会统一信用代码")
	private  String relEntryDeclareCreditCode;
	/**
     * 对应报关单申报单位代码
     */
	@XdoSize(max = 10, message = "{对应报关单申报单位代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("对应报关单申报单位代码")
	private  String corrEntryDeclareCode;
	/**
     * 对应报关单申报单位名称
     */
	@XdoSize(max = 70, message = "{对应报关单申报单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("对应报关单申报单位名称")
	private  String corrEntryDeclareName;
	/**
     * 报关单类型
     */
	@XdoSize(max = 1, message = "{报关单类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("报关单类型")
	private  String entryType;
	/**
     * 启运、运抵国别代码
     */
	@XdoSize(max = 3, message = "{启运、运抵国别代码长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("启运、运抵国别代码")
	private  String tradeCountry;
	/**
     * 备注
     */
	@XdoSize(max = 512, message = "{备注长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 数据来源
     */
	@XdoSize(max = 255, message = "{数据来源长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("数据来源")
	private  String source;
	/**
     * 预核扣时间
     */
	@ApiModelProperty("预核扣时间")
	private  Date preVrfdedDate;
	/**
    * 预核扣时间-开始
    */
	@ApiModelProperty("预核扣时间-开始")
	private String preVrfdedDateFrom;
	/**
    * 预核扣时间-结束
    */
	@ApiModelProperty("预核扣时间-结束")
    private String preVrfdedDateTo;
	/**
     * 正式核扣时间
     */
	@ApiModelProperty("正式核扣时间")
	private  Date formalVrfdedDate;
	/**
    * 正式核扣时间-开始
    */
	@ApiModelProperty("正式核扣时间-开始")
	private String formalVrfdedDateFrom;
	/**
    * 正式核扣时间-结束
    */
	@ApiModelProperty("正式核扣时间-结束")
    private String formalVrfdedDateTo;
	/**
     * 清单进出卡口状态代码
     */
	@XdoSize(max = 1, message = "{清单进出卡口状态代码长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("清单进出卡口状态代码")
	private  String listIochkptStatus;
	/**
     * 核扣方式代码
     */
	@XdoSize(max = 4, message = "{核扣方式代码长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("核扣方式代码")
	private  String vrfdedMode;
	/**
     * 核算代码
     */
	@XdoSize(max = 4, message = "{核算代码长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("核算代码")
	private  String duCode;
	/**
     * 账册（手册）内部编号
     */
	@XdoSize(max = 30, message = "{账册（手册）内部编号长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("账册（手册）内部编号")
	private  String copEmsNo;
	/**
     * 报关单申报日期
     */
	@ApiModelProperty("报关单申报日期")
	private  Date entryDeclareDate;
	/**
    * 报关单申报日期-开始
    */
	@ApiModelProperty("报关单申报日期-开始")
	private String entryDeclareDateFrom;
	/**
    * 报关单申报日期-结束
    */
	@ApiModelProperty("报关单申报日期-结束")
    private String entryDeclareDateTo;
	/**
     * 提单号
     */
	@XdoSize(max = 50, message = "{提单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("提单号")
	private  String deliveryId;
	/**
     * 是否人工归并（0：自动归并 1：人工归并）
     */
	@XdoSize(max = 1, message = "{是否人工归并（0：自动归并 1：人工归并）长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("是否人工归并（0：自动归并 1：人工归并）")
	private  String mergeType;
	/**
     * 入库时间
     */
	@ApiModelProperty("入库时间")
	private  Date arrivalDate;
	/**
    * 入库时间-开始
    */
	@ApiModelProperty("入库时间-开始")
	private String arrivalDateFrom;
	/**
    * 入库时间-结束
    */
	@ApiModelProperty("入库时间-结束")
    private String arrivalDateTo;
	/**
     * 清单类型
     */
	@XdoSize(max = 1, message = "{清单类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("清单类型")
	private  String billListType;
	/**
     * 报关单状态
     */
	@XdoSize(max = 1, message = "{报关单状态长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("报关单状态")
	private  String entryStucd;
	/**
     * 子系统ID
     */
	@XdoSize(max = 3, message = "{子系统ID长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("子系统ID")
	private  String chId;
	/**
     * 申报标志
     */
	@XdoSize(max = 1, message = "{申报标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报标志")
	private  String delareFlag;
	/**
     * 包ID
     */
	@XdoSize(max = 512, message = "{包ID长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("包ID")
	private  String pocketId;
	/**
     * 当前包序号
     */
	@Digits(integer = 11, fraction = 0, message = "{当前包序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("当前包序号")
	private BigDecimal curPocketNo;
	/**
     * 总包数
     */
	@Digits(integer = 11, fraction = 0, message = "{总包数必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("总包数")
	private BigDecimal totalPocketQty;
	/**
     * 是否结构化
     */
	@XdoSize(max = 20, message = "{是否结构化长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("是否结构化")
	private  String structuredType;
	/**
     * 有效标志 0-有效 1-无效
     */
	@XdoSize(max = 1, message = "{有效标志 0-有效 1-无效长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("有效标志 0-有效 1-无效")
	private  String validMark;
	/**
     * 操作类型
     */
	@XdoSize(max = 1, message = "{操作类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("操作类型")
	private  String opType;
	/**
     * 系统编号
     */
	@XdoSize(max = 3, message = "{系统编号长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("系统编号")
	private  String sysNo;
	/**
     * 申报类型1-备案申请 2-变更申 请 3-删除申请（目前默认为：1，后期会增 加 2 和 3）
     */
	@XdoSize(max = 1, message = "{申报类型1-备案申请 2-变更申 请 3-删除申请（目前默认为：1，后期会增 加 2 和 3） 长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报类型1-备案申请 2-变更申 请 3-删除申请（目前默认为：1，后期会增 加 2 和 3） ")
	private  String declareType;
	/**
     * 备案表头ID
     */
	@NotEmpty(message="{备案表头ID不能为空！}")
	@XdoSize(max = 40, message = "{备案表头ID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案表头ID")
	private  String headId;
	/**
     * 杂费币制
     */
	@XdoSize(max = 3, message = "{杂费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("杂费币制")
	private  String otherCurr;
	/**
     * 杂费类型
     */
	@XdoSize(max = 1, message = "{杂费类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("杂费类型")
	private  String otherMark;
	/**
     * 杂费
     */
	@Digits(integer = 11, fraction = 5, message = "{杂费必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("杂费")
	private  BigDecimal otherRate;
	/**
     * 运费币制
     */
	@XdoSize(max = 3, message = "{运费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运费币制")
	private  String feeCurr;
	/**
     * 运费类型
     */
	@XdoSize(max = 1, message = "{运费类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运费类型")
	private  String feeMark;
	/**
     * 运费
     */
	@Digits(integer = 11, fraction = 5, message = "{运费必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("运费")
	private  BigDecimal feeRate;
	/**
     * 保费币制
     */
	@XdoSize(max = 3, message = "{保费币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("保费币制")
	private  String insurCurr;
	/**
     * 保费类型
     */
	@XdoSize(max = 1, message = "{保费类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("保费类型")
	private  String insurMark;
	/**
     * 保费
     */
	@Digits(integer = 11, fraction = 5, message = "{保费必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("保费")
	private  BigDecimal insurRate;
	/**
     * 供应商编码
     */
	@XdoSize(max = 20, message = "{供应商编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("供应商编码")
	private  String supplierCode;
	/**
     * 供应商
     */
	@XdoSize(max = 100, message = "{供应商长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("供应商")
	private  String supplierName;
	/**
     * 货代编码
     */
	@XdoSize(max = 50, message = "{货代编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货代编码")
	private  String forwardCode;
	/**
     * 货代
     */
	@XdoSize(max = 100, message = "{货代长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货代")
	private  String forwardName;
	/**
     * 主提运单
     */
	@XdoSize(max = 50, message = "{主提运单长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("主提运单")
	private  String mawb;
	/**
     * 提运单号
     */
	@XdoSize(max = 50, message = "{提运单号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("提运单号")
	private  String hawb;
	/**
     * 合同协议号
     */
	@XdoSize(max = 50, message = "{合同协议号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("合同协议号")
	private  String contrNo;
	/**
     * 航班日期
     */
	@ApiModelProperty("航班日期")
	private  Date flightDate;
	/**
    * 航班日期-开始
    */
	@ApiModelProperty("航班日期-开始")
	private String flightDateFrom;
	/**
    * 航班日期-结束
    */
	@ApiModelProperty("航班日期-结束")
    private String flightDateTo;
	/**
     * 出货日期
     */
	@ApiModelProperty("出货日期")
	private  Date shipDate;
	/**
    * 出货日期-开始
    */
	@ApiModelProperty("出货日期-开始")
	private String shipDateFrom;
	/**
    * 出货日期-结束
    */
	@ApiModelProperty("出货日期-结束")
    private String shipDateTo;
	/**
     * 境外发货人code
     */
	@XdoSize(max = 255, message = "{境外发货人code长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境外发货人code")
	private  String overseasShipper;
	/**
     * 境外发货人name
     */
	@XdoSize(max = 255, message = "{境外发货人name长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("境外发货人name")
	private  String overseasShipperName;
	/**
     * 最终目的国
     */
	@XdoSize(max = 3, message = "{最终目的国长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("最终目的国")
	private  String districtCountry;
	/**
     * 入境口岸/出境口岸
     */
	@XdoSize(max = 6, message = "{入境口岸/出境口岸长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("入境口岸/出境口岸")
	private  String entryPort;
	/**
     * 贸易国别
     */
	@XdoSize(max = 6, message = "{贸易国别长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("贸易国别")
	private  String tradeNation;
	/**
     * 启运港
     */
	@XdoSize(max = 6, message = "{启运港长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("启运港")
	private  String despPort;
	/**
     * 许可证号
     */
	@XdoSize(max = 50, message = "{许可证号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("许可证号")
	private  String licenseNo;
	/**
     * 体积
     */
	@Digits(integer = 11, fraction = 5, message = "{体积必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("体积")
	private  BigDecimal volume;
	/**
     * 征免性质
     */
	@XdoSize(max = 3, message = "{征免性质长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("征免性质")
	private  String cutMode;
	/**
     * 指运港/装货港
     */
	@XdoSize(max = 40, message = "{指运港/装货港长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("指运港/装货港")
	private  String destPort;
	/**
     * 净重
     */
	@Digits(integer = 11, fraction = 5, message = "{净重必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("净重")
	private  BigDecimal netWt;
	/**
     * 毛重
     */
	@Digits(integer = 11, fraction = 5, message = "{毛重必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("毛重")
	private  BigDecimal grossWt;
	/**
     * 成交方式
     */
	@XdoSize(max = 3, message = "{成交方式长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("成交方式")
	private  String transMode;
	/**
     * 运输工具名称
     */
	@XdoSize(max = 100, message = "{运输工具名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运输工具名称")
	private  String trafName;
	/**
     * 航次号
     */
	@XdoSize(max = 100, message = "{航次号长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("航次号")
	private  String voyageNo;
	/**
     * 包装种类
     */
	@XdoSize(max = 50, message = "{包装种类长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("包装种类")
	private  String wrapType;
	/**
     * 件数
     */
	@Digits(integer = 11, fraction = 0, message = "{件数必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("件数")
	private BigDecimal packNum;
	/**
     * 货物存放地点
     */
	@XdoSize(max = 255, message = "{货物存放地点长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货物存放地点")
	private  String warehouse;
	/**
     * 保税标识 0-保税 1-非保税
     */
	@XdoSize(max = 1, message = "{保税标识 0-保税 1-非保税长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("保税标识 0-保税 1-非保税")
	private  String bondMark;
	/**
     * 特殊关系确认
     */
	@XdoSize(max = 1, message = "{特殊关系确认长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("特殊关系确认")
	private  String confirmSpecial;
	/**
     * 价格影响确认
     */
	@XdoSize(max = 1, message = "{价格影响确认长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("价格影响确认")
	private  String confirmPrice;
	/**
     * 支持特许权使用费确认
     */
	@XdoSize(max = 1, message = "{支持特许权使用费确认长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("支持特许权使用费确认")
	private  String confirmRoyalties;
	/**
     * 自报自缴
     */
	@XdoSize(max = 1, message = "{自报自缴长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("自报自缴")
	private  String dutySelf;

	/**
	 * 核销周期 DCR_CYCLE
	 */
	@ApiModelProperty("核销周期")
	private String dcrCycle;
	/**
	 * 包装种类2
	 */
	@ApiModelProperty("包装种类2")
	private  String wrapType2;
	/**
	 * 创建时间-开始
	 */
	@ApiModelProperty("创建时间-开始")
	private String insertTimeFrom;
	/**
	 * 创建时间-结束
	 */
	@ApiModelProperty("创建时间-结束")
	private String insertTimeTo;
	/**
	 * 发送状态
	 */
	@ApiModelProperty("发送状态")
	private String sendApiStatus;
	/**
	 * 报关单报文状态
	 */
	@ApiModelProperty("报关单报文状态")
	private  String entryStatus;
	/**
	 * 审核状态
	 */
	@ApiModelProperty("审核状态")
	private String apprStatus;
	/**
	 * 清单申报单位(反)
	 */
	@XdoSize(max = 10, message = "{清单申报单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("清单申报单位")
	private  String agentCode;
	/**
	 * 报关单申报单位名称(反)
	 */
	@XdoSize(max = 70, message = "{清单申报单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("清单申报单位名称")
	private  String agentName;
	/**
	 * 报关单申报单位社会统一信用代码(反)
	 */
	@XdoSize(max = 18, message = "{清单申报单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("清单申报单位社会信用代码")
	private  String agentCreditCode;
	/**
	 * 消费使用单位编码
	 */
	@XdoSize(max = 10, message = "{消费使用单位编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("消费使用单位编码")
	private  String ownerCode;
	/**
	 * 消费使用单位名称
	 */
	@XdoSize(max = 70, message = "{消费使用单位名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("消费使用单位名称")
	private  String ownerName;
	/**
	 * 消费使用单位社会信用代码
	 */
	@XdoSize(max = 18, message = "{消费使用单位社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("消费使用单位社会信用代码")
	private  String ownerCreditCode;
	/**
	 * 是否涉证 0-否，1-是
	 */
	@ApiModelProperty("是否涉证")
	@XdoSize(max = 1, message = "{是否涉证长度不能超过1位字节长度!}")
	private String cardMark;
	/**
	 * 是否涉检 0-否，1-是
	 */
	@ApiModelProperty("是否涉证")
	@XdoSize(max = 1, message = "{是否涉证长度不能超过1位字节长度!}")
	private String checkMark;
	/**
	 * 是否涉税 0-否，1-是
	 */
	@ApiModelProperty("是否涉证")
	@XdoSize(max = 1, message = "{是否涉证长度不能超过1位字节长度!}")
	private String taxMark;
	/**
	 * 发送日期
	 */
	@ApiModelProperty("发送日期")
	private Date sendApiTime;
	/**
	 * 发送日期-开始
	 */
	@ApiModelProperty("发送日期-开始")
	private String sendApiTimeFrom;
	/**
	 * 发送日期-结束
	 */
	@ApiModelProperty("发送日期-结束")
	private String sendApiTimeTo;
	/**
	 * 公式价格确认
	 */
	@ApiModelProperty("公式价格确认")
	private String confirmFormulaPrice;
	/**
	 * 暂定价格确认
	 */
	@ApiModelProperty("暂定价格确认 ")
	private String confirmTempPrice;
}
