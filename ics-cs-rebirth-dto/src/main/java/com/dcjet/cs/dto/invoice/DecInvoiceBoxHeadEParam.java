package com.dcjet.cs.dto.invoice;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 *
 * <AUTHOR>
 * @date: 2019-6-12
 */
@Setter @Getter
@ApiModel(value = "出口发票箱单表头传入参数")
public class DecInvoiceBoxHeadEParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 关联编号（单据内部编号）
     */
	@NotEmpty(message="{关联编号（单据内部编号）不能为空！}")
	@XdoSize(max = 50, message = "{关联编号（单据内部编号）长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("关联编号（单据内部编号）")
	private  String emsListNo;
	/**
     * 发票号码
     */
	@NotEmpty(message="{发票号码不能为空！}")
	@XdoSize(max = 100, message = "{发票号码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发票号码")
	private  String invoiceNo;
	/**
     * 合同协议号
     */
	@XdoSize(max = 32, message = "{合同协议号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("合同协议号")
	private  String contrNo;
	/**
     * 客户代码
     */
	@XdoSize(max = 50, message = "{客户代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户代码")
	private  String customerCode;
	/**
     * 运输方式
     */
	@XdoSize(max = 6, message = "{运输方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运输方式")
	private  String trafMode;
	/**
     * 货代编码
     */
	@XdoSize(max = 50, message = "{货代编码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货代编码")
	private  String forwardCode;
	/**
     * 目的地
     */
	@XdoSize(max = 100, message = "{目的地长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("目的地")
	private  String destination;
	/**
     * 总毛重
     */
	@Digits(integer = 11, fraction = 5, message = "{总毛重必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("总毛重")
	private  BigDecimal grossWt;
	/**
     * 总净重
     */
	@Digits(integer = 11, fraction = 5, message = "{总净重必须为数字,整数位最大11位,小数最大5位!}")
	@ApiModelProperty("总净重")
	private  BigDecimal netWt;
	/**
     * 件数
     */
	@Digits(integer = 11, fraction = 0, message = "{件数必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("件数")
	private  Integer packNum;
	/**
     * 包装种类
     */
	@XdoSize(max = 50, message = "{包装种类长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("包装种类")
	private  String wrapType;
	/**
     * 出货日期
     */
	@ApiModelProperty("出货日期")
	private  Date shipmentDate;
	/**
    * 出货日期-开始
    */
	@ApiModelProperty("出货日期-开始")
	private String shipmentDateFrom;
	/**
    * 出货日期-结束
    */
	@ApiModelProperty("出货日期-结束")
    private String shipmentDateTo;
	/**
     * 模板id
     */
	@NotEmpty(message="{模板id不能为空！}")
	@XdoSize(max = 36, message = "{模板id长度不能超过36位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("模板id")
	private  String templateHeadId;
	/**
     * 状态 1：锁定 0：未锁定
     */
	@ApiModelProperty("状态 1：锁定 0：未锁定")
	private  String status;
	/**
     * 提单表头id
     */
	@NotEmpty(message="{提单表头id不能为空！}")
	@XdoSize(max = 36, message = "{提单表头id长度不能超过36位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("提单表头id")
	private  String erpHeadId;
	/**
     * 备注
     */
	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 模板编号
     */
	@NotEmpty(message="{模板编号不能为空！}")
	@XdoSize(max = 20, message = "{模板编号长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("模板编号")
	private  String templateCode;
	/**
     * 客户名称
     */
//	@NotEmpty(message="{客户名称不能为空！}")
	@XdoSize(max = 200, message = "{客户名称长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户名称")
	private  String customerName;
	/**
     * 货代名称
     */
	@XdoSize(max = 100, message = "{货代名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货代名称")
	private  String forwardName;
	/**
	 * 收货人代码
	 */
	@NotEmpty(message="{境外收货人代码不能为空！}")
	@XdoSize(max = 50, message = "{境外收货人代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收货人代码")
	private  String consigneeCode;
	/**
	 * 收货人名称
	 */
	@NotEmpty(message="{境外收货人名称不能为空！}")
	@XdoSize(max = 200, message = "{境外收货人名称长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收货人名称")
	private  String consigneeName;
	/**
	 * 收货人地址
	 */
	@XdoSize(max = 250, message = "{收货人地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收货人地址")
	private  String consigneeAddr;
	/**
	 * 收货人电话
	 */
	@XdoSize(max = 100, message = "{收货人电话长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收货人电话")
	private  String consigneeTel;
	/**
	 * 启运港
	 */
	@XdoSize(max = 20, message = "{启运港长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("启运港")
	private  String despPort;
	/**
	 * 目的港
	 */
	@XdoSize(max = 50, message = "{目的港长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("目的港")
	private  String destPort;
	/**
	 * SHIP_TO
	 */
	@XdoSize(max = 512, message = "{SHIP_TO地址及电话长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("SHIP_TO")
	private  String shipTo;
	/**
	 * 发票日期
	 */
	@ApiModelProperty("发票日期")
	private  Date invoiceDate;
	/**
	 * 发票日期-开始
	 */
	@ApiModelProperty("发票日期-开始")
	private String invoiceDateFrom;
	/**
	 * 发票日期-结束
	 */
	@ApiModelProperty("发票日期-结束")
	private String invoiceDateTo;
	/**
	 * 币制
	 */
	@XdoSize(max = 20, message = "{币制长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("币制")
	private  String curr;
	/**
	 * 货代
	 */
	@XdoSize(max = 50, message = "{货代长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("货代")
	private  String forwarder;
	/**
	 * 运输方式
	 */
	@XdoSize(max = 20, message = "{运输方式长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("运输方式")
	private  String trafModeName;
	/**
	 * 付款方式
	 */
	@XdoSize(max = 50, message = "{付款方式长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("付款方式")
	private  String paymentMode;
	/**
	 * 成交方式
	 */
	@XdoSize(max = 100, message = "{成交方式长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("成交方式")
	private  String transModeName;
	/**
	 * 银行信息
	 */
	@XdoSize(max = 500, message = "{银行信息长度不能超过500位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("银行信息")
	private  String bankInfo;
	/**
	 * 唛头
	 */
	@XdoSize(max = 500, message = "{唛头长度不能超过500位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("唛头")
	private  String marks;

	/**
	 * SHIP_TO名称
	 */
	@XdoSize(max = 100, message = "{SHIP_TO名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("SHIP_TO名称")
	private  String shipToName;
	/***
	 * 汇率
	 */
	@Digits(integer = 5, fraction = 4, message = "{汇率重必须为数字,整数位最大5位,小数最大4位!}")
	@ApiModelProperty("汇率")
	private BigDecimal exchangeRate;
	/***
	 * 运抵国
	 */
	@ApiModelProperty("运抵国")
	private String tradeCountry;
	/**
	 * 单位净重
	 */
	@Digits(integer = 10, fraction = 5, message = "{单位净重必须为数字,整数位最大10位,小数最大5位!}")
	@ApiModelProperty("单位净重")
	private BigDecimal unitNetWt;

	/**
	 * 单位体积
	 */
	@Digits(integer = 10, fraction = 5, message = "{单位体积必须为数字,整数位最大10位,小数最大5位!}")
	@ApiModelProperty("单位体积")
	private BigDecimal unitVolume;
	/**
	 * 包装尺寸
	 */
	@ApiModelProperty("包装尺寸")
	private String packing;
}
