package com.dcjet.cs.dto.repair;

import com.dcjet.cs.dto.base.BasicDto;
import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 出境修理表头
 * 
 * generated by Generate dc
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "出境修理表头 DTO")
@Setter @Getter
public class RepairWarningDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "单据内部编号")
    private String emsListNo;

    @ApiModelProperty(value = "报关单号")
    private String entryNo;

    @ApiModelProperty(value = "最迟复运日期")
    private Date endDate;

    @ApiModelProperty(value = "复运完成日期")
    private Date completeDate;

    @ApiModelProperty(value = "预警状态, 0 正常，1 预警 2 超期 3 注销")
    @ExcelColumn(map = "0:正常,1:预警,2:超期,3:注销")
    private String warningStatus;

    @ApiModelProperty(value = "预警天数")
    private Integer warningDay;

    @ApiModelProperty(value = "备注")
    private String warningNote;

    @ApiModelProperty(value = "企业料号")
    private String facGNo;

    @ApiModelProperty(value = "商品编码")
    private String codeTS;

    @ApiModelProperty(value = "商品名称")
    private String gname;

    @ApiModelProperty(value = "申报数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "剩余数量")
    private BigDecimal remainQty;

    @ApiModelProperty("复运数量")
    private BigDecimal useQty;

    @ApiModelProperty("制单人")
    private String userName;

    public void update() {
        this.getUserName();
        this.getUseQty();
    }

    @Override
    public String getUserName() {
        this.userName = (getInsertUser()==null?"":this.getInsertUser())+ (this.getInsertUserName()==null?"":"("+this.getInsertUserName()+")");
        return userName;
    }

    public BigDecimal getUseQty() {
        if (useQty == null) {
            if (qty != null) {
                useQty = qty.subtract(remainQty);
            }
        }
        return useQty;
    }
    
}