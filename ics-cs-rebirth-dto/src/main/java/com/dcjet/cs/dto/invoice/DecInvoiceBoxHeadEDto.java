package com.dcjet.cs.dto.invoice;
import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2019-6-12
 */
@ApiModel(value = "出口发票箱单表头返回信息")
@Setter @Getter
public class DecInvoiceBoxHeadEDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String sid;
	/**
      * 关联编号（单据内部编号）
      */
    @ApiModelProperty("关联编号（单据内部编号）")
	private  String emsListNo;
	/**
      * 发票号码
      */
    @ApiModelProperty("发票号码")
	private  String invoiceNo;
	/**
      * 合同协议号
      */
    @ApiModelProperty("合同协议号")
	private  String contrNo;
	/**
      * 客户代码
      */
    @ApiModelProperty("客户代码")
	private  String customerCode;
	/**
      * 运输方式
      */
    @ApiModelProperty("运输方式")
	private  String trafMode;
	/**
      * 货代编码
      */
    @ApiModelProperty("货代编码")
	private  String forwardCode;
	/**
      * 目的地
      */
    @ApiModelProperty("目的地")
	private  String destination;
	/**
      * 总毛重
      */
    @ApiModelProperty("总毛重")
	private  BigDecimal grossWt;
	/**
      * 总净重
      */
    @ApiModelProperty("总净重")
	private  BigDecimal netWt;
	/**
      * 件数
      */
    @ApiModelProperty("件数")
	private  Integer packNum;
	/**
      * 包装种类
      */
    @ApiModelProperty("包装种类")
	private  String wrapType;
	/**
      * 出货日期
      */
    @ApiModelProperty("出货日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date shipmentDate;
	/**
      * 模板id
      */
    @ApiModelProperty("模板id")
	private  String templateHeadId;
	/**
      * 状态 1：锁定 0：未锁定
      */
    @ApiModelProperty("状态 1：锁定 0：未锁定")
	private  String status;
	/**
      * 企业代码
      */
    @ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
      * 提单表头id
      */
    @ApiModelProperty("提单表头id")
	private  String erpHeadId;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;

	/**
      * 模板编号
      */
    @ApiModelProperty("模板编号")
	private  String templateCode;
	/**
      * 客户名称
      */
    @ApiModelProperty("客户名称")
	private  String customerName;
	/**
      * 货代名称
      */
    @ApiModelProperty("货代名称")
	private  String forwardName;
	/**
	 * 收货人代码
	 */
	@ApiModelProperty("收货人代码")
	private  String consigneeCode;
	/**
	 * 收货人名称
	 */
	@ApiModelProperty("收货人名称")
	private  String consigneeName;
	/**
	 * 收货人地址
	 */
	@ApiModelProperty("收货人地址")
	private  String consigneeAddr;
	/**
	 * 收货人电话
	 */
	@ApiModelProperty("收货人电话")
	private  String consigneeTel;
	/**
	 * 启运港
	 */
	@ApiModelProperty("启运港")
	private  String despPort;
	/**
	 * 目的港
	 */
	@ApiModelProperty("目的港")
	private  String destPort;
	/**
	 * SHIP_TO
	 */
	@ApiModelProperty("SHIP_TO")
	private  String shipTo;
	/**
	 * 发票日期
	 */
	@ApiModelProperty("发票日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date invoiceDate;
	/**
	 * 币制
	 */
	@ApiModelProperty("币制")
	private  String curr;
	/**
	 * 货代
	 */
	@ApiModelProperty("货代")
	private  String forwarder;
	/**
	 * 运输方式
	 */
	@ApiModelProperty("运输方式")
	private  String trafModeName;
	/**
	 * 付款方式
	 */
	@ApiModelProperty("付款方式")
	private  String paymentMode;
	/**
	 * 成交方式
	 */
	@ApiModelProperty("成交方式")
	private  String transModeName;
	/**
	 * 银行信息
	 */
	@ApiModelProperty("银行信息")
	private  String bankInfo;
	/**
	 * 唛头
	 */
	@ApiModelProperty("唛头")
	private  String marks;
	/**
	 * SHIP_TO名称·
	 */
	@ApiModelProperty("SHIP_TO名称")
	private  String shipToName;
	/***
	 * 汇率
	 */
	@ApiModelProperty("汇率")
	private BigDecimal exchangeRate;
	/***
	 * 运抵国
	 */
	@ApiModelProperty("运抵国")
	private String tradeCountry;
	/**
	 * 单位净重
	 */
	@ApiModelProperty("单位净重")
	private BigDecimal unitNetWt;

	/**
	 * 单位体积
	 */
	@ApiModelProperty("单位体积")
	private BigDecimal unitVolume;
	/**
	 * 包装尺寸
	 */
	@ApiModelProperty("包装尺寸")
	private String packing;
}
