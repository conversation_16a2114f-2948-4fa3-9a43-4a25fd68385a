package com.dcjet.cs.dto.imp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * generated by Generate dcits
 *
 * @author: 鏈辨涓�
 * @date: 2019-04-21
 */
@Setter
@Getter
@ApiModel(value = "鍙傛暟")
public class VImpEmsCusImgParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private String sid;
    /**
     * 料号
     */
    @ApiModelProperty("料号")
    private String copGNo;
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    private java.math.BigDecimal serialNo;
    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String codeTS;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String GName;
    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String GModel;
    /**
     * 申报计量单位
     */
    @ApiModelProperty("申报计量单位")
    private String unit;
    /**
     * 申报数量
     */
    @ApiModelProperty("申报数量")
    private java.math.BigDecimal qty;
    /**
     * 申报单价
     */
    @ApiModelProperty("申报单价")
    private java.math.BigDecimal decPrice;
    /**
     * 币制
     */
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 征免方式
     */
    @ApiModelProperty("征免方式")
    private String dutyMode;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
}
