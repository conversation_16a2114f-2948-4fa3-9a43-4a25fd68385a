package com.dcjet.cs.dto.temporary;

import com.dcjet.cs.dto.base.BasicParam;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 出境表头
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-04-16
 */
@ApiModel(value = "出境表头新增修改参数")
@Setter @Getter
public class TemporaryEHeadParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *担保方式
     */
    @ApiModelProperty(value = "担保方式")
    @XdoSize(max = 20, message = "{担保方式长度不能超过20位字节长度(一个汉字2位字节长度)!}") 
    private String assureType;
    
    /**
     *担保金额
     */
    @ApiModelProperty(value = "担保金额")
    @Digits(integer = 10, fraction = 2, message = "{担保金额必须为数字,整数位最大10位,小数最大2位!}")
    private BigDecimal assurePrice;
    
    /**
     *最迟复运日期
     */
    @ApiModelProperty(value = "最迟复运日期") 
    private Date endDate;
    
    /**
     *备注
     */
    @ApiModelProperty(value = "备注")
    @XdoSize(max = 512, message = "{备注长度不能超过512位字节长度(一个汉字2位字节长度)!}") 
    private String note;
    
}
