package com.dcjet.cs.dto.erp.sap;

import com.dcjet.cs.dto.erp.sap.constants.ErpSapConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date: 2019-9-5
 */
@Setter
@Getter
@ApiModel(description= "ERP出口提单表头传入参数")
public class ErpDecEHeadParam {
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private  String sid;

    @ApiModelProperty("关联提单编号")
    private String linkedNo;

    @ApiModelProperty("提取状态（0 未提取 1已提取）")
    private String status;

    @ApiModelProperty("数据状态(1：修改，2：删除，3：新增)")
    private Integer modifyMark;

    @ApiModelProperty("发票号")
    private String invoiceNo;

    @ApiModelProperty("合同协议号")
    private String contrNo;

    @ApiModelProperty("出货日期-起 yyyy-MM-dd")
    private String shipDateFrom;

    @ApiModelProperty("出货日期-止 yyyy-MM-dd")
    private String shipDateTo;

    @ApiModelProperty("运输方式")
    private String trafMode;

    @ApiModelProperty("监管方式")
    private String tradeMode;

    @ApiModelProperty("接收时间-起 yyyy-MM-dd")
    private String insertTimeFrom;

    @ApiModelProperty("接收时间-止 yyyy-MM-dd")
    private String insertTimeTo;

    private String tradeCode;
    private String cliType= ErpSapConstants.FOD_TYPE;
    private String IEPort;
    private String entryPort;
    private String destinationCountry;
    private String declareCode;
    private String tradeCreditCode;
    private String declareName;
    private String emsNo;
    private String cutMode;
    private String licenseNo;
    private String mergeType;
    private String voyageDateFrom;
    private String voyageDateTo;
}
