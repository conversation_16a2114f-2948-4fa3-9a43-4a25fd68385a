package com.dcjet.cs.dto.returned;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2021-2-5
 */
@ApiModel(value = "退运表体返回信息")
@Setter @Getter
public class GwReturnListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private String sid;
	/**
      * 料号
      */
    @ApiModelProperty("料号")
	private String facGNo;
	/**
      * 商品名称(企业料件/成品)
      */
    @ApiModelProperty("商品名称(企业料件/成品)")
	private String copGName;
	/**
      * 状态（0.未匹配 1.已匹配）
      */
    @ApiModelProperty("状态（0.未匹配 1.已匹配）")
	private String status;
	/**
      * 数量
      */
    @ApiModelProperty("数量")
	private BigDecimal qty;
	/**
      * 单位
      */
    @ApiModelProperty("单位")
	private String unit;
	/**
      * 单价
      */
    @ApiModelProperty("单价")
	private BigDecimal price;
	/**
      * 目的国
      */
    @ApiModelProperty("目的国")
	private String destinationCountry;
	/**
	 * 原产国
	 */
	@ApiModelProperty("原产国")
	private String originCountry;
	/**
      * 币制
      */
    @ApiModelProperty("币制")
	private String curr;
	/**
      * 销退订单号
      */
    @ApiModelProperty("销退订单号")
	private String returnOrderNo;
	/**
      * 数据来源（0.界面新增 1.出口发货单提取 2.erp接口提取）
      */
    @ApiModelProperty("数据来源（0.界面新增 1.出口发货单提取 2.erp接口提取）")
	private String dataSource;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private String tradeCode;
	/**
      * 制单人
      */
    @ApiModelProperty("制单人")
	private String insertUser;
	/**
      * 制单时间
      */
    @ApiModelProperty("制单时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date insertTime;
	/**
      * 制单人姓名
      */
    @ApiModelProperty("制单人姓名")
	private String insertUserName;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateTime;
	/**
      * 更新人姓名
      */
    @ApiModelProperty("更新人姓名")
	private String updateUserName;
	/**
      * 退运表头id
      */
    @ApiModelProperty("退运表头id")
	private String headId;
	/**
      * 出口发货单表体表中的id/erp出口明细表的id
      */
    @ApiModelProperty("出口发货单表体表中的id/erp出口明细表的id")
	private String billListId;
	/**
	 * 单据号
	 */
	@ApiModelProperty("单据号")
	private  String linkedNo;
	/**
	 * 序号
	 */
	@ApiModelProperty("序号")
	private  Integer lineNo;

	/**
	 * 总价
	 */
	private BigDecimal totalPrice;

}
