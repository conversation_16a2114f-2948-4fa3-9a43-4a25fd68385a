package com.dcjet.cs.dto.returned;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2021-2-5
 */
@ApiModel(value = "退运已匹配返回信息")
@Setter @Getter
public class GwReturnListMatchDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private  String sid;
	/**
      * 退运表头id
      */
    @ApiModelProperty("退运表头id")
	private  String headId;
	/**
      * 退运表体的id
      */
    @ApiModelProperty("退运表体的id")
	private  String listId;
	/**
      * 报关单号
      */
    @ApiModelProperty("报关单号")
	private  String entryNo;
	/**
      * 报关单申报日期
      */
    @ApiModelProperty("报关单申报日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	@JsonProperty("dDate")
	private Date dDate;
	/**
      * 企业料号
      */
    @ApiModelProperty("企业料号")
	private  String facGNo;
	/**
      * 核注清单编号
      */
    @ApiModelProperty("核注清单编号")
	private  String listNo;
	/**
      * 单位
      */
    @ApiModelProperty("单位")
	private  String unit;
	/**
      * 单价
      */
    @ApiModelProperty("单价")
	private  BigDecimal decPrice;
	@ApiModelProperty("总价")
	private  BigDecimal decTotal;
	/**
      * 目的国
      */
    @ApiModelProperty("目的国")
	private  String destinationCountry;
	/**
      * 原产国
      */
    @ApiModelProperty("原产国")
	private  String originCountry;
	/**
      * 币制
      */
    @ApiModelProperty("币制")
	private  String curr;
	/**
      * 制单人
      */
    @ApiModelProperty("制单人")
	private  String insertUser;
	/**
      * 制单时间
      */
    @ApiModelProperty("制单时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 制单人姓名
      */
    @ApiModelProperty("制单人姓名")
	private  String insertUserName;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 更新人姓名
      */
    @ApiModelProperty("更新人姓名")
	private  String updateUserName;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 匹配的数量
      */
    @ApiModelProperty("匹配的数量")
	private  BigDecimal qty;

	/**
	 * 可匹配数量
	 */
	@ApiModelProperty("可匹配数量")
	private BigDecimal kppQty;
	/**
	 * 剩余数量
	 */
	@ApiModelProperty("剩余数量")
	private BigDecimal syQty;
	@ApiModelProperty("上次数量")
	private BigDecimal lastQty;
	@ApiModelProperty("监管方式")
	private String tradeMode;
	/**
	 * 提单表体sid
	 */
	private String decListId;
	/**
	 * 发票号
	 */
	@ApiModelProperty("发票号")
	private String invoiceNo;
	/**
	 * 产地证编号
	 */
	@ApiModelProperty("产地证编号")
	private String documentNo;
	@ApiModelProperty("产地证编号")
	private String gName;
	@ApiModelProperty("产地证编号")
	private String emsListNo;
	@ApiModelProperty("净重")
	private BigDecimal netWt;
	@ApiModelProperty("法一数量")
	private BigDecimal qty1;
	@ApiModelProperty("法一单位")
	private String unit1;
	@ApiModelProperty("法二数量")
	private BigDecimal qty2;
	@ApiModelProperty("法二单位")
	private String unit2;

}
