package com.dcjet.cs.dto.ocrInvoice.ocrE;
import com.xdo.domain.ExcelExportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date: 2023-2-13
 */
@Getter
@Setter
@ApiModel(description = "出口表头查询传入参数")
public class GwOcrInvoiceEHeadLocExportParam extends ExcelExportParam {
    /**
     * 导出传入参数
     */
    @ApiModelProperty("查询参数")
    private GwOcrInvoiceEHeadLocParam exportColumns;
}
