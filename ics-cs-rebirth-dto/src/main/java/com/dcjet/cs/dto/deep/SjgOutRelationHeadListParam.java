package com.dcjet.cs.dto.deep;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2019-10-29
 */
@Setter @Getter
@ApiModel(value = "深加工转出备案关系表头传入参数")
public class SjgOutRelationHeadListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 转入方海关十位代码
     */
	@NotEmpty(message="{转入方海关十位代码不能为空！}")
	@XdoSize(max = 10, message = "{转入方海关十位代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转入方海关十位代码")
	private  String tradeCodeIn;
	/**
     * 转入方名称
     */
	@NotEmpty(message="{转入方名称不能为空！}")
	@XdoSize(max = 100, message = "{转入方名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转入方名称")
	private  String tradeNameIn;
	/**
     * 转入方手（账）册号
     */
	@NotEmpty(message="{转入方手（账）册号不能为空！}")
	@XdoSize(max = 12, message = "{转入方手（账）册号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转入方手（账）册号")
	private  String emsNoIn;
	/**
     * 转出方海关十位代码
     */
	@NotEmpty(message="{转出方海关十位代码不能为空！}")
	@XdoSize(max = 10, message = "{转出方海关十位代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出方海关十位代码")
	private  String tradeCodeOut;
	/**
     * 转出方名称
     */
	@NotEmpty(message="{转出方名称不能为空！}")
	@XdoSize(max = 100, message = "{转出方名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出方名称")
	private  String tradeNameOut;
	/**
     * 转出方手（账）册号
     */
	@NotEmpty(message="{转出方手（账）册号不能为空！}")
	@XdoSize(max = 12, message = "{转出方手（账）册号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出方手（账）册号")
	private  String emsNoOut;
	/**
     * 执行情况(0.停用 1.启用)
     */
	@NotEmpty(message="{执行情况(0.停用 1.启用)不能为空！}")
	@XdoSize(max = 2, message = "{执行情况(0.停用 1.启用)长度不能超过2位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("执行情况(0.停用 1.启用)")
	private  String status;
	/**
     * 转入方手（账）册号有效日期
     */
	@ApiModelProperty("转入方手（账）册号有效日期")
	private  Date validDateIn;
	/**
    * 转入方手（账）册号有效日期-开始
    */
	@ApiModelProperty("转入方手（账）册号有效日期-开始")
	private String validDateInFrom;
	/**
    * 转入方手（账）册号有效日期-结束
    */
	@ApiModelProperty("转入方手（账）册号有效日期-结束")
    private String validDateInTo;
	/**
     * 转入方代码(客户代码)
     */
	@NotEmpty(message="{转入方代码(客户代码)不能为空！}")
	@XdoSize(max = 50, message = "{转入方代码(客户代码)长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转入方代码(客户代码)")
	private  String customerCodeIn;


	/**
	 * 备案关系表头的SID
	 */
	@NotEmpty(message="{备案关系表头的SID不能为空！}")
	@XdoSize(max = 40, message = "{备案关系表头的SID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案关系表头的SID")
	private  String headId;
	/**
	 * 转出企业料号(该栏位暂不使用)
	 */
	@NotEmpty(message="{转出企业料号(该栏位暂不使用)不能为空！}")
	@XdoSize(max = 50, message = "{转出企业料号(该栏位暂不使用)长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出企业料号(该栏位暂不使用)")
	private  String facGNoOut;
	/**
	 * 转出备案料号
	 */
	@NotEmpty(message="{转出备案料号不能为空！}")
	@XdoSize(max = 32, message = "{转出备案料号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出备案料号")
	private  String copGNoOut;
	/**
	 * 转出备案序号
	 */
	@NotNull(message="{转出备案序号不能为空！}")
	@Digits(integer = 11, fraction = 0, message = "{转出备案序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("转出备案序号")
//	@JsonProperty("gNoOut")
	private  Integer gNoOut;
	/**
	 * 转入企业料号
	 */
	@XdoSize(max = 50, message = "{转入企业料号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转入企业料号")
	private  String facGNoIn;
	/**
	 * 转入备案料号
	 */
	@XdoSize(max = 32, message = "{转入备案料号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转入备案料号")
	private  String copGNoIn;
	/**
	 * 转入备案序号
	 */
	@NotNull(message="{转入备案序号不能为空！}")
	@Digits(integer = 11, fraction = 0, message = "{转入备案序号必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("转入备案序号")
//	@JsonProperty("gNoIn")
	private  Integer gNoIn;
}
