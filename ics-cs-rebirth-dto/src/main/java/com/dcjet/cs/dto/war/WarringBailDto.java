package com.dcjet.cs.dto.war;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2019-4-19
 */
@ApiModel(value = "预警Bail返回信息")
@Setter @Getter
public class WarringBailDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
      * 序号
      */
    @ApiModelProperty("序号")
	private BigDecimal serialNo;
	/**
      * 保函、保证金类型
      */
    @ApiModelProperty("保函、保证金类型")
	private  String bailType;
	/**
      * 单据号
      */
    @ApiModelProperty("单据号")
	private  String documentNo;
	/**
      * 有效期起
      */
    @ApiModelProperty("有效期起")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date dateStart;
	/**
      * 有效期止
      */
    @ApiModelProperty("有效期止")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date dateEnd;
	/**
      * 报关单号
      */
    @ApiModelProperty("报关单号")
	private  String entryNo;
	/**
      * 客户
      */
    @ApiModelProperty("客户")
	private  String customer;
	/**
      * 缴纳机关
      */
    @ApiModelProperty("缴纳机关")
	private  String payDept;
	/**
      * 单号事项
      */
    @ApiModelProperty("单号事项")
	private  String note;
	/**
      * 负责人
      */
    @ApiModelProperty("负责人")
	private  String chargePerson;
	/**
      * 状态 0 正常 1 注销
      */
    @ApiModelProperty("状态 0 正常 1 注销")
	private  String status;
	/**
      * 所属企业编码
      */
    @ApiModelProperty("所属企业编码")
	private  String tradeCode;
	/**
      * 手册号
      */
    @ApiModelProperty("手册号")
	private  String emsNo;
	/**
      * 总金额
      */
    @ApiModelProperty("总金额")
	private  BigDecimal totalPrice;

	/**
	 * 缴纳日期
	 */
	@ApiModelProperty("缴纳日期")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date paymentDate;
	/**
	 * 返回日期
	 */
	@ApiModelProperty("返回日期")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date returnDate;

	/**
	 * 超期天数
	 */
	@ApiModelProperty("超期天数")
	private String days;

	/**
	 * 预警状态
	 */
	@ApiModelProperty("预警状态")
	private String dataStatus;
	/**
	 * 预警状态名称
	 */
	@ApiModelProperty("预警状态名称")
	private String dataStatusName;


}
