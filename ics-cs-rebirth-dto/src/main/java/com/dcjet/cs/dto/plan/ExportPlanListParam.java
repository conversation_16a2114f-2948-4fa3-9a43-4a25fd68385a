package com.dcjet.cs.dto.plan;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2022-7-27
 */
@Setter
@Getter
@ApiModel(value = "传入参数")
public class ExportPlanListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 表头SID
     */
    @XdoSize(max = 40, message = "{表头SID长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("表头SID")
    private String headId;
    /**
     * 企业代码
     */
    @XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 单据号(出货单号)
     */
    @NotEmpty(message = "{单据号(出货单号)不能为空！}")
    @XdoSize(max = 40, message = "{单据号(出货单号)长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("单据号(出货单号)")
    private String linkedNo;
    /**
     * 单据序号
     */
    @NotEmpty(message = "{单据序号不能为空！}")
    @XdoSize(max = 4, message = "{单据序号长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("单据序号")
    private String lineNo;
    /**
     * 保完税标志 0 保税 1 非保税
     */
    @NotEmpty(message = "{保完税标志不能为空！}")
    @XdoSize(max = 1, message = "{保完税标志长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("保完税标志 0 保税 1 非保税")
    private String bondMark;
    /**
     * 工厂
     */
    @NotEmpty(message = "{工厂不能为空！}")
    @XdoSize(max = 10, message = "{工厂长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("工厂")
    private String factory;
    /**
     * 企业料号
     */
    @NotEmpty(message = "{企业料号不能为空！}")
    @XdoSize(max = 40, message = "{企业料号长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业料号")
    private String facGNo;
    /**
     * 申报数量
     */
    @NotNull(message = "{申报数量不能为空！}")
    @Digits(integer = 9, fraction = 4, message = "{申报数量必须为数字,整数位最大9位,小数最大4位!}")
    @ApiModelProperty("申报数量")
    private BigDecimal qty;
    /**
     * 申报单价
     */
    @NotNull(message = "{申报单价不能为空！}")
    @Digits(integer = 9, fraction = 6, message = "{申报单价必须为数字,整数位最大9位,小数最大6位!}")
    @ApiModelProperty("申报单价")
    private BigDecimal decPrice;
    /**
     * 申报总价
     */
    @NotNull(message = "{申报总价不能为空！}")
    @Digits(integer = 9, fraction = 4, message = "{申报总价必须为数字,整数位最大9位,小数最大4位!}")
    @ApiModelProperty("申报总价")
    private BigDecimal decTotal;
    /**
     * 系统单价
     */
    @NotNull(message = "{系统单价不能为空！}")
    @Digits(integer = 9, fraction = 6, message = "{系统单价必须为数字,整数位最大9位,小数最大6位!}")
    @ApiModelProperty("系统单价")
    private BigDecimal systemPrice;
    /**
     * 系统总价
     */
    @NotNull(message = "{系统总价不能为空！}")
    @Digits(integer = 9, fraction = 4, message = "{系统总价必须为数字,整数位最大9位,小数最大4位!}")
    @ApiModelProperty("系统总价")
    private BigDecimal systemTotal;
    /**
     * ERP交易单位
     */
//    @NotEmpty(message = "{ERP交易单位不能为空！}")
    @XdoSize(max = 10, message = "{ERP交易单位长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("ERP交易单位")
    private String unitErp;
    /**
     * 币制
     */
    @NotEmpty(message = "{币制不能为空！}")
    @XdoSize(max = 3, message = "{币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("币制")
    private String curr;
    /**
     * 毛重
     */
    @Digits(integer = 9, fraction = 2, message = "{毛重必须为数字,整数位最大9位,小数最大2位!}")
    @ApiModelProperty("毛重")
    private BigDecimal grossWt;
    /**
     * 净重
     */
    @Digits(integer = 9, fraction = 2, message = "{净重必须为数字,整数位最大9位,小数最大2位!}")
    @ApiModelProperty("净重")
    private BigDecimal netWt;
    /**
     * 客户编码(收货方)
     */
    @NotEmpty(message = "{客户编码(收货方)不能为空！}")
    @XdoSize(max = 50, message = "{客户编码(收货方)长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户编码(收货方)")
    private String supplierCode;
    /**
     * 客户名称
     */
    @XdoSize(max = 255, message = "{客户名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户名称")
    private String supplierName;
    /**
     * 客户料号
     */
    @XdoSize(max = 40, message = "{客户料号长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户料号")
    private String customerGNo;
    /**
     * 客户订单号
     */
    @XdoSize(max = 40, message = "{客户订单号长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户订单号")
    private String customerPoNo;
    /**
     * 加工费单价
     */
    @Digits(integer = 9, fraction = 6, message = "{加工费单价必须为数字,整数位最大9位,小数最大6位!}")
    @ApiModelProperty("加工费单价")
    private BigDecimal decPriceProcess;
    /**
     * 加工费总价
     */
    @Digits(integer = 9, fraction = 4, message = "{加工费总价必须为数字,整数位最大9位,小数最大4位!}")
    @ApiModelProperty("加工费总价")
    private BigDecimal decTotalProcess;
    /**
     * 备注1
     */
    @XdoSize(max = 200, message = "{备注1长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注1")
    private String note1;
    /**
     * 备注2
     */
    @XdoSize(max = 200, message = "{备注2长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注2")
    private String note2;
    /**
     * 物料类型 i 料件  e 成品
     */
    @XdoSize(max = 1, message = "{物料类型长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("物料类型 i 料件  e 成品")
    private String gmark;
    /**
     * 出口方式
     */
    @XdoSize(max = 40, message = "{出口方式长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("出口方式")
    private String outWay;
    /**
     * 出库关联单号
     */
    @XdoSize(max = 40, message = "{出库关联单号长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("出库关联单号")
    private String inOutRelNo;
    /**
     * 买方客户代码
     */
    @NotEmpty(message = "{买方客户代码不能为空！}")
    @XdoSize(max = 40, message = "{买方客户代码长度不能超过40位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("买方客户代码")
    private String buyerSupplierCode;
    /**
     * 创建人
     */
    @XdoSize(max = 50, message = "{创建人长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date insertTime;
    /**
     * 创建时间-开始
     */
    @ApiModelProperty("创建时间-开始")
    private String insertTimeFrom;
    /**
     * 创建时间-结束
     */
    @ApiModelProperty("创建时间-结束")
    private String insertTimeTo;
    /**
     * 更新人
     */
    @XdoSize(max = 50, message = "{更新人长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("更新人")
    private String updateUser;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 更新时间-开始
     */
    @ApiModelProperty("更新时间-开始")
    private String updateTimeFrom;
    /**
     * 更新时间-结束
     */
    @ApiModelProperty("更新时间-结束")
    private String updateTimeTo;
    /**
     * 制单人姓名
     */
    @XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
    @ApiModelProperty("数据来源 1 录入 2 导入 3 提取")
    private String dataSource;

}
