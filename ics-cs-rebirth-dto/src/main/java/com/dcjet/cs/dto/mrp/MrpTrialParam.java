package com.dcjet.cs.dto.mrp;

import com.dcjet.cs.dto.base.BasicParam;
import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 税金试算
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2019-12-17
 */
@ApiModel(value = "税金试算 新增修改参数")
@Setter @Getter
public class MrpTrialParam extends BasicParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业料件料号
     */
    @ApiModelProperty(value = "企业料件料号")
    private String facGNo;
    /**
     * 备案料件料号
     */
    @ApiModelProperty(value = "备案料件料号")
    private String copGNo;
    /**
     * 备案料件序号
     */
    @ApiModelProperty(value = "备案料件序号")
    private String gNo;
    /**
     * 料件商品编码
     */
    @ApiModelProperty(value = "料件商品编码")
    private String codeTS;
    /**
     * 料件商品名称
     */
    @ApiModelProperty(value = "料件商品名称")
    private String gName;
    /**
     * 原产国
     */
    @ApiModelProperty(value = "原产国")
    private String originCountry;
    /**
     * 表头主键
     */
    @ApiModelProperty(value = "表头主键")
    private String headId;
}