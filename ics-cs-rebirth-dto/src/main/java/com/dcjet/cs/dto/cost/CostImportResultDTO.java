package com.dcjet.cs.dto.cost;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 费用导入结果
 * <AUTHOR>
@Getter
@Setter
public class CostImportResultDTO {
    /***
     * 无效的费用科目
     */
    private List<String> unavailableCourses = new ArrayList<>();
    /***
     * 非法的内部提单编号
     */
    private List<String> illegalEMSLISTNOs = new ArrayList<>();
    /***
     * 最终成功导入的数据量
     */
    private Integer successAmount;
    /***
     * 结果，文字描述
     */
    private String result;
}
