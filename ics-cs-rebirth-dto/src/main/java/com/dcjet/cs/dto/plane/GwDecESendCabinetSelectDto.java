package com.dcjet.cs.dto.plane;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021-1-25
 */
@Setter
@Getter
@ApiModel(value = "发货订舱柜型信息返回参数")
public class GwDecESendCabinetSelectDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    @ApiModelProperty("状态")
    private String status;
    /**
     * 柜型
     */
    @ApiModelProperty("柜型")
    private String cabinetType;
    /**
     * 柜号
     */
    @ApiModelProperty("柜号")
    private String containerNo;
    /**
     * 拖柜日期
     */
    @ApiModelProperty("拖柜日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cabinetDate;
    /**
     * 分票号
     */
    @ApiModelProperty("分票号")
    private String branchNo;
    /**
     * 拖柜备注
     */
    @ApiModelProperty("拖柜备注")
    private String note;

    /**
     * 对应表体行数
     */
    @ApiModelProperty("对应表体行数")
    private String listCount;

    @ApiModelProperty("shipmentId")
    private String shipmentId;

}
