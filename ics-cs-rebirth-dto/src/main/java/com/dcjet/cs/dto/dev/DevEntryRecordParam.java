package com.dcjet.cs.dto.dev;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2019-10-8
 */
@Setter @Getter
@ApiModel(value = "设备管理-报关记录传入参数")
public class DevEntryRecordParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 单据内部编号
     */
	@XdoSize(max = 32, message = "{单据内部编号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("单据内部编号")
	private  String emsListNo;
	/**
     * 报关单号
     */
	@XdoSize(max = 18, message = "{报关单号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("报关单号")
	private  String entryNo;
	/**
     * 申报日期
     */
	@ApiModelProperty("申报日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date declareDate;
	/**
    * 申报日期-开始
    */
	@ApiModelProperty("申报日期-开始")
	private String declareDateFrom;
	/**
    * 申报日期-结束
    */
	@ApiModelProperty("申报日期-结束")
    private String declareDateTo;
	/**
     * 监管方式
     */
	@XdoSize(max = 4, message = "{监管方式长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("监管方式")
	private  String tradeMode;
	/**
     * 商品名称
     */
	@XdoSize(max = 255, message = "{商品名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("商品名称")
	private  String GName;
	/**
     * 规格型号
     */
	@XdoSize(max = 255, message = "{规格型号长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("规格型号")
	private  String GModel;
	/**
     * 商品编码
     */
	@XdoSize(max = 10, message = "{商品编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("商品编码")
	private  String codeTS;
	/**
     * 单位
     */
	@XdoSize(max = 3, message = "{单位长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("单位")
	private  String unit;
	/**
     * 数量
     */
	@Digits(integer = 20, fraction = 5, message = "{数量必须为数字,整数位最大20位,小数最大5位!}")
	@ApiModelProperty("数量")
	private  BigDecimal qty;
	/**
     * 金额
     */
	@Digits(integer = 23, fraction = 2, message = "{金额必须为数字,整数位最大23位,小数最大2位!}")
	@ApiModelProperty("金额")
	private  BigDecimal decTotal;
	/**
     * 币制
     */
	@XdoSize(max = 3, message = "{币制长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("币制")
	private  String curr;
	/**
     * 备案号
     */
	@XdoSize(max = 12, message = "{备案号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备案号")
	private  String emsNo;
	/**
     * 项目确认书编号
     */
	@XdoSize(max = 50, message = "{项目确认书编号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("项目确认书编号")
	private  String itemNo;
	/**
     * 监管有效期
     */
	@ApiModelProperty("监管有效期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date tradeValidDate;
	/**
    * 监管有效期-开始
    */
	@ApiModelProperty("监管有效期-开始")
	private String tradeValidDateFrom;
	/**
    * 监管有效期-结束
    */
	@ApiModelProperty("监管有效期-结束")
    private String tradeValidDateTo;
	/**
     * 企业代码
     */
	@XdoSize(max = 10, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业代码")
	private  String tradeCode;
	/**
	 * 物料类型（I料件、E成品）
	 */
	@XdoSize(max = 30, message = "{企业代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("物料类型（I料件、E成品）")
	private  String GMark;
	/**
	 * 放行日期
	 */
	@ApiModelProperty("放行日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date passDate;
}
