package com.dcjet.cs.dto.deep;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2019-11-5
 */
@Setter @Getter
@ApiModel(value = "SjgInBillHeadParam")
public class SjgInBillHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 转入单据内部编号
     */
//	@NotEmpty(message="{转入单据内部编号不能为空！}")
	@XdoSize(max = 32, message = "{转入单据内部编号长度不能超过32位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转入单据内部编号")
	private  String emsListNo;
	/**
     * 转出方备案号
     */
	@NotEmpty(message="{转出方备案号不能为空！}")
	@XdoSize(max = 12, message = "{转出方备案号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出方备案号")
	private  String emsNoOut;
	/**
     * 转出方海关十位代码
     */
	@NotEmpty(message="{转出方海关十位代码不能为空！}")
	@XdoSize(max = 10, message = "{转出方海关十位代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出方海关十位代码")
	private  String tradeCodeOut;
	/**
     * 转出方企业名称
     */
	@NotEmpty(message="{转出方企业名称不能为空！}")
	@XdoSize(max = 100, message = "{转出方企业名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出方企业名称")
	private  String tradeNameOut;
	/**
     * 转入方备案号
     */
	@NotEmpty(message="{转入方备案号不能为空！}")
	@XdoSize(max = 12, message = "{转入方备案号长度不能超过12位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转入方备案号")
	private  String emsNoIn;
	/**
     * 申请表编号
     */
	@XdoSize(max = 18, message = "{申请表编号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申请表编号")
	private  String applyNo;
	/**
     * 转入报关单号
     */
	@XdoSize(max = 18, message = "{转入报关单号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转入报关单号")
	private  String entryNoIn;
	/**
     * 转出报关单号
     */
	@XdoSize(max = 18, message = "{转出报关单号长度不能超过18位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("转出报关单号")
	private  String entryNoOut;
	/**
     * 状态
     */
	@NotEmpty(message="{状态不能为空！}")
	@XdoSize(max = 1, message = "{状态长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("状态")
	private  String status;
	/**
     * 结转日期开始
     */
	@ApiModelProperty("结转日期开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
	private  Date beginDate;
	/**
    * 结转日期开始-开始
    */
	@ApiModelProperty("结转日期开始-开始")
	private String beginDateFrom;
	/**
    * 结转日期开始-结束
    */
	@ApiModelProperty("结转日期开始-结束")
    private String beginDateTo;
	/**
     * 结转日期结束
     */
	@ApiModelProperty("结转日期结束")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
	private  Date endDate;
	/**
    * 结转日期结束-开始
    */
	@ApiModelProperty("结转日期结束-开始")
	private String endDateFrom;
	/**
    * 结转日期结束-结束
    */
	@ApiModelProperty("结转日期结束-结束")
    private String endDateTo;
	/**
     * 备注
     */
	@XdoSize(max = 50, message = "{备注长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String remark;
	/**
     * 转入方海关十位代码
     */
//	@NotEmpty(message="{转入方海关十位代码不能为空！}")
//	@XdoSize(max = 10, message = "{转入方海关十位代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
//	@ApiModelProperty("转入方海关十位代码")
//	private  String tradeCodeIn;
	/**
     * 数据来源
     */
	@NotEmpty(message="{数据来源不能为空！}")
	@XdoSize(max = 1, message = "{数据来源长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("数据来源")
	private  String dataSource;
	/**
     * 转出方代码(供应商客户代码)
     */
//	@NotEmpty(message="{转出方代码(供应商客户代码)不能为空！}")
//	@XdoSize(max = 50, message = "{转出方代码(供应商客户代码)长度不能超过50位字节长度(一个汉字2位字节长度)!}")
//	@ApiModelProperty("转出方代码(供应商客户代码)")
//	private  String customerCodeOut;
	/**
     * 监管方式
     */
	@NotEmpty(message="{监管方式不能为空！}")
	@XdoSize(max = 6, message = "{监管方式长度不能超过6位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("监管方式")
	private  String tradeMode;
	/**
     * 深加工转入备案表头ID
     */
	@NotEmpty(message="{深加工转入备案表头ID不能为空！}")
	@XdoSize(max = 50, message = "{深加工转入备案表头ID长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("深加工转入备案表头ID")
	private  String headId;
	/**
     * 提单模板表头
     */
	@NotEmpty(message="{提单模板表头不能为空！}")
	@XdoSize(max = 50, message = "{提单模板表头长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("提单模板表头")
	private  String templeteId;
	/**
     * 总净重
     */
	@Digits(integer = 14, fraction = 8, message = "{总净重必须为数字,整数位最大14位,小数最大8位!}")
	@ApiModelProperty("总净重")
	private  BigDecimal netWt;
	/**
     * 总毛重
     */
	@Digits(integer = 14, fraction = 5, message = "{总毛重必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("总毛重")
	private  BigDecimal grossWt;
	/**
     * 件数
     */
	@Digits(integer = 11, fraction = 0, message = "{件数必须为数字,整数位最大11位,小数最大0位!}")
	@ApiModelProperty("件数")
	private BigDecimal packNum;
	/**
     * 数量汇总
     */
	@Digits(integer = 14, fraction = 5, message = "{数量汇总必须为数字,整数位最大14位,小数最大5位!}")
	@ApiModelProperty("数量汇总")
	private  BigDecimal sumQty;
	/**
     * 金额汇总
     */
	@Digits(integer = 20, fraction = 5, message = "{金额汇总必须为数字,整数位最大20位,小数最大5位!}")
	@ApiModelProperty("金额汇总")
	private  BigDecimal sumDecTotal;
	/**
     * 收发货单编号
     */
	@XdoSize(max = 2000, message = "{收发货单编号长度不能超过2000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("收发货单编号")
	private  String rsBillNo;
	/**
	 * 发票号
	 */
	@XdoSize(max = 50, message = "{发票号长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发票号")
	private String invoiceNo;
}
