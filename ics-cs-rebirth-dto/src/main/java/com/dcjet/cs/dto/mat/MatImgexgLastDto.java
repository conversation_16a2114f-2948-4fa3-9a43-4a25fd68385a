package com.dcjet.cs.dto.mat;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 * @date: 2019-6-3
 */
@ApiModel(value = "MatImgexgLastDto返回信息")
@Setter @Getter
public class MatImgexgLastDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 最新的物料数据
      */
    @ApiModelProperty("最新的物料数据")
	private  String lastData;
	/**
      * 企业名称
      */
    @ApiModelProperty("企业名称")
	private  String tradeCode;
}
