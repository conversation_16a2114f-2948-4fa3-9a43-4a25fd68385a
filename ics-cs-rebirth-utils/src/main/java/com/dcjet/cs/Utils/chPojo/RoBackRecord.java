package com.dcjet.cs.Utils.chPojo;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 回调erp数据记录关联表
 */
@Setter
@Getter
@Table(name = "T_ERP_BACK_FILL")
public class RoBackRecord {
    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private String sid;
    /**
     * 关联核注清单表头Sid
     */
    @Column(name = "HEAD_ID")
    private String headId;
    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    /**
     * 回填标记（0回填失败 1回填成功）
     */
    @Column(name = "SEND_MARK")
    private String sendMark;
    /**
     * 回填类型
     */
    @Column(name = "BACK_FILL_TYPE")
    private String backFillType;
    /**
     * 进出口类型
     */
    @Column(name = "I_E_TYPE")
    private String ieType;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /**
     * 创建时间
     */
    @Column(name = "INSERT_TIME")
    private Date insertTime;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

}
