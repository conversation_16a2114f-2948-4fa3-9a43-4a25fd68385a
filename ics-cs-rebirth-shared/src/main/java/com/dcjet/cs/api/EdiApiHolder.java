package com.dcjet.cs.api;

import com.xdo.common.json.JsonObjectMapper;
import com.xdo.findsword.ServiceTokenInterceptor;
import okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import javax.annotation.PostConstruct;
import java.lang.invoke.MethodHandles;
import java.util.concurrent.TimeUnit;

@Component
public class EdiApiHolder {

    private static final Logger log = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    private Retrofit retrofit;

    @Value("${gw.api.url}")
    private String apiBaseUrl;

    @Autowired
    ServiceTokenInterceptor serviceTokenInterceptor;

    @PostConstruct
    public void init() {
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(serviceTokenInterceptor)
                .callTimeout(30, TimeUnit.SECONDS).build();

        retrofit = new Retrofit.Builder()
                .addConverterFactory(JacksonConverterFactory.create(JsonObjectMapper.getInstance().getMapper()))
                .client(client)
                .baseUrl(apiBaseUrl)
                .build();
    }


    /***
     * 创建需要调用的智慧关务开放接口
     *
     * @param service
     * @param <T>
     * @return
     */
    public <T> T create(final Class<T> service) {
        return retrofit.create(service);
    }
}
