package com.dcjet.cs.service.callMetaData;

import com.dcjet.cs.metaData.config.WcfFunction;
import com.dcjet.cs.metaData.main.CallMetaDataUtil;
import com.dcjet.cs.metaData.wcf.ServiceStub;
import com.dcjet.cs.model.BillEntryModel;
import com.dcjet.cs.model.EmsNoModel;
import com.dcjet.cs.model.ExgImgModel;
import com.dcjet.cs.util.ConstantsStatus;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Auther: Administrator
 * @Date: 2019/4/18 18:06
 * @Description:
 */
@Component
public class CallMetaDataWcf {
    @Resource
    private CallMetaDataUtil callMetaDataUtil;

    /**
     * 功能描述:获取备案通过账册信息
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/4/18
     * @param: strTradeCode  企业编码
     * @param: strCopEmsNo  企业内部编号
     * @return: 更新结果信息
     */
    public List<EmsNoModel> getPassEmsInfo(String strTradeCode, String strCopEmsNo) throws Exception {
        ServiceStub.OperateInfo operateInfo = new ServiceStub.OperateInfo();
        operateInfo.setTradeCodeKey(strTradeCode);
        operateInfo.setEmsNo("");
        operateInfo.setOrgNo(strCopEmsNo);
        operateInfo.setBatch(0);
        operateInfo.setDcrTimes(0);
        operateInfo.setQueryTimeStamp(ConstantsStatus.STATUS_0);
        return callMetaDataUtil.CallWcf(WcfFunction.EmsNo, operateInfo, EmsNoModel.class);
    }
    /**
     * 功能描述:获取备案通过的成品
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/4/18
     * @param: strTradeCode  企业编码
     * @param: strCopListNo  企业内部编号
     * @return: 更新结果信息
     */
    public List<ExgImgModel> getPassedExg(String strTradeCode, String strEmsNo,List<String> listCopGNos) throws Exception {
        ServiceStub.OperateInfo operateInfo = new ServiceStub.OperateInfo();
        operateInfo.setTradeCodeKey(strTradeCode);
        operateInfo.setEmsNo(strEmsNo);
        ServiceStub.ArrayOfstring listInfo = new ServiceStub.ArrayOfstring();
        listInfo.setString(listCopGNos.toArray(new String[listCopGNos.size()]));
        operateInfo.setListInfoData(listInfo);
        operateInfo.setBatch(0);
        operateInfo.setDcrTimes(0);
        operateInfo.setQueryTimeStamp(ConstantsStatus.STATUS_0);
        return callMetaDataUtil.CallWcf(WcfFunction.OrgExg, operateInfo, ExgImgModel.class);
    }
    /**
     * 功能描述:获取备案通过的料件
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/4/18
     * @param: strTradeCode  企业编码
     * @param: strCopListNo  企业内部编号
     * @return: 更新结果信息
     */
    public List<ExgImgModel> getPassedImg(String strTradeCode, String strEmsNo,List<String> listCopGNos) throws Exception {
        ServiceStub.OperateInfo operateInfo = new ServiceStub.OperateInfo();
        operateInfo.setTradeCodeKey(strTradeCode);
        operateInfo.setEmsNo(strEmsNo);
        ServiceStub.ArrayOfstring listInfo = new ServiceStub.ArrayOfstring();
        listInfo.setString(listCopGNos.toArray(new String[listCopGNos.size()]));
        operateInfo.setListInfoData(listInfo);
        operateInfo.setBatch(0);
        operateInfo.setDcrTimes(0);
        operateInfo.setQueryTimeStamp(ConstantsStatus.STATUS_0);
        return callMetaDataUtil.CallWcf(WcfFunction.OrgImg, operateInfo, ExgImgModel.class);
    }
    /**
     * 功能描述:获取备案通过的清单、报关单信息
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/4/18
     * @param: strTradeCode  企业编码
     * @param: strCopListNo  企业内部编号
     * @param: dataMark  数据标识 1：金二 2：H2000
     * @return: 更新结果信息
     */
    public List<BillEntryModel> getPassedBillEndEntry(String strTradeCode, List<String> listEmsListNos,String dataMark) throws Exception {
        ServiceStub.OperateInfo operateInfo = new ServiceStub.OperateInfo();
        operateInfo.setTradeCodeKey(strTradeCode);
        operateInfo.setEmsNo("");
        ServiceStub.ArrayOfstring listInfo = new ServiceStub.ArrayOfstring();
        listInfo.setString(listEmsListNos.toArray(new String[listEmsListNos.size()]));
        operateInfo.setListInfoData(listInfo);
        operateInfo.setBatch(0);
        operateInfo.setDcrTimes(0);
        operateInfo.setQueryTimeStamp(ConstantsStatus.STATUS_0);

        return callMetaDataUtil.CallWcf(dataMark.equals(ConstantsStatus.STATUS_1)?WcfFunction.Bill:WcfFunction.QueryJgtCSBillInfo, operateInfo, BillEntryModel.class);
    }
}
