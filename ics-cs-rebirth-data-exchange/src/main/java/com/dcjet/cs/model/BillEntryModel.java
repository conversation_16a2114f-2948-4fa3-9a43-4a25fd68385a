package com.dcjet.cs.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Getter;
import lombok.Setter;

/**
 * @Auther: Administrator
 * @Date: 2019/4/19 14:30
 * @Description:清单、报关单数据模型
 */
@Getter
@Setter
@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
public class BillEntryModel extends  BaseModel {
    /**
     * 企业清单内部编号
     */
    @JsonAlias("EMS_LIST_NO")
    private String emsListNo;
    /**
     * 账册号
     */
    @JsonAlias("EMS_NO")
    private String emsNo;
    /**
     * 清单编号
     */
    @JsonAlias("LIST_NO")
    private String listNo;
    /**
     * 清单流水号
     */
    @JsonAlias("BILL_LIST_NO")
    private String billListNo;
    /**
     * 清单预录入编号
     */
    @JsonAlias("PRE_SEQ_NO")
    private String preSeqNo;
    /**
     * 报关单统一编号
     */
    @JsonAlias("SEQ_NO")
    private String seqNo;
    /**
     * 报关单号
     */
    @JsonAlias("ENTRY_NO")
    private String entryNo;
    /**
     * 进出口标志
     */
    @JsonAlias("I_E_MARK")
    private String IEMark;
    /**
     * 状态
     */
    @JsonAlias("STATUS")
    private String status;
//    /**
//     * 报关单申报日期
//     */
//    @JsonAlias("ENTRY_DECLARE_DATE")
//    private String entryDeclareDate;
//    /**
//     * 清单申报日期
//     */
//    @JsonAlias("DECLARE_DATE")
//    private String DeclareDate;
    /**
     * 数据标识 1：金二 2：H2000
     */
    private String dataMark;
    /**
     * 发送api状态
     */
    private String sendApiStatus;
}
