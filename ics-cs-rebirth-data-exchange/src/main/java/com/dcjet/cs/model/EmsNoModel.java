package com.dcjet.cs.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @Auther: Administrator
 * @Date: 2019/4/19 14:19
 * @Description:账册号模型
 */
@Getter
@Setter
@JsonAutoDetect(fieldVisibility=JsonAutoDetect.Visibility.ANY, getterVisibility=JsonAutoDetect.Visibility.NONE)
public class EmsNoModel extends BaseModel {
    /**
     * 企业内部编号
     */
    @JsonAlias("COP_EMS_NO")
    private String copEmsNo;
    /**
     * 账册号
     */
    @JsonAlias("EMS_NO")
    private String emsNo;
    /**
     * 金二审批状态
     */
    @JsonAlias("STATUS")
    private String status;
    /**
     * 最终审核时间
     */
    @JsonAlias("LAST_APPR_TIME")
    private Date lastApprTime;

    /**
     *单据标识
     * 11：金二手册 12：H2000手册 21：金二账册 22：H2000账册
     */
    @JsonIgnore
    private String billFlag;
    @JsonIgnore
    private String sendFlag;
    @JsonIgnore
    private String billFlagName;

    @JsonIgnore
    private String gMark;
}
