<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dao.UpdateKimTwoDataMapper">
    <resultMap id="emsNoResultMap" type="com.dcjet.cs.model.EmsNoModel">
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
        <result column="COP_EMS_NO" property="copEmsNo" jdbcType="VARCHAR" />
        <result column="EMS_NO" property="emsNo" jdbcType="VARCHAR" />
        <result column="BILL_FLAG" property="billFlag" jdbcType="VARCHAR" />
        <result column="g_mark" property="gMark" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="exgImgResultMap" type="com.dcjet.cs.model.ExgImgModel">
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
        <result column="EMS_NO" property="emsNo" jdbcType="VARCHAR" />
        <result column="COP_G_NO" property="copGNo" jdbcType="VARCHAR" />
        <result column="SID" property="sid" jdbcType="VARCHAR" />
        <result column="LAST_AUDIT_PASS_DATE" property="lastOprDate" jdbcType="DATE" />
    </resultMap>
    <resultMap id="billEntryResultMap" type="com.dcjet.cs.model.BillEntryModel">
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
        <result column="EMS_LIST_NO" property="emsListNo" jdbcType="VARCHAR" />
        <result column="I_E_MARK" property="IEMark" jdbcType="VARCHAR" />
        <result column="DATA_MARK" property="dataMark" jdbcType="VARCHAR" />
    </resultMap>
    <!-- 获取发送备案的且发送账册成功的数据 -->
    <select id="getWaitingPassCopListNos" resultMap="emsNoResultMap" >
        SELECT
          A.*,
          CASE WHEN A.BILL_FLAG = '11' THEN '金二手册' WHEN A.BILL_FLAG = '21' THEN '金二账册' WHEN A.BILL_FLAG = '31' THEN '点讯通' ELSE A.BILL_FLAG END AS billFlagName,
          CASE WHEN B.TRADE_CODE IS NULL OR B.TRADE_CODE = '' THEN 'Y' ELSE 'N' END as sendFlag
        FROM (
            SELECT
                T.TRADE_CODE,
                T.COP_EMS_NO,
                T.BILL_FLAG,
                T.EMS_NO,t.g_mark
            FROM
              T_MAT_IMGEXG_ORG T
            WHERE T.APPR_STATUS='JA' AND SEND_API_STATUS='1'
            GROUP BY
                T.TRADE_CODE,
                T.COP_EMS_NO,
                T.BILL_FLAG,
                T.EMS_NO,t.g_mark
        ) A
        LEFT JOIN (SELECT DISTINCT TRADE_CODE FROM T_GWSTD_WCF_CONFIG) B ON A.TRADE_CODE = B.TRADE_CODE
    </select>
    <select id="getEmsHeadCopListNos" resultMap="emsNoResultMap" >
        SELECT
          A.*,
          CASE WHEN A.BILL_FLAG = '11' THEN '金二手册' WHEN A.BILL_FLAG = '21' THEN '金二账册' WHEN A.BILL_FLAG = '31' THEN '点讯通' ELSE A.BILL_FLAG END AS billFlagName,
          CASE WHEN B.TRADE_CODE IS NULL OR B.TRADE_CODE = '' THEN 'Y' ELSE 'N' END as sendFlag
        FROM (
          SELECT
            T.TRADE_CODE,
            T.COP_EMS_NO,
            T.BILL_FLAG,
            T.EMS_NO,
            T.APPR_STATUS as STATUS
        FROM
            T_GWSTD_EMS_HEAD T
        WHERE T.APPR_STATUS in ('JA','J1')
        GROUP BY
            T.TRADE_CODE,
            T.COP_EMS_NO,
            T.BILL_FLAG,
            T.EMS_NO,
            T.APPR_STATUS
        ) A
        LEFT JOIN (SELECT DISTINCT TRADE_CODE FROM T_GWSTD_WCF_CONFIG) B ON A.TRADE_CODE = B.TRADE_CODE
    </select>
    <!-- 更新审核通过账册的成品、料件信息-->
    <select id="updatePassedEmsInfo" statementType="CALLABLE">
    {
         CALL P_MAT_EXGIMG_ORG_PASS_API(
          #{tradeCode,mode=IN,jdbcType=VARCHAR},
          #{emsNo,mode=IN,jdbcType=VARCHAR},
          #{copEmsNo,mode=IN,jdbcType=VARCHAR},
          #{lastApprTime,mode=IN,jdbcType=TIMESTAMP},
          #{status,mode=IN,jdbcType=VARCHAR},
          #{billFlag,mode=IN,jdbcType=VARCHAR},
          #{errMsg,mode=OUT,jdbcType=VARCHAR}
         )
     }
    </select>
    <select id="updatePassedEmsInfo" statementType="CALLABLE" resultType="map" databaseId="postgresql">
         CALL P_MAT_EXGIMG_ORG_PASS_API(
          #{tradeCode,mode=IN,jdbcType=VARCHAR},
          #{emsNo,mode=IN,jdbcType=VARCHAR},
          #{copEmsNo,mode=IN,jdbcType=VARCHAR},
          #{lastApprTime,mode=IN,jdbcType=TIMESTAMP},
          #{status,mode=IN,jdbcType=VARCHAR},
          #{billFlag,mode=IN,jdbcType=VARCHAR},
          #{errMsg,mode=IN,jdbcType=VARCHAR}
         )
    </select>
    <!-- 更新审核通过账册编号-->
    <update id="updatePassedEmsNo" parameterType="com.dcjet.cs.model.EmsNoModel">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE T_MAT_IMGEXG_ORG T SET T.EMS_NO= #{item.emsNo} WHERE T.TRADE_CODE= #{item.tradeCode} AND
            T.COP_EMS_NO= #{item.copListNo}
        </foreach>
    </update>
    <!-- 获取待审核通过成品、料件 ；内生通过 变更标志为新增、修改；发送状态为：成功；且手账册号不为空-->
    <select id="getWaitingPassExgImgs" resultMap="exgImgResultMap" parameterType="java.lang.String">
        SELECT
        T.TRADE_CODE,
        T.EMS_NO,
        T.COP_G_NO,
        T.SID,
        P.LAST_AUDIT_PASS_DATE
        FROM
        T_MAT_IMGEXG_ORG T,T_MAT_IMGEXG_ORG_PASS P WHERE T.SID=P.SID(+)
        AND  T.G_MARK=#{GMark}  AND T.MODIFY_MARK IN ('1','3') AND T.APPR_STATUS='8' AND T.SEND_API_STATUS='1' AND  T.EMS_NO IS NOT NULL
        GROUP BY
        T.TRADE_CODE,
        T.EMS_NO,
        T.COP_G_NO,
        T.SID,
        P.LAST_AUDIT_PASS_DATE
    </select>
    <!-- 更新备案通过成品料件状态 -->
    <update id="updatePassedExgsStatus" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
               P_MAT_EXGIMG_ORG_PASS(
                null,
                #{item.tradeCode,mode=IN,jdbcType=VARCHAR},
                #{item.emsNo,mode=IN,jdbcType=VARCHAR},
                #{item.copGNo,mode=IN,jdbcType=VARCHAR},
                #{GMark,mode=IN,jdbcType=VARCHAR},
                #{item.lastOprDate,mode=IN,jdbcType=TIMESTAMP}
                )
        </foreach>
    </update>
    <!-- 获取待审核通过清单企业内部编号 【发送成功或退单的数据】 -->
    <select id="getWaitingPassEmsListNo" resultMap="billEntryResultMap"  >
        SELECT T.TRADE_CODE,T.EMS_NO,T.EMS_LIST_NO,T.I_E_MARK,T.DATA_MARK FROM T_DEC_I_BILL_HEAD T,T_DEC_ERP_I_HEAD_N H  WHERE T.HEAD_ID=H.SID AND H.APPR_STATUS='8'
        AND T.SEND_API_STATUS IN ('1','J3') AND T.DATA_MARK=#{dataMark}
        UNION ALL
        SELECT T.TRADE_CODE,T.EMS_NO,T.EMS_LIST_NO,T.I_E_MARK,T.DATA_MARK FROM T_DEC_E_BILL_HEAD T,T_DEC_ERP_E_HEAD_N H  WHERE T.HEAD_ID=H.SID AND H.APPR_STATUS='8'
        AND T.SEND_API_STATUS IN ('1','J3')  AND T.DATA_MARK=#{dataMark}
    </select>

    <!-- 更新审核通过清单编号 -->
    <update id="updatePassedListNo" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE
            <choose>
                <when test="item.IEMark=='I'.toString()">
                    T_DEC_I_BILL_HEAD T
                </when>
                <otherwise >
                    T_DEC_E_BILL_HEAD T
                </otherwise>
            </choose>
            SET LIST_NO= #{item.listNo} ,ENTRY_NO=
            #{item.entryNo},BILL_SEQ_NO=#{item.preSeqNo},SEQ_NO=#{item.seqNo},BILL_STATUS=#{item.status}
            <if test="item.sendApiStatus!=null and item.sendApiStatus!=''">
                ,SEND_API_STATUS=#{item.sendApiStatus}
            </if>
            WHERE T.TRADE_CODE= #{item.tradeCode}
            AND  T.EMS_LIST_NO= #{item.emsListNo}
            ;
            --更新报关单追踪
            UPDATE
            <choose>
                <when test="item.IEMark=='I'.toString()">
                    T_DEC_I_CUSTOMS_TRACK T
                </when>
                <otherwise >
                    T_DEC_E_CUSTOMS_TRACK T
                </otherwise>
            </choose>
            SET ENTRY_NO= #{item.entryNo}
            WHERE T.TRADE_CODE= #{item.tradeCode}
            AND T.EMS_LIST_NO= #{item.emsListNo};
            --更新报关单信息
            UPDATE
            <choose>
                <when test="item.IEMark=='I'.toString()">
                    T_DEC_I_ENTRY_HEAD T
                </when>
                <otherwise>
                    T_DEC_E_ENTRY_HEAD T
                </otherwise>
            </choose>
            SET ENTRY_NO= #{item.entryNo} ,SEQ_NO=#{item.seqNo}
            WHERE T.TRADE_CODE= #{item.tradeCode}
            AND T.EMS_LIST_NO= #{item.emsListNo}
        </foreach>
    </update>
    <update id="updatePassedListNo" parameterType="java.util.List" databaseId="postgresql">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE
            <choose>
                <when test="item.IEMark=='I'.toString()">
                    T_DEC_I_BILL_HEAD T
                </when>
                <otherwise>
                    T_DEC_E_BILL_HEAD T
                </otherwise>
            </choose>
            SET LIST_NO= #{item.listNo} ,ENTRY_NO=
            #{item.entryNo},BILL_SEQ_NO=#{item.preSeqNo},SEQ_NO=#{item.seqNo},BILL_STATUS=#{item.status}
            <if test="item.sendApiStatus!=null and item.sendApiStatus!=''">
                ,SEND_API_STATUS=#{item.sendApiStatus}
            </if>
            WHERE T.TRADE_CODE= #{item.tradeCode}
            AND T.EMS_LIST_NO= #{item.emsListNo}
            ;
            --更新报关单追踪
            UPDATE
            <choose>
                <when test="item.IEMark=='I'.toString()">
                    T_DEC_I_CUSTOMS_TRACK T
                </when>
                <otherwise>
                    T_DEC_E_CUSTOMS_TRACK T
                </otherwise>
            </choose>
            SET ENTRY_NO= #{item.entryNo}
            WHERE T.TRADE_CODE= #{item.tradeCode}
            AND T.EMS_LIST_NO= #{item.emsListNo};
            --更新报关单信息
            UPDATE
            <choose>
                <when test="item.IEMark=='I'.toString()">
                    T_DEC_I_ENTRY_HEAD T
                </when>
                <otherwise>
                    T_DEC_E_ENTRY_HEAD T
                </otherwise>
            </choose>
            SET ENTRY_NO= #{item.entryNo} ,SEQ_NO=#{item.seqNo}
            WHERE T.TRADE_CODE= #{item.tradeCode}
            AND T.EMS_LIST_NO= #{item.emsListNo}
        </foreach>
    </update>

    <select id="updatePassedEmsData" statementType="CALLABLE">
    {
         CALL P_EMS_MAT_PASS_API(
          #{tradeCode,mode=IN,jdbcType=VARCHAR},
          #{emsNo,mode=IN,jdbcType=VARCHAR},
          #{copEmsNo,mode=IN,jdbcType=VARCHAR},
          #{lastApprTime,mode=IN,jdbcType=TIMESTAMP},
          #{status,mode=IN,jdbcType=VARCHAR},
          #{headStatus,mode=IN,jdbcType=VARCHAR},
          #{billFlag,mode=IN,jdbcType=VARCHAR},
          #{errMsg,mode=OUT,jdbcType=VARCHAR}
         )
     }
    </select>
    <select id="updatePassedEmsData" statementType="CALLABLE" resultType="map" databaseId="postgresql">
         CALL P_EMS_MAT_PASS_API(
          #{tradeCode,mode=IN,jdbcType=VARCHAR},
          #{emsNo,mode=IN,jdbcType=VARCHAR},
          #{copEmsNo,mode=IN,jdbcType=VARCHAR},
          #{lastApprTime,mode=IN,jdbcType=TIMESTAMP},
          #{status,mode=IN,jdbcType=VARCHAR},
          #{headStatus,mode=IN,jdbcType=VARCHAR},
          #{billFlag,mode=IN,jdbcType=VARCHAR},
          #{errMsg,mode=IN,jdbcType=VARCHAR}
         )
    </select>
    <select id="getTradeCode" resultType="com.dcjet.cs.mat.model.WcfConfig">
        SELECT
        T.TRADE_CODE
        FROM
        T_GWSTD_WCF_CONFIG T
        GROUP BY
        T.TRADE_CODE
    </select>
</mapper>
